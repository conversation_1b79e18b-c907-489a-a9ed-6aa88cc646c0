# proxy conf

#worker_processes            auto;
#worker_cpu_affinity         auto;

worker_rlimit_nofile        100000;

error_log                   "pipe:@CRONOLOG_HOME@/sbin/cronolog /home/<USER>/logs/nginx/cronolog/%Y/%m/%Y-%m-%d-error.log" warn;

pid                         /home/<USER>/logs/nginx/nginx.pid;

events {
    use                     epoll;
    worker_connections      20480;
}


http {
    include                 @NGINX_HOME@/conf/mime.types;
    default_type            application/octet-stream;
    proxy_store             off;    
    underscores_in_headers  on; #Enables the use of underscores in client request header fields

    sendfile                on;
    tcp_nopush              on;

    server_tokens           off;

    keepalive_timeout       0;
    server_names_hash_bucket_size 256;

    proxy_ignore_client_abort on;
    client_header_timeout   1m;
    send_timeout            1m;
    client_max_body_size    100m;
    client_body_buffer_size 64k;
    index                   index.html index.htm;
    
    log_format  main  '$http_orig_client_ip - $remote_addr:$remote_port - $remote_user [$time_local] '
                      '"$request_method http://$host$request_uri" $status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$upstream_addr" $request_time '
                      '$upstream_response_time zone="$cookie_zone" "$cookie_ali_apache_tracktmp"'; 

    access_log              "pipe:@CRONOLOG_HOME@/sbin/cronolog /home/<USER>/logs/nginx/cronolog/%Y/%m/%Y-%m-%d-access.log" main;
    log_not_found           off;

    gzip                    on;
    gzip_http_version       1.0;
    gzip_comp_level         6;
    gzip_min_length         1024;
    gzip_proxied            any;
    gzip_vary               on;
    gzip_disable            msie6;
    gzip_buffers            96 8k;
    gzip_types              text/xml text/plain text/css application/javascript application/x-javascript application/rss+xml application/json;
# 统计
    proxy_temp_path          /home/<USER>/temp/nginx/proxy_temp 1 2;
    client_body_temp_path   /home/<USER>/temp/nginx/client_body_temp 1 2;
    proxy_set_header         X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_redirect           off;
    proxy_buffers            128 8k;
    proxy_intercept_errors   on;

    # fight DDoS attack, tune the numbers below according your application!!!
    #limit_req_zone          $binary_remote_addr  zone=req:20m   rate=200r/s;
    #limit_req               zone=req  burst=100;
    #limit_zone              conn $binary_remote_addr  20m;
    #limit_conn              conn 200;


    # waf, fight hashdos attack
    #waf                              on;
    #waf_max_post_params              1000;
    #waf_max_args                     1000;
    #waf_max_cookies                  1000;
    #waf_post_delimiter_maxlen        70;

    server {
        listen              80 default_server;
        #rewrite  ^/$       /index.html redirect;


        location /ALIPAY_HEALTH_CHECK/ {            
            alias  /home/<USER>/ALIPAY_HEALTH_CHECK ;            
            access_log off;        
        }

        # 80 -> 8080
        location ~* (^.+\.(resource|json|tile|htm|xls|pdf|zip)($|\;.?))|(^.*(/[^/\.]+)|(/)$){
            # uriSuffix (file types) white list
            proxy_pass http://sofaboot;
            proxy_set_header Host $http_host;
            proxy_set_header X-Forwarded-By $server_addr:$server_port;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header Connection "";
            proxy_http_version 1.1;
            proxy_connect_timeout 5s;
            proxy_read_timeout 300s;
            proxy_send_timeout 300s;
            proxy_buffer_size 16k;
            proxy_buffers 8 64k;
            proxy_busy_buffers_size 128k;
            add_header X-Accel-Buffering no;
        }

        # default server
        #location / {
        #    root /home/<USER>/release/run/target/static;
        #    index index.html;
        #    expires 1d;
        #}

        location ~ ^(.*)\/\.svn\/{
            deny all;        
        }        
    }

      upstream sofaboot{
            server 127.0.0.1:8888;
            keepalive 5;
        }

    server {
        listen              80;
        server_name         status.taobao.com;
        location            = /nginx_status {
            stub_status     on;
            allow 127.0.0.0/24;
            deny all;
        }
    }
}
