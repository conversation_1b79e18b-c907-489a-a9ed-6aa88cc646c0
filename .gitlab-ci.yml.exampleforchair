before_script:
  - '[ ! -d $HOME/.nvm ] && git clone http://gitlab.alibaba-inc.com/node/nvm.git $HOME/.nvm'
  - 'cd $HOME/.nvm && git pull -p && cd -'
  - 'source $HOME/.nvm/nvm.sh'

node:
  script:
    - 'NODE_VERSION=4'
    - 'nvm install $NODE_VERSION'
    - 'nvm use $NODE_VERSION && npm install -g tnpm --registry=http://registry.npm.alibaba-inc.com --no-cache'
    - 'nvm use $NODE_VERSION && tnpm install --no-cache'
    - 'nvm use $NODE_VERSION && tnpm run ci'