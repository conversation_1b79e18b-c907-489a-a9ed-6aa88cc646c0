<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <Console name="STDOUT-APPENDER" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %X{traceId} %-5p %c{2} - %m%n%throwable" charset="UTF-8"/>
        </Console>

        <Console name="STDERR-APPENDER" target="SYSTEM_ERR">
            <PatternLayout pattern="%d %-5p %c{2} - %m%n%throwable" charset="UTF-8"/>
        </Console>

        <RollingFile name="ERROR-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/common-error.log"
                     filePattern="${ctx:logging.path}/${ctx:spring.application.name}/common-error.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
            <ThresholdFilter level="ERROR"/>
        </RollingFile>

        <RollingFile name="APP-DEFAULT-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/app-default.log"
                     filePattern="${ctx:logging.path}/${ctx:spring.application.name}/app-default.log.%d{yyyy-MM-dd-HH}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="SPRING-APPENDER" fileName="${ctx:logging.path}/spring/spring.log"
                     filePattern="${ctx:logging.path}/spring/spring.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="NO-USAGE-APPENDER" fileName="${ctx:logging.path}/no-usage/no-usage.log"
                     filePattern="${ctx:logging.path}/no-usage/no-usage.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="METHOD-RT-APPENDER" fileName="${ctx:logging.path}/codegencore/method-rt.log"
                     filePattern="${ctx:logging.path}/codegencore/method-rt.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d - %m%n"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="CODE-COMPLETIONS-INPUT-APPENDER" fileName="${ctx:logging.path}/codegencore/code-completions-input.log"
                     filePattern="${ctx:logging.path}/codegencore/code-completions-input.log.%d{yyyy-MM-dd}-%i"
                     append="true">
            <PatternLayout
                    pattern="%d - %m%n"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <SizeBasedTriggeringPolicy size="1024MB"/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="CODE-COMPLETIONS-OUTPUT-APPENDER" fileName="${ctx:logging.path}/codegencore/code-completions-output.log"
                     filePattern="${ctx:logging.path}/codegencore/code-completions-output.log.%d{yyyy-MM-dd}-%i"
                     append="true">
            <PatternLayout
                    pattern="%d - %m%n"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <SizeBasedTriggeringPolicy size="1024MB"/>
            <DefaultRolloverStrategy/>
        </RollingFile>


        <RollingFile name="TSINGYAN-APPENDER" fileName="${ctx:logging.path}/codegencore/tsingyan-completions.log"
                     filePattern="${ctx:logging.path}/codegencore/tsingyan-completions.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d - %m%n"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>
        <RollingFile name="TSINGYAN-TRACK-APPENDER" fileName="${ctx:logging.path}/codegencore/tsingyan-track.log"
                     filePattern="${ctx:logging.path}/codegencore/tsingyan-track.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d - %m%n"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>
        <RollingFile name="CHAT-COMPLETION-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/chat-completion.log"
                     filePattern="${ctx:logging.path}/${ctx:spring.application.name}/chat-completion.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>
        <RollingFile name="ONLINE-DEBUG-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/online-debug.log"
                     filePattern="${ctx:logging.path}/${ctx:spring.application.name}/online-debug.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>
        <!--限流器日志-->
        <RollingFile name="RATE-LIMIT-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/rate-limit.log"
                     filePattern="${ctx:logging.path}/${ctx:spring.application.name}/rate-limit.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <!--GPT cache 日志-->
        <RollingFile name="GPT-CACHE-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/gpt-cache.log"
                     filePattern="${ctx:logging.path}/${ctx:spring.application.name}/gpt-cache.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <RollingFile name="OTHERS-INFO-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/others_info.log"
                     filePattern="${ctx:logging.path}/${ctx:spring.application.name}/others_info.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{req.requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>
        <!--  Antmonitor GOC 监控日志  -->
        <RollingFile name="GOC-LOG-APPENDER" fileName="${ctx:logging.path}/codegencore/goc-log.log"
                     filePattern="${ctx:logging.path}/codegencore/goc-log.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d [%X{traceId} - %X{req.requestURIWithQueryString}] %m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <!--log to ODPS-->
        <RollingFile name="BIZ-METRIC-APPENDER" fileName="${ctx:logging.path}/codegencore/bizmetric.log"
                     shouldAsync="true" queueLength="512" neverBlock="true"
                     filePattern="${ctx:log_root}/devops/bizmetric.log.%d{yyyy-MM-dd}">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss}@@%m%n%throwable" charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

        <!-- svat 日志 -->
        <RollingFile name="SVAT-DIGEST-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/svat-digest.log"
                     filePattern="${ctx:logging.path}/${ctx:spring.application.name}/svat-digest.log.%d{yyyy-MM-dd}"
                     append="true">
            <PatternLayout
                    pattern="%d{yyyy-MM-dd HH:mm:ss},%X{traceId},%m%n%throwable"
                    charset="UTF-8"/>
            <TimeBasedTriggeringPolicy/>
            <DefaultRolloverStrategy/>
        </RollingFile>

    </Appenders>

    <Loggers>
        <AsyncLogger name="STDOUT" additivity="false" level="info">
            <AppenderRef ref="STDOUT-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="STDERR" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="STDERR-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="com.taobao.tair" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="NO-USAGE-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="com.taobao.vipserver" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="NO-USAGE-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="com.alipay.codegencore.acts.test" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="APP-DEFAULT-APPENDER"/>
            <AppenderRef ref="STDOUT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="com.alipay.codegencore" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="APP-DEFAULT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="STDOUT-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="org.springframework" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="SPRING-APPENDER"/>
        </AsyncLogger>

        <AsyncRoot level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="APP-DEFAULT-APPENDER"/>
            <AppenderRef ref="ERROR-APPENDER"/>
            <AppenderRef ref="STDOUT-APPENDER"/>
        </AsyncRoot>

        <AsyncLogger name="GPTACCEPT" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="METHOD-RT-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="GPTCACHE" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="GPT-CACHE-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="GPTCACHERAY" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="GPT-CACHE-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="RATELIMIT" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="RATE-LIMIT-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="GPTINPUT" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="CODE-COMPLETIONS-OUTPUT-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="CODEGENINPUT" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="CODE-COMPLETIONS-INPUT-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="TSINGYANMONITOR" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="TSINGYAN-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="TSINGYANTRACK" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="TSINGYAN-TRACK-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="CHATCOMPLETION" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="CHAT-COMPLETION-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="ONLINEDEBUG" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="ONLINE-DEBUG-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="OTHERSINFO" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="OTHERS-INFO-APPENDER"/>
        </AsyncLogger>
        <AsyncLogger name="GOCLOG" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="GOC-LOG-APPENDER"/>
        </AsyncLogger>
        <!--log to ODPS-->
        <AsyncLogger name="BIZ-METRIC" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="BIZ-METRIC-APPENDER"/>
        </AsyncLogger>

        <AsyncLogger name="SVAT-DIGEST" additivity="false" level="${ctx:logging.level.com.alipay.codegencore}">
            <AppenderRef ref="SVAT-DIGEST-APPENDER"/>
        </AsyncLogger>
    </Loggers>
</Configuration>