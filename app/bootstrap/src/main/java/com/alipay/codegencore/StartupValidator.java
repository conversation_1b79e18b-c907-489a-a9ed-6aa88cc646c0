package com.alipay.codegencore;

import javax.annotation.Resource;

import com.alibaba.common.lang.StringUtil;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore
 * @CreateTime : 2023-11-27
 */
@Component
public class StartupValidator implements ApplicationRunner {

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Value("${spring.application.name}")
    private String serviceName;

    /**
     * 判断网络和配置是否是统一环境
     *
     * @param args
     */
    @Override
    public void run(ApplicationArguments args) {
        // 判断是网络环境后判断应用名是否与对应环境分支的应用名一致
        if (codeGPTDrmConfig.isIntranetApplication()) {
            if (!StringUtil.equals(serviceName, AppConstants.INTRANET_APPLICATION_NAME)) {
                throw new BizException(ResponseEnum.DEPLOYMENT_ENVIRONMENT_INCONSISTENCY);
            }
        }
        else {
            if (!StringUtil.equals(serviceName, AppConstants.EXTRANET_APPLICATION_NAME)) {
                throw new BizException(ResponseEnum.DEPLOYMENT_ENVIRONMENT_INCONSISTENCY);
            }
        }
    }
}
