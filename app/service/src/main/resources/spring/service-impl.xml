<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sofa="http://schema.alipay.com/sofa/schema/slite"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd

        http://schema.alipay.com/sofa/schema/slite http://schema.alipay.com/sofa/slite.xsd"
       default-autowire="byName">
    <sofa:consumer id="schedulerEventConsumer" group="S-codegencore-scheduler">
        <sofa:listener ref="codegencoreMessageListener"/>
        <sofa:channels>
            <sofa:channel value="TP_F_SC">
                <sofa:event eventType="direct" eventCode="EC_codegencore_model_health_inspection"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
            <sofa:channel value="TP_F_SC">
                <sofa:event eventType="direct" eventCode="EC_codegencore_model_env_inspection"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
            <sofa:channel value="TP_F_SC">
                <sofa:event eventType="direct" eventCode="EC_codegencore_model_keepliving_inspection"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
            <sofa:channel value="TP_F_SC">
                <sofa:event eventType="direct" eventCode="EC_codegencore_model_health_degree_notify"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
            <sofa:channel value="TP_F_SC">
                <sofa:event eventType="direct" eventCode="EC_codegencore_yuque_document_renew"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
            <sofa:channel value="TP_F_SC">
                <sofa:event eventType="direct" eventCode="EC_codegencore_check_notify_zsearch_status"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
            <sofa:channel value="TP_F_SC">
                <sofa:event eventType="direct" eventCode="EC_codegencore_index_build_task_check"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
        </sofa:channels>
        <sofa:binding.msg_broker/>
    </sofa:consumer>
    <sofa:consumer id="receiveEventConsumer" group="S-codegencore-receive">
        <sofa:listener ref="codegencoreMessageListener"/>
        <sofa:channels>
            <sofa:channel value="TP_F_EXTDATASYNC">
                <sofa:event eventType="direct" eventCode="EC_datahub_cto_code"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
        </sofa:channels>
        <sofa:binding.msg_broker/>
    </sofa:consumer>
    <sofa:consumer id="inspectionEventConsumer" group="S-codegencore-inspection">
        <sofa:listener ref="codegencoreMessageListener"/>
        <sofa:channels>
            <sofa:channel value="TP_O_LINKE">
                <sofa:event eventType="direct" eventCode="EC_RELEASE_STAGE_CHANGE"
                            waterMark="-1" persistence="false"/>
            </sofa:channel>
        </sofa:channels>
        <sofa:binding.msg_broker/>
    </sofa:consumer>
</beans>