package com.alipay.codegencore.service.tool.learning.plugin.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.UpdateSessionContextRequestModel;
import com.alipay.codegencore.model.response.linke.ApprovalUser;
import com.alipay.codegencore.model.response.linke.DefaultPRValueVO;
import com.alipay.codegencore.model.response.linke.WorkItemDefaultVO;
import com.alipay.codegencore.model.response.linke.WorkItemVO;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.DevInsightService;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.tool.learning.plugin.CodeFuseCallLinke;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.codegencore.utils.http.HttpClient;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.tool.learning.plugin.impl
 * @CreateTime : 2024-03-28
 */
@Service
public class CodeFuseCallLinkeImpl implements CodeFuseCallLinke {

    private static final Logger LOGGER = LoggerFactory.getLogger(CodeFuseCallLinkeImpl.class);

    @Resource
    private ChatSessionManageService chatSessionManageService;
    @Resource
    private DevInsightService        devInsightService;

    @Resource
    private TokenService tokenService;

    @Resource
    private UserAclService userAclService;

    /**
     * 获取应用名的fassUrl
     */
    private static final String APP_NAME_URL
            = "https://codegencore.alipay.com/api/tool/http?functionName=myjf.common.acifunc.cfinetune.copilot.xdcopilotlinkeappsearch";
    /**
     * 获取工作项列表的fassurl
     */
    private static final String WORK_ITEM_URL
            = "https://codegencore.alipay.com/api/tool/http?functionName=myjf.common.acifunc.cfinetune.copilot"
            + ".xdcopilotlinkeworkitemsearch";
    /**
     * 获取评审人列表的fassUrl
     */
    private static final String REVIEW_SEARCH_URL
            = "https://codegencore.alipay.com/api/tool/http?functionName=myjf.common.acifunc.cfinetune.copilot.xdcopilotlinkereviewsearch";
    /**
     * 获取分支列表的fassUrl
     */
    private static final String BRANCH_LIST_URL
            = "https://codegencore.alipay.com/api/tool/http?functionName=myjf.common.acifunc.cfinetune.copilot.xdcopilotlinkebranchlist";
    /**
     * 获取迭代默认值的fassUrl
     */
    private static final String DEFAULT_ITERATION
            = "https://codegencore.alipay.com/api/tool/http?functionName=myjf.common.acifunc.cfinetune.copilot.xdcopilotlinkeiterationtype";
    /**
     * 获取迭代默认值的fassUrl
     */
    private static final String DEFAULT_PR
            = "https://codegencore.alipay.com/api/tool/http?functionName=myjf.common.acifunc.cfinetune.copilot.xdcopilotproptinit";

    /**
     * 根据ampid和关键字查询应用名称
     *
     * @param empId
     * @param query
     * @return
     */
    @Override
    public List<String> getAppName(String empId, String query, String sessionId) {
        String content = getContent(empId, query, sessionId);
        JSONObject post = getFass(getEnVUrl(APP_NAME_URL), content);
        return JSONObject.parseArray(post.getString("apps"), String.class);
    }

    /**
     * 获取工作项列表
     *
     * @param empId
     * @param query
     * @return
     */
    @Override
    public List<WorkItemVO> getWorkItemList(String empId, String query, String sessionId) {

        String content = getContent(empId, query, sessionId);
        JSONObject post = getFass(getEnVUrl(WORK_ITEM_URL), content);
        return JSONObject.parseArray(post.getString("items"), WorkItemVO.class);
    }

    /**
     * 获取评审人列表
     *
     * @param empId
     * @param query
     * @return
     */
    @Override
    public List<ApprovalUser> getApprovalUser(String empId, String query, String sessionId) {
        String content = getContent(empId, query, sessionId);
        JSONObject post = getFass(getEnVUrl(REVIEW_SEARCH_URL), content);
        return JSONObject.parseArray(post.getString("reviewers"), ApprovalUser.class);
    }

    /**
     * 获取分支列表
     *
     * @param empId
     * @param query
     * @param sessionId
     * @return
     */
    @Override
    public List<String> getBranchList(String empId, String query, String sessionId) {
        String content = getContent(empId, query, sessionId);
        JSONObject post = getFass(getEnVUrl(BRANCH_LIST_URL), content);
        return JSONObject.parseArray(post.getString("branches"), String.class);
    }

    /**
     * 设置会话的上下文
     *
     * @param sessionId
     * @param key
     * @param value
     * @return
     */
    @Override
    public Long setSessionContext(String sessionId, String key, String value) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        ChatSessionDO chatSession = chatSessionManageService.getChatSession(sessionId);
        if(currentUser.getId().compareTo(chatSession.getUserId())!=0){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "当前会话不属于当前用户");
        }
        String extInfo = chatSession.getExtInfo();
        JSONObject extInfoJson;

        if (extInfo != null) {
            extInfoJson = JSONObject.parseObject(extInfo);
        }
        else {
            extInfoJson = new JSONObject();
        }
        extInfoJson.put(key, value);
        ChatSessionDO chatSessionDO = new ChatSessionDO();
        chatSessionDO.setUid(sessionId);
        chatSessionDO.setExtInfo(JSONObject.toJSONString(extInfoJson));
        chatSessionManageService.updateSession(chatSessionDO);
        return null;
    }

    /**
     * 批量更新会话上下文
     * @param updateSessionContextRequestModel
     */
    @Override
    public void setSessionContextBatch(UpdateSessionContextRequestModel updateSessionContextRequestModel) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        LOGGER.info("批量更新会话上下文:{}", JSON.toJSONString(updateSessionContextRequestModel));
        ChatSessionDO chatSession = chatSessionManageService.getChatSession(updateSessionContextRequestModel.getSessionId());
        if(currentUser.getId().compareTo(chatSession.getUserId())!=0){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "当前会话不属于当前用户");
        }
        if(CollectionUtils.isEmpty(updateSessionContextRequestModel.getUpdateItems())){
            return;
        }

        for (UpdateSessionContextRequestModel.SingleSessionContextRequest singleSessionContextRequest : updateSessionContextRequestModel.getUpdateItems()){
            if("repoPath".equalsIgnoreCase(singleSessionContextRequest.getKey())){
                String repoPath = (String) singleSessionContextRequest.getValue();
                String[] split = repoPath.split("/");
                if (split.length != 2) {
                    throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "invalid repo path:" + repoPath);
                }
            }
        }

        String extInfo = chatSession.getExtInfo();
        JSONObject extInfoJson;

        if (extInfo != null) {
            extInfoJson = JSONObject.parseObject(extInfo);
        }
        else {
            extInfoJson = new JSONObject();
        }

        List<UpdateSessionContextRequestModel.SingleSessionContextRequest> updateItems = updateSessionContextRequestModel.getUpdateItems();
        for (UpdateSessionContextRequestModel.SingleSessionContextRequest singleSessionContextRequest : updateItems){
            extInfoJson.put(singleSessionContextRequest.getKey(), singleSessionContextRequest.getValue());
        }

        ChatSessionDO chatSessionDO = new ChatSessionDO();
        chatSessionDO.setUid(updateSessionContextRequestModel.getSessionId());
        chatSessionDO.setExtInfo(JSONObject.toJSONString(extInfoJson));
        chatSessionManageService.updateSession(chatSessionDO);
    }


    /**
     * 根据empId获取默认迭代的信息
     *
     * @param empId
     * @param sessionId
     * @return
     */
    @Override
    public WorkItemDefaultVO getDefaultIteration(String empId, String sessionId) {
        String content = getContent(empId, null, sessionId);
        return JSONObject.toJavaObject(getFass(getEnVUrl(DEFAULT_ITERATION), content), WorkItemDefaultVO.class);
    }

    /**
     * 获取创建pr的默认信息
     *
     * @param empId
     * @param sessionId
     * @return
     */
    @Override
    public DefaultPRValueVO getDefaultPrValue(String empId, String sessionId) {
        String content = getContent(empId, null, sessionId);
        DefaultPRValueVO defaultPRValueVO = JSONObject.toJavaObject(getFass(getEnVUrl(DEFAULT_PR), content), DefaultPRValueVO.class);
        if (sessionId != null){
            this.setSessionContext(sessionId,"defaultIterationId",defaultPRValueVO.getIterationId());
        }

        return defaultPRValueVO;
    }

    /**
     * 给url组装上环境信息
     *
     * @param url
     * @return
     */
    private String getEnVUrl(String url) {
        boolean prePub = VisableEnvUtil.isPrePub();
        if (prePub) {
            url = url + "&env=PRE";
        }
        else {
            url = url + "&env=PROD";
        }
        return url;

    }

    /**
     * 组装请求fass的body
     *
     * @param empId
     * @param query
     * @param sessionId
     * @return
     */
    private String getContent(String empId, String query, String sessionId) {

        JSONObject content = new JSONObject();
        JSONObject user = JSON.parseObject(devInsightService.queryUser(empId).get(0).toString());
        content.put("empId", empId);

        content.put("username", user.get("account"));
        if (query != null) {
            content.put("search", query);
        }
        String tenantName = "alipay";
        if (sessionId != null) {
            ChatSessionDO chatSession = chatSessionManageService.getChatSession(sessionId);

            String iterationId = null;
            if (chatSession.getExtInfo() != null) {
                JSONObject extInfo = JSONObject.parseObject(chatSession.getExtInfo());
                if (extInfo.getString("tenantName") != null) {
                    tenantName = extInfo.getString("tenantName");
                }
                if (extInfo.getString("iterationId") != null) {
                    iterationId = extInfo.getString("iterationId");
                }
                else {
                    iterationId = extInfo.getString("defaultIterationId");
                }
            }
            if (iterationId != null) {
                content.put("iterationId", iterationId);
            }
        }

        content.put("tenantName", tenantName);
        return content.toJSONString();
    }

    /**
     * 请求fass接口
     *
     * @param url
     * @param content
     * @return
     */
    private JSONObject getFass(String url, String content) {

        JSONObject response = new JSONObject();
        try {
            LOGGER.info("请求fass接口:url{},content{}", url, content);
            response = JSONObject.parseObject(HttpClient.post(url).content(content)
                    .header("Content-Type", "application/json;charset=utf-8")
                    .header("codegpt_user", AppConstants.CODEGPT_TOKEN_USER)
                    .header("codegpt_token", tokenService.getTokenSystem(AppConstants.CODEGPT_TOKEN_USER).getToken())
                    .syncExecuteWithExceptionThrow(10000));
            LOGGER.info("请求fass接口response{}", response);
        } catch (Exception e) {
            LOGGER.error("ResponseEnum.ERROR_THROW");
            throw new BizException(ResponseEnum.HTTP_ERROR, "获取linke数据失败");
        }
        if (!response.getBoolean("success")) {
            LOGGER.info("请求fass接口失败:response{}", response);
            throw new BizException(ResponseEnum.HTTP_ERROR, "获取linke数据失败");
        }
        return JSONObject.parseObject(response.getString("data"));
    }
}
