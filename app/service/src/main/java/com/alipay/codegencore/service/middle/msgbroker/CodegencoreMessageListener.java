/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2017 All Rights Reserved.
 */
package com.alipay.codegencore.service.middle.msgbroker;

import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.common.event.UniformEvent;
import com.alipay.common.event.UniformEventContext;
import com.alipay.common.event.UniformEventMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用于处理msgBroker信息的监听器
 *
 * <AUTHOR>
 * @version : MachineServiceImpl.java, v 0.1 2020年10月26日 5:28 下午 yunchen Exp $
 */
@Service("codegencoreMessageListener")
public class CodegencoreMessageListener implements UniformEventMessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(CodegencoreMessageListener.class);

    @Resource
    private List<CodegencoreEventHandler> codemeasureEventHandlers;

    /**
     * 处理器Map, Key:topic|eventCode
     */
    private Map<String, CodegencoreEventHandler> eventHandlerMap;

    /**
     * 初始化处理器Map
     */
    @PostConstruct
    public void init() {
        eventHandlerMap = new HashMap<>();
        for (CodegencoreEventHandler codegencoreEventHandler : codemeasureEventHandlers) {
            eventHandlerMap.put(codegencoreEventHandler.getTopicEventCodePair(), codegencoreEventHandler);
        }
    }

    /**
     * 处理消息的回调
     *
     * @param message
     * @param uniformEventContext
     */
    @Override
    public void onUniformEvent(UniformEvent message, UniformEventContext uniformEventContext) {
        String dispatchKey = message.getTopic() + "|" + message.getEventCode();
        String eventId = message.getId();
        CodegencoreEventHandler eventHandler = eventHandlerMap.get(dispatchKey);
        if (eventHandler == null) {
            LOGGER.error("消息处理找不到处理器！key={}, messageId={}", dispatchKey, eventId);
            return;
        }

        // 这里的handle是void函数，里面是多线程处理，主要为了防止处理超时消息重发的问题,所以这里的返回值就被忽略了
        eventHandler.handle(message);
    }

}
