package com.alipay.codegencore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.mutable.MutablePair;
import cn.hutool.core.util.StrUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.codegencore.dal.example.ChatMessageDOExample;
import com.alipay.codegencore.dal.example.ChatSessionDOExample;
import com.alipay.codegencore.dal.mapper.ChatMessageDOMapper;
import com.alipay.codegencore.dal.mapper.ChatMessageManualMapper;
import com.alipay.codegencore.dal.mapper.ChatSessionDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.contant.AlgoImplConfigKey;
import com.alipay.codegencore.model.contant.WebApiContents;
import com.alipay.codegencore.model.copilot.CodeReference;
import com.alipay.codegencore.model.domain.*;
import com.alipay.codegencore.model.enums.*;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.*;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.PluginServiceRequestContext;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.model.tool.learning.*;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLog;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLogGroup;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageLog;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.remote.RemoteAgentStatus;
import com.alipay.codegencore.model.remote.TaskObject;
import com.alipay.codegencore.model.request.CodeGPTVoteRequestBean;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.model.util.RemoteAgentStreamDataBuildUtils;
import com.alipay.codegencore.service.answer.RepoChatService;
import com.alipay.codegencore.service.codegpt.*;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.*;
import com.alipay.codegencore.service.common.limiter.qpx.QpxLimiterFactory;
import com.alipay.codegencore.service.impl.model.AlgoModelHealthUtilService;
import com.alipay.codegencore.service.impl.model.AlgoModelUtilService;
import com.alipay.codegencore.service.impl.model.StreamDataQueueUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.FunctionCallService;
import com.alipay.codegencore.service.tool.learning.PluginWorkflowService;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.service.utils.SessionUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.service.utils.TbaseCacheService;
import com.alipay.codegencore.utils.CollectLogUtils;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 对话服务
 *
 * <AUTHOR>
 */
@Service
public class ChatMessageServiceImpl implements ChatMessageService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChatMessageServiceImpl.class);

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");

    /**
     * 文件注释正则
     */
    private static final Pattern FILE_ANNOTATION_PATTERN = Pattern.compile("〔(\\d+)〕");

    @Resource
    private ChatMessageDOMapper chatMessageDOMapper;

    @Resource
    private ChatSessionManageService chatSessionManageService;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private ChatMessageManualMapper chatMessageManualMapper;

    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;

    @Resource
    private TbaseCacheService tbaseCacheService;

    @Resource
    private ChatSessionDOMapper chatSessionDOMapper;

    @Resource
    private CalculateTokenService calculateTokenService;
    @Resource
    private SceneService sceneService;

    @Resource
    private AlgoModelUtilService algoModelUtilService;

    @Resource
    private PluginWorkflowService pluginWorkflowService;

    @Resource
    private FunctionCallService functionCallService;

    @Resource
    private PluginService pluginService;

    @Resource
    private OssService ossService;

    @Resource
    private MayaService mayaService;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;

    @Resource
    private DocumentHandleService documentHandleService;

    @Resource
    private ConfigService configService;

    @Resource
    private RepoChatService repoChatService;

    @Resource
    private CustomizeChatService customizeChatService;

    @Resource
    private StreamDataQueueUtilService  streamDataQueueUtilService;

    /**
     * 重新生成回答
     *
     * @return 回复内容
     */
    @Override
    public void regenerationAnswer(HttpServletResponse httpServletResponse, String sessionUid,String uid, Boolean remoteAgent, String answerUid) {

        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
        SceneDO scene = null;
        if(chatSessionDO.getSceneId() !=  null){
            scene = sceneService.getSceneById(chatSessionDO.getSceneId());
        }

        String modelName = chatSessionDO.getModel();
        if(scene!=null && scene.getMode() == 0){
            modelName = scene.getModel();
        }

        // 模型前置监测
        modelAvailableCheck(modelName, scene, chatSessionDO);
        boolean lock = tbaseCacheService.getLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid, 60 * 1000);
        if (!lock) {
            throw new BizException(ResponseEnum.REPEAT_QUESTIONS);
        }
        try {
            conversationPreCheck(chatSessionDO, modelName);
            List<ChatMessageDO> chatMessageDOList = listChatMessage(sessionUid, false, false);
            // 去除历史消息里面的文件搜索过程
            replaceOriginAnswerContent(chatMessageDOList);
            ChatMessageDO lastMessageDO = getLastMessage(chatMessageDOList, true);
            ChatCompletionRequest chatRequest = generateRequest(chatSessionDO, scene, chatMessageDOList, true);
            chatRequest.setStream(true);
            chatRequest.setModel(null); // 设置为 null 使用 algoBackendDo 中的默认 model
            ChatMessageDO chatMessageDO = null;

            ensureChatMessageListLegal(chatRequest);
            String query;
            if (uid != null) {
                ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
                chatMessageDOExample.createCriteria().andUidEqualTo(uid);
                chatMessageDO = chatMessageDOMapper.selectByExample(chatMessageDOExample).get(0);
                query = chatMessageDO.getContent();

                // 指定了uid，说明是用户修改了问题，需要更新问题内容
                chatRequest.getMessages().get(chatRequest.getMessages().size() - 1).setContent(query);
            } else {
                query = chatRequest.getMessages().get(chatRequest.getMessages().size() - 1).getContent();
            }

            List<SegmentInfo> segmentInfoList = searchDoc(query, chatSessionDO);
            JSONObject docsInfo = ChatUtils.exactDocsInfoAndUpdateSegmentInfo(segmentInfoList);
            ChatUtils.addDocSearchResultToChatRequest(segmentInfoList, chatRequest);

            long generateIndex = lastMessageDO.getGenerationIndex() + 1;
            String uniqueAnswerId = sessionUid
                    + "_" + lastMessageDO.getQueryIndex()
                    + "_" + generateIndex;

            String requestId = ShortUid.getUid();

            ConversationTypeEnum conversationType = getConversationType(scene);
            RuntimeInfo runtimeInfo = Optional.ofNullable(lastMessageDO.getRuntimeInfo())
                    .map(info -> JSONObject.parseObject(info, RuntimeInfo.class))
                    .orElse(null);

            // 如果仓库在上一次的对话中使用的就是仓库问答，重新生成也会使用仓库问答
            if (runtimeInfo!=null && runtimeInfo.getRepoChatInfo()!=null){
                conversationType = ConversationTypeEnum.REPO_CHAT;
                // 仓库问答用量监控
                if(StringUtils.isBlank(answerUid)){
                    CHAT_LOGGER.warn("repo chat from CodfuseWeb,sessionUid:{},uid:{}",sessionUid,uid);
                }
            }

            UserAuthDO userAuthDO = userAclService.getCurrentUser();
            // 当前用户没有这个助手的对话记录,本次才需要增加助手的使用人数
            boolean needAddSceneUserCount = scene != null && !chatSessionManageService.hasSceneSession(userAuthDO.getId(), scene.getId());
            // 当前用户没有这个模型的对话记录,本次才需要增加模型的使用人数
            boolean needAddModelUserCount = !chatSessionManageService.hasModelSession(userAuthDO.getId(), modelName);
            if(conversationType == ConversationTypeEnum.FIX_TOOL_CALL){
                Consumer<StreamResponseModel> pluginResultHandler = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, null, segmentInfoList, docsInfo, null, lastMessageDO.getQueryIndex(), lastMessageDO.getGenerationIndex() + 1,uid);
                handlePluginConversation(pluginResultHandler, chatSessionDO, chatMessageDOList, requestId, chatRequest, docsInfo, uniqueAnswerId, scene, remoteAgent, answerUid, httpServletResponse);
            } else if(conversationType == ConversationTypeEnum.AUTO_TOOL_CALL){
                //function call
                Consumer<StreamResponseModel> pluginResultHandler = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, null, segmentInfoList, docsInfo, null,  lastMessageDO.getQueryIndex(), lastMessageDO.getGenerationIndex() + 1,uid);
                handleFunctionConversation(pluginResultHandler, chatSessionDO, chatMessageDOList, requestId, chatRequest, docsInfo, uniqueAnswerId, scene, remoteAgent, answerUid, httpServletResponse);
            } else if (conversationType == ConversationTypeEnum.REPO_CHAT) {
                if (runtimeInfo==null || runtimeInfo.getRepoChatInfo() == null) {
                    throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
                }

                JSONObject repoChatConfig = runtimeInfo.getRepoChatInfo().getJSONObject("repoChatConfig");
                JSONArray referenceInfoList = runtimeInfo.getRepoChatInfo().getJSONArray("references");
                List<CodeReference> codeReferences = referenceInfoList == null?null:referenceInfoList.toJavaList(CodeReference.class);

                Consumer<StreamResponseModel> responseConsumer = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, null,  segmentInfoList, docsInfo, runtimeInfo.getRepoChatInfo(),  lastMessageDO.getQueryIndex(),lastMessageDO.getGenerationIndex() + 1,uid);
                handleRepoChatConversation(responseConsumer, chatSessionDO, requestId, chatRequest, docsInfo, uniqueAnswerId,true, remoteAgent, answerUid, repoChatConfig, codeReferences, httpServletResponse);
            }else if (conversationType == ConversationTypeEnum.CUSTOMIZE) {
                Consumer<StreamResponseModel> responseConsumer = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, null,  segmentInfoList, docsInfo, runtimeInfo.getRepoChatInfo(),  lastMessageDO.getQueryIndex(),lastMessageDO.getGenerationIndex() + 1,uid);
                handleCustomizeChatConversation(responseConsumer, chatSessionDO, requestId, chatRequest, docsInfo, uniqueAnswerId,true, remoteAgent, answerUid, httpServletResponse);
            } else{
                Consumer<StreamResponseModel> responseConsumer = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, null,  segmentInfoList, docsInfo, null, lastMessageDO.getQueryIndex(),lastMessageDO.getGenerationIndex() + 1,uid);
                handleCommonConversation(responseConsumer, chatSessionDO, requestId, chatRequest, docsInfo, uniqueAnswerId, scene, true, remoteAgent, answerUid, httpServletResponse);
            }
            // 成功后增加助手,插件,模型的相关使用次数
            addUsageCount(lastMessageDO, scene, modelName, needAddSceneUserCount, needAddModelUserCount);
        } finally {
            tbaseCacheService.releaseLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid);
        }
    }

    /**
     * 提问并获取流式输出
     *
     * @param sessionUid 会话uid
     * @param content    提问内容
     * @return
     */
    @Override
    public void conversation(HttpServletResponse httpServletResponse, String sessionUid, String content , Boolean isModified, Boolean remoteAgent, String answerUid, Boolean tryRepoSearch, JSONObject extraInfo) {
        //验证问题长度是否超过配置上限
        Long tokenQty = calculateTokenService.getTokenQty(content);
        JSONObject sessionConfig = chatSessionManageService.getSessionConfig(sessionUid,null);
        Long maxToken = sessionConfig.getLong(AppConstants.MAX_TOKEN);
        if (tokenQty != null && maxToken != null && tokenQty.compareTo(maxToken) > 0){
            throw new BizException(ResponseEnum.QUESTION_LENGTH_EXCEEDS_LIMIT);
        }

        // 监测逻辑前置， 减少没必要的全局所的开销
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
        SceneDO scene = null;
        if(chatSessionDO.getSceneId() !=  null){
            scene = sceneService.getSceneById(chatSessionDO.getSceneId());
        }
        String modelName = chatSessionDO.getModel();
        if(scene!=null && scene.getMode() == 0){
            modelName = scene.getModel();
        }
        conversationPreCheck(chatSessionDO, modelName);
        // 模型可用性监测， 目前只针对模型和普通助手监测（插件类监测目前不支持）
        modelAvailableCheck(modelName, scene, chatSessionDO);

        boolean lock = tbaseCacheService.getLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid, 60 * 1000);
        if (!lock) {
            throw new BizException(ResponseEnum.REPEAT_QUESTIONS);
        }
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        boolean needAddSceneUserCount = scene != null && !chatSessionManageService.hasSceneSession(userAuthDO.getId(), scene.getId());
        boolean needAddModelUserCount = !chatSessionManageService.hasModelSession(userAuthDO.getId(), modelName);
        try {
            List<ChatMessageDO> chatMessageDOList = listChatMessage(sessionUid, false, false);
            // 去除历史消息里面的文件搜索过程
            replaceOriginAnswerContent(chatMessageDOList);
            ChatMessageDO lastMessageDO = getLastMessage(chatMessageDOList,false);
            ChatCompletionRequest chatRequest = generateRequest(chatSessionDO, scene, chatMessageDOList, false);
            chatRequest.setStream(true);
            chatRequest.setModel(null); // 设置为 null 使用 algoBackendDo 中的默认 model

            ChatMessage userMessage = new ChatMessage();
            userMessage.setRole(ChatRoleEnum.USER.getName());
            userMessage.setContent(content);
            chatRequest.getMessages().add(userMessage);

            List<SegmentInfo> segmentInfoList = searchDoc(content, chatSessionDO);

            JSONObject docsInfo = ChatUtils.exactDocsInfoAndUpdateSegmentInfo(segmentInfoList);
            ChatUtils.addDocSearchResultToChatRequest(segmentInfoList, chatRequest);

            String requestId = ShortUid.getUid();
            long queryIndex;
            if (isModified){
                queryIndex= lastMessageDO == null ? 1 : lastMessageDO.getQueryIndex() ;
            }else {
                queryIndex = lastMessageDO == null ? 1 : lastMessageDO.getQueryIndex()+ 1;
            }
            long generateIndexAssistant = lastMessageDO == null ? 0 : lastMessageDO.getGenerationIndex()+ 1;
            ChatMessageDO lastUserMessage = getLastUserMessage(chatMessageDOList, false);
            long generateIndex = isModified ? lastUserMessage.getGenerationIndex() + 1 : 0;


            String uniqueAnswerId = sessionUid + "_" + queryIndex + "_" + generateIndex;

            ChatMessageDO queryMessageDO = new ChatMessageDO();
            queryMessageDO.setContent(content);
            queryMessageDO.setRole(ChatRoleEnum.USER.getName());
            queryMessageDO.setQueryIndex(queryIndex);
            queryMessageDO.setGenerationIndex(generateIndex);
            queryMessageDO.setUserId(chatSessionDO.getUserId());
            queryMessageDO.setSessionUid(sessionUid);
            queryMessageDO.setUid(requestId);
            queryMessageDO.setVersion(codeGPTDrmConfig.getChatMessageFormatVersion());

            ConversationTypeEnum conversationType = getConversationType(scene, tryRepoSearch);
            LOGGER.info("conversationType:{}，tryRepoSearch:{}", conversationType,tryRepoSearch);
            if(conversationType == ConversationTypeEnum.FIX_TOOL_CALL){
                Consumer<StreamResponseModel> pluginResultHandler = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, queryMessageDO,  segmentInfoList, docsInfo, null,  queryIndex, generateIndexAssistant, requestId);
                handlePluginConversation(pluginResultHandler, chatSessionDO, chatMessageDOList, requestId, chatRequest, docsInfo, uniqueAnswerId, scene, remoteAgent, answerUid, httpServletResponse);
            } else if(conversationType == ConversationTypeEnum.AUTO_TOOL_CALL){
                //function call
                Consumer<StreamResponseModel> pluginResultHandler = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, queryMessageDO,  segmentInfoList, docsInfo, null, queryIndex, generateIndexAssistant, requestId);
                handleFunctionConversation(pluginResultHandler, chatSessionDO, chatMessageDOList, requestId, chatRequest, docsInfo, uniqueAnswerId, scene, remoteAgent, answerUid, httpServletResponse);
            } else if (conversationType == ConversationTypeEnum.REPO_CHAT) {
                JSONObject repoChatInfo = JSON.parseObject(JSON.toJSONString(exactRepoInfoFromChatSession(chatSessionDO)));
                if(repoChatInfo == null){
                    throw new BizException(ResponseEnum.ANSWER_REPO_ARG_ERR,"由于缺少仓库信息，仓库问答终止");
                }
                JSONObject repoChatConfig = Optional.ofNullable(extraInfo).orElse(new JSONObject()).getJSONObject("repoChatConfig");
                JSONArray referenceInfoList = Optional.ofNullable(extraInfo).orElse(new JSONObject()).getJSONArray("references");
                List<CodeReference> codeReferences = null;
                if(repoChatConfig!=null){
                    repoChatInfo.put("repoChatConfig", repoChatConfig);
                }
                if(referenceInfoList!=null){
                    codeReferences = referenceInfoList.toJavaList(CodeReference.class);
                    repoChatInfo.put("references", codeReferences);
                }

                Consumer<StreamResponseModel> responseConsumer = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, queryMessageDO,  segmentInfoList, docsInfo, repoChatInfo,  queryIndex,generateIndexAssistant,requestId);
                handleRepoChatConversation(responseConsumer, chatSessionDO, requestId, chatRequest, docsInfo, uniqueAnswerId,false, remoteAgent, answerUid, repoChatConfig, codeReferences, httpServletResponse);
            }
            else if (conversationType == ConversationTypeEnum.CUSTOMIZE) {
                Consumer<StreamResponseModel> responseConsumer = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, queryMessageDO,  segmentInfoList, docsInfo, null,  queryIndex,generateIndexAssistant,requestId);
                handleCustomizeChatConversation(responseConsumer, chatSessionDO, requestId, chatRequest, docsInfo, uniqueAnswerId,true, remoteAgent, null, httpServletResponse);
            }else{
                Consumer<StreamResponseModel> responseConsumer = streamResponseModel -> handleConversationResult(streamResponseModel, chatSessionDO, queryMessageDO,  segmentInfoList, docsInfo, null,  queryIndex,generateIndexAssistant, requestId);
                handleCommonConversation(responseConsumer, chatSessionDO, requestId, chatRequest, docsInfo, uniqueAnswerId, scene, false, remoteAgent, answerUid, httpServletResponse);
            }
            // 成功后增加助手,插件,模型的相关使用次数
            addUsageCount(lastMessageDO, scene, modelName, needAddSceneUserCount, needAddModelUserCount);
        } finally {
            tbaseCacheService.releaseLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid);
        }
    }

    /**
     * 提交表单，继续对话
     * 先只支持function call类型的会话
     * @param httpServletResponse
     * @param uid
     * @param data
     */
    @Override
    public void continueConversation(HttpServletResponse httpServletResponse, String uid, JSONObject data, Boolean remoteAgent) {
        ChatMessageDO chatMessageDO = getChatMessageByUid(uid);
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(chatMessageDO.getSessionUid());
        SceneDO scene = sceneService.getSceneById(chatSessionDO.getSceneId());
        List<ChatMessageDO> chatMessageDOList = listChatMessage(chatMessageDO.getSessionUid(), true, false);
        // 弹出表单接着提问 然后点击提交表单 把表单之后的message过滤掉
        int index = 0;
        for (int i = 0; i < chatMessageDOList.size(); i++) {
            if (StrUtil.equals(chatMessageDOList.get(i).getUid(), uid)) {
                index = i;
                break;
            }
        }
        chatMessageDOList = chatMessageDOList.subList(0, index + 1);

        ChatMessageDO latestChatMessageDO = chatMessageDOList.get(chatMessageDOList.size() - 1);
        // 4.2 更改表单提交停止取消
        if(latestChatMessageDO == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "last message is not pausing, do not need continue");
        }

        String uniqueAnswerId = chatSessionDO.getUid()
                + "_" + chatMessageDO.getQueryIndex()
                + "_" + chatMessageDO.getGenerationIndex();

        //获取消息类型
        ConversationTypeEnum conversationType = getConversationType(scene);

        if (conversationType == ConversationTypeEnum.AUTO_TOOL_CALL) {
            // 组装上下文
            ChatCompletionRequest chatRequest = generateRequest(chatSessionDO, scene, chatMessageDOList, false);
            chatRequest.setStream(true);
            chatRequest.setModel(null);

            //这里特殊一些，已经有assistant消息了，所以取倒数第二条消息
            String query = chatRequest.getMessages().get(chatRequest.getMessages().size()-2).getContent();
            List<SegmentInfo> segmentInfoList = searchDoc(query, chatSessionDO);

            JSONObject docsInfo = ChatUtils.exactDocsInfoAndUpdateSegmentInfo(segmentInfoList);
            ChatUtils.addDocSearchResultToChatRequest(segmentInfoList, chatRequest);

            // 继续执行后续流程
            Consumer<StreamResponseModel> pluginResultHandler = streamResponseModel -> handleContinueConversationResult(streamResponseModel, chatMessageDO, segmentInfoList, docsInfo, null);
            handleFunctionContinueConversation(pluginResultHandler, data, chatSessionDO, chatMessageDOList, chatMessageDO, chatRequest, docsInfo, uniqueAnswerId, scene, remoteAgent, httpServletResponse);
        }
    }

    @Override
    public List<SegmentInfo> searchDoc(String query, ChatSessionDO chatSessionDO) {
        List<SegmentInfo> segmentInfoList = documentHandleService.recallSegment(chatSessionDO.getUid(), query);
        LOGGER.info("doc search, query: {}, search result size:{}, detail:{}", query, segmentInfoList.size(), JSON.toJSONString(segmentInfoList));
        return segmentInfoList;
    }

    /**
     * 判断conversation的类型
     * @param scene
     * @return
     */
    @Override
    public ConversationTypeEnum getConversationType(SceneDO scene){
        return getConversationType(scene, false);
    }


    private ConversationTypeEnum getConversationType(SceneDO scene, Boolean tryRepoSearch){
        if(scene == null){
            return ConversationTypeEnum.NORMAL;
        }
        FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(scene);
        if(scene.getMode() == SceneChatModeEnum.COMMON.getMode()){
            return ConversationTypeEnum.NORMAL;
        }
        if(functionCallConfig.getCustomizeChatConfig()!=null && StringUtils.isNotBlank((String)functionCallConfig.getCustomizeChatConfig().get("url"))){
            return ConversationTypeEnum.CUSTOMIZE;
        }
        if (Boolean.TRUE.equals(tryRepoSearch)) {
            // mode为2且包含会话上下文包含repo信息才会走仓库问答
            // 如果上下文没有这些信息会走继续走下方的判断
            return ConversationTypeEnum.REPO_CHAT;
        }

        String pluginListStr = scene.getPluginList();
        List<Long> pluginIdList = JSON.parseArray(pluginListStr, Long.class);
        // functionCallConfig配置了repoChat也会默认走仓库问答
        if(functionCallConfig.getRepoChat()){
            return ConversationTypeEnum.REPO_CHAT;
        }

        if(pluginIdList.isEmpty()) {
            return ConversationTypeEnum.NORMAL;
        }else if(pluginIdList.size() == 1) {
            // 只有一个插件，要判断是智能匹配还是固定匹配
            if (functionCallConfig.getCallFunctionEveryRound()){
                return ConversationTypeEnum.FIX_TOOL_CALL;
            }else {
                return ConversationTypeEnum.AUTO_TOOL_CALL;
            }
        }else{
            return ConversationTypeEnum.AUTO_TOOL_CALL;
        }
    }


    private void handleCommonConversation(Consumer<StreamResponseModel> responseConsumer,
                                          ChatSessionDO chatSessionDO,
                                          String requestId,
                                          ChatCompletionRequest chatRequest,
                                          JSONObject docsInfo,
                                          String uniqueAnswerId,
                                          SceneDO scene,
                                          boolean isRegenerate,
                                          Boolean remoteAgent,
                                          String answerUid,
                                          HttpServletResponse httpServletResponse){
        AlgoBackendDO algoBackendDO;
        if (scene != null && scene.getMode() == 0){
            algoBackendDO = algoBackendService.getAlgoBackendByName(scene.getModel());
        }else{
            algoBackendDO = algoBackendService.getAlgoBackendByName(chatSessionDO.getModel());
        }

        overwriteModelConfig(chatSessionDO.getUid(), algoBackendDO);
        if (algoBackendDO == null) {
            throw new BizException(ResponseEnum.MODEL_NOT_EXISTED);
        }
        if (!algoBackendDO.getEnable() && !chatSessionDO.getSceneTest()) {
            throw new BizException(ResponseEnum.MODEL_UNSERVICEABLE);
        }

        UserAuthDO userAuthDO = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);

        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = (chatStreamPartResponse)-> processStreamResponse(new NewPluginStreamPartResponse(chatStreamPartResponse), docsInfo, remoteAgent, chatSessionDO, httpServletResponse);
        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey)-> noneSerializationCacheManager.del(tBaseKey);
        Consumer<String> needDelTBaseKeyConsumerQueue = (tBaseKey) -> streamDataQueueUtilService.del(tBaseKey);
        GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId, AppConstants.CODEGPT_TOKEN_USER, false, algoBackendDO,
                chatRequest
                , responseConsumer, isRegenerate);
        params.setChatStreamPartResponseHandler(chatStreamPartResponseConsumer);
        params.setNeedDelTBaseKey(codeGPTDrmConfig.isModelEnableQueue() ? needDelTBaseKeyConsumerQueue : needDelTBaseKeyConsumer);
        // 流式会用到这个Handler
        params.setUserOriginalModel(algoBackendDO.getModel());
        params.setUniqueAnswerId(uniqueAnswerId);
        if (StringUtils.isNotBlank(answerUid)) {
            params.setAnswerUid(answerUid);
        } else {
            params.setAnswerUid(ShortUid.getUid());
        }
        params.setRequestUserId(userAuthDO.getId());
        //获取会话设置的生效环境
        String modelEnv = "auto";
        JSONObject sessionConfig = chatSessionManageService.getSessionConfig(chatSessionDO.getUid(),null);
        if (sessionConfig.containsKey(AppConstants.IMPL_CONFIG)){
            JSONObject sceneImplConfig = JSONObject.parseObject(sessionConfig.getString(AppConstants.IMPL_CONFIG));
            if (sceneImplConfig.containsKey("modelEnv")){
                modelEnv = sceneImplConfig.getString("modelEnv");
            }
        }
        params.setModelEnv(modelEnv);
        if(params.getChatCompletionRequest().getMessages() != null){
            AlgoModelUtilService.updateDefaultSystemPrompt(params.getChatCompletionRequest(), algoBackendDO);
        }
        //流式传输，要把response设置为流式
        ChatUtils.setServletToEventStream(httpServletResponse);
        CHAT_LOGGER.info("chat completion request model {}, user:{}, id:{}, content:{}", algoBackendDO.getModel(), AppConstants.CODEGPT_TOKEN_USER, requestId, JSON.toJSONString(chatRequest));
        AlgoModelExecutor.getInstance().executorStreamChat(algoBackendDO, params);
    }

    /**
     * 处理插件的对话流程
     */
    private void handlePluginConversation(Consumer<StreamResponseModel> pluginResultHandler,
                                          ChatSessionDO chatSessionDO,
                                          List<ChatMessageDO> chatMessageDOList,
                                          String requestId,
                                          ChatCompletionRequest chatRequest,
                                          JSONObject docsInfo,
                                          String uniqueAnswerId,
                                          SceneDO scene,
                                          Boolean remoteAgent,
                                          String answerUid,
                                          HttpServletResponse httpServletResponse){
        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey)-> noneSerializationCacheManager.del(tBaseKey);
        Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer = (pluginStreamPartResponse)-> processStreamResponse(pluginStreamPartResponse, docsInfo, remoteAgent, chatSessionDO, httpServletResponse);

        UserAuthDO userAuthDO = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);
        PluginServiceRequestContext params = new PluginServiceRequestContext(chatSessionDO,requestId, AppConstants.CODEGPT_TOKEN_USER,userAuthDO, false, null, chatRequest
                ,pluginResultHandler, false);

        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        params.setChatMessageDOList(chatMessageDOList);
        params.setPluginStreamPartResponseConsumer(pluginStreamPartResponseConsumer);
        // 流式会用到这个Handler
        params.setUniqueAnswerId(uniqueAnswerId);
        if (StringUtils.isNotBlank(answerUid)) {
            params.setAnswerUid(answerUid);
        } else {
            params.setAnswerUid(ShortUid.getUid());
        }
        params.setPluginParams(new HashMap<>());
        params.setPluginIndex(0);

        //获取插件，当前只支持单插件模式
        String pluginListStr = scene.getPluginList();
        List<Long> pluginIdList = JSON.parseArray(pluginListStr, Long.class);
        PluginDO pluginDO = pluginService.getPluginById(pluginIdList.get(0));
        params.setPluginDO(pluginDO);
        PluginInfo pluginInfo = new PluginInfo(pluginDO.getId(), pluginDO.getName(), pluginDO.getDescription());
        params.setPluginInfo(pluginInfo);

        //流式传输，要把response设置为流式
        ChatUtils.setServletToEventStream(httpServletResponse);
        pluginWorkflowService.streamChat(params, pluginDO);
    }

    /**
     * 处理function call的对话流程
     */
    private void handleFunctionConversation(Consumer<StreamResponseModel> pluginResultHandler,
                                            ChatSessionDO chatSessionDO,
                                            List<ChatMessageDO> chatMessageDOList,
                                            String requestId,
                                            ChatCompletionRequest chatRequest,
                                            JSONObject docsInfo,
                                            String uniqueAnswerId,
                                            SceneDO scene,
                                            Boolean remoteAgent,
                                            String answerUid,
                                            HttpServletResponse httpServletResponse) {
        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey)-> noneSerializationCacheManager.del(tBaseKey);
        Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer =
                (pluginStreamPartResponse) -> processStreamResponse(pluginStreamPartResponse, docsInfo, remoteAgent, chatSessionDO, httpServletResponse);
        UserAuthDO userAuthDO = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);

        PluginServiceRequestContext params = new PluginServiceRequestContext(chatSessionDO, requestId, AppConstants.CODEGPT_TOKEN_USER, userAuthDO, false, null, chatRequest
                ,pluginResultHandler, false);

        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        params.setChatMessageDOList(chatMessageDOList);
        params.setPluginStreamPartResponseConsumer(pluginStreamPartResponseConsumer);
        // 流式会用到这个Handler
        params.setUniqueAnswerId(uniqueAnswerId);
        if (StringUtils.isNotBlank(answerUid)) {
            params.setAnswerUid(answerUid);
        } else {
            params.setAnswerUid(ShortUid.getUid());
        }
        params.setPluginParams(new HashMap<>());

        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(sceneService.getFunctionCallConfig(scene).getFunctionCallModel());
        params.setAlgoBackendDO(algoBackendDO);

        params.setSceneDO(scene);
        String pluginListStr = scene.getPluginList();
        List<Long> pluginIdList = JSON.parseArray(pluginListStr, Long.class);
        List<PluginDO> pluginDO = pluginService.getPluginByIdList(pluginIdList,false);
        //流式传输，要把response设置为流式
        ChatUtils.setServletToEventStream(httpServletResponse);
        functionCallService.streamChat(params, pluginDO);
    }


    /**
     * 处理提交表单后的function call的对话流程
     */
    private void handleFunctionContinueConversation(Consumer<StreamResponseModel> pluginResultHandler,
                                            JSONObject data,
                                            ChatSessionDO chatSessionDO,
                                            List<ChatMessageDO> chatMessageDOList,
                                            ChatMessageDO currentMessageDO,
                                            ChatCompletionRequest chatRequest,
                                            JSONObject docsInfo,
                                            String uniqueAnswerId,
                                            SceneDO scene,
                                            Boolean remoteAgent,
                                            HttpServletResponse httpServletResponse) {
        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey)-> noneSerializationCacheManager.del(tBaseKey);
        Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer = (pluginStreamPartResponse)-> processStreamResponse(pluginStreamPartResponse, docsInfo, remoteAgent, chatSessionDO, httpServletResponse);
        UserAuthDO userAuthDO = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);
        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(sceneService.getFunctionCallConfig(scene).getFunctionCallModel());

        PluginServiceRequestContext params = new PluginServiceRequestContext(chatSessionDO, chatMessageDOList, chatSessionDO.getUid(), AppConstants.CODEGPT_TOKEN_USER, userAuthDO, chatRequest,pluginResultHandler);
        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        params.setPluginStreamPartResponseConsumer(pluginStreamPartResponseConsumer);
        params.setUniqueAnswerId(uniqueAnswerId);
        params.setAnswerUid(currentMessageDO.getUid());
        params.setPluginParams(data);
        params.setAlgoBackendDO(algoBackendDO);
        params.setSceneDO(scene);

        String pluginListStr = scene.getPluginList();
        List<Long> pluginIdList = JSON.parseArray(pluginListStr, Long.class);
        List<PluginDO> pluginDO = pluginService.getPluginByIdList(pluginIdList,false);
        //流式传输，要把response设置为流式
        ChatUtils.setServletToEventStream(httpServletResponse);
        functionCallService.continueStreamChat(params, pluginDO);
    }

    /**
     * 仓库问答对话流程
     *
     * <AUTHOR>
     * @since 2024.07.12
     * @param responseConsumer responseConsumer
     * @param chatSessionDO chatSessionDO
     * @param requestId requestId
     * @param chatRequest chatRequest
     * @param docsInfo docsInfo
     * @param uniqueAnswerId uniqueAnswerId
     * @param isRegenerate isRegenerate
     * @param remoteAgent remoteAgent
     * @param answerUid answerUid
     * @param httpServletResponse httpServletResponse
     */
    private void handleRepoChatConversation(Consumer<StreamResponseModel> responseConsumer,
                                            ChatSessionDO chatSessionDO,
                                            String requestId,
                                            ChatCompletionRequest chatRequest,
                                            JSONObject docsInfo,
                                            String uniqueAnswerId,
                                            boolean isRegenerate,
                                            Boolean remoteAgent,
                                            String answerUid,
                                            JSONObject repoChatConfig,
                                            List<CodeReference> codeReferences,
                                            HttpServletResponse httpServletResponse) {
        // repoInfo一定不会为空
        RepoInfoModel repoInfo = exactRepoInfoFromChatSession(chatSessionDO);
        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(chatSessionDO.getModel());

        UserAuthDO userAuthDO = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);

        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = (chatStreamPartResponse) -> processStreamResponse(new NewPluginStreamPartResponse(chatStreamPartResponse), docsInfo, remoteAgent, chatSessionDO, httpServletResponse);
        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey) -> noneSerializationCacheManager.del(tBaseKey);

        String userId = String.format("code_copilot_%s", userAclService.getCurrentUser().getEmpId());
        GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId, userId, false, null, chatRequest
                , responseConsumer, isRegenerate);

        params.setRepoInfo(repoInfo);
        params.setChatStreamPartResponseHandler(chatStreamPartResponseConsumer);
        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        // 流式会用到这个Handler
        params.setUniqueAnswerId(uniqueAnswerId);
        if (StringUtils.isNotBlank(answerUid)) {
            params.setAnswerUid(answerUid);
        } else {
            params.setAnswerUid(ShortUid.getUid());
        }
        params.setRequestUserId(userAuthDO.getId());
        params.setAlgoBackendDO(algoBackendDO);

        //流式传输，要把response设置为流式
        ChatUtils.setServletToEventStream(httpServletResponse);
        CHAT_LOGGER.info("repo chat: user:{}, id:{}, content:{}", userId, requestId, JSON.toJSONString(chatRequest));
        repoChatService.streamChat(params, repoChatConfig, codeReferences, httpServletResponse);
    }
    /**
     * 自定义接口问答对话流程
     *
     * <AUTHOR>
     * @since 2024.07.12
     * @param responseConsumer responseConsumer
     * @param chatSessionDO chatSessionDO
     * @param requestId requestId
     * @param chatRequest chatRequest
     * @param docsInfo docsInfo
     * @param uniqueAnswerId uniqueAnswerId
     * @param isRegenerate isRegenerate
     * @param remoteAgent remoteAgent
     * @param answerUid answerUid
     * @param httpServletResponse httpServletResponse
     */
    private void handleCustomizeChatConversation(Consumer<StreamResponseModel> responseConsumer,
                                            ChatSessionDO chatSessionDO,
                                            String requestId,
                                            ChatCompletionRequest chatRequest,
                                            JSONObject docsInfo,
                                            String uniqueAnswerId,
                                            boolean isRegenerate,
                                            Boolean remoteAgent,
                                            String answerUid,
                                            HttpServletResponse httpServletResponse) {
        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(chatSessionDO.getModel());
        UserAuthDO userAuthDO = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);
        Map<String, Object> implConfigMap = AlgoBackendUtil.getImplConfigMap(algoBackendDO);
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = (chatStreamPartResponse) -> processStreamResponse(new NewPluginStreamPartResponse(chatStreamPartResponse), docsInfo, remoteAgent, chatSessionDO, httpServletResponse);
        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey) -> noneSerializationCacheManager.del(tBaseKey);
        GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId, AppConstants.CODEGPT_TOKEN_USER, false, null, chatRequest
                , responseConsumer, isRegenerate);
        SceneDO scene = sceneService.getSceneById(chatSessionDO.getSceneId());
        FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(scene);
        Map<String, Object> customizeChatConfig = functionCallConfig.getCustomizeChatConfig();
        params.setCustomizeUrl((String) customizeChatConfig.get("url"));
        if(customizeChatConfig.containsKey(AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME)){
            implConfigMap.put(AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME, customizeChatConfig.get(AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME));
        }else {
            implConfigMap.put(AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME, codeGPTDrmConfig.getPluginLLMStageFirstStreamDataWaitTime());
        }
        if(customizeChatConfig.containsKey(AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME)){
            implConfigMap.put(AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME, customizeChatConfig.get(AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME));
        }else {
            implConfigMap.put(AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME, codeGPTDrmConfig.getPluginCommonStreamDataWaitTime());
        }
        algoBackendDO.setImplConfig(JSON.toJSONString(implConfigMap));
        params.setChatStreamPartResponseHandler(chatStreamPartResponseConsumer);
        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        // 流式会用到这个Handler
        params.setUniqueAnswerId(uniqueAnswerId);
        if (StringUtils.isNotBlank(answerUid)) {
            params.setAnswerUid(answerUid);
        } else {
            params.setAnswerUid(ShortUid.getUid());
        }
        params.setRequestUserId(userAuthDO.getId());
        params.setAlgoBackendDO(algoBackendDO);

        //流式传输，要把response设置为流式
        ChatUtils.setServletToEventStream(httpServletResponse);
        CHAT_LOGGER.info("customize chat: user:{}, id:{}, content:{}", AppConstants.CODEGPT_TOKEN_USER, requestId, JSON.toJSONString(chatRequest));
        customizeChatService.streamChat(params);
    }

    /**
     * 将回话模型配置覆盖到模型配置
     * @param sessionUid
     * @param algoBackendDO
     */
    private void overwriteModelConfig(String sessionUid,AlgoBackendDO algoBackendDO){
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidEqualTo(sessionUid).andDeletedEqualTo((byte)0);
        ChatSessionDO chatSessionDO = chatSessionDOMapper.selectByExample(chatSessionDOExample).get(0);
        String modelConfig = chatSessionDO.getModelConfig();
        if (StringUtils.isNotBlank(modelConfig)){
            JSONObject modelImplConfig = JSON.parseObject(modelConfig);
            if (modelImplConfig.get(AppConstants.MAX_TOKEN) != null){
                Object maxToken = modelImplConfig.get(AppConstants.MAX_TOKEN);
                if (maxToken instanceof String){
                    algoBackendDO.setMaxToken(Integer.valueOf((String) maxToken));
                }else {
                    algoBackendDO.setMaxToken((Integer) maxToken);
                }
            }
            if (modelImplConfig.get(AppConstants.MAX_ROUND) != null){
                Object maxRound = modelImplConfig.get(AppConstants.MAX_ROUND);
                if (maxRound instanceof String){
                    algoBackendDO.setMaxRound(Integer.valueOf((String) maxRound));
                }else {
                    algoBackendDO.setMaxRound((Integer) maxRound);
                }

            }
            if (modelImplConfig.get(AppConstants.IMPL_CONFIG) != null){
                JSONObject sessionImplConfig = JSONObject.parseObject(JSON.toJSONString(modelImplConfig.get(AppConstants.IMPL_CONFIG)));
                JSONObject algoBackendImplConfig = JSONObject.parseObject(algoBackendDO.getImplConfig());
                algoBackendImplConfig.putAll(sessionImplConfig);
                algoBackendDO.setImplConfig(JSONObject.toJSONString(algoBackendImplConfig));
            }
        }
    }

    private void handleConversationResult(StreamResponseModel streamResponseModel,
                                          ChatSessionDO chatSessionDO,
                                          ChatMessageDO queryMessageDO,
                                          List<SegmentInfo> docSearchResult,
                                          JSONObject docsInfo,
                                          JSONObject repoChatInfo,
                                          long queryIndex,
                                          long generationIndex,String uid){
        ChatMessageDO assistantMessageDO = new ChatMessageDO();
        // 如果仓库问答有refresh内容则去除originAnswer内容
        replaceAnswerContent(streamResponseModel);
        assistantMessageDO.setContent(streamResponseModel.getAnswerMessage().getContent());
        CHAT_LOGGER.info("handleConversationResult,uid:{},content:{}",uid,streamResponseModel.getAnswerMessage().getContent());
        CHAT_LOGGER.info("streamResponseModel is {}",JSON.toJSONString(streamResponseModel));
        assistantMessageDO.setRole(ChatRoleEnum.ASSISTANT.getName());
        assistantMessageDO.setQueryIndex(queryIndex);
        assistantMessageDO.setGenerationIndex(generationIndex);
        assistantMessageDO.setUserId(chatSessionDO.getUserId());
        assistantMessageDO.setSessionUid(chatSessionDO.getUid());
        assistantMessageDO.setUid(streamResponseModel.getAnswerUid());
        assistantMessageDO.setServiceAbnormalResp(streamResponseModel.getServiceAbnormalResp());
        assistantMessageDO.setHitCache(streamResponseModel.getCached() != null && streamResponseModel.getCached() ? (byte) 1 : (byte) 0);
        assistantMessageDO.setHitQuery(streamResponseModel.getHitQuery());
        assistantMessageDO.setVersion(codeGPTDrmConfig.getChatMessageFormatVersion());
        assistantMessageDO.setParent(uid);
        mergeInfoToRuntimeInfo(streamResponseModel, docSearchResult, docsInfo, repoChatInfo);
        assistantMessageDO.setRuntimeInfo(streamResponseModel.getRuntimeInfo());
        // 将errorMsg和clear存入数据库
        assistantMessageDO.setErrorMsg(streamResponseModel.getErrorMsg());
        assistantMessageDO.setClear(streamResponseModel.getClear());
        CHAT_LOGGER.info("chatMessageDO:{}", JSON.toJSONString(assistantMessageDO));

        // 判断是否要弹出表单，如果弹表单，需要setStatus
        if (StringUtils.equals(ChatMessageStatusEnum.PAUSE.name(), streamResponseModel.getMessageStatus())) {
            assistantMessageDO.setStatus(ChatMessageStatusEnum.PAUSE.name());
        } else {
            assistantMessageDO.setStatus(ChatMessageStatusEnum.FINISH.name());
        }
        if (streamResponseModel.getPluginLogGroup() != null) {
            String pluginLog = JSON.toJSONString(streamResponseModel.getPluginLogGroup(), SerializerFeature.DisableCircularReferenceDetect);
            assistantMessageDO.setPluginLog(pluginLog);
            recordPluginLog(streamResponseModel.getPluginLogGroup());
        }

        // 审核不通过时保存错误码
        if (streamResponseModel.getCheckResultModel() != null && algoModelUtilService.hasCheckFailed(streamResponseModel.getCheckResultModel())) {
            if(queryMessageDO != null){
                queryMessageDO.setReviewResult(JSON.toJSONString(streamResponseModel.getCheckResultModel()));
            }
            assistantMessageDO.setReviewResult(JSON.toJSONString(streamResponseModel.getCheckResultModel()));
        }

        if(queryMessageDO!=null){
            chatSessionManageService.insertPairChatMessage(queryMessageDO, assistantMessageDO);
        }else{
            chatMessageDOMapper.insertSelective(assistantMessageDO);
        }
    }

    private void handleContinueConversationResult(StreamResponseModel streamResponseModel,
                                                  ChatMessageDO chatMessageDO,
                                                  List<SegmentInfo> docSearchResult,
                                                  JSONObject repoChatInfo,
                                                  JSONObject docsInfo){

        // 判断是否要弹出表单，如果弹表单，需要setStatus
        if (StringUtils.equals(ChatMessageStatusEnum.PAUSE.name(), streamResponseModel.getMessageStatus())) {
            chatMessageDO.setStatus(ChatMessageStatusEnum.PAUSE.name());
        } else {
            chatMessageDO.setStatus(ChatMessageStatusEnum.FINISH.name());
        }

        chatMessageDO.setContent(streamResponseModel.getAnswerMessage().getContent());
        chatMessageDO.setServiceAbnormalResp(streamResponseModel.getServiceAbnormalResp());

        mergeInfoToRuntimeInfo(streamResponseModel, docSearchResult, docsInfo, repoChatInfo);
        chatMessageDO.setRuntimeInfo(streamResponseModel.getRuntimeInfo());

        // pluginLog更新
        if (streamResponseModel.getPluginLogGroup() != null) {
            String pluginLog = JSON.toJSONString(streamResponseModel.getPluginLogGroup(), SerializerFeature.DisableCircularReferenceDetect);
            chatMessageDO.setPluginLog(pluginLog);
            recordPluginLog(streamResponseModel.getPluginLogGroup());
        }

        // 审核不通过时保存错误码
        if (streamResponseModel.getCheckResultModel() != null && algoModelUtilService.hasCheckFailed(streamResponseModel.getCheckResultModel())) {
            chatMessageDO.setReviewResult(JSON.toJSONString(streamResponseModel.getCheckResultModel()));
        }

        // 更新会话信息
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andUidEqualTo(chatMessageDO.getUid());
        int count = chatMessageDOMapper.updateByExampleSelective(chatMessageDO, chatMessageDOExample);
        if (count != 1) {
            throw new BizException(ResponseEnum.UPDATE_DATABASE_FAILED);
        }
    }

    @Override
    public MaYiDevMessageCountModel getMaYiDeveloperCount(Date gmtCreateBegin, Date gmtCreateEnd, String empId, boolean needEmpList) {
        MaYiDevMessageCountModel maYiDevMessageCountModel = new MaYiDevMessageCountModel();
        Long userId = null;
        if (StringUtils.isNotBlank(empId)) {
            UserAuthDO userAuthDO = userAclService.queryUserByEmpId(empId);
            if (userAuthDO == null) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "empId not exist");
            }
            userId = userAuthDO.getId();
        }
        int totalQuestionCount = chatMessageManualMapper.selectTotalAnswerCount(gmtCreateBegin, gmtCreateEnd, userId, null);
        int totalLikeOrObjectionCount = chatMessageManualMapper.selectTotalAnswerCount(gmtCreateBegin, gmtCreateEnd, userId, true);
        maYiDevMessageCountModel.setTotalQuestionCount(totalQuestionCount);
        maYiDevMessageCountModel.setTotalLikeOrObjectionCount(totalLikeOrObjectionCount);
        List<MaYiDevMessageCountModel.EmpMessageCount> empMessageCountList = new ArrayList<>();
        maYiDevMessageCountModel.setEmpList(empMessageCountList);
        if (!needEmpList || totalQuestionCount == 0) {
            return maYiDevMessageCountModel;
        }
        List<UserMessageCountDO> questionCountList = chatMessageManualMapper.selectAnswerMessageCountGroupByUserId(gmtCreateBegin, gmtCreateEnd, userId, null);
        List<UserMessageCountDO> likeOrObjectionCountList = chatMessageManualMapper.selectAnswerMessageCountGroupByUserId(gmtCreateBegin, gmtCreateEnd, userId, true);
        Map<Long, UserMessageCountDO> likeOrObjectionCountMap = buildLikeOrObjectionCountMap(likeOrObjectionCountList);
        for (UserMessageCountDO questionCount : questionCountList) {
            MaYiDevMessageCountModel.EmpMessageCount empMessageCount = new MaYiDevMessageCountModel.EmpMessageCount();
            String dbEmpId = codeFuseUserAuthService.selectEmpIdByUserId(questionCount.getUserId());
            empMessageCount.setEmpId(dbEmpId);
            empMessageCount.setQuestionCount(questionCount.getMessageCount());
            int likeOrObjectionCount = 0;
            if (likeOrObjectionCountMap.containsKey(questionCount.getUserId())) {
                likeOrObjectionCount = likeOrObjectionCountMap.get(questionCount.getUserId()).getMessageCount();
            }
            empMessageCount.setLikeOrObjectionCount(likeOrObjectionCount);
            empMessageCountList.add(empMessageCount);
        }
        return maYiDevMessageCountModel;
    }

    private Map<Long, UserMessageCountDO> buildLikeOrObjectionCountMap(List<UserMessageCountDO> likeOrObjectionCountList) {
        if (CollectionUtils.isEmpty(likeOrObjectionCountList)) {
            return new HashMap<>(0);
        }
        Map<Long, UserMessageCountDO> userMessageCountDOMap = new HashMap<>(likeOrObjectionCountList.size());
        for (UserMessageCountDO userMessageCountDO : likeOrObjectionCountList) {
            userMessageCountDOMap.put(userMessageCountDO.getUserId(), userMessageCountDO);
        }
        return userMessageCountDOMap;
    }

    /**
     * 获取最新生成的一条消息的uid
     *
     * @param sessionUid 会话uid
     * @return
     */
    @Override
    public List<ChatMessageDO> getLatestAssistantMessage(String sessionUid) {
        PageHelper.startPage(0, 1);
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria()
                .andSessionUidEqualTo(sessionUid)
                .andDeletedEqualTo((byte) 0)
                .andRoleEqualTo(ChatRoleEnum.USER.getName());

        chatMessageDOExample.setOrderByClause("query_index desc, generation_index desc");
        List<ChatMessageDO> chatMessageDOS = chatMessageDOMapper.selectByExample(chatMessageDOExample);
        PageHelper.startPage(0, 1);
        ChatMessageDOExample chatMessageDOExampleUser = new ChatMessageDOExample();
        chatMessageDOExampleUser.createCriteria()
                .andSessionUidEqualTo(sessionUid)
                .andDeletedEqualTo((byte) 0)
                .andRoleEqualTo(ChatRoleEnum.ASSISTANT.getName());

        chatMessageDOExampleUser.setOrderByClause("query_index desc,gmt_create desc");
        chatMessageDOS.addAll(chatMessageDOMapper.selectByExample(chatMessageDOExampleUser));
        return chatMessageDOS;
    }

    /**
     * 获取会话内容全部对话内容
     * showMultiGeneratedAnswer ==  ture
     * chatMessageDOList 列表示例
     *              queryIndex generationIndex
     * user             1          0
     * ASSISTANT        1          0
     * user             1          1
     * ASSISTANT        1          0
     * ASSISTANT        1          1
     * ASSISTANT        1          2
     * user             1          2
     * ASSISTANT        1          0
     * user             2          0
     * ASSISTANT        2          0
     * showMultiGeneratedAnswer ==  false时为一轮中最后一个user和对应的ASSISTANT 列表
     *
     * @param sessionUid               会话uid
     * @param showMultiGeneratedAnswer 是否展示生成多次的答案
     * @return
     */
    @Override
    public List<ChatMessageDO> listChatMessage(String sessionUid, boolean showMultiGeneratedAnswer, boolean rewriteFileAnnotation) {
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andSessionUidEqualTo(sessionUid);

        chatMessageDOExample.setOrderByClause("id,query_index, role desc, generation_index");
        List<ChatMessageDO> chatMessageDOList = chatMessageDOMapper.selectByExample(chatMessageDOExample);
        for (ChatMessageDO chatMessageDO: chatMessageDOList){
            if(!ChatRoleEnum.ASSISTANT.getName().equalsIgnoreCase(chatMessageDO.getRole())){
                continue;
            }

            String content = chatMessageDO.getContent();
            String runtimeInfoStr = chatMessageDO.getRuntimeInfo();
            JSONObject runtimeInfo = runtimeInfoStr==null?new JSONObject():JSONObject.parseObject(runtimeInfoStr);
            JSONObject docsInfo = runtimeInfo.getJSONObject("docsInfo");
            if(rewriteFileAnnotation){
                content = rewriteFileAnnotation(content, docsInfo, FileAnnotationTypeEnum.valueOf(codeGPTDrmConfig.getFileAnnotationType()));
            }
            chatMessageDO.setContent(content);
        }

        if (CollectionUtils.isEmpty(chatMessageDOList)) {
            return new ArrayList<>();
        }

        if (showMultiGeneratedAnswer) {
            return chatMessageDOList;
        } else {
            Map<Long, List<ChatMessageDO>> latestMessageMap = new HashMap<>();
            Long lastQueryIndex = null;
            ChatMessageDO lastUserMessageDO = null;
            ChatMessageDO lastAssistantMessageDO = null;

            for (ChatMessageDO chatMessageDO : chatMessageDOList) {
                Long currentQueryIndex = chatMessageDO.getQueryIndex();
                if (lastQueryIndex == null || !lastQueryIndex.equals(currentQueryIndex)) {
                    lastQueryIndex = currentQueryIndex;
                    lastUserMessageDO = null;
                    lastAssistantMessageDO = null;
                }
                if (ChatRoleEnum.USER.getName().equals(chatMessageDO.getRole())) {
                    lastUserMessageDO = chatMessageDO;
                } else {
                    lastAssistantMessageDO = chatMessageDO;
                }
                if (lastUserMessageDO != null && lastAssistantMessageDO != null) {
                    latestMessageMap.put(lastQueryIndex, Arrays.asList(lastUserMessageDO, lastAssistantMessageDO));
                }
            }
            if (lastUserMessageDO != null && lastAssistantMessageDO != null) {
                latestMessageMap.put(lastQueryIndex, Arrays.asList(lastUserMessageDO, lastAssistantMessageDO));
            }
            return latestMessageMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        }
    }

    @Override
    public ChatMessageDO getChatMessageByUid(String uid) {
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andUidEqualTo(uid);
        List<ChatMessageDO> chatMessageDOList = chatMessageDOMapper.selectByExample(chatMessageDOExample);
        if (CollectionUtils.isEmpty(chatMessageDOList)) {
            LOGGER.warn("get chat messages is empty by uid : {}", uid);
            throw new BizException(ResponseEnum.MESSAGE_IS_NOT_EXIST);
        }
        return chatMessageDOList.get(0);
    }

    @Override
    public List<ChatMessageDO> getChatMessagePairByUid(String uid) {
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andUidEqualTo(uid);
        List<ChatMessageDO> chatMessageDOList = chatMessageDOMapper.selectByExample(chatMessageDOExample);
        if (CollectionUtils.isEmpty(chatMessageDOList)) {
            throw new BizException(ResponseEnum.MESSAGE_IS_NOT_EXIST);
        }
        ChatMessageDO chatMessageDO = chatMessageDOList.get(0);

        ChatMessageDOExample chatMessageDOReq = new ChatMessageDOExample();
        chatMessageDOReq.createCriteria().andSessionUidEqualTo(chatMessageDO.getSessionUid());
        chatMessageDOReq.createCriteria().andQueryIndexEqualTo(chatMessageDO.getQueryIndex());
        return chatMessageDOMapper.selectByExample(chatMessageDOReq);
    }


    /**
     * 更新聊天消息
     *
     * @param codeGPTVoteRequestBean uid必须要有
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateChatMessage(CodeGPTVoteRequestBean codeGPTVoteRequestBean) {
        // 先把历史的comment查出来,再用最新的进行更新
        ChatMessageDO obChatMessageDO = getChatMessageByUid(codeGPTVoteRequestBean.getUid());
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        chatMessageDO.setComment(getVoteCommentJson(obChatMessageDO.getComment(), codeGPTVoteRequestBean));
        if (codeGPTVoteRequestBean.getVote() != null) {
            chatMessageDO.setVote(Long.valueOf(codeGPTVoteRequestBean.getVote()));
        }
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andUidEqualTo(codeGPTVoteRequestBean.getUid());
        int count = chatMessageDOMapper.updateByExampleSelective(chatMessageDO, chatMessageDOExample);
        if (count != 1) {
            LOGGER.warn("更新数据库失败,实际更新成功:{}条,不符合预期,回滚数据,codeGPTVoteRequestBean:{}", count, JSONObject.toJSONString(codeGPTVoteRequestBean));
            throw new BizException(ResponseEnum.UPDATE_DATABASE_FAILED);
        }
    }

    @Override
    public int putLanguages(String uid, String languages) {
        if (StringUtils.isAnyEmpty(uid,languages)){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        chatMessageDO.setLanguages(languages);
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andUidEqualTo(uid).andDeletedEqualTo((byte) 0);
        return chatMessageDOMapper.updateByExampleSelective(chatMessageDO, chatMessageDOExample);
    }

    @Override
    public JSONArray getMessageReference(String uid) {
        // 优先从缓存中获取数据
        Serializable tBaseRet = refreshableCommonTbaseCacheManager.get(AppConstants.CODEGENCORE_MESSAGE_MORE_REFERENCE + "_" + uid);
        if (tBaseRet != null) {
            return (JSONArray) tBaseRet;
        }
        // 缓存中没有的话再从数据源获取数据
        JSONArray result = new JSONArray();
        String mockReferenceDataStr = codeGPTDrmConfig.getMockReferenceDataList();
        List<JSONObject> mockReferenceDataList = JSON.parseArray(mockReferenceDataStr, JSONObject.class);
        Random random = new Random();
        int count = random.nextInt(mockReferenceDataList.size()) + 1;
        for (int j = 0; j < count; j++) {
            result.add(mockReferenceDataList.get(j));
        }
        // 存入缓存
        refreshableCommonTbaseCacheManager.set(AppConstants.CODEGENCORE_MESSAGE_MORE_REFERENCE + "_" + uid, result);
        return result;
    }

    @Override
    public void deleteAllMessage(String sessionUid) {
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andSessionUidEqualTo(sessionUid);
        ChatMessageDO record = new ChatMessageDO();
        record.setDeleted((byte) 1);
        chatMessageDOMapper.updateByExampleSelective(record, chatMessageDOExample);
    }

    @Override
    public int updateContentReviewResult(String messageUid, Pair<ReviewPlatformEnum, ReviewResultModel> reviewResultModelPair) {
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andUidEqualTo(messageUid);
        List<ChatMessageDO> chatMessageDOList = chatMessageDOMapper.selectByExample(chatMessageDOExample);
        if (CollectionUtils.isEmpty(chatMessageDOList)) {
            return 0;
        }
        ChatMessageDO chatMessageDO = chatMessageDOList.get(0);
        CheckResultModel checkResultModel;
        if (StringUtils.isNotBlank(chatMessageDO.getReviewResult())) {
            checkResultModel = JSON.parseObject(chatMessageDO.getReviewResult(), CheckResultModel.class);
        } else {
            checkResultModel = new CheckResultModel(ChatRoleEnum.getChatRoleEnumByName(chatMessageDO.getRole()), true, null, null, null, null, null);
        }
        checkResultModel.putResultModelMap(reviewResultModelPair.getKey(), reviewResultModelPair.getValue());
        if (!reviewResultModelPair.getValue().isRet() &&
                !codeGPTDrmConfig.getNoBlockingCheckPlatformList().contains(reviewResultModelPair.getKey())) {
            checkResultModel.setAllCheckRet(false);
        }
        ChatMessageDO record = new ChatMessageDO();
        record.setReviewResult(JSON.toJSONString(checkResultModel));
        return chatMessageDOMapper.updateByExampleSelective(record, chatMessageDOExample);
    }

    private String getVoteCommentJson(String dbComment, CodeGPTVoteRequestBean codeGPTVoteRequestBean) {
        JSONObject dbCommentJson;
        if (StringUtils.isBlank(dbComment)) {
            dbCommentJson = new JSONObject();
        } else {
            dbCommentJson = JSON.parseObject(dbComment);
        }
        if (StringUtils.isNotBlank(codeGPTVoteRequestBean.getText())) {
            dbCommentJson.put("text", codeGPTVoteRequestBean.getText());
        }
        if (CollectionUtils.isNotEmpty(codeGPTVoteRequestBean.getTags())) {
            dbCommentJson.put("tags", codeGPTVoteRequestBean.getTags());
        }
        if (MapUtils.isNotEmpty(codeGPTVoteRequestBean.getCheckFeedback())) {
            Map<ReviewPlatformEnum, Integer> checkFeedback = new LinkedHashMap<>();
            if (dbCommentJson.containsKey("checkFeedback")) {
                Map<String, Object> checkFeedbackDb = dbCommentJson.getJSONObject("checkFeedback").getInnerMap();
                for (Map.Entry<String, Object> entry : checkFeedbackDb.entrySet()) {
                    ReviewPlatformEnum key = EnumUtils.getEnumIgnoreCase(ReviewPlatformEnum.class, entry.getKey());
                    Integer val = (Integer) entry.getValue();
                    checkFeedback.put(key, val);
                }
            }
            checkFeedback.putAll(codeGPTVoteRequestBean.getCheckFeedback());
            dbCommentJson.put("checkFeedback", checkFeedback);
        }
        return dbCommentJson.toJSONString();
    }

    /**
     * 根据数据库信息生成请求信息
     *
     * @param chatSessionDO     会话信息
     * @param chatMessageDOList 从数据库查询出来的信息，要求按照query_index,role和generation_index排过序，而且未删除元素
     * @param isRegeneration    是否是重新生成答案
     * @return
     */
    private ChatCompletionRequest generateRequest(ChatSessionDO chatSessionDO,
                                                  SceneDO scene,
                                                  List<ChatMessageDO> chatMessageDOList,
                                                  boolean isRegeneration) {
        ChatCompletionRequest chatRequest = new ChatCompletionRequest();

        String modelName = chatSessionDO.getModel();
        if(scene!=null && scene.getMode() == 0){
            modelName = scene.getModel();
        }

        ChatRequestExtData chatRequestExtData = algoModelUtilService.getCodeFuseChatRequestExtData(userAclService.getCurrentUser().getEmpId(), chatSessionDO.getUid(), modelName);
        chatRequest.setChatRequestExtData(chatRequestExtData);
        List<ChatMessage> chatMessageList = new ArrayList<>();
        chatRequest.setMessages(chatMessageList);

        //先从场景中获取system prompt
        String systemPrompt = null;
        if (scene!=null && StringUtil.isNotEmpty(scene.getSystemPrompt())){
            systemPrompt = scene.getSystemPrompt();
        }

        //如果session对模型的调用配置有配置，那么system prompt以modelConfig中的为准
        if(chatSessionDO.getModelConfig()!=null && StringUtil.isNotEmpty(chatSessionDO.getModelConfig())){
            JSONObject modelConfig = JSON.parseObject(chatSessionDO.getModelConfig());
            JSONObject implConfig = modelConfig.getJSONObject(AppConstants.IMPL_CONFIG);
            if (implConfig != null && implConfig.get(AlgoImplConfigKey.SYSTEM_PROMPT) != null){
                systemPrompt = implConfig.getString(AlgoImplConfigKey.SYSTEM_PROMPT);
            }
        }

        if(systemPrompt !=null){
            ChatMessage systemMessage = new ChatMessage();
            systemMessage.setRole(ChatRoleEnum.SYSTEM.getName());
            systemMessage.setContent(systemPrompt);
            chatMessageList.add(systemMessage);
        }

        // 把数据库的消息按照(1问N答)进行拆分
        List<List<ChatMessageDO>> chatMessageDOListPair = new ArrayList<>();
        // 一个pair里面按顺序存储的是1问N答
        List<ChatMessageDO> pair = new ArrayList<>();
        // chatMessageDOList的数据是按照1问N答的顺序排序的
        for (ChatMessageDO chatMessageDO : chatMessageDOList) {
            if (ChatRoleEnum.USER.getName().equals(chatMessageDO.getRole())) {
                if (CollectionUtils.isNotEmpty(pair)) {
                    chatMessageDOListPair.add(pair);
                    pair = new ArrayList<>();
                }
                pair.add(chatMessageDO);
            } else if (ChatRoleEnum.ASSISTANT.getName().equals(chatMessageDO.getRole())) {
                pair.add(chatMessageDO);
            }
        }
        if (CollectionUtils.isNotEmpty(pair)) {
            chatMessageDOListPair.add(pair);
        }
        for (int i = 0; i < chatMessageDOListPair.size(); i++) {
            List<ChatMessageDO> thisPair = chatMessageDOListPair.get(i);
            // 重新生成 并且 当前是最后一对,那当前对作为有效对
            if (isRegeneration && i == chatMessageDOListPair.size() - 1) {
                chatMessageList.addAll(convertToMessageList(thisPair));
                continue;
            }
            // 当前对全部审核通过，并且不是超时异常状态
            if (!checkFailed(thisPair) && checkServiceAbnormalResp(thisPair)) {
                chatMessageList.addAll(convertToMessageList(thisPair));
            }
        }
        return chatRequest;
    }

    /**
     * 重写文件注解
     * @param newPluginStreamPartResponse
     * @param docsInfo
     * @return
     */
    @Override
    public void processStreamResponse(NewPluginStreamPartResponse newPluginStreamPartResponse,
                                       JSONObject docsInfo,
                                       HttpServletResponse httpServletResponse){
        processStreamResponse(newPluginStreamPartResponse, docsInfo, false, null, httpServletResponse);
    }

    @Override
    public void processStreamResponse(NewPluginStreamPartResponse newPluginStreamPartResponse,
                                      JSONObject docsInfo, Boolean remoteAgent, ChatSessionDO chatSessionDO,
                                      HttpServletResponse httpServletResponse) {
        String content = newPluginStreamPartResponse.getContent();
        FileAnnotationTypeEnum fileAnnotationType = FileAnnotationTypeEnum.valueOf(codeGPTDrmConfig.getFileAnnotationType());
        String contentWithFileAnnotation = rewriteFileAnnotation(content, docsInfo, fileAnnotationType);
        newPluginStreamPartResponse.setContent(contentWithFileAnnotation);
        String data;
        if (remoteAgent) {
            TaskObject taskObject = TaskObject.of(newPluginStreamPartResponse, chatSessionDO.getUid(), chatSessionDO.getSceneId());
            boolean finished = false;
            if (RemoteAgentStatus.COMPLETED == taskObject.getStatus()) {
                finished = true;
                taskObject.setStatus(RemoteAgentStatus.IN_PROGRESS);
            }
            ChatUtils.flushSseResponseNoData(httpServletResponse, JSON.toJSONString(taskObject, SerializerFeature.SortField));
            if (finished) {
                ChatUtils.flushSseResponseNoData(httpServletResponse,
                        JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildCompletedData(
                                taskObject.getSessionId(), chatSessionDO.getSceneId(), taskObject.getId())));
            }
        } else {
            data = JSON.toJSONString(newPluginStreamPartResponse, SerializerFeature.SortField);
            ChatUtils.flushSseResponse(httpServletResponse, data);
        }
    }

    /**
     * 重写文件注解
     * @param originStr
     * @param docsInfo
     * @return
     */
    @Override
    public String rewriteFileAnnotation(String originStr, JSONObject docsInfo, FileAnnotationTypeEnum fileAnnotationType){
        if(StringUtils.isBlank(originStr)){
            return originStr;
        }

        Matcher m = FILE_ANNOTATION_PATTERN.matcher(originStr);

        StringBuffer sb = new StringBuffer();

        while(m.find()) {
            String index = m.group(1);

            String replaceStr = null;
            if(FileAnnotationTypeEnum.LINK.equals(fileAnnotationType) && docsInfo!=null && docsInfo.containsKey(index)){
                JSONObject docInfo = docsInfo.getJSONObject(index);
                String docTitle = docInfo.getString("title");
                String docUrl = docInfo.getString("url");
                replaceStr = String.format("[@%s](%s)", docTitle, docUrl);
            }else{
                replaceStr = "";
            }

            m.appendReplacement(sb, replaceStr);
        }
        m.appendTail(sb);

        return sb.toString();
    }

    /**
     * 把RAG信息合并到runtimeInfo中
     * @param streamResponseModel
     * @param docSearchResult
     * @param docsInfo
     */
    private void mergeInfoToRuntimeInfo(StreamResponseModel streamResponseModel, List<SegmentInfo> docSearchResult, JSONObject docsInfo, JSONObject repoChatInfo){
        String runtimeInfoStr = streamResponseModel.getRuntimeInfo();
        JSONObject runtimeInfo = new JSONObject();
        if(StringUtils.isNotBlank(runtimeInfoStr)){
            runtimeInfo = JSON.parseObject(runtimeInfoStr);
        }
        // 把仓库问答的模型回复合并到repoChatInfo中
        if(runtimeInfo.containsKey("keywords")){
            repoChatInfo.put("keywords", runtimeInfo.get("keywords"));
            runtimeInfo.remove("keywords");
        }
        if(runtimeInfo.containsKey("fileList")){
            repoChatInfo.put("fileList", runtimeInfo.get("fileList"));
            runtimeInfo.remove("fileList");
        }
        if(runtimeInfo.containsKey("originAnswerContent")){
            repoChatInfo.put("originAnswerContent", runtimeInfo.get("originAnswerContent"));
            runtimeInfo.remove("originAnswerContent");
        }
        if(runtimeInfo.containsKey("refreshAnswerContent")){
            repoChatInfo.put("refreshAnswerContent", runtimeInfo.get("refreshAnswerContent"));
            runtimeInfo.remove("refreshAnswerContent");
        }
        if(runtimeInfo.containsKey("recommendQuestion")){
            repoChatInfo.put("recommendQuestion", runtimeInfo.get("recommendQuestion"));
            runtimeInfo.remove("recommendQuestion");
        }
        if(docsInfo!=null){
            runtimeInfo.put("docsInfo", docsInfo);
        }
        if(CollectionUtils.isNotEmpty(docSearchResult)){
            runtimeInfo.put("docSearchResult", docSearchResult);
        }
        if(repoChatInfo!=null){
            runtimeInfo.put("repoChatInfo", repoChatInfo);
        }
        OTHERS_LOGGER.info("mergeInfoToRuntimeInfo, runtimeInfo:{}", JSON.toJSONString(runtimeInfo));
        streamResponseModel.setRuntimeInfo(JSON.toJSONString(runtimeInfo));
    }

    private void replaceAnswerContent(StreamResponseModel streamResponseModel){
        String runtimeInfoStr = streamResponseModel.getRuntimeInfo();
        JSONObject runtimeInfo = new JSONObject();
        if(StringUtils.isNotBlank(runtimeInfoStr)){
            runtimeInfo = JSON.parseObject(runtimeInfoStr);
        }
        if(runtimeInfo.containsKey("refreshAnswerContent")){
            streamResponseModel.getAnswerMessage().setContent(runtimeInfo.getString("refreshAnswerContent"));
        }
    }

    /**
     * 校验问答对是否是 超时回复
     * 问答对中有多个答时 只要有一个答没有超时就送给ai
     *
     * @param thisPair
     * @return
     */
    private boolean checkServiceAbnormalResp(List<ChatMessageDO> thisPair) {
        for (ChatMessageDO chatMessageDO : thisPair) {
            if (chatMessageDO.getServiceAbnormalResp() == null && StringUtil.equals(chatMessageDO.getRole(), ChatRoleEnum.ASSISTANT.getName())) {
                return true;
            }
        }
        return false;
    }

    private List<ChatMessage> convertToMessageList(List<ChatMessageDO> chatMessageDOList) {
        List<ChatMessage> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(chatMessageDOList)) {
            return result;
        }
        for (ChatMessageDO chatMessageDO : chatMessageDOList) {
            result.add(convertToMessage(chatMessageDO));
        }
        return result;
    }

    private ChatMessage convertToMessage(ChatMessageDO chatMessageDO) {
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole(chatMessageDO.getRole());
        chatMessage.setContent(chatMessageDO.getContent());
        return chatMessage;
    }

    /**
     * 更新AI回复的内容,把不合理内容替换掉
     *
     * @param chatMessageDOList 必须是按照1问1-N答的顺序来传参
     */
    @Override
    public void updateCheckFailedContent(List<ChatMessageDO> chatMessageDOList) {
        if (CollectionUtils.isEmpty(chatMessageDOList)) {
            return;
        }
        // 存储当前最新答案的question
        ChatMessageDO nowQuestion = null;
        for (ChatMessageDO chatMessageDO : chatMessageDOList) {
            if (ChatRoleEnum.USER.getName().equals(chatMessageDO.getRole())) {
                nowQuestion = chatMessageDO;
            } else {
                String newContent = null;
                if (nowQuestion==null) {
                    throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"消息列表不合法");
                }
                // 改造：根据数据库中的clear字段判断是否安全检查通过，将content置空，将原来的安全审核信息存入errorMsg，兼容以前的数据，所以保留了对checkResultModel的检查
                if(!chatMessageDO.getClear()){
                    Pair<Boolean, CheckResultModel> nowQuestionCheckPair = checkFailed(nowQuestion);
                    if (nowQuestionCheckPair.getKey()) {
                        newContent = algoModelUtilService.getCheckFailedMsg(nowQuestionCheckPair.getValue());
                    }
                    Pair<Boolean, CheckResultModel> answerCheckPair = checkFailed(chatMessageDO);
                    if (StringUtils.isNotBlank(newContent) && answerCheckPair.getKey()) {
                        newContent = algoModelUtilService.getCheckFailedMsg(answerCheckPair.getValue());
                    }
                    if (StringUtils.isNotBlank(newContent)) {
                        chatMessageDO.setContent("");
                        chatMessageDO.setErrorMsg(newContent);
                    }
                }else{
                    // 将返回前端的content置空
                    chatMessageDO.setContent("");
                }

            }
        }
    }

    private boolean checkFailed(List<ChatMessageDO> chatMessageDOList) {
        for (ChatMessageDO chatMessageDO : chatMessageDOList) {
            // 有一个审核失败,这批就算失败
            if (checkFailed(chatMessageDO).getKey()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否审核未通过
     *
     * @param chatMessageDO 参数
     * @return <true=审核未通过>
     */
    private Pair<Boolean, CheckResultModel> checkFailed(ChatMessageDO chatMessageDO) {
        if (chatMessageDO == null || StringUtils.isBlank(chatMessageDO.getReviewResult())) {
            return new MutablePair<>(false, null);
        }
        String reviewResult = chatMessageDO.getReviewResult();
        CheckResultModel checkResultModel = JSON.parseObject(reviewResult, CheckResultModel.class);
        return new MutablePair<>(!checkResultModel.isAllCheckRet(), checkResultModel);
    }

    /**
     * 解析pluginStreamBuffer获取PluginLogModel
     * @param pluginStreamBuffer
     * @return
     */
    private PluginLogModel getPluginLogByPluginBuffer(PluginStreamBuffer pluginStreamBuffer, Integer index) {
        if(pluginStreamBuffer == null){
            return null;
        }

        PluginLogModel pluginLogModel = new PluginLogModel();
        // 添加preRequest日志
        if (pluginStreamBuffer.getPreRequestContent().length() != 0) {
            if (StringUtils.equalsIgnoreCase(AppConstants.PRE_REQUEST_STAGE, pluginStreamBuffer.getErrorStage())) {
                pluginLogModel.addStageLog(index, AppConstants.PRE_REQUEST_STAGE, false, pluginStreamBuffer.getPreRequestContent().toString(), pluginStreamBuffer.getPluginInfo());
            } else {
                pluginLogModel.addStageLog(index, AppConstants.PRE_REQUEST_STAGE, true, pluginStreamBuffer.getPreRequestContent().toString(), pluginStreamBuffer.getPluginInfo());
            }
        }
        // 添加llm日志
        if (pluginStreamBuffer.getLlmStageContent().length() != 0) {
            if (StringUtils.equalsIgnoreCase(AppConstants.LLM_STAGE, pluginStreamBuffer.getErrorStage())) {
                pluginLogModel.addStageLog(index, AppConstants.LLM_STAGE, false, pluginStreamBuffer.getLlmStageContent().toString(), pluginStreamBuffer.getPluginInfo());
            } else {
                pluginLogModel.addStageLog(index, AppConstants.LLM_STAGE, true, pluginStreamBuffer.getLlmStageContent().toString(), pluginStreamBuffer.getPluginInfo());
            }
        }
        // 添加postRequest日志
        if (pluginStreamBuffer.getPostRequestContent().length() != 0) {
            if (StringUtils.equalsIgnoreCase(AppConstants.POST_REQUEST_STAGE, pluginStreamBuffer.getErrorStage())) {
                pluginLogModel.addStageLog(index, AppConstants.POST_REQUEST_STAGE, false, pluginStreamBuffer.getPostRequestContent().toString(), pluginStreamBuffer.getPluginInfo());
            } else {
                pluginLogModel.addStageLog(index, AppConstants.POST_REQUEST_STAGE, true, pluginStreamBuffer.getPostRequestContent().toString(), pluginStreamBuffer.getPluginInfo());
            }
        }
        // 添加summary日志
        if (StringUtils.isNotEmpty(pluginStreamBuffer.getSummaryContent())) {
            if (StringUtils.equalsIgnoreCase(AppConstants.SUMMARY, pluginStreamBuffer.getErrorStage())) {
                pluginLogModel.addStageLog(index, AppConstants.SUMMARY, false, pluginStreamBuffer.getSummaryContent().toString(), pluginStreamBuffer.getPluginInfo());
            } else {
                pluginLogModel.addStageLog(index, AppConstants.SUMMARY, true, pluginStreamBuffer.getSummaryContent().toString(), pluginStreamBuffer.getPluginInfo());
            }
        }

        return pluginLogModel;
    }

    private PluginLogModel getPluginLogByFunctionBuffer(FunctionCallStreamBuffer functionCallStreamBuffer) {
        if (functionCallStreamBuffer == null) {
            return null;
        }

        PluginLogModel pluginLogModel = new PluginLogModel();

        Map<Integer, FunctionCallStreamBuffer.OneRoundFunctionCallBuffer> functionCallBuffer = functionCallStreamBuffer.getFunctionCallBuffer();

        if(functionCallBuffer.isEmpty()){
            return null;
        }

        for (int i = 0; i < functionCallBuffer.size(); i++) {
            FunctionCallStreamBuffer.OneRoundFunctionCallBuffer oneRoundFunctionCallBuffer = functionCallBuffer.get(i);

            PluginLogModel.StageLogModel functionDecidestageLogModel = new PluginLogModel.StageLogModel(i, AppConstants.FUNCTION_DECIDE_STAGE, true, null
                    , oneRoundFunctionCallBuffer.getPluginStreamBuffer().getPluginInfo(), oneRoundFunctionCallBuffer.getChatFunctionCall());
            pluginLogModel.addStageLog(functionDecidestageLogModel);

            PluginLogModel subPluginLogModel = getPluginLogByPluginBuffer(oneRoundFunctionCallBuffer.getPluginStreamBuffer(), i);
            if(subPluginLogModel != null){
                pluginLogModel.getStageLogModels().addAll(subPluginLogModel.getStageLogModels());
            }
        }
        return pluginLogModel;
    }



    private void conversationPreCheck(ChatSessionDO chatSessionDO, String modelName){
        if (chatSessionDO == null) {
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.TOKEN_USER, AppConstants.CODEGPT_TOKEN_USER, modelName)) {
            throw new BizException(ResponseEnum.CURRENT_TOKEN_LIMITING_ANOMALY);
        }
    }

    private ChatMessageDO getLastMessage(List<ChatMessageDO> chatMessageDOList, boolean isRegenerated){
        ChatMessageDO lastMessageDO = null;
        if (isRegenerated && chatMessageDOList.isEmpty()) {
            throw new BizException(ResponseEnum.MESSAGE_IS_NOT_EXIST);
        }

        if (!chatMessageDOList.isEmpty()) {
            lastMessageDO = chatMessageDOList.get(chatMessageDOList.size() - 1);
            if (ChatRoleEnum.USER.getName().equals(lastMessageDO.getRole())) {
                throw new BizException(ResponseEnum.SESSION_QUERY_STATE_ILLEGAL);
            }
        }
        return lastMessageDO;
    }

    /**
     * 获取最后一个问题
     * @param chatMessageDOList
     * @param isRegenerated
     * @return
     */
    private ChatMessageDO getLastUserMessage(List<ChatMessageDO> chatMessageDOList, boolean isRegenerated){
        ChatMessageDO lastMessageDO = null;
        if (isRegenerated && chatMessageDOList.isEmpty()) {
            throw new BizException(ResponseEnum.MESSAGE_IS_NOT_EXIST);
        }

        if (!chatMessageDOList.isEmpty()) {
            lastMessageDO = chatMessageDOList.get(chatMessageDOList.size() - 2);
            if (ChatRoleEnum.ASSISTANT.getName().equals(lastMessageDO.getRole())) {
                throw new BizException(ResponseEnum.SESSION_QUERY_STATE_ILLEGAL);
            }
        }
        return lastMessageDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bindSessionFileAndGetSummary(SessionFileSummaryRequest sessionFileSummaryRequest) {
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionFileSummaryRequest.getSessionUid());
        if (CollectionUtil.isEmpty(sessionFileSummaryRequest.getBindingFileUidList())) {
            throw new BizException(ResponseEnum.NEED_UPLOAD_FILE_FAILED);
        }
        if (chatSessionDO == null) {
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        // 会话本身已经绑定文件不在本次绑定列表的话需要删掉
        if (SessionUtils.getSessionBindingFileSize(chatSessionDO) > 0) {
            deleteInvalidBindingFile(chatSessionDO, sessionFileSummaryRequest);
        }

        // 获取该会话本次应该绑定的文件列表
        JSONArray fileList = new JSONArray();
        for (String fileUid : sessionFileSummaryRequest.getBindingFileUidList()) {
            Serializable serializable = defaultCacheManager.get(AppConstants.SESSION_FILE_TBASE_PREFIX + fileUid);
            if (serializable == null) {
                throw new BizException(ResponseEnum.REFRESH_WEB_RETRY);
            }
            fileList.add(serializable);
        }
        Map<String, JSONObject> fileMap = buildFileMap(fileList);
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        // 2、获取对应的summary
        List<String> summaryFileUidList = sessionFileSummaryRequest.getSummaryFileUidList();
        StringBuilder answer = new StringBuilder();
        if (CollectionUtils.isNotEmpty(summaryFileUidList)) {
            StringBuilder question = new StringBuilder();
            answer.append(documentChatConfig.getString("summaryContentTitle"));
            for (String summaryFileUid : summaryFileUidList) {
                if (!fileMap.containsKey(summaryFileUid)) {
                    throw new BizException(ResponseEnum.FILE_UID_IS_ILLEGAL, "文件UID和当前会话没有绑定,fileUid:" + summaryFileUid);
                }
                JSONObject fileJson = fileMap.get(summaryFileUid);
                boolean summaryReturnUser = fileJson.getBoolean("summaryReturnUser");
                if (summaryReturnUser) {
                    throw new BizException(ResponseEnum.FILE_SUMMARY_ALREADY_RETURN_USER, ResponseEnum.FILE_SUMMARY_ALREADY_RETURN_USER.getErrorMsg() + ",fileUid:" + summaryFileUid);
                }
                String fileName = fileJson.getString("fileName");
                String jsonStr = ossService.getString(AppConstants.CHAT_DOCUMENT_SUMMARY + summaryFileUid);
                JSONObject summaryJson = JSONObject.parseObject(jsonStr);
                String fileSummary = summaryJson.getString("fileSummary");
                question.append(fileName).append(" ");
                answer.append(fileName).append("\n").append(fileSummary);
                fileJson.put("summaryReturnUser", true);
            }
            saveMessage(chatSessionDO, question.toString(), answer.toString());
            Long documentChatSceneId = documentChatConfig.getLong("documentChatSceneId");
            boolean needAddSceneUserCount = !chatSessionManageService.hasSceneSession(userAuthDO.getId(), documentChatSceneId);
            addUsageCount(null, sceneService.getSceneById(documentChatSceneId), null, needAddSceneUserCount,false);
        }
        JSONArray fileListNew = new JSONArray();
        fileListNew.addAll(fileMap.values());
        // 更新db的summaryReturnUser状态
        SessionUtils.updateSessionBindingFile(chatSessionDO, fileListNew);
        chatSessionManageService.updateSession(chatSessionDO);
        return answer.toString();
    }

    private void deleteInvalidBindingFile(ChatSessionDO chatSessionDO,SessionFileSummaryRequest sessionFileSummaryRequest) {
        JSONArray fileList = JSONObject.parseObject(chatSessionDO.getExtInfo()).getJSONArray("fileList");
        List<String> needDelFileUidList = new ArrayList<>();
        for (int i = 0; i < fileList.size(); i++) {
            JSONObject fileObj = fileList.getJSONObject(i);
            String fileUid = fileObj.getString("fileUid");
            if (!sessionFileSummaryRequest.getBindingFileUidList().contains(fileUid)) {
                needDelFileUidList.add(fileUid);
            }
        }
        for (String needDelFileUid : needDelFileUidList) {
            ossService.deleteFile(AppConstants.CHAT_DOCUMENT + needDelFileUid);
            ossService.deleteFile(AppConstants.CHAT_DOCUMENT_SUMMARY + needDelFileUid);
            defaultCacheManager.del(AppConstants.SESSION_FILE_TBASE_PREFIX + needDelFileUid);
        }
    }

    private Map<String, JSONObject> buildFileMap(JSONArray fileList) {
        if (fileList == null || fileList.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, JSONObject> retMap = new HashMap<>(fileList.size());
        for (int i = 0; i < fileList.size(); i++) {
            JSONObject fileJson = fileList.getJSONObject(i);
            String fileUid = fileJson.getString("fileUid");
            retMap.put(fileUid, fileJson);
        }
        return retMap;
    }

    private void saveMessage(ChatSessionDO chatSessionDO, String question, String answer) {
        String sessionUid = chatSessionDO.getUid();
        boolean lock = tbaseCacheService.getLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid, 60 * 1000);
        if (!lock) {
            throw new BizException(ResponseEnum.REPEAT_QUESTIONS);
        }
        try {
            List<ChatMessageDO> chatMessageDOList = listChatMessage(sessionUid, false, false);
            ChatMessageDO lastMessageDO = getLastMessage(chatMessageDOList, false);

            String questionUid = ShortUid.getUid();
            String answerUid = ShortUid.getUid();
            long queryIndex = lastMessageDO == null ? 1 : lastMessageDO.getQueryIndex() + 1;
            long generateIndex = 0;

            ChatMessageDO queryMessageDO = new ChatMessageDO();
            queryMessageDO.setContent(question);
            queryMessageDO.setRole(ChatRoleEnum.USER.getName());
            queryMessageDO.setQueryIndex(queryIndex);
            queryMessageDO.setGenerationIndex(generateIndex);
            queryMessageDO.setUserId(chatSessionDO.getUserId());
            queryMessageDO.setSessionUid(sessionUid);
            queryMessageDO.setUid(questionUid);

            ChatMessageDO answerMessageDO = new ChatMessageDO();
            answerMessageDO.setContent(answer);
            answerMessageDO.setRole(ChatRoleEnum.ASSISTANT.getName());
            answerMessageDO.setQueryIndex(queryIndex);
            answerMessageDO.setGenerationIndex(generateIndex);
            answerMessageDO.setUserId(chatSessionDO.getUserId());
            answerMessageDO.setSessionUid(sessionUid);
            answerMessageDO.setUid(answerUid);
            answerMessageDO.setServiceAbnormalResp("summary");
            chatSessionManageService.insertPairChatMessage(queryMessageDO, answerMessageDO);
        } finally {
            tbaseCacheService.releaseLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid);
        }
    }

    @Override
    public List<SessionFileResponse> getSessionFileResponse(String sessionUid) {
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
        if (chatSessionDO == null) {
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        if (SessionUtils.getSessionBindingFileSize(chatSessionDO) == 0) {
            return new ArrayList<>();
        }
        List<SessionFileResponse> sessionFileResponseList = new ArrayList<>();
        JSONArray fileList = JSONObject.parseObject(chatSessionDO.getExtInfo()).getJSONArray("fileList");
        for (int i = 0; i < fileList.size(); i++) {
            JSONObject fileJson = fileList.getJSONObject(i);
            String fileUid = fileJson.getString("fileUid");
            String fileName = fileJson.getString("fileName");
            boolean summaryReturnUser = fileJson.getBoolean("summaryReturnUser");
            sessionFileResponseList.add(new SessionFileResponse(fileUid, fileName, summaryReturnUser));
        }
        return sessionFileResponseList;
    }

    @Override
    public Boolean addSessionExtInfo(String sessionUid, JSONObject sessionExtInfo) {
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
        if (chatSessionDO == null) {
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        SessionUtils.addSessionConfig(chatSessionDO, sessionExtInfo);
        return chatSessionManageService.updateSession(chatSessionDO);
    }

    @Override
    public JSONObject getSessionExtInfo(String sessionUid) {
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
        if (chatSessionDO == null) {
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        return JSON.parseObject(chatSessionDO.getExtInfo());
    }

    @Override
    public Boolean deleteSessionExtInfo(String sessionUid, List<String> keys) {
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
        if (chatSessionDO == null) {
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        SessionUtils.deleteSessionConfig(chatSessionDO, keys);
        return chatSessionManageService.updateSession(chatSessionDO);
    }

    @Override
    public Boolean updateMessage(ChatMessageDO chatMessageDO) {
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andUidEqualTo(chatMessageDO.getUid());
        return chatMessageDOMapper.updateByExampleSelective(chatMessageDO, chatMessageDOExample) == 1;
    }

    /**
     * 根据uid查询当前轮会话有多少ai回复
     *
     * @param sessionUid
     * @return
     */
    @Override
    public Long selectLastMessageCount(String sessionUid, Long queryIndex) {
        PageHelper.startPage(0, 1);
        ChatMessageDOExample chatMessageDOExampleUser = new ChatMessageDOExample();
        chatMessageDOExampleUser.createCriteria()
                .andSessionUidEqualTo(sessionUid)
                .andDeletedEqualTo((byte) 0)
                .andRoleEqualTo(ChatRoleEnum.ASSISTANT.getName())
                .andQueryIndexEqualTo(queryIndex);
        chatMessageDOExampleUser.setOrderByClause("gmt_create desc");
        List<ChatMessageDO> chatMessageDOS = chatMessageDOMapper.selectByExample(chatMessageDOExampleUser);
        return chatMessageDOS.size() == 0 ? 0 : chatMessageDOS.get(0).getGenerationIndex()+1;
    }

    private void ensureChatMessageListLegal(ChatCompletionRequest chatRequest){
        List<ChatMessage> chatMessageList = chatRequest.getMessages();
        for (int i = chatMessageList.size() - 1; i >= 0; i--) {
            ChatMessage chatMessage = chatMessageList.get(i);
            if (ChatRoleEnum.ASSISTANT.getName().equals(chatMessage.getRole())) {
                chatMessageList.remove(i);
            } else if (ChatRoleEnum.USER.getName().equals(chatMessage.getRole())) {
                break;
            }
        }
    }

    /**
     * 对话时没有历史消息进行助手和插件的使用次数增加
     */
    private void addUsageCount(ChatMessageDO lastMessageDO, SceneDO scene, String model, boolean needAddSceneUserCount, boolean needAddModelUserCount) {
        // 助手非空才需要更新助手和插件的数据
        if (scene != null) {
            // 增加助手的使用消息数
            sceneService.addSceneUsageMessageCount(scene.getId());
            // 增加助手的使用人数
            if (needAddSceneUserCount) {
                sceneService.addSceneUsageUserCount(scene.getId());
            }
            // 增加助手的使用会话数
            if (lastMessageDO == null) {
                sceneService.addSceneUsageCount(scene.getId());
                // 增加插件的使用会话数
                if (scene.getMode() == 1) {
                    List<Long> pluginList = JSONArray.parseArray(scene.getPluginList(), Long.class);
                    pluginList.forEach(pluginService::addPluginUsageCount);
                }
            }
        }
        // 助手为空或者助手的类型是非插件类型的才需要更新模型的数据
        if ((scene == null || scene.getMode() == 0) && StringUtils.isNotBlank(model)) {
            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
            model = algoBackendDO.getModel();
            // 增加模型的使用消息数
            algoBackendService.addUsageMessageCount(model);
            // 增加模型的使用人数
            if (needAddModelUserCount) {
                algoBackendService.addUsageUserCount(model);
            }
            // 增加模型的会话数
            if (lastMessageDO == null) {
                algoBackendService.addUsageSessionCount(model);
            }
        }
    }

    private void modelAvailableCheck(String modelName, SceneDO scene, ChatSessionDO chatSessionDO) {
        if(!codeGPTDrmConfig.isMayaModelDeploymentCheck()) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("mayaModelDeploymentCheck is false, don't need check");
            }
            return;
        }
        if(null != scene) {
            ConversationTypeEnum conversationType = getConversationType(scene);
            if(conversationType.equals(ConversationTypeEnum.AUTO_TOOL_CALL) || conversationType.equals(ConversationTypeEnum.FIX_TOOL_CALL)) {
                if(LOGGER.isInfoEnabled()) {
                    LOGGER.info("this is a function call plugin, no need for model check, {}", chatSessionDO.getUid());
                }
                return;
            }
        }
        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(modelName);
        String modelImpl = algoBackendDO.getImpl();
        if(modelImpl.equalsIgnoreCase("ChatGptModelHandler")
                || modelImpl.equalsIgnoreCase("AntGLMModelHandler")
                || modelImpl.equalsIgnoreCase("ChatGptModelHubHandler") ) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("unMAYA model not need check, {}", chatSessionDO.getUid());
            }
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(algoBackendDO.getImplConfig());
        String modelEnvFromDb = jsonObject.getString("modelEnv");

        String modelEnv = "auto";
        JSONObject sessionConfig = chatSessionManageService.getSessionConfig(chatSessionDO.getUid(),null);
        if (sessionConfig.containsKey(AppConstants.IMPL_CONFIG)){
            JSONObject sceneImplConfig = JSONObject.parseObject(sessionConfig.getString(AppConstants.IMPL_CONFIG));
            if (sceneImplConfig.containsKey("modelEnv")){
                modelEnv = sceneImplConfig.getString("modelEnv");
            }
        }
        if(modelEnv.equalsIgnoreCase("auto")
                && StringUtils.isNotBlank(modelEnvFromDb)
                && !modelEnvFromDb.equalsIgnoreCase("auto")) {
            modelEnv = modelEnvFromDb;
        }

        String actualEnv = mayaService.getActualEnv(modelEnv);
        JSONObject availableServers = mayaService.getModelAvailableServers(modelName, false);
        // cover new model logic, max gap is 10 mins
        // try to check again, when no server available
        if(null == availableServers
                || availableServers.get(actualEnv) == null
                || availableServers.getJSONObject(actualEnv).get("summary") == null
                || availableServers.getJSONObject(actualEnv).getJSONObject("summary").get("isAvailable") == null
                || !availableServers.getJSONObject(actualEnv).getJSONObject("summary").getBoolean("isAvailable")) {
            availableServers = mayaService.getModelAvailableServers(modelName, true);
        }
        if(availableServers == null
            || availableServers.get(actualEnv) == null
            || availableServers.getJSONObject(actualEnv).get("summary") == null
            || availableServers.getJSONObject(actualEnv).getJSONObject("summary").get("isAvailable") == null
            || !availableServers.getJSONObject(actualEnv).getJSONObject("summary").getBoolean("isAvailable")) {
            LOGGER.warn("no available server, modelName:{},actualEnv: {},availableServers:{}", modelName, actualEnv,JSONObject.toJSONString(availableServers));
            algoModelHealthUtilService.costHealth(modelEnv, modelName, ResponseEnum.MODEL_NO_AVAILABLE_SERVER_ERROR.name());
            throw new BizException(ResponseEnum.MODEL_NO_AVAILABLE_SERVER_ERROR);
        }
    }

    private void recordPluginLog(PluginLogGroup pluginLogGroup) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to record plugin log");
        }

        if(null == pluginLogGroup.getPluginLogList()
                || 0 == pluginLogGroup.getPluginLogList().size()) {
            LOGGER.warn("plugin log is empty");
            return;
        }

        try {
            for(PluginLog pluginLog : pluginLogGroup.getPluginLogList()) {
                recordPluginLog(pluginLog);
            }
        } catch (Exception exception) {
            LOGGER.warn("parse PluginLogGroup exception, ", exception);
        }

    }

    private void recordPluginLog(PluginLog pluginLog) {
        if(null == pluginLog
            || null == pluginLog.getPluginInfo()
            || null == pluginLog.getStageLogList()
            || 0 == pluginLog.getStageLogList().size()) {
            LOGGER.warn("pluginLog info is invalid");
            return;
        }
        for(StageLog stageLog : pluginLog.getStageLogList()) {
            recordPluginLog(pluginLog.getPluginInfo(), stageLog);
        }
    }

    private void recordPluginLog(PluginInfo pluginInfo, StageLog stageLog) {
        Long pluginId = pluginInfo.getId();
        String pluginName = pluginInfo.getName();

        String stageName = stageLog.getStageName();
        String finishReason = stageLog.getFinishReason();
        Boolean status = stageLog.isStatus();
        String type = stageLog.getType();

        Map<String, Object> recordInfo = new HashMap<>();
        recordInfo.put("pluginId", pluginId);
        recordInfo.put("pluginName", pluginName);
        recordInfo.put("stageName", stageName);
        recordInfo.put("finishReason", finishReason);
        recordInfo.put("type", type);
        recordInfo.put("status", status);
        recordInfo.put("output", null);

        if(null != stageLog.getStageInfo() && null != stageLog.getStageInfo().getOutput()) {
            Object output = stageLog.getStageInfo().getOutput();
            recordInfo.put("output", JSONObject.toJSON(output));
        }

        CollectLogUtils.printCollectLog(RecordLogEnum.PLUGIN_USED, recordInfo);
    }

    /**
     * 获取仓库信息
     * @param chatSessionDO
     * @return
     */
    private RepoInfoModel exactRepoInfoFromChatSession(ChatSessionDO chatSessionDO) {
        JSONObject sessionConfig = SessionUtils.getSessionExtInfo(chatSessionDO);

        String repoPath = sessionConfig.getString(AppConstants.SESSION_CONTEXT_REPO_PATH_KEY);
        String branch = sessionConfig.getString(AppConstants.SESSION_CONTEXT_BRANCH_KEY);

        // 兼容网页版通过会话表单填写的仓库信息
        if(repoPath == null && branch == null && sessionConfig.containsKey(AppConstants.SESSION_CONTEXT_FORM_DATA_KEY)){
            JSONObject formData = sessionConfig.getJSONObject(AppConstants.SESSION_CONTEXT_FORM_DATA_KEY);
            String repoGroup = formData.getString("repoGroup");
            String repoName = formData.getString("repoName");
            if (repoGroup != null && repoName != null) {
                repoPath = repoGroup + "/" + repoName;
            }
            branch = formData.getString("branch");
        }

        if(repoPath==null){
            return null;
        }
        branch= AntCodeClient.defaultIfBlank(branch);

        return new RepoInfoModel(repoPath, branch);
    }
    /**
     * 仓库问答历史消息替换为originAnswerContent
     *
     * <AUTHOR>
     * @since 2024.08.20
     * @param chatMessageDOList chatMessageDOList
     */
    private void replaceOriginAnswerContent(List<ChatMessageDO> chatMessageDOList) {
        if (CollectionUtils.isEmpty(chatMessageDOList)) {
            return;
        }
        for (ChatMessageDO chatMessage : chatMessageDOList) {
            if (chatMessage.getRole().equalsIgnoreCase(ChatRoleEnum.ASSISTANT.name())&& StringUtils.isNotBlank(chatMessage.getRuntimeInfo())) {
                JSONObject repoChatInfo = JSONObject.parseObject(chatMessage.getRuntimeInfo()).getJSONObject("repoChatInfo");
                if(repoChatInfo != null && repoChatInfo.containsKey("refreshAnswerContent")){
                    String refreshAnswerContent = repoChatInfo.getString("refreshAnswerContent");
                    chatMessage.setContent(refreshAnswerContent);
                    OTHERS_LOGGER.info("replaceOriginAnswerContent, chatMessage:{}", JSONObject.toJSONString(chatMessage));
                }else if (repoChatInfo != null && repoChatInfo.containsKey("originAnswerContent")) {
                    String originAnswerContent = repoChatInfo.getString("originAnswerContent");
                    chatMessage.setContent(originAnswerContent);
                    OTHERS_LOGGER.info("replaceOriginAnswerContent, chatMessage:{}", JSONObject.toJSONString(chatMessage));
                }
            }
        }
        OTHERS_LOGGER.info("replaceOriginAnswerContent, chatMessageDOList:{}", JSONObject.toJSONString(chatMessageDOList));
    }
}
