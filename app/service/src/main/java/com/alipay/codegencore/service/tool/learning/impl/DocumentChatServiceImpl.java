package com.alipay.codegencore.service.tool.learning.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.DocumentToChatResponse;
import com.alipay.codegencore.model.model.EmbeddingResponseModel;
import com.alipay.codegencore.model.request.DocumentToChatRequestBean;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.OssService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.DocumentChatService;
import com.alipay.codegencore.service.utils.SessionUtils;
import com.alipay.codegencore.utils.embedding.VectorCalculate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 文档对话服务实现类
 */
@Service
public class DocumentChatServiceImpl implements DocumentChatService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentChatServiceImpl.class);

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private ChatSessionManageService chatSessionManageService;
    @Resource
    private OssService ossService;
    @Resource
    private CalculateTokenService calculateTokenService;
    @Resource
    private AlgoBackendService algoBackendService;

    @Override
    public DocumentToChatResponse documentToChatPluginService(DocumentToChatRequestBean documentToChatRequestBean) {
        long startTime = System.currentTimeMillis();
        if (documentToChatRequestBean == null || StringUtils.isBlank(documentToChatRequestBean.getQuery()) || StringUtils.isBlank(documentToChatRequestBean.getSessionUid())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        Integer topNSimilarity = documentChatConfig.getInteger("topNSimilarity");
        BigDecimal minSimilarity = documentChatConfig.getBigDecimal("minSimilarity");
        String promptFormat = documentChatConfig.getString("promptFormat");
        String llmModelName = documentToChatRequestBean.getLlmModelName();
        AlgoBackendDO llmModelDO = algoBackendService.getAlgoBackendByName(llmModelName);
        String llmPrompt = promptFormat.replace("question", documentToChatRequestBean.getQuery());
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(documentToChatRequestBean.getSessionUid());
        if (chatSessionDO == null || SessionUtils.getSessionBindingFileSize(chatSessionDO) == 0) {
            throw new BizException(ResponseEnum.NEED_UPLOAD_FILE_FAILED);
        }
        JSONArray fileList = JSONObject.parseObject(chatSessionDO.getExtInfo()).getJSONArray("fileList");
        if (fileList.size() > 1) {
            throw new BizException(ResponseEnum.SESSION_ALREADY_BINDING_FILE);
        }
        List<BigDecimal> queryEmbeddingList = chatSessionManageService.getEmbeddingList(documentToChatRequestBean.getQuery());
        StringBuilder contentSb = new StringBuilder();
        for (int i = 0; i < fileList.size(); i++) {
            JSONObject fileJson = fileList.getJSONObject(i);
            String fileUid = fileJson.getString("fileUid");
            String jsonStr = ossService.getString(AppConstants.CHAT_DOCUMENT_SUMMARY + fileUid);
            JSONObject summaryJson = JSONObject.parseObject(jsonStr);
            String fileSummary = summaryJson.getString("fileSummary");
            List<EmbeddingResponseModel> embeddingResponseModelList = summaryJson.getJSONArray("embeddingResponseModelList").toJavaList(EmbeddingResponseModel.class);
            List<EmbeddingResponseModel> topNSimilarityList = VectorCalculate.calculateCosineSimilarityTopN(queryEmbeddingList, embeddingResponseModelList, topNSimilarity);
            boolean needFileSummary = topNSimilarityList.get(0).getOriginalSimilarity().compareTo(minSimilarity) < 0;
            int count = 1;
            if (needFileSummary && StringUtils.isNotBlank(fileSummary)) {
                LOGGER.info("documentToChatPluginService needFileSummary,documentToChatRequestBean:{}", JSON.toJSONString(documentToChatRequestBean));
                contentSb.append(String.format("Source 〔%s〕 摘要", count++)).append("\n").append(fileSummary);
            }
            for (EmbeddingResponseModel embeddingResponseModel : topNSimilarityList) {
                if (calculateTokenService.getTokenQty(llmPrompt + contentSb + embeddingResponseModel.getOriginalStr()) > llmModelDO.getMaxToken()) {
                    break;
                }
                contentSb.append("\n\n").append(String.format("Source 〔%s〕", count++)).append("\n").append(embeddingResponseModel.getOriginalStr());
            }
        }
        llmPrompt = llmPrompt.replace("searchResult", contentSb.toString());
        DocumentToChatResponse documentToChatResponse = new DocumentToChatResponse();
        documentToChatResponse.setPrompt(llmPrompt);
        documentToChatResponse.setProcessingTime(new BigDecimal(System.currentTimeMillis() - startTime).divide(new BigDecimal("1000.00"), 2, RoundingMode.HALF_UP));
        LOGGER.info("documentToChatPluginService response,documentToChatResponse:{}", JSON.toJSONString(documentToChatResponse));
        return documentToChatResponse;
    }

}
