package com.alipay.codegencore.service.ideaevo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * ideaEvo相关服务
 */
public interface IdeaEvoService {
    /**
     * ai代码生成规划
     * @param featureData 用户请求数据
     *      1 输入配置
     *         query:TYPE_STRING:[1]        对应bytes 类型
     *      2 输出配置
     *         out_float:TYPE_FP64:[1]        对应float64 类型
     *         out_string:TYPE_STRING:[1]     对应bytes_ 类型
     *         out_int:TYPE_INT32:[1].        对应int32 类型
     *      其他
     *      1. TYPE_STRING对应是python bytes类型(或者np.bytes_)，目标是方便传递二进制内容，比如图片的binary内容，减少base64转换开销;
     *         bytes类型可以通过decode函数明确转换成python str类型
     *      2. 参数维度见使用文档
     */
    JSONObject aiCodePlan(JSONObject featureData, String modelEnv);

    /**
     * 代码生成
     * @param featureData
     * @return
     */
    JSONArray aiCodeGen(JSONObject featureData, String modelEnv);
}
