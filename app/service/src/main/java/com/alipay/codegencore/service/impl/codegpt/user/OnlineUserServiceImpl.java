/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.impl.codegpt.user;

import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.service.codegpt.user.OnlineUserService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import static com.alipay.codegencore.model.contant.WebApiContents.ONLINE_USER_LIST_TBASE_KEY;
import static com.alipay.codegencore.model.contant.WebApiContents.ONLINE_USER_OFFLINE_TIME;
import static com.alipay.codegencore.model.contant.WebApiContents.TBASE_MOCK_USER_INFO_KEY;

/**
 * <AUTHOR>
 * @version OnlineUserServiceImpl.java, v 0.1 2023年03月30日 16:13 xiaobin
 */
@Service
public class OnlineUserServiceImpl implements OnlineUserService {

    @Resource
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private CodeGPTDrmConfig drmConfig;

    @Resource
    private UserAuthDOMapper userAuthDOMapper;

    @Override
    public void heartbeat(UserAuthDO user) {
        defaultCacheManager.zadd(ONLINE_USER_LIST_TBASE_KEY, System.currentTimeMillis(), user.getId());
    }

    @Override
    public Long getOnlineUserNum() {
        return defaultCacheManager.zcount(ONLINE_USER_LIST_TBASE_KEY, getEarliestOnlineTime(), Double.MAX_VALUE);
    }

    @Override
    public boolean isOnline(UserAuthDO user) {
        Double score = defaultCacheManager.zscore(ONLINE_USER_LIST_TBASE_KEY, user.getId());
        if (score == null) {
            return false;
        }
        return score.longValue() > getEarliestOnlineTime();
    }

    @Override
    public boolean tryAcquireUserRateLimiter(UserAuthDO user) {
        return isOnlineUser(user) || tryAcquireOnlineQuota(user);
    }

    private Long getEarliestOnlineTime() {
        return System.currentTimeMillis() - ONLINE_USER_OFFLINE_TIME;
    }

    private boolean isOnlineUser(UserAuthDO user) {
        Double zScore = defaultCacheManager.zscore(ONLINE_USER_LIST_TBASE_KEY, user.getId());
        return zScore != null && System.currentTimeMillis() - zScore < ONLINE_USER_OFFLINE_TIME;
    }

    private boolean tryAcquireOnlineQuota(UserAuthDO user) {
        if (defaultCacheManager.zcount(ONLINE_USER_LIST_TBASE_KEY, System.currentTimeMillis() - ONLINE_USER_OFFLINE_TIME, Double.MAX_VALUE)
                >= drmConfig.getMaxOnlineUserNum()) {
            return false;
        }
        defaultCacheManager.zadd(ONLINE_USER_LIST_TBASE_KEY, System.currentTimeMillis(), user.getId());
        return true;
    }

    @Override
    public void mockUser(String empId, String mockEmpId) {
        UserAuthDOExample example = new UserAuthDOExample();
        example.createCriteria().andEmpIdEqualTo(mockEmpId);
        List<UserAuthDO> mockUser = userAuthDOMapper.selectByExample(example);
        defaultCacheManager.hset(TBASE_MOCK_USER_INFO_KEY, empId, mockUser.get(0));
    }
}