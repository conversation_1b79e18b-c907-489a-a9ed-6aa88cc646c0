package com.alipay.codegencore.service.codegpt;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.model.tool.learning.FunctionCallConfig;
import com.alipay.codegencore.model.model.yuque.YuQueTokenInfoModel;
import com.alipay.codegencore.model.openai.UserSaveSceneVO;
import com.alipay.codegencore.model.response.PageResponse;

import java.util.List;

/**
 * <AUTHOR> 助手表Service
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.service.codegpt.user
 * @CreateTime : 2023-07-11
 */
public interface SceneService {

    /**
     * 通过bizId获取助手
     * @param bizId
     * @return
     */
    SceneDO getSceneByBizId(String bizId);

    /**
     * 通过助手Id查找助手
     *
     * @param sceneId 助手id
     * @return
     */
    SceneDO getSceneById(Long sceneId);

    /**
     * 根据id获取用户可用的助手
     * @param sceneId
     * @return
     */
    UserSaveSceneVO getUserAvailableSceneById(Long sceneId, boolean onlyBaseInfo);


    /**
     * 获取所有助手 包括未启用助手
     *
     * @return
     */
    List<SceneDO> getAllScene();

    /**
     * 用户创建新助手
     *
     * @param sceneDO
     * @return
     */
    Long addScene(SceneDO sceneDO);

    /**
     * 更新助手信息
     *
     * @param sceneDO
     * @return
     */
    Boolean updateSceneById(SceneDO sceneDO);


    /**
     * 根据ID更新场景助手信息
     * @param sceneDO
     * @return
     */
    Boolean updateScene(SceneDO sceneDO);

    /**
     * 管理员使用
     *
     * @param sceneDO
     * @return
     */
    Boolean adminUpdateSceneById(SceneDO sceneDO);

    /**
     * 增加助手使用次数
     *
     * @param sceneId
     */
    Boolean addSceneUsageCount(Long sceneId);

    /**
     * 增加使用消息数
     * @param sceneId
     */
    void addSceneUsageMessageCount(Long sceneId);

    /**
     * 增加助手的使用人数
     * @param sceneId
     */
    void addSceneUsageUserCount(Long sceneId);

    /**
     * 删除助手
     *
     * @param id 助手id
     * @return
     */
    Boolean deleteSceneById(long id);


    /**
     * 获取默认助手
     *
     * @return
     */
    SceneDO getDefaultScene();


    /**
     * 获取用户收藏助手
     *
     * @return
     */
    List<SceneDO> getUserSaveScene(String query);

    /**
     * 物理删除助手
     *
     * @param id
     * @return
     */
    Boolean physicalDeletion(Long id);

    /**
     * 根据id获取多个助手
     * @param ids
     * @return
     */
    List<SceneDO> getSceneByListId(List<Long> ids);

    /**
     * 获取function call 配置
     * @param sceneDO
     * @return
     */
    FunctionCallConfig getFunctionCallConfig(SceneDO sceneDO);

    /**
     * 获取场景表单的schema
     * @param id
     * @return
     */
    JSONObject getSceneSchema(Long id);

    /**
     * 获取一个模型对应创建的所有助手
     * @param model
     * @return
     */
    List<SceneDO> getSceneByModelName(String model);

    /**
     * 用户是否有权限编辑这个插件
     * @param sceneId
     * @param userId
     * @return true=有权限编辑
     */
    boolean editableSceneUser(Long sceneId, Long userId);

    /**
     * 用户是否有权使用这个这个助手
     * @param sceneId
     * @param userId
     * @return true=有权限编辑
     */
    boolean availableSceneUser(Long sceneId, Long userId);


    /**
     * 获取消息的scene信息
     * @param messageUid
     * @return
     */
    SceneDO getSceneByMessageUid(String messageUid);

    /**
     * 获取当前用户可编辑助手
     *
     * @param query    查询
     * @param pageNo   页数
     * @param pageSize 每页个数
     * @param sceneTag 助手标签
     * @return
     */
    PageResponse<List<UserSaveSceneVO>> getEditableSceneByUser(String query, int pageNo, int pageSize, String sceneTag);

    /**
     * 获取用户自己创建的助手
     *
     * <AUTHOR>
     * @since 2024.11.22
     * @param query query
     * @param pageNo pageNo
     * @param pageSize pageSize
     * @return com.alipay.codegencore.model.response.PageResponse<java.util.List<com.alipay.codegencore.model.openai.UserSaveSceneVO>>
     */
    PageResponse<List<UserSaveSceneVO>> getMySceneByUser(String query, int pageNo, int pageSize);

    /**
     * 获取用户有权限查看的所有已启用助手
     *
     * @param query
     * @return
     */
    List<UserSaveSceneVO> getAllSceneByEnable(String query);

    /**
     * 获取用户你有权限查看的插件端开启助手
     *
     * <AUTHOR>
     * @since 2024.11.22
     * @return java.util.List<com.alipay.codegencore.model.openai.UserSaveSceneVO>
     */
    List<UserSaveSceneVO> getAllSceneByPluginEnable();

    /**
     * 绑定文档到助手上
     * @param sceneId
     * @param documentUid
     */
    void bindDocument(Long sceneId, String documentUid);

    /**
     * 解除助手上绑定的文件
     * @param sceneId
     * @param documentUid
     */
    void unbindDocument(Long sceneId, String documentUid);

    /**
     * 绑定团队token
     * <AUTHOR>
     * @since 2024.01.17
     * @param token token
     */
    YuQueTokenInfoModel bindGroupToken(Long sceneId, String token);
}
