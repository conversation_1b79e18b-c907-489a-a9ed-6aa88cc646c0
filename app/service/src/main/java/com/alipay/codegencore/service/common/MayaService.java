package com.alipay.codegencore.service.common;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.maya.model.MayaRequest;

import java.io.Serializable;
import java.util.List;

/**
 * 调用maya平台服务
 * <AUTHOR>
 */
public interface MayaService {
    /**
     * 获取推理结果
     *
     * @param requestStr     请求
     * @param sceneName      场景名
     * @param chainName      服务名/版本号
     * @param requestTimeOut 请求超时时间，单位ms
     * @return 推理结果
     */
    String getInferenceResult(String requestStr, String sceneName, String chainName, int requestTimeOut, String requestKey, String responseKey, boolean needGrpc);


    /**
     * 获取推理结果
     *
     * @param requestStr     请求
     * @param sceneName      场景名
     * @param chainName      服务名/版本号
     * @param requestTimeOut 请求超时时间，单位ms
     * @param algoBackendDO  模型配置信息
     * @param algoBackendDO  模型配置信息
     * @return 推理结果
     */
    String getInferenceResult(String requestStr, String sceneName, String chainName,
                              int requestTimeOut, String requestKey, String responseKey, boolean needGrpc, AlgoBackendDO algoBackendDO, String modelEnv);

    /**
     * 获取请求maya的对象
     *
     * @param keyStr
     * @param valueStr
     * @param sceneName
     * @param chainName
     * @param requestTimeOut
     * @return
     */
    MayaRequest getMayaRequest(String keyStr, String valueStr, String sceneName, String chainName, int requestTimeOut,String token);


    /**
     * 根据环境获取对应的服务器列表
     *
     * @param algoBackendDO 算法后端对象
     * @param modelEnv      环境标识
     * @return 服务器列表
     */
    List<String> getServerListByEnv(AlgoBackendDO algoBackendDO, String modelEnv, boolean needGrpc);

    /**
     * 当前环境是否和真实请求环境一致
     *
     * @param modelEnv      环境标识
     * @return 是否一致
     */
    boolean isDifferentMayaEnv(String modelEnv);

    /**
     * 根据模型获取部署状态
     * model  模型名称
     * needRealTime 是否需要实时获取， 默认为false
     * @return
     */
    JSONObject getModelAvailableServers(String modelName, Boolean needRealTime);

    /**
     * 根据模型获取部署状态
     * sceneName  sceneName
     * chainName chainName
     * @return
     */
    JSONObject getModelAvailableServers(String sceneName, String chainName);

    /**
     * 获取模型的真实环境， 输入为prod/pre/auto, 输出为prod/pre
     * modelEnv modelEnv
     * @return
     */
    String getActualEnv(String modelEnv);

    /**
     * 是否包含了部署环境
     * modelName modelName
     * modelEnv modelEnv
     * @return
     */
    boolean hasAvailableServers(String modelName, String modelEnv);

    /**
     * 合并缓存数据
     */
    JSONObject combineEnvServersDetail(Serializable cacheStr, JSONObject curDetail);

}
