package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.http.HttpClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 计算token的服务实现类
 */
@Service
public class CalculateTokenServiceImpl implements CalculateTokenService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CalculateTokenServiceImpl.class);

    private static final String URI = "/api/calculate_token";

    private static final Long SKIP_TOKEN_QTY = 10L;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Override
    public Long getTokenQty(String content) {
        String response = null;
        try {
            if (StringUtils.isBlank(content)) {
                return 0L;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("text", content);
            response = HttpClient.post(codeGPTDrmConfig.getAntnluServiceUrl() + URI)
                    .content(jsonObject.toJSONString())
                    .syncExecuteWithExceptionThrow(30000L);
            JSONObject jsonRet = JSONObject.parseObject(response);
            if (jsonRet.containsKey("success") && jsonRet.getBoolean("success")) {
                return jsonRet.getLong("tokenQty");
            }
            LOGGER.warn("antnluService calculate_token failed,response:{},content:{}", response, content);
        } catch (Exception e) {
            LOGGER.error("antnluService calculate_token failed,response:" + response + "content:" + content, e);
        }
        if(codeGPTDrmConfig.isSkipCalculateToken()){
            return SKIP_TOKEN_QTY;
        }
        throw new BizException(ResponseEnum.CALCULATE_TOKEN_FAILED);
    }

}
