package com.alipay.codegencore.service.common.limiter.qpx;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tbase.model.RateLimiterTuple;
import com.alipay.codegencore.service.common.limiter.RateLimiter;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

/**
 * QPX 限流器实现类
 */
public class TBaseQpxRateLimiter implements RateLimiter, Serializable {
    private static final long serialVersionUID = 1L;

    private Logger logger = LoggerFactory.getLogger(TBaseQpxRateLimiter.class);

    private final String rateLimiterKey;
    private Long windowTimeMills;
    private Long windowTotalQuota;

    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    private CodeGPTDrmConfig drmConfig;

    /**
     * 限流器初始化函数
     *
     * @param defaultCacheManager
     * @param drmConfig
     */
    public TBaseQpxRateLimiter(String rateLimiterKey, RefreshableCommonTbaseCacheManager defaultCacheManager, CodeGPTDrmConfig drmConfig) {
        this.rateLimiterKey = rateLimiterKey;
        this.defaultCacheManager = defaultCacheManager;
        this.drmConfig = drmConfig;
        JSONObject qpxConfig = drmConfig.getQpxRateLimiterConfigJson().getJSONObject(rateLimiterKey);
        windowTimeMills = qpxConfig.getLong("windowTimeMills");
        windowTotalQuota = qpxConfig.getLong("windowTotalQuota");
    }

    /**
     * 限流器初始化
     * @param rateLimiterKey
     * @param defaultCacheManager
     * @param drmConfig
     * @param windowTimeMills
     * @param windowTotalQuota
     */
    public TBaseQpxRateLimiter(String rateLimiterKey, RefreshableCommonTbaseCacheManager defaultCacheManager, CodeGPTDrmConfig drmConfig, Long windowTimeMills, Long windowTotalQuota) {
        this.rateLimiterKey = rateLimiterKey;
        this.defaultCacheManager = defaultCacheManager;
        this.drmConfig = drmConfig;
        this.windowTimeMills = windowTimeMills;
        this.windowTotalQuota = windowTotalQuota;
    }

    /**
     * 限流器初始化函数
     * @param rateLimiterKey
     * @param windowTimeMills
     * @param windowTotalQuota
     */
    public TBaseQpxRateLimiter(String rateLimiterKey) {
        this.rateLimiterKey = rateLimiterKey;
    }

    public void setLogger(Logger logger) {
        this.logger = logger;
    }

    public void setDefaultCacheManager(RefreshableCommonTbaseCacheManager defaultCacheManager) {
        this.defaultCacheManager = defaultCacheManager;
    }

    public void setWindowTimeMills(Long windowTimeMills) {
        this.windowTimeMills = windowTimeMills;
    }

    public void setWindowTotalQuota(Long windowTotalQuota) {
        this.windowTotalQuota = windowTotalQuota;
    }

    public void setDrmConfig(CodeGPTDrmConfig drmConfig) {
        this.drmConfig = drmConfig;
    }

    @Override
    public boolean tryAcquire() {
        return tryAcquire(1, 0);
    }

    private boolean tryAcquire(long costTokenNum, long timeoutMillis) {
        timeoutMillis = Math.min(timeoutMillis, getRateLimiterMaxWaitMills());
        long startTime = System.currentTimeMillis();
        while (true) {
            RateLimiterTuple rateLimiterTuple = defaultCacheManager.ratelimiter(rateLimiterKey, windowTotalQuota, costTokenNum, "PX", windowTimeMills);
            if (rateLimiterTuple.limited == 0) {
                return true;
            }
            if (System.currentTimeMillis() - startTime >= timeoutMillis) {
                break;
            }
            int sleepMillis = getRateLimiterSleepMills();
            try {
                Thread.sleep(sleepMillis);
            } catch (InterruptedException e) {
                logger.error("thread interrupted!", e);
            }
        }
        // 等待超时
        logger.warn("{} try acquire get rate limiter timeout!", rateLimiterKey);
        return false;
    }

    /***
     * 获取每次获取令牌的时间间隔
     * @return
     */
    private int getRateLimiterSleepMills() {
        return drmConfig.getRateLimiterSleepMills();
    }

    /***
     * 获取每次获取令牌的时间间隔
     * @return
     */
    private int getRateLimiterMaxWaitMills() {
        return drmConfig.getRateLimiterMaxWaitMills();
    }

    public Long getWindowTimeMills() {
        return windowTimeMills;
    }

    public Long getWindowTotalQuota() {
        return windowTotalQuota;
    }
}
