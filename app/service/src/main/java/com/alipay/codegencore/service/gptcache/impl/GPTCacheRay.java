package com.alipay.codegencore.service.gptcache.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import com.antgroup.ray.serving.client.api.Service;
import com.antgroup.ray.serving.client.spi.ServiceClient;
import com.antgroup.ray.serving.common.model.Result;
import com.antgroup.ray.serving.common.model.router.RouteInfo;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 2023-05-24 15:26:42
 */
@org.springframework.stereotype.Service
public class GPTCacheRay{
    private static final Logger LOGGER = LoggerFactory.getLogger("GPTCACHERAY");

    /**
     * 数据库模式，可选值为dev, sit, prod
     */
    @AppConfig("dbmode")
    public String dbmode;

    private ServiceClient rayServiceClient = null;
    private RouteInfo routeInfo = null;

    /**
     * 调用gptCache服务去获取缓存
     *
     * @param model 模型类型
     * @param chatMessageList list
     */
    public JSONObject getCache(String model, List<ChatMessage> chatMessageList) {
        try {
            ServiceClient serviceClient = getRayServingClient();
            RouteInfo routeInfoTemp = getRouteInfo();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", "query");
            JSONObject modelJsonObject = new JSONObject();
            modelJsonObject.put("model", model);
            jsonObject.put("scope", modelJsonObject);
            jsonObject.put("model", model);
            jsonObject.put("query", chatMessageList);
            LOGGER.info("getCache jsonObject {}", jsonObject.toJSONString());
            Object[] params = new Object[]{jsonObject.toJSONString()};
            Result<Object> callResult = serviceClient.call("gptcache_serving", routeInfoTemp, params);

            if (callResult == null) {
                return null;
            }
            LOGGER.info("getCache String.valueOf(callResult.getData()):{}", callResult.getData());
            JSONObject responseData = JSON.parseObject(String.valueOf(callResult.getData()));
            LOGGER.info("getCache responseData:{}", responseData);
            return responseData;
        } catch (Exception e) {
            LOGGER.warn("getCache exception, error message:{}", ExceptionUtils.getMessage(e));
        }

        return null;
    }

    /**
     * 写到gptCache服务
     *
     * @param model 模型类型
     * @param query 问题
     * @param answer 回答
     */
    public JSONObject putCache(String model, List<ChatMessage> query, String answer) {
        try {
            ServiceClient serviceClient = getRayServingClient();
            RouteInfo routeInfoTemp = getRouteInfo();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", "insert");
            JSONObject scope = new JSONObject();
            scope.put("model", model);
            jsonObject.put("scope", scope);
            ArrayList<JSONObject> chatInfolist = new ArrayList<>();
            JSONObject chatInfo = new JSONObject();
            chatInfo.put("query", query);
            chatInfo.put("answer", answer);
            chatInfolist.add(chatInfo);
            jsonObject.put("chat_info", chatInfolist);
            LOGGER.info("putCache jsonObject {}", jsonObject.toJSONString());
            Object[] params = new Object[]{jsonObject.toJSONString()};
            Result<Object> callResult = serviceClient.call("gptcache_serving", routeInfoTemp, params);
            if (callResult == null || callResult.getData() == null) {
                return null;
            }
            LOGGER.info("putCache data:{}", callResult.getData().toString());
            return JSON.parseObject(String.valueOf(callResult.getData()));
        } catch (Exception e) {
            LOGGER.warn("putCache exception, error message: {}", ExceptionUtils.getMessage(e));
        }

        return null;
    }

    /**
     * 清楚缓存
     * @param model 模型
     * @return
     */
    public JSONObject removeCache(String model) {
        try {
            ServiceClient serviceClient = getRayServingClient();
            RouteInfo routeInfoTemp = getRouteInfo(10000);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", "remove");
            jsonObject.put("remove_type", "truncate_by_model");
            JSONObject scope = new JSONObject();
            scope.put("model", model);
            jsonObject.put("scope", scope);

            LOGGER.info("removeCache jsonObject {}", jsonObject.toJSONString());
            Object[] params = new Object[]{jsonObject.toJSONString()};
            Result<Object> callResult = serviceClient.call("gptcache_serving", routeInfoTemp, params);
            if (callResult == null || callResult.getData() == null) {
                return null;
            }
            JSONObject responseData = JSON.parseObject(String.valueOf(callResult.getData()));
            LOGGER.info("removeCache responseData:{}", responseData);
            return responseData;
        } catch (Exception e) {
            LOGGER.error("removeCache exception ", e);
        }

        return null;
    }

    private ServiceClient getRayServingClient(){
        try {
            if (rayServiceClient == null){
                Map<String, String> config = new HashMap<>();
                config.put("ray.serving.client.call.retry.times", "0");
                Result<ServiceClient> serviceResult = Service.getServiceClient(config);
                rayServiceClient = serviceResult.getData();
            }
        } catch (Exception e){
            LOGGER.error("getRayServingClient ", e);
            rayServiceClient = null;
        }

        return rayServiceClient;
    }

    private RouteInfo getRouteInfo(){
        return getRouteInfo(2000);
    }

    private RouteInfo getRouteInfo(long timeout){
        try {
            if (routeInfo == null){
                routeInfo = new RouteInfo();
                //和之前创建endpoint的时候传入的path保持一致
                if(!"prod".equalsIgnoreCase(dbmode)){
                    routeInfo.setRoute("/gptcache_dev");
                } else {
                    routeInfo.setRoute("/gptcache");
                }
                //----注意：如果请求体大于32k，请设置一下参数，否则会出现write overflow问题----
                Map<String, String> config = new HashMap<>();
                config.put("ray.serving.client.sofa.rpc.connection.num", "5");
                config.put("ray.serving.client.call.timeout", String.valueOf(timeout));
                routeInfo.setConfig(config);
            }
        } catch (Exception e){
            LOGGER.error("getRouteInfo ", e);
            routeInfo = null;
        }

        return routeInfo;
    }
}
