package com.alipay.codegencore.service.tool.learning;

import com.alipay.codegencore.model.model.DocumentToChatResponse;
import com.alipay.codegencore.model.request.DocumentToChatRequestBean;


/**
 * 该接口定义了一个文档转聊天服务，可以将文档中的内容转换为聊天会话。
 */
public interface DocumentChatService {

    /**
     * 将文档转换为聊天会话的方法。
     *
     * @param documentToChatRequestBean 包含需要转换的文档内容和相关参数的请求对象。
     * @return 返回一个包含转换后的聊天会话信息的响应对象。
     */
    DocumentToChatResponse documentToChatPluginService(DocumentToChatRequestBean documentToChatRequestBean);

}



