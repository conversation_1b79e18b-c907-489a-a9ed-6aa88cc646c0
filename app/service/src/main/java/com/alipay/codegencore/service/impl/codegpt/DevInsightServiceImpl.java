/*
 * Ant Group
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.alipay.codegencore.service.impl.codegpt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.service.codegpt.DevInsightService;
import com.alipay.codegencore.utils.adapter.HttpAdapter;
import com.alipay.codegencore.utils.adapter.HttpAdapterResponse;
import com.alipay.sofa.platform.cache.impl.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 研发洞察service
 */
@Service
public class DevInsightServiceImpl implements DevInsightService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DevInsightServiceImpl.class);
    /**
     * 工号或者花名 支持模糊搜索员工
     *
     * @param query
     * @return
     */
    @Override
    public JSONArray queryUser(String query) {
        Map<String, Object> params = getAimsParams();
        params.put("query", query);
        StringBuilder completeUrl = new StringBuilder("https://aims.alipay.com/service/api/dept/search/user" + "?");
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            completeUrl.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        LOGGER.info("调用研发洞察{}" , completeUrl);
        HttpAdapterResponse response = null;
        response = HttpAdapter.request("GET", "https://aims.alipay.com/service/api/dept/search/user", params, new HashMap<>(), null);
        JSONObject retContent = JSONObject.parseObject(response.getRetContent());
        LOGGER.info("研发洞察返回{}" , retContent);
        return JSON.parseArray(retContent.getJSONArray("object").toJSONString());
    }

    /**
     * 组装请求参数
     * @return
     */
    private Map<String, Object> getAimsParams() {
        String systemId = "C-Stone";
        String date = dateToStringWithFormat(new Date(), "yyyyMMddHH");
        String secretKey = DigestUtils.md5Hex(systemId + "SMk8v3rj&y8OUvei" + date);
        Map<String, Object> params = new HashMap<>();
        params.put("systemId", systemId);
        params.put("secretKey", secretKey);
        return params;
    }

    /**
     * 时间格式转换
     * @param date
     * @param format
     * @return
     */
    private String dateToStringWithFormat(Date date, String format) {

        if (date==null) {
            return  null;
        }

        SimpleDateFormat sdf=new SimpleDateFormat(format, Locale.US);

        return sdf.format( date );
    }
}