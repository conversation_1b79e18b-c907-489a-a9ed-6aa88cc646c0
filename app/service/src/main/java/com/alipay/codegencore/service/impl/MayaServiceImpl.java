package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.arks.client.Item;
import com.alipay.arks.client.enums.LoadBalancingPolicy;
import com.alipay.arks.client.enums.ModelServerType;
import com.alipay.arks.client.enums.RouterPriority;
import com.alipay.arks.client.enums.RpcMode;
import com.alipay.codegencore.dal.example.AlgoBackendDOExample;
import com.alipay.codegencore.dal.mapper.AlgoBackendDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.RecordLogEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.impl.model.AlgoModelHealthUtilService;
import com.alipay.codegencore.utils.CollectLogUtils;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.maya.MayaClient;
import com.alipay.maya.config.MayaClientConfig;
import com.alipay.maya.exception.MayaException;
import com.alipay.maya.model.MayaRequest;
import com.alipay.maya.model.MayaResponse;
import com.alipay.mist.shade.google.common.collect.ImmutableMap;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * maya服务实现
 * <AUTHOR>
 */
@Service
public class MayaServiceImpl implements MayaService {
    private static final Logger LOGGER = LoggerFactory.getLogger( MayaServiceImpl.class );

    private static final String PRE_ANTVIP_ACQUIRE_URL = "http://antvip-pool.cz99s.alipay.com:9500/antvipDomains/acquire";
    private static final String PROD_ANTVIP_ACQUIRE_URL = "http://antvip-pool.global.alipay.com:9500/antvipDomains/acquire";
    private static final String GET_SERVER_BY_NAME = "https://aistudio.alipay.com/api/v1/mpsMayaService/queryModelService";
    private static final String TOKEN_PRIVATE = "7e9174f2-98e2-4aca-a048-5451cf85d0fe";


    @AppConfig("spring.application.name")
    private String appName;

    @Resource
    private AlgoBackendDOMapper algoBackendDOMapper;

    @Resource
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;

    @Resource
    private AlgoBackendService algoBackendService;

    @Override
    public MayaRequest getMayaRequest(String keyStr, String valueStr, String sceneName, String chainName, int requestTimeOut, String token) {
        // 构造请求
        MayaRequest request = new MayaRequest();
        // 业务场景名,必填,需要根据服务名寻址
        request.setSceneName(sceneName);
        // 必填,服务版本会作为url路径http
        request.setChainName(chainName);
        // 服务端类型,默认ModelServerType.MAYA
        request.setServerType(ModelServerType.MAYA);
        // 整体超时时间,不设置默认从MayaClientConfig里获取是600ms
        request.setRequestTimeOut(requestTimeOut);
        // 可选,建立连接超时时间,不设置默认从MayaClientConfig里获取是100ms
        request.setConnectTimeOut(1000);
        // 可选,等待服务端返回超时时间,不设置默认从MayaClientConfig里获取是500ms
        request.setReadTimeOut(requestTimeOut-1000);
        // N2S负载均衡策略 https://yuque.antfin.com/aii/aistudio/wra3bwby2bfu3wtt#snzVZ
        request.setLoadBalancingPolicy(LoadBalancingPolicy.N2S);
        // mayaClient默认使用的同城优先路由策略，同城没有实例才会跨城路由。
        // 对时延不敏感的业务方，或者gpu卡在不同城市间分布不均的业务方，可以开启跨城路由，来充分利用现有的所有gpu卡。
        request.setLbConfig(ImmutableMap.of("arks.antvip.arrow.cross.city","true"));
        // 此处填入调用token，token由服务开发者在接入管理页面申请，请向开发者获取
        if (StringUtils.isNotBlank(token)) {
            request.setAppToken(token);
        }

        // item,一个Item代表一次推理，n个Item就是一个长度为n的batch,返回结果也在response.items中
        List<Item> items = new ArrayList<>();
        request.setItems(items);
        Item item = new Item();
        items.add(item);
        item.setItemId("itemId1");
        Map<String, String> item1Features = new HashMap<>();
        item.setFeatures(item1Features);
        item1Features.put(keyStr, valueStr);
        // 如果是强类型和多维的tensor输入，则使用TensorFeature类型(有FE的模型不支持)
        // item.setTensorFeature();
        return request;
    }

    @Override
    public String getInferenceResult(String requestStr, String sceneName, String chainName, int requestTimeOut, String requestKey, String responseKey, boolean needGrpc) {
        return getInferenceResult(requestStr, sceneName, chainName, requestTimeOut, requestKey, responseKey, needGrpc, null, null);
    }

    @Override
    public String getInferenceResult(String requestStr, String sceneName, String chainName, int requestTimeOut, String requestKey, String responseKey, boolean needGrpc,
                                     AlgoBackendDO algoBackendDO, String modelEnv) {
        MayaClientConfig config = new MayaClientConfig();
        // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
        config.setAppName(appName);
        MayaClient mayaClient = MayaClient.getInstance(config);
        MayaRequest request = getMayaRequest(requestKey, requestStr, sceneName, chainName, requestTimeOut, AlgoBackendUtil.exactTokenConfig(algoBackendDO));
        if (needGrpc) {
            request.setRpcMode(RpcMode.GRPC);
        } else {
            request.setRpcMode(RpcMode.HTTP);
        }

        List<String> serverList = getServerListByEnv(algoBackendDO, modelEnv, needGrpc);
        if(null != serverList && serverList.size() > 0) {
            request.setServerList(serverList);
            request.setRouterPriority(RouterPriority.CONFIG);
        }

        LOGGER.info("request maya param:{}", JSON.toJSONString(request));

        MayaResponse response;
        try{
            response = mayaClient.call(request);
        } catch (MayaException mayaException) {
            LOGGER.error("request maya error MayaException", mayaException);
            if(null != mayaException.getErrorCode() && 0 != mayaException.getErrorCode().getIndex()) {
                throw new BizException(mayaException2Enum(mayaException.getErrorCode().getIndex()), mayaException.getMessage());
            } else {
                throw new BizException(ResponseEnum.AI_CALL_ERROR, mayaException);
            }
        } catch (Exception e){
            LOGGER.error("request maya error", e);
            throw new BizException(ResponseEnum.AI_CALL_ERROR, e);
        }

        LOGGER.info("request maya response:{}", JSON.toJSONString(response));
        if (response.getErrorCode() == 0) {
            LOGGER.info("process success : {}", JSON.toJSONString(response.getResults()));
            // 兼容原arks服务返回字段,只支持字符串类型返回
            return response.getItems().get(0).getAttributes().get(responseKey);
        } else {
            LOGGER.error("process failed: error code : {}, error name {}, error message {}",
                    response.getErrorCode(), response.getErrorName(), response.getErrorMsg());
            throw new BizException(mayaException2Enum(response.getErrorCode()), String.format("AI模型调用报错,错误信息: %s", response.getErrorMsg()));
        }
    }

    private String getValidModelEnv(AlgoBackendDO algoBackendDO, String modelEnv) {
        if(StringUtils.isNotBlank(modelEnv)
                && !modelEnv.equalsIgnoreCase("auto")) {
            return modelEnv;
        }

        if(algoBackendDO == null) {
            LOGGER.warn("algoBackendDO is null");
            return "auto";
        }

        if(algoBackendDO.getImplConfig() != null) {
           JSONObject jsonObject = JSONObject.parseObject(algoBackendDO.getImplConfig());
           if(null != jsonObject.get("modelEnv")) {
               String sessionModelEnv = jsonObject.getString("modelEnv");
               if(!sessionModelEnv.equalsIgnoreCase("auto")) {
                   return sessionModelEnv;
               }
           }
        }

        if(null == algoBackendDO.getModel() || algoBackendDO.getModel().isBlank()) {
            LOGGER.warn("algoBackendDO'model is null");
            return "auto";
        }

        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andModelEqualTo(algoBackendDO.getModel());

        List<AlgoBackendDO> algoBackendDOList = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        AlgoBackendDO ret = algoBackendDOList.isEmpty() ? null : algoBackendDOList.get(0);
        if(null == ret) {
            return "auto";
        }

        if(ret.getImplConfig() != null) {
            JSONObject jsonObject = JSONObject.parseObject(ret.getImplConfig());
            if(null != jsonObject.get("modelEnv")) {
                String modelEnvModelDb= jsonObject.getString("modelEnv");
                if(!modelEnvModelDb.equalsIgnoreCase("auto")) {
                    return modelEnvModelDb;
                }
            }
        }
        return "auto";
    }

    /**
     * 根据环境获取对应的服务器列表
     *
     * @param algoBackendDO 算法后端对象
     * @param modelEnv      环境标识
     * @return 服务器列表
     */
    @Override
    public List<String> getServerListByEnv(AlgoBackendDO algoBackendDO, String modelEnv, boolean needGrpc) {
        if(null == modelEnv || modelEnv.isBlank()) {
            LOGGER.info("modelEnv is null or empty");
            return null;
        }

        String validModelEnv = getValidModelEnv(algoBackendDO, modelEnv);
        if(!isDifferentMayaEnv(validModelEnv)) {
            return null;
        }

        String actualEnv =  getActualEnv(validModelEnv);
        JSONObject detail = getModelAvailableServers(algoBackendDO.getModel(), false);
        JSONArray serverArray = null;
        try {
            if(null != detail.get(actualEnv)
                && null != detail.getJSONObject(actualEnv).get("summary")
                && null != detail.getJSONObject(actualEnv).getJSONObject("summary").get("availableSever")) {
                serverArray = detail.getJSONObject(actualEnv).getJSONObject("summary").getJSONArray("availableSever");
            }
        } catch (Exception ex) {
            LOGGER.warn("get model env failed from tbase exception, {}", algoBackendDO.getModel(), ex);
        }

        if(serverArray == null) {
            LOGGER.warn("get model env failed from tbase, {}", algoBackendDO.getModel());
            return null;
        }

        String port = needGrpc ? "10001" : "10000";
        return serverArray.stream().map(s -> s + ":" + port).collect(Collectors.toList());
    }

    // 返回值：布尔类型，true表示环境相同，false表示环境不同
    @Override
    public boolean isDifferentMayaEnv(String modelEnv) {
        // 如果传入的环境参数为空，则认为环境相同
        if (null == modelEnv) {
            return false;
        }
        // 如果传入的环境参数为"auto"，则认为环境相同
        if (modelEnv.equalsIgnoreCase("auto")) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("modelEnv is default : auto");
            }
            return false;
        }
        String curEnv = VisableEnvUtil.isPrePub() ? "pre" : "prod";
        // 判断传入的环境参数和当前环境是否相同
        if (modelEnv.equalsIgnoreCase(curEnv)) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("env is same: {}", modelEnv);
            }
            return false;
        }
        // 如果传入的环境参数和当前环境不同，则认为环境不同
        return true;
    }

    // 保持原子化操作， 将实时获取server信息和写缓存内聚到getModelAvailableServers中
    @Override
    public JSONObject getModelAvailableServers(String modelName, Boolean needRealTime) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to getModelAvailableServers, {} , {}", modelName, needRealTime);
        }

        if(!needRealTime) {
            return getAvailableServersFromCache(modelName);
        }

        AlgoBackendDO ret = algoBackendService.getAlgoBackendByName(modelName);
        if(ret == null) {
            LOGGER.warn("no model info found in db : {}", modelName);
            throw new BizException(ResponseEnum.MODEL_CONFIG_ERROR);
        }
        JSONObject impl = JSON.parseObject(ret.getImplConfig());
        String sceneName = impl.getString("sceneName");
        String chainName = impl.getString("chainName");

        if(StringUtils.isEmpty(sceneName)
                || StringUtils.isEmpty(sceneName)) {
            LOGGER.warn("invalid model impl : {}", modelName);
            throw new BizException(ResponseEnum.MODEL_SCENE_CHAIN_NAME_EMPTY_ERROR);
        }

        String serverName = getServerName(sceneName, chainName);
        if(StringUtils.isEmpty(serverName)) {
            LOGGER.warn("get sever name failed : {}, {}", sceneName, chainName);
            throw new BizException(ResponseEnum.MODEL_SCENE_CHAIN_NAME_INVALID_ERROR);
        }

        JSONObject realTimeDetail = getModelAvailableServers(sceneName, chainName);
        String cacheKey = AppConstants.MODEL_ENV_CHECK_PREFIX + modelName;
        Serializable value = defaultCacheManager.get(cacheKey);
        JSONObject combinedDetail = combineEnvServersDetail(value, realTimeDetail);
        defaultCacheManager.setex(cacheKey, AppConstants.THREE_DAYS, combinedDetail);

        return combinedDetail;
    }

    @Override
    public JSONObject getModelAvailableServers(String sceneName, String chainName) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to get avaiable servers : {}, {}", sceneName, chainName);
        }
        String serverName = getServerName(sceneName, chainName);
        List<String> prodList = crossServerName2Ip(serverName, "prod");
        List<String> preList = crossServerName2Ip(serverName, "pre");
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("prod, pre ip list : {}, {}", JSONObject.toJSONString(prodList), JSONObject.toJSONString(preList));
        }

        recordLog(sceneName, chainName, serverName, prodList, "prod");
        recordLog(sceneName, chainName, serverName, preList, "pre");
        return constructResult(sceneName, chainName, serverName, prodList, preList);
    }

    @Override
    public String getActualEnv(String modelEnv) {
        if(modelEnv.equalsIgnoreCase("auto")) {
            return VisableEnvUtil.isPrePub() ? "pre" : "prod";
        }
        return modelEnv;
    }

    @Override
    public boolean hasAvailableServers(String modelName, String modelEnv) {
        JSONObject jsonObject = getModelAvailableServers(modelName, false);
        return jsonObject != null
                || jsonObject.get(modelEnv) != null
                || jsonObject.getJSONObject(modelEnv).get("summary") != null
                || jsonObject.getJSONObject(modelEnv).getJSONObject("summary").get("isAvailable") != null
                || jsonObject.getJSONObject(modelEnv).getJSONObject("summary").getBoolean("isAvailable");
    }

    public static ResponseEnum mayaException2Enum(Integer errorCode) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to transfer MayaException to ResponseEnum, {}", errorCode);
        }
        return ResponseEnum.getByCode(400 + errorCode);
    }

    public JSONObject combineEnvServersDetail(Serializable cacheStr, JSONObject curDetail) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to combine detail");
        }

        JSONObject combinedDetail = curDetail.clone();
        JSONObject cacheJson = null;
        if(null != cacheStr) {
            cacheJson = (JSONObject) cacheStr;
        }

        JSONObject prodHistory = new JSONObject();
        Boolean prodStatus = combinedDetail.getJSONObject("prod").getJSONObject("summary").getBoolean("isAvailable");
        if(prodStatus) {
            prodHistory.put("lastAvailable", curDetail.getJSONObject("prod").get("checkTime"));
            if( null != cacheJson
                    && null != cacheJson.getJSONObject("prod").get("checkHistory")
                    && null != cacheJson.getJSONObject("prod").getJSONObject("checkHistory").get("lastUnavailable")) {
                prodHistory.put("lastUnavailable", cacheJson.getJSONObject("prod").getJSONObject("checkHistory").getString("lastUnavailable"));
            }
        } else {
            prodHistory.put("lastUnavailable", curDetail.getJSONObject("prod").get("checkTime"));
            if( null != cacheJson
                    && null != cacheJson.getJSONObject("prod").get("checkHistory")
                    && null != cacheJson.getJSONObject("prod").getJSONObject("checkHistory").get("lastAvailable")) {
                prodHistory.put("lastAvailable", cacheJson.getJSONObject("prod").getJSONObject("checkHistory").getString("lastAvailable"));
            }
        }
        combinedDetail.getJSONObject("prod").put("checkHistory", prodHistory);

        JSONObject preHistory = new JSONObject();
        Boolean preStatus = combinedDetail.getJSONObject("pre").getJSONObject("summary").getBoolean("isAvailable");
        if(preStatus) {
            preHistory.put("lastAvailable", curDetail.getJSONObject("pre").get("checkTime"));
            if( null != cacheJson
                    && null != cacheJson.getJSONObject("pre").get("checkHistory")
                    && null != cacheJson.getJSONObject("pre").getJSONObject("checkHistory").get("lastUnavailable")) {
                preHistory.put("lastUnavailable", cacheJson.getJSONObject("pre").getJSONObject("checkHistory").getString("lastUnavailable"));
            }
        } else {
            preHistory.put("lastUnavailable", curDetail.getJSONObject("pre").get("checkTime"));
            if( null != cacheJson
                    && null != cacheJson.getJSONObject("pre").get("checkHistory")
                    && null != cacheJson.getJSONObject("pre").getJSONObject("checkHistory").get("lastAvailable")) {
                preHistory.put("lastAvailable", cacheJson.getJSONObject("pre").getJSONObject("checkHistory").getString("lastAvailable"));
            }
        }
        combinedDetail.getJSONObject("pre").put("checkHistory", preHistory);

        return combinedDetail;
    }

    private JSONObject constructResult(String sceneName, String chainName, String serverName,
                                       List<String> prodList, List<String> preList) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to construct result, {}, {}, {}", sceneName, chainName, serverName);
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("chainName", chainName);
        jsonObject.put("sceneName", sceneName);
        jsonObject.put("publishDomain", serverName);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date current = new Date(System.currentTimeMillis());
        String currentStr = dateFormat.format(current);

        // prod detail
        JSONObject prodJ = new JSONObject();
        JSONObject prodSummary = new JSONObject();
        if(null != prodList && prodList.size() > 0) {
            prodSummary.put("isAvailable", true);
            prodSummary.put("availableSever", prodList);
        } else {
            prodSummary.put("isAvailable", false);
            prodSummary.put("availableSever", null);
        }
        prodJ.put("summary", prodSummary);
        prodJ.put("checkTime", currentStr);
        prodJ.put("checkHistory", null);
        jsonObject.put("prod", prodJ);

        // pre detail
        JSONObject preJ = new JSONObject();
        JSONObject preSummary = new JSONObject();
        if(null != preList && preList.size() > 0) {
            preSummary.put("isAvailable", true);
            preSummary.put("availableSever", preList);
        } else {
            preSummary.put("isAvailable", false);
            preSummary.put("availableSever", null);
        }
        preJ.put("summary", preSummary);
        preJ.put("checkTime", currentStr);
        preJ.put("checkHistory", null);
        jsonObject.put("pre", preJ);

        return jsonObject;
    }

    private JSONObject getAvailableServersFromCache(String modelName) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to getAvailableServersFromCache, {}", modelName);
        }

        String tbKey = AppConstants.MODEL_ENV_CHECK_PREFIX + modelName;
        Serializable value = defaultCacheManager.get(tbKey);
        if(null == value) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("get tbase info is null : {}", tbKey);
            }
            return null;
        }

        return (JSONObject) value;
    }

    /**
     * 获取服务器地址
     *
     * @param sceneName 场景名
     * @param chainName 链路名
     * @return 返回服务器地址
     */
    private String getServerName(String sceneName, String chainName) {

        HttpResponse response = null;
        LOGGER.info("send request to server name, {}, {}", sceneName, chainName);
        HttpClient httpClient = null;
        String content = null;
        JSONObject sendData = new JSONObject();
        sendData.put("sceneName", sceneName);
        sendData.put("chainName", chainName);
        sendData.put("token", TOKEN_PRIVATE);

        try {
            HttpPost post = new HttpPost(GET_SERVER_BY_NAME);
            httpClient = HttpClientBuilder.create().build();
            ByteArrayEntity entity = new ByteArrayEntity(JSON.toJSONString(sendData).getBytes(StandardCharsets.UTF_8));
            post.setEntity(entity);
            post.setHeader("Content-Type", "application/json");
            response = httpClient.execute(post);
            content = EntityUtils.toString(response.getEntity());
        } catch (Exception ex) {
            // 如果出现异常，则记录日志并返回null
            LOGGER.error("get maya server ip failed, {}", sendData);
        }
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("get server content is {}", content);
        }
        if(StringUtils.isEmpty(content)) {
            return null;
        }

        return getServerName(content);
    }

    private String getServerName(String response) {
        JSONObject jsonObject = null;
        try {
            jsonObject = JSON.parseObject(response);
        } catch (Exception ex) {
            LOGGER.error("transfer response to json object failed, {}", response);
        }
        if(null == jsonObject) {
            LOGGER.error("after tranfer response is null, {}", response);
            return null;
        }
        JSONArray ja = jsonObject.getJSONArray("data");
        if(0 == ja.size()) {
            LOGGER.error("data size is 0, {}", response);
            return null;
        }

        JSONObject detail = (JSONObject) ja.get(0);
        if(detail == null
                || StringUtils.isEmpty(detail.getString("publishDomain"))) {
            LOGGER.error("publishDomain not existed, {}", response);
            return null;
        }
        return detail.getString("publishDomain") + ".global.alipay.com";
    }

    // 定义一个方法，接收两个参数：服务器名和环境名称
    private List<String> crossServerName2Ip(String serverName, String modelEnv) {
        JSONObject sendData = new JSONObject();
        // 创建一个HashMap对象，用于存储校验和
        JSONObject transferData = new JSONObject();
        // 将指定服务器名转换成"N"并添加到HashMap中
        transferData.put(serverName, "Y");
        // 将HashMap赋值给sendData
        sendData.put("vipDomainName2ChecksumMap", transferData);

        String url = modelEnv.equalsIgnoreCase("prod") ? PROD_ANTVIP_ACQUIRE_URL : PRE_ANTVIP_ACQUIRE_URL;
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("request antvip url is {}", url);
        }
        // 发送POST请求获取响应结果
        HttpResponse response = null;
        LOGGER.info("send request to antvip server, {}", JSON.toJSONString(sendData));
        HttpClient httpClient = null;
        String content = null;
        try {
            HttpPost post = new HttpPost(url);
            // ConnectionRequestTimeout: get connection from connection pool
            // ConnectTimeout : establish connection between client and sever
            // SocketTimeout : wait response from server
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(3000)
                    .setConnectTimeout(3000)
                    .setSocketTimeout(3000).build();
            post.setConfig(requestConfig);
            httpClient = HttpClientBuilder.create().build();
            ByteArrayEntity entity = new ByteArrayEntity(JSON.toJSONString(sendData).getBytes(StandardCharsets.UTF_8));
            post.setEntity(entity);
            response = httpClient.execute(post);
            content = EntityUtils.toString(response.getEntity());

        } catch (Exception ex) {
            // 如果出现异常，则记录日志并返回null
            LOGGER.error("get maya server ip failed, {}", sendData, ex);
        }

        LOGGER.info("get response to antvip server, {}", content);
        // 调用getIps方法，传入响应结果作为参数，返回IP列表
        return getIps(content, serverName);
    }

    private List<String> getIps(String input, String serverName) {
        JSONObject jsonObject = null;
        try {
            jsonObject = JSON.parseObject(input);
        } catch (Exception ex) {
            LOGGER.error("transfer server to json object failed, {}", input);
        }

        if(null == jsonObject) {
            LOGGER.error("transfer server to json result is null, {}", input);
            return null;
        }

        if(null == jsonObject.get("vipDomains")) {
            LOGGER.error("no vipDomain contained, {}", input);
            return null;
        }

        List<String> ips = new ArrayList<>();
        JSONArray realNodes = null;
        try {
            realNodes = jsonObject.getJSONArray("vipDomains");
            for(Object jb : realNodes) {
                JSONObject jbJson = (JSONObject) jb;
                if(null != jbJson
                        && null != jbJson.getString("name")
                        && serverName.equalsIgnoreCase(jbJson.getString("name"))) {
                    JSONArray nodes = jbJson.getJSONArray("realNodes");
                    for(Object node : nodes) {
                        JSONObject loop = (JSONObject) node;
                        if(loop.containsKey("ip")
                                && null != loop.get("ip")
                                && loop.containsKey("available")
                                && null != loop.get("available")) {
                            Boolean avilable = (Boolean) loop.get("available");
                            if(avilable) {
                                ips.add((String) loop.get("ip"));
                            }
                        }
                    }
                }
            }
        } catch(Exception ex) {
            LOGGER.error("force type transfer failed, {}", input);
        }

        if(ips.size() == 0) {
            LOGGER.warn("after filter, ips size is 0, {}", input);
            return null;
        }
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("the {} ips is {}", serverName, JSON.toJSONString(ips));
        }
        return ips;
    }

    public static boolean isMayaImpl(AlgoBackendDO algoBackendDO) {
        if("ChatGptModelHandler".equalsIgnoreCase(algoBackendDO.getImpl())
                || "AntGLMModelHandler".equalsIgnoreCase(algoBackendDO.getImpl())
                || "ChatGptModelHubHandler".equalsIgnoreCase(algoBackendDO.getImpl())){
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("impl is ChatGptModelHandler or AntGLMModelHandler, so not need check deploy, {}", algoBackendDO.getModel());
            }
            return false;
        }
        return true;
    }

    public static List<AlgoBackendDO> jumpLogic(List<AlgoBackendDO> algoBackendDOList) {
        final Map<String, AlgoBackendDO> existedModel = new HashMap<>();
        algoBackendDOList.stream().filter(a->StringUtils.isNotBlank(a.getModel())).forEach(a->existedModel.put(a.getModel(), a));
        final HashMap<String, AlgoBackendDO> processedModel = new HashMap<>();

        algoBackendDOList.stream().filter(a->StringUtils.isNotBlank(a.getModel())).forEach(
                a->{
                    if(StringUtils.isNotBlank(a.getJump())) {
                        if(existedModel.containsKey(a.getJump())) {
                            processedModel.put(a.getJump(), existedModel.get(a.getJump()));
                        }
                    } else {
                        processedModel.put(a.getModel(), a);
                    }
                }
        );

        return new ArrayList<>(processedModel.values());
    }

    private void recordLog(String sceneName, String chainName, String serverName, List<String> serverList, String modelEnv) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("sceneName", sceneName);
        detail.put("chainName", chainName);
        detail.put("serverName", serverName);
        detail.put("modelEnv", modelEnv);
        detail.put("serverSize", serverList == null ? 0 : serverList.size());
        CollectLogUtils.printCollectLog(RecordLogEnum.MODEL_ENV_INFO, detail);
    }
}
