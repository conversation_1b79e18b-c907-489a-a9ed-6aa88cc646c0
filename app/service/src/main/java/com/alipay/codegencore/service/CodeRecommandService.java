package com.alipay.codegencore.service;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.model.CompletionsCodeModel;
import com.alipay.codegencore.model.request.CompletionsRequestBean;
import com.alipay.codegencore.model.request.tsingyan.CompletionRequestBean;
import com.alipay.codegencore.model.response.tsingyan.TsingyanConfigDataModel;

import java.util.HashMap;
import java.util.Map;

/**
 * 代码推荐服务
 *
 * <AUTHOR>
 * 创建时间 2022-01-07
 */
public interface CodeRecommandService {

    /**
     * 补全推荐接口
     *
     * @param completionsRequestBean
     * @return
     */
    @Deprecated
    CompletionsCodeModel completions(CompletionsRequestBean completionsRequestBean);


    /**
     * 算法参数key
     * text：代码上文内容，插件上传
     */
    String KEY_TEXT = "text";
    /**
     * 算法参数key
     * length：补全token长度，默认drm中 {@link TsingyanConfigDataModel#getCompletionConfigModel} 对象中配置
     */
    String KEY_LENGTH = "length";
    /**
     * 算法参数key
     * nums:补全候选集长度,插件上传，默认drm中 {@link TsingyanConfigDataModel#getCompletionConfigModel} 对象中配置
     */
    String KEY_NUM = "nums";
    /**
     * 算法参数key
     * traceid：请求id,插件上传
     */
    String KEY_TRACEID = "traceid";


    /**
     * 组装算法的补全请求,算法接口说明参考：{@see https://yuque.antfin-inc.com/iikq97/ukuoxm/givz5exe3vz5tem2}
     * 备注：后续待大模型上线后实现会随之更新
     * @param completionRequestBean
     * @return
     */
    default Map<String, Object> assembleCompletionRequest(CompletionRequestBean completionRequestBean) {
        Map<String, Object> param = new HashMap<>(4);
        param.put(KEY_TEXT, completionRequestBean.getPrompt());
        param.put(KEY_TRACEID, completionRequestBean.getSessionId());
        param.put(KEY_LENGTH, AppConstants.TSINGYAN_PLUGIN_CONFIG_MODEL.getCompletionConfigModel().getCompletionTokenLength());
        param.put(KEY_NUM,completionRequestBean.getCompletionNum());
        return param;
    }

}
