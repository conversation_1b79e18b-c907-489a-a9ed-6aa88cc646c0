package com.alipay.codegencore.service.impl;

import com.alipay.codegencore.dal.example.PromptTemplateDOExample;
import com.alipay.codegencore.dal.mapper.PromptTemplateDOMapper;
import com.alipay.codegencore.model.domain.PromptTemplateDO;
import com.alipay.codegencore.service.common.PromptTemplateService;
import com.google.common.cache.Cache;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.09.23
 */
@Service
public class PromptTemplateServiceImpl implements PromptTemplateService {
    private static final Logger LOGGER = LoggerFactory.getLogger( PromptTemplateServiceImpl.class );

    @Resource
    private PromptTemplateDOMapper promptTemplateDOMapper;
    @Resource(name = "promptCachePool")
    private Cache<String, String> promptTemplateCache;

    @Override
    public String getPromptTemplateText(String name, boolean forceGetFromDb) {
        String templateText = null;
        try {
            if(forceGetFromDb){
                promptTemplateCache.invalidate(name);
            }
            templateText = promptTemplateCache.get(name, new Callable<String>() {
                @Override
                public String call(){
                    PromptTemplateDOExample promptTemplateDOExample = new PromptTemplateDOExample();
                    promptTemplateDOExample.createCriteria().andNameEqualTo(name);
                    List<PromptTemplateDO> promptTemplateDOS = promptTemplateDOMapper.selectByExample(promptTemplateDOExample);
                    if(CollectionUtils.isNotEmpty(promptTemplateDOS)){
                        return promptTemplateDOS.get(0).getTemplateText();
                    }
                    return null;
                }
            });
        }catch (Exception e){
            LOGGER.warn("获取模板{}异常:{}",name, e.getMessage());
        }
        return templateText;
    }
}
