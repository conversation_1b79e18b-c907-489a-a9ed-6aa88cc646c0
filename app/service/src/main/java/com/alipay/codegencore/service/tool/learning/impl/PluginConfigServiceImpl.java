package com.alipay.codegencore.service.tool.learning.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.AlgoBackendDOExample;
import com.alipay.codegencore.dal.mapper.AlgoBackendDOMapper;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.tool.learning.PluginTypeEnum;
import com.alipay.codegencore.model.tool.learning.WorkflowConfigCheckResult;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.PluginConfigService;
import com.alipay.codegencore.service.tool.learning.ToolLearningUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.SafeConstructor;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class PluginConfigServiceImpl implements PluginConfigService {

    private static final Logger LOGGER = LoggerFactory.getLogger( PluginConfigServiceImpl.class );

    @Resource
    private AlgoBackendDOMapper algoBackendDOMapper;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    /**
     * 校验工作配置文件
     *
     * @param yamlText yaml文本
     * @return 校验结果
     */
    @Override
    public WorkflowConfigCheckResult checkWorkFlowYaml(String yamlText) {
        //用snakeYaml反序列化yaml文本，得到一个map，如果报错，返回错误信息
        // SafeConstructor because we may not trust the server or a moderator
        Yaml yaml = new Yaml(new SafeConstructor());
        Map<String, Object> data;
        try {
            data = yaml.load(yamlText);
        } catch (Exception e) {
            LOGGER.error("yaml解析失败", e);
            return new WorkflowConfigCheckResult(false, "YAML解析失败: " + e.getMessage(), "");
        }

        //最外层模块的校验，需要包含info, params, stages三个字段，其中info和stages字段必须存在
        if (!data.containsKey("info")) {
            return new WorkflowConfigCheckResult(false, "缺少info配置，插件相关基础信息无法解析","");
        }

        if (!data.containsKey("stages")) {
            return new WorkflowConfigCheckResult(false, "缺少stages配置，插件对应调用流程无法发起", "");
        }

        WorkflowConfigCheckResult result;
        //校验info字段
        result = checkInfo(data.get("info"));
        if (!result.isSuccess()) {
            return result;
        }

        //校验params字段
        //解析params字段得到一个参数名列表，参数名为name，下面的校验会用到
        List<String> paramNameList = new ArrayList<>();
        if (data.containsKey("params")) {
            result = checkParams(data.get("params"));
            if (!result.isSuccess()) {
                return result;
            }
            List<Map<String, Object>> params = (List<Map<String, Object>>) data.get("params");
            for (Map<String, Object> param : params) {
                paramNameList.add((String) param.get("name"));
            }

            //query字段是默认字段，不会写到配置文件中，但是要加入这个列表
            paramNameList.add("query");
        }

        // 校验stages字段
        // 判断插件的type，不同的type有不同的校验
        result = checkStages(data.get("stages"));
        if (!result.isSuccess()) {
            return result;
        }
        String pluginType = getPluginTypeByInfo(data.get("info"));
        if (StringUtils.equalsIgnoreCase(pluginType, PluginTypeEnum.PIPELINE.getName())) {
            result = checkPipelineStages(data, paramNameList);
        } else if (StringUtils.equalsIgnoreCase(pluginType, PluginTypeEnum.API.getName())) {
            result = checkApiStages(data);
        }

        return result.isSuccess() ? new WorkflowConfigCheckResult(true, "校验成功", "") : result;
    }

    /**
     * 解析工作流配置文件
     *
     * @param yamlText yaml文本
     * @return 解析结果
     */
    @Override
    public Map<String, Object> parseWorkFlowYaml(String yamlText) {
        // SafeConstructor because we may not trust the server or a moderator
        Yaml yaml = new Yaml(new SafeConstructor());
        try {
            return yaml.load(yamlText);
        } catch (Exception e) {
            LOGGER.error("解析工作流配置文件失败", e);
            return null;
        }
    }

    /**
     * 将aci信息添加到工作流配置文件中
     *
     * @param yamlText
     * @param aciInfo
     * @return
     */
    @Override
    public String addAciInfoToWorkflowYaml(String yamlText, String aciInfo) {
        JSONObject aciInfos = JSONObject.parseObject(aciInfo);
        Map<String, Object> data = parseWorkFlowYaml(yamlText);
        try {
            if(aciInfos.containsKey("component")){
                aciInfos.put("ymlString",buildPipelineYml(aciInfos.getString("component")));
            }
            List<Map<String, Object>> params = (List<Map<String, Object>>) data.get("params");
            for (Map<String, Object> param : params) {
                if (aciInfos.containsKey(param.get("name"))) {
                    param.put("default", aciInfos.getString((String) param.get("name")));
                }
            }
            Map<String, Object> info = (Map<String, Object>) data.get("info");
            if (aciInfos.containsKey("name")) {
                info.put("name", aciInfos.getString("name"));
            }
            if (aciInfos.containsKey("description")) {
                info.put("description", aciInfos.getString("description"));
            }
            DumperOptions options = new DumperOptions();
            options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
            options.setPrettyFlow(true);
            Yaml yaml = new Yaml(options);
            return yaml.dump(data);
        } catch (Exception e) {
            LOGGER.error("添加aci信息到配置失败", e);
            return null;
        }
    }


    /**
     * info字段校验。
     *
     * @param infoConfig info字段的配置
     * @return 校验结果
     */
    private WorkflowConfigCheckResult checkInfo(Object infoConfig) {
        //info字段校验：首先info字段是一个Map，
        //name,description,version字段必须存在
        if (!(infoConfig instanceof Map)) {
            // 返回校验结果
            return new WorkflowConfigCheckResult(false, "info字段必须是Map类型", "info");
        }

        Map<String, Object> infoConfigMap = (Map<String, Object>) infoConfig;

        // 检查必需字段
        String[] requiredFields = {"name", "version", "description"};
        for (String field : requiredFields) {
            if (!infoConfigMap.containsKey(field)) {
                // 返回校验结果
                return new WorkflowConfigCheckResult(false,
                        "info字段必须包含" + field + "字段",
                        "info." + field);
            }
        }

        // 如果所有必要字段都存在，返回成功的校验结果
        return new WorkflowConfigCheckResult(true, "校验成功", "info");
    }

    /**
     * params字段校验
     *
     * @param paramsConfig params字段的配置
     * @return 校验结果
     */
    private WorkflowConfigCheckResult checkParams(Object paramsConfig) {
        //params字段校验：params需要时一个list，list中的元素必须是map
        if(!(paramsConfig instanceof List)) {
            return new WorkflowConfigCheckResult(false, "params字段必须是列表", "params");
        }

        List paramsList = (List) paramsConfig;

        for(int i = 0; i < paramsList.size(); i++) {
            Object param = paramsList.get(i);
            if(!(param instanceof Map)) {
                return new WorkflowConfigCheckResult(false, "列表中的元素必须是字典", "params[" + i + "]");
            }

            //params中列表的元素校验：每个元素必须包含name, required字段，而且插件工作流在前期只支持一个query参数，所以所有参数的default字段现在也必须存在，后续可以不用设置
            //以及schema字段，且该schema字段中还需要包含type字段
            Map paramMap = (Map) param;
            if(!paramMap.containsKey("name")
                    || !paramMap.containsKey("schema")
                    || !paramMap.containsKey("required")) {
                return new WorkflowConfigCheckResult(false, "元素必须包含name,schema,required字段", "params[" + i + "]");
            }

            Map schemaMap = (Map) paramMap.get("schema");
            if(!schemaMap.containsKey("type")) {
                return new WorkflowConfigCheckResult(false, "schema 字段必须包含 type 字段", "params[" + i + "].schema.type");
            }
        }

        return new WorkflowConfigCheckResult(true,  "校验成功!", "params");
    }

    /**
     * stages字段校验
     *
     * @param stageConfig stages字段的配置
     * @return 校验结果
     */
    private WorkflowConfigCheckResult checkStages(Object stageConfig) {
        //stages字段校验：stages字段是一个list
        if (!(stageConfig instanceof List)) {
            return new WorkflowConfigCheckResult(false, "stages字段需要是一个list", "stages");
        }
        List<Map<String, String>> stages = (List<Map<String, String>>) stageConfig;
        //stage列表中元素校验
        List<String> correctOrder = Lists.newArrayList("preRequest", "llm", "postRequest", "summary");
        int prevIndex = -1;
        for (int i = 0; i < stages.size(); i++) {
            Map<String, String> stage = stages.get(i);
            //检查每个元素都包含name字段
            if (!stage.containsKey("name")) {
                return new WorkflowConfigCheckResult(false, "每个元素必须包含name字段", "stages[" + i + "].name");
            }
            String stageName = stage.get("name");
            //找出当前阶段名称在预定义的正确顺序数组中的索引
            int currentIndex = correctOrder.indexOf(stageName);
            //检查当前阶段是否存在，且顺序是正确的
            if(currentIndex == -1 || currentIndex <= prevIndex){
                return new WorkflowConfigCheckResult(false, "元素顺序是固定的，不能颠倒顺序", "stages[" + i + "].name");
            }
            prevIndex = currentIndex;
        }
        if (stages.isEmpty() ) {
            return new WorkflowConfigCheckResult(false, "stage不能为空", "stages");
        }
        return new WorkflowConfigCheckResult(true, "校验成功", "");
    }

    /**
     * preRequest阶段校验
     *
     * @param preRequestStageConfig preRequest阶段的配置
     * @return 校验结果
     */
    private WorkflowConfigCheckResult checkPreRequestStage(String pluginType, Map<String, Object> preRequestStageConfig) {
        if(preRequestStageConfig == null){
            return new WorkflowConfigCheckResult(true, "preRequest阶段配置为空，跳过校验", "");
        }
        //preRequest阶段校验：必须包含api_list字段，api_list是一个列表
        if (!preRequestStageConfig.containsKey("api_list")) {
            return new WorkflowConfigCheckResult(false, "缺少api_list字段", "stages[preRequest].api_list");
        }
        Object apiListObject = preRequestStageConfig.get("api_list");
        if (!(apiListObject instanceof List)) {
            return new WorkflowConfigCheckResult(false, "api_list不是一个列表", "stages[preRequest].api_list");
        }

        List<Object> apiList = (List<Object>) apiListObject;
        for (int i = 0; i < apiList.size(); i++) {
            if (!(apiList.get(i) instanceof Map)) {
                return new WorkflowConfigCheckResult(false, "api_list中的元素不是一个map", "stages[preRequest].api_list[" + i + "]");
            }

            Map<String, Object> api = (Map<String, Object>) apiList.get(i);
            //api_list校验：是一个map，必须要包含name, role, url, responses对象
            if (!(api.containsKey("name") && api.containsKey("role") && api.containsKey("url") && api.containsKey("responses"))) {
                return new WorkflowConfigCheckResult(false, "api_list中元素缺少name, role, url, responses中的至少一个字段", "stages[preRequest].api_list[" + i + "]");
            }

            // responses对象校验，是一个符合OAS协议的response
            Map<String, Object> responses = (Map<String, Object>) api.get("responses");
            WorkflowConfigCheckResult checkResult = checkOASResponse(pluginType, responses, "stages[preRequest].api_list[" + i + "].responses");
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }

        return new WorkflowConfigCheckResult(true, "校验通过", "");
    }

    /**
     * llm阶段校验
     *
     * @param llmStageConfig llm阶段的配置
     * @return 校验结果
     */
    private WorkflowConfigCheckResult checkLLMStage(Map<String, Object> llmStageConfig, List<String> paramNameList, List<String> preResponseKeyList) {
        if(llmStageConfig == null){
            return new WorkflowConfigCheckResult(true, "llm阶段配置为空，跳过校验", "");
        }

        //llm阶段校验：必须包含modelName和promptTemplate字段
        if (!llmStageConfig.containsKey("modelName") || !llmStageConfig.containsKey("promptTemplate")) {
            return new WorkflowConfigCheckResult(false, "llm阶段缺少modelName或promptTemplate字段", "stages[llm].{modelName/promptTemplate}");
        }

        //modelName字段校验：是一个字符串，且modelName必须能在数据库中找到一个存在的模型
        Object modelName = llmStageConfig.get("modelName");
        if (!(modelName instanceof String) || !existModelInDB((String)modelName)){
            return new WorkflowConfigCheckResult(false, "modelName字段非字符串类型或数据库中找不到对应模型", "stages[llm].modelName");
        }

        //promptTemplate字段校验：promptTemplate使用的是python中fstring的语法，{}中的字段，必须是params，preResponse中存在的字段，而且是通过`params.`或者`preResponse.`引用
        String promptTemplate = (String)llmStageConfig.get("promptTemplate");
        List<String> fieldsInPromptTemplate = ToolLearningUtil.extractFieldsFromPromptTemplate(promptTemplate);

        List<String> legalFieldList = Lists.newArrayList("query", "params", "preResponse");
        for (String field : fieldsInPromptTemplate) {
            if (!field.contains(".")) {
                if (!legalFieldList.contains(field)) {
                    return new WorkflowConfigCheckResult(false, String.format("template中的%s字段引用格式错误，不包含.的字段必须是query,params,preResponse其中之一", field), "stages[llm].template");
                }
            } else {
                String realField = field.substring(field.indexOf('.') + 1);
                if ((field.startsWith("params.") && !paramNameList.contains(realField)) ||
                        (field.startsWith("preResponse.") && !preResponseKeyList.contains(realField))) {
                    return new WorkflowConfigCheckResult(false, String.format("template中的%s字段未在params, preResponse中定义", field), "stages[llm].template");
                }
            }
        }


        return new WorkflowConfigCheckResult(true, "llm阶段校验成功", "");
    }

    private boolean existModelInDB(String modelName){
        //查询数据库，判断是否存在该模型
        //return true if exist
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andModelEqualTo(modelName);

        List<AlgoBackendDO> algoBackendDOList = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        if(algoBackendDOList.isEmpty()){
            return false;
        }

        AlgoBackendDO ret = algoBackendDOList.get(0);
        return ret.getEnable();
    }

    /**
     * postRequest阶段校验
     *
     * @param postRequestStageConfig postRequest阶段的配置
     * @return 校验结果
     */
    private WorkflowConfigCheckResult checkPostRequestStage(Map<String, Object> postRequestStageConfig) {
        //postRequest阶段校验：必须包含url和responses字段
        if(!postRequestStageConfig.containsKey("url")) {
            return new WorkflowConfigCheckResult(false, "缺少'url'字段", "stages[postRequest]");
        }

        //校验是否包含responses字段
        if(!postRequestStageConfig.containsKey("responses")) {
            return new WorkflowConfigCheckResult(false, "缺少'responses'字段", "stages[postRequest]");
        }

        //responses对象校验，是一个符合OAS协议的response，OAS对象的的response校验使用方法：WorkflowConfigCheckResult checkOASResponse(Map<String, Object> responses, String preLocation)
        Map<String, Object> responses = (Map<String, Object>)postRequestStageConfig.get("responses");
        return checkOASResponse(PluginTypeEnum.PIPELINE.getName() ,responses, "stages[postRequest].responses");
    }

    /**
     * summary阶段校验
     *
     * @param summaryStageConfig summary阶段的配置
     * @return 校验结果
     */
    private WorkflowConfigCheckResult checkSummaryStage(Map<String, Object> summaryStageConfig,
                                                        List<String> paramNameList,
                                                        List<String> preResponseKeyList,
                                                        List<String> postResponseKeyList) {
        //summary阶段校验：除了name只包含template字段
        if (summaryStageConfig.keySet().size() != 2 || !summaryStageConfig.containsKey("template")) {
            return new WorkflowConfigCheckResult(false, "summary阶段的配置错误，只应包含template字段", "stages[summary].template");
        }

        //template字段校验：采用python中的fstring格式，{}中的字段，必须是params，preResponse, postResponse中存在字段，或者llmResult，除了llmResult，其他的字段需要通过`params.`, `preResponse.`或者`postResponse.`引用
        String template = (String) summaryStageConfig.get("template");
        List<String> fieldsInTemplate = ToolLearningUtil.extractFieldsFromPromptTemplate(template);

        List<String> legalFieldList = Lists.newArrayList("query", "llmResult", "params", "preResponse", "postResponse");
        for (String field : fieldsInTemplate) {
            if (!field.contains(".")) {
                if (!legalFieldList.contains(field)) {
                    return new WorkflowConfigCheckResult(false, String.format("template中的%s字段引用格式错误，不包含.的字段必须是query,llmResult,params,preResponse,postResponse其中之一", field), "stages[summary].template");
                }
            } else {
                String realField = field.substring(field.indexOf('.') + 1);
                if ((field.startsWith("params.") && !paramNameList.contains(realField)) ||
                        (field.startsWith("preResponse.") && !preResponseKeyList.contains(realField)) ||
                        (field.startsWith("postResponse.") && !postResponseKeyList.contains(realField))) {
                    return new WorkflowConfigCheckResult(false, String.format("template中的%s字段未在params, preResponse或postResponse中定义", field), "stages[summary].template");
                }
            }
        }

        return new WorkflowConfigCheckResult(true, "summary阶段校验成功", "");
    }


    /**
     * 校验OAS协议的response对象
     *
     * @param responses responses对象
     * @param preLocation 前置路径
     * @return 校验结果
     */
    private WorkflowConfigCheckResult checkOASResponse(String pluginType, Map<String, Object> responses, String preLocation) {
        // 需要包含 200，400，500 三个字段，其中 200 字段必须存在
        if (!responses.containsKey("200")) {
            return new WorkflowConfigCheckResult(false, "responses中必须包含200字段", preLocation);
        }

        for (Map.Entry<String, Object> entry : responses.entrySet()) {
            Object responseObject = responses.get(entry.getKey());
            if (!(responseObject instanceof Map)) {
                return new WorkflowConfigCheckResult(false, "response下的某个状态信息必须是一个map",  String.format("%s.%s", preLocation, entry.getKey()));
            }
            Map<String, Object> responseMap = (Map<String, Object>) responseObject;
            if (!responseMap.containsKey("description") || !responseMap.containsKey("content")) {
                return new WorkflowConfigCheckResult(false, "必须包含description和content字段", String.format("%s.%s", preLocation, entry.getKey()));
            }
            Object content = responseMap.get("content");
            if (!(content instanceof Map)) {
                return new WorkflowConfigCheckResult(false, "content字段必须是一个map", String.format("%s.%s.%s", preLocation, entry.getKey(), "content"));
            }
            Map<String, Object> mapContent = (Map<String, Object>) content;
            if (mapContent.size() != 1 || !mapContent.containsKey("application/json")) {
                return new WorkflowConfigCheckResult(false, "content字段必须包含且仅application/json字段", String.format("%s.%s.%s", preLocation, entry.getKey(), "content"));
            }
            Map<String, Object> jsonMap = (Map<String, Object>) mapContent.get("application/json");
            // schema 校验
            if (!jsonMap.containsKey("schema") || !(jsonMap instanceof Map)) {
                return new WorkflowConfigCheckResult(false, "application/json字段必须包含schema信息", String.format("%s.%s.%s", preLocation, entry.getKey(), "content.application/json"));
            }
            Map<String, Object> schemaMap = (Map<String, Object>) jsonMap.get("schema");
            // type 字段值必须为 object
            if (PluginTypeEnum.PIPELINE.getName().equalsIgnoreCase(pluginType)) {
                if (!"object".equals(schemaMap.get("type"))){
                    return new WorkflowConfigCheckResult(false, "schema的类型必须为object", String.format("%s.%s.%s", preLocation, entry.getKey(), "content.application/json.schema"));
                }
                WorkflowConfigCheckResult workflowConfigCheckResult = checkOASResponseProperty(schemaMap, preLocation, entry.getKey());
                if (!workflowConfigCheckResult.isSuccess()) {
                    return workflowConfigCheckResult;
                }
            }
        }

        return new WorkflowConfigCheckResult(true, "校验通过", preLocation);
    }

    private WorkflowConfigCheckResult checkOASResponseProperty(Map<String, Object> schemaMap, String preLocation, String key){
        // properties 字段校验
        if (!schemaMap.containsKey("properties") || !(schemaMap.get("properties") instanceof Map)) {
            return new WorkflowConfigCheckResult(false, "schema字段中的properties字段必须存在且为一个map", String.format("%s.%s.%s", preLocation, key, "content.application/json.schema"));
        }
        Map<String, Map<String, Object>> propertiesMap = (Map<String, Map<String, Object>>) schemaMap.get("properties");

        for (Map.Entry<String, Map<String, Object>> property : propertiesMap.entrySet()) {
            if (!property.getValue().containsKey("type") || !property.getValue().containsKey("description")) {
                return new WorkflowConfigCheckResult(false,
                        String.format("properties字段中的%s字段必须包含type和description字段", property.getKey()),
                        String.format("%s.%s.%s", preLocation, key, "content.application/json.schema.properties." + property.getKey()));
            }
        }

        // required 字段校验
        if (!schemaMap.containsKey("required") || !(schemaMap.get("required") instanceof List)) {
            return new WorkflowConfigCheckResult(false, "schema字段中的required字段必须存在且为一个list",  String.format("%s.%s.%s", preLocation, key, "content.application/json.schema"));
        }
        List<String> requiredList = (List<String>) schemaMap.get("required");

        for (String requiredField : requiredList) {
            if (!propertiesMap.containsKey(requiredField)) {
                return new WorkflowConfigCheckResult(false,
                        String.format("required信息中的%s字段必须在properties中存在", requiredField),
                        String.format("%s.%s.%s", preLocation, key, "content.application/json.schema.required"));
            }
        }

        if(!"200".equals(key) && (!propertiesMap.containsKey("code") || !propertiesMap.containsKey("msg"))) {
            // 非200的返回值，必须包含code和msg字段
            return new WorkflowConfigCheckResult(false,
                    "非200的返回值，必须包含code和msg字段",
                    String.format("%s.%s.%s", preLocation, key, "content.application/json.schema"));
        }
        return new WorkflowConfigCheckResult(true, "校验通过", preLocation);
    }

    /**
     * 获取插件的type，pipeline 和 api
     * @param infoConfig
     * @return
     */
    @Override
    public String getPluginTypeByInfo(Object infoConfig) {
        Map<String, Object> infoConfigMap = (Map<String, Object>) infoConfig;
        if (!infoConfigMap.containsKey("type")) {
            return "pipeline";
        }
        return (String) infoConfigMap.get("type");
    }

    /**
     * 校验pipeline类型
     * @param data
     * @param paramNameList
     * @return
     */
    private WorkflowConfigCheckResult checkPipelineStages(Map<String, Object> data, List<String> paramNameList) {
        WorkflowConfigCheckResult result;

        List<Map<String, Object>> stageConfigList = (List<Map<String, Object>>) data.get("stages");
        Map<String, Object> preRequestStageConfig = stageConfigList.stream().filter(stageConfig -> "preRequest".equals(stageConfig.get("name"))).findFirst().orElse(null);
        Map<String, Object> llmStageConfig = stageConfigList.stream().filter(stageConfig -> "llm".equals(stageConfig.get("name"))).findFirst().orElse(null);
        Map<String, Object> postRequestStageConfig = stageConfigList.stream().filter(stageConfig -> "postRequest".equals(stageConfig.get("name"))).findFirst().orElse(null);
        Map<String, Object> summaryStageConfig = stageConfigList.stream().filter(stageConfig -> "summary".equals(stageConfig.get("name"))).findFirst().orElse(null);

        //preRequest阶段校验
        result = checkPreRequestStage(PluginTypeEnum.PIPELINE.getName(), preRequestStageConfig);
        if (!result.isSuccess()) {
            return result;
        }

        //解析preResponse字段得到一个参数名列表，下面的校验会用到
        List<String> preResponseKeyList = new ArrayList<>();
        if (preRequestStageConfig != null) {
            List<Map<String, Object>> apiListInfo = (List<Map<String, Object>>) preRequestStageConfig.get("api_list");
            Map<String, Object> preResponseInfo = (Map<String, Object>) apiListInfo.get(0).get("responses");
            if (preResponseInfo.containsKey("200")) {
                Map<String, Object> response200 = (Map<String, Object>) preResponseInfo.get("200");
                Map<String, Object> content = (Map<String, Object>) response200.get("content");
                Map<String, Object> appJson = (Map<String, Object>) content.get("application/json");
                Map<String, Object> schema = (Map<String, Object>) appJson.get("schema");
                Map<String, Object> properties = (Map<String, Object>) schema.get("properties");
                preResponseKeyList.addAll(properties.keySet());
            }
        }


        //llm阶段校验
        result = checkLLMStage(llmStageConfig, paramNameList, preResponseKeyList);
        if (!result.isSuccess()) {
            return result;
        }

        //postRequest阶段校验
        List<String> postResponseKeyList = new ArrayList<>();
        if(postRequestStageConfig != null) {
            result = checkPostRequestStage(postRequestStageConfig);
            if (!result.isSuccess()) {
                return result;
            }

            //解析postResponse字段得到一个参数名列表，下面的校验会用到
            Map<String, Object> postResponseInfo = (Map<String, Object>) postRequestStageConfig.get("responses");
            if (postResponseInfo.containsKey("200")) {
                Map<String, Object> response200 = (Map<String, Object>) postResponseInfo.get("200");
                Map<String, Object> content = (Map<String, Object>) response200.get("content");
                Map<String, Object> appJson = (Map<String, Object>) content.get("application/json");
                Map<String, Object> schema = (Map<String, Object>) appJson.get("schema");
                Map<String, Object> properties = (Map<String, Object>) schema.get("properties");
                postResponseKeyList.addAll(properties.keySet());
            }
        }

        //summary阶段校验
        if(summaryStageConfig!=null){
            result = checkSummaryStage(summaryStageConfig, paramNameList, preResponseKeyList, postResponseKeyList);
        }

        return result;
    }

    /**
     * 校验api类型插件的stages配置
     * api类型只有preRequest阶段
     * @param data
     * @return
     */
    private WorkflowConfigCheckResult checkApiStages(Map<String, Object> data) {
        WorkflowConfigCheckResult result;

        List<Map<String, Object>> stageConfigList = (List<Map<String, Object>>) data.get("stages");
        Map<String, Object> preRequestStageConfig = stageConfigList.stream().filter(stageConfig -> "preRequest".equals(stageConfig.get("name"))).findFirst().orElse(null);

        //preRequest阶段校验
        result = checkPreRequestStage(PluginTypeEnum.API.getName(), preRequestStageConfig);
        if (!result.isSuccess()) {
            return result;
        }

        return result;
    }

    /**
     * 组装流水线yaml
     *
     * <AUTHOR>
     * @since 2024.06.14
     * @param componentName componentName
     * @return java.lang.String
     */
    private String buildPipelineYml(String componentName){
        String component = JSONObject.parseObject(codeGPTDrmConfig.getPluginDefaultConfig()).getString("component");
        Map<String, Object> defaultYml = parseWorkFlowYaml(component);
        Map<String,String> jobInfo = new HashMap<>();
        Map<String,Object> jobs = new HashMap<>();
        ArrayList stage = (ArrayList) defaultYml.get("stages");
        jobInfo.put("stage", (String)stage.get(0));
        jobInfo.put("component",componentName);
        jobs.put(componentName,jobInfo);
        defaultYml.put("jobs",jobs);
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setPrettyFlow(true);
        Yaml yaml = new Yaml(options);
        return yaml.dump(defaultYml);
    }
}
