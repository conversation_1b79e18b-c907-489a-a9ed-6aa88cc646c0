package com.alipay.codegencore.service.middle.msgbroker;

import com.alipay.codegencore.service.answer.AnswerIndexService;
import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.codegencore.service.middle.tbase.TbaseCacheService;
import com.alipay.codegencore.utils.thread.ThreadPoolUtils;
import com.alipay.common.event.UniformEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.middle.msgbroker
 * @CreateTime : 2024-09-05
 */
@Service
public class IndexBuildTaskCheckListener implements CodegencoreEventHandler {

    private static final Logger logger = LoggerFactory.getLogger(IndexBuildTaskCheckListener.class);

    /**
     * topic|eventCode的Key 一般用于注册
     */
    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_F_SC|EC_codegencore_index_build_task_check";

    /**
     * 加锁
     */
    private static final String REPO_INDEX_CHECK_HANDLE = "REPO_INDEX_CHECK_HANDLE";

    @Resource
    private AnswerIndexService answerIndexService;

    @Autowired
    private TbaseCacheService tbaseCacheService;

    @Override
    public void handle(UniformEvent message) {

        ThreadPoolUtils.execute(ThreadPoolUtils.indexBuildPool, () -> {
            try {
                //加锁
                boolean lock = tbaseCacheService.getLock(REPO_INDEX_CHECK_HANDLE, 1200);
                if (lock) {
                    answerIndexService.repoTaskCheck();
                }
            } catch (Exception e) {
                logger.error("found exception", e);
            } finally {
                tbaseCacheService.releaseLock(REPO_INDEX_CHECK_HANDLE);
            }
        });

    }

    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }

}
