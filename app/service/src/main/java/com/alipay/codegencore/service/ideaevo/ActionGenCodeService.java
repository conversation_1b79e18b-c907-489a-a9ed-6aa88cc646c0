package com.alipay.codegencore.service.ideaevo;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.openai.CodeInfoFile;
import com.alipay.codegencore.model.openai.GenCodeFileRequest;
import com.alipay.codegencore.model.openai.GenPlanRequest;
import com.alipay.codegencore.model.openai.PlanFile;
import com.alipay.codegencore.utils.code.BloopSearchClient;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 19:34
 */
public interface ActionGenCodeService {

    /**
     * 生成计划
     * @param request
     * @return
     */
    List<PlanFile> genPlanFile(GenPlanRequest request, String modelEnv, String searchType, String planType, String modelName, String planPromptName);

    /**
     * 代码文件级别生成
     * @param genCodeFileRequest
     * @param modelEnv
     * @return
     */
    CodeInfoFile genCodeFile(GenCodeFileRequest genCodeFileRequest, String modelEnv, String modelName, String codePromptName);


    /**
     * 代码文件级别生成
     * @param genCodeFileRequest
     * @param modelEnv
     * @return
     */
    void genCodeFileStream(GenCodeFileRequest genCodeFileRequest, String modelEnv, String modelName);


    /**
     * 搜索相关代码
     * @param searchRequest
     * @return
     */
    List<BloopSearchClient.CodeResult> search(GenPlanRequest searchRequest);

    /**
     * 评测（新方案）
     * @param request
     * @param modelEnv
     * @return
     */
    JSONObject evaluationFile(GenPlanRequest request, String modelEnv, String searchType,
                              String planType, String codeType, String planModelName,
                              String codeModelName, int codeModelCount, String planPromptName);

}
