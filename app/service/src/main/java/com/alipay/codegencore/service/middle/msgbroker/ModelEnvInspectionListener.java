package com.alipay.codegencore.service.middle.msgbroker;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.common.event.UniformEvent;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.service.msgbroker
 * @CreateTime : 2023-11-01
 */
@Slf4j
@Service("modelEnvInspectionListener")
public class ModelEnvInspectionListener implements CodegencoreEventHandler {

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private MayaService mayaService;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_F_SC|EC_codegencore_model_env_inspection";

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelEnvInspectionListener.class);

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Override
    public void handle(UniformEvent message) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to monitor model env");
        }

        appThreadPool.submit(
                () -> {
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("start to monitor model env by thread");
                    }
                    List<String> ignoreModels = null;
                    try {
                        ignoreModels = JSONObject.parseObject(codeGPTDrmConfig.getIgnoreEnvCheckModels(), List.class);
                    } catch (Exception ex) {
                        LOGGER.warn("json parse error, ignoreEnvCheckModels", ex);
                    }
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("start to monitor model env by thread, ignoreModels : {}", ignoreModels);
                    }
                    final List<String> ignoreModelsF = ignoreModels == null ? new ArrayList<>() : ignoreModels;
                    List<AlgoBackendDO> algoBackendDOList = algoBackendService.getAllAlgoBackend().stream()
                            .filter(AlgoBackendDO::getEnable)
                            .filter(m->StringUtils.isNotBlank(m.getImpl()))
                            .filter(m->!"AntGLMModelHandler".equalsIgnoreCase(m.getImpl()))
                            .filter(m->!"ChatGptModelHubHandler".equalsIgnoreCase(m.getImpl()))
                            .filter(m->!"ChatGptModelHandler".equalsIgnoreCase(m.getImpl()))
                            .filter(m->!ignoreModelsF.contains(m.getModel()))
                            .collect(Collectors.toList());
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("check models, size : {}", algoBackendDOList.size());
                    }
                    List<String> modelNames = algoBackendDOList.stream().map(AlgoBackendDO::getModel).collect(Collectors.toList());
                    List<String> failedModels = new ArrayList<>();
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("check models detail : {}", modelNames);
                    }

                    Map<String, JSONObject> uniqueKey2Detail = new HashMap<>();
                    for(AlgoBackendDO algoBackendDO : algoBackendDOList) {
                        if(algoBackendDO == null) {
                            LOGGER.warn("model is null");
                            continue;
                        }

                        if(StringUtils.isEmpty(algoBackendDO.getImplConfig())) {
                            LOGGER.warn("model's impl is empty");
                            failedModels.add(algoBackendDO.getModel());
                            continue;
                        }

                        JSONObject implDetail = null;
                        try {
                            implDetail = JSONObject.parseObject(algoBackendDO.getImplConfig());
                        } catch (Exception ex) {
                            LOGGER.warn("json parse error, {}", algoBackendDO.getModel(), ex);
                        }

                        if(null == implDetail
                                || StringUtils.isEmpty(implDetail.getString("sceneName"))
                                || StringUtils.isEmpty(implDetail.getString("chainName"))) {
                            LOGGER.warn("invalid model impl in db, {}", algoBackendDO.getModel());
                            failedModels.add(algoBackendDO.getModel());
                            continue;
                        }

                        String sceneName = implDetail.getString("sceneName");
                        String chainName = implDetail.getString("chainName");
                        String uniqueKey = getUniqueKey(sceneName, chainName);
                        if(uniqueKey2Detail.containsKey(uniqueKey)) {
                            if(LOGGER.isInfoEnabled()) {
                                LOGGER.info("{} has checked by this monitor check", uniqueKey);
                            }
                            continue;
                        }

                        JSONObject detail = mayaService.getModelAvailableServers(sceneName, chainName);
                        if(LOGGER.isInfoEnabled()) {
                            LOGGER.info("get maya deploy info : {}, {}", uniqueKey, JSONObject.toJSONString(detail));
                        }
                        uniqueKey2Detail.put(uniqueKey, detail);
                    }
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("finished env check, failed models, size: {}, detail : {}", failedModels.size(), JSONObject.toJSONString(failedModels));
                    }

                    refreshCacheDetails(algoBackendDOList, uniqueKey2Detail);
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("end to monitor model env by thread");
                    }
                }
        );

        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("end to monitor model env");
        }
    }

    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }

    private void refreshCacheDetails(List<AlgoBackendDO> algoBackendDOList, Map<String, JSONObject> model2Detail) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to refresh cache, size is {}", algoBackendDOList.size());
        }
        for(AlgoBackendDO algoBackendDO : algoBackendDOList) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("start to refreshCacheDetails : {}", algoBackendDO.getModel());
            }
            String uniqueKey = model2Unique(algoBackendDO);
            String cacheKey = AppConstants.MODEL_ENV_CHECK_PREFIX + algoBackendDO.getModel();
            if(StringUtils.isEmpty(uniqueKey)) {
                if(noneSerializationCacheManager.exists(cacheKey)) {
                    LOGGER.warn("delete {} tbase, because of empty sceneName or chainName", algoBackendDO.getModel());
                    noneSerializationCacheManager.del(cacheKey);
                }
                continue;
            }
            JSONObject currentDetail = model2Detail.get(uniqueKey);
            if(null == currentDetail) {
                if(noneSerializationCacheManager.exists(cacheKey)) {
                    LOGGER.warn("delete {} tbase, because of invalid sceneName or chainName", algoBackendDO.getModel());
                    noneSerializationCacheManager.del(cacheKey);
                }
                continue;
            }

            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("start to combine detail : {}, {}", cacheKey, uniqueKey);
            }
            JSONObject combineDetail = null;
            try {
                combineDetail = mayaService.combineEnvServersDetail(noneSerializationCacheManager.get(cacheKey), currentDetail);
            } catch(Exception exception) {
                LOGGER.warn("combine detail failed, {}", algoBackendDO.getModel(), exception);
            }

            if(null == combineDetail ||
                    combineDetail.isEmpty()) {
                LOGGER.warn("combined result is empty : {}, {}", cacheKey, uniqueKey);
                continue;
            }
            noneSerializationCacheManager.setex(cacheKey, AppConstants.HEALTH_CHECK_CACHE_EXPIRED, combineDetail);
        }
    }

    private String model2Unique(AlgoBackendDO algoBackendDO) {
        JSONObject implDetail = JSONObject.parseObject(algoBackendDO.getImplConfig());
        if(null == implDetail
                || StringUtils.isEmpty(implDetail.getString("sceneName"))
                || StringUtils.isEmpty(implDetail.getString("chainName"))) {
            LOGGER.warn("invalid model impl in DB, {}", algoBackendDO.getModel());
            return null;
        }

        return getUniqueKey(implDetail.getString("sceneName"), implDetail.getString("chainName"));
    }

    private String getUniqueKey(String sceneName, String chainName) {
        return sceneName + "___" + chainName;
    }
}
