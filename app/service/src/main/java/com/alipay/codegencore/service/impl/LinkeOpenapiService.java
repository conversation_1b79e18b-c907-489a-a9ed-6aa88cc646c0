package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.response.linke.IterationUnitVo;
import com.alipay.codegencore.model.response.linke.IterationVo;
import com.alipay.codegencore.model.response.linke.ReleaseVo;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.devapi.sdk.SignUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.03.06
 */
@Service
public class LinkeOpenapiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LinkeOpenapiService.class);
    // API_GATEWAY域名，预发环境: https://devapi-prepub.alipay.com
    private final static String API_GATEWAY_HOST = "https://devapi.alipay.com";
    private final static String LINKE_EXTERNAL_API = "/linke/api/release/";
    @Resource
    private ConfigService configService;

    /**
     * 根据发布单id判断是否是当前应用部署完成
     *
     * <AUTHOR>
     * @since 2024.03.06
     * @param externalId externalId
     * @return java.lang.Boolean
     */
    public Boolean checkAppNameByExternalId(String externalId) {
        JSONObject langChainResponse = null;
        try {
            String accessKey = configService.getConfigByKey("linkeAccessKey",false);
            String accessSecret = configService.getConfigByKey("linkeAccessSecret",false);
            long timestamp = System.currentTimeMillis();
            String response = HttpClient.get(API_GATEWAY_HOST + LINKE_EXTERNAL_API + externalId)
                    .header("AccessKey", accessKey)
                    .header("Signature", SignUtil.sign(accessKey, accessSecret, timestamp))
                    .header("Timestamp", String.valueOf(timestamp)).syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
            LOGGER.info("调用linke接口返回response:{}",response);

        } catch (Exception e) {
            LOGGER.info("调用linke接口异常", e.getMessage());
            throw new BizException(ResponseEnum.HTTP_ERROR, e.getMessage());
        }
        if (langChainResponse != null && langChainResponse.getBoolean("success")) {
            ReleaseVo releaseVo = JSON.parseObject(langChainResponse.getString("result"), ReleaseVo.class);
            if (releaseVo == null) {
                LOGGER.info("发布单为空");
                return false;
            }
            if (!releaseVo.getStatus().equalsIgnoreCase("COMPLETED")) {
                // 如果发布单未完成，返回false
                LOGGER.info("发布单状态为：{}",releaseVo.getStatus());
                return false;
            }
            if(CollectionUtils.isEmpty(releaseVo.getIterations())||CollectionUtils.isEmpty(releaseVo.getIterations().get(0).getUnits())){
                LOGGER.info("发布单迭代或迭代单元为空,无法确定应用信息");
                return false;
            }
            for (IterationVo iterationVo : releaseVo.getIterations()) {
                for (IterationUnitVo unit : iterationVo.getUnits()) {
                    // 如果发布单已完成且为当前应用，则返回true
                    if (unit.getAppName().equalsIgnoreCase(AppConstants.INTRANET_APPLICATION_NAME)) {
                        LOGGER.info("发布单已完成且为当前应用{},可以触发自动巡检",AppConstants.INTRANET_APPLICATION_NAME);
                        return true;
                    }
                }
            }

        }
        LOGGER.info("当前发布单非当前应用{}",AppConstants.INTRANET_APPLICATION_NAME);
        return false;
    }

}
