/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.alipay.arks.client.Item;
import com.alipay.arks.client.enums.ModelServerType;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.common.TotoroService;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.maya.MayaClient;
import com.alipay.maya.config.MayaClientConfig;
import com.alipay.maya.model.MayaRequest;
import com.alipay.maya.model.MayaResponse;
import org.apache.http.util.TextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version TotoroGptImpl.java, v 0.1 2023年05月31日 上午10:25 changxing.cx
 */
@Service
public class TotoroGptImpl implements TotoroService {
    private static final Logger logger = LoggerFactory.getLogger(TotoroGptImpl.class);

    private static final String     MAYA_GET_RATE_LIMITER_FAIL_TRACE_MSG = "server is too busy";
    private              MayaClient mayaClient;

    private MayaRequest buildMayaRequest(String data, String sceneName, String chainName, String feature) {
        MayaRequest request = buildBaseRequest(sceneName, chainName);
        // item,一个Item代表一次推理，n个Item就是一个长度为n的batch,返回结果也在response.items中
        JSONObject obj = new JSONObject();
        obj.put(feature, data);
        List<Item> items = buildItems(obj);
        request.setItems(items);

        return request;
    }

    private MayaRequest buildMayaRequest(JSONObject feature, String sceneName, String chainName) {
        MayaRequest request = buildBaseRequest(sceneName, chainName);
        // item,一个Item代表一次推理，n个Item就是一个长度为n的batch,返回结果也在response.items中
        request.setItems(buildItems(feature));

        return request;
    }

    private List<Item> buildItems(JSONObject feature) {
        List<Item> items = new ArrayList<>();
        Item item = new Item();
        items.add(item);
        item.setItemId(ShortUid.getUid());
        Map<String,String> item1Features = new HashMap<>(4);

        Set<String> keys = feature.keySet();
        for (String k : keys) {
            item1Features.put(k, feature.getString(k));
        }
        item.setFeatures(item1Features);
        return items;
    }

    private MayaRequest buildBaseRequest(String sceneName, String chainName) {
        // 构造请求
        MayaRequest request = new MayaRequest();
        // 业务场景名,必填,需要根据服务名寻址
        request.setSceneName(sceneName);
        // 必填,服务版本会作为url路径http
        request.setChainName(chainName);
        // 服务端类型,默认ModelServerType.MAYA
        request.setServerType(ModelServerType.MAYA);
        // 整体超时时间,不设置默认从MayaClientConfig里获取是600ms
        request.setRequestTimeOut(90000);
        // 可选,建立连接超时时间,不设置默认从MayaClientConfig里获取是100ms
        request.setConnectTimeOut(20000);
        // 可选,等待服务端返回超时时间,不设置默认从MayaClientConfig里获取是500ms
        request.setReadTimeOut(60000);
        request.setRetryTimes(2);

        return request;
    }

    private MayaClient getMayaClient(String appName) {
        if (mayaClient != null) {
            return mayaClient;
        }

        MayaClientConfig config = new MayaClientConfig();
        // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
        config.setAppName(appName);
        mayaClient = MayaClient.getInstance(config);
        return mayaClient;
    }

    private String requestMaya(MayaRequest request, String appName, String outputTag) throws BizException {
        MayaClient mayaClient = getMayaClient(appName);

        MayaResponse response;
        try {
            response = mayaClient.call(request);
        } catch (Exception e) {
            throw new BizException(ResponseEnum.AI_CALL_ERROR, e);
        }

        if (response.getErrorCode() == 0) {
            if (logger.isInfoEnabled()) {
                logger.info("process success : {}", JSON.toJSONString(response.getResults()));
            }
            // 兼容原arks服务返回字段,只支持字符串类型返回
            return response.getItems().get(0).getAttributes().get(outputTag);
        }
        else {
            logger.error("process failed: error name {}, error message {}", response.getErrorName(),
                    response.getErrorMsg());
            throw new BizException(MayaServiceImpl.mayaException2Enum(response.getErrorCode()), String.format("AI模型调用报错,错误信息: %s", response.getErrorMsg()));
        }
    }

    /**
     * 将自然语言转化为任务语言
     *
     * @param data 自然语言字符串
     * @return 任务语言字符串
     */
    @Override
    public String nl2TotoroDSL(String data) {
        // 构建Maya请求对象
        MayaRequest request = buildMayaRequest(data, "totorogpt_ft", "v2.1", "query");
        // 发送Maya请求并返回结果
        return requestMaya(request, "totoro-gpt", "answer");
    }

    /**
     * 将自然语言转化为任务语言
     *
     * @param data 自然语言字符串
     * @return 任务语言字符串
     */
    @Override
    public String nl2task(JSONObject data) {
        String tag = data.getString("llm_tag");
        String intentNl = data.getString("intention");

        MayaRequest request = buildMayaRequest(intentNl, "totorogpt_ft", "v2.1", "query");
        String dsl = requestMaya(request, "totoro-gpt", "answer");
        String[] dslCount = dsl.split("[;。；\n]");

        if (!TextUtils.isBlank(tag) && tag.equals("aigc_v1")) {
            JSONArray array = new JSONArray();
            for (String dslLine : dslCount) {
                JSONObject obj = new JSONObject();
                obj.put("dsl", dslLine);
                array.add(obj);
            }
            return array.toJSONString();
        }

        String[] nlCount = intentNl.split("[;。；\n]");
        JSONArray array = new JSONArray();

        if (nlCount.length == dslCount.length) {
            for (int i = 0; i < nlCount.length; i++) {
                String line = nlCount[i];
                boolean containsPattern = Pattern.matches(
                        ".*(点击|判断|打开|安装|卸载|强杀|登录|输入|向上|向右|monkey|scheme|等待|home|截图|返回).*", line);
                if (containsPattern && line.contains("点击") && line.length() > 8
                        && (!line.contains("\'") || !line.contains("\""))) {
                    containsPattern = false;
                }

                if (!containsPattern) {
                    data.put("intention", line);
                    request = buildMayaRequest(data, "ui_llm_inference", "v1");

                    String result = requestMaya(request, "ui_llm_inference", "outputs");
                    JSONArray arrayItem = JSON.parseArray(result);
                    array.addAll(arrayItem);
                    continue;
                }

                JSONObject obj = new JSONObject();
                obj.put("dsl", dslCount[i]);
                array.add(obj);
            }
            return array.toJSONString();
        }

        if (nlCount.length > 1) {
            for (String line : nlCount) {
                boolean containsPattern = Pattern.matches(
                        ".*(点击|判断|打开|安装|卸载|强杀|登录|输入|向上|向右|monkey|scheme|等待|home|截图|返回).*", line);
                if (containsPattern && line.contains("点击") && line.length() > 8
                        && (!line.contains("\'") || !line.contains("\""))) {
                    containsPattern = false;
                }

                if (!containsPattern) {
                    data.put("intention", line);
                    request = buildMayaRequest(data, "ui_llm_inference", "v1");

                    String result = requestMaya(request, "ui_llm_inference", "outputs");
                    JSONArray arrayItem = JSON.parseArray(result);
                    array.addAll(arrayItem);
                    continue;
                }

                request = buildMayaRequest(line, "totorogpt_ft", "v2.1", "query");
                dsl = requestMaya(request, "totoro-gpt", "answer");
                dslCount = dsl.split("[;。；\n]");

                for (String dslLine : dslCount) {
                    JSONObject obj = new JSONObject();
                    obj.put("dsl", dslLine);
                    array.add(obj);
                }
            }

            return array.toJSONString();
        }

        request = buildMayaRequest(data, "ui_llm_inference", "v1");
        // 发送Maya请求并返回结果
        return requestMaya(request, "ui_llm_inference", "outputs");
    }

}
