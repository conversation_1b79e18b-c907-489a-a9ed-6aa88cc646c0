/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.codegpt.user;

import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.request.TokenRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.model.response.TokenResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @version TokenService.java, v 0.1 2023年08月10日 上午11:17 yhw01352860
 */
public interface TokenService {

    /**
     * token列表
     * @param pageNo
     * @param pageSize
     * @param filterField
     * @return
     */
    PageResponse<List<TokenResponse>> tokenList(int pageNo, int pageSize, String filterField);
    /**
     * 查看token信息
     * @param user
     * @param queryToken
     * @return
     */
    BaseResponse<TokenResponse> getToken(String user, Long id, boolean queryToken);

    /**
     * 系统来获取token
     * @param user
     * @return
     */
    TokenDO getTokenSystem(String user);

    /**
     * 新增token
     * @param tokenDO
     * @return
     */
    BaseResponse insert(TokenRequestBean tokenDO);

    /**
     * 编辑token
     * @param tokenDO
     * @return
     */
    BaseResponse update(TokenRequestBean tokenDO);
}
