package com.alipay.codegencore.service.impl.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RateLimitTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.limiter.qpx.QpxLimiterFactory;
import com.alipay.codegencore.service.gptcache.bean.GptCacheResponse;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zcache.serialize.BytesObject;
import com.alipay.zdatafront.common.service.facade.commonquery.CommonQueryService;
import com.alipay.zdatafront.common.service.facade.commonquery.param.CommonQueryParam;
import com.alipay.zdatafront.common.service.facade.commonquery.result.CommonQueryResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

/**
 * 枢纽 GPT
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.impl.model
 * @CreateTime : 2023-08-30
 */
@Service("HubChatGptModelService")
public class HubChatGgtModelServiceImpl implements LanguageModelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HubChatGgtModelServiceImpl.class);

    @Resource
    private AlgoModelUtilService algoModelUtilService;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource
    private CheckService checkService;

    @Resource
    protected CommonQueryService commonQueryService;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource
    private StreamDataQueueUtilService streamDataQueueUtilService;


    @Resource
    private ConfigService configService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    private static final String SERVICE_NAME   = "asyn_chatgpt_prompts_completions_query_dataview";
    private static final String VISIT_DOMAIN   = "BU_cto";
    private static final String VISIT_BIZ      = "BU_cto_code";
    private static final String VISIT_BIZ_LINE = "BU_cto_code_line";

    private static final String VISIT_BIZ_GPT4      = "BU_code_gpt4";
    private static final String VISIT_DOMAIN_GPT4   = "BU_code";
    private static final String VISIT_BIZ_LINE_GPT4 = "BU_code_gpt4_zongsheng";
    private static final int    CACHE_INTERVAL = 0;
    private static final String N              = "1";
    private static final String MQ             = "MQ";
    private static final String PULL             = "PULL";

    private static final String PULL_SERVICE_NAME   = "chatgpt_response_query_dataview";
    private static final int    PULL_CACHE_INTERVAL = -1;

    private static final String EC_DATAHUB_CTO_CODE = "EC_datahub_cto_code";
    private static final String TP_F_EXTDATASYNC    = "TP_F_EXTDATASYNC";

    /**
     * 非流式对话
     * @param params 请求参数
     * @return
     */
    @Override
    public ChatMessage chat(GptAlgModelServiceRequest params) {
        // 获取参数信息
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();

        // 限流校验
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        // 设置后端请求model
        if (chatCompletionRequest.getModel() == null) {
            chatCompletionRequest.setModel(AlgoBackendUtil.exactRequestModelModel(algoBackendDO));
        }
        // 发送请求
        sendRequest(params, false);
        JSONObject mqMessage = getMQMessage(params);
        String content = mqMessage.getString(AppConstants.CONTENT);
        if (content == null) {
            content = AppConstants.DEFAULT_CODEGPT_FUNCTION_CALL_START_TOKEN+ JSONObject.toJSONString(mqMessage.get(AppConstants.FUNCTION_CALL_STREAM_FINISH_REASON));
        }
        return new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), content);
    }

    @Override
    public boolean isServiceOk() {
        return true;
    }

    /**
     * 流式对话
     * @param params 请求参数
     */
    @Override
    public void streamChatForServlet(GptAlgModelServiceRequest params) {
        // 获取参数信息
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();

        // 限流校验
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        // 设置后端请求model
        if (chatCompletionRequest.getModel() == null) {
            chatCompletionRequest.setModel(AlgoBackendUtil.exactRequestModelModel(algoBackendDO));
        }
        String requestId = params.getRequestId();
        Consumer<StreamResponseModel> resultHandler = params.getResultHandler();
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = params.getChatStreamPartResponseHandler();
        // 对用户输入的问题进行审核,输入内容一批全部审
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        // 获取额外配置参数
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        if (chatRequestExtData != null) {
            chatRequestExtData.setAlgoBackendDO(algoBackendDO);
        }
        // 检查提问是否符合安全审查规范
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(requestId, messages, chatRequestExtData,true);
        // 如果提问不合规, 则停止响应
        if (!requestCheckResultModel.isAllCheckRet()) {
            algoModelUtilService.processWhenQuestionFailCheck(requestCheckResultModel, chatStreamPartResponseConsumer, resultHandler);
            return;
        }
        // 开启gptCache并从gptCache写入TBase数据成功 不发送请求
        GptCacheResponse gptCacheResponse = null;
        if (algoBackendDO.getEnableGptCache()) {
            gptCacheResponse = algoModelUtilService.getGPTCache(algoBackendDO, params);
        }
        if (gptCacheResponse != null && StringUtils.isNotBlank(gptCacheResponse.getAnswer())) {
            if (codeGPTDrmConfig.isModelEnableQueue()) {
                algoModelUtilService.pushToQueue(gptCacheResponse, params);
            }
            else {
                algoModelUtilService.pushToTBase(gptCacheResponse, params);
            }
        }
        else {
            sendRequest(AlgoModelUtilService.copyParamWithoutServletResponse(params), true);
            // 从mq获取枢纽的返回结果并存入TBase
            if (codeGPTDrmConfig.isModelEnableQueue()) {
                appThreadPool.execute(() -> putQueue(AlgoModelUtilService.copyParamWithoutServletResponse(params)));
            }
            else {
                appThreadPool.execute(() -> putTBase(AlgoModelUtilService.copyParamWithoutServletResponse(params)));
            }
        }
        algoModelUtilService.getChatDataFromTBase(params, chatCompletionRequest.getMessages(), null, algoBackendDO.getEnableGptCache(),
                new ChatStreamBuffer(),codeGPTDrmConfig.isModelEnableQueue());

    }

    /**
     * 向chatGpt枢纽发送请求
     *
     * @param gptAlgModelServiceRequest
     * @param isStream
     */
    private void sendRequest(GptAlgModelServiceRequest gptAlgModelServiceRequest, Boolean isStream) {
        ChatCompletionRequest chatCompletionRequest = gptAlgModelServiceRequest.getChatCompletionRequest();

        String requestId = gptAlgModelServiceRequest.getRequestId();

        CommonQueryParam commonQueryParam = new CommonQueryParam();
        commonQueryParam.setServiceName(SERVICE_NAME);
        commonQueryParam.setVisitBiz(chatCompletionRequest.getModel().startsWith("gpt-3") ? VISIT_BIZ : VISIT_BIZ_GPT4);
        commonQueryParam.setVisitDomain(chatCompletionRequest.getModel().startsWith("gpt-3") ? VISIT_DOMAIN : VISIT_DOMAIN_GPT4);
        commonQueryParam.setVisitBizLine(chatCompletionRequest.getModel().startsWith("gpt-3") ? VISIT_BIZ_LINE : VISIT_BIZ_LINE_GPT4);
        commonQueryParam.setCacheInterval(CACHE_INTERVAL);
        JSONObject implConfig = JSONObject.parseObject(gptAlgModelServiceRequest.getAlgoBackendDO().getImplConfig());
        boolean pull = implConfig.getBoolean("pull") != null && implConfig.getBoolean("pull");
        commonQueryParam.setQueryConditions(buildConditions(chatCompletionRequest, requestId, pull));
        LOGGER.info(String.format("开始发请求给枢纽chatGpt,requestId:%s,commonQueryParam:%s", requestId, JSON.toJSONString(commonQueryParam)));
        CommonQueryResult commonQueryResult = commonQueryService.queryData(commonQueryParam);
        LOGGER.info(String.format("枢纽chatGpt Result:%s", JSON.toJSONString(commonQueryResult)));

        if (!commonQueryResult.isSuccess()) {
            LOGGER.error(String.format("枢纽chatGpt调用失败:%s", commonQueryResult.getErrorMessage()));
            if (isStream) {
                if (codeGPTDrmConfig.isModelEnableQueue()) {
                    algoModelUtilService.pushErrorMsgToTBase(Collections.singletonList(gptAlgModelServiceRequest),
                            commonQueryResult.getErrorMessage(), ResponseEnum.AI_CALL_ERROR);
                }
                else {
                    algoModelUtilService.handleEveryStreamError(ResponseEnum.AI_CALL_ERROR.getErrorMsg(), noneSerializationCacheManager,
                            requestId, ResponseEnum.AI_CALL_ERROR);
                }

            }
            else {
                throw new BizException(ResponseEnum.AI_CALL_ERROR);
            }

        }

    }

    /**
     * 获取枢纽chatgptMq返回的数据存入TBase
     *
     * @param gptAlgModelServiceRequest
     */
    private JSONObject getMQMessage(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        String requestId = gptAlgModelServiceRequest.getRequestId();
        Serializable serializable;
        long waitStartTime = System.currentTimeMillis();
        long mqTimeOut = Long.parseLong(String.valueOf(AlgoBackendUtil.getImplConfigByKey(gptAlgModelServiceRequest.getAlgoBackendDO(), "mqTimeOut")));
        while (true) {
            serializable = noneSerializationCacheManager.get(requestId);
            if (serializable != null) {
                break;
            }
            if (System.currentTimeMillis() - waitStartTime > mqTimeOut) {
                throw new BizException(ResponseEnum.MQ_TIME_OUT);
            }
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                LOGGER.error("An InterruptedException occurred while sleeping: {}, Thread: {}",
                e.getMessage(), Thread.currentThread().getName(), e);
            }
        }
        String streamStr = StringUtils.toEncodedString(((BytesObject) serializable).getBytes(), StandardCharsets.UTF_8);
        JSONObject message = JSONObject.parseObject(streamStr);
        noneSerializationCacheManager.del(requestId);
        return message;
    }

    /**
     * 把消息存到Queue
     *
     * @param gptAlgModelServiceRequest
     */
    private void putQueue(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        JSONObject implConfig = JSONObject.parseObject(gptAlgModelServiceRequest.getAlgoBackendDO().getImplConfig());
        JSONObject message;
        if (implConfig.getBoolean("pull")!= null && implConfig.getBoolean("pull")) {
            message = pullMessage(gptAlgModelServiceRequest);
        }
        else {
            message = getMQMessage(gptAlgModelServiceRequest);
        }
        String key = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, gptAlgModelServiceRequest.getUniqueAnswerId());
        JSONObject jsonObject = new JSONObject();
        //写结束字符
        Object content = message.get(AppConstants.CONTENT);
        Object functionCall = message.get(AppConstants.FUNCTION_CALL_STREAM_FINISH_REASON);
        if (functionCall != null){
            jsonObject.put("functionCall", functionCall);
        }
        jsonObject.put(AppConstants.CONTENT, content);
        jsonObject.put("finishReason", "stop");
        BytesObject bytesObject = new BytesObject(jsonObject.toString().getBytes(StandardCharsets.UTF_8));
        streamDataQueueUtilService.rpush(key, bytesObject);
    }

    /**
     * 把消息存到TBase
     *
     * @param gptAlgModelServiceRequest
     */
    private void putTBase(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        JSONObject message = getMQMessage(gptAlgModelServiceRequest);
        String key = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, gptAlgModelServiceRequest.getUniqueAnswerId());
        JSONObject jsonObject = new JSONObject();
        //写结束字符
        Object content = message.get(AppConstants.CONTENT);
        Object functionCall = message.get(AppConstants.FUNCTION_CALL_STREAM_FINISH_REASON);
        if (functionCall != null) {
            jsonObject.put("functionCall", functionCall);
        }
        jsonObject.put(AppConstants.CONTENT, content);
        jsonObject.put("finishReason", "stop");
        noneSerializationCacheManager.rpush(key, new BytesObject(jsonObject.toString().getBytes(StandardCharsets.UTF_8)));
        noneSerializationCacheManager.expire(key, 12 * 60 * 60);
    }

    /**
     * 拉取枢纽chatGpt返回的数据
     *
     * @param gptAlgModelServiceRequest
     * @return
     */
    private JSONObject pullMessage(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        String requestId = gptAlgModelServiceRequest.getRequestId();
        noneSerializationCacheManager.del(requestId);
        ChatCompletionRequest chatCompletionRequest = gptAlgModelServiceRequest.getChatCompletionRequest();
        CommonQueryParam commonQueryParam = new CommonQueryParam();
        commonQueryParam.setServiceName(PULL_SERVICE_NAME);
        commonQueryParam.setVisitBiz(chatCompletionRequest.getModel().startsWith("gpt-3") ? VISIT_BIZ : VISIT_BIZ_GPT4);
        commonQueryParam.setVisitDomain(chatCompletionRequest.getModel().startsWith("gpt-3") ? VISIT_DOMAIN : VISIT_DOMAIN_GPT4);
        commonQueryParam.setVisitBizLine(chatCompletionRequest.getModel().startsWith("gpt-3") ? VISIT_BIZ_LINE : VISIT_BIZ_LINE_GPT4);
        commonQueryParam.setCacheInterval(PULL_CACHE_INTERVAL);
        HashMap<String,String> queryConditions = new HashMap<>();
        queryConditions.put("messageKey", requestId);
        commonQueryParam.setQueryConditions(queryConditions);
        LOGGER.info(String.format("PULL枢纽chatGpt,requestId:%s,commonQueryParam:%s", requestId, JSON.toJSONString(commonQueryParam)));
        long waitStartTime = System.currentTimeMillis();
        long mqTimeOut = Long.parseLong(
                String.valueOf(AlgoBackendUtil.getImplConfigByKey(gptAlgModelServiceRequest.getAlgoBackendDO(), "mqTimeOut")));
        JSONObject message = null;
        while (true) {
            CommonQueryResult commonQueryResult = commonQueryService.queryData(commonQueryParam);
            if (commonQueryResult.isSuccess()) {
                JSONObject responseBody = JSONObject.parseObject(String.valueOf(commonQueryResult.getValues().get("response")));
                if (responseBody == null) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        LOGGER.error("An InterruptedException occurred while sleeping: {}, Thread: {}",
                                e.getMessage(), Thread.currentThread().getName(), e);
                    }
                    continue;
                }
                if (responseBody.get("errorMsg") != null) {
                    LOGGER.error(String.format("枢纽chatGpt调用失败:%s", responseBody.getString("errorMsg")));
                    algoModelUtilService.pushErrorMsgToTBase(Collections.singletonList(gptAlgModelServiceRequest),
                            responseBody.getString("errorMsg"), ResponseEnum.AI_CALL_ERROR);
                }
                LOGGER.info(String.format("PULL枢纽chatGpt response,requestId:%s,responseBody:%s", requestId,
                        JSONObject.toJSONString(responseBody)));
                JSONArray choices = JSONArray.parseArray(JSON.toJSONString(responseBody.get("choices")));
                JSONObject choicesIndex = JSONObject.parseObject(JSON.toJSONString(choices.get(0)));
                message = JSONObject.parseObject(JSON.toJSONString(choicesIndex.get("message")));
            }
            if (message != null) {
                break;
            }
            if (System.currentTimeMillis() - waitStartTime > mqTimeOut) {
                algoModelUtilService.pushErrorMsgToTBase(Collections.singletonList(gptAlgModelServiceRequest), "等待mq返回超时",
                        ResponseEnum.MQ_TIME_OUT);
            }
        }
        return message;
    }

    /**
     * 组装枢纽chatGpt需要的参数
     *
     * @param chatCompletionRequest
     * @param requestId
     * @return
     */
    private Map<String,String> buildConditions(ChatCompletionRequest chatCompletionRequest, String requestId, boolean pull) {
        String apiKey = configService.getConfigByKey(AppConstants.API_KEY, false);
        Map<String,String> map = new HashMap<>();
        map.put("model", chatCompletionRequest.getModel());
        map.put("messages", JSONArray.toJSONString(chatCompletionRequest.getMessages()));
        map.put("messageKey", requestId);
        map.put("outputType", pull ? PULL : MQ);
        if (!pull) {
            map.put("mqTag", EC_DATAHUB_CTO_CODE);
            map.put("mqTopic", TP_F_EXTDATASYNC);
        }
        map.put("n", N);
        map.put("api_key", apiKey);
        if (chatCompletionRequest.getFunctionCall() != null) {
            map.put("function_call", JSONObject.toJSONString(chatCompletionRequest.getFunctionCall()));
        }
        if (chatCompletionRequest.getFunctions() != null) {
            map.put("functions", JSONObject.toJSONString(chatCompletionRequest.getFunctions()));
        }
        return map;
    }
}
