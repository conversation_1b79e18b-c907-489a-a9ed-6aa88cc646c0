package com.alipay.codegencore.service.impl.answer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.copilot.CodeReference;
import com.alipay.codegencore.model.domain.AnswerIndexBuildTaskDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.answer.IndexScopeType;
import com.alipay.codegencore.model.enums.answer.TaskState;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.RepoInfoModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.service.answer.AnswerIndexService;
import com.alipay.codegencore.service.answer.RepoChatService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.impl.model.AlgoModelUtilService;
import com.alipay.codegencore.service.impl.model.StreamDataQueueUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.StreamDataListener;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Flow;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 仓库问答服务
 */
@Service
@Slf4j
public class RepoChatServiceImpl implements RepoChatService {

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    @Resource
    private AnswerIndexService answerIndexService;

    @Resource
    private AlgoModelUtilService algoModelUtilService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private CheckService checkService;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource
    private StreamDataQueueUtilService streamDataQueueUtilService;

    @Resource
    private ConfigService configService;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Override
    public void streamChat(GptAlgModelServiceRequest params,
                           JSONObject repoChatConfig,
                           List<CodeReference> codeReferences,
                           HttpServletResponse httpServletResponse) {
        Consumer<StreamResponseModel> resultHandler = params.getResultHandler();
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = params.getChatStreamPartResponseHandler();

        String requestId = params.getRequestId();
        String uniqueAnswerId = params.getUniqueAnswerId();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        // 对用户输入的问题进行审核,输入内容一批全部审
        List<ChatMessage> messages = chatCompletionRequest.getMessages();

        CHAT_LOGGER.info("codegpt completion requestId:{},user:{} request:{}", requestId, params.getUserName(), JSON.toJSONString(chatCompletionRequest));

        // 检查提问是否符合安全审查规范
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(requestId, messages, null, false);
        if (!requestCheckResultModel.isAllCheckRet()) {
            algoModelUtilService.processWhenQuestionFailCheck(requestCheckResultModel, chatStreamPartResponseConsumer, resultHandler);
        }

        RepoInfoModel repoInfo = params.getRepoInfo();

        //TODO: 没有仓库信息就无法问答，可能要考虑默认模型托底问答的策略
        ResponseEnum checkRes;
        if(repoInfo == null){
            checkRes = ResponseEnum.ANSWER_REPO_ARG_ERR;
        }else{
            checkRes = preCheck(StringUtils.substringAfterLast(params.getUserName(), "_"),
                    repoInfo.getRepoGroup(), repoInfo.getRepoName(), repoInfo.getBranch(), codeGPTDrmConfig.isAntcodeCopilotAutoIndexRepo(),repoChatConfig);
        }

        if (!ResponseEnum.SUCCESS.equals(checkRes)) {
            log.info("begin chat completion:{}", checkRes);
            String errorMsg = checkRes.getErrorMsg();
            if(ResponseEnum.ANSWER_REPO_NOT_INDEX.equals(checkRes)){
                errorMsg = codeGPTDrmConfig.getAntcodeCopilotRepoNotIndexResponse();
            }else if (ResponseEnum.ANSWER_REPO_INDEX_BUILDING.equals(checkRes)){
                errorMsg = codeGPTDrmConfig.getAntcodeCopilotRepoIndexingResponse();
            }

            if(codeGPTDrmConfig.isModelEnableQueue()){
                algoModelUtilService.memoryQueueStreamError(errorMsg, uniqueAnswerId, checkRes);
            }else {
                algoModelUtilService.handleEveryStreamError(errorMsg, noneSerializationCacheManager, uniqueAnswerId, checkRes);
            }
        } else {
            String streamInputId = String.format("%s%s", AppConstants.STREAM_INPUT_PREFIX, params.getUniqueAnswerId());
            log.info("begin repo answer:{};{}", repoInfo.getRepoAddr(), repoInfo.getBranch());
            appThreadPool.execute(() -> repoChat(messages, repoChatConfig, codeReferences, params.getUserName(), requestId, uniqueAnswerId, streamInputId, repoInfo, httpServletResponse));
        }
        algoModelUtilService.getChatDataFromTBase(params, messages, requestCheckResultModel, false, new ChatStreamBuffer(),codeGPTDrmConfig.isModelEnableQueue());
    }

    @Override
    public ResponseEnum preCheck(String workNo, String repoGroup, String repoName, String branch, boolean toBuild,JSONObject repoChatConfig) {
        if (StringUtils.isBlank(repoGroup) || StringUtils.isBlank(repoName) || StringUtils.isBlank(branch)) {
            return ResponseEnum.ANSWER_REPO_ARG_ERR;
        }

        //增加用户对于仓库是否有权限的校验
        if (codeGPTDrmConfig.isEnableMemberAccessCheck()) {
            if (!AntCodeClient.memberAllowAccess(workNo, repoGroup, repoName)) {
                log.warn("user:{} not allow repo:{}-{} answer", workNo, repoGroup, repoName);
                return ResponseEnum.ANSWER_REPO_NO_PERMISSION;
            }
        }
        // pr问答跳过校验
        if(repoChatConfig != null){
            String preUrl = repoChatConfig.getString("pr_url");
            if(StringUtils.isNotBlank(preUrl)){
                return ResponseEnum.SUCCESS;
            }
        }

        //先检查数据库，比较快
        AnswerIndexBuildTaskDO taskDO = answerIndexService.getTask(repoGroup, repoName, branch);
        if (taskDO == null) {

            //校验仓库是否存在
            try {
                AntCodeClient.CommitInfo commitInfo = answerIndexService.repoCheck(repoGroup, repoName, branch);

                //索引构建
                if (toBuild) {
                    log.info("begin repo index build:{};{}", repoGroup, repoName);
                    boolean indexScopeTypeValid = EnumUtils.isValidEnum(IndexScopeType.class, codeGPTDrmConfig.getDefaultIndexScopeType());
                    if (!indexScopeTypeValid){
                        log.error("defaultIndexScopeType is not valid:{}", codeGPTDrmConfig.getDefaultIndexScopeType());
                        throw new BizException(ResponseEnum.ANSWER_BUILD_TYPE_NOT_SUPPORT);
                    }
                    answerIndexService.indexBuild(commitInfo, AntCodeClient.buildRepoURL(repoGroup, repoName),codeGPTDrmConfig.getTaskPriority());
                    return ResponseEnum.ANSWER_REPO_INDEX_BUILDING;
                }
                return ResponseEnum.ANSWER_REPO_NOT_INDEX;
            } catch (BizException e) {
                return e.getErrorType();
            }
        }

        //查看状态
        if (TaskState.FINISH.equalsName(taskDO.getState())
                || TaskState.PART_BUILDING.equalsName(taskDO.getState())) {
            return ResponseEnum.SUCCESS;
        }

        //可以加上构建进度判断

        return ResponseEnum.ANSWER_REPO_INDEX_BUILDING;
    }


    /**
     * 处理仓库问答对话流程
     */
    private void repoChat(List<ChatMessage> messages,
                          JSONObject repoChatConfig,
                          List<CodeReference> codeReferences,
                          String user,
                          String requestId,
                          String uniqueAnswerId,
                          String streamInputId,
                          RepoInfoModel repoInfo,
                          HttpServletResponse httpServletResponse) {
        log.info("begin repo answer:{}, {};{};",requestId, repoInfo.getRepoAddr(), repoInfo.getBranch());

        String host = getPybloopHost();
        String url = host+AppConstants.CONFIG_KEY_BLOOP_ANSWER_URI;
        log.info("repo pybloop url:{}", url);

        Map<String, Object> requestData = new HashMap<>(5);
        String repoRef = String.format("%s/%s/%s", AppConstants.ANTCODE_GIT_DOMAIN, repoInfo.getRepoGroup(), repoInfo.getRepoName());
        String branch = AntCodeClient.defaultIfBlank(repoInfo.getBranch());

        requestData.put("repo_ref", repoRef);
        requestData.put("messages", messages);
        requestData.put("branch", branch);
        requestData.put("user", user);
        requestData.put("search_only", false);
        requestData.put("request_id", requestId);
        requestData.put("client_config", repoChatConfig);
        requestData.put("references", codeReferences);

        AtomicReference<String> traceId = new AtomicReference<>();


        try {
            HttpClient.post(url)
                    .content(JSON.toJSONString(requestData))
                    .streamExecuteWithResponseHandler(600000, new StreamDataListener() {

                        @Override
                        public void onConnect(Flow.Subscription subscription) {
                            log.info("repo answer on connect, requestId: {}", requestId);
                        }

                        @Override
                        public void eachData(String data, Flow.Subscription subscription) {
                            // 处理取消逻辑

                            if (algoModelUtilService.needCloseInputStream(streamInputId)) {
                                log.info("close input stream");
                                subscription.cancel();
                            }

                            log.debug("repo answer each data: {}", data);
                            if (StringUtils.isBlank(data)) {
                                return;
                            }
                            // 预处理流式数据包
                            if (data.startsWith("data: ")) {
                                data = data.substring(6);
                            }else {
                                log.info("不符合流式格式: {}",data);
                                throw new BizException(ResponseEnum.STREAM_THROW_EXCEPTION,"不符合流式格式");
                            }

                            if(StringUtils.isNotBlank(traceId.get())){
                                data = addTraceIdToStreamData(data, traceId.get());
                            }
                            if(codeGPTDrmConfig.isModelEnableQueue()){
                                ChatUtils.handleForwardQueueStreamData(data,streamDataQueueUtilService,uniqueAnswerId);
                            }else {
                                ChatUtils.handleForwardEveryStreamData(data, noneSerializationCacheManager, uniqueAnswerId);
                            }

                        }

                        @Override
                        public void onError(Throwable throwable) {
                            Pattern pattern = Pattern.compile("^Stream.*cancelled$", Pattern.DOTALL);
                            Matcher matcher = pattern.matcher(throwable.getMessage());
                            if (matcher.find()) {
                                log.error("手动结束流", throwable);
                                return;
                            }
                           log.error("chagpt stream request failed, id: {}, user:{}", requestId, user, throwable);
                            if(codeGPTDrmConfig.isModelEnableQueue()){
                                algoModelUtilService.memoryQueueStreamError(ResponseEnum.ANSWER_REPO_FAIL.getErrorMsg(),uniqueAnswerId,ResponseEnum.STREAM_DELTA_PROCESS_FAILED);
                            }else {
                                algoModelUtilService.handleEveryStreamError(ResponseEnum.ANSWER_REPO_FAIL.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.STREAM_DELTA_PROCESS_FAILED);
                            }
                        }

                        @Override
                        public void onComplete() {
                            log.info("repo answer on complete");
                        }

                    }, (statusCode, errorResponse) -> {
                        log.error("repo chat Stream error,requestId:{}, uniqueAnswerId: {}, statusCode:{},errorResponse:{}", requestId, uniqueAnswerId, statusCode, errorResponse);
                        ChatMessage endMessage = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), "仓库问答流程出错，请重试或者联系管理员排查");
                        if(codeGPTDrmConfig.isModelEnableQueue()){
                            algoModelUtilService.memoryQueueStreamError(endMessage.getContent(),uniqueAnswerId,ResponseEnum.ANSWER_REPO_FAIL);
                        }else {
                            algoModelUtilService.handleEveryStreamError(endMessage.getContent(),noneSerializationCacheManager,uniqueAnswerId,ResponseEnum.ANSWER_REPO_FAIL);
                        }

                    }, responseInfo -> {
                        traceId.set(responseInfo.headers().firstValue("Trace-Id").orElse(null));
                        log.info("repo answer response info, requestId: {}, traceId: {}", requestId, traceId);
                        httpServletResponse.addHeader("Trace-Id", traceId.get());
                        try {
                            httpServletResponse.getWriter().flush();
                        } catch (IOException e) {
                            log.error("flush response error", e);
                        }
                    });
        } catch (Exception e) {
            log.error("仓库问答报错, requestId: {}, uniqueAnswerId: {}", requestId, uniqueAnswerId, e);
            if(codeGPTDrmConfig.isModelEnableQueue()){
                algoModelUtilService.memoryQueueStreamError(ResponseEnum.ANSWER_REPO_FAIL.getErrorMsg(),uniqueAnswerId,ResponseEnum.AI_CALL_ERROR);
            }else {
                algoModelUtilService.handleEveryStreamError(ResponseEnum.ANSWER_REPO_FAIL.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.AI_CALL_ERROR);
            }
        }
    }

    /**
     * 获取pybloop的host
     * @return
     */
    @Override
    public String getPybloopHost(){
        if(VisableEnvUtil.isPrePub()){
            return configService.getConfigByKey(AppConstants.CONFIG_KEY_BLOOP_PRE_HOST, false);
        }else{
            return configService.getConfigByKey(AppConstants.CONFIG_KEY_BLOOP_HOST, false);
        }
    }

    private String addTraceIdToStreamData(String data, String traceId) {
        if (StringUtils.isBlank(data)) {
            return data;
        }
        return data.replaceFirst("\\{", "{\"traceId\":\"" + traceId + "\",");
    }
}
