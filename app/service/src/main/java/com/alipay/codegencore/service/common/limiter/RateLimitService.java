package com.alipay.codegencore.service.common.limiter;

import java.util.List;

import com.alipay.codegencore.model.domain.RateLimitDO;
import com.alipay.codegencore.model.enums.limit.LimitTypeEnum;
import com.alipay.codegencore.model.response.PageResponse;

/**
 * 限流service
 */
public interface RateLimitService {


    /**
     * 根据参数查询限流模版规则
     * @param limitType
     * @param callerList
     * @param targetList
     * @return
     */
    List<RateLimitDO> getRateLimitListByParam(LimitTypeEnum limitType, List<String> callerList, List<String> targetList);

    /**
     * 插入RateLimitDO对象到数据库，只插入非空字段
     *
     * @param rateLimitDO 要插入的RateLimitDO对象
     */
    void insertSelective(RateLimitDO rateLimitDO);

    /**
     * 根据主键更新RateLimitDO对象到数据库，只更新非空字段
     *
     * @param rateLimitDO 要更新的RateLimitDO对象
     */
    void updateByPrimaryKeySelective(RateLimitDO rateLimitDO);

    /**
     * 根据主键删除RateLimitDO对象从数据库
     *
     * @param id 要删除的RateLimitDO对象的主键
     */
    void deleteByPrimaryKey(Long id);

    /**
     * 获取所有限流配置
     *
     * @param pageNo   页数
     * @param pageSize 每页数量
     * @return
     */
    PageResponse<List<RateLimitDO>> getAllRateLimit(int pageNo, int pageSize,String query,String type,String template,Boolean needLimit,String sorted);

    /**
     * 根据id查询一条限流规则
     *
     * @param id 页数
     * @return
     */
    RateLimitDO getRateLimitById(Long id);

}
