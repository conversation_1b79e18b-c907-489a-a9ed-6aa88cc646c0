package com.alipay.codegencore.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.AlgoBackendDOExample;
import com.alipay.codegencore.dal.example.AlgoBackendDOExample.Criteria;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.AlgoBackendDOMapper;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.AlgoBackendImplEnum;
import com.alipay.codegencore.model.enums.BaseModelHandlerEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserTypeEnum;
import com.alipay.codegencore.model.enums.VisableUserEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.AlgoBackendModel;
import com.alipay.codegencore.model.model.ModelAvailableServer;
import com.alipay.codegencore.model.model.ModelAvailableSimply;
import com.alipay.codegencore.model.model.ModelCurrentAvailableInfo;
import com.alipay.codegencore.model.model.ModelHealthCheck;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.impl.model.AlgoModelHealthUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.alipay.codegencore.model.contant.WebApiContents.CONTEXT_USER;

/**
 * 算法模型配置实现类
 */
@Service("algoBackendService")
public class AlgoBackendServiceImpl implements AlgoBackendService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AlgoBackendServiceImpl.class);
    private static final Set<String> SUPPORTED_IMPLS = Set.of(
            AlgoBackendImplEnum.CodeGPTModelHandler.name(),
            AlgoBackendImplEnum.MayaStreamModelHandler.name()
    );

    @Resource
    private AlgoBackendDOMapper algoBackendDOMapper;
    @Resource
    private UserAuthDOMapper userAuthDOMapper;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;
    @Resource
    private MayaService mayaService;


    @Override
    public AlgoBackendDO getAlgoBackendByName(String model){

        if(model == null){
            return null;
        }

        model = model.toUpperCase(); //配置中的模型名称统一为大写

        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andModelEqualTo(model);

        List<AlgoBackendDO> algoBackendDOList = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        AlgoBackendDO ret = algoBackendDOList.isEmpty() ? null : algoBackendDOList.get(0);

        //处理跳转
        if(ret != null && ret.getJump() != null){
            ret = getAlgoBackendByName(ret.getJump());
        }

        return ret;
    }

    @Override
    public AlgoBackendDO getAlgoBackendByNameNoJump(String model){
        if(model == null){
            return null;
        }
        model = model.toUpperCase(); //配置中的模型名称统一为大写
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andModelEqualTo(model);
        List<AlgoBackendDO> algoBackendDOList = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        return algoBackendDOList.isEmpty() ? null : algoBackendDOList.get(0);
    }

    @Override
    public AlgoBackendModel selectByPrimaryKey(Long id) {
        AlgoBackendDO algoBackendDO = algoBackendDOMapper.selectByPrimaryKey(id);
        AlgoBackendModel algoBackendModel = new AlgoBackendModel();
        BeanUtil.copyProperties(algoBackendDO, algoBackendModel);
        updateResponseOwnerInfo(algoBackendModel);
        return algoBackendModel;
    }

    /**
     * 获取默认的模型
     *
     * @return 默认模型信息
     */
    @Override
    public AlgoBackendDO getDefaultAlgoBackend() {
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andEnableEqualTo(true)
                .andVisableUserEqualTo (VisableUserEnum.ALL.getCode());
        List<AlgoBackendDO> algoBackendDOList = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        return algoBackendDOList.get(0);
    }

    @Override
    public List<AlgoBackendDO> getAllAlgoBackend(){

        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        return algoBackendDOMapper.selectByExample(algoBackendDOExample);
    }

    /**
     * 获取所有模型-分页
     * @param pageNo
     * @param pageSize
     * @param modelName  根据模模型名称模糊查询
     * @return
     */
    @Override
    public PageResponse<List<AlgoBackendModel>> getAllPageAlgoBackend(int pageNo, int pageSize, String modelName) {
        Page<AlgoBackendModel> pageInfo = PageHelper.startPage(pageNo, pageSize);
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        if (StringUtils.isNotEmpty(modelName)){
            algoBackendDOExample.createCriteria().andModelLike("%" + modelName + "%");
            algoBackendDOExample.or().andModelDescriptionLike("%" + modelName + "%");
        }
        List<AlgoBackendDO> algoBackendDOS = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        List<AlgoBackendModel> algoBackendModels = new ArrayList<>();
        for (AlgoBackendDO algoBackendDO : algoBackendDOS) {
            AlgoBackendModel algoBackendModel = new AlgoBackendModel();
            BeanUtil.copyProperties(algoBackendDO, algoBackendModel);
            updateResponseOwnerInfo(algoBackendModel);
            algoBackendModels.add(algoBackendModel);
        }
        return PageResponse.build(ResponseEnum.SUCCESS, algoBackendModels, pageInfo.getTotal());
    }

    /**
     * 新增模型
     *
     * @param algoBackendModel
     * @return
     */
    @Override
    public Boolean addModel(AlgoBackendModel algoBackendModel, UserAuthDO userAuthDO) {
        String model = algoBackendModel.getModel();
        if (model == null || !model.matches("^[A-Z0-9_]+$")) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型名只能有大写字母，下划线，数字");
        }
        algoBackendModel.setCreateUserId(userAuthDO.getId().intValue());
        algoBackendModel.setModel(model);
        updateOwnerUserId(algoBackendModel);
        if (algoBackendModel.getOwnerUserId() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型名负责人不能为空");
        }
        int insert = algoBackendDOMapper.insertSelective(algoBackendModel);
        LOGGER.info("addModel success,operator empId:{},alipayAccount:{},algoBackendModel:{}",
                userAuthDO.getEmpId(), userAuthDO.getAlipayAccount(), JSON.toJSONString(algoBackendModel));
        return insert == 1;
    }

    /**
     * 修改模型信息
     *
     * @param algoBackendModel
     * @return
     */
    @Override
    public Boolean updateModel(AlgoBackendModel algoBackendModel, UserAuthDO userAuthDO) {
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andIdEqualTo(algoBackendModel.getId());
        String model = algoBackendModel.getModel().toUpperCase();
        model = model.replaceAll(" ", "");
        algoBackendModel.setModel(model);
        updateOwnerUserId(algoBackendModel);
        AlgoBackendDO algoBackendDODb = algoBackendDOMapper.selectByPrimaryKey(algoBackendModel.getId());
        // db里没有ownerUserId 这次编辑时强制填写
        if (algoBackendDODb.getOwnerUserId() == null &&  algoBackendModel.getOwnerUserId() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型名负责人不能为空");
        }
        int updated = algoBackendDOMapper.updateByExampleSelective(algoBackendModel, algoBackendDOExample);
        LOGGER.info("updateModel success,operator empId:{},alipayAccount:{},algoBackendDODb:{},algoBackendDOUpdate:{}",
                userAuthDO.getEmpId(), userAuthDO.getAlipayAccount(), JSON.toJSONString(algoBackendDODb), JSON.toJSONString(algoBackendModel));
        return updated == 1;
    }


    /**
     * 获取模型jump配置的下拉列表
     *
     * @return
     */
    @Override
    public List<String> getModelJumpConfigList() {
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andEnableNotEqualTo(false).andJumpIsNull();
        List<AlgoBackendDO> algoBackendDOS = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        return algoBackendDOS.stream().map(AlgoBackendDO::getModel).distinct().sorted(String::compareTo).collect(Collectors.toList());
    }

    /**
     * 删除指定模型信息
     *
     * @param id 模型id
     * @return
     */
    @Override
    public boolean deleteModel(long id, UserAuthDO userAuthDO) {
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andIdEqualTo(id);
        AlgoBackendDO algoBackendDODb = algoBackendDOMapper.selectByPrimaryKey(id);
        int delete = algoBackendDOMapper.deleteByExample(algoBackendDOExample);
        LOGGER.info("deleteModel success,operator empId:{},alipayAccount:{},algoBackendDODb:{}",
                userAuthDO.getEmpId(), userAuthDO.getAlipayAccount(), JSON.toJSONString(algoBackendDODb));
        return delete == 1;
    }

    @Override
    public List<AlgoBackendDO> getUserModelList(Long ownerUserId) {
        if (ownerUserId  == null){
            return null;
        }
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andOwnerUserIdEqualTo(ownerUserId.intValue());
        List<AlgoBackendDO> algoBackendDOList = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        return algoBackendDOList.stream().map(map ->{
            if (StringUtils.isNotEmpty(map.getJump())){
                return getAlgoBackendByName(map.getModel());
            }
            return map;
        }).collect(Collectors.toList());
    }

    private void updateOwnerUserId(AlgoBackendModel algoBackendModel) {
        if (algoBackendModel==null) {
            return;
        }
        if (codeGPTDrmConfig.isIntranetApplication()) {
            if (StringUtils.isNotEmpty(algoBackendModel.getOwnerUserEmpId())) {
                UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
                userAuthDOExample.createCriteria().andEmpIdEqualTo(algoBackendModel.getOwnerUserEmpId());
                List<UserAuthDO> userAuthDOList = userAuthDOMapper.selectByExample(userAuthDOExample);
                if (CollectionUtils.isNotEmpty(userAuthDOList)) {
                    UserAuthDO ownerUser = userAuthDOList.get(0);
                    algoBackendModel.setOwnerUserId(ownerUser.getId().intValue());
                }
            }
        } else {
            if (StringUtils.isNotEmpty(algoBackendModel.getAlipayAccount())) {
                UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
                userAuthDOExample.createCriteria().andAlipayAccountEqualTo(algoBackendModel.getAlipayAccount());
                List<UserAuthDO> userAuthDOList = userAuthDOMapper.selectByExample(userAuthDOExample);
                if (CollectionUtils.isNotEmpty(userAuthDOList)) {
                    UserAuthDO ownerUser = userAuthDOList.get(0);
                    algoBackendModel.setOwnerUserId(ownerUser.getId().intValue());
                }
            }
        }
    }

    private void updateResponseOwnerInfo(AlgoBackendModel algoBackendModel) {
        if (algoBackendModel == null || algoBackendModel.getOwnerUserId() == null) {
            return;
        }
        UserAuthDO userAuthDO = userAuthDOMapper.selectByPrimaryKey(Long.valueOf(algoBackendModel.getOwnerUserId()));
        if (userAuthDO == null) {
            return;
        }
        if (codeGPTDrmConfig.isIntranetApplication()) {
            algoBackendModel.setOwnerUserEmpId(userAuthDO.getEmpId());
            algoBackendModel.setOwnerUserNickName(userAuthDO.getUserName());
        } else {
            algoBackendModel.setAlipayAccount(userAuthDO.getAlipayAccount());
        }
    }

    /**
     * 获取用户模型
     *
     * @param pageNo    页数
     * @param pageSize  每页大小
     * @param modelName 根据模模型名称模糊查询
     * @return
     */
    @Override
    public PageResponse<List<AlgoBackendModel>> getUserAlgoBackend(int pageNo, int pageSize, String modelName) {
        Page<AlgoBackendDO> pageInfo = PageHelper.startPage(pageNo, pageSize);
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        Criteria criteria = algoBackendDOExample.createCriteria();
        criteria.andOwnerUserIdEqualTo(Math.toIntExact(currentUser.getId()));
        criteria.andImplIn(Arrays.asList(BaseModelHandlerEnum.AntGLM.getValue(),BaseModelHandlerEnum.CodeFuse.getValue()));
        Criteria or = algoBackendDOExample.or();
        or.andCreateUserIdEqualTo(Math.toIntExact(currentUser.getId()));
        or.andImplIn(Arrays.asList(BaseModelHandlerEnum.AntGLM.getValue(),BaseModelHandlerEnum.CodeFuse.getValue()));
        if (StringUtils.isNotEmpty(modelName)) {
            or.andModelLike("%" + modelName + "%");
            criteria.andModelLike("%" + modelName + "%");
        }
        List<AlgoBackendModel> algoBackendModels = new ArrayList<>();
        List<AlgoBackendDO> algoBackendDOS = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        for (AlgoBackendDO algoBackendDO : algoBackendDOS) {
            AlgoBackendModel algoBackendModel = new AlgoBackendModel();
            BeanUtil.copyProperties(algoBackendDO, algoBackendModel);
            updateResponseOwnerInfo(algoBackendModel);
            upModelHealthDegree(algoBackendModel);
            algoBackendModels.add(algoBackendModel);
        }
        return PageResponse.build(ResponseEnum.SUCCESS, algoBackendModels, pageInfo.getTotal());
    }

    /**
     * 通过Id查找模型
     *
     * @param id 模型id
     * @return
     */
    @Override
    public AlgoBackendModel getUserModelInfo(Long id) {
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andIdEqualTo(id);
        List<AlgoBackendDO> algoBackendDOS = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        AlgoBackendModel algoBackendModel = null;
        if (!algoBackendDOS.isEmpty()){
            if(!checkUserAuth(algoBackendDOS.get(0))){
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "您没有权限");
            }
            algoBackendModel = new AlgoBackendModel();
            BeanUtil.copyProperties(algoBackendDOS.get(0), algoBackendModel);
            updateResponseOwnerInfo(algoBackendModel);
            JSONObject implConfig = JSON.parseObject(algoBackendModel.getImplConfig());
            implConfig.put("maxRound",algoBackendModel.getMaxRound());
            implConfig.put("maxToken",algoBackendModel.getMaxToken());
            algoBackendModel.setImplConfig(JSON.toJSONString(implConfig));
        }
        return algoBackendModel;
    }

    /**
     * 用户新增模型
     *
     * @return
     */
    @Override
    public Boolean addUserModel(AlgoBackendModel algoBackendDO) {
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        String model = algoBackendDO.getModel();
        if (model == null || !model.matches("^[A-Z0-9_]+$")) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型名只能有大写字母，下划线，数字");
        }
        if (model.length() > 32) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型名过长");
        }
        if (algoBackendDO.getModelDescription() == null || algoBackendDO.getModelDescription().length() >999) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型描述参数过长");
        }
        algoBackendDOExample.createCriteria().andModelEqualTo(model);
        List<AlgoBackendDO> algoBackendDOS = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        if (CollectionUtil.isNotEmpty(algoBackendDOS)) {
            throw new BizException(ResponseEnum.SCENE_REPEAT);
        }
        //校验模型implConfig配置 当前只有格式校验
        checkImplConfig(algoBackendDO);

        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        JSONObject implConfig = JSON.parseObject(algoBackendDO.getImplConfig());
        algoBackendDO.setMaxRound(implConfig.getInteger("maxRound"));
        algoBackendDO.setMaxToken(implConfig.getInteger("maxToken"));
        implConfig.remove("maxRound");
        implConfig.remove("maxToken");
        algoBackendDO.setImplConfig(JSON.toJSONString(implConfig));
        algoBackendDO.setModel(model);
        algoBackendDO.setEnable(algoBackendDO.getEnable() != null && algoBackendDO.getEnable());
        algoBackendDO.setCreateUserId(Math.toIntExact(currentUser.getId()));
        // 所有环境可见
        algoBackendDO.setVisableEnv(1);
        // 所有人可见
        algoBackendDO.setVisableUser(1);
        //健康检查默认关闭
        algoBackendDO.setNeedHealthCheck(false);
        //gptCache 默认关闭
        algoBackendDO.setEnableGptCache(false);
        convertOwnerUserEmpIdToUserId(algoBackendDO);
        int insert = algoBackendDOMapper.insertSelective(algoBackendDO);
        LOGGER.info("addModel success,operator empId:{},alipayAccount:{},algoBackendDO:{}",
                currentUser.getEmpId(), currentUser.getAlipayAccount(), JSON.toJSONString(algoBackendDO));
        return insert == 1;
    }

    /**
     * 校验模型implConfig配置
     *
     * @param algoBackendDO
     */
    private void checkImplConfig(AlgoBackendModel algoBackendDO) {
        try {
            JSON.parseObject(algoBackendDO.getImplConfig());
        } catch (Exception e) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "implConfig格式错误");
        }
    }

    /**
     * 将前端传的 ownerUserEmpId 转为userId
     *
     * @param algoBackendDO
     */
    private void convertOwnerUserEmpIdToUserId(AlgoBackendModel algoBackendDO) {
        if (algoBackendDO.getOwnerUserEmpId() != null) {
            List<String> empIds = codeFuseUserAuthService.insertUserAuth(Collections.singletonList(algoBackendDO.getOwnerUserEmpId()));
            if (!empIds.isEmpty()) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "ownerUserEmpId 不存在");
            }
            Long userId = codeFuseUserAuthService.empId2UserId(algoBackendDO.getOwnerUserEmpId());
            algoBackendDO.setOwnerUserId(userId.intValue());
        }
    }

    /**
     * 用户修改模型信息
     *
     * @param algoBackendDO 模型信息
     * @return
     */
    @Override
    public Boolean updateUserModel(AlgoBackendModel algoBackendDO) {
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andIdEqualTo(algoBackendDO.getId());
        AlgoBackendDO algoBackendDODb = algoBackendDOMapper.selectByPrimaryKey(algoBackendDO.getId());
        if (algoBackendDODb == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型ID不正确");
        }
        if (algoBackendDO.getModel() != null && algoBackendDO.getModel().length() > 32 && !algoBackendDO.getModel().startsWith("ACI") && !algoBackendDO.getModel().startsWith("MASS")) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型名过长");
        }
        if (algoBackendDO.getModelDescription() != null && algoBackendDO.getModelDescription().length() > 999) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型描述参数过长");
        }
        // 非管理员只能操作自己的模型
        if (currentUser.getAdmin() == 0) {
            boolean isCreateUser = algoBackendDODb.getCreateUserId() != null && algoBackendDODb.getCreateUserId().longValue() == currentUser.getId();
            boolean isOwnerUser = algoBackendDODb.getOwnerUserId() != null && algoBackendDODb.getOwnerUserId().longValue() == currentUser.getId();
            // 不是创建者也不是owner就不能修改
            if (!isCreateUser && !isOwnerUser) {
                throw new BizException(ResponseEnum.NO_AUTH);
            }
        }
        if (algoBackendDO.getImplConfig() != null) {
            JSONObject implConfig = JSON.parseObject(algoBackendDO.getImplConfig());
            JSONObject dbImplConfig = JSONObject.parseObject(algoBackendDODb.getImplConfig());
            if(algoBackendDO.getImpl()!=null && algoBackendDO.getImpl().equalsIgnoreCase(algoBackendDODb.getImpl())){
                // 不修改基座模型时，模型只需要更新老配置参数的值
                dbImplConfig.forEach((key, value) -> {
                    if (implConfig.get(key) != null) {
                        dbImplConfig.put(key, implConfig.get(key));
                    }
                });
                algoBackendDO.setImplConfig(JSON.toJSONString(dbImplConfig));
            }
            if (implConfig.get("maxRound") != null){
                algoBackendDO.setMaxRound(implConfig.getInteger("maxRound"));
            }
            if ( implConfig.get("maxToken") != null){
                algoBackendDO.setMaxToken(implConfig.getInteger("maxToken"));
            }
        }
        // 模型创建人不可更改
        algoBackendDO.setCreateUserId(null);
        convertOwnerUserEmpIdToUserId(algoBackendDO);
        int updated = algoBackendDOMapper.updateByExampleSelective(algoBackendDO, algoBackendDOExample);
        LOGGER.info("updateUserModel success,operator empId:{},alipayAccount:{},algoBackendDODb:{},algoBackendDOUpdate:{}",
                currentUser.getEmpId(), currentUser.getAlipayAccount(), JSON.toJSONString(algoBackendDODb),
                JSON.toJSONString(algoBackendDO));
        return updated == 1;
    }

    /**
     * 用户删除指定模型信息
     *
     * @param id 模型id
     * @return
     */
    @Override
    public boolean deleteUserModel(long id) {
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria()
                .andOwnerUserIdEqualTo(Math.toIntExact(currentUser.getId()))
                .andIdEqualTo(id);
        algoBackendDOExample.or().andCreateUserIdEqualTo(Math.toIntExact(currentUser.getId()))
                .andIdEqualTo(id);
        AlgoBackendDO algoBackendDO = algoBackendDOMapper.selectByPrimaryKey(id);
        int delete = algoBackendDOMapper.deleteByExample(algoBackendDOExample);
        LOGGER.info("deleteUserModel success,operator empId:{},alipayAccount:{},algoBackendDODb:{}",
                currentUser.getEmpId(), currentUser.getAlipayAccount(), JSON.toJSONString(algoBackendDO));
        return delete == 1;
    }

    /**
     * 获取基座模型
     *
     * @param pageNo    页数
     * @param pageSize  每页大小
     * @param modelName 根据模模型名称模糊查询
     * @return
     */
    @Override
    public PageResponse<List<AlgoBackendModel>> getModelBase(int pageNo, int pageSize, String modelName) {
        Page<AlgoBackendDO> pageInfo = PageHelper.startPage(pageNo, pageSize);
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        Criteria criteriaModelName = algoBackendDOExample.createCriteria();
        Criteria criteriaContent = algoBackendDOExample.createCriteria();
        criteriaModelName.andModelBaseEqualTo(true).andEnableEqualTo(true);
        criteriaContent.andModelBaseEqualTo(true).andEnableEqualTo(true);
        if (StringUtils.isNotEmpty(modelName)) {
            criteriaModelName.andModelLike("%" + modelName + "%");
            criteriaContent.andModelDescriptionLike("%" + modelName + "%");
        }
        algoBackendDOExample.or(criteriaContent);
        List<AlgoBackendModel> algoBackendModels = new ArrayList<>();
        List<AlgoBackendDO> algoBackendDOS = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        JSONObject sceneId = JSONObject.parseObject(codeGPTDrmConfig.getSceneId());

        for (AlgoBackendDO algoBackendDO : algoBackendDOS) {
            AlgoBackendModel algoBackendModel = new AlgoBackendModel();
            BeanUtil.copyProperties(algoBackendDO, algoBackendModel);
            algoBackendModel.setSceneId(sceneId.getString(algoBackendDO.getModel()));
            updateResponseOwnerInfo(algoBackendModel);
            upModelHealthDegree(algoBackendModel);
            algoBackendModels.add(algoBackendModel);
        }
        return PageResponse.build(ResponseEnum.SUCCESS, algoBackendModels, pageInfo.getTotal());
    }


    @Override
    public void addUsageMessageCount(String model) {
        AlgoBackendDO algoBackendDO = getAlgoBackendByNameNoJump(model);
        if (algoBackendDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        algoBackendDO.setUsageMessageCount(algoBackendDO.getUsageMessageCount() + 1);
        AlgoBackendDOExample updateParam = new AlgoBackendDOExample();
        updateParam.createCriteria().andIdEqualTo(algoBackendDO.getId());
        algoBackendDOMapper.updateByExampleSelective(algoBackendDO, updateParam);
    }

    @Override
    public void addUsageUserCount(String model) {
        AlgoBackendDO algoBackendDO = getAlgoBackendByNameNoJump(model);
        if (algoBackendDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        algoBackendDO.setUsageUserCount(algoBackendDO.getUsageUserCount() + 1);
        AlgoBackendDOExample updateParam = new AlgoBackendDOExample();
        updateParam.createCriteria().andIdEqualTo(algoBackendDO.getId());
        algoBackendDOMapper.updateByExampleSelective(algoBackendDO, updateParam);
    }

    @Override
    public void addUsageSessionCount(String model) {
        AlgoBackendDO algoBackendDO = getAlgoBackendByNameNoJump(model);
        if (algoBackendDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        algoBackendDO.setUsageSessionCount(algoBackendDO.getUsageSessionCount() + 1);
        AlgoBackendDOExample updateParam = new AlgoBackendDOExample();
        updateParam.createCriteria().andIdEqualTo(algoBackendDO.getId());
        algoBackendDOMapper.updateByExampleSelective(algoBackendDO, updateParam);
    }

    @Override
    public List<AlgoBackendDO> getAllSeeModel() {
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        algoBackendDOExample.createCriteria().andVisableUserEqualTo(VisableUserEnum.ALL.getCode())
                .andEnableEqualTo(true).andModelBaseEqualTo(false);
        return algoBackendDOMapper.selectByExample(algoBackendDOExample);
    }

    @Override
    public PageResponse<List<ModelCurrentAvailableInfo>> getModelAvailableInfo(int pageNo, int pageSize, String modelName) {
        Page<AlgoBackendModel> pageInfo = PageHelper.startPage(pageNo, pageSize);
        AlgoBackendDOExample algoBackendDOExample = new AlgoBackendDOExample();
        Criteria criteria = algoBackendDOExample.createCriteria();
        if (StringUtils.isNotEmpty(modelName)){
            criteria.andModelLike("%" + modelName + "%");
        }
        criteria.andImplIn(Arrays.asList(AlgoBackendImplEnum.CodeGPTModelHandler.name(),AlgoBackendImplEnum.MayaStreamModelHandler.name()));
        List<AlgoBackendDO> algoBackendDOS = algoBackendDOMapper.selectByExample(algoBackendDOExample);
        List<ModelCurrentAvailableInfo> ModelCurrentAvailableInfoList = new ArrayList<>();
        algoBackendDOS.parallelStream().forEach(algoBackendDO -> {
            ModelCurrentAvailableInfo modelCurrentAvailableInfo = new ModelCurrentAvailableInfo();
            modelCurrentAvailableInfo.setModelName(algoBackendDO.getModel());
            modelCurrentAvailableInfo.setModelDescription(algoBackendDO.getModelDescription());
            modelCurrentAvailableInfo.setVisableEnv(algoBackendDO.getVisableEnv());
            try {
                JSONObject modelHealthDegree = algoModelHealthUtilService.getModelHealthDegree(algoBackendDO.getModel());
                ModelHealthCheck modelHealthCheck = JSON.parseObject(
                        JSON.toJSONString(modelHealthDegree),
                        ModelHealthCheck.class);
                modelCurrentAvailableInfo.setHealth(modelHealthCheck);
                JSONObject result = mayaService.getModelAvailableServers(algoBackendDO.getModel(), false);
                ModelAvailableServer modelAvailableServer = JSON.parseObject(
                        JSON.toJSONString(result),
                        ModelAvailableServer.class);
                modelCurrentAvailableInfo.setDeployInfo(modelAvailableServer);

            } catch (RuntimeException e) {
                LOGGER.warn("getModelAvailableInfo fail  modelName:{} ，error:{}", algoBackendDO.getModel(),e);
            }
            ModelCurrentAvailableInfoList.add(modelCurrentAvailableInfo);
        });
        return PageResponse.build(ResponseEnum.SUCCESS, ModelCurrentAvailableInfoList, pageInfo.getTotal());
    }

    @Override
    public ModelCurrentAvailableInfo getSingleModelAvailableInfo(String modelName) {
        AlgoBackendDO algoBackendDO = getAlgoBackendByName(modelName);
        if(algoBackendDO == null){
            LOGGER.info("find no model named {}",modelName);
            return null;
        }
        if(!SUPPORTED_IMPLS.contains(algoBackendDO.getImpl())){
            LOGGER.info("model is not supported {}",modelName);
            return null;
        }
        ModelCurrentAvailableInfo modelCurrentAvailableInfo = new ModelCurrentAvailableInfo();
        modelCurrentAvailableInfo.setModelName(modelName);
        modelCurrentAvailableInfo.setModelDescription(algoBackendDO.getModelDescription());
        modelCurrentAvailableInfo.setVisableEnv(algoBackendDO.getVisableEnv());
        try {
            JSONObject modelHealthDegree = algoModelHealthUtilService.getModelHealthDegree(algoBackendDO.getModel());
            ModelHealthCheck modelHealthCheck = JSON.parseObject(
                    JSON.toJSONString(modelHealthDegree),
                    ModelHealthCheck.class);
            modelCurrentAvailableInfo.setHealth(modelHealthCheck);
            JSONObject result = mayaService.getModelAvailableServers(algoBackendDO.getModel(), true);
            ModelAvailableServer modelAvailableServer = JSON.parseObject(
                    JSON.toJSONString(result),
                    ModelAvailableServer.class);
            modelCurrentAvailableInfo.setDeployInfo(modelAvailableServer);

        } catch (RuntimeException e) {
            LOGGER.warn("getModelAvailableInfo fail  modelName:{} ，error:{}", algoBackendDO.getModel(),e);
        }
        return modelCurrentAvailableInfo;
    }

    @Override
    public List<ModelAvailableSimply> checkModelsAvailable(List<String> modelNames) {
        if(CollectionUtils.isEmpty(modelNames)){
            return null;
        }
        List<ModelAvailableSimply> modelsAvailable = new ArrayList<>();
        for (String modelName : modelNames) {
            AlgoBackendDO algoBackendDO = getAlgoBackendByName(modelName);
            ModelAvailableSimply modelAvailable = new ModelAvailableSimply(modelName);
            try {
                JSONObject result = mayaService.getModelAvailableServers(modelName, true);
                ModelAvailableServer modelAvailableServer = JSON.parseObject(
                        JSON.toJSONString(result),
                        ModelAvailableServer.class);
                if(modelAvailableServer!=null){
                    modelAvailable.setPre(modelAvailableServer.getPre().getSummary().isAvailable());
                    modelAvailable.setProd(modelAvailableServer.getProd().getSummary().isAvailable());
                }else {
                    modelAvailable.setProd(false);
                    modelAvailable.setPre(false);
                }
                if(modelAvailable.getPre()){
                    modelAvailable.setPre(algoModelHealthUtilService.isHealth("pre", algoBackendDO.getModel()) > 0);
                }
                if(modelAvailable.getProd()){
                    modelAvailable.setProd(algoModelHealthUtilService.isHealth("prod", algoBackendDO.getModel()) > 0);
                }
            }catch (RuntimeException e) {
                LOGGER.warn("getModelAvailableInfo fail  modelName:{} ，error:{}", algoBackendDO.getModel(),e);
                modelAvailable.setProd(false);
                modelAvailable.setPre(false);
            }
            modelsAvailable.add(modelAvailable);
        }
        return modelsAvailable;
    }

    /**
     * 填充模型的健康度信息
     *
     * @param algoBackendModel
     */
    private void upModelHealthDegree(AlgoBackendModel algoBackendModel) {
        JSONObject modelHealthDegree = algoModelHealthUtilService.getModelHealthDegree(algoBackendModel.getModel());
        LOGGER.info("getModelHealthDegree modelName:{},modelHealthDegree{}", algoBackendModel.getModel(),
                modelHealthDegree);
        Integer isHealthPre = algoModelHealthUtilService.isHealth("pre", algoBackendModel.getModel());
        Integer isHealthProd = algoModelHealthUtilService.isHealth("prod", algoBackendModel.getModel());
        try {
            ModelHealthCheck modelHealthCheck = JSON.parseObject(
                    JSON.toJSONString(modelHealthDegree),
                    ModelHealthCheck.class);
            algoBackendModel.setHealthPre(isHealthPre);
            algoBackendModel.setHealthProd(isHealthProd);
            String lastFailedReasonPre = modelHealthCheck.getPre().getSummary().getLastFailedReason();
            String lastFailedReasonProd = modelHealthCheck.getProd().getSummary().getLastFailedReason();
            algoBackendModel.setLastFailedReasonPre(ResponseEnum.valueOf(lastFailedReasonPre).getErrorMsg());

            algoBackendModel.setLastFailedReasonProd(ResponseEnum.valueOf(lastFailedReasonProd).getErrorMsg());

        } catch (RuntimeException e) {
            LOGGER.info("getModelHealthDegree fail  modelName:{},modelHealthDegree{}", algoBackendModel.getModel(), modelHealthDegree);
        }
    }

    /**
     * 检查当前用户是否有权限
     *
     * <AUTHOR>
     * @since 2024.11.04
     * @param algoBackendDO algoBackendDO
     * @return boolean
     */
    private boolean checkUserAuth(AlgoBackendDO algoBackendDO){
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if(currentUser == null){
            return false;
        }
        if(algoBackendDO.getVisableUser() == 1){
            return true;
        }
        if(currentUser.getAdmin() == UserTypeEnum.SUPER_ADMIN.getCode()){
            return true;
        }
        if(algoBackendDO.getVisableUser() == 2 ){
            if(currentUser.getAdmin() == UserTypeEnum.ADMIN.getCode()||algoBackendDO.getOwnerUserId().longValue() == currentUser.getId()||algoBackendDO.getCreateUserId().longValue() == currentUser.getId()){
                return true;
            }
        }
        if(algoBackendDO.getVisableUser() == 0){
            return algoBackendDO.getOwnerUserId().longValue() == currentUser.getId()||algoBackendDO.getCreateUserId().longValue() == currentUser.getId();
        }
        return false;
    }

}
