package com.alipay.codegencore.service.tool.learning.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.enums.ChatMessageStatusEnum;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.LackParamStrategyEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.PluginServiceRequestContext;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.model.tool.learning.FunctionCallConfig;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.model.tool.learning.RuntimeInfo;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLogGroup;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatFunction;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.impl.model.AlgoModelUtilService;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.FunctionCallService;
import com.alipay.codegencore.service.tool.learning.PluginConfigService;
import com.alipay.codegencore.service.tool.learning.PluginWorkflowService;
import com.alipay.codegencore.service.tool.learning.ToolLearningUtil;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FunctionCallServiceImpl implements FunctionCallService {

    private static final Logger LOGGER = LoggerFactory.getLogger( FunctionCallServiceImpl.class );

    /**
     * 默认参数的正则表达式
     */
    public static final Pattern DEFAULT_PARAM_PATTERN = Pattern.compile("^(\\$)?\\{(\\w+)}$");

    @AppConfig("tool_search_url")
    private String toolSearchUrl;
    @Resource
    private CheckService checkService;

    @Resource
    private AlgoModelUtilService algoModelUtilService;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private PluginConfigService pluginConfigService;

    @Resource
    private PluginWorkflowService pluginWorkflowService;

    @Resource
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private SceneService sceneService;

    @Resource
    private SecAgentHelper secAgentHelper;

    @Override
    public List<String> searchTool(List<ChatMessage> chatMessageList, SceneDO sceneDO) {
        Map<String, Object> params = new HashMap<>();
        params.put("messages", chatMessageList);
        params.put("sceneId", sceneDO.getId().toString());

        String responseStr = null;
        try {
            responseStr = HttpClient.post(toolSearchUrl).content(JSON.toJSONString(params)).syncExecuteWithExceptionThrow(10000);
            JSONObject response = JSON.parseObject(responseStr);
            return response.getJSONObject("result").getJSONObject("CodegptToolManager").getJSONArray("toolList").toJavaList(String.class);
        } catch (Exception e) {
            LOGGER.error("search tool error, param: {}",JSON.toJSONString(params), e);
            return null;
        }
    }

    /**
     * 执行chatFunctionCall
     *
     * @param chatFunctionCall 方法调用
     */
    @Override
    public String executeFunction(PluginServiceRequestContext queryParams,
                                  ChatFunctionCall chatFunctionCall,
                                  PluginDO pluginDO,
                                  Integer index,
                                  boolean isContinue) {

        // 集成 agentSecSdk.callTool, 判断用户是否有 工具/插件 使用权限, 根据 agentSecSdk.callTool 返回结果做相应处理
        secAgentHelper.checkPluginAuth(queryParams,pluginDO,chatFunctionCall);

        Map<String, Object> workFlowConfig = pluginConfigService.parseWorkFlowYaml(pluginDO.getWorkflowConfig());

        String argumentsJson = chatFunctionCall.getArguments();

        JSONObject arguments = JSON.parseObject(argumentsJson);

        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey)-> noneSerializationCacheManager.del(tBaseKey);
        Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer = (pluginStreamPartResponse)-> LOGGER.info(JSON.toJSONString(pluginStreamPartResponse));

        PluginServiceRequestContext params = new PluginServiceRequestContext();
        BeanUtil.copyProperties(queryParams, params);

        params.setPluginParams(arguments);
        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        params.setPluginStreamPartResponseConsumer(pluginStreamPartResponseConsumer);
        // 流式会用到这个Handler
        params.setPluginIndex(index);
        PluginInfo pluginInfo = new PluginInfo(pluginDO.getId(), pluginDO.getName(), pluginDO.getDescription());
        params.setPluginInfo(pluginInfo);
        params.setPluginDO(pluginDO);

        //流式传输，要把response设置为流式
        //ChatUtils.setServletToEventStream(httpServletResponse);
        String pluginExecutionResult = pluginWorkflowService.pluginMainWorkflow(params, workFlowConfig, true, isContinue);
        LOGGER.info("pluginExecutionResult:{}", pluginExecutionResult);
        return pluginExecutionResult;
    }

    /**
     * 流式对话
     *
     * @param queryParams 用户提问
     * @param pluginDOList    数据库中的插件信息
     */
    @Override
    public void streamChat(PluginServiceRequestContext queryParams, List<PluginDO> pluginDOList) {
        ChatCompletionRequest chatCompletionRequest = queryParams.getChatCompletionRequest();
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        List<ChatMessage> copyMessages = JSON.parseArray(JSON.toJSONString(messages), ChatMessage.class);

        // 检查提问是否符合安全审查规范
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(queryParams.getRequestId(), messages, queryParams.getChatCompletionRequest().getChatRequestExtData(),false);

        if (!requestCheckResultModel.isAllCheckRet()) {
            ToolLearningUtil.stopStream( ResponseEnum.CHECK_FAILED, null, requestCheckResultModel, queryParams.getPluginStreamPartResponseConsumer(), queryParams.getPluginResultHandler(), queryParams.getAnswerUid(), null, algoModelUtilService.getCheckFailedMsg(requestCheckResultModel), null);
            return;
        }

        Map<String, Pair<ChatFunction, PluginDO>> chatFunctionInfo = ToolLearningUtil.exactFunctionInfoFromPlugin(pluginDOList);

        List<ChatMessage> completeChatMessageList = getCompleteChatMessageList(queryParams);

        FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(queryParams.getSceneDO());
        List<String> qualifiedFunctionNameList;
        //只有配置了toolSearchEnabled并且设置为true的时候才会进行tool search
        if(functionCallConfig.getToolSearchEnabled()!=null && functionCallConfig.getToolSearchEnabled()){
            qualifiedFunctionNameList = searchTool(completeChatMessageList, queryParams.getSceneDO());
        }else{
            LOGGER.info("tool search disabled, skip tool search");
            qualifiedFunctionNameList = new ArrayList<>(chatFunctionInfo.keySet());
        }
        List<ChatFunction> qualifiedFunctions = new ArrayList<>();
        if(qualifiedFunctionNameList == null || qualifiedFunctionNameList.isEmpty()){
            LOGGER.info("no qualified function found, completeChatMessageList: {}, sceneId: {}", JSON.toJSONString(completeChatMessageList), queryParams.getSceneDO().getId());
        }else{
            for (String functionName : qualifiedFunctionNameList) {
                Pair<ChatFunction, PluginDO> functionPluginDOPair = chatFunctionInfo.get(functionName);
                if (functionPluginDOPair == null ) {
                    LOGGER.error("no function with name: {}", functionName);
                    continue;
                }
                qualifiedFunctions.add(functionPluginDOPair.getKey());
            }
        }

        ChatCompletionRequest requestWithFunction = new ChatCompletionRequest();
        BeanUtil.copyProperties(chatCompletionRequest, requestWithFunction);


        if(functionCallConfig.getFunctionCallMultiRoundEnabled()){
            requestWithFunction.setMessages(copyMessages);
        }else{
            //function call仅支持单轮，所有要把历史消息删除掉，只保留system消息，和最终的prompt
            List<ChatMessage> cutMessageList = new ArrayList<>();
            String systemPrompt = AlgoBackendUtil.exactSystemPromptConfig(queryParams.getAlgoBackendDO());
            ChatMessage systemMessage = new ChatMessage();
            systemMessage.setRole(ChatRoleEnum.SYSTEM.getName());
            systemMessage.setContent(systemPrompt == null ? "": systemPrompt);
            cutMessageList.add(systemMessage);

            ChatMessage userMessage = copyMessages.get(copyMessages.size()-1);
            cutMessageList.add(userMessage);
            requestWithFunction.setMessages(cutMessageList);
        }

        requestWithFunction.setFunctions(qualifiedFunctions);

        appThreadPool.execute(()-> {
            try{
                List<String> functionNameList = qualifiedFunctions.stream().map(ChatFunction::getName).collect(Collectors.toList());
                LOGGER.info("function call start, function name list: {}", JSON.toJSONString(functionNameList));
                processFunctionCall(queryParams, qualifiedFunctions, requestWithFunction, chatFunctionInfo, 0);
            }catch (Throwable e){
                LOGGER.error("pluginMainWorkflow error", e);
                ToolLearningUtil.sendMessageToTbase(noneSerializationCacheManager,
                        queryParams.getUniqueAnswerId(),
                        null, -1,
                        e.getMessage(), AppConstants.ANSWER_STAGE, "最终答案", ResponseEnum.AI_CALL_ERROR.name());
            }
        });

        getChatDataFromTBase(queryParams, requestWithFunction, false);
    }

    /**
     * 提交表单后继续流式对话
     *
     * @param queryParams
     * @param pluginDOList
     */
    @Override
    public void continueStreamChat(PluginServiceRequestContext queryParams, List<PluginDO> pluginDOList) {

        ChatCompletionRequest chatCompletionRequest = queryParams.getChatCompletionRequest();

        Map<String, Pair<ChatFunction, PluginDO>> chatFunctionInfo = ToolLearningUtil.exactFunctionInfoFromPlugin(pluginDOList);
        List<ChatFunction> chatFunctionList = new ArrayList<>();
        for (Map.Entry<String, Pair<ChatFunction, PluginDO>> entry : chatFunctionInfo.entrySet()) {
            chatFunctionList.add(entry.getValue().getKey());
        }

        // 获取持久化的runtimeInfo
        ChatMessageDO latestChatMessageDO = queryParams.getChatMessageDOList().get(queryParams.getChatMessageDOList().size()-1);
        RuntimeInfo runtimeInfo = JSONObject.parseObject(latestChatMessageDO.getRuntimeInfo(), RuntimeInfo.class);
        List<ChatFunction> qualifiedFunctions = runtimeInfo.getChatFunctionList();
        List<ChatMessage> chatMessageList = runtimeInfo.getChatMessageList();
        Integer pluginCallIndex = runtimeInfo.getPluginCallIndex();

        ChatCompletionRequest requestWithFunction = new ChatCompletionRequest();
        BeanUtil.copyProperties(chatCompletionRequest, requestWithFunction);
        requestWithFunction.setFunctions(qualifiedFunctions);
        requestWithFunction.setMessages(chatMessageList);

        // 获取最后一个chatMessage, 把表单的data放到arguments里
        ChatMessage lastChatMessage = chatMessageList.get(chatMessageList.size()-1);
        lastChatMessage.getFunctionCall().setArguments(JSONObject.toJSONString(queryParams.getPluginParams()));
        // 获取要执行的插件
        Pair<ChatFunction, PluginDO> functionPluginDOPair = chatFunctionInfo.get(lastChatMessage.getFunctionCall().getName());
        PluginDO pluginDO = functionPluginDOPair.getValue();

        appThreadPool.execute(()-> {
            try{
                List<String> functionNameList = qualifiedFunctions.stream().map(ChatFunction::getName).collect(Collectors.toList());
                LOGGER.info("function call continue start, function name list: {}", JSON.toJSONString(functionNameList));
                // 执行未执行的Funciton
                if (continueExecuteFunction(requestWithFunction, queryParams,  pluginDO, lastChatMessage, pluginCallIndex)) {
                    // 继续function call
                    processFunctionCall(queryParams, qualifiedFunctions, requestWithFunction, chatFunctionInfo, pluginCallIndex+1);
                }
            }catch (Throwable e){
                LOGGER.error("pluginMainWorkflow error", e);
                NewPluginStreamPartResponse abnormalPluginStreamPartResponse = new NewPluginStreamPartResponse();
                abnormalPluginStreamPartResponse.setFinishReason(ResponseEnum.AI_CALL_ERROR.name());
                ToolLearningUtil.sendMessageToTbase(noneSerializationCacheManager,
                        queryParams.getUniqueAnswerId(),
                        abnormalPluginStreamPartResponse);
            }
        });

        getChatDataFromTBase(queryParams, requestWithFunction, true);
    }

    private boolean requestToolGpt(PluginServiceRequestContext queryParams,
                                   ChatCompletionRequest chatCompletionRequest,
                                   Map<String, Pair<ChatFunction, PluginDO>> chatFunctionInfo,
                                   Integer index){
        AtomicBoolean outputContent = new AtomicBoolean(false);
        String answerUid = queryParams.getAnswerUid();

        GptAlgModelServiceRequest gptAlgModelServiceRequest = new GptAlgModelServiceRequest(queryParams.getRequestId()
                , queryParams.getUserName()
                , queryParams.isStressTest()
                , queryParams.getAlgoBackendDO()
                , chatCompletionRequest
                , null
                , queryParams.isRegenerate());

        AtomicBoolean functionCallEnabled = new AtomicBoolean(false);
        AtomicInteger streamIndex = new AtomicInteger();
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = (chatStreamPartResponse)->{
            LOGGER.debug("tool model calling, get delta stream data: {}", JSON.toJSONString(chatStreamPartResponse));
            ChatStreamPartResponse.Choice choice = chatStreamPartResponse.getChoices().get(0);
            ChatMessage delta = choice.getDelta();
            String finishReason = choice.getFinishReason();
            //没有Function call信息的时候，需要输出到前端
            if(streamIndex.get() == 0){
                functionCallEnabled.set(isFunctionCall(chatStreamPartResponse));
            }

            if(!functionCallEnabled.get()){
                if (AppConstants.DEFAULT_STREAM_FINISH_REASON.equalsIgnoreCase(finishReason)){
                    finishReason = ResponseEnum.SUCCESS.name();
                }

                ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, delta.getContent(), finishReason,null);
            }

            streamIndex.addAndGet(1);
        };


        gptAlgModelServiceRequest.setChatStreamPartResponseHandler(chatStreamPartResponseConsumer);

        Consumer<StreamResponseModel> resultHandler = streamResponseModel -> {
            LOGGER.info("tool model call end, get result: {}", JSON.toJSONString(streamResponseModel));
            //判断流式数据中是否包含function call对象
            ChatMessage chatMessage = streamResponseModel.getAnswerMessage();
            if(chatMessage.getFunctionCall()!=null){
                //发送functionDecide数据
                Pair<ChatFunction, PluginDO> functionPluginDOPair = chatFunctionInfo.get(chatMessage.getFunctionCall().getName());
                if (functionPluginDOPair == null || functionPluginDOPair.getValue() == null) {
                    LOGGER.error("pluginDO is null, pluginName:{}", chatMessage.getFunctionCall().getName());
                    ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, "Tool learning异常。方法"+chatMessage.getFunctionCall().getName()+"不存在", ResponseEnum.FUNCTION_NOT_EXIST.name(),null);
                    outputContent.set(true);
                    return;
                }
                PluginDO pluginDO = functionPluginDOPair.getValue();

                //function call 和 content均不为空的情况下，说明碰到了参数缺失的情况，要根据处理策略进行不同的处理
                if(StringUtils.isNotBlank(chatMessage.getContent())){
                    FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(queryParams.getSceneDO());
                    if(LackParamStrategyEnum.AI_QUERY.equals(functionCallConfig.getLackParamStrategy())){

                        LOGGER.info("function call lack param, pick ai query strategy, content: {}, function call: {}", chatMessage.getContent(), chatMessage.getFunctionCall());
                        //把chatMessage存储到messageList，后续这条消息会落库到runtimeInfo字段
                        //由于这里直接就是发送answer阶段，流程结束了，开始落库。可以修改chatCompletionRequest的message列表，如果后续message列表还有其他用途，这里要记得修改
                        chatCompletionRequest.getMessages().add(chatMessage);

                        ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, chatMessage.getContent(), ResponseEnum.SUCCESS.name(),null);
                        outputContent.set(true);
                        return;
                    }
                }

                // 把模型决策结果加入到message中
                chatCompletionRequest.getMessages().add(chatMessage);

                //如果包含，则调用对应的插件
                //调用插件的时候要增加一个参数，说明这是在多插件场景进行调用
                if(queryParams.getChatCompletionRequest().getOnlyDecide()){
                    LOGGER.info("only decide, skip function execution");
                    //TODO: 这里把message整体塞到消息中，处理不太规范，后续需要优化
                    ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, chatMessage.getContent(), ResponseEnum.SUCCESS.name(),chatMessage.getFunctionCall());
                    outputContent.set(true);
                    return;
                }
                String response = executeFunction(queryParams, chatMessage.getFunctionCall(), pluginDO, index, false);

                // response为null，认为流程中断
                if (response == null) {
                    outputContent.set(true);
                    return;
                }

                //把插件调用结果加入到message中
                ChatMessage functionCallMessage = new ChatMessage();
                functionCallMessage.setRole(ChatRoleEnum.FUNCTION.getName());
                functionCallMessage.setName(chatMessage.getFunctionCall().getName());
                functionCallMessage.setContent(response);
                chatCompletionRequest.getMessages().add(functionCallMessage);
            }else{
                if (chatMessage.getContent() != null) {
                    ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, chatMessage.getContent(), ResponseEnum.SUCCESS.name(),null);
                    chatCompletionRequest.getMessages().add(chatMessage);
                }
                outputContent.set(true);
            }
        };

        gptAlgModelServiceRequest.setResultHandler(resultHandler);
        gptAlgModelServiceRequest.setUniqueAnswerId(queryParams.getUniqueAnswerId());

        AlgoModelUtilService.updateDefaultSystemPrompt(gptAlgModelServiceRequest.getChatCompletionRequest(), gptAlgModelServiceRequest.getAlgoBackendDO());
        algoModelUtilService.updateCheckSwitch(chatCompletionRequest, queryParams.getChatSessionDO(), gptAlgModelServiceRequest.getAlgoBackendDO().getModel());
        //流式传输，要把response设置为流式
        AlgoModelExecutor.getInstance().executorStreamChat(gptAlgModelServiceRequest.getAlgoBackendDO(), gptAlgModelServiceRequest);
        return outputContent.get();
    }

    /**
     * 判断是否是function call
     * @param firstStreamResponse
     * @return
     */
    private boolean isFunctionCall(ChatStreamPartResponse firstStreamResponse){
        LOGGER.debug("check is function call, firstStreamResponse: {}", JSON.toJSONString(firstStreamResponse));
        if(firstStreamResponse.getChoices().get(0).getDelta().getFunctionCall() != null
                && StringUtils.isNotBlank(firstStreamResponse.getChoices().get(0).getDelta().getFunctionCall().getName())){
            return true;
        }

        //codegpt的特殊判定逻辑，codegpt的function call内容是完全放到content字段中的，只不过是以特殊的token(#function)开头，#function后面是一个json，function call的信息在其中
        //另外需要特殊说明，一般function call为空，则content也为空，但是codegpt的function call为空的时候，content可能不为空，这种情况是缺少了参数。这么做的目的是为了方便后续的策略处理，既可以用content内容去追问，也可以用function call的内容弹出表单
        String firstContent = firstStreamResponse.getChoices().get(0).getDelta().getContent();
        if(firstContent!=null && firstContent.startsWith(AppConstants.DEFAULT_CODEGPT_FUNCTION_CALL_START_TOKEN)){
            return true;
        }

        return false;
    }

    /**
     * 从插件的参数配置中获取function的参数
     * @param pluginParamsList
     * @param needLLM 是否需要LLM决策，需要则只提取decideByLLM的参数，否则提出全部参数
     * @return
     */
    private JSONObject getFunctionParamsFromPlugin(List<Map<String, Object>> pluginParamsList, boolean needLLM){
        JSONObject functionParamsConfig = new JSONObject();
        functionParamsConfig.put("type", "object");
        List<String> requireFieldList = new ArrayList<>();
        Map<String, Object> properties = new HashMap<>();

        for(Map<String, Object> pluginParamConfig: pluginParamsList){
            //判断是否需要LLM决策，默认需要决策
            boolean decideByLLM = (boolean) pluginParamConfig.getOrDefault("decideByLLM", true);
            //排除掉不需要LLM决策的参数
            if(needLLM && !decideByLLM){
                continue;
            }

            String paramName = (String) pluginParamConfig.get("name");
            Map<String, Object> paramSchema = (Map<String, Object>) pluginParamConfig.get("schema");
            boolean paramRequired = (boolean) pluginParamConfig.get("required");

            if(paramRequired){
                requireFieldList.add(paramName);
            }

            Map<String, Object> propertyConfig = new HashMap<>(paramSchema);
            propertyConfig.put("description", pluginParamConfig.get("description"));
            Object defaultValue = pluginParamConfig.get("default");
            if(needLLM && defaultValue instanceof String && DEFAULT_PARAM_PATTERN.matcher((String) defaultValue).matches()){
               defaultValue = null;
            }

            if(defaultValue != null){
                propertyConfig.put("default", pluginParamConfig.get("default"));
            }

            properties.put(paramName, propertyConfig);
        }
        functionParamsConfig.put("properties", properties);
        functionParamsConfig.put("required", requireFieldList);
        return functionParamsConfig;
    }

    /**
     * 1、从tbase中获取数据
     * 2、分段送审
     * 3、flush数据给调用方
     * 4、写入gptCatch(如需要)
     *
     * @param params
     */
    private void getChatDataFromTBase(PluginServiceRequestContext params, ChatCompletionRequest requestWithFunction, boolean isContinue) {
        //数据包的顺序为：function call决策1 -> 插件调用结果1 -> function call决策2 -> 插件调用结果2 -> ... -> function call决策n -> 插件调用结果n -> 最终结果一系列数据表的输出
        //超时机制：相比单插件机制，多插件增加了决策步骤，决策步骤的包含模型推理，这个要超时的时间要单独设置
        //异常判定：tbase中获取到异常数据，这部分需要工作线程在异常的时候要把异常发到tbase
        //结束判定：index为-1，而且finishReason不为空
        Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer = params.getPluginStreamPartResponseConsumer();
        String uniqueAnswerId = params.getUniqueAnswerId();
        String key = String.format("%s%s", AppConstants.PLUGIN_STREAM_DATA_PREFIX, uniqueAnswerId);
        PluginLogGroup pluginLogGroup = new PluginLogGroup();
        StringBuilder answerContent = new StringBuilder();
        String answerUid = params.getAnswerUid();
        if (isContinue) {
            String pluginLog = params.getChatMessageDOList().get(params.getChatMessageDOList().size()-1).getPluginLog();
            pluginLogGroup = JSON.parseObject(pluginLog, PluginLogGroup.class);
            ToolLearningUtil.processPluginLogGroup(pluginLogGroup);
        }

        long waitStartTime = System.currentTimeMillis();
        while (true) {
            if (System.currentTimeMillis() - waitStartTime > codeGPTDrmConfig.getFunctionCallDefaultTimeOut()) {
                LOGGER.warn("get function call stream data timeout, uniqueAnswerId:{}, waitTime:{}ms,", uniqueAnswerId, System.currentTimeMillis() - waitStartTime);
                NewPluginStreamPartResponse abnormalPluginStreamPartResponse = new NewPluginStreamPartResponse();
                abnormalPluginStreamPartResponse.setFinishReason(ResponseEnum.ANSWER_OUTPUT_TIME_OUT.name());
                abnormalPluginStreamPartResponse.setContent(ResponseEnum.ANSWER_OUTPUT_TIME_OUT.getErrorMsg());
                if (pluginStreamPartResponseConsumer != null){
                    pluginStreamPartResponseConsumer.accept(abnormalPluginStreamPartResponse);
                }
                ToolLearningUtil.addStageInfoToPluginLog(pluginLogGroup, abnormalPluginStreamPartResponse);
                processFinish(abnormalPluginStreamPartResponse,
                        requestWithFunction,
                        answerContent,
                        pluginLogGroup,
                        key,
                        answerUid,
                        params.getPluginResultHandler(),
                        params.getNeedDelTBaseKey());
                return;
            }

            String streamStr = ChatUtils.safeTbaseLpop(noneSerializationCacheManager, key);
            if (streamStr == null) {
                try {
                    Thread.sleep(codeGPTDrmConfig.getPluginStreamDataPollingStep());
                } catch (InterruptedException e) {
                    LOGGER.warn("sleep failed", e);
                    continue;
                }
                continue;
            }

            waitStartTime = System.currentTimeMillis();
            LOGGER.debug("get function call stream data: {}", streamStr);

            NewPluginStreamPartResponse streamPartResponse = JSON.parseObject(streamStr, NewPluginStreamPartResponse.class);

            // 当监听到用户cancel操作时， 补全数据结构， 通知前端并将"用户主动取消"落库
            if(StringUtils.isNotBlank(streamPartResponse.getFinishReason())
                    && ResponseEnum.USER_CANCELED.name().equalsIgnoreCase(streamPartResponse.getFinishReason())) {
                LOGGER.info("user canceled, streamPartResponse: {}", JSON.toJSONString(streamPartResponse));
                streamPartResponse.setContent(ResponseEnum.USER_CANCELED.getErrorMsg());
                streamPartResponse.setFinishReason(ResponseEnum.USER_CANCELED.name());
                if (pluginStreamPartResponseConsumer != null){
                    pluginStreamPartResponseConsumer.accept(streamPartResponse);
                }
                ToolLearningUtil.addStageInfoToPluginLog(pluginLogGroup, streamPartResponse);
                processFinish(streamPartResponse,
                        requestWithFunction,
                        answerContent,
                        pluginLogGroup,
                        key,
                        answerUid,
                        params.getPluginResultHandler(),
                        params.getNeedDelTBaseKey());
                return;
            }

            if(StageTypeEnum.ANSWER.getName().equalsIgnoreCase(streamPartResponse.getType())){
                answerContent.append(streamPartResponse.getContent());
            }else{
                ToolLearningUtil.addStageInfoToPluginLog(pluginLogGroup, streamPartResponse);
            }

            if (pluginStreamPartResponseConsumer != null){
                pluginStreamPartResponseConsumer.accept(streamPartResponse);
            }

            if (ToolLearningUtil.isPluginCallPause(streamPartResponse)) {
                LOGGER.info("function call pause, pluginStreamPartResponse: {}", JSON.toJSONString(streamPartResponse));
                processPause(streamPartResponse,
                        requestWithFunction,
                        pluginLogGroup,
                        key,
                        answerUid,
                        params.getPluginResultHandler(),
                        params.getNeedDelTBaseKey());
                return;
            }

            if(ToolLearningUtil.isPluginCallEnd(streamPartResponse)){
                LOGGER.info("function call finished, streamPartResponse: {}", JSON.toJSONString(streamPartResponse));
                processFinish(streamPartResponse,
                        requestWithFunction,
                        answerContent,
                        pluginLogGroup,
                        key,
                        answerUid,
                        params.getPluginResultHandler(),
                        params.getNeedDelTBaseKey());
                return;
            }
        }
    }

    private void processFinish(NewPluginStreamPartResponse streamPartResponse,
                               ChatCompletionRequest requestWithFunction,
                               StringBuilder answerContent,
                               PluginLogGroup pluginLogGroup,
                               String key,
                               String answerUid,
                               Consumer<StreamResponseModel> pluginResultHandler,
                               Consumer<String> needDelTBaseKey) {
        LOGGER.info("function call finished, type:{}, finishReason:{}", streamPartResponse.getType(), streamPartResponse.getFinishReason());

        // 获取runtimeInfo
        RuntimeInfo runtimeInfo = new RuntimeInfo();
        runtimeInfo.setChatFunctionList(requestWithFunction.getFunctions());
        runtimeInfo.setChatMessageList(requestWithFunction.getMessages());
        runtimeInfo.setPluginCallIndex(streamPartResponse.getPluginIndex());

        String serviceAbnormalResp = null;
        String answer = null;

        //成功状态下，答案只可能只llm和summary阶段的输出
        if(ResponseEnum.SUCCESS.name().equalsIgnoreCase(streamPartResponse.getFinishReason())){
            answer = answerContent.toString();
        }else{
            //失败的情况处理
            serviceAbnormalResp = streamPartResponse.getFinishReason();
            if(ResponseEnum.USER_CANCELED.name().equalsIgnoreCase(serviceAbnormalResp)){
                answer = ResponseEnum.USER_CANCELED.getErrorMsg();
            }else{
                answer = "插件调用失败，请稍后重试："+streamPartResponse.getContent();
            }

            CheckResultModel checkResult = streamPartResponse.getCheckResultModel();
            if (checkResult!=null && !checkResult.isAllCheckRet()){
                answer = algoModelUtilService.getCheckFailedMsg(checkResult);
            }
        }

        StreamResponseModel streamResponseModel = new StreamResponseModel();
        streamResponseModel.setAnswerUid(answerUid);
        streamResponseModel.setPluginLogGroup(pluginLogGroup);
        ChatMessage chatMessage = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), answer);
        chatMessage.setFunctionCall(streamPartResponse.getChatFunctionCall());
        streamResponseModel.setAnswerMessage(chatMessage);
        streamResponseModel.setServiceAbnormalResp(serviceAbnormalResp);
        streamResponseModel.setRuntimeInfo(JSONObject.toJSONString(runtimeInfo));

        if (pluginResultHandler != null) {
            pluginResultHandler.accept(streamResponseModel);
        }
        if (needDelTBaseKey != null) {
            needDelTBaseKey.accept(key);
        }
    }

    private void processPause(NewPluginStreamPartResponse pluginStreamPartResponse,
                              ChatCompletionRequest requestWithFunction,
                              PluginLogGroup pluginLogGroup,
                              String key,
                              String answerUid,
                              Consumer<StreamResponseModel> pluginResultHandler,
                              Consumer<String> needDelTBaseKey) {
        LOGGER.info("function call paused, type:{}, finishReason:{}", pluginStreamPartResponse.getType(), pluginStreamPartResponse.getFinishReason());

        // 获取runtimeInfo
        RuntimeInfo runtimeInfo = new RuntimeInfo();
        runtimeInfo.setChatFunctionList(requestWithFunction.getFunctions());
        runtimeInfo.setChatMessageList(requestWithFunction.getMessages());
        runtimeInfo.setPluginCallIndex(pluginStreamPartResponse.getPluginIndex());

        StreamResponseModel streamResponseModel = new StreamResponseModel();
        streamResponseModel.setAnswerUid(answerUid);
        streamResponseModel.setPluginLogGroup(pluginLogGroup);
        streamResponseModel.setAnswerMessage(new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), null));
        streamResponseModel.setMessageStatus(ChatMessageStatusEnum.PAUSE.name());
        streamResponseModel.setRuntimeInfo(JSONObject.toJSONString(runtimeInfo));

        if (pluginResultHandler != null) {
            pluginResultHandler.accept(streamResponseModel);
        }
        if (needDelTBaseKey != null) {
            needDelTBaseKey.accept(key);
        }
    }

    /**
     * 判断函数调用是否走场景配置
     * @param queryParams
     * @param chatCompletionRequest
     * @param index
     * @return
     */
    private boolean isExecuteSceneConfig(PluginServiceRequestContext queryParams,
                                         ChatCompletionRequest chatCompletionRequest,
                                         Integer index) {
        // 获取配置
        FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(queryParams.getSceneDO());
        String answerUid = queryParams.getAnswerUid();

        // 如果是首轮，先校验是否配置了第一个function
        if (index == 0) {
            String firstFunctionName = functionCallConfig.getFirstFunctionName();
            if (StringUtils.isNotBlank(firstFunctionName)) {
                // 首次参数目前默认为空，在default里配置，后续从前端传参数进来
                queryParams.setNextFunctionCall(new ChatFunctionCall(firstFunctionName, JSONObject.toJSONString(queryParams.getPluginParams())));
                return true;
            }
        } else {
            // 非首轮，查看上一轮执行的function是否配置了next function
            // 获取上一轮执行的function
            List<ChatMessage> chatMessageList = chatCompletionRequest.getMessages();
            ChatMessage lastChatMessage = chatMessageList.get(chatMessageList.size()-1);
            String functionName = lastChatMessage.getName();
            Map<String,String> map = functionCallConfig.getNextFunctionName();
            if (ObjectUtils.isNotEmpty(map) && map.containsKey(functionName)) {
                JSONObject response = JSONObject.parseObject(lastChatMessage.getContent());
                if ((response.getInteger("status") != null && response.getInteger("status") == 999) || StringUtils.equals(map.get(functionName), "end")) {
                    // 如果上一轮执行失败，执行停止，输出错误信息
                    // 如果不想继续调用，可以设置为end
                    String answer = StringUtils.isNotEmpty(response.getString("answer")) ? response.getString("answer") : response.toJSONString();
                    ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager,
                            queryParams.getUniqueAnswerId(), answerUid, answer, ResponseEnum.SUCCESS.name(),null);
                    queryParams.setNextFunctionCall(null);
                } else {
                    // 参数默认是上一轮函数调用的response
                    queryParams.setNextFunctionCall(new ChatFunctionCall(map.get(functionName), lastChatMessage.getContent()));
                }
                return true;
            }
        }

        return false;
    }

    /**
     * 执行场景配置的function
     * @param queryParams
     * @param chatCompletionRequest
     * @param chatFunctionInfo
     * @param index
     * @return
     */
    private boolean executeSceneConfig(PluginServiceRequestContext queryParams,
                                       ChatCompletionRequest chatCompletionRequest,
                                       Map<String, Pair<ChatFunction, PluginDO>> chatFunctionInfo,
                                       Integer index) {
        String answerUid = queryParams.getAnswerUid();
        if (queryParams.getNextFunctionCall() != null) {
            // 组装ASSISTANT的chatMessage 并把插件决策结果放入message中
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setRole(ChatRoleEnum.ASSISTANT.getName());
            chatMessage.setFunctionCall(queryParams.getNextFunctionCall());
            chatMessage.setContent(null);
            chatCompletionRequest.getMessages().add(chatMessage);

            // 校验function是否存在
            Pair<ChatFunction, PluginDO> functionPluginDOPair = chatFunctionInfo.get(chatMessage.getFunctionCall().getName());
            if (functionPluginDOPair == null || functionPluginDOPair.getValue() == null) {
                LOGGER.error("pluginDO is null, pluginName:{}", chatMessage.getFunctionCall().getName());
                ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, "Tool learning异常。方法"+chatMessage.getFunctionCall().getName()+"不存在", ResponseEnum.FUNCTION_NOT_EXIST.name(),null);
                return false;
            }
            PluginDO pluginDO = functionPluginDOPair.getValue();

            if(queryParams.getChatCompletionRequest().getOnlyDecide()){
                LOGGER.info("only decide, skip function execution");
                //TODO: 这里把message整体塞到消息中，处理不太规范，后续需要优化
                ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, chatMessage.getContent(), ResponseEnum.SUCCESS.name(),chatMessage.getFunctionCall());
                return true;
            }
            // 调用插件
            String response = executeFunction(queryParams, chatMessage.getFunctionCall(), pluginDO, index, false);

            if (response == null) {
                return true;
            }

            // 组装FUNCTION的chatMessage
            ChatMessage functionCallMessage = new ChatMessage();
            functionCallMessage.setRole(ChatRoleEnum.FUNCTION.getName());
            functionCallMessage.setName(chatMessage.getFunctionCall().getName());
            functionCallMessage.setContent(response);

            //调用结果加入到message中
            chatCompletionRequest.getMessages().add(functionCallMessage);

            return false;
        } else {
            return true;
        }
    }

    /**
     *
     * @param chatCompletionRequest
     * @param queryParams
     * @param chatMessage List chatMessage最后一个，即function call的message
     * @param index
     */
    private boolean continueExecuteFunction(ChatCompletionRequest chatCompletionRequest, PluginServiceRequestContext queryParams, PluginDO pluginDO, ChatMessage chatMessage, Integer index) {
        // 执行function
        String response = executeFunction(queryParams, chatMessage.getFunctionCall(), pluginDO, index, true);

        // response为null，认为流程中断
        if (response == null) {
            return false;
        }

        //把插件调用结果加入到message中
        ChatMessage functionCallMessage = new ChatMessage();
        functionCallMessage.setRole(ChatRoleEnum.FUNCTION.getName());
        functionCallMessage.setName(chatMessage.getFunctionCall().getName());
        functionCallMessage.setContent(response);
        chatCompletionRequest.getMessages().add(functionCallMessage);

        return true;
    }

    /**
     * function call循环流程
     * @param queryParams
     * @param qualifiedFunctions
     * @param requestWithFunction
     * @param chatFunctionInfo
     * @param pluginCallIndex
     */
    private void processFunctionCall(PluginServiceRequestContext queryParams, List<ChatFunction> qualifiedFunctions, ChatCompletionRequest requestWithFunction, Map<String, Pair<ChatFunction, PluginDO>> chatFunctionInfo, Integer pluginCallIndex) {
        boolean outputContent = false;
        while (!outputContent){
            queryParams.setPluginIndex(pluginCallIndex);
            if(pluginCallIndex >= codeGPTDrmConfig.getMaxFunctionCallRound()){
                requestWithFunction.setFunctions(null);
            }else{
                //超过最大的function call次数，就把function call的信息传给模型
                requestWithFunction.setFunctions(qualifiedFunctions);
            }
            // 是否走场景配置的方法调用
            if (isExecuteSceneConfig(queryParams, requestWithFunction, pluginCallIndex)) {
                outputContent = executeSceneConfig(queryParams, requestWithFunction, chatFunctionInfo, pluginCallIndex);
            } else {
                outputContent = requestToolGpt(queryParams, requestWithFunction, chatFunctionInfo, pluginCallIndex);
            }

            pluginCallIndex++;
        }
    }

    private List<ChatMessage> getCompleteChatMessageList(PluginServiceRequestContext queryParams){
        if(queryParams.getChatMessageDOList()==null || queryParams.getChatMessageDOList().isEmpty()){
            return queryParams.getChatCompletionRequest().getMessages();
        }

        List<ChatMessageDO> chatMessageDOList = queryParams.getChatMessageDOList();
        ChatMessageDO latestChatMessage = chatMessageDOList.get(chatMessageDOList.size()-1);

        List<ChatMessage> completeChatMessageList = new ArrayList<>();
        if(StringUtils.isNotBlank(latestChatMessage.getRuntimeInfo())){
            RuntimeInfo runtimeInfo = JSONObject.parseObject(latestChatMessage.getRuntimeInfo(), RuntimeInfo.class);
            if(runtimeInfo.getChatMessageList()!=null){
                completeChatMessageList.addAll(runtimeInfo.getChatMessageList());
            }
        }

        //获取user message
        List<ChatMessage> messages = queryParams.getChatCompletionRequest().getMessages();
        completeChatMessageList.add(messages.get(messages.size()-1));

        return completeChatMessageList;
    }

}
