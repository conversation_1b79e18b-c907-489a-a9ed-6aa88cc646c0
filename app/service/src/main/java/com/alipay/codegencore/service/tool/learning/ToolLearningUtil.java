package com.alipay.codegencore.service.tool.learning;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.enums.ChatMessageStatusEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLog;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLogGroup;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageInfo;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageLog;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema.ModelStageOutput;
import com.alipay.codegencore.model.openai.ChatFunction;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import com.alipay.codegencore.model.response.PluginStreamPartResponse;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.utils.CommonTools;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zcache.serialize.BytesObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 工具学习工具类
 *
 * <AUTHOR>
 */
public class ToolLearningUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger( ToolLearningUtil.class );

    /**
     * fstring pattern "\\{([^\\}]+)\\}"
     */
    public static final Pattern FSTRING_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    public static final Pattern DEFAULT_PARAM_PATTERN = Pattern.compile("^(\\$)?\\{(\\w+)}$");

    /**
     * 向tbase写入阶段信息
     * @param noneSerializationCacheManager
     * @param uniqueAnswerId
     * @param finishReason
     * @param pluginIndex
     * @param type
     * @param stageInfo
     */
    public static void sendStageInfoToTbase(RefreshableCommonTbaseCacheManager noneSerializationCacheManager,
                                          String uniqueAnswerId,
                                          String anwerUid,
                                          Integer pluginIndex,
                                          Integer stageIndex,
                                          String type,
                                          StageInfo stageInfo,
                                          String finishReason) {
        NewPluginStreamPartResponse streamData = new NewPluginStreamPartResponse(anwerUid, finishReason, pluginIndex, stageIndex, type, stageInfo);
        String tbaseKey = String.format("%s%s", AppConstants.PLUGIN_STREAM_DATA_PREFIX, uniqueAnswerId);
        LOGGER.debug("sendMessageToTbase tbaseKey:{}, message: {}", tbaseKey, JSON.toJSONString(streamData));
        noneSerializationCacheManager.rpush(tbaseKey, new BytesObject(JSON.toJSONString(streamData).getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * 发送答案阶段信息
     * @param noneSerializationCacheManager
     * @param uniqueAnswerId
     * @param answerContent
     * @param finishReason
     */
    public static void sendAnswerToTbase(RefreshableCommonTbaseCacheManager noneSerializationCacheManager,
                                            String uniqueAnswerId,
                                            String answerUid,
                                            String answerContent,
                                            String finishReason,
                                            ChatFunctionCall functionCall) {
        NewPluginStreamPartResponse streamData = new NewPluginStreamPartResponse();
        streamData.setType(StageTypeEnum.ANSWER.getName());
        streamData.setId(answerUid);
        streamData.setContent(answerContent);
        streamData.setFinishReason(finishReason);
        streamData.setChatFunctionCall(functionCall);
        String tbaseKey = String.format("%s%s", AppConstants.PLUGIN_STREAM_DATA_PREFIX, uniqueAnswerId);
        LOGGER.debug("sendAnswerToTbase tbaseKey:{}, message: {}", tbaseKey, JSON.toJSONString(streamData));
        noneSerializationCacheManager.rpush(tbaseKey, new BytesObject(JSON.toJSONString(streamData).getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * 向tbase写入消息
     * @param uniqueAnswerId
     * @param content
     * @param stage
     * @param stageName
     * @param finishReason
     */
    public static void sendMessageToTbase(RefreshableCommonTbaseCacheManager noneSerializationCacheManager,
                                          String uniqueAnswerId,
                                          PluginInfo pluginInfo,
                                          Integer index,
                                          String content,
                                          String stage,
                                          String stageName,
                                          String finishReason) {
        sendMessageToTbase(noneSerializationCacheManager, uniqueAnswerId, pluginInfo, index, content, stage, stageName, finishReason, false, null, null);
    }

    /**
     * 向tbase写入消息
     * @param uniqueAnswerId
     * @param content
     * @param stage
     * @param stageName
     * @param finishReason
     */
    public static void sendMessageToTbase(RefreshableCommonTbaseCacheManager noneSerializationCacheManager,
                                          String uniqueAnswerId,
                                          PluginInfo pluginInfo,
                                          Integer index,
                                          String content,
                                          String stage,
                                          String stageName,
                                          String finishReason,
                                          boolean keyContent,
                                          CheckResultModel checkResultModel,
                                          List<String> stageList) {
        PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(uniqueAnswerId, pluginInfo, index, content, stage, stageName, finishReason,keyContent, checkResultModel, stageList);
        sendMessageToTbase(noneSerializationCacheManager, uniqueAnswerId, streamData);
    }

    /**
     * 向tbase写入消息
     * @param noneSerializationCacheManager
     * @param uniqueAnswerId
     * @param streamData
     */
    public static void sendMessageToTbase(RefreshableCommonTbaseCacheManager noneSerializationCacheManager,
                                          String uniqueAnswerId,
                                          Object streamData){
        String tbaseKey = String.format("%s%s", AppConstants.PLUGIN_STREAM_DATA_PREFIX, uniqueAnswerId);
        LOGGER.debug("sendMessageToTbase tbaseKey:{}, message: {}", tbaseKey, JSON.toJSONString(streamData));
        noneSerializationCacheManager.rpush(tbaseKey, new BytesObject(JSON.toJSONString(streamData).getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * 从promptTemplate中提取出所有的字段
     *
     * @param promptTemplate
     * @return
     */
    public static List<String> extractFieldsFromPromptTemplate(String promptTemplate) {
        List<String> fieldsList = new ArrayList<>();
        //[^\\}]匹配除了 } 之外的所有字符
        Matcher matcher = FSTRING_PATTERN.matcher(promptTemplate);
        while (matcher.find()) {
            fieldsList.add(matcher.group(1));
        }
        return fieldsList;
    }

    /**
     * 流式对话中途停止
     *
     * @param checkResultModel    审核结果
     * @param finishReason        结束原因,空则从审核结果中取
     * @param pluginResultHandler 落库参数
     * @param answerUid           答案落库的uid
     * @param answerDb            落库的答案
     * @param answerFlush         返回给调用方的答案
     */
    public static void stopStream(ResponseEnum finishReason,
                                  ChatFunctionCall functionCall,
                                  CheckResultModel checkResultModel,
                                  Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer,
                                  Consumer<StreamResponseModel> pluginResultHandler,
                                  String answerUid,
                                  String answerDb,
                                  String answerFlush,
                                  PluginLogGroup pluginLogGroup) {
        Pair<ChatStreamPartResponse, StreamResponseModel> stopStreamResult = ChatUtils.getStopStreamResponse(checkResultModel, finishReason, functionCall,answerUid, answerDb, answerFlush);

        StreamResponseModel streamResponseModel = stopStreamResult.getValue();
        streamResponseModel.setPluginLogGroup(pluginLogGroup);

        NewPluginStreamPartResponse newPluginStreamPartResponse = new NewPluginStreamPartResponse();
        newPluginStreamPartResponse.setFinishReason(finishReason.name());
        // 安全审核不通过，填充错误信息
        if(!checkResultModel.isAllCheckRet()){
            newPluginStreamPartResponse.setContent(finishReason.getErrorMsg());
        }
        if (pluginStreamPartResponseConsumer != null) {
            pluginStreamPartResponseConsumer.accept(newPluginStreamPartResponse);
        }

        if (pluginResultHandler != null) {
            pluginResultHandler.accept(streamResponseModel);
        }
    }


    /**
     * 将promptTemplate中的字段替换成对应的值
     *
     * @param template     模板
     * @param query        用户提问
     * @param params       参数
     * @param preResponse  前置响应
     * @param llmResult    大模型回复
     * @param postResponse 后置响应
     * @return 替换后的字符串
     */
    public static String applyPromptTemplate(String template, String query,
                                             Map<String, Object> params,
                                             Map<String, Object> preResponse,
                                             String llmResult,
                                             Map<String, Object> postResponse) {
        Matcher matcher = FSTRING_PATTERN.matcher(template);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1);
            Object value = getValueFromPluginContext(key, query, params, preResponse, llmResult, postResponse);

            if (value == null) {
                value = "";
            }
            matcher.appendReplacement(sb, Matcher.quoteReplacement(value.toString()));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 从插件执行上下文中获取值
     * @param key
     * @param query
     * @param params
     * @param preResponse
     * @param llmResult
     * @param postResponse
     * @return
     */
    public static Object getValueFromPluginContext(String key,
                                                   String query,
                                                   Map<String, Object> params,
                                                   Map<String, Object> preResponse,
                                                   String llmResult,
                                                   Map<String, Object> postResponse) {
        Object value;
        if (key.startsWith("params.")) {
            value = params.get(key.substring(7));
        } else if (key.startsWith("preResponse.")) {
            value = preResponse.get(key.substring(12));
        } else if (key.startsWith("postResponse.")) {
            value = postResponse.get(key.substring(13));
        } else if ("query".equals(key)) {
            value = query;
        } else if ("llmResult".equals(key)) {
            value = llmResult;
        } else if ("param".equals(key)) {
            value = JSON.toJSONString(params);
        } else if ("preResponse".equals(key)) {
            value = JSON.toJSONString(preResponse);
        } else if ("postResponse".equals(key)) {
            value = JSON.toJSONString(postResponse);
        } else {
            value = null;
        }
        return value;
    }

    /**
     * 从多轮提问中提取简单的query，即获取最后一个角色为user的消息内容
     * @param chatMessageList
     * @return
     */
    public static String getSimpleQuery(List<ChatMessage> chatMessageList){
        int messageSize = chatMessageList.size();
        return chatMessageList.get(messageSize-1).getContent();
    }

    /**
     * 根据输入输出构造StageInfo
     * @param stageType 阶段类型
     * @param stageInfoType 阶段信息类型，表示输入或者输出
     * @param infoFieldName 字段名
     * @param stageInfoData 字段数据
     */
    public static StageInfo buildStageInfo(StageTypeEnum stageType, StageInfo.StageInfoTypeEnum stageInfoType, String infoFieldName, Object stageInfoData){
        try {
            return new StageInfo(stageType,stageInfoType,infoFieldName,stageInfoData);
        } catch (Exception e) {
            LOGGER.info("build stage info error",e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 在StageInfo增加信息
     * @param stageType 阶段类型
     * @param stageInfoType 阶段信息类型，表示输入或者输出
     * @param infoFieldName 字段名
     * @param stageInfoData 字段数据
     */
    public static void addStageInfo(StageInfo stageInfo, StageTypeEnum stageType, StageInfo.StageInfoTypeEnum stageInfoType, String infoFieldName, Object stageInfoData){
        try {
            stageInfo.addInfo(stageType,stageInfoType,infoFieldName,stageInfoData);
        } catch (Exception e) {
            LOGGER.info("build stage info error",e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 把流式数据转化成pluginLog
     * @param pluginLogGroup
     * @param pluginStreamPartResponse
     */
    public static void addStageInfoToPluginLog(PluginLogGroup pluginLogGroup, NewPluginStreamPartResponse pluginStreamPartResponse){
        Integer pluginIndex = pluginStreamPartResponse.getPluginIndex();
        Integer stageIndex = pluginStreamPartResponse.getStageIndex();
        StageInfo stageInfo = pluginStreamPartResponse.getStageInfo();
        String finishReason = pluginStreamPartResponse.getFinishReason();
        StageTypeEnum type = StageTypeEnum.fromString(pluginStreamPartResponse.getType());

        if(type == null){
            //TODO: 简单处理，忽略一个阶段正常结束，下一个阶段未开始的情况的特殊处理
            if(StringUtils.isNotBlank(finishReason)){
                if(!pluginLogGroup.getPluginLogList().isEmpty()){
                    PluginLog latestPluginLog = pluginLogGroup.getPluginLogList().get(pluginLogGroup.getPluginLogList().size()-1);
                    latestPluginLog.setStatus(ResponseEnum.SUCCESS.name().equals(finishReason));

                    if(!latestPluginLog.getStageLogList().isEmpty()){
                        StageLog latestStageLog = latestPluginLog.getStageLogList().get(latestPluginLog.getStageLogList().size()-1);
                        latestStageLog.setStatus(ResponseEnum.SUCCESS.name().equals(finishReason));
                        latestStageLog.setFinishReason(finishReason);
                    }
                }
            }else{
                throw new RuntimeException("stage type finishReason both null, fatal error");
            }
            return;
        }


        if(pluginIndex == null){
            //pluginIndex为空一般为answer阶段，该阶段的数据不会存储插件信息中
            return;
        }

        PluginLog latestPluginLog = null;

        //pluginIndex超过插件信息中的插件数量，说明是新的插件
        //而且这个阶段是functionCall类型的数据包
        if(pluginIndex == pluginLogGroup.getPluginLogList().size()){
            if(!StageTypeEnum.FUNCTION_CALL.equals(type)){
                LOGGER.error("新查询的第一条消息不是functionCall类型，无法解析, pluginStreamResponse: {}", JSON.toJSONString(pluginStreamPartResponse));
                return;
            }
            latestPluginLog = new PluginLog();
            latestPluginLog.setPluginInfo(pluginStreamPartResponse.getPluginInfo());
            latestPluginLog.setStatus(true);
            pluginLogGroup.getPluginLogList().add(latestPluginLog);
        }else{
            latestPluginLog = pluginLogGroup.getPluginLogList().get(pluginLogGroup.getPluginLogList().size()-1);
        }

        StageLog latestStageLog = null;
        if(!latestPluginLog.getStageLogList().isEmpty()){
            latestStageLog = latestPluginLog.getStageLogList().get(latestPluginLog.getStageLogList().size()-1);
        }

        //pluginLog中没有阶段信息，或者stage为空（functionCall阶段）或者stageIndex已经超出最后一个阶段
        if(StageTypeEnum.FUNCTION_CALL.equals(type) || stageIndex == latestPluginLog.getStageLogList().size()-1){
            latestStageLog = new StageLog();
            latestStageLog.setType(type.getName());
            String stageName = StageTypeEnum.FUNCTION_CALL.equals(type)? "决策": String.format("步骤%s", CommonTools.number2Chinese(stageIndex + 1));
            latestStageLog.setStageName(stageName);
            latestPluginLog.getStageLogList().add(latestStageLog);
        }

        StageInfo existingStageInfo = latestStageLog.getStageInfo();

        if(stageInfo!=null && stageInfo.getInput()!=null){
            Class<?> inputSchema = type.getInputSchema();
            Object existingInputInfo = existingStageInfo.getInput();
            Object newInputInfo = stageInfo.getInput();
            existingStageInfo.setInput(mergeStageInfo(inputSchema,existingInputInfo,newInputInfo));
        }

        if(stageInfo!=null &&stageInfo.getOutput()!=null){
            Class<?> outputSchema = type.getOutputSchema();
            Object existingOutputInfo = existingStageInfo.getOutput();
            Object newOutputInfo = stageInfo.getOutput();
            existingStageInfo.setOutput(mergeStageInfo(outputSchema,existingOutputInfo,newOutputInfo));
        }

        if(finishReason!=null){
            latestStageLog.setStatus(ResponseEnum.SUCCESS.name().equalsIgnoreCase(finishReason));
            latestStageLog.setFinishReason(finishReason);

            latestPluginLog.setStatus(ResponseEnum.SUCCESS.name().equalsIgnoreCase(finishReason));
        }
    }

    /**
     * 合并stageInfo
     * @param stageInfoSchema stageInfo的类型定义
     * @param existingStageInfo 已有的stageInfo，类型为stageInfoSchema
     * @param newStageInfo 新的stageInfo，类型为stageInfoSchema
     */
    public static Object mergeStageInfo(Class<?> stageInfoSchema, Object existingStageInfo, Object newStageInfo){
        if (stageInfoSchema == null ) {
            LOGGER.info("schema can not be null");
            return null;
        }

        if(newStageInfo == null){
            return existingStageInfo;
        }

        if(existingStageInfo == null){
            try {
                existingStageInfo = stageInfoSchema.getConstructor().newInstance();
            } catch (Exception e) {
                LOGGER.info("create new stage info error, schema: {}", stageInfoSchema,e);
                return null;
            }
        }

        if(!stageInfoSchema.isInstance(newStageInfo)){
            newStageInfo = JSON.parseObject(JSON.toJSONString(newStageInfo), stageInfoSchema);
        }

        if (!stageInfoSchema.isInstance(existingStageInfo)) {
            throw new IllegalArgumentException("existingStageInfo and newStageInfo must be instance of stageInfoSchema");
        }

        //特殊处理：如果是model阶段，需要把llmResult累加
        if(stageInfoSchema.equals(ModelStageOutput.class)){
            ModelStageOutput existingModelStageOutput = (ModelStageOutput) existingStageInfo;
            ModelStageOutput newModelStageOutput = (ModelStageOutput) newStageInfo;
            if(existingModelStageOutput.getLlmResult() == null){
                existingModelStageOutput.setLlmResult(newModelStageOutput.getLlmResult());
            } else if(newModelStageOutput.getLlmResult() != null){
                existingModelStageOutput.setLlmResult(existingModelStageOutput.getLlmResult() + newModelStageOutput.getLlmResult());
            }
        }else{
            CopyOptions copyOptions = CopyOptions.create().setIgnoreNullValue(true);
            BeanUtil.copyProperties(newStageInfo, existingStageInfo, copyOptions);
        }

        return existingStageInfo;
    }

    /**
     * 判断插件调用是否结束
     * @param pluginStreamPartResponse
     * @return
     */
    public static boolean isPluginCallEnd(NewPluginStreamPartResponse pluginStreamPartResponse) {
        CheckResultModel checkResult = pluginStreamPartResponse.getCheckResultModel();
        if (checkResult!=null && !checkResult.isAllCheckRet()){
            return true;
        }

        if(StageTypeEnum.ANSWER.getName().equalsIgnoreCase(pluginStreamPartResponse.getType()) && pluginStreamPartResponse.getFinishReason()!=null){
            return true;
        }

        if(!StageTypeEnum.ANSWER.getName().equalsIgnoreCase(pluginStreamPartResponse.getType())
                && pluginStreamPartResponse.getFinishReason() != null
                && !ResponseEnum.SUCCESS.name().equalsIgnoreCase(pluginStreamPartResponse.getFinishReason())){
            return true;
        }
        return false;
    }

    /**
     * 判断插件调用是否暂停
     * @param pluginStreamPartResponse
     * @return
     */
    public static boolean isPluginCallPause(NewPluginStreamPartResponse pluginStreamPartResponse) {
        if (pluginStreamPartResponse.getFinishReason() != null && pluginStreamPartResponse.getFinishReason().equalsIgnoreCase(ChatMessageStatusEnum.PAUSE.name())) {
            return true;
        }
        return false;
    }

    /**
     * get function list from plugin
     * @param pluginDOList plugin list
     * @return function list
     */
    public static Map<String, Pair<ChatFunction, PluginDO>> exactFunctionInfoFromPlugin(List<PluginDO> pluginDOList) {
        Map<String, Pair<ChatFunction, PluginDO>> chatFunctionInfo = new HashMap<>();
        for(PluginDO pluginDO: pluginDOList){
            ChatFunction chatFunction = new ChatFunction();

            String configYaml = pluginDO.getWorkflowConfig();
            Map<String, Object> workFlowConfig = parseWorkFlowYaml(configYaml);

            Map<String, Object> pluginInfo = (Map<String, Object>) workFlowConfig.get("info");

            //工具的名称和描述优先从yaml中获取
            chatFunction.setName(pluginInfo.getOrDefault("name", pluginDO.getName()).toString());
            chatFunction.setDescription(pluginInfo.getOrDefault("description", pluginDO.getDescription()).toString());

            List<Map<String, Object>> paramSchemaList = (List<Map<String, Object>>) workFlowConfig.get("params");
            JSONObject functionParamConfig = getFunctionParamsFromPlugin(paramSchemaList, true);
            chatFunction.setParameters(functionParamConfig);

            Pair<ChatFunction, PluginDO> chatFunctionPluginDOPair = new Pair<>(chatFunction, pluginDO);
            chatFunctionInfo.put(chatFunction.getName(), chatFunctionPluginDOPair);
        }
        return chatFunctionInfo;
    }

    /**
     * get chatFunction from single plugin
     * @param pluginDO
     * @param needLLM 是否需要LLM决策，需要则只提取decideByLLM的参数，否则提出全部参数
     * @return
     */
    public static ChatFunction exactFunctionInfoFromPlugin(PluginDO pluginDO, boolean needLLM) {
        ChatFunction chatFunction = new ChatFunction();

        String configYaml = pluginDO.getWorkflowConfig();
        Map<String, Object> workFlowConfig = parseWorkFlowYaml(configYaml);

        Map<String, Object> pluginInfo = (Map<String, Object>) workFlowConfig.get("info");

        //工具的名称和描述优先从yaml中获取
        String name = pluginInfo.getOrDefault("name", pluginDO.getName()).toString();
        chatFunction.setName(name);
        String description = pluginInfo.getOrDefault("description", pluginDO.getDescription()).toString();
        chatFunction.setDescription(description);

        List<Map<String, Object>> paramSchemaList = (List<Map<String, Object>>) workFlowConfig.get("params");
        JSONObject functionParamConfig = getFunctionParamsFromPlugin(paramSchemaList, needLLM);
        JSONObject functionParamConfigDisplay = getFunctionParamsFromPluginDisplay(paramSchemaList, needLLM);
        functionParamConfig.put("name",name);
        functionParamConfigDisplay.put("name",name);
        if (pluginInfo.get("title") != null) {
            functionParamConfig.put("title", pluginInfo.get("title"));
            functionParamConfigDisplay.put("title", pluginInfo.get("title"));
        }
        else {
            functionParamConfig.put("title", description);
            functionParamConfigDisplay.put("title", description);
        }

        chatFunction.setParameters(functionParamConfig);
        chatFunction.setFunctionParamConfigDisplay(functionParamConfigDisplay);

        return chatFunction;
    }

    /**
     * 获取对话级表单的uiSchema
     * @param pluginDO
     * @return
     */
    public static String exactMessageFormUiSchemaFromPlugin(PluginDO pluginDO, String defaultUiSchema) {
        JSONObject uiSchemaJson = new JSONObject();
        // 配置默认的uiSchema
        if (StringUtils.isNotEmpty(defaultUiSchema)) {
            uiSchemaJson = JSONObject.parseObject(defaultUiSchema);
        }
        Map<String, Object> workFlowConfig = parseWorkFlowYaml(pluginDO.getWorkflowConfig());
        List<Map<String, Object>> paramSchemaList = (List<Map<String, Object>>) workFlowConfig.get("params");
        for(Map<String, Object> pluginParamConfig: paramSchemaList){
            JSONObject paramUiSchema = new JSONObject();
            String paramName = (String) pluginParamConfig.get("name");
            if (pluginParamConfig.get("formReadOnly") != null && (boolean) pluginParamConfig.get("formReadOnly") == true){
                paramUiSchema.put("ui:readonly", true);
            }
            if (pluginParamConfig.get("formHidden") != null && (boolean) pluginParamConfig.get("formHidden") == true) {
                paramUiSchema.put("ui:widget", "hidden");
            }
            if (pluginParamConfig.get("formSchema") != null ) {
                JSONObject formSchema = JSONObject.parseObject(JSONObject.toJSONString(pluginParamConfig.get("formSchema")));
                if (formSchema.get("visual") != null ){
                    JSONObject ui = JSONObject.parseObject(JSONObject.toJSONString(formSchema.get("visual")));
                    paramUiSchema.put(ui.getString("name"),ui.get("value"));
                }
                if (formSchema.get("options") != null){
                    JSONObject options = JSONObject.parseObject(JSONObject.toJSONString(formSchema.get("options")));
                    if (options.get("searchUrl") != null){
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("searchUrl", options.get("searchUrl"));
                        if (options.get("manualSearch") != null){
                            jsonObject.put("manualSearch",options.get("manualSearch"));
                        }
                        if (options.get("mode") != null){
                            jsonObject.put("mode",options.get("mode"));
                        }
                        Object placeholder = pluginParamConfig.get("placeholder");
                        if (placeholder != null) {
                            jsonObject.put("placeholder", placeholder);
                        }
                        paramUiSchema.put("ui:options",jsonObject);
                    }
                }
            }
            if (!paramUiSchema.isEmpty()) {
                uiSchemaJson.put(paramName, paramUiSchema);
            }
        }
        return uiSchemaJson.toJSONString();
    }

    /**
     * 从插件的参数配置中获取function的参数 获取发送给模型的schema
     * @param pluginParamsList
     * @param needLLM 是否需要LLM决策，需要则只提取decideByLLM的参数，否则提出全部参数
     * @return
     */
    public static JSONObject getFunctionParamsFromPlugin(List<Map<String, Object>> pluginParamsList, boolean needLLM){
        JSONObject functionParamsConfig = new JSONObject();
        functionParamsConfig.put("type", "object");
        List<String> requireFieldList = new ArrayList<>();
        Map<String, Object> properties = new LinkedHashMap<>(10);

        for(Map<String, Object> pluginParamConfig: pluginParamsList){
            //判断是否需要LLM决策，默认需要决策
            boolean decideByLLM = (boolean) pluginParamConfig.getOrDefault("decideByLLM", true);
            //排除掉不需要LLM决策的参数
            if(needLLM && !decideByLLM){
                continue;
            }

            String paramName = (String) pluginParamConfig.get("name");
            Map<String, Object> paramSchema = (Map<String, Object>) pluginParamConfig.get("schema");
            boolean paramRequired = (boolean) pluginParamConfig.get("required");

            if(paramRequired){
                requireFieldList.add(paramName);
            }

            Map<String, Object> propertyConfig = new HashMap<>(paramSchema);
            propertyConfig.put("description", pluginParamConfig.get("description"));
            Object defaultValue = pluginParamConfig.get("default");
            if(needLLM && defaultValue instanceof String && DEFAULT_PARAM_PATTERN.matcher((String) defaultValue).matches()){
                defaultValue = null;
            }
            if(defaultValue != null){
                propertyConfig.put("default", defaultValue);
            }
            Object items = pluginParamConfig.get("items");
            if (items != null) {
                propertyConfig.put("items", items);
            }
            Object enumValue = pluginParamConfig.get("enum");
            if (enumValue != null) {
                propertyConfig.put("enum", enumValue);
            }
            properties.put(paramName, propertyConfig);
        }
        functionParamsConfig.put("properties", properties);
        functionParamsConfig.put("required", requireFieldList);
        return functionParamsConfig;
    }

    /**
     * 从插件的参数配置中获取function的参数 获取前端展示的jsonSchema
     * @param pluginParamsList
     * @param needLLM 是否需要LLM决策，需要则只提取decideByLLM的参数，否则提出全部参数
     * @return
     */
    public static JSONObject getFunctionParamsFromPluginDisplay(List<Map<String, Object>> pluginParamsList, boolean needLLM){
        JSONObject functionParamsConfig = new JSONObject();
        functionParamsConfig.put("type", "object");
        List<String> requireFieldList = new ArrayList<>();
        Map<String, Object> properties = new LinkedHashMap<>(10);

        for(Map<String, Object> pluginParamConfig: pluginParamsList){
            //判断是否需要LLM决策，默认需要决策
            boolean decideByLLM = (boolean) pluginParamConfig.getOrDefault("decideByLLM", true);
            //排除掉不需要LLM决策的参数
            if(needLLM && !decideByLLM){
                continue;
            }

            String paramName = (String) pluginParamConfig.get("name");
            Map<String, Object> paramSchema = (Map<String, Object>) pluginParamConfig.get("schema");
            boolean paramRequired = (boolean) pluginParamConfig.get("required");

            if(paramRequired){
                requireFieldList.add(paramName);
            }

            Map<String, Object> propertyConfig = new HashMap<>(paramSchema);
            Object defaultValue = pluginParamConfig.get("default");

            if(needLLM && defaultValue instanceof String && DEFAULT_PARAM_PATTERN.matcher((String) defaultValue).matches()){
                defaultValue = null;
            }

            if (pluginParamConfig.get("displayDescription") != null){
                propertyConfig.put("description", pluginParamConfig.get("description"));
            }
            if(defaultValue != null){
                propertyConfig.put("default", defaultValue);
            }
            Object defaultUrl = pluginParamConfig.get("defaultUrl");
            if(defaultUrl != null){
                propertyConfig.put("defaultUrl", defaultUrl);
            }
            Object enumValue = pluginParamConfig.get("enum");
            if (enumValue != null) {
                propertyConfig.put("enum", enumValue);
            }
            Object enumNames = pluginParamConfig.get("enumNames");
            if (enumNames != null) {
                propertyConfig.put("enumNames", enumNames);
            }
            Object items = pluginParamConfig.get("items");
            if (items != null) {
                propertyConfig.put("items", items);
            }
            Object maxItems = pluginParamConfig.get("maxItems");
            if (maxItems != null) {
                propertyConfig.put("maxItems", maxItems);
            }
            Object minItems = pluginParamConfig.get("minItems");
            if (minItems != null) {
                propertyConfig.put("minItems", minItems);
            }
            Object title = pluginParamConfig.get("title");
            if (title != null) {
                propertyConfig.put("title", title);
            }

            properties.put(paramName, propertyConfig);
        }
        functionParamsConfig.put("properties", properties);
        functionParamsConfig.put("required", requireFieldList);
        return functionParamsConfig;
    }

    /**
     * 解析工作流配置文件
     * @param yamlText yaml文本
     * @return
     */
    public static Map<String, Object> parseWorkFlowYaml(String yamlText) {
        Yaml yaml = new Yaml();
        try {
            return yaml.load(yamlText);
        } catch (Exception e) {
            LOGGER.error("解析工作流配置文件失败", e);
            return null;
        }
    }

    /**
     * 处理插件日志信息，添加INPUT和OUTPUT类型
     * @param pluginLogGroup
     * @return
     */
    public static void processPluginLogGroup(PluginLogGroup pluginLogGroup) {
        List<PluginLog> pluginLogList = pluginLogGroup.getPluginLogList();
        for (PluginLog pluginLog : pluginLogList) {
            for (StageLog stageLog : pluginLog.getStageLogList()) {
                Class<?> inputSchema = StageTypeEnum.fromString(stageLog.getType()).getInputSchema();
                Class<?> outputSchema = StageTypeEnum.fromString(stageLog.getType()).getOutputSchema();
                StageInfo stageInfo = stageLog.getStageInfo();
                if (stageInfo.getInput() != null) {
                    stageInfo.setInput(JSON.parseObject(JSON.toJSONString(stageInfo.getInput()), inputSchema));
                }
                if (stageInfo.getOutput() != null) {
                    stageInfo.setOutput(JSON.parseObject(JSON.toJSONString(stageInfo.getOutput()), outputSchema));
                }
            }
        }
    }
}
