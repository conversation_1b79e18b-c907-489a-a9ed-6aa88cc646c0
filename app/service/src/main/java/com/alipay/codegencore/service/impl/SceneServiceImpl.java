package com.alipay.codegencore.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.SceneDOExample;
import com.alipay.codegencore.dal.example.SceneDOExample.Criteria;
import com.alipay.codegencore.dal.mapper.PluginDOMapper;
import com.alipay.codegencore.dal.mapper.SceneDOMapper;
import com.alipay.codegencore.dal.mapper.SceneManualMapper;
import com.alipay.codegencore.model.domain.*;
import com.alipay.codegencore.model.enums.*;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.PluginCommand;
import com.alipay.codegencore.model.model.tool.learning.FunctionCallConfig;
import com.alipay.codegencore.model.model.yuque.YuQueGroupResponseModel;
import com.alipay.codegencore.model.model.yuque.YuQueTokenInfoModel;
import com.alipay.codegencore.model.openai.UserSaveSceneVO;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.UserSceneRecordsService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.PostBuilder;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.http.HttpResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 助手表Service
 *
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.service.impl.model
 * @CreateTime : 2023-07-11
 */
@Service
public class SceneServiceImpl implements SceneService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SceneServiceImpl.class);

    @Resource
    private SceneDOMapper     sceneDOMapper;
    @Resource
    private SceneManualMapper sceneManualMapper;

    @Resource
    private UserAclService userAclService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private UserSceneRecordsService userSceneRecordsService;

    @Resource
    private ConfigService configService;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private YuQueDocUtilService yuQueDocUtilService;

    @Resource
    private DocumentHandleService documentHandleService;

    @Resource
    private SecAgentHelper secAgentHelper;

    @Resource
    private PluginDOMapper pluginDOMapper;

    @Override
    public SceneDO getSceneByBizId(String bizId) {
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andBizIdEqualTo(bizId);
        List<SceneDO> sceneDOS = sceneDOMapper.selectByExample(sceneDOExample);
        if (CollectionUtils.isEmpty(sceneDOS)) {
            return null;
        }
        return sceneDOS.get(0);
    }

    /**
     * 通过助手Id查找助手
     *
     * @param sceneId 助手id
     * @return
     */
    @Override
    public SceneDO getSceneById(Long sceneId) {
        return sceneDOMapper.selectByPrimaryKey(sceneId);
    }

    /**
     * 根据id获取用户可用的助手
     * @param sceneId
     * @return
     */
    @Override
    public UserSaveSceneVO getUserAvailableSceneById(Long sceneId,boolean onlyBaseInfo) {
        SceneDO scene = sceneDOMapper.selectByPrimaryKey(sceneId);
        if (scene == null) {
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        FunctionCallConfig functionCallConfig = this.getFunctionCallConfig(scene);
        UserAuthDO currentUser = userAclService.getCurrentUser();
//        if(!onlyBaseInfo && !checkUserEditAuth(scene,currentUser)){
//            throw new BizException(ResponseEnum.NO_AUTH,"你没有当前助手的编辑权限");
//        }
        UserSaveSceneVO userSaveSceneVO = new UserSaveSceneVO();
        BeanUtil.copyProperties(scene, userSaveSceneVO);
        userSaveSceneVO.setCallFunctionEveryRound(functionCallConfig.getCallFunctionEveryRound());
        List<Long> scenes = JSONArray.parseArray(currentUser.getSaveScenes(), Long.class);
        if(StringUtils.isNotBlank(scene.getPluginCommand())){
            userSaveSceneVO.setPluginCommandList(JSONArray.parseArray(scene.getPluginCommand(), PluginCommand.class));
            // 补偿插件名称
            if(CollectionUtils.isNotEmpty(userSaveSceneVO.getPluginCommandList())){
                for (PluginCommand pluginCommand : userSaveSceneVO.getPluginCommandList()) {
                    if(StringUtils.isBlank(pluginCommand.getPluginName())){
                        PluginDO pluginDO = pluginDOMapper.selectByPrimaryKey(pluginCommand.getPluginId());
                        pluginCommand.setPluginName(pluginDO.getName());
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(scenes) && scenes.contains(sceneId)) {
            userSaveSceneVO.setUserSaveScene(true);
        }
        // 加入默认助手标识
        if (userSaveSceneVO.getId().longValue() == codeGPTDrmConfig.getSceneDefaultId().longValue()) {
            userSaveSceneVO.setDefaultScene(true);
        }
        // 助手已删除
        if (scene.getDeleted() == 1) {
            userSaveSceneVO.setSceneAvailableStatus(SceneAvailableStatus.DELETE);
        }
        else if (scene.getEnable() == 0) {
            userSaveSceneVO.setSceneAvailableStatus(SceneAvailableStatus.DISABLE);
        }
        else if (!this.availableSceneUser(sceneId, currentUser.getId())) {
            userSaveSceneVO.setSceneAvailableStatus(SceneAvailableStatus.NO_AUTH);
        }
        else {
            userSaveSceneVO.setSceneAvailableStatus(SceneAvailableStatus.NORMAL);
        }
        if(onlyBaseInfo){
            userSaveSceneVO.setYuqueTokenList("[]");
            userSaveSceneVO.setDocumentUidList("");
        }
        if (StringUtils.isNotBlank(userSaveSceneVO.getDocumentUidList())) {
            List<String> documentUidList = JSONArray.parseArray(userSaveSceneVO.getDocumentUidList(), String.class);
            List<DocumentDO> documentDOList = documentHandleService.getDocumentByUidList(documentUidList);
            userSaveSceneVO.setDocumentList(documentDOList);
        }
        return userSaveSceneVO;
    }

    /**
     * 获取所有助手 包括未启用助手
     *
     * @return
     */
    @Override
    public List<SceneDO> getAllScene() {
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andDeletedEqualTo(0);
        return sceneDOMapper.selectByExample(sceneDOExample);
    }

    /**
     * 用户创建新助手
     *
     * @param sceneDO
     * @return
     */
    @Override
    public Long addScene(SceneDO sceneDO) {
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andNameEqualTo(sceneDO.getName());
        List<SceneDO> sceneDOS = sceneDOMapper.selectByExample(sceneDOExample);
        if (CollectionUtil.isNotEmpty(sceneDOS)) {
            throw new BizException(ResponseEnum.SCENE_REPEAT);
        }
        //默认全部环境可见,未启用状态
        sceneDO.setVisableEnv(VisableEnvEnum.ALL.getCode());
        sceneDO.setEnable(0);
        sceneDOMapper.insertSelective(sceneDO);
        // 向 agent安全网关 同步agent信息
        secAgentHelper.agentInit(sceneDO);
        return sceneDO.getId();
    }

    /**
     * 更新助手信息
     *
     * @param sceneDO
     * @return
     */
    @Override
    public Boolean updateSceneById(SceneDO sceneDO) {
        //默认全部环境可见
        sceneDO.setVisableEnv(VisableEnvEnum.ALL.getCode());
        UserAuthDO currentUser = userAclService.getCurrentUser();
        SceneDOExample sceneDOExample = new SceneDOExample();
        Criteria criteria = sceneDOExample.createCriteria();
        criteria.andIdEqualTo(sceneDO.getId());
        int update = 0;
        List<SceneDO> sceneDOS = sceneDOMapper.selectByExample(sceneDOExample);
        if (CollectionUtil.isEmpty(sceneDOS)) {
            UserSceneRecordsDO controlInfoByUser = userSceneRecordsService.getUserPermissionInfo(currentUser.getId(),
                    sceneDO.getId());
            if (controlInfoByUser != null && controlInfoByUser.getControlType() == ControlTypeEnum.UPDATE.getCode()) {
                sceneDOExample = new SceneDOExample();
                sceneDOExample.createCriteria().andIdEqualTo(sceneDO.getId());
                update = sceneDOMapper.updateByExampleSelective(sceneDO, sceneDOExample);
                LOGGER.info("updateSceneById ,operator empId:{} sceneDO:{}", currentUser.getId(), JSON.toJSONString(sceneDO));
                return update == 1;
            }
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        update = sceneDOMapper.updateByExampleSelective(sceneDO, sceneDOExample);
        // 向 agent安全网关 同步agent信息
        secAgentHelper.agentModify(sceneDO);
        return update == 1;
    }

    @Override
    public Boolean updateScene(SceneDO sceneDO) {
        if (sceneDO.getId() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andIdEqualTo(sceneDO.getId());
        int count = sceneDOMapper.updateByExampleSelective(sceneDO, sceneDOExample);
        // 向 agent安全网关 同步agent信息
        secAgentHelper.agentModify(sceneDO);

        return count == 1;
    }

    /**
     * 管理员使用
     *
     * @param sceneDO
     * @return
     */
    @Override
    public Boolean adminUpdateSceneById(SceneDO sceneDO) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andIdEqualTo(sceneDO.getId());
        int update = sceneDOMapper.updateByExampleSelective(sceneDO, sceneDOExample);
        LOGGER.info("updateSceneByIdAdmin ,operator empId:{} sceneDO:{}", currentUser.getId(), JSON.toJSONString(sceneDO));
        // 向 agent安全网关 同步agent信息
        secAgentHelper.agentModify(sceneDO);

        return update == 1;
    }

    /**
     * 增加助手使用会话数
     *
     * @param sceneId
     */
    @Override
    public Boolean addSceneUsageCount(Long sceneId) {

        SceneDO scene = this.getSceneById(sceneId);
        SceneDO sceneDO = new SceneDO();
        sceneDO.setId(sceneId);
        sceneDO.setUsageCount(scene.getUsageCount() + 1);
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andIdEqualTo(sceneDO.getId());
        int update = sceneDOMapper.updateByExampleSelective(sceneDO, sceneDOExample);
        return update == 1;
    }

    @Override
    public void addSceneUsageMessageCount(Long sceneId) {
        SceneDO scene = this.getSceneById(sceneId);
        SceneDO sceneDO = new SceneDO();
        sceneDO.setId(sceneId);
        long usageMessageCount = scene.getUsageMessageCount() == null ? 0 : scene.getUsageMessageCount();
        sceneDO.setUsageMessageCount(usageMessageCount + 1);
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andIdEqualTo(sceneDO.getId());
        sceneDOMapper.updateByExampleSelective(sceneDO, sceneDOExample);
    }

    @Override
    public void addSceneUsageUserCount(Long sceneId) {
        SceneDO scene = this.getSceneById(sceneId);
        SceneDO sceneDO = new SceneDO();
        sceneDO.setId(sceneId);
        sceneDO.setUsageUserCount(scene.getUsageUserCount() + 1);
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andIdEqualTo(sceneDO.getId());
        sceneDOMapper.updateByExampleSelective(sceneDO, sceneDOExample);
    }

    /**
     * 删除助手
     *
     * @param id 助手id
     * @return
     */
    @Override
    public Boolean deleteSceneById(long id) {
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andIdEqualTo(id);
        UserAuthDO currentUser = userAclService.getCurrentUser();
        SceneDO sceneDO = sceneDOMapper.selectByPrimaryKey(id);
        // 是管理员可以直接删除 非管理员只能删除自己创建的助手
        if (sceneDO == null){
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        if (!userAclService.isAdmin()) {
            if (!Objects.equals(sceneDO.getUserId(), currentUser.getId())) {
                throw new BizException(ResponseEnum.USER_SCENE_NOT_MATCH);
            }
        }
        SceneDO updateScene = new SceneDO();
        updateScene.setDeleted(1);
        updateScene.setName(sceneDO.getName() + "_delete_" + System.currentTimeMillis() );
        int update = sceneDOMapper.updateByExampleSelective(updateScene, sceneDOExample);
        // 删除助手 同时删除记录表的助手所有信息
        userSceneRecordsService.deleteSceneUserControl(id,null);
        LOGGER.info("deleteSceneById ,operator empId:{} sceneId:{}", currentUser.getId(), id);

        // 向 agent安全网关 同步agent信息
        secAgentHelper.agentDelete(id);

        return update == 1;
    }



    /**
     * 获取默认助手
     *
     * @return
     */
    @Override
    public SceneDO getDefaultScene() {
        return getSceneById(codeGPTDrmConfig.getSceneDefaultId());
    }

    /**
     * 获取用户收藏助手
     *
     * @return
     */
    @Override
    public  List<SceneDO> getUserSaveScene(String query) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        String saveScenes = currentUser.getSaveScenes();
        List<Long> sceneIds = JSONArray.parseArray(saveScenes,Long.class);
        if (CollectionUtil.isEmpty(sceneIds)) {
            return new ArrayList<>();
        }
        SceneDOExample sceneDOExample = new SceneDOExample();
        SceneDOExample.Criteria criteria = sceneDOExample.createCriteria();
        criteria.andIdIn(sceneIds).andDeletedEqualTo(0).andEnableEqualTo(1);
        if (StringUtil.isNotEmpty(query)){
            criteria.andNameLike(query +"%");
        }
        List<UserSceneRecordsDO> controlInfoByUser = userSceneRecordsService.getAllControlInfoByUser(currentUser.getId());
        List<Long> collect = controlInfoByUser.stream().map(UserSceneRecordsDO::getSceneId).collect(Collectors.toList());
        criteria.andVisableUserEqualTo(SceneVisableUserEnum.VISABLE_USER.getCode())
                .andVisableUserIn(Collections.singletonList(SceneVisableUserEnum.ALL.getCode()))
                .andIdIn(collect)
                .andUserIdEqualTo(currentUser.getId());
        if (currentUser.getAdmin() == UserTypeEnum.USER.getCode()) {
            criteria.andVisableUserIn(Collections.singletonList(SceneVisableUserEnum.ADMIN.getCode()))
                    .andIdIn(collect);
        }

        //不是预发环境 排除掉仅预发可见的助手
        if (!VisableEnvUtil.isPrePub()) {
            criteria.andVisableEnvNotEqualTo(VisableEnvEnum.PREPUB.getCode());
        }
        return sceneDOMapper.selectByExample(sceneDOExample);
    }

    /**
     * 物理删除助手
     *
     * @param id
     * @return
     */
    @Override
    public Boolean physicalDeletion(Long id) {
        // 向 agent安全网关 同步agent信息
        secAgentHelper.agentDelete(id);

        return sceneDOMapper.deleteByPrimaryKey(id) == 1;
    }

    /**
     * 根据id查询用户信息
     *
     * @param ids
     * @return
     */
    @Override
    public List<SceneDO> getSceneByListId(List<Long> ids) {
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andIdIn(ids);
        return sceneDOMapper.selectByExample(sceneDOExample);
    }

    /**
     * 获取function call 配置
     *
     * @param sceneDO
     * @return
     */
    @Override
    public FunctionCallConfig getFunctionCallConfig(SceneDO sceneDO) {
        String functionCallConfigStr = sceneDO.getFunctionCallConfig();

        FunctionCallConfig functionCallConfig;
        if(StringUtil.isNotEmpty(functionCallConfigStr)){
            functionCallConfig = JSON.parseObject(functionCallConfigStr,FunctionCallConfig.class);
        }else{
            functionCallConfig = new FunctionCallConfig();
        }

        if(StringUtil.isBlank(functionCallConfig.getFunctionCallModel())){
            functionCallConfig.setFunctionCallModel(codeGPTDrmConfig.getDefaultFunctionCallModel());
        }

        if(functionCallConfig.getFunctionCallMultiRoundEnabled() == null){
            functionCallConfig.setFunctionCallMultiRoundEnabled(codeGPTDrmConfig.isFunctionCallMultiRoundEnabled());
        }

        if(functionCallConfig.getLackParamStrategy() == null){
            functionCallConfig.setLackParamStrategy(EnumUtil.fromString(LackParamStrategyEnum.class, codeGPTDrmConfig.getLackParamStrategy(), LackParamStrategyEnum.USER_FORM));
        }

        if(functionCallConfig.getCallFunctionEveryRound() == null){
            functionCallConfig.setCallFunctionEveryRound(false);
        }

        if(functionCallConfig.getRepoChat() == null){
            functionCallConfig.setRepoChat(false);
        }

        return functionCallConfig;
    }

    /**
     * 在FunctionCallConfig中添加四个字段 sceneSchema和sceneSchemaUrl
     * sceneSchema存在直接使用该schema，否则通过url获取schema信息
     * @param id
     * @return
     */
    @Override
    public JSONObject getSceneSchema(Long id) {
        SceneDO sceneDO = sceneDOMapper.selectByPrimaryKey(id);
        FunctionCallConfig functionCallConfig = getFunctionCallConfig(sceneDO);

        JSONObject res = new JSONObject();
        // 获取jsonSchema
        String sceneJsonSchema = functionCallConfig.getSceneJsonSchema();
        String sceneJsonSchemaUrl = functionCallConfig.getSceneJsonSchemaUrl();
        if (StringUtil.isNotBlank(sceneJsonSchema)) {
            res.put("jsonSchema", sceneJsonSchema);
        } else if (StringUtil.isNotBlank(sceneJsonSchemaUrl)) {
            res.put("jsonSchema", getSceneSchemaByUrl(sceneJsonSchemaUrl));
        }
        // 获取uiSchema
        String sceneUiSchema = functionCallConfig.getSceneUiSchema();
        String sceneUiSchemaUrl = functionCallConfig.getSceneUiSchemaUrl();
        if (StringUtil.isNotBlank(sceneUiSchema)) {
            res.put("uiSchema", sceneUiSchema);
        } else if (StringUtil.isNotBlank(sceneUiSchemaUrl)) {
            res.put("uiSchema", getSceneSchemaByUrl(sceneUiSchemaUrl));
        }
        // 其他配置
        res.put("emptyFormButtonName", functionCallConfig.getEmptyFormButtonName());
        res.put("formButtonName", functionCallConfig.getFormButtonName());

        return res;
    }

    @Override
    public List<SceneDO> getSceneByModelName(String model) {
        SceneDOExample sceneDOExample = new SceneDOExample();
        SceneDOExample.Criteria criteria = sceneDOExample.createCriteria();
        criteria.andModeEqualTo(0);
        List<String> models = Lists.newArrayList(model);
        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
        if (!model.equals(algoBackendDO.getModel())) {
            models.add(algoBackendDO.getModel());
        }
        criteria.andModelIn(models);
        List<SceneDO> sceneDOList = sceneDOMapper.selectByExample(sceneDOExample);
        if (CollectionUtils.isEmpty(sceneDOList)) {
            return new ArrayList<>();
        }
        return sceneDOList;
    }

    @Override
    public boolean editableSceneUser(Long sceneId, Long userId) {
        SceneDO sceneDO = getSceneById(sceneId);
        UserAuthDO userAuthDO = userAclService.selectByUserId(userId);
        // 创建者或者owner可以编辑助手
        if (Objects.equals(sceneDO.getOwnerUserId(), userId) ||
                Objects.equals(sceneDO.getUserId(), userId)) {
            return true;
        }
        // 管理员有权限编辑
        if (userAuthDO.getAdmin() == 2) {
            return true;
        }
        UserSceneRecordsDO userSceneRecordsDO = userSceneRecordsService.getUserPermissionInfo(userId, sceneId);
        if (userSceneRecordsDO == null) {
            return false;
        }
        return userSceneRecordsDO.getControlType() == ControlTypeEnum.UPDATE.getCode();
    }

    @Override
    public boolean availableSceneUser(Long sceneId, Long userId) {
        SceneDO sceneDO = getSceneById(sceneId);
        UserAuthDO userAuthDO = userAclService.selectByUserId(userId);
        //助手已删除
        if (sceneDO.getDeleted() == 1) {
            return false;
        }
        // 管理员有权限查看
        if (userAuthDO.getAdmin() == 2) {
            return true;
        }
        // 助手全员可见
        if (sceneDO.getVisableUser() == SceneVisableUserEnum.ALL.getCode()) {
            return true;
        }
        // 创建者或者owner可以查看助手
        if (Objects.equals(sceneDO.getOwnerUserId(), userId) ||
                Objects.equals(sceneDO.getUserId(), userId)) {
            return true;
        }
        UserSceneRecordsDO userSceneRecordsDO = userSceneRecordsService.getUserPermissionInfo(userId, sceneId);
        return userSceneRecordsDO != null;
    }

    @Override
    public SceneDO getSceneByMessageUid(String messageUid) {
        return sceneManualMapper.selectByMessageUid(messageUid);
    }

    /**
     * 获取当前用户可编辑助手
     *
     * @param query    查询
     * @param pageNo   页数
     * @param pageSize 每页个数
     * @param sceneTag 助手标签
     * @return
     */
    @Override
    public PageResponse<List<UserSaveSceneVO>> getEditableSceneByUser(String query, int pageNo, int pageSize, String sceneTag) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        Long userId = currentUser.getId();

        List<SceneDO> editableSceneByUser = new ArrayList<>();
        long total;
        if (userAclService.isAdmin()) {
            Long editableSceneByUserCount = sceneManualMapper.getEditableSceneByAdminCount(userId, query);
            if (editableSceneByUserCount == 0) {
                return PageResponse.build(ResponseEnum.SUCCESS, new ArrayList<>(), 0L);
            }
            List<Long> sceneIdList = sceneManualMapper.getEditableSceneByAdmin(userId, query, (pageNo - 1) * pageSize, pageSize);
            if (CollectionUtil.isNotEmpty(sceneIdList)) {
                editableSceneByUser = this.getSceneByListId(sceneIdList);
                CollectionUtil.sort(editableSceneByUser, Comparator.comparing(SceneDO::getGmtCreate).thenComparing(SceneDO::getUsageCount).reversed());
            }
            total = editableSceneByUserCount;
        }
        else {
            Long editableSceneByUserCount = sceneManualMapper.getEditableSceneByUserCount(userId, query);
            if (editableSceneByUserCount == 0) {
                return PageResponse.build(ResponseEnum.SUCCESS, new ArrayList<>(), 0L);
            }
            List<Long> sceneIdList = sceneManualMapper.getEditableSceneByUser(userId, query, (pageNo - 1) * pageSize, pageSize);
            if (CollectionUtil.isNotEmpty(sceneIdList)) {
                editableSceneByUser = this.getSceneByListId(sceneIdList);
                CollectionUtil.sort(editableSceneByUser, Comparator.comparing(SceneDO::getGmtCreate).thenComparing(SceneDO::getUsageCount).reversed());
            }
            total = editableSceneByUserCount;
        }
        List<UserSaveSceneVO> userSaveSceneVOS = new ArrayList<>();
        editableSceneByUser.forEach(sceneDO -> {
            UserSaveSceneVO userSaveSceneVO = new UserSaveSceneVO();
            BeanUtil.copyProperties(sceneDO, userSaveSceneVO);
            userSaveSceneVOS.add(userSaveSceneVO);
        });
        return PageResponse.build(ResponseEnum.SUCCESS, userSaveSceneVOS, total);
    }

    @Override
    public PageResponse<List<UserSaveSceneVO>> getMySceneByUser(String query, int pageNo, int pageSize) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        List<Long> mySceneByUser = sceneManualMapper.getEditableSceneByUser(currentUser.getId(), query, (pageNo - 1) * pageSize, pageSize);
        long total = sceneManualMapper.getEditableSceneByUserCount(currentUser.getId(), query);
        List<UserSaveSceneVO> userSaveSceneVOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(mySceneByUser)) {
            List<SceneDO> sceneByListId = this.getSceneByListId(mySceneByUser);
            sceneByListId.forEach(sceneDO -> {
                UserSaveSceneVO userSaveSceneVO = new UserSaveSceneVO();
                BeanUtil.copyProperties(sceneDO, userSaveSceneVO);
                userSaveSceneVOS.add(userSaveSceneVO);
            });
        }
        return PageResponse.build(ResponseEnum.SUCCESS, userSaveSceneVOS, total);
    }

    /**
     * 获取用户有权限查看的所有已启用助手
     *
     * @param query
     * @return
     */
    @Override
    public List<UserSaveSceneVO> getAllSceneByEnable(String query) {

        UserAuthDO currentUser = userAclService.getCurrentUser();
        List<SceneDO> allSceneByEnable = new ArrayList<>();
        if (currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()) {
            List<Long> sceneIdList = sceneManualMapper.getAllSceneByEnable(currentUser.getId(), query);
            if (CollectionUtil.isNotEmpty(sceneIdList)) {
                allSceneByEnable = this.getSceneByListId(sceneIdList);
                CollectionUtil.sort(allSceneByEnable, Comparator.comparing(SceneDO::getSceneSort).thenComparing(SceneDO::getUsageCount).reversed());
            }
        }
        else {
            List<Long> sceneIdList = sceneManualMapper.getAdminAllSceneByEnable(currentUser.getId(), query);
            if (CollectionUtil.isNotEmpty(sceneIdList)) {
                allSceneByEnable = this.getSceneByListId(sceneIdList);
                CollectionUtil.sort(allSceneByEnable, Comparator.comparing(SceneDO::getSceneSort).thenComparing(SceneDO::getUsageCount).reversed());
            }
        }
        List<UserSaveSceneVO> userSaveSceneVOS = new ArrayList<>();
        List<Long> longs = JSONArray.parseArray(currentUser.getSaveScenes(), Long.class);
        allSceneByEnable.forEach(sceneDO -> {
            UserSaveSceneVO userSaveSceneVO = new UserSaveSceneVO();
            BeanUtil.copyProperties(sceneDO, userSaveSceneVO);
            // 对当前登录用户收藏的助手打标
            if (CollectionUtil.isNotEmpty(longs) && longs.contains(sceneDO.getId())) {
                userSaveSceneVO.setUserSaveScene(true);
            }
            // 加入默认助手标识
            if (Objects.equals(sceneDO.getId(), codeGPTDrmConfig.getSceneDefaultId())) {
                userSaveSceneVO.setDefaultScene(true);
            }
            userSaveSceneVOS.add(userSaveSceneVO);
        });
        return userSaveSceneVOS;
    }

    @Override
    public List<UserSaveSceneVO> getAllSceneByPluginEnable() {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "用户信息缺失，检查是否传递empId");
        }
        List<SceneDO> allSceneByPluginEnable = new ArrayList<>();
        if (currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()) {
            List<Long> sceneIdList = sceneManualMapper.getAllSceneByPluginEnable(currentUser.getId());
            if (CollectionUtil.isNotEmpty(sceneIdList)) {
                allSceneByPluginEnable = this.getSceneByListId(sceneIdList);
                CollectionUtil.sort(allSceneByPluginEnable, Comparator.comparing(SceneDO::getSceneSort).thenComparing(SceneDO::getUsageCount).reversed());
            }
        }else {
            List<Long> sceneIdList = sceneManualMapper.getAdminAllSceneByPluginEnable();
            if (CollectionUtil.isNotEmpty(sceneIdList)) {
                allSceneByPluginEnable = this.getSceneByListId(sceneIdList);
                CollectionUtil.sort(allSceneByPluginEnable, Comparator.comparing(SceneDO::getSceneSort).thenComparing(SceneDO::getUsageCount).reversed());
            }
        }
        List<UserSaveSceneVO> userSaveSceneVOS = new ArrayList<>();
        allSceneByPluginEnable.forEach(sceneDO -> {
            UserSaveSceneVO userSaveSceneVO = new UserSaveSceneVO();
            BeanUtil.copyProperties(sceneDO, userSaveSceneVO);
            userSaveSceneVO.setPluginCommandList(JSONArray.parseArray(sceneDO.getPluginCommand(), PluginCommand.class));
            userSaveSceneVOS.add(userSaveSceneVO);
        });
        return userSaveSceneVOS;
    }

    @Override
    public void bindDocument(Long sceneId, String documentUid) {
        SceneDO sceneDO = sceneDOMapper.selectByPrimaryKey(sceneId);
        if (sceneDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "sceneId 错误");
        }
        JSONArray documentUidList = new JSONArray();
        if (StringUtils.isNotBlank(sceneDO.getDocumentUidList())) {
            documentUidList = JSONArray.parseArray(sceneDO.getDocumentUidList());
        }
        documentUidList.add(documentUid);
        sceneDO.setDocumentUidList(documentUidList.toJSONString());
        sceneDOMapper.updateByPrimaryKeySelective(sceneDO);
    }

    @Override
    public void unbindDocument(Long sceneId, String documentUid) {
        SceneDO sceneDO = sceneDOMapper.selectByPrimaryKey(sceneId);
        if (sceneDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "sceneId 错误");
        }
        if (StringUtils.isBlank(sceneDO.getDocumentUidList()) ||
                !JSONArray.parseArray(sceneDO.getDocumentUidList()).contains(documentUid)) {
            return;
        }
        JSONArray documentUidList = JSONArray.parseArray(sceneDO.getDocumentUidList());
        documentUidList.remove(documentUid);
        sceneDO.setDocumentUidList(documentUidList.toJSONString());
        sceneDOMapper.updateByPrimaryKeySelective(sceneDO);
    }

    @Override
    public YuQueTokenInfoModel bindGroupToken(Long sceneId, String token) {
        SceneDO sceneDO = sceneDOMapper.selectByPrimaryKey(sceneId);
        if (sceneDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "sceneId 错误");
        }
        String yuQueTokenList = sceneDO.getYuqueTokenList();
        List<YuQueTokenInfoModel> tokenList = null;
        if(StringUtils.isBlank(yuQueTokenList)){
            tokenList = new ArrayList<>();
        }else {
            tokenList = JSONArray.parseArray(yuQueTokenList, YuQueTokenInfoModel.class);
        }
        for (YuQueTokenInfoModel yuQueTokenInfoModel : tokenList) {
            if(yuQueTokenInfoModel.getToken().equals(token)){
                throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE,"请勿重复绑定Token");
            }
        }
        YuQueGroupResponseModel group = yuQueDocUtilService.getGroupLogin(token);
        if(StringUtils.isBlank(group.getLogin())){
            throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE, "token"+token+"已失效");
        }
        YuQueTokenInfoModel yuQueTokenInfo = new YuQueTokenInfoModel();
        yuQueTokenInfo.setToken(token);
        yuQueTokenInfo.setOperateUserId(userAclService.getCurrentUser().getId());
        yuQueTokenInfo.setName(group.getName());
        yuQueTokenInfo.setTeamId(group.getId());
        tokenList.add(yuQueTokenInfo);
        sceneDO.setYuqueTokenList(JSONObject.toJSONString(tokenList));
        sceneDOMapper.updateByPrimaryKeySelective(sceneDO);
        return yuQueTokenInfo;
    }


    /**
     * 通过http请求url获取schema
     * post请求，body加empId，后续根据需求再加其他参数
     * @param sceneSchemaUrl
     * @return
     */
    private String getSceneSchemaByUrl(String sceneSchemaUrl) {
        try {
            JSONObject paramsJson = new JSONObject();
            paramsJson.put("empId", userAclService.getCurrentUser().getEmpId());
            PostBuilder postBuilder = HttpClient.post(sceneSchemaUrl);
            if (sceneSchemaUrl.startsWith("https://codegencore")) {
                postBuilder.header("Authorization", configService.getConfigByKey("Authorization", false));
            }
            HttpResponse<String> response = postBuilder.content(paramsJson.toJSONString()).syncExecuteWithFullResponse(codeGPTDrmConfig.getPluginRequestDefaultTimeOut());
            return response.body();
        } catch (Exception e) {
            LOGGER.error("getSceneSchemaByUrl error", e);
            return null;
        }
    }

    /**
     * 检查是否有编辑权限
     *
     * <AUTHOR>
     * @since 2024.10.18
     * @param sceneDO sceneDO
     * @param currentUser currentUser
     * @return boolean
     */
    private boolean checkUserEditAuth(SceneDO sceneDO , UserAuthDO currentUser) {
        //助手已删除
        if (sceneDO.getDeleted() == 1) {
            return false;
        }
        // 管理员有权限查看
        if (currentUser.getAdmin() == 2) {
            return true;
        }
        // 创建者或者owner可以查看助手
        if (Objects.equals(sceneDO.getOwnerUserId(), currentUser.getId()) ||
                Objects.equals(sceneDO.getUserId(),currentUser.getId())) {
            return true;
        }
        UserSceneRecordsDO userSceneRecordsDO = userSceneRecordsService.getUserPermissionInfo(currentUser.getId(), sceneDO.getId());
        return userSceneRecordsDO != null && userSceneRecordsDO.getControlType() == 2;
    }

}
