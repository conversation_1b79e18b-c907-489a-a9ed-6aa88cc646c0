package com.alipay.codegencore.service.impl.model;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.alipay.antq.common.utils.StringUtils;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RateLimitTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.openai.*;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.limiter.qpx.QpxLimiterFactory;
import com.alipay.codegencore.service.gptcache.bean.GptCacheResponse;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.common.tracer.util.TracerContextUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import groovy.util.logging.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

import static com.alipay.codegencore.model.enums.ResponseEnum.STREAM_THROW_EXCEPTION;

/**
 * antGLM语言模型
 *
 * <AUTHOR>
 */
@Service("antGLMLanguageModelService")
@Slf4j
public class AntGLMLanguageModelServiceImpl implements LanguageModelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AntGLMLanguageModelServiceImpl.class);

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");
    @Resource
    private CheckService checkService;

    @Resource
    private ChatMessageService chatMessageService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private AlgoModelUtilService algoModelUtilService;

    /**
     * 请求AntGML的应用名
     */
    private static final String appName = "codeFuse";
    /**
     * 请求AntGML的Token
     */
    private static final String appToken = "2c553211511c43f9";

    @Override
    public boolean isServiceOk() {
        return true;
    }

    private void sendRequest(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        LOGGER.info("antglm sendRequest user: {}, request: {}", gptAlgModelServiceRequest.getUserName(), JSON.toJSONString(gptAlgModelServiceRequest.getChatCompletionRequest()));
        AlgoBackendDO algoBackendDO = gptAlgModelServiceRequest.getAlgoBackendDO();
        String uniqueAnswerId = gptAlgModelServiceRequest.getUniqueAnswerId();
        String sessionId = gptAlgModelServiceRequest.getChatCompletionRequest().getChatRequestExtData().getSessionUid();
        String bizId = gptAlgModelServiceRequest.getChatCompletionRequest().getChatRequestExtData().getBizId();
        ChatRequestExtData chatRequestExtData = gptAlgModelServiceRequest.getChatCompletionRequest().getChatRequestExtData();
        OTHERS_LOGGER.info("开始发请求给AntGLM服务,sessionId:{},bizId:{},currentThreadId:{}", sessionId, bizId, Thread.currentThread().getId());
        AntLLMOpenAPIRequest request = buildAntLLMOpenAPIRequest(gptAlgModelServiceRequest.getUserName(), gptAlgModelServiceRequest.getChatCompletionRequest(),algoBackendDO, chatRequestExtData);
        boolean[] stopOnMessage = {false};
        boolean[] alreadyClose = {false};
        long[] firstPackageTime = {0};
        CountDownLatch firstPackage = new CountDownLatch(1);
        String streamInputId = String.format("%s%s", AppConstants.STREAM_INPUT_PREFIX, gptAlgModelServiceRequest.getUniqueAnswerId());
        try {
            WebSocketClient wsClient = new WebSocketClient(
                    new URI(AppConstants.ANTGML_WEBSOCKET_URL), new Draft_6455(), null, AlgoBackendUtil.exactConnTimeoutConfig(algoBackendDO)) {
                /**
                 * 建立链接后, 业务方会调用该方法
                 *
                 * @param handshakedata
                 */
                @Override
                public void onOpen(ServerHandshake handshakedata) {
                    // 当连接建立成功后，会回调该方法
                    OTHERS_LOGGER.info("ws:onOpen: handshakedata:{},request:{}", handshakedata, JSONObject.toJSONString(request));
                    // 发送请求
                    this.send(JSONObject.toJSONString(request));
                }

                /**
                 * 处理流式响应内容
                 * 首包的turn字段是1, turn是累加的下发。预测不了总共多少turn，只能在实际结束时候调用client里的close方法
                 *
                 * @param message
                 */
                @Override
                public void onMessage(String message) {
                    OTHERS_LOGGER.info("ws:onMessage message:{},sessionId:{},bizId:{},currentThreadId:{}", message, sessionId, bizId, Thread.currentThread().getId());
                    if(algoModelUtilService.needCloseInputStream(streamInputId)){
                        stopOnMessage[0] = true;
                        close();
                    }

                    if (firstPackage.getCount() == 1) {
                        firstPackageTime[0] = System.currentTimeMillis();
                    }
                    firstPackage.countDown();

                    if (stopOnMessage[0]) {
                        LOGGER.warn("该流已经关闭,不在进行接收,sessionId:{},bizId:{}", sessionId, bizId);
                        return;
                    }
                    // 接收到服务端发送的消息
                    if (StringUtil.isEmpty(message)) {
                        onClose(ResponseEnum.AI_CALL_ERROR.getErrorCode(), "antGLM stream message is null", false);
                        stopOnMessage[0] = true;
                    }
                    try {
                        // 流式协议写会多次调用该方法
                        // 可以在这里反序列化成对象后处理
                        AntLLMOpenAPIResponse antLLMOpenAPIResponse = JSONObject.parseObject(message, AntLLMOpenAPIResponse.class);
                        if (antLLMOpenAPIResponse == null) {
                            LOGGER.warn("anrglm json is null,message:{},sessionId:{},bizId:{}", message, sessionId, bizId);
                            onClose(0, "antLLMOpenAPIResponse is null", false);
                            stopOnMessage[0] = true;
                            return;
                        }
                        if (antLLMOpenAPIResponse.getRevoke() || !antLLMOpenAPIResponse.getSuccess()) {
                            LOGGER.warn("anrglm json is failed,message:{},sessionId:{},bizId:{}", message, sessionId, bizId);
                            onClose(ResponseEnum.AI_CALL_ERROR.getErrorCode(), "antGLM stream message failed", false);
                            stopOnMessage[0] = true;
                            return;
                        }
                        ChatMessage delta = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), antLLMOpenAPIResponse.getResponse());
                        ChatUtils.handleEveryStreamData(delta, null, noneSerializationCacheManager, uniqueAnswerId);
                    } catch (Exception e) {
                        LOGGER.error("ws on_message error:" + e.getMessage(), e);
                        onClose(ResponseEnum.AI_CALL_ERROR.getErrorCode(), "antGLM stream message is null", false);
                        stopOnMessage[0] = true;
                    }
                }

                /**
                 * 流式响应结束后, 业务方会主动调用这个方法
                 *
                 * @param code
                 * @param reason
                 * @param remote 当server关闭连接时，remote=true；当client调用close()关闭，remote=false
                 */
                @Override
                public void onClose(int code, String reason, boolean remote) {
                    if (alreadyClose[0]) {
                        return;
                    }
                    alreadyClose[0] = true;
                    stopOnMessage[0] = true;
                    OTHERS_LOGGER.info("ws:onClose: code={}, reason={}, remote={}", code, reason, remote);
                    if (!remote) {
                        OTHERS_LOGGER.info("客户端手动结束流,sessionId:{},bizId:{}", sessionId, bizId);
                        algoModelUtilService.handleEveryStreamError(reason, noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.getByCode(code));
                    } else {
                        // 只有当code=1000，表示是服务端已经处理完成，正常关闭
                        if (code == 1000) {
                            ChatUtils.handleEveryStreamData(null, "stop", noneSerializationCacheManager, uniqueAnswerId);
                        } else {
                            algoModelUtilService.handleEveryStreamError(reason, noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.getByCode(code));
                        }
                    }
                }

                /**
                 * 处理异常信息
                 *
                 * @param ex
                 */
                @Override
                public void onError(Exception ex) {
                    // 请求过程中发生异常（一般是本地异常），会回调该方法；当error发生后，一般会调用onClose进行关闭连接，close_code不等于1000
                    LOGGER.error("ws:onError:" + ex.getMessage(), ex);
                    if (!alreadyClose[0]) {
                        algoModelUtilService.handleEveryStreamError(ex.getMessage(), noneSerializationCacheManager, uniqueAnswerId, STREAM_THROW_EXCEPTION);
                    }
                    alreadyClose[0] = true;
                    stopOnMessage[0] = true;
                }
            };
            long startConnectTime = System.currentTimeMillis();
            // 建立链接
            boolean connectSuccess = wsClient.connectBlocking();
            // 超时处理
            if (!connectSuccess) {
                OTHERS_LOGGER.info("连接失败；此时你的onError及onClose方法都会被调用到,sessionId:{},bizId:{}", sessionId, bizId);
                alreadyClose[0] = true;
            }
            long connectSuccessTime = System.currentTimeMillis();
            long connectCostTime = connectSuccessTime - startConnectTime;
            firstPackage.await();
            long firstPackageCostTime = firstPackageTime[0] - connectSuccessTime;
            long connectAndFirstPackageCostTime = firstPackageTime[0] - startConnectTime;
            OTHERS_LOGGER.info("建立连接成功,建连耗时:{},首包耗时:{},建连加首包耗时:{},是否超时:{},sessionId:{},bizId:{}",
                    connectCostTime, firstPackageCostTime, connectAndFirstPackageCostTime,
                    connectAndFirstPackageCostTime >= AlgoBackendUtil.exactFirstStreamDataWaitTimeConfig(algoBackendDO), sessionId, bizId);
        } catch (Throwable e) {
            LOGGER.error("建立webSocket连接异常,sessionId:" + sessionId + ",bizId:" + bizId, e);
            algoModelUtilService.handleEveryStreamError(e.getMessage(), noneSerializationCacheManager, uniqueAnswerId, STREAM_THROW_EXCEPTION);
        }
    }

    private AntLLMOpenAPIRequest buildAntLLMOpenAPIRequest(String user,ChatCompletionRequest chatCompletionRequest, AlgoBackendDO algoBackendDO, ChatRequestExtData chatRequestExtData) {
        String[] modelInfo = AlgoBackendUtil.exactModelInfoConfig(algoBackendDO);
        // 构造request
        AntLLMOpenAPIRequest request = new AntLLMOpenAPIRequest();
        // 鉴权及调用信息
        // 必填-应用名
        request.setAppName(appName);
        // 必填-应用token
        request.setAppToken(appToken);
        // 设置traceId
        request.setTraceId(TracerContextUtil.getTraceId());
        // 对话请求
        request.setReqType("chat"); // 必填-chat表示对话请求
        // 必填-用户请求
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        request.setQuery(messages.get(messages.size() - 1).getContent());
        // 流式请求-这里填false
        request.setDoSample(false);
        // 设置最大响应长度
        request.setMaxOutputLength(AlgoBackendUtil.exactMaxOutputLengthConfig(algoBackendDO)); // 最大输出长度-按需设置
        String sessionId = chatRequestExtData.getSessionUid();
        if(chatRequestExtData.isLingXiMultipleRounds()){
            // 选填-会话id，多轮对话时通过该id表示会话关系；最好由调用方生成并传入，同一个会话传入相同id
            request.setSessionId(sessionId);
        }
        // 必填-调用人的工号/id
        request.setUserId(chatRequestExtData.getEmpId());
        //模型名称
        request.setModelId(modelInfo[0]);
        // 模型版本
        request.setModelVersion(modelInfo[1]);
        //场景
        request.setSceneName(user);
        request.setStopSequences(chatCompletionRequest.getStop());
        return request;
    }

    /**
     * 对话
     *
     * @param params 对话请求
     * @return ai模型回答
     */
    @Override
    public ChatMessage chat(GptAlgModelServiceRequest params) {
        LOGGER.info("antglm chat user: {}, request: {}", params.getUserName(), JSON.toJSONString(params.getChatCompletionRequest()));
        String user = params.getUserName();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        if(chatCompletionRequest.getPrompt()!=null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"antglm不支持prompt补全模式");
        }
        String requestId = params.getRequestId();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        updateAntGLMConfig(algoBackendDO, user);
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        if(messages == null || messages.isEmpty() || messages.get(messages.size()-1) == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "messages is null");
        }
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        if (chatRequestExtData == null || StringUtil.isEmpty(chatRequestExtData.getEmpId())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "empId is null");
        }
        //对应的模型
        String[] modelInfo = AlgoBackendUtil.exactModelInfoConfig(algoBackendDO);
        if (modelInfo == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "modelInfo is null,param = " + params.getUserOriginalModel());
        }
        // 构造request
        AntLLMOpenAPIRequest request = new AntLLMOpenAPIRequest();
        // 鉴权及调用信息
        // 必填-应用名
        request.setAppName(appName);
        // 设置traceId
        request.setTraceId(TracerContextUtil.getTraceId());
        // 对话请求
        request.setReqType("chat"); // 必填-chat表示对话请求
        // 必填-用户请求
        request.setQuery(messages.get(messages.size() - 1).getContent());
        // 流式请求-这里填false
        request.setDoSample(true);
        // 设置最大响应长度
        request.setMaxOutputLength(AlgoBackendUtil.exactMaxOutputLengthConfig(algoBackendDO));
        String sessionId = chatRequestExtData.getSessionUid();
        // 选填-会话id，多轮对话时通过该id表示会话关系；最好由调用方生成并传入，同一个会话传入相同id
        if(chatRequestExtData.isLingXiMultipleRounds()){
            request.setSessionId(sessionId);
        }
        // 必填-调用人的工号/id
        request.setUserId(chatRequestExtData.getEmpId());
        //模型名称
        request.setModelId(modelInfo[0]);
        // 模型版本
        request.setModelVersion(modelInfo[1]);
        //场景
        request.setSceneName(user);
        request.setStopSequences(chatCompletionRequest.getStop());
        OTHERS_LOGGER.info("request={}", JSONObject.toJSONString(request));
        chatRequestExtData.setAlgoBackendDO(algoBackendDO);
        // 非流式请求，antglm内部已经做了审核能力，调用方不用审核。
        // 开始请求ai服务
        String result = getAiResult(params.getModelEnv(),requestId, user, algoBackendDO, request);
        return new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), result);
    }


    private String getAiResult(String modelEnv,String requestId, String user, AlgoBackendDO algoBackendDO, AntLLMOpenAPIRequest request) {
        LOGGER.info("getAiResult request={}", JSONObject.toJSONString(request));
        String response = null;
        try {
            response = HttpClient.post(AppConstants.ANTGML_HTTP_URL)
                    .header("Authorization", "Bearer " + appToken)
                    .header("Content-Type", "application/json")
                    .content(JSONObject.toJSONString(request))
                    .syncExecuteWithExceptionThrow(180000L);
        } catch (Exception e) {
            LOGGER.error("AntGml completion request failed, id: {}, user:{}", requestId, user, e);
            throw new RuntimeException(e);
        }
        if (response == null) {
            OTHERS_LOGGER.info("AntGML completion get no result, id:{}, user:{}", requestId, user);
            return null;
        }
        LOGGER.info("getAiResult response={}", JSONObject.toJSONString(response));
        // 可以在这里反序列化成对象后处理
        AntLLMOpenAPIResponse antLLMOpenAPIResponse = JSONObject.parseObject(response, AntLLMOpenAPIResponse.class);
        if (antLLMOpenAPIResponse == null) {
            throw new BizException(ResponseEnum.AI_CALL_ERROR, "antLLMOpenAPIResponse is null");
        }
        if (!antLLMOpenAPIResponse.getSuccess()) {
            throw new BizException(ResponseEnum.AI_CALL_ERROR, antLLMOpenAPIResponse.getErrorMsg());
        }
        if (antLLMOpenAPIResponse.getRejected()) {
            throw new BizException(ResponseEnum.CHECK_FAILED, antLLMOpenAPIResponse.getResponse());
        }
        return antLLMOpenAPIResponse.getResponse();
    }

    @Override
    public void streamChatForServlet(GptAlgModelServiceRequest params) {
        // 获取请求参数
        String user = params.getUserName();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        if(chatCompletionRequest.getPrompt()!=null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"antglm不支持prompt补全模式");
        }
        String requestId = params.getRequestId();
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = params.getChatStreamPartResponseHandler();
        Consumer<StreamResponseModel> resultHandler = params.getResultHandler();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        updateAntGLMConfig(algoBackendDO, user);
        //对应的模型
        String[] modelInfo = AlgoBackendUtil.exactModelInfoConfig(algoBackendDO);
        if (modelInfo == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "modelInfo is null,param = " + params.getUserOriginalModel());
        }
        // 限流检查
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        // 必须是流式请求
        if (!chatCompletionRequest.getStream()) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "stream must be true");
        }

        // 安全校验要求必须传入empId
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        if (chatRequestExtData == null || StringUtil.isEmpty(chatRequestExtData.getEmpId())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "empId is null");
        }
        // 配置模型信息
        chatRequestExtData.setAlgoBackendDO(algoBackendDO);

        // 对用户输入的问题进行审核,输入内容一批全部审
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        if(messages == null || messages.isEmpty() || messages.get(messages.size()-1) == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "messages is null");
        }
        List<ChatMessage> copyMessages = JSON.parseArray(JSON.toJSONString(messages), ChatMessage.class);

        // 安全校验
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(requestId, messages, chatCompletionRequest.getChatRequestExtData(), false);

        // 判断送检结果
        if (!requestCheckResultModel.isAllCheckRet()) {
            // 问题不通过, 停止流响应
            algoModelUtilService.processWhenQuestionFailCheck(requestCheckResultModel, chatStreamPartResponseConsumer, resultHandler);
            return;
        }
        // 开启gptCache并从gptCache写入TBase数据成功 不发送请求
        GptCacheResponse gptCacheResponse  = null;
        if(algoBackendDO.getEnableGptCache()){
            gptCacheResponse =  algoModelUtilService.getGPTCache(algoBackendDO, params);
        }
        if(gptCacheResponse !=  null  && StringUtils.isNotBlank(gptCacheResponse.getAnswer())){
            GptCacheResponse finalGptCacheResponse = gptCacheResponse;
            appThreadPool.execute(() -> algoModelUtilService.pushToTBase(finalGptCacheResponse, params));
        }else{
            appThreadPool.execute(() -> sendRequest(AlgoModelUtilService.copyParamWithoutServletResponse(params)));
        }
        algoModelUtilService.getChatDataFromTBase(params, copyMessages, requestCheckResultModel, algoBackendDO.getEnableGptCache(),new ChatStreamBuffer(),false);
    }

    private void updateAntGLMConfig(AlgoBackendDO algoBackendDO, String user) {
        JSONObject antGLMAuthConfig = codeGPTDrmConfig.getAntGLMAuthConfig();
        if (antGLMAuthConfig == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "antGLMAuthConfig is blank");
        }
        JSONObject antGLMAuthConfigUser;
        if (antGLMAuthConfig.containsKey(user)) {
            antGLMAuthConfigUser = antGLMAuthConfig.getJSONObject(user);
        } else if (antGLMAuthConfig.containsKey("others")) {
            antGLMAuthConfigUser = antGLMAuthConfig.getJSONObject("others");
        } else {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "antGLMAuthConfig is blank,user:" + user);
        }
        if (!antGLMAuthConfigUser.containsKey("appName") || !antGLMAuthConfigUser.containsKey("appToken")) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "antGLMAuthConfig is blank,appName and appToken is blank,user:" + user);
        }
        JSONObject implConfig = JSON.parseObject(algoBackendDO.getImplConfig());
        implConfig.put("appName", antGLMAuthConfigUser.getString("appName"));
        implConfig.put("appToken", antGLMAuthConfigUser.getString("appToken"));
        algoBackendDO.setImplConfig(implConfig.toJSONString());
    }

}
