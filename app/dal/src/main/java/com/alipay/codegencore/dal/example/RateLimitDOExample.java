package com.alipay.codegencore.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RateLimitDOExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public RateLimitDOExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andEnableIsNull() {
            addCriterion("enable is null");
            return (Criteria) this;
        }

        public Criteria andEnableIsNotNull() {
            addCriterion("enable is not null");
            return (Criteria) this;
        }

        public Criteria andEnableEqualTo(Boolean value) {
            addCriterion("enable =", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotEqualTo(Boolean value) {
            addCriterion("enable <>", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThan(Boolean value) {
            addCriterion("enable >", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable >=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThan(Boolean value) {
            addCriterion("enable <", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("enable <=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableIn(List<Boolean> values) {
            addCriterion("enable in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotIn(List<Boolean> values) {
            addCriterion("enable not in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("enable between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable not between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andNeedLimitIsNull() {
            addCriterion("need_limit is null");
            return (Criteria) this;
        }

        public Criteria andNeedLimitIsNotNull() {
            addCriterion("need_limit is not null");
            return (Criteria) this;
        }

        public Criteria andNeedLimitEqualTo(Boolean value) {
            addCriterion("need_limit =", value, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitNotEqualTo(Boolean value) {
            addCriterion("need_limit <>", value, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitGreaterThan(Boolean value) {
            addCriterion("need_limit >", value, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("need_limit >=", value, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitLessThan(Boolean value) {
            addCriterion("need_limit <", value, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitLessThanOrEqualTo(Boolean value) {
            addCriterion("need_limit <=", value, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitIn(List<Boolean> values) {
            addCriterion("need_limit in", values, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitNotIn(List<Boolean> values) {
            addCriterion("need_limit not in", values, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitBetween(Boolean value1, Boolean value2) {
            addCriterion("need_limit between", value1, value2, "needLimit");
            return (Criteria) this;
        }

        public Criteria andNeedLimitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("need_limit not between", value1, value2, "needLimit");
            return (Criteria) this;
        }

        public Criteria andStopIsNull() {
            addCriterion("stop is null");
            return (Criteria) this;
        }

        public Criteria andStopIsNotNull() {
            addCriterion("stop is not null");
            return (Criteria) this;
        }

        public Criteria andStopEqualTo(Boolean value) {
            addCriterion("stop =", value, "stop");
            return (Criteria) this;
        }

        public Criteria andStopNotEqualTo(Boolean value) {
            addCriterion("stop <>", value, "stop");
            return (Criteria) this;
        }

        public Criteria andStopGreaterThan(Boolean value) {
            addCriterion("stop >", value, "stop");
            return (Criteria) this;
        }

        public Criteria andStopGreaterThanOrEqualTo(Boolean value) {
            addCriterion("stop >=", value, "stop");
            return (Criteria) this;
        }

        public Criteria andStopLessThan(Boolean value) {
            addCriterion("stop <", value, "stop");
            return (Criteria) this;
        }

        public Criteria andStopLessThanOrEqualTo(Boolean value) {
            addCriterion("stop <=", value, "stop");
            return (Criteria) this;
        }

        public Criteria andStopIn(List<Boolean> values) {
            addCriterion("stop in", values, "stop");
            return (Criteria) this;
        }

        public Criteria andStopNotIn(List<Boolean> values) {
            addCriterion("stop not in", values, "stop");
            return (Criteria) this;
        }

        public Criteria andStopBetween(Boolean value1, Boolean value2) {
            addCriterion("stop between", value1, value2, "stop");
            return (Criteria) this;
        }

        public Criteria andStopNotBetween(Boolean value1, Boolean value2) {
            addCriterion("stop not between", value1, value2, "stop");
            return (Criteria) this;
        }

        public Criteria andIgnoreListIsNull() {
            addCriterion("ignore_list is null");
            return (Criteria) this;
        }

        public Criteria andIgnoreListIsNotNull() {
            addCriterion("ignore_list is not null");
            return (Criteria) this;
        }

        public Criteria andIgnoreListEqualTo(String value) {
            addCriterion("ignore_list =", value, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListNotEqualTo(String value) {
            addCriterion("ignore_list <>", value, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListGreaterThan(String value) {
            addCriterion("ignore_list >", value, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListGreaterThanOrEqualTo(String value) {
            addCriterion("ignore_list >=", value, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListLessThan(String value) {
            addCriterion("ignore_list <", value, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListLessThanOrEqualTo(String value) {
            addCriterion("ignore_list <=", value, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListLike(String value) {
            addCriterion("ignore_list like", value, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListNotLike(String value) {
            addCriterion("ignore_list not like", value, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListIn(List<String> values) {
            addCriterion("ignore_list in", values, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListNotIn(List<String> values) {
            addCriterion("ignore_list not in", values, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListBetween(String value1, String value2) {
            addCriterion("ignore_list between", value1, value2, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andIgnoreListNotBetween(String value1, String value2) {
            addCriterion("ignore_list not between", value1, value2, "ignoreList");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsIsNull() {
            addCriterion("window_time_mills is null");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsIsNotNull() {
            addCriterion("window_time_mills is not null");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsEqualTo(Integer value) {
            addCriterion("window_time_mills =", value, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsNotEqualTo(Integer value) {
            addCriterion("window_time_mills <>", value, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsGreaterThan(Integer value) {
            addCriterion("window_time_mills >", value, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsGreaterThanOrEqualTo(Integer value) {
            addCriterion("window_time_mills >=", value, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsLessThan(Integer value) {
            addCriterion("window_time_mills <", value, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsLessThanOrEqualTo(Integer value) {
            addCriterion("window_time_mills <=", value, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsIn(List<Integer> values) {
            addCriterion("window_time_mills in", values, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsNotIn(List<Integer> values) {
            addCriterion("window_time_mills not in", values, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsBetween(Integer value1, Integer value2) {
            addCriterion("window_time_mills between", value1, value2, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTimeMillsNotBetween(Integer value1, Integer value2) {
            addCriterion("window_time_mills not between", value1, value2, "windowTimeMills");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaIsNull() {
            addCriterion("window_total_quota is null");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaIsNotNull() {
            addCriterion("window_total_quota is not null");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaEqualTo(Integer value) {
            addCriterion("window_total_quota =", value, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaNotEqualTo(Integer value) {
            addCriterion("window_total_quota <>", value, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaGreaterThan(Integer value) {
            addCriterion("window_total_quota >", value, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaGreaterThanOrEqualTo(Integer value) {
            addCriterion("window_total_quota >=", value, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaLessThan(Integer value) {
            addCriterion("window_total_quota <", value, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaLessThanOrEqualTo(Integer value) {
            addCriterion("window_total_quota <=", value, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaIn(List<Integer> values) {
            addCriterion("window_total_quota in", values, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaNotIn(List<Integer> values) {
            addCriterion("window_total_quota not in", values, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaBetween(Integer value1, Integer value2) {
            addCriterion("window_total_quota between", value1, value2, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andWindowTotalQuotaNotBetween(Integer value1, Integer value2) {
            addCriterion("window_total_quota not between", value1, value2, "windowTotalQuota");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andCallerIsNull() {
            addCriterion("caller is null");
            return (Criteria) this;
        }

        public Criteria andCallerIsNotNull() {
            addCriterion("caller is not null");
            return (Criteria) this;
        }

        public Criteria andCallerEqualTo(String value) {
            addCriterion("caller =", value, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerNotEqualTo(String value) {
            addCriterion("caller <>", value, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerGreaterThan(String value) {
            addCriterion("caller >", value, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerGreaterThanOrEqualTo(String value) {
            addCriterion("caller >=", value, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerLessThan(String value) {
            addCriterion("caller <", value, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerLessThanOrEqualTo(String value) {
            addCriterion("caller <=", value, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerLike(String value) {
            addCriterion("caller like", value, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerNotLike(String value) {
            addCriterion("caller not like", value, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerIn(List<String> values) {
            addCriterion("caller in", values, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerNotIn(List<String> values) {
            addCriterion("caller not in", values, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerBetween(String value1, String value2) {
            addCriterion("caller between", value1, value2, "caller");
            return (Criteria) this;
        }

        public Criteria andCallerNotBetween(String value1, String value2) {
            addCriterion("caller not between", value1, value2, "caller");
            return (Criteria) this;
        }

        public Criteria andTargetIsNull() {
            addCriterion("target is null");
            return (Criteria) this;
        }

        public Criteria andTargetIsNotNull() {
            addCriterion("target is not null");
            return (Criteria) this;
        }

        public Criteria andTargetEqualTo(String value) {
            addCriterion("target =", value, "target");
            return (Criteria) this;
        }

        public Criteria andTargetNotEqualTo(String value) {
            addCriterion("target <>", value, "target");
            return (Criteria) this;
        }

        public Criteria andTargetGreaterThan(String value) {
            addCriterion("target >", value, "target");
            return (Criteria) this;
        }

        public Criteria andTargetGreaterThanOrEqualTo(String value) {
            addCriterion("target >=", value, "target");
            return (Criteria) this;
        }

        public Criteria andTargetLessThan(String value) {
            addCriterion("target <", value, "target");
            return (Criteria) this;
        }

        public Criteria andTargetLessThanOrEqualTo(String value) {
            addCriterion("target <=", value, "target");
            return (Criteria) this;
        }

        public Criteria andTargetLike(String value) {
            addCriterion("target like", value, "target");
            return (Criteria) this;
        }

        public Criteria andTargetNotLike(String value) {
            addCriterion("target not like", value, "target");
            return (Criteria) this;
        }

        public Criteria andTargetIn(List<String> values) {
            addCriterion("target in", values, "target");
            return (Criteria) this;
        }

        public Criteria andTargetNotIn(List<String> values) {
            addCriterion("target not in", values, "target");
            return (Criteria) this;
        }

        public Criteria andTargetBetween(String value1, String value2) {
            addCriterion("target between", value1, value2, "target");
            return (Criteria) this;
        }

        public Criteria andTargetNotBetween(String value1, String value2) {
            addCriterion("target not between", value1, value2, "target");
            return (Criteria) this;
        }

        public Criteria andTemplateIsNull() {
            addCriterion("template is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIsNotNull() {
            addCriterion("template is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateEqualTo(String value) {
            addCriterion("template =", value, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateNotEqualTo(String value) {
            addCriterion("template <>", value, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateGreaterThan(String value) {
            addCriterion("template >", value, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateGreaterThanOrEqualTo(String value) {
            addCriterion("template >=", value, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateLessThan(String value) {
            addCriterion("template <", value, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateLessThanOrEqualTo(String value) {
            addCriterion("template <=", value, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateLike(String value) {
            addCriterion("template like", value, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateNotLike(String value) {
            addCriterion("template not like", value, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateIn(List<String> values) {
            addCriterion("template in", values, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateNotIn(List<String> values) {
            addCriterion("template not in", values, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateBetween(String value1, String value2) {
            addCriterion("template between", value1, value2, "template");
            return (Criteria) this;
        }

        public Criteria andTemplateNotBetween(String value1, String value2) {
            addCriterion("template not between", value1, value2, "template");
            return (Criteria) this;
        }

        public Criteria andSortedIsNull() {
            addCriterion("sorted is null");
            return (Criteria) this;
        }

        public Criteria andSortedIsNotNull() {
            addCriterion("sorted is not null");
            return (Criteria) this;
        }

        public Criteria andSortedEqualTo(Integer value) {
            addCriterion("sorted =", value, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedNotEqualTo(Integer value) {
            addCriterion("sorted <>", value, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedGreaterThan(Integer value) {
            addCriterion("sorted >", value, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedGreaterThanOrEqualTo(Integer value) {
            addCriterion("sorted >=", value, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedLessThan(Integer value) {
            addCriterion("sorted <", value, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedLessThanOrEqualTo(Integer value) {
            addCriterion("sorted <=", value, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedIn(List<Integer> values) {
            addCriterion("sorted in", values, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedNotIn(List<Integer> values) {
            addCriterion("sorted not in", values, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedBetween(Integer value1, Integer value2) {
            addCriterion("sorted between", value1, value2, "sorted");
            return (Criteria) this;
        }

        public Criteria andSortedNotBetween(Integer value1, Integer value2) {
            addCriterion("sorted not between", value1, value2, "sorted");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityIsNull() {
            addCriterion("mark_priority is null");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityIsNotNull() {
            addCriterion("mark_priority is not null");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityEqualTo(Byte value) {
            addCriterion("mark_priority =", value, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityNotEqualTo(Byte value) {
            addCriterion("mark_priority <>", value, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityGreaterThan(Byte value) {
            addCriterion("mark_priority >", value, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityGreaterThanOrEqualTo(Byte value) {
            addCriterion("mark_priority >=", value, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityLessThan(Byte value) {
            addCriterion("mark_priority <", value, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityLessThanOrEqualTo(Byte value) {
            addCriterion("mark_priority <=", value, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityIn(List<Byte> values) {
            addCriterion("mark_priority in", values, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityNotIn(List<Byte> values) {
            addCriterion("mark_priority not in", values, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityBetween(Byte value1, Byte value2) {
            addCriterion("mark_priority between", value1, value2, "markPriority");
            return (Criteria) this;
        }

        public Criteria andMarkPriorityNotBetween(Byte value1, Byte value2) {
            addCriterion("mark_priority not between", value1, value2, "markPriority");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigIsNull() {
            addCriterion("priority_config is null");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigIsNotNull() {
            addCriterion("priority_config is not null");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigEqualTo(String value) {
            addCriterion("priority_config =", value, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigNotEqualTo(String value) {
            addCriterion("priority_config <>", value, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigGreaterThan(String value) {
            addCriterion("priority_config >", value, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigGreaterThanOrEqualTo(String value) {
            addCriterion("priority_config >=", value, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigLessThan(String value) {
            addCriterion("priority_config <", value, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigLessThanOrEqualTo(String value) {
            addCriterion("priority_config <=", value, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigLike(String value) {
            addCriterion("priority_config like", value, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigNotLike(String value) {
            addCriterion("priority_config not like", value, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigIn(List<String> values) {
            addCriterion("priority_config in", values, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigNotIn(List<String> values) {
            addCriterion("priority_config not in", values, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigBetween(String value1, String value2) {
            addCriterion("priority_config between", value1, value2, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andPriorityConfigNotBetween(String value1, String value2) {
            addCriterion("priority_config not between", value1, value2, "priorityConfig");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_rate_limit
     *
     * @mbg.generated do_not_delete_during_merge Fri Dec 29 11:32:12 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}