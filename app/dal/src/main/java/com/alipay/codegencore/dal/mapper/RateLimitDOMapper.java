package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.RateLimitDOExample;
import com.alipay.codegencore.model.domain.RateLimitDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface RateLimitDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    long countByExample(RateLimitDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    int deleteByExample(RateLimitDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    @Delete({
        "delete from cg_rate_limit",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    @Insert({
        "insert into cg_rate_limit (gmt_create, gmt_modified, ",
        "enable, need_limit, ",
        "stop, ignore_list, ",
        "window_time_mills, window_total_quota, ",
        "type, caller, target, ",
        "template, sorted, ",
        "mark_priority, priority_config, ",
        "create_user_id, remark)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{enable,jdbcType=TINYINT}, #{needLimit,jdbcType=TINYINT}, ",
        "#{stop,jdbcType=TINYINT}, #{ignoreList,jdbcType=VARCHAR}, ",
        "#{windowTimeMills,jdbcType=INTEGER}, #{windowTotalQuota,jdbcType=INTEGER}, ",
        "#{type,jdbcType=VARCHAR}, #{caller,jdbcType=VARCHAR}, #{target,jdbcType=VARCHAR}, ",
        "#{template,jdbcType=VARCHAR}, #{sorted,jdbcType=INTEGER}, ",
        "#{markPriority,jdbcType=TINYINT}, #{priorityConfig,jdbcType=VARCHAR}, ",
        "#{createUserId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(RateLimitDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    int insertSelective(RateLimitDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    List<RateLimitDO> selectByExample(RateLimitDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, enable, need_limit, stop, ignore_list, window_time_mills, ",
        "window_total_quota, type, caller, target, template, sorted, mark_priority, priority_config, ",
        "create_user_id, remark",
        "from cg_rate_limit",
        "where id = #{id,jdbcType=BIGINT}"
    })
    RateLimitDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    int updateByExampleSelective(@Param("record") RateLimitDO record, @Param("example") RateLimitDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    int updateByExample(@Param("record") RateLimitDO record, @Param("example") RateLimitDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    int updateByPrimaryKeySelective(RateLimitDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_rate_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    @Update({
        "update cg_rate_limit",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "enable = #{enable,jdbcType=TINYINT},",
          "need_limit = #{needLimit,jdbcType=TINYINT},",
          "stop = #{stop,jdbcType=TINYINT},",
          "ignore_list = #{ignoreList,jdbcType=VARCHAR},",
          "window_time_mills = #{windowTimeMills,jdbcType=INTEGER},",
          "window_total_quota = #{windowTotalQuota,jdbcType=INTEGER},",
          "type = #{type,jdbcType=VARCHAR},",
          "caller = #{caller,jdbcType=VARCHAR},",
          "target = #{target,jdbcType=VARCHAR},",
          "template = #{template,jdbcType=VARCHAR},",
          "sorted = #{sorted,jdbcType=INTEGER},",
          "mark_priority = #{markPriority,jdbcType=TINYINT},",
          "priority_config = #{priorityConfig,jdbcType=VARCHAR},",
          "create_user_id = #{createUserId,jdbcType=BIGINT},",
          "remark = #{remark,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(RateLimitDO record);
}