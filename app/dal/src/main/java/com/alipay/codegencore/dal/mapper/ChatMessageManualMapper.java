package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.domain.UserMessageCountDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 手写的ChatMessageMapper,自动生成时无需覆盖
 */
public interface ChatMessageManualMapper extends ChatMessageDOMapper {


    @Select({"<script> " +
            "select count(*) from cg_chat_message " +
            "where gmt_create between #{gmtCreateBegin} and #{gmtCreateEnd} and role='assistant' " +
            "<if test='userId!=null'> and user_id=#{userId} </if>" +
            "<if test='needVote!=null and needVote'> and vote is not null </if>" +
            "</script>"})
    int selectTotalAnswerCount(@Param("gmtCreateBegin") Date gmtCreateBegin,
                               @Param("gmtCreateEnd") Date gmtCreateEnd,
                               @Param("userId") Long userId,
                               @Param("needVote") Boolean needVote);


    @Select({"<script> select count(*) as messageCount,user_id from cg_chat_message " +
            "where gmt_create between #{gmtCreateBegin} and #{gmtCreateEnd} and role='assistant' " +
            "<if test='userId!=null'> and user_id=#{userId} </if>" +
            "<if test='needVote!=null and needVote'> and vote is not null </if> " +
            "group by user_id" +
            "</script>"})
    List<UserMessageCountDO> selectAnswerMessageCountGroupByUserId(@Param("gmtCreateBegin") Date gmtCreateBegin,
                                                                   @Param("gmtCreateEnd") Date gmtCreateEnd,
                                                                   @Param("userId") Long userId,
                                                                   @Param("needVote") Boolean needVote);

}
