package com.alipay.codegencore.dal.example;
import java.util.ArrayList;
import java.util.List;

public class CodeTemplateDOExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public CodeTemplateDOExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumIsNull() {
            addCriterion("biz_scene_enum is null");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumIsNotNull() {
            addCriterion("biz_scene_enum is not null");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumEqualTo(Integer value) {
            addCriterion("biz_scene_enum =", value, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumNotEqualTo(Integer value) {
            addCriterion("biz_scene_enum <>", value, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumGreaterThan(Integer value) {
            addCriterion("biz_scene_enum >", value, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumGreaterThanOrEqualTo(Integer value) {
            addCriterion("biz_scene_enum >=", value, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumLessThan(Integer value) {
            addCriterion("biz_scene_enum <", value, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumLessThanOrEqualTo(Integer value) {
            addCriterion("biz_scene_enum <=", value, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumIn(List<Integer> values) {
            addCriterion("biz_scene_enum in", values, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumNotIn(List<Integer> values) {
            addCriterion("biz_scene_enum not in", values, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumBetween(Integer value1, Integer value2) {
            addCriterion("biz_scene_enum between", value1, value2, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andBizSceneEnumNotBetween(Integer value1, Integer value2) {
            addCriterion("biz_scene_enum not between", value1, value2, "bizSceneEnum");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andRuleIsNull() {
            addCriterion("rule is null");
            return (Criteria) this;
        }

        public Criteria andRuleIsNotNull() {
            addCriterion("rule is not null");
            return (Criteria) this;
        }

        public Criteria andRuleEqualTo(String value) {
            addCriterion("rule =", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotEqualTo(String value) {
            addCriterion("rule <>", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThan(String value) {
            addCriterion("rule >", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThanOrEqualTo(String value) {
            addCriterion("rule >=", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLessThan(String value) {
            addCriterion("rule <", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLessThanOrEqualTo(String value) {
            addCriterion("rule <=", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLike(String value) {
            addCriterion("rule like", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotLike(String value) {
            addCriterion("rule not like", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleIn(List<String> values) {
            addCriterion("rule in", values, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotIn(List<String> values) {
            addCriterion("rule not in", values, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleBetween(String value1, String value2) {
            addCriterion("rule between", value1, value2, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotBetween(String value1, String value2) {
            addCriterion("rule not between", value1, value2, "rule");
            return (Criteria) this;
        }

        public Criteria andSceneDescIsNull() {
            addCriterion("scene_desc is null");
            return (Criteria) this;
        }

        public Criteria andSceneDescIsNotNull() {
            addCriterion("scene_desc is not null");
            return (Criteria) this;
        }

        public Criteria andSceneDescEqualTo(String value) {
            addCriterion("scene_desc =", value, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescNotEqualTo(String value) {
            addCriterion("scene_desc <>", value, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescGreaterThan(String value) {
            addCriterion("scene_desc >", value, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescGreaterThanOrEqualTo(String value) {
            addCriterion("scene_desc >=", value, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescLessThan(String value) {
            addCriterion("scene_desc <", value, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescLessThanOrEqualTo(String value) {
            addCriterion("scene_desc <=", value, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescLike(String value) {
            addCriterion("scene_desc like", value, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescNotLike(String value) {
            addCriterion("scene_desc not like", value, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescIn(List<String> values) {
            addCriterion("scene_desc in", values, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescNotIn(List<String> values) {
            addCriterion("scene_desc not in", values, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescBetween(String value1, String value2) {
            addCriterion("scene_desc between", value1, value2, "sceneDesc");
            return (Criteria) this;
        }

        public Criteria andSceneDescNotBetween(String value1, String value2) {
            addCriterion("scene_desc not between", value1, value2, "sceneDesc");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_code_template
     *
     * @mbg.generated do_not_delete_during_merge Wed Apr 13 13:41:01 CST 2022
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}