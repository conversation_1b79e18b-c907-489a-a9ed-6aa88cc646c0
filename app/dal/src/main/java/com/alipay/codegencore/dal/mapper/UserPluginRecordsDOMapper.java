package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.UserPluginRecordsDOExample;
import com.alipay.codegencore.model.domain.UserPluginRecordsDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface UserPluginRecordsDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    long countByExample(UserPluginRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int deleteByExample(UserPluginRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    @Delete({
        "delete from cg_user_plugin_records",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    @Insert({
        "insert into cg_user_plugin_records (gmt_create, gmt_modified, ",
        "user_id, plugin_id, ",
        "control_type, deleted)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{userId,jdbcType=BIGINT}, #{pluginId,jdbcType=BIGINT}, ",
        "#{controlType,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(UserPluginRecordsDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int insertSelective(UserPluginRecordsDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    List<UserPluginRecordsDO> selectByExample(UserPluginRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, user_id, plugin_id, control_type, deleted",
        "from cg_user_plugin_records",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("com.alipay.codegencore.dal.mapper.UserPluginRecordsDOMapper.BaseResultMap")
    UserPluginRecordsDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int updateByExampleSelective(@Param("record") UserPluginRecordsDO record, @Param("example") UserPluginRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int updateByExample(@Param("record") UserPluginRecordsDO record, @Param("example") UserPluginRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int updateByPrimaryKeySelective(UserPluginRecordsDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_plugin_records
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    @Update({
        "update cg_user_plugin_records",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "user_id = #{userId,jdbcType=BIGINT},",
          "plugin_id = #{pluginId,jdbcType=BIGINT},",
          "control_type = #{controlType,jdbcType=INTEGER},",
          "deleted = #{deleted,jdbcType=TINYINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(UserPluginRecordsDO record);
    /**
     * 查询可见权限和编辑权限的工具id列表
     *
     * <AUTHOR>
     * @since 2024.08.14
     * @param userId userId
     * @return java.util.List<java.lang.Long>
     */
    @Select({
            "select plugin_id "
            + "from cg_user_plugin_records "
            + "where user_id = #{userId,jdbcType=BIGINT} "
            + "and deleted = 0"
    })
    List<Long> selectPluginIdListByUserId(@Param("userId") Long userId);
    /**
     * 查询编辑权限的工具id列表
     *
     * <AUTHOR>
     * @since 2024.08.14
     * @param userId userId
     * @return java.util.List<java.lang.Long>
     */
    @Select({
            "select plugin_id "
            + "from cg_user_plugin_records "
            + "where user_id = #{userId,jdbcType=BIGINT} "
            + "and deleted = 0 "
            + "and control_type = 2"
    })
    List<Long> selectEditPluginIdListByUserId(@Param("userId") Long userId);

}