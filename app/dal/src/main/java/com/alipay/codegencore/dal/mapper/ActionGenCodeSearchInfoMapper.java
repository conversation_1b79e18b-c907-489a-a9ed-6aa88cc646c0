package com.alipay.codegencore.dal.mapper;


import com.alipay.codegencore.model.domain.ActionGenCodeSearchInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 2024/11/1 15:38
 */
public interface ActionGenCodeSearchInfoMapper {

    /**
     * 插入一条搜索结果数据
     * @param sessionId
     * @param query
     * @param recall
     * @return
     */
    int insert(@Param("sessionId") String sessionId,
               @Param("query") String query,
               @Param("recall") String recall);

    /**
     * 根据 sessionId 查询搜索结果数据
     * @param sessionId
     * @return
     */
    List<ActionGenCodeSearchInfoDO> getBySessionId(@Param("sessionId") String sessionId);

}
