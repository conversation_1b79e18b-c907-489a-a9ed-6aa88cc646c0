package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.domain.AnswerIndexBuildTaskDO;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

public interface AnswerIndexBuildTaskMapper {

    /**
     * 插入一个任务
     * @param taskDO
     */
    void insertTask(AnswerIndexBuildTaskDO taskDO);

    /**
     * 根据id查询任务
     * @param id
     * @return
     */
    AnswerIndexBuildTaskDO getById(Long id);

    /**
     * 根据仓库信息查询任务
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    AnswerIndexBuildTaskDO getByRepoInfo(@Param("groupPath") String groupPath, @Param("projectPath") String projectPath, @Param("branch") String branch);


    /**
     * 根据仓库信息获取仓库问题
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    AnswerIndexBuildTaskDO getQuestion(@Param("groupPath") String groupPath, @Param("projectPath") String projectPath, @Param("branch") String branch);

    /**
     * 更新任务状态
     * @param taskId
     * @param state
     * @param lastBuildStartTime
     * @return
     */
    int updateTaskBuilding(@Param("taskId") Long taskId, @Param("state") String state,
                           @Param("lastBuildCommit") String lastBuildCommit,
                           @Param("taskRecordId") Long taskRecordId,
                           @Param("lastBuildStartTime") Timestamp lastBuildStartTime);

    /**
     * 构建任务完成
     * @param taskId
     * @param lastBuildEndTime
     * @return
     */
    int updateTaskFinish(@Param("taskId") Long taskId, @Param("lastBuildEndTime") Timestamp lastBuildEndTime);

    /**
     * 更新 wiki 信息
     * @param taskId
     * @param summaryStatus
     * @param question
     * @return
     */
    int updateWikiInfo(@Param("taskId") Long taskId, @Param("summaryStatus") String summaryStatus, @Param("question") String question);

    /**
     *
     * @return
     */
    List<Long> queryBuildingTaskId(@Param("state") String state, @Param("buildScopeType") String buildScopeType,
                                   @Param("limit") Integer limit);

    /**
     *
     * @return
     */
    List<AnswerIndexBuildTaskDO> queryBuildingTask(@Param("state") String state, @Param("buildScopeType") String buildScopeType,
                                   @Param("limit") Integer limit);

    /**
     * 删除任务
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    int deleteByRepoInfo(@Param("groupPath") String groupPath, @Param("projectPath") String projectPath, @Param("branch") String branch);

    /**
     * 根据id和时间查询任务
     *
     * @param id
     * @param startTime
     * @param endTime
     * @return
     */
    List<AnswerIndexBuildTaskDO> getTaskByIdAndTime(@Param("id") Long id, @Param("startTime") String startTime,
                                                    @Param("endTime") String endTime);

    /**
     * 根据id查询任务
     */
    List<AnswerIndexBuildTaskDO> getTaskById(@Param("id") Long id);
}
