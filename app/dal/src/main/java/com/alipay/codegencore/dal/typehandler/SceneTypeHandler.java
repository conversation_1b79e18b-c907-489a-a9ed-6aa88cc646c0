package com.alipay.codegencore.dal.typehandler;

import com.alipay.codegencore.model.enums.SceneTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @version : SceneTypeHandler.java, v 0.1 2023年10月25日 17:03 baoping Exp $
 */
@MappedJdbcTypes(JdbcType.TINYINT)
@MappedTypes(value = {SceneTypeEnum.class})
public class SceneTypeHandler implements TypeHandler<SceneTypeEnum>  {
    /**
     * 将枚举转为 int
     */
    @Override
    public void setParameter(PreparedStatement preparedStatement, int i, SceneTypeEnum userStatusEnum, JdbcType jdbcType) throws SQLException {
        preparedStatement.setInt(i, userStatusEnum.getValue());
    }

    /**
     * 根据枚举值获取枚举
     */
    @Override
    public SceneTypeEnum getResult(ResultSet resultSet, String name) throws SQLException {
        return SceneTypeEnum.getSceneTypeEnumByValue(resultSet.getInt(name));
    }

    /**
     * 根据枚举值获取枚举
     */
    @Override
    public SceneTypeEnum getResult(ResultSet resultSet, int index) throws SQLException {
        return SceneTypeEnum.getSceneTypeEnumByValue(resultSet.getInt(index));
    }

    /**
     * 根据枚举值获取枚举
     */
    @Override
    public SceneTypeEnum getResult(CallableStatement callableStatement, int index) throws SQLException {
        return SceneTypeEnum.getSceneTypeEnumByValue(callableStatement.getInt(index));
    }
}
