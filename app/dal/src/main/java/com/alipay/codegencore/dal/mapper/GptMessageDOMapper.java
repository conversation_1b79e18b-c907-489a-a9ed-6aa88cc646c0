package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.GptMessageDOExample;
import com.alipay.codegencore.model.domain.GptMessageDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface GptMessageDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    long countByExample(GptMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    int deleteByExample(GptMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    @Delete({
        "delete from links_gpt_message",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    @Insert({
        "insert into links_gpt_message (gmt_create, gmt_modified, ",
        "deleted, mongo_id, ",
        "conversation_id, user_id, ",
        "type, content, replay_message_id, ",
        "gpt_model, source_id, ",
        "ext_info)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{deleted,jdbcType=TINYINT}, #{mongoId,jdbcType=VARCHAR}, ",
        "#{conversationId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, ",
        "#{type,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{replayMessageId,jdbcType=VARCHAR}, ",
        "#{gptModel,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, ",
        "#{extInfo,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(GptMessageDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    int insertSelective(GptMessageDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    List<GptMessageDO> selectByExample(GptMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, deleted, mongo_id, conversation_id, user_id, type, ",
        "content, replay_message_id, gpt_model, source_id, ext_info",
        "from links_gpt_message",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("com.alipay.codegencore.dal.mapper.GptMessageDOMapper.BaseResultMap")
    GptMessageDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    int updateByExampleSelective(@Param("record") GptMessageDO record, @Param("example") GptMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    int updateByExample(@Param("record") GptMessageDO record, @Param("example") GptMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    int updateByPrimaryKeySelective(GptMessageDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    @Update({
        "update links_gpt_message",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "deleted = #{deleted,jdbcType=TINYINT},",
          "mongo_id = #{mongoId,jdbcType=VARCHAR},",
          "conversation_id = #{conversationId,jdbcType=VARCHAR},",
          "user_id = #{userId,jdbcType=VARCHAR},",
          "type = #{type,jdbcType=VARCHAR},",
          "content = #{content,jdbcType=VARCHAR},",
          "replay_message_id = #{replayMessageId,jdbcType=VARCHAR},",
          "gpt_model = #{gptModel,jdbcType=VARCHAR},",
          "source_id = #{sourceId,jdbcType=VARCHAR},",
          "ext_info = #{extInfo,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(GptMessageDO record);
}