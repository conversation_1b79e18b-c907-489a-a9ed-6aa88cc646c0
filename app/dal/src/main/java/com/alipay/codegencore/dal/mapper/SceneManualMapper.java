/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.domain.SceneDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @version SceneManualMapper.java, v 0.1 2023年12月28日 下午5:35 lqb01337046
 */
public interface SceneManualMapper extends SceneDOMapper {
    @Select({"<script>"
            + "select  id "
            + " from    cg_scene "
            + "where   deleted = 0  "
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + "and (owner_user_id = #{userId} or user_id = #{userId} or id  in (select scene_id  from cg_user_scene_records where deleted"
            + " = 0 and user_id"
            + " = #{userId} and control_type = 2))  "
            + " order by gmt_create DESC,usage_count DESC LIMIT #{pageNo},#{pageSize}  "
            + "</script>"})
    List<Long> getEditableSceneByUser(@Param("userId") Long userId, @Param("query") String query, @Param("pageNo") int pageNo,
                                         @Param("pageSize") int pageSize);

    @Select({"<script>"
            + "select count(0) "
            + "from    cg_scene "
            + "where   deleted = 0  "
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + "and (owner_user_id = #{userId} or user_id = #{userId} or id  in (select scene_id  from cg_user_scene_records where deleted"
            + " = 0 and user_id"
            + " = #{userId} and control_type = 2))  "
            + "</script>"})
    Long getEditableSceneByUserCount(@Param("userId") Long userId, @Param("query") String query);

    @Select({"<script>"
            + "select count(0) "
            + "from    cg_scene "
            + "where   (owner_user_id = #{userId} or user_id = #{userId})"
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + "</script>"})
    Long getMySceneByUserCount(@Param("userId") Long userId, @Param("query") String query);

    @Select({"<script>"
            + "select id "
            + "from    cg_scene "
            + "where   (owner_user_id = #{userId} or user_id = #{userId})"
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + "order by gmt_create DESC,usage_count DESC LIMIT #{pageNo},#{pageSize}"
            + "</script>"})
    List<Long> getMySceneByUser(@Param("userId") Long userId, @Param("query") String query, @Param("pageNo") int pageNo,
    @Param("pageSize") int pageSize);

    @Select({"<script>"
            + "select  id "
            + " from    cg_scene "
            + "where   deleted = 0  "
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + " order by gmt_create DESC,usage_count DESC LIMIT #{pageNo},#{pageSize}  "
            + "</script>"})
    List<Long> getEditableSceneByAdmin(@Param("userId") Long userId, @Param("query") String query, @Param("pageNo") int pageNo,
                                          @Param("pageSize") int pageSize);

    @Select({"<script>"
            + "select count(0) "
            + "from    cg_scene "
            + "where   deleted = 0  "
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + "</script>"})
    Long getEditableSceneByAdminCount(@Param("userId") Long userId, @Param("query") String query);

    @Select({"<script>"
            + "select  id "
            + " from    cg_scene "
            + "where   deleted = 0 and enable = 1"
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + "and ( (visable_user = 2 and audit_status = 2)  or owner_user_id = #{userId} or user_id = #{userId} or id  in (select "
            + "scene_id  from cg_user_scene_records where deleted = 0 and user_id"
            + " = #{userId}))  "
            + " order by scene_sort DESC,usage_count DESC  "
            + "</script>"})
    List<Long> getAllSceneByEnable(@Param("userId") Long userId, @Param("query") String query);

    @Select({"<script>"
            + "select  id "
            + " from    cg_scene "
            + "where   deleted = 0 and  enable = 1 "
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + " order by scene_sort DESC,usage_count DESC  "
            + "</script>"})
    List<Long> getAdminAllSceneByEnable(@Param("userId") Long userId, @Param("query") String query);

    @Select({"<script>"
            + "select  id "
            + " from    cg_scene "
            + "where   deleted = 0 and plugin_enable = 1 "
            + "and ( (visable_user = 2 and audit_status = 2)  or owner_user_id = #{userId} or user_id = #{userId} or id  in (select "
            + "scene_id  from cg_user_scene_records where deleted = 0 and user_id"
            + " = #{userId}))  "
            + " order by scene_sort DESC,usage_count DESC  "
            + "</script>"})
    List<Long> getAllSceneByPluginEnable(@Param("userId") Long userId);

    @Select({"<script>"
            + "select  id "
            + " from    cg_scene "
            + "where   deleted = 0 and  plugin_enable = 1 "
            + " order by scene_sort DESC,usage_count DESC  "
            + "</script>"})
    List<Long> getAdminAllSceneByPluginEnable();

    @Select({
            "SELECT",
            "cs.id, cs.gmt_create, cs.gmt_modified, cs.name, cs.description, cs.system_prompt, cs.deleted, cs.query_template_list_json, ",
            "cs.mode, cs.model, cs.plugin_list, cs.user_id, cs.visable_user, cs.usage_count, cs.audit_status, ",
            "cs.enable, cs.scene_tag, cs.visable_env, cs.multi_round_support, cs.function_call_config, cs.biz_id, ",
            "cs.scene_type, cs.icon_url, cs.icon_background_color, cs.usage_user_count, cs.usage_message_count, ",
            "cs.owner_user_id, cs.recommend_scene, cs.scene_sort, cs.use_instructions",
            "FROM cg_scene cs",
            "JOIN cg_chat_session ccs ON cs.id = ccs.scene_id",
            "JOIN cg_chat_message ccm ON ccs.uid = ccm.session_uid",
            "WHERE ccm.uid = #{messageUid,jdbcType=VARCHAR}"
    })
    SceneDO selectByMessageUid(String messageUid);
}
