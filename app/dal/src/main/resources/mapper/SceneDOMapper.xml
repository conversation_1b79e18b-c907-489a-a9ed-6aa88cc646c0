<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.SceneDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.SceneDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="system_prompt" jdbcType="VARCHAR" property="systemPrompt" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="query_template_list_json" jdbcType="VARCHAR" property="queryTemplateListJson" />
    <result column="mode" jdbcType="INTEGER" property="mode" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="plugin_list" jdbcType="VARCHAR" property="pluginList" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="visable_user" jdbcType="INTEGER" property="visableUser" />
    <result column="usage_count" jdbcType="BIGINT" property="usageCount" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="scene_tag" jdbcType="VARCHAR" property="sceneTag" />
    <result column="visable_env" jdbcType="INTEGER" property="visableEnv" />
    <result column="multi_round_support" jdbcType="TINYINT" property="multiRoundSupport" />
    <result column="function_call_config" jdbcType="VARCHAR" property="functionCallConfig" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="scene_type" jdbcType="TINYINT" property="sceneType" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="icon_background_color" jdbcType="VARCHAR" property="iconBackgroundColor" />
    <result column="usage_user_count" jdbcType="BIGINT" property="usageUserCount" />
    <result column="usage_message_count" jdbcType="BIGINT" property="usageMessageCount" />
    <result column="owner_user_id" jdbcType="BIGINT" property="ownerUserId" />
    <result column="recommend_scene" jdbcType="TINYINT" property="recommendScene" />
    <result column="scene_sort" jdbcType="INTEGER" property="sceneSort" />
    <result column="use_instructions" jdbcType="VARCHAR" property="useInstructions" />
    <result column="document_uid_list" jdbcType="VARCHAR" property="documentUidList" />
    <result column="yuque_token_list" jdbcType="VARCHAR" property="yuqueTokenList" />
    <result column="plugin_command" jdbcType="VARCHAR" property="pluginCommand" />
    <result column="plugin_enable" jdbcType="TINYINT" property="pluginEnable" />
    <result column="scene_tips" jdbcType="VARCHAR" property="sceneTips" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    id, gmt_create, gmt_modified, name, description, system_prompt, deleted, query_template_list_json, 
    mode, model, plugin_list, user_id, visable_user, usage_count, audit_status, enable, 
    scene_tag, visable_env, multi_round_support, function_call_config, biz_id, scene_type, 
    icon_url, icon_background_color, usage_user_count, usage_message_count, owner_user_id, 
    recommend_scene, scene_sort, use_instructions, document_uid_list, yuque_token_list, 
    plugin_command, plugin_enable, scene_tips
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.SceneDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_scene
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.SceneDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    delete from cg_scene
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.SceneDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_scene
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="systemPrompt != null">
        system_prompt,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="queryTemplateListJson != null">
        query_template_list_json,
      </if>
      <if test="mode != null">
        mode,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="pluginList != null">
        plugin_list,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="visableUser != null">
        visable_user,
      </if>
      <if test="usageCount != null">
        usage_count,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="sceneTag != null">
        scene_tag,
      </if>
      <if test="visableEnv != null">
        visable_env,
      </if>
      <if test="multiRoundSupport != null">
        multi_round_support,
      </if>
      <if test="functionCallConfig != null">
        function_call_config,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="sceneType != null">
        scene_type,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="iconBackgroundColor != null">
        icon_background_color,
      </if>
      <if test="usageUserCount != null">
        usage_user_count,
      </if>
      <if test="usageMessageCount != null">
        usage_message_count,
      </if>
      <if test="ownerUserId != null">
        owner_user_id,
      </if>
      <if test="recommendScene != null">
        recommend_scene,
      </if>
      <if test="sceneSort != null">
        scene_sort,
      </if>
      <if test="useInstructions != null">
        use_instructions,
      </if>
      <if test="documentUidList != null">
        document_uid_list,
      </if>
      <if test="yuqueTokenList != null">
        yuque_token_list,
      </if>
      <if test="pluginCommand != null">
        plugin_command,
      </if>
      <if test="pluginEnable != null">
        plugin_enable,
      </if>
      <if test="sceneTips != null">
        scene_tips,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="systemPrompt != null">
        #{systemPrompt,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="queryTemplateListJson != null">
        #{queryTemplateListJson,jdbcType=VARCHAR},
      </if>
      <if test="mode != null">
        #{mode,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="pluginList != null">
        #{pluginList,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="visableUser != null">
        #{visableUser,jdbcType=INTEGER},
      </if>
      <if test="usageCount != null">
        #{usageCount,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
      <if test="sceneTag != null">
        #{sceneTag,jdbcType=VARCHAR},
      </if>
      <if test="visableEnv != null">
        #{visableEnv,jdbcType=INTEGER},
      </if>
      <if test="multiRoundSupport != null">
        #{multiRoundSupport,jdbcType=TINYINT},
      </if>
      <if test="functionCallConfig != null">
        #{functionCallConfig,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="sceneType != null">
        #{sceneType,jdbcType=TINYINT},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="iconBackgroundColor != null">
        #{iconBackgroundColor,jdbcType=VARCHAR},
      </if>
      <if test="usageUserCount != null">
        #{usageUserCount,jdbcType=BIGINT},
      </if>
      <if test="usageMessageCount != null">
        #{usageMessageCount,jdbcType=BIGINT},
      </if>
      <if test="ownerUserId != null">
        #{ownerUserId,jdbcType=BIGINT},
      </if>
      <if test="recommendScene != null">
        #{recommendScene,jdbcType=TINYINT},
      </if>
      <if test="sceneSort != null">
        #{sceneSort,jdbcType=INTEGER},
      </if>
      <if test="useInstructions != null">
        #{useInstructions,jdbcType=VARCHAR},
      </if>
      <if test="documentUidList != null">
        #{documentUidList,jdbcType=VARCHAR},
      </if>
      <if test="yuqueTokenList != null">
        #{yuqueTokenList,jdbcType=VARCHAR},
      </if>
      <if test="pluginCommand != null">
        #{pluginCommand,jdbcType=VARCHAR},
      </if>
      <if test="pluginEnable != null">
        #{pluginEnable,jdbcType=TINYINT},
      </if>
      <if test="sceneTips != null">
        #{sceneTips,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.SceneDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    select count(*) from cg_scene
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    update cg_scene
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.systemPrompt != null">
        system_prompt = #{record.systemPrompt,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=INTEGER},
      </if>
      <if test="record.queryTemplateListJson != null">
        query_template_list_json = #{record.queryTemplateListJson,jdbcType=VARCHAR},
      </if>
      <if test="record.mode != null">
        mode = #{record.mode,jdbcType=INTEGER},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.pluginList != null">
        plugin_list = #{record.pluginList,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.visableUser != null">
        visable_user = #{record.visableUser,jdbcType=INTEGER},
      </if>
      <if test="record.usageCount != null">
        usage_count = #{record.usageCount,jdbcType=BIGINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.enable != null">
        enable = #{record.enable,jdbcType=INTEGER},
      </if>
      <if test="record.sceneTag != null">
        scene_tag = #{record.sceneTag,jdbcType=VARCHAR},
      </if>
      <if test="record.visableEnv != null">
        visable_env = #{record.visableEnv,jdbcType=INTEGER},
      </if>
      <if test="record.multiRoundSupport != null">
        multi_round_support = #{record.multiRoundSupport,jdbcType=TINYINT},
      </if>
      <if test="record.functionCallConfig != null">
        function_call_config = #{record.functionCallConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.sceneType != null">
        scene_type = #{record.sceneType,jdbcType=TINYINT},
      </if>
      <if test="record.iconUrl != null">
        icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.iconBackgroundColor != null">
        icon_background_color = #{record.iconBackgroundColor,jdbcType=VARCHAR},
      </if>
      <if test="record.usageUserCount != null">
        usage_user_count = #{record.usageUserCount,jdbcType=BIGINT},
      </if>
      <if test="record.usageMessageCount != null">
        usage_message_count = #{record.usageMessageCount,jdbcType=BIGINT},
      </if>
      <if test="record.ownerUserId != null">
        owner_user_id = #{record.ownerUserId,jdbcType=BIGINT},
      </if>
      <if test="record.recommendScene != null">
        recommend_scene = #{record.recommendScene,jdbcType=TINYINT},
      </if>
      <if test="record.sceneSort != null">
        scene_sort = #{record.sceneSort,jdbcType=INTEGER},
      </if>
      <if test="record.useInstructions != null">
        use_instructions = #{record.useInstructions,jdbcType=VARCHAR},
      </if>
      <if test="record.documentUidList != null">
        document_uid_list = #{record.documentUidList,jdbcType=VARCHAR},
      </if>
      <if test="record.yuqueTokenList != null">
        yuque_token_list = #{record.yuqueTokenList,jdbcType=VARCHAR},
      </if>
      <if test="record.pluginCommand != null">
        plugin_command = #{record.pluginCommand,jdbcType=VARCHAR},
      </if>
      <if test="record.pluginEnable != null">
        plugin_enable = #{record.pluginEnable,jdbcType=TINYINT},
      </if>
      <if test="record.sceneTips != null">
        scene_tips = #{record.sceneTips,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    update cg_scene
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      name = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      system_prompt = #{record.systemPrompt,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=INTEGER},
      query_template_list_json = #{record.queryTemplateListJson,jdbcType=VARCHAR},
      mode = #{record.mode,jdbcType=INTEGER},
      model = #{record.model,jdbcType=VARCHAR},
      plugin_list = #{record.pluginList,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      visable_user = #{record.visableUser,jdbcType=INTEGER},
      usage_count = #{record.usageCount,jdbcType=BIGINT},
      audit_status = #{record.auditStatus,jdbcType=INTEGER},
      enable = #{record.enable,jdbcType=INTEGER},
      scene_tag = #{record.sceneTag,jdbcType=VARCHAR},
      visable_env = #{record.visableEnv,jdbcType=INTEGER},
      multi_round_support = #{record.multiRoundSupport,jdbcType=TINYINT},
      function_call_config = #{record.functionCallConfig,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      scene_type = #{record.sceneType,jdbcType=TINYINT},
      icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      icon_background_color = #{record.iconBackgroundColor,jdbcType=VARCHAR},
      usage_user_count = #{record.usageUserCount,jdbcType=BIGINT},
      usage_message_count = #{record.usageMessageCount,jdbcType=BIGINT},
      owner_user_id = #{record.ownerUserId,jdbcType=BIGINT},
      recommend_scene = #{record.recommendScene,jdbcType=TINYINT},
      scene_sort = #{record.sceneSort,jdbcType=INTEGER},
      use_instructions = #{record.useInstructions,jdbcType=VARCHAR},
      document_uid_list = #{record.documentUidList,jdbcType=VARCHAR},
      yuque_token_list = #{record.yuqueTokenList,jdbcType=VARCHAR},
      plugin_command = #{record.pluginCommand,jdbcType=VARCHAR},
      plugin_enable = #{record.pluginEnable,jdbcType=TINYINT},
      scene_tips = #{record.sceneTips,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.SceneDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 16:25:51 CST 2024.
    -->
    update cg_scene
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="systemPrompt != null">
        system_prompt = #{systemPrompt,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="queryTemplateListJson != null">
        query_template_list_json = #{queryTemplateListJson,jdbcType=VARCHAR},
      </if>
      <if test="mode != null">
        mode = #{mode,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="pluginList != null">
        plugin_list = #{pluginList,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="visableUser != null">
        visable_user = #{visableUser,jdbcType=INTEGER},
      </if>
      <if test="usageCount != null">
        usage_count = #{usageCount,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=INTEGER},
      </if>
      <if test="sceneTag != null">
        scene_tag = #{sceneTag,jdbcType=VARCHAR},
      </if>
      <if test="visableEnv != null">
        visable_env = #{visableEnv,jdbcType=INTEGER},
      </if>
      <if test="multiRoundSupport != null">
        multi_round_support = #{multiRoundSupport,jdbcType=TINYINT},
      </if>
      <if test="functionCallConfig != null">
        function_call_config = #{functionCallConfig,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="sceneType != null">
        scene_type = #{sceneType,jdbcType=TINYINT},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="iconBackgroundColor != null">
        icon_background_color = #{iconBackgroundColor,jdbcType=VARCHAR},
      </if>
      <if test="usageUserCount != null">
        usage_user_count = #{usageUserCount,jdbcType=BIGINT},
      </if>
      <if test="usageMessageCount != null">
        usage_message_count = #{usageMessageCount,jdbcType=BIGINT},
      </if>
      <if test="ownerUserId != null">
        owner_user_id = #{ownerUserId,jdbcType=BIGINT},
      </if>
      <if test="recommendScene != null">
        recommend_scene = #{recommendScene,jdbcType=TINYINT},
      </if>
      <if test="sceneSort != null">
        scene_sort = #{sceneSort,jdbcType=INTEGER},
      </if>
      <if test="useInstructions != null">
        use_instructions = #{useInstructions,jdbcType=VARCHAR},
      </if>
      <if test="documentUidList != null">
        document_uid_list = #{documentUidList,jdbcType=VARCHAR},
      </if>
      <if test="yuqueTokenList != null">
        yuque_token_list = #{yuqueTokenList,jdbcType=VARCHAR},
      </if>
      <if test="pluginCommand != null">
        plugin_command = #{pluginCommand,jdbcType=VARCHAR},
      </if>
      <if test="pluginEnable != null">
        plugin_enable = #{pluginEnable,jdbcType=TINYINT},
      </if>
      <if test="sceneTips != null">
        scene_tips = #{sceneTips,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>