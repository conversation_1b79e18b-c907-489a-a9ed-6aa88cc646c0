<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.PluginDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.PluginDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="workflow_config" jdbcType="VARCHAR" property="workflowConfig" />
    <result column="enable" jdbcType="INTEGER" property="enable" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="usage_count" jdbcType="BIGINT" property="usageCount" />
    <result column="owner_user_id" jdbcType="BIGINT" property="ownerUserId" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="icon_background_color" jdbcType="VARCHAR" property="iconBackgroundColor" />
    <result column="use_instructions" jdbcType="VARCHAR" property="useInstructions" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="aci_info" jdbcType="VARCHAR" property="aciInfo" />
    <result column="visable_user" jdbcType="INTEGER" property="visableUser" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    id, gmt_create, gmt_modified, name, description, workflow_config, enable, deleted, 
    user_id, usage_count, owner_user_id, icon_url, icon_background_color, use_instructions, 
    type, aci_info, visable_user
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.PluginDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_plugin
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.PluginDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    delete from cg_plugin
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.PluginDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_plugin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="workflowConfig != null">
        workflow_config,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="usageCount != null">
        usage_count,
      </if>
      <if test="ownerUserId != null">
        owner_user_id,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="iconBackgroundColor != null">
        icon_background_color,
      </if>
      <if test="useInstructions != null">
        use_instructions,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="aciInfo != null">
        aci_info,
      </if>
      <if test="visableUser != null">
        visable_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="workflowConfig != null">
        #{workflowConfig,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="usageCount != null">
        #{usageCount,jdbcType=BIGINT},
      </if>
      <if test="ownerUserId != null">
        #{ownerUserId,jdbcType=BIGINT},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="iconBackgroundColor != null">
        #{iconBackgroundColor,jdbcType=VARCHAR},
      </if>
      <if test="useInstructions != null">
        #{useInstructions,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="aciInfo != null">
        #{aciInfo,jdbcType=VARCHAR},
      </if>
      <if test="visableUser != null">
        #{visableUser,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.PluginDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    select count(*) from cg_plugin
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    update cg_plugin
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.workflowConfig != null">
        workflow_config = #{record.workflowConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null">
        enable = #{record.enable,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.usageCount != null">
        usage_count = #{record.usageCount,jdbcType=BIGINT},
      </if>
      <if test="record.ownerUserId != null">
        owner_user_id = #{record.ownerUserId,jdbcType=BIGINT},
      </if>
      <if test="record.iconUrl != null">
        icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.iconBackgroundColor != null">
        icon_background_color = #{record.iconBackgroundColor,jdbcType=VARCHAR},
      </if>
      <if test="record.useInstructions != null">
        use_instructions = #{record.useInstructions,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.aciInfo != null">
        aci_info = #{record.aciInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.visableUser != null">
        visable_user = #{record.visableUser,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    update cg_plugin
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      name = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      workflow_config = #{record.workflowConfig,jdbcType=VARCHAR},
      enable = #{record.enable,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      usage_count = #{record.usageCount,jdbcType=BIGINT},
      owner_user_id = #{record.ownerUserId,jdbcType=BIGINT},
      icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      icon_background_color = #{record.iconBackgroundColor,jdbcType=VARCHAR},
      use_instructions = #{record.useInstructions,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      aci_info = #{record.aciInfo,jdbcType=VARCHAR},
      visable_user = #{record.visableUser,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.PluginDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    update cg_plugin
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="workflowConfig != null">
        workflow_config = #{workflowConfig,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="usageCount != null">
        usage_count = #{usageCount,jdbcType=BIGINT},
      </if>
      <if test="ownerUserId != null">
        owner_user_id = #{ownerUserId,jdbcType=BIGINT},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="iconBackgroundColor != null">
        icon_background_color = #{iconBackgroundColor,jdbcType=VARCHAR},
      </if>
      <if test="useInstructions != null">
        use_instructions = #{useInstructions,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="aciInfo != null">
        aci_info = #{aciInfo,jdbcType=VARCHAR},
      </if>
      <if test="visableUser != null">
        visable_user = #{visableUser,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>