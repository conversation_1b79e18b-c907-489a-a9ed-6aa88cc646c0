<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.RateLimitDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.RateLimitDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="need_limit" jdbcType="TINYINT" property="needLimit" />
    <result column="stop" jdbcType="TINYINT" property="stop" />
    <result column="ignore_list" jdbcType="VARCHAR" property="ignoreList" />
    <result column="window_time_mills" jdbcType="INTEGER" property="windowTimeMills" />
    <result column="window_total_quota" jdbcType="INTEGER" property="windowTotalQuota" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="caller" jdbcType="VARCHAR" property="caller" />
    <result column="target" jdbcType="VARCHAR" property="target" />
    <result column="template" jdbcType="VARCHAR" property="template" />
    <result column="sorted" jdbcType="INTEGER" property="sorted" />
    <result column="mark_priority" jdbcType="TINYINT" property="markPriority" />
    <result column="priority_config" jdbcType="VARCHAR" property="priorityConfig" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    id, gmt_create, gmt_modified, enable, need_limit, stop, ignore_list, window_time_mills, 
    window_total_quota, type, caller, target, template, sorted, mark_priority, priority_config, 
    create_user_id, remark
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.RateLimitDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_rate_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.RateLimitDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    delete from cg_rate_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.RateLimitDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_rate_limit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="needLimit != null">
        need_limit,
      </if>
      <if test="stop != null">
        stop,
      </if>
      <if test="ignoreList != null">
        ignore_list,
      </if>
      <if test="windowTimeMills != null">
        window_time_mills,
      </if>
      <if test="windowTotalQuota != null">
        window_total_quota,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="caller != null">
        caller,
      </if>
      <if test="target != null">
        target,
      </if>
      <if test="template != null">
        template,
      </if>
      <if test="sorted != null">
        sorted,
      </if>
      <if test="markPriority != null">
        mark_priority,
      </if>
      <if test="priorityConfig != null">
        priority_config,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=TINYINT},
      </if>
      <if test="needLimit != null">
        #{needLimit,jdbcType=TINYINT},
      </if>
      <if test="stop != null">
        #{stop,jdbcType=TINYINT},
      </if>
      <if test="ignoreList != null">
        #{ignoreList,jdbcType=VARCHAR},
      </if>
      <if test="windowTimeMills != null">
        #{windowTimeMills,jdbcType=INTEGER},
      </if>
      <if test="windowTotalQuota != null">
        #{windowTotalQuota,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="caller != null">
        #{caller,jdbcType=VARCHAR},
      </if>
      <if test="target != null">
        #{target,jdbcType=VARCHAR},
      </if>
      <if test="template != null">
        #{template,jdbcType=VARCHAR},
      </if>
      <if test="sorted != null">
        #{sorted,jdbcType=INTEGER},
      </if>
      <if test="markPriority != null">
        #{markPriority,jdbcType=TINYINT},
      </if>
      <if test="priorityConfig != null">
        #{priorityConfig,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.RateLimitDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    select count(*) from cg_rate_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    update cg_rate_limit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enable != null">
        enable = #{record.enable,jdbcType=TINYINT},
      </if>
      <if test="record.needLimit != null">
        need_limit = #{record.needLimit,jdbcType=TINYINT},
      </if>
      <if test="record.stop != null">
        stop = #{record.stop,jdbcType=TINYINT},
      </if>
      <if test="record.ignoreList != null">
        ignore_list = #{record.ignoreList,jdbcType=VARCHAR},
      </if>
      <if test="record.windowTimeMills != null">
        window_time_mills = #{record.windowTimeMills,jdbcType=INTEGER},
      </if>
      <if test="record.windowTotalQuota != null">
        window_total_quota = #{record.windowTotalQuota,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.caller != null">
        caller = #{record.caller,jdbcType=VARCHAR},
      </if>
      <if test="record.target != null">
        target = #{record.target,jdbcType=VARCHAR},
      </if>
      <if test="record.template != null">
        template = #{record.template,jdbcType=VARCHAR},
      </if>
      <if test="record.sorted != null">
        sorted = #{record.sorted,jdbcType=INTEGER},
      </if>
      <if test="record.markPriority != null">
        mark_priority = #{record.markPriority,jdbcType=TINYINT},
      </if>
      <if test="record.priorityConfig != null">
        priority_config = #{record.priorityConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    update cg_rate_limit
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      enable = #{record.enable,jdbcType=TINYINT},
      need_limit = #{record.needLimit,jdbcType=TINYINT},
      stop = #{record.stop,jdbcType=TINYINT},
      ignore_list = #{record.ignoreList,jdbcType=VARCHAR},
      window_time_mills = #{record.windowTimeMills,jdbcType=INTEGER},
      window_total_quota = #{record.windowTotalQuota,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      caller = #{record.caller,jdbcType=VARCHAR},
      target = #{record.target,jdbcType=VARCHAR},
      template = #{record.template,jdbcType=VARCHAR},
      sorted = #{record.sorted,jdbcType=INTEGER},
      mark_priority = #{record.markPriority,jdbcType=TINYINT},
      priority_config = #{record.priorityConfig,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.RateLimitDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Dec 29 11:32:12 CST 2023.
    -->
    update cg_rate_limit
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=TINYINT},
      </if>
      <if test="needLimit != null">
        need_limit = #{needLimit,jdbcType=TINYINT},
      </if>
      <if test="stop != null">
        stop = #{stop,jdbcType=TINYINT},
      </if>
      <if test="ignoreList != null">
        ignore_list = #{ignoreList,jdbcType=VARCHAR},
      </if>
      <if test="windowTimeMills != null">
        window_time_mills = #{windowTimeMills,jdbcType=INTEGER},
      </if>
      <if test="windowTotalQuota != null">
        window_total_quota = #{windowTotalQuota,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="caller != null">
        caller = #{caller,jdbcType=VARCHAR},
      </if>
      <if test="target != null">
        target = #{target,jdbcType=VARCHAR},
      </if>
      <if test="template != null">
        template = #{template,jdbcType=VARCHAR},
      </if>
      <if test="sorted != null">
        sorted = #{sorted,jdbcType=INTEGER},
      </if>
      <if test="markPriority != null">
        mark_priority = #{markPriority,jdbcType=TINYINT},
      </if>
      <if test="priorityConfig != null">
        priority_config = #{priorityConfig,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>