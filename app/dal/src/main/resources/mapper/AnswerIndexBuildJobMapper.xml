<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.AnswerIndexBuildJobMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.AnswerIndexBuildJobDO">

    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="group_path" jdbcType="VARCHAR" property="groupPath" />
    <result column="project_path" jdbcType="VARCHAR" property="projectPath" />
    <result column="branch" jdbcType="VARCHAR" property="branch" />
    <result column="last_commit_id" jdbcType="VARCHAR" property="lastCommitId" />
    <result column="old_file_path" jdbcType="VARCHAR" property="oldFilePath" />
    <result column="new_file_path" jdbcType="VARCHAR" property="newFilePath" />
    <result column="file_status" jdbcType="VARCHAR" property="fileStatus" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="fail_count" jdbcType="INTEGER" property="failCount" />
    <result column="fail_message" jdbcType="VARCHAR" property="failMessage" />
    <result column="handle_info" jdbcType="VARCHAR" property="handleInfo" />
  </resultMap>

  <insert id="batchInsertJob" useGeneratedKeys="true" keyProperty="id">
      insert into answer_index_build_job
      (task_id, group_path, project_path, branch, last_commit_id,
      old_file_path, new_file_path, file_status, state)
      values
      <foreach collection="jobList" item="item" separator=",">
          (#{item.taskId}, #{item.groupPath}, #{item.projectPath}, #{item.branch}, #{item.lastCommitId},
        #{item.oldFilePath}, #{item.newFilePath}, #{item.fileStatus}, #{item.state})
      </foreach>
  </insert>

  <select id="getNeedBuildJobs" resultMap="BaseResultMap">
    select id, task_id, group_path, project_path,
    branch, last_commit_id, old_file_path, new_file_path, file_status
    from answer_index_build_job
    where task_id in
    <foreach collection="taskIdList" item="taskId" index="index" open="(" separator="," close=")">
      #{taskId}
    </foreach>
    and state = "INIT"
    order by gmt_modified asc
    limit #{size}
  </select>

  <update id="markJobBuilding">
    update answer_index_build_job
    <set>
      state = "BUILDING", handle_info = #{handleInfo}
    </set>
    <where>
      id in
      <foreach collection="jobIdList" open="(" item="item" separator="," close=")">
          #{item}
      </foreach>
      and state = "INIT"
    </where>
  </update>

  <select id="getById" resultMap="BaseResultMap">
    select id, fail_count
    from answer_index_build_job
    where id = #{jobId}
  </select>

  <update id="markJobBuildingState">
    update answer_index_build_job
    <set>
      state = #{state}
      <if test="state == 'INIT'">
        ,fail_count = fail_count + 1
      </if>
      <if test="failMessage != null">
        ,fail_message = #{failMessage}
      </if>
    </set>
    <where>
      id in
      <foreach collection="jobIdList" open="(" item="item" separator="," close=")">
        #{item}
      </foreach>
      and state = "BUILDING"
    </where>
  </update>

  <select id="queryBuildingTimeout" resultMap="BaseResultMap">
    select id, fail_count
    from answer_index_build_job
    <where>
      task_id in
      <foreach collection="taskIdList" open="(" item="item" separator="," close=")">
        #{item}
      </foreach>
      and state = "BUILDING"
      and gmt_modified &lt; #{timeout}
    </where>
  </select>

  <select id="queryFailJobs" resultMap="BaseResultMap">
    select id, fail_message
    from answer_index_build_job
    where task_id = #{taskId} and `state` = "FAIL"
  </select>

  <select id="countNeedBuildByTaskId" resultType="int">
    select count(id)
    from answer_index_build_job
    where task_id = #{taskId} and state in ("INIT", "BUILDING")
  </select>

  <delete id="deleteByIdList" >
    delete from answer_index_build_job
    where id in
    <foreach collection="idList" open="(" item="item" separator="," close=")">
    #{item}
    </foreach>
  </delete>

  <select id="queryJobIdListLessThanId" resultType="Long">
    select id
    from answer_index_build_job
    where id &lt; #{id}
    order by id desc
    limit #{size}
  </select>

  <update id="retryJobs">
    update answer_index_build_job
    <set>
      state = #{state}
    </set>
    <where>
      id in
      <foreach collection="jobIdList" open="(" item="item" separator="," close=")">
        #{item}
      </foreach>
      and state = "FAIL"
    </where>
  </update>

  <update id="updateJobState">
    update answer_index_build_job
    <set>
      state = #{state}
    </set>
    <where>
      id in
      <foreach collection="jobIdList" open="(" item="item" separator="," close=")">
        #{item}
      </foreach>
    </where>
  </update>

  <select id="getJobByIds" resultMap="BaseResultMap">
    select *
    from answer_index_build_job
    where id in
    <foreach collection="jobIdList" item="jobId" index="index" open="(" separator="," close=")">
      #{jobId}
    </foreach>
  </select>

  <delete id="deleteById" parameterType="long">
    delete from answer_index_build_job
    where task_id = #{taskId}
  </delete>

  <update id="resetFailHandle">
    update answer_index_build_job
    <set>
      state = "INIT", fail_count = 0, fail_message = null
    </set>
    <where>
      task_id = #{taskId}
      and state = 'FAIL'
      and fail_message like CONCAT('%', #{failMessage}, '%')
    </where>
  </update>

</mapper>