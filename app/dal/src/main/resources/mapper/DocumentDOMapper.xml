<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.DocumentDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.DocumentDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="document_name" jdbcType="VARCHAR" property="documentName" />
    <result column="document_size" jdbcType="BIGINT" property="documentSize" />
    <result column="document_status" jdbcType="VARCHAR" property="documentStatus" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="content_oss_url" jdbcType="VARCHAR" property="contentOssUrl" />
    <result column="content_length" jdbcType="BIGINT" property="contentLength" />
    <result column="segment_oss_url" jdbcType="VARCHAR" property="segmentOssUrl" />
    <result column="summary" jdbcType="VARCHAR" property="summary" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
    <result column="zsearch_client" jdbcType="VARCHAR" property="zsearchClient" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    id, gmt_create, gmt_modified, uid, document_name, document_size, document_status, 
    source, content_oss_url, content_length, segment_oss_url, summary, ext_info, create_user_id, 
    update_user_id, zsearch_client
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.DocumentDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_document
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.DocumentDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    delete from cg_document
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.DocumentDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_document
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="documentName != null">
        document_name,
      </if>
      <if test="documentSize != null">
        document_size,
      </if>
      <if test="documentStatus != null">
        document_status,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="contentOssUrl != null">
        content_oss_url,
      </if>
      <if test="contentLength != null">
        content_length,
      </if>
      <if test="segmentOssUrl != null">
        segment_oss_url,
      </if>
      <if test="summary != null">
        summary,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="zsearchClient != null">
        zsearch_client,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=VARCHAR},
      </if>
      <if test="documentName != null">
        #{documentName,jdbcType=VARCHAR},
      </if>
      <if test="documentSize != null">
        #{documentSize,jdbcType=BIGINT},
      </if>
      <if test="documentStatus != null">
        #{documentStatus,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="contentOssUrl != null">
        #{contentOssUrl,jdbcType=VARCHAR},
      </if>
      <if test="contentLength != null">
        #{contentLength,jdbcType=BIGINT},
      </if>
      <if test="segmentOssUrl != null">
        #{segmentOssUrl,jdbcType=VARCHAR},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=BIGINT},
      </if>
      <if test="zsearchClient != null">
        #{zsearchClient,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.DocumentDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    select count(*) from cg_document
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    update cg_document
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.uid != null">
        uid = #{record.uid,jdbcType=VARCHAR},
      </if>
      <if test="record.documentName != null">
        document_name = #{record.documentName,jdbcType=VARCHAR},
      </if>
      <if test="record.documentSize != null">
        document_size = #{record.documentSize,jdbcType=BIGINT},
      </if>
      <if test="record.documentStatus != null">
        document_status = #{record.documentStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.contentOssUrl != null">
        content_oss_url = #{record.contentOssUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.contentLength != null">
        content_length = #{record.contentLength,jdbcType=BIGINT},
      </if>
      <if test="record.segmentOssUrl != null">
        segment_oss_url = #{record.segmentOssUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.summary != null">
        summary = #{record.summary,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=BIGINT},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=BIGINT},
      </if>
      <if test="record.zsearchClient != null">
        zsearch_client = #{record.zsearchClient,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    update cg_document
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      uid = #{record.uid,jdbcType=VARCHAR},
      document_name = #{record.documentName,jdbcType=VARCHAR},
      document_size = #{record.documentSize,jdbcType=BIGINT},
      document_status = #{record.documentStatus,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=VARCHAR},
      content_oss_url = #{record.contentOssUrl,jdbcType=VARCHAR},
      content_length = #{record.contentLength,jdbcType=BIGINT},
      segment_oss_url = #{record.segmentOssUrl,jdbcType=VARCHAR},
      summary = #{record.summary,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=BIGINT},
      update_user_id = #{record.updateUserId,jdbcType=BIGINT},
      zsearch_client = #{record.zsearchClient,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.DocumentDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 15 14:27:02 CST 2024.
    -->
    update cg_document
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=VARCHAR},
      </if>
      <if test="documentName != null">
        document_name = #{documentName,jdbcType=VARCHAR},
      </if>
      <if test="documentSize != null">
        document_size = #{documentSize,jdbcType=BIGINT},
      </if>
      <if test="documentStatus != null">
        document_status = #{documentStatus,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="contentOssUrl != null">
        content_oss_url = #{contentOssUrl,jdbcType=VARCHAR},
      </if>
      <if test="contentLength != null">
        content_length = #{contentLength,jdbcType=BIGINT},
      </if>
      <if test="segmentOssUrl != null">
        segment_oss_url = #{segmentOssUrl,jdbcType=VARCHAR},
      </if>
      <if test="summary != null">
        summary = #{summary,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=BIGINT},
      </if>
      <if test="zsearchClient != null">
        zsearch_client = #{zsearchClient,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>