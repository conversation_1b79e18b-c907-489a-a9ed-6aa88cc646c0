<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alipay</groupId>
		<artifactId>codegencore-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<artifactId>codegencore-web</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<dependencies>
		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>codegencore-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.security</groupId>
			<artifactId>alipay-security-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>web-alipay-sofa-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<!-- 接入aci组件的依赖 -->
		<dependency>
			<groupId>com.alipay.linkede</groupId>
			<artifactId>aclinkelib-common-service-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.linkede</groupId>
			<artifactId>aclinkelib-common-util</artifactId>
		</dependency>
		<!-- 接入aci组件的依赖end -->

		<!-- faas -->
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-function-api</artifactId>
			<version>0.2.0</version>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-function</artifactId>
			<version>0.7.0</version>
		</dependency>
		<!-- faas end-->

	</dependencies>

</project>
