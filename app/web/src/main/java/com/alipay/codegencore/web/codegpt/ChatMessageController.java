package com.alipay.codegencore.web.codegpt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.enums.*;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.request.CodeGPTQueryRequestBean;
import com.alipay.codegencore.model.request.CodeGPTVoteRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.IntentionRecognitionService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.model.StreamDataQueueUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.service.utils.TbaseCacheService;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zcache.serialize.BytesObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 利用ai模型进行对话的controller
 *
 * <AUTHOR>
 * 创建时间 2022-02-28
 */
@Slf4j
@CodeTalkWebApi
@RestController
@RequestMapping("/webapi/message")
public class ChatMessageController {

    private static final Logger LOGGER = LoggerFactory.getLogger( ChatMessageController.class );
    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    @Resource
    private ChatMessageService chatMessageService;

    @Resource
    private UserAclService userAclService;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager;


    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource
    private TbaseCacheService tbaseCacheService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private ChatSessionManageService chatSessionManageService;

    @Resource
    private IntentionRecognitionService intentionRecognitionService;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private SceneService               sceneService;

    @Resource
    private StreamDataQueueUtilService streamDataQueueUtilService;

    /**
     * 流式输出接口
     * @param queryRequest
     * @return
     */
    @PostMapping(value = "/conversation",  produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void conversation(HttpServletResponse httpServletResponse, @RequestBody CodeGPTQueryRequestBean queryRequest) {
        if (!userAclService.isSessionBelongToUser(queryRequest.getSessionUid())) {
            log.info("session:{} does not belong to current user", queryRequest.getSessionUid());
            throw new BizException(ResponseEnum.USER_SESSION_NOT_MATCH);
        }
        // 仓库问答请求用量监控
        if(queryRequest!= null && queryRequest.getTryRepoSearch()!=null && queryRequest.getTryRepoSearch()){
            CHAT_LOGGER.warn("repo chat from CodfuseWeb,sessionUid:{},query:{}",queryRequest.getSessionUid(),
                    queryRequest.getContent());
        }
        // fix bug for deleted session
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(queryRequest.getSessionUid());
        if(null == chatSessionDO || chatSessionDO.getDeleted() == 1) {
            log.info("session:{} not existed", queryRequest.getSessionUid());
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }

        //TODO: mock侧边栏的消息格式，中间态临时逻辑，交接完成之后要删掉
        if (Boolean.TRUE.equals(queryRequest.getMockCopilot())){
            ChatUtils.setServletToEventStream(httpServletResponse);
            JSONObject mockSideBar = new JSONObject();
            mockSideBar.put("type", "id");
            mockSideBar.put("id", ShortUid.getUid());
            ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(mockSideBar));
        }

        chatMessageService.conversation(httpServletResponse,
                queryRequest.getSessionUid(),
                queryRequest.getContent(),
                queryRequest.getModified(),
                false,
                null,
                queryRequest.getTryRepoSearch(),
                queryRequest.getExtraInfo());
    }

    /**
     * 流式输出接口，提交表单
     * @return
     */
    @PostMapping(value = "/continueConversation",  produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void continueConversation(HttpServletResponse httpServletResponse, @RequestParam String uid, @RequestBody JSONObject data) {
        if(!userAclService.isMsgBelongToUser(uid)) {
            log.warn("message:{} does not belong to current user", uid);
            throw new BizException(ResponseEnum.USER_SESSION_NOT_MATCH);
        }

        chatMessageService.continueConversation(httpServletResponse, uid, data, false);
    }


    /**
     * 获取最新生成的一条消息的uid
     * @param sessionUid
     * @return
     */
    @GetMapping(value = "/getLatestAssistantMessage")
    public BaseResponse<List<ChatMessageDO>> getLatestAssistantMessage(@RequestParam String sessionUid) {
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", sessionUid));
        }

        List<ChatMessageDO> latestAssistantMessage = chatMessageService.getLatestAssistantMessage(sessionUid);
        return BaseResponse.build(latestAssistantMessage);
    }

    /**
     * 重新生成回答
     */
    @PostMapping(value = "/regenerate", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void regenerate(HttpServletResponse httpServletResponse, @RequestParam String sessionUid,@RequestParam(required = false) String uid) {
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            throw new BizException(ResponseEnum.USER_SESSION_NOT_MATCH);
        }

        // fix bug for deleted session
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
        if(null == chatSessionDO || chatSessionDO.getDeleted() == 1) {
            log.info("session:{} not existed", sessionUid);
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }

        chatMessageService.regenerationAnswer(httpServletResponse, sessionUid, uid, false,null);
    }

    /**
     * 获取会话列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public BaseResponse<List<ChatMessageDO>> list(@RequestParam String sessionUid,
                                        @RequestParam(defaultValue = "false") boolean showMultiGeneratedAnswer,
                                        @RequestParam(defaultValue = "true") boolean rewriteFileAnnotation) {
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", sessionUid));
        }
        List<ChatMessageDO> chatMessageDOList = chatMessageService.listChatMessage(sessionUid, showMultiGeneratedAnswer,
                rewriteFileAnnotation);
        chatMessageDOList = updateContent(chatMessageDOList);
        for (ChatMessageDO chatMessageDO : chatMessageDOList) {
            if (ChatRoleEnum.ASSISTANT.getName().equals(chatMessageDO.getRole()) && chatMessageDO.getContent() == null) {
                chatMessageDO.setContent(AppConstants.UNKNOWN_NULL_CONTENT);
            }
        }
        return BaseResponse.build(chatMessageDOList);
    }

    /**
     * 保存 or 修改模型回复代码的语言
     * @return
     */
    @PostMapping(value = "/putLanguages")
    public BaseResponse<Integer> putLanguages(@RequestParam String uid, @RequestBody JSONArray languages) {
        if(!userAclService.isMsgBelongToUser(uid)) {
            log.warn("message:{} does not belong to current user", uid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", uid));
        }
        return BaseResponse.build(chatMessageService.putLanguages(uid,JSONObject.toJSONString(languages)));
    }

    /**
     * 根据消息的uid获取这个消息的内容
     * @param uid 参数
     * @return 消息内容
     */
    @GetMapping(value = "/getMessage")
    public BaseResponse<ChatMessageDO> getMessage(@RequestParam String uid) {
        ChatMessageDO chatMessageDO = chatMessageService.getChatMessageByUid(uid);
        List<ChatMessageDO> chatMessageDOList = chatMessageService.getChatMessagePairByUid(uid);
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(chatMessageDO.getSessionUid());
        if (chatSessionDO==null || chatSessionDO.getDeleted()==1) {
            log.info("message:{} does not exist", uid);
            return BaseResponse.build(ResponseEnum.MESSAGE_IS_NOT_EXIST, String.format("message:%s does not exist", uid));
        }
        if (!userAclService.isSessionBelongToUser(chatSessionDO.getUid())) {
            log.info("session:{} does not belong to current user", chatSessionDO.getUid());
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", chatSessionDO.getUid()));
        }
        // 更新审核失败的AI回复的文本
        chatMessageService.updateCheckFailedContent(chatMessageDOList);
        for (ChatMessageDO messageDO : chatMessageDOList) {
            if (uid.equals(messageDO.getUid())) {
                chatMessageDO = messageDO;
            }
        }
        if (ChatRoleEnum.ASSISTANT.getName().equals(chatMessageDO.getRole()) && chatMessageDO.getContent() == null) {
            chatMessageDO.setContent(AppConstants.UNKNOWN_NULL_CONTENT);
        }
        return BaseResponse.build(chatMessageDO);
    }



    /**
     * 为模型结果点赞/反对
     * @param codeGPTVoteRequestBean 请求数据
     * @return string
     */
    @PostMapping("/vote")
    public BaseResponse<List<String>> vote(@RequestBody CodeGPTVoteRequestBean codeGPTVoteRequestBean) {
        verifyParam(codeGPTVoteRequestBean);
        ChatMessageDO chatMessageDO = chatMessageService.getChatMessageByUid(codeGPTVoteRequestBean.getUid());
        if (!ChatRoleEnum.ASSISTANT.getName().equals(chatMessageDO.getRole())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"You can only vote the assistant output results.");
        }
        if (!userAclService.isSessionBelongToUser(chatMessageDO.getSessionUid())) {
            log.info("session:{} does not belong to current user", chatMessageDO.getSessionUid());
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", chatMessageDO.getSessionUid()));
        }
        chatMessageService.updateChatMessage(codeGPTVoteRequestBean);
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取更多推荐的内容
     * @param uid msg的uid
     * @return list
     */
    @GetMapping("/getReference")
    public BaseResponse<JSONArray> getReference(@RequestParam String uid) {
        if (StringUtils.isBlank(uid)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"uid is blank.");
        }
        ChatMessageDO chatMessageDO = chatMessageService.getChatMessageByUid(uid);
        if (!userAclService.isSessionBelongToUser(chatMessageDO.getSessionUid())) {
            log.info("session:{} does not belong to current user", chatMessageDO.getSessionUid());
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", chatMessageDO.getSessionUid()));
        }
        // mock获取这个消息的更对推荐内容,可能返回空
        return BaseResponse.build(mockGetReference(uid));
    }

    /**
     * 删除一个会话中的全部消息
     * @param sessionUid 会话id
     * @return
     */
    @PostMapping("/deleteAllMessage")
    public BaseResponse<Void> deleteAllMessage(@RequestParam String sessionUid) {
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", sessionUid));
        }
        chatMessageService.deleteAllMessage(sessionUid);
        return BaseResponse.buildSuccess();
    }


    private JSONArray mockGetReference(String uid) {
        int i = getFirstNum(uid);
        if (i%2 == 0) {
            return new JSONArray();
        }
        return chatMessageService.getMessageReference(uid);
    }

    private static int getFirstNum(String uid) {
        for (char c : uid.toCharArray()) {
            if (Character.isDigit(c)) {
                return Character.getNumericValue(c);
            }
        }
        return 0;
    }

    private void verifyParam(CodeGPTVoteRequestBean codeGPTVoteRequestBean) {
        if (codeGPTVoteRequestBean == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if (StringUtils.isBlank(codeGPTVoteRequestBean.getUid())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"uid is blank");
        }
        if (codeGPTVoteRequestBean.getVote() == null && MapUtils.isEmpty(codeGPTVoteRequestBean.getCheckFeedback())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"vote and checkFeedback is blank");
        }
        if (codeGPTVoteRequestBean.getVote() != null &&
                codeGPTVoteRequestBean.getVote() != 1 &&
                codeGPTVoteRequestBean.getVote() != 0) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"vote is illegal");
        }
    }
    /**
     * 使用ai模型 给出对话标题
     *
     * @param message
     * @return
     */
    @PostMapping(path = "/contentTitle")
    public BaseResponse<String> contentTitle(@RequestParam String sessionUid, @RequestBody ChatMessage message) {
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", sessionUid));
        }
        String codeGPTSystemValue = codeGPTDrmConfig.getCodeGPTSystemValue();
        message.setContent(codeGPTSystemValue + message.getContent());
        List<ChatMessage> chatMessages = new ArrayList<>(Collections.singletonList(message));
        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        chatCompletionRequest.setMessages(chatMessages);
        String model = codeGPTDrmConfig.getGenerateTitleUseModel();
        String chatCompletionResponse = null;
        try {

            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
            if (algoBackendDO == null) {
                throw new BizException(ResponseEnum.MODEL_NOT_EXISTED);
            }
            if (!algoBackendDO.getEnable()) {
                throw new BizException(ResponseEnum.MODEL_UNSERVICEABLE);
            }
            // 更新走审核的字段
            updateCheckSwitch(model,chatCompletionRequest,sessionUid);
            GptAlgModelServiceRequest param = new GptAlgModelServiceRequest(sessionUid, AppConstants.CODEGPT_TOKEN_USER, false, algoBackendDO, chatCompletionRequest);
            param.setUniqueAnswerId(sessionUid + "_title");
            param.setRequestUserId(userAclService.getCurrentUser().getId());
            ChatMessage assistantMessage = AlgoModelExecutor.getInstance().executorChat(algoBackendDO, param);
            chatCompletionResponse = assistantMessage.getContent();
        } catch (Exception e) {
            LOGGER.info("chatCompletionResponse error, use default title",e);
        }

        //标题为空，说明模型调用异常，使用默认标题
        if (StringUtils.isBlank(chatCompletionResponse)) {
            chatCompletionResponse = AppConstants.SESSION_DEFAULT_TITLE;
        } else if (chatCompletionResponse.length() > 20) {
            //预防ai模型返回超过20字符
            chatCompletionResponse = chatCompletionResponse.substring(0, 20);
        }
        //存生成的标题
        chatSessionManageService.updateSessionTitle(sessionUid, chatCompletionResponse);
        return BaseResponse.build(chatCompletionResponse);
    }

    private void updateCheckSwitch(String model, ChatCompletionRequest chatCompletionRequest, String sessionUid) {
        JSONObject codeFuseCheckSwitch = JSONObject.parseObject(codeGPTDrmConfig.getCodeFuseCheckSwitch());
        JSONObject modelCheckSwitch = codeFuseCheckSwitch.getJSONObject(model.toUpperCase());
        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
        chatRequestExtData.setCodeGPTUser(AppConstants.CODEGPT_TOKEN_USER);
        chatRequestExtData.setEmpId(userAclService.getCurrentUser().getEmpId());
        chatRequestExtData.setContentIsTitle(true);
        chatRequestExtData.setSessionUid(sessionUid);
        if (codeFuseCheckSwitch.containsKey(model.toUpperCase())) {
            chatRequestExtData.setAntDsrCheck(modelCheckSwitch.getBoolean(ReviewPlatformEnum.ANTDSR.name()));
            chatRequestExtData.setInfoSecCheck(modelCheckSwitch.getBoolean(ReviewPlatformEnum.INFOSEC.name()));
            chatRequestExtData.setKeymapCheck(modelCheckSwitch.getBoolean(ReviewPlatformEnum.KEYMAP.name()));
        }
        chatCompletionRequest.setChatRequestExtData(chatRequestExtData);
    }

    /**
     * ai回复超时时 更新文本
     *
     * @param chatMessageDOList
     * @return
     */
    private List<ChatMessageDO> updateContent(List<ChatMessageDO> chatMessageDOList) {
        // 更新审核失败的AI回复的文本
        chatMessageService.updateCheckFailedContent(chatMessageDOList);
        return chatMessageDOList.stream().peek(s -> {
            // 设Log为null，单独接口获取
            if (s.getServiceAbnormalResp() != null && ResponseEnum.ANSWER_OUTPUT_TIME_OUT.name().equalsIgnoreCase(s.getServiceAbnormalResp())) {
                s.setErrorMsg(ResponseEnum.ANSWER_OUTPUT_TIME_OUT.getErrorMsg());
            }
            // 用户取消不需要errorMsg
            if(ResponseEnum.USER_CANCELED.name().equalsIgnoreCase(s.getServiceAbnormalResp())){
                s.setErrorMsg(null);
            }
            if(ResponseEnum.PAUSE.name().equalsIgnoreCase(s.getStatus())){
                s.setContent("请填写表单之后继续进行");
            }

        }).collect(Collectors.toList());
    }

    /**
     * 通过uid获取回答的插件日志
     * @param uid
     * @return
     */
    @GetMapping("/getPluginLog")
    public BaseResponse<JSONObject> getPluginLog(@RequestParam String uid) {
        if (StringUtils.isBlank(uid)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "uid is blank.");
        }
        ChatMessageDO chatMessageDO = chatMessageService.getChatMessageByUid(uid);
        if (!userAclService.isSessionBelongToUser(chatMessageDO.getSessionUid())) {
            log.info("session:{} does not belong to current user", chatMessageDO.getSessionUid());
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", chatMessageDO.getSessionUid()));
        }
        if (!StringUtils.equals(chatMessageDO.getRole(), ChatRoleEnum.ASSISTANT.getName())) {
            return BaseResponse.build(ResponseEnum.ONLY_ASSISTANT_HAS_PLUGIN_LOG, String.format("uid:%s role not equals assistant", chatMessageDO.getUid()));
        }
        JSONObject object = JSONObject.parseObject(chatMessageDO.getPluginLog());
        return BaseResponse.build(object);
    }

    /**
     * 取消一次流式问答
     * @param sessionUid 会话Id
     * @param queryIndex 问题楼层
     * @param generationIndex 答案需要（解决重新回答问题场景）
     * @return 执行是否成功
     */
    @GetMapping(value = "/cancelMessage")
    public BaseResponse<ResponseEnum> cancelMessage(@RequestParam String sessionUid,
                                                  @RequestParam(defaultValue = "1") Long queryIndex,
                                                  @RequestParam(defaultValue = "0") Long generationIndex,
                                                  @RequestParam(required = false) String messageUid,
                                                  @RequestParam(required = false) boolean repoSearch) {

        if(log.isInfoEnabled()) {
            log.info("start to cancel message : {}, {}, {}, {}", sessionUid, queryIndex, generationIndex,repoSearch);
        }

        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", sessionUid));
        }

        // 如果uid不为空，则是消息表单取消场景，是非流式的
        if (StringUtils.isNotBlank(messageUid)) {
            ChatMessageDO chatMessageDO = chatMessageService.getChatMessageByUid(messageUid);
            // 4.7 取消PAUS状态检查
            chatMessageDO.setStatus(ChatMessageStatusEnum.FINISH.name());
            chatMessageDO.setContent("用户主动取消");
            chatMessageDO.setServiceAbnormalResp(ResponseEnum.USER_CANCELED.name());
            chatMessageDO.setErrorMsg(null);
            chatMessageService.updateMessage(chatMessageDO);
            return BaseResponse.build(ResponseEnum.SUCCESS);
        }
        generationIndex = chatMessageService.selectLastMessageCount(sessionUid, queryIndex);

        boolean pluginEnabled = isPluginEnabled(sessionUid);
        String uniqueId = sessionUid + "_" + queryIndex + "_" + generationIndex;
        // 兼容插件和非插件情况
        String streamPrefix = pluginEnabled && !repoSearch ? AppConstants.PLUGIN_STREAM_DATA_PREFIX : AppConstants.STREAM_DATA_PREFIX;
        String streamAnswerId = String.format("%s%s", streamPrefix, uniqueId);
        if(log.isInfoEnabled()) {
            log.info("stop tbase key : {}", streamAnswerId);
        }

        JSONObject streamJson = new JSONObject();
        streamJson.put("finishReason", ResponseEnum.USER_CANCELED.name());
        BytesObject bytesObject = new BytesObject(streamJson.toJSONString().getBytes(StandardCharsets.UTF_8));
        Long ret = noneSerializationCacheManager.rpush(streamAnswerId,bytesObject);
        if(0L == ret) {
            String message = String.format("failed to cancel message : %s", streamAnswerId);
            log.warn(message);
            return BaseResponse.build(ResponseEnum.ERROR_THROW, message);
        }

        // 删除tbase数据的key， 这样输入通道能够感知， 并关闭输入通道
        // 减少不必要的内存使用
        String streamInputId = String.format("%s%s", AppConstants.STREAM_INPUT_PREFIX, uniqueId);
        BytesObject bo = new BytesObject(AppConstants.INPUT_CLOSE_TAG.getBytes(StandardCharsets.UTF_8));
        String o = noneSerializationCacheManager.seset(streamInputId, bo, 0, 60);
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("success delete tbase key {}, after cancel", streamInputId);
            if(StringUtils.isNotBlank(o)) {
                LOGGER.info("set tbase and return {}", o);
            }
        }
        if(StringUtils.isBlank(o)) {
            String message = String.format("failed to write AIGC close message : %s, %s, %s", sessionUid, queryIndex, generationIndex);
            log.warn(message);
            return BaseResponse.build(ResponseEnum.ERROR_THROW, message);
        }

        // 同步保证取消操作完成(默认超时为3秒钟)
        boolean lock = tbaseCacheService.getLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid, 1, 3 * 1000);
        if(lock) {
            return BaseResponse.build(ResponseEnum.SUCCESS);
        }

        log.error("取消获取锁超时");
        return BaseResponse.build(ResponseEnum.ERROR_THROW, "取消获取锁超时");
    }


    /**
     * 判断当前会话是否为第一轮对话
     *
     * @param sessionUid 会话唯一标识符
     * @return 如果是第一轮对话则返回true，否则返回false
     */
    private boolean isPluginEnabled(String sessionUid) {
        // 获取聊天会话对象
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);

        // 初始化场景对象
        SceneDO scene = null;
        if (chatSessionDO.getSceneId() != null) {
            // 根据场景ID获取场景对象
            scene = sceneService.getSceneById(chatSessionDO.getSceneId());
            ConversationTypeEnum conversationTypeEnum = chatMessageService.getConversationType(scene);
            return conversationTypeEnum == ConversationTypeEnum.FIX_TOOL_CALL || conversationTypeEnum == ConversationTypeEnum.AUTO_TOOL_CALL;
        }
        return false;
    }


}

