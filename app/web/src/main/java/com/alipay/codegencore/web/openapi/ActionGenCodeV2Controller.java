package com.alipay.codegencore.web.openapi;

import com.alipay.codegencore.model.openai.GenCodeFileRequest;
import com.alipay.codegencore.service.ideaevo.ActionGenCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 16:38
 */
@Slf4j
@RestController
@RequestMapping("/v2/generate")
public class ActionGenCodeV2Controller {


    @Autowired
    private ActionGenCodeService actionGenCodeService;

    /**
     * 生成代码
     * @param modelEnv
     * @param genCodeFileRequest
     * @return
     */
    @PostMapping(path = "/code/file", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void genCodeFile(HttpServletResponse httpServletResponse,
                                    @RequestHeader(name = "modelEnv", defaultValue = "auto") String modelEnv,
                                    @RequestHeader(name = "modelName", required = false) String modelName,
                                    @RequestBody @Validated GenCodeFileRequest genCodeFileRequest) {
        log.info("session:{} code gen env:{} req:{}", genCodeFileRequest.getSessionId(), modelEnv, genCodeFileRequest);
        actionGenCodeService.genCodeFileStream(genCodeFileRequest, modelEnv, modelName);
    }
}
