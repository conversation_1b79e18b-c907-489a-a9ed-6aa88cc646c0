package com.alipay.codegencore.web.openapi;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.utils.HttpConnectKeepAlive;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @version 1.0 2024/10/29 11:18
 */
public class KeepAliveUtils {

    private static final Logger log = LoggerFactory.getLogger(KeepAliveUtils.class);

    /**
     * 统一心跳处理
     * @param future
     * @param maxTimeout
     * @param httpServletResponse
     * @return
     * @param <T>
     */
    public static <T> BaseResponse<T> keepAlive(Future<T> future, long maxTimeout, HttpServletResponse httpServletResponse) {

        HttpConnectKeepAlive.keepAlive(future, maxTimeout, httpServletResponse);

        try {
            T t = future.get(10L, TimeUnit.SECONDS);
            return BaseResponse.build(t);
        } catch (TimeoutException e) {
            log.error("gen timeout.", e);
            return BaseResponse.build(ResponseEnum.SVAT_GEN_TIMEOUT);
        } catch (ExecutionException e) {
            log.error("execution exception", e);
            Throwable cause = e.getCause();
            //最多尝试 5 次获取业务失败原因
            for (int i = 0; i < 5; i++) {
                if (cause instanceof BizException) {
                    BizException bizException = (BizException) cause;
                    return BaseResponse.build(bizException.getErrorType(), bizException.getMessage());
                }
                cause = cause.getCause();
            }
            return BaseResponse.build(ResponseEnum.SVAT_GEN_FAIL);
        } catch (InterruptedException e) {
            log.error("interrupted exception", e);
            return BaseResponse.build(ResponseEnum.SVAT_GEN_FAIL);
        } finally {
            if (!future.isDone()) {
                future.cancel(true);
            }
        }
    }

}
