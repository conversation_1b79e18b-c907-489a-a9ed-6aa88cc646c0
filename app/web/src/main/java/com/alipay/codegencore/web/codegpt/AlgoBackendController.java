package com.alipay.codegencore.web.codegpt;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.BaseModelHandlerEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.AlgoBackendModel;
import com.alipay.codegencore.model.model.ModelCurrentAvailableInfo;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.GPTCacheService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.model.AlgoModelHealthUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.msgbroker.ModelHealthDegreeNotifyListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 模型管理
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.web.codegpt
 * @CreateTime : 2023-06-19
 */
@Slf4j
@RestController
@CodeTalkWebApi
@RequestMapping("/webapi/algoBackend")
public class AlgoBackendController {

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private GPTCacheService gptCacheService;

    @Resource
    private MayaService mayaService;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;

    @Resource
    private ModelHealthDegreeNotifyListener modelHealthDegreeNotifyListener;

    @Resource
    private ChatSessionManageService chatSessionManageService;

    private static final Logger LOGGER = LoggerFactory.getLogger( AlgoBackendController.class );

    //获取所有模型
    @PostMapping(path = "/getAllModel")
    public BaseResponse<List<AlgoBackendDO>> getAllAlgoBackend() {
        checkAdmin();
        return BaseResponse.build(algoBackendService.getAllAlgoBackend());
    }

    /**
     * 获取所有模型-分页
     *
     * @param pageNo
     * @param pageSize
     * @param modelName 根据模模型名称模糊查询
     * @return
     */
    @GetMapping(path = "/getAllPageAlgoBackend")
    public PageResponse<List<AlgoBackendModel>> getAllPageAlgoBackend(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                      @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                                      @RequestParam(value = "modelName", required = false) String modelName) {
        checkAdmin();
        return algoBackendService.getAllPageAlgoBackend(pageNo, pageSize, modelName);
    }

    /**
     * 通过Id查找模型
     *
     * @param id
     * @return
     */
    @GetMapping(path = "/getModelInfo")
    public BaseResponse<AlgoBackendModel> getModelInfo(@RequestParam(value = "id") Long id) {
        checkAdmin();
        return BaseResponse.build(algoBackendService.selectByPrimaryKey(id));
    }

    /**
     * 新增模型
     *
     * @param algoBackendModel
     * @return
     */
    @PostMapping(path = "/addModel")
    public BaseResponse<Boolean> addModel(@RequestBody AlgoBackendModel algoBackendModel) {
        checkAdmin();
        return BaseResponse.build(algoBackendService.addModel(algoBackendModel, userAclService.getCurrentUser()));
    }

    /**
     * 修改模型信息
     *
     * @param algoBackendModel
     * @return
     */
    @PostMapping(path = "/updateModel")
    public BaseResponse<Boolean> updateModel(@RequestBody AlgoBackendModel algoBackendModel) {
        checkAdmin();
        if (algoBackendModel.getId() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "更新模型必要带上id");
        }
        return BaseResponse.build(algoBackendService.updateModel(algoBackendModel, userAclService.getCurrentUser()));
    }

    /**
     * 删除指定模型信息
     *
     * @param id 模型id
     * @return
     */
    @PostMapping(path = "/deleteModel")
    public BaseResponse<Boolean> deleteModel(@RequestParam Long id) {
        checkAdmin();
        return BaseResponse.build(algoBackendService.deleteModel(id, userAclService.getCurrentUser()));
    }

    /**
     * 获取模型Handler配置的下拉列表
     *
     * @return
     */
    @GetMapping(path = "/getModelImplConfigList")
    public BaseResponse<Object> getModelImplConfigList() {
        JSONObject modelHandler = JSON.parseObject(codeGPTDrmConfig.getModelHandler());
        Object result;
        if (userAclService.isAdmin()) {
            result = modelHandler.get("admin");
        }
        else {
            result = modelHandler.get("user");
        }
        return BaseResponse.build(result);
    }

    /**
     * 获取模型jump配置的下拉列表
     *
     * @return
     */
    @GetMapping(path = "/getModelJumpConfigList")
    public BaseResponse<List<String>> getModelJumpConfigList() {
        checkAdmin();
        return BaseResponse.build(algoBackendService.getModelJumpConfigList());
    }

    /**
     * 删除某个模型的缓存
     *
     * @return
     */
    @PostMapping(path = "/removeModelCache")
    public BaseResponse<Boolean> removeModelCache(@RequestParam String model) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if (currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!userAclService.isAdmin()) {
            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
            if (algoBackendDO == null || !(algoBackendDO.getCreateUserId().longValue() == currentUser.getId() ||
                    algoBackendDO.getOwnerUserId().longValue() == currentUser.getId())){
                throw new BizException(ResponseEnum.NO_AUTH);
            }
        }
        gptCacheService.removeCache(model);
        return BaseResponse.buildSuccess();
    }

    /**
     * 根据模型获取部署状态
     * model  模型名称
     * needRealTime 是否需要实时获取， 默认为false
     * @return
     */
//    @GetMapping(path = "/getModelAvailableServers")
//    public BaseResponse<JSONObject> getModelAvailableServers(@RequestParam String model,
//                                                             @RequestParam(value = "needRealTime", defaultValue = "false") Boolean needRealTime) {
//        if(LOGGER.isInfoEnabled()) {
//            LOGGER.info("start to get model severs : {}, {}", model, needRealTime);
//        }
//
//        JSONObject result = mayaService.getModelAvailableServers(model, needRealTime);
//        return BaseResponse.build(result);
//    }

    /**
     * 获取模型的健康度
     * model  模型名称
     * @return
     */
    @GetMapping(path = "/getModelHealthDegree")
    public BaseResponse<Object> getModelHealthDegree(@RequestParam String model) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to get model health degree : {}", model);
        }

        JSONObject healthDegree = algoModelHealthUtilService.getHealthDegreeCacheByModel(model);
        return BaseResponse.build(healthDegree);
    }

    /**
     * 判断是否是管理员
     */
    private void checkAdmin() {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
    }

    /**
     * 获取用户模型
     *
     * @param pageNo    页数
     * @param pageSize  每页大小
     * @param modelName 根据模模型名称模糊查询
     * @return
     */
    @GetMapping(path = "/getUserAlgoBackend")
    public PageResponse<List<AlgoBackendModel>> getUserAlgoBackend(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                                @RequestParam(value = "modelName", required = false) String modelName) {
        return algoBackendService.getUserAlgoBackend(pageNo, pageSize, modelName);
    }

    /**
     * 通过Id查找模型
     *
     * @param id 模型id
     * @return
     */
    @GetMapping(path = "/getUserModelInfoById")
    public BaseResponse<AlgoBackendModel> getUserModelInfo(@RequestParam(value = "id") Long id) {
        return BaseResponse.build(algoBackendService.getUserModelInfo(id));
    }

    /**
     * 用户新增模型
     *
     * @return
     */
    @PostMapping(path = "/addUserModel")
    public BaseResponse<Boolean> addUserModel(@RequestBody AlgoBackendModel algoBackendDO) {
        if (ObjectUtil.hasEmpty(algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        return BaseResponse.build(algoBackendService.addUserModel(algoBackendDO));
    }

    /**
     * 用户修改模型信息
     *
     * @param algoBackendDO 模型信息
     * @return
     */
    @PostMapping(path = "/updateUserModel")
    public BaseResponse<Boolean> updateUserModel(@RequestBody AlgoBackendModel algoBackendDO) {
        if (algoBackendDO.getId() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "更新模型必要带上id");
        }
        return BaseResponse.build(algoBackendService.updateUserModel(algoBackendDO));
    }

    /**
     * 用户删除指定模型信息
     *
     * @param id 模型id
     * @return
     */
    @PostMapping(path = "/deleteUserModel")
    public BaseResponse<Boolean> deleteUserModel(@RequestParam Long id) {
        // 校验模型是否存在以及用户的权限
        if(algoBackendService.getUserModelInfo(id) == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型不存在");
        }
        return BaseResponse.build(algoBackendService.deleteUserModel(id));
    }

    /**
     * 获取基座模型
     *
     * @param pageNo    页数
     * @param pageSize  每页大小
     * @param modelName 根据模模型名称模糊查询
     * @return
     */
    @GetMapping(path = "/getModelBase")
    public PageResponse<List<AlgoBackendModel>> getModelBase(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                             @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                             @RequestParam(value = "modelName", required = false) String modelName) {
        return algoBackendService.getModelBase(pageNo, pageSize, modelName);
    }

    /**
     * 获取用户的handler选项
     *
     * @return
     */
    @GetMapping(path = "/getBaseModelHandler")
    public BaseResponse<List<HashMap<String,String>>> getBaseModelHandlerEnum() {
        List<HashMap<String,String>> result = new ArrayList<>();
        Arrays.asList(BaseModelHandlerEnum.values()).forEach(s -> {
                    HashMap<String,String> baseModelHandler = new HashMap<>();
                    baseModelHandler.put("label", s.toString());
                    baseModelHandler.put("value", s.getValue());
                    baseModelHandler.put("description", s.getDescription());
                    result.add(baseModelHandler);
                }
        );
        return BaseResponse.build(result);
    }

    /**
     * 获取默认模型配置
     *
     * @param handler
     * @return
     */
    @GetMapping(path = "/getDefaultModelImplConfig")
    public BaseResponse<Object> getDefaultModelImplConfig(@RequestParam String handler) {
        JSONObject modelHandler = JSON.parseObject(codeGPTDrmConfig.getDefaultModelImplConfig());
        String result;
        if (StrUtil.equals(handler,BaseModelHandlerEnum.AntGLM.getValue())) {
            result = modelHandler.getString(BaseModelHandlerEnum.AntGLM.toString());
        }
        else {
            result = modelHandler.getString(BaseModelHandlerEnum.CodeFuse.toString());

        }
        return BaseResponse.build(JSONArray.parseArray(result));
    }

    @GetMapping(path = "/getModelDegreeDetail")
    public BaseResponse<Object>  getModelDegreeDetail(@RequestParam(value = "modelName") String modelName) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to check health degree detail, {}", modelName);
        }

        JSONObject detail = algoModelHealthUtilService.getModelHealthDegree(modelName);
        if(null == detail){
            LOGGER.warn("model health degree not found in cache");
            return BaseResponse.build(ResponseEnum.NO_MODEL_HEALTH_DEGREE_INFO, ResponseEnum.NO_MODEL_HEALTH_DEGREE_INFO.getErrorMsg());
        }
        return BaseResponse.build(detail);
    }

    @GetMapping(path = "/isModelHealth")
    public BaseResponse<Integer>  isModelHealth(@RequestParam(value = "modelName") String modelName
            ,@RequestParam(value = "modelEnv") String modelEnv) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to check health degree, {}, {}", modelName, modelEnv);
        }

        Integer isHealth = algoModelHealthUtilService.isHealth(modelEnv, modelName);
        return BaseResponse.build(isHealth);
    }

    /**
     * 更新会话的模型配置到模型配置
     *
     * @param sessionUid 会话uid
     * @return
     */
    @PostMapping(value = "/updateModelConfigFromSession")
    public BaseResponse<Boolean> updateModelConfigFromSession(@RequestParam Long modelId, @RequestParam String sessionUid,
                                                              @RequestBody JSONObject implConfig) {
        AlgoBackendModel dbAlgoBackendModel = algoBackendService.selectByPrimaryKey(modelId);

        if (dbAlgoBackendModel == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型不存在");
        }
        AlgoBackendModel algoBackendModel = new AlgoBackendModel();
        algoBackendModel.setId(modelId);
        algoBackendModel.setImplConfig(JSON.toJSONString(implConfig));
        // 兼容新的模型编辑逻辑
        algoBackendModel.setImpl(dbAlgoBackendModel.getImpl());
        Boolean updateUserModel = algoBackendService.updateUserModel(algoBackendModel);
        // 更新到模型配置后 清空会话的模型配置
        ChatSessionDO chatSessionDO = new ChatSessionDO();
        chatSessionDO.setUid(sessionUid);
        chatSessionDO.setModelConfig("");
        chatSessionManageService.updateSession(chatSessionDO);
        return BaseResponse.build(updateUserModel);
    }

    /**
     * 获取模型可用性信息
     *
     * @param pageNo
     * @param pageSize
     * @param modelName
     * @return
     */
    @GetMapping(path = "/getModelAvailableInfo")
    public PageResponse<List<ModelCurrentAvailableInfo>> getModelAvailableInfo(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                               @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                                               @RequestParam(value = "modelName", required = false)
                                                                              String modelName) {
        checkAdmin();
        return algoBackendService.getModelAvailableInfo(pageNo, pageSize, modelName);

    }
}
