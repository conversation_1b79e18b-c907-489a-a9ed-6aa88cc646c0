package com.alipay.codegencore.web.codegpt;

import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.model.UpdateSessionContextRequestModel;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.linke.ApprovalUser;
import com.alipay.codegencore.model.response.linke.WorkItemVO;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.tool.learning.plugin.CodeFuseCallLinke;
import com.alipay.codegencore.utils.code.AntCodeClient;
import groovy.util.logging.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * codefuse 侧边栏
 *
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.web.codegpt
 * @CreateTime : 2024-04-12
 */
@Slf4j
@RestController
@CodeTalkWebApi
@RequestMapping("/webapi/message/form")
public class ChatMessageFormController {
    @Resource
    private CodeFuseCallLinke codeFuseCallLinke;

    @Resource
    private UserAclService userAclService;

    /**
     * 获取应用列表
     *
     * @param query
     * @param empId
     * @return
     */
    @GetMapping(path = "/getAppNames")
    public BaseResponse<List<String>> getAppNames(@RequestParam(required = false) String query, @RequestParam String empId,
                                                  @RequestParam String sessionId) {
        if(!checkCurrentUser(empId)){
            BaseResponse.build(ResponseEnum.NO_AUTH,"当前参数empId不属于当前用户");
        }
        return BaseResponse.build(codeFuseCallLinke.getAppName(empId, query, sessionId));
    }


    /**
     * 根据获取工作项列表
     *
     * @param empId
     * @return
     */
    @GetMapping(path = "/getWorkItemList")
    public BaseResponse<List<WorkItemVO>> getWorkItemList(@RequestParam String empId, @RequestParam(required = false) String query,
                                                          @RequestParam String sessionId) {
        if(!checkCurrentUser(empId)){
            BaseResponse.build(ResponseEnum.NO_AUTH,"当前参数empId不属于当前用户");
        }
        return BaseResponse.build(codeFuseCallLinke.getWorkItemList(empId, query, sessionId));
    }

    /**
     * 获取pr审批人列表
     *
     * @param empId
     * @return
     */
    @GetMapping(path = "/getApprovalUser")
    public BaseResponse<List<ApprovalUser>> getApprovalUser(@RequestParam String empId, @RequestParam(required = false) String query,
                                                            @RequestParam String sessionId) {
        if(!checkCurrentUser(empId)){
            BaseResponse.build(ResponseEnum.NO_AUTH,"当前参数empId不属于当前用户");
        }
        return BaseResponse.build(codeFuseCallLinke.getApprovalUser(empId, query, sessionId));
    }

    /**
     * 设置会话的上下文
     *
     * @param sessionId
     * @param key
     * @param value
     * @return
     */
    @PostMapping(path = "/upSessionContext")
    public BaseResponse<Long> setSessionContext(@RequestParam String sessionId, @RequestParam String key, @RequestParam String value) {
        codeFuseCallLinke.setSessionContext(sessionId, key, value);
        return BaseResponse.buildSuccess();
    }

    /**
     * 批量设置会话的上下文
     *
     * @return
     */
    @PostMapping(path = "/upSessionContextBatch")
    public BaseResponse<Long> setSessionContextBatch(@RequestBody UpdateSessionContextRequestModel body) {
        codeFuseCallLinke.setSessionContextBatch(body);
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取分支列表
     *
     * @param empId
     * @param query
     * @param sessionId
     * @return
     */
    @GetMapping(path = "/getBranchList")
    public BaseResponse<List<String>> getBranchList(@RequestParam String empId, @RequestParam(required = false) String query,
                                                    @RequestParam String sessionId) {
        if(!checkCurrentUser(empId)){
            BaseResponse.build(ResponseEnum.NO_AUTH,"当前参数empId不属于当前用户");
        }
        return BaseResponse.build(codeFuseCallLinke.getBranchList(empId, query, sessionId));
    }


    /**
     * 搜索antcode组列表
     * @param keyword
     * @return
     */
    @GetMapping(path = "/searchAntcodeGroup")
    public BaseResponse<List<String>> searchAntcodeGroup(@RequestParam(defaultValue = "release") String keyword){
        List<AntCodeClient.RepoGroupInfo> repoGroupInfoList = AntCodeClient.searchAntcodeGroup(keyword);
        List<String> repoGroupList = repoGroupInfoList.stream().map(AntCodeClient.RepoGroupInfo::getName).collect(Collectors.toList());
        return BaseResponse.build(repoGroupList);
    }

    /**
     * 搜索antcode仓库列表
     *
     * @param keyword
     * @return
     */
    @GetMapping(path = "/searchAntcodeRepo")
    public BaseResponse<List<String>> searchAntcodeRepo(@RequestParam String repoGroup,
                                                        @RequestParam(defaultValue = "") String keyword,
                                                        @RequestParam(defaultValue = "50") Integer limit){
        return BaseResponse.build(AntCodeClient.searchAntcodeRepo(repoGroup, keyword, limit));
    }

    /**
     * 搜索antcode仓库列表
     *
     * @param keyword
     * @return
     */
    @GetMapping(path = "/searchAntcodeRepoBranch")
    public BaseResponse<List<String>> searchAntcodeRepoBranch(@RequestParam String repoGroup,
                                                              @RequestParam String repoName,
                                                              @RequestParam(defaultValue = "") String keyword,
                                                              @RequestParam(defaultValue = "50") Integer limit){
        return BaseResponse.build(AntCodeClient.getBranchList(repoGroup, repoName, keyword, limit));
    }

    /**
     * 检查当前empId是否属于当前用户
     *
     * <AUTHOR>
     * @since 2024.11.18
     * @param empId empId
     * @return boolean
     */
    private boolean checkCurrentUser(String empId) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        return currentUser.getEmpId().equalsIgnoreCase(empId);
    }

}
