package com.alipay.codegencore.web.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.antq.common.utils.StandardCharsets;
import com.alipay.codegencore.model.enums.EnvEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.common.ZarkService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.FaasFunctionService;
import com.alipay.codegencore.service.tool.learning.plugin.impl.DataTableSelectPlugin;
import com.alipay.sofa.function.InvokeParam;
import com.alipay.sofa.function.RpcReference;
import com.alipay.sofa.function.SOFAFunction;
import com.alipay.sofa.function.generic.GenericRequest;
import com.alipay.sofa.function.generic.GenericResponse;
import com.alipay.sofa.function.generic.SerializeType;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 方法调用对应的工具方法
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/tool")
public class FunctionCallToolController {


    private static final Logger LOGGER = LoggerFactory.getLogger( FunctionCallToolController.class );

    private static final String AUTH_TOKEN = "Authorization";

    @Resource
    private DataTableSelectPlugin dataTableSelectPlugin;

    @Resource
    private FaasFunctionService faasFunctionService;

    @Resource
    private ZarkService zarkService;

    @Resource
    private ConfigService configService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;


    @Resource
    private UserAclService     userAclService;

    @Resource
    private AlgoBackendService algoBackendService;

    /**
     * faas平台工具
     * @param functionName
     * @param param
     * @return
     */
    @PostMapping(path="/http")
    public Object fassFunction(HttpServletRequest request,
                               @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                               @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                @RequestParam String functionName,
                                @RequestParam EnvEnum env,
                                @RequestBody JSONObject param) throws IOException {
        Map<String, Object> errorMsg = checkOpenapiToken(request, codeGPTUser, codeGPTToken);
        if (errorMsg != null) {
            return errorMsg;
        }

        if (!checkAuth(request, functionName)) {
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        return faasFunctionService.fassFunction(functionName, env, param);
    }

    /**
     * 数据表选择插件
     * @param params
     * @return
     */
    @PostMapping(path="/dataTableSelect")
    public Object dataTableSelect(HttpServletRequest request,
                                  @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                  @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                  @RequestBody JSONObject params){
        Map<String, Object> errorMsg = checkOpenapiToken(request, codeGPTUser, codeGPTToken);
        if (errorMsg != null) {
            return errorMsg;
        }
        Map<String,Object> tableInfo = (Map<String, Object>)dataTableSelectPlugin.request(params);
        if(tableInfo == null){
            return Map.of("success", false, "msg", "选择数据表过程中出现异常");
        }
        return tableInfo;
    }

    /**
     * 通过rpc的方式调用faas平台函数，函数入参数要能解析json，否则会报反序列化异常错误
     * 没法选择调用的环境，开发环境调stable，生产调生产，尽量把函数都上到生产
     * @param functionName 函数名
     * @param param 调用参数
     * @return
     */
    @PostMapping(path="/rpc")
    public Object fassRpcFunction(HttpServletRequest request,
                                  @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                  @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                  @RequestParam String functionName, @RequestBody JSONObject param) {
        Map<String, Object> errorMsg = checkOpenapiToken(request, codeGPTUser, codeGPTToken);
        if (errorMsg != null) {
            return errorMsg;
        }
        if (!checkAuth(request, functionName)) {
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        LOGGER.info("fassRpcFunction functionName:{}, param:{}", functionName, param);
        long startTime = System.currentTimeMillis();
        //初始化目标函数
        InvokeParam faasParam = InvokeParam.builder().appName("codegencore").vipUrl("functiongw-pool:12200").functionName(functionName)
                .addressWait(15000).timeout(45000).build();
        SOFAFunction<GenericRequest, GenericResponse> genericFunction = RpcReference.initGenericFunction(faasParam);
        //请求参数
        String json = JSON.toJSONString(param);
        //泛化调用
        GenericRequest gr = new GenericRequest();
        gr.setSerializeType(SerializeType.JSON);
        gr.setBody(json.getBytes(StandardCharsets.UTF_8));
        LOGGER.info("fassRpcFunction functionName, after init, start to apply:{}, param:{}", functionName, param);
        GenericResponse apply = genericFunction.apply(gr);
        LOGGER.info("fassRpcFunction functionName:{} costTime:{} ", functionName, System.currentTimeMillis() - startTime);
        Object resp = null;
        if (apply.getSerializeType() == SerializeType.JSON) {
            resp = JSON.parseObject(new String(apply.getBody(), StandardCharsets.UTF_8), Object.class);
            LOGGER.info("fassRpcFunction functionName:{}, param:{}, response:{}", functionName, param, resp.toString());
        }
        return resp;
    }

    /**
     * 调用zark服务
     * @param env
     * @param param
     * @return
     */
    @PostMapping(path="/zark/**")
    public Object zarkFunction(HttpServletRequest request,
                               @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                               @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                               @RequestParam EnvEnum env, @RequestBody JSONObject param) {
        Map<String, Object> errorMsg = checkOpenapiToken(request, codeGPTUser, codeGPTToken);
        if (errorMsg != null) {
            return errorMsg;
        }
        String url = new UrlPathHelper().getLookupPathForRequest(request);
        String uri = url.substring("/api/tool/zark".length());
        return zarkService.requestCommonZarkUrl(uri, env, param);
    }


    /**
     * 鉴权
     * @param request
     * @return
     */
    private boolean checkAuth(HttpServletRequest request, String functionName) {
        List<String> needCheckAuthFaasList = JSON.parseArray(codeGPTDrmConfig.getNeedCheckAuthFaasList(), String.class);
        if (!needCheckAuthFaasList.contains(functionName)) {
            return true;
        }
        String tokenRequest = request.getHeader(AUTH_TOKEN);
        String tokenConfig = configService.getConfigByKey(AUTH_TOKEN, false);
        if (StringUtils.isNotBlank(tokenRequest) && StringUtils.equalsIgnoreCase(tokenRequest, tokenConfig)) {
            return true;
        }
        return false;
    }

    /**
     * token鉴权
     * @param httpServletRequest
     * @return
     */
    private Map<String, Object> checkOpenapiToken(HttpServletRequest httpServletRequest,  String codeGPTUser,  String codeGPTToken) {
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            LOGGER.info("assistant conversation request not authorized, user: {}", codeGPTUser);
            HashMap<String, Object> errorMsg = new HashMap<>();
            errorMsg.put("code", "6");
            errorMsg.put("errorMsg", "权限不足");
            return errorMsg;
        }
        return null;
    }
}
