package com.alipay.codegencore.web.remote.vo;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/12 11:21
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RemoteAgentBaseResponse<T> {

    @JsonProperty(value = "success")
    private Boolean success;

    @JsonProperty(value = "error_code")
    private Integer errorCode;

    @JsonProperty(value = "error_msg")
    private String errorMsg;

    @JsonProperty(value = "data")
    private T data;

    /**
     * 成功返回
     * @return
     * @param <T>
     */
    public static <T> RemoteAgentBaseResponse<T> buildSuccess() {
        RemoteAgentBaseResponse<T> RemoteAgentBaseResponse =  new RemoteAgentBaseResponse<>(Boolean.TRUE);
        return RemoteAgentBaseResponse;
    }

    /**
     * 成功返回
     * @param data
     * @param <T>
     * @return
     */
    public static <T> RemoteAgentBaseResponse<T> buildSuccess(T data) {
        RemoteAgentBaseResponse<T> RemoteAgentBaseResponse = new RemoteAgentBaseResponse<>(Boolean.TRUE,
                ResponseEnum.SUCCESS.getErrorCode(), ResponseEnum.SUCCESS.getErrorMsg());
        RemoteAgentBaseResponse.setData(data);
        return RemoteAgentBaseResponse;
    }

    /**
     * 失败返回
     * @param code
     * @param message
     * @return
     * @param <T>
     */
    public static <T> RemoteAgentBaseResponse<T> buildFail(Integer code, String message) {
        RemoteAgentBaseResponse<T> RemoteAgentBaseResponse = new RemoteAgentBaseResponse<>(Boolean.FALSE, code, message);
        return RemoteAgentBaseResponse;
    }

    /**
     * 失败返回
     * @param responseEnum
     * @return
     * @param <T>
     */
    public static <T> RemoteAgentBaseResponse<T> buildFail(ResponseEnum responseEnum) {
        RemoteAgentBaseResponse<T> RemoteAgentBaseResponse = new RemoteAgentBaseResponse<>(Boolean.FALSE,
                responseEnum.getErrorCode(), responseEnum.getErrorMsg());
        return RemoteAgentBaseResponse;
    }

    public RemoteAgentBaseResponse() {

    }

    public RemoteAgentBaseResponse(Boolean success) {
        this.success = success;
    }

    public RemoteAgentBaseResponse(Boolean success, Integer code, String message) {
        this.success = success;
        this.errorCode = code;
        this.errorMsg = message;
    }


    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
