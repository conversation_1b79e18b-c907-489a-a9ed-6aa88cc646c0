package com.alipay.codegencore.web.openapi;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alipay.codegencore.dal.mapper.AnswerIndexBuildJobMapper;
import com.alipay.codegencore.dal.mapper.AnswerIndexBuildTaskMapper;
import com.alipay.codegencore.model.domain.AnswerIndexBuildTaskDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.answer.SourceCode;
import com.alipay.codegencore.model.request.answer.IndexBuildRequest;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.answer.IndexBuildResponse;
import com.alipay.codegencore.model.response.answer.JobPullResponse;
import com.alipay.codegencore.service.answer.AnswerIndexService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.drm.SvatDrmConfig;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.code.BloopSearchClient;
import com.alipay.codegencore.utils.code.CodeInsightClient;
import com.alipay.codegencore.utils.code.CodefuseSearchClient;
import com.alipay.codegencore.utils.thread.ThreadPoolUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 问答索引构建
 */
@RestController
@RequestMapping("/api/answer/index")
@Slf4j
public class AnswerIndexController {

    @Autowired
    private AnswerIndexService answerIndexService;

    @Autowired
    private AnswerIndexBuildTaskMapper answerIndexBuildTaskMapper;

    @Autowired
    private AnswerIndexBuildJobMapper answerIndexBuildJobMapper;

    @Autowired
    private SvatDrmConfig svatDrmConfig;

    @Autowired
    private CodeGPTDrmConfig codeGPTDrmConfig;

    /**
     * 仓库级别索引构建
     * @return
     */
    @PostMapping("/build")
    public BaseResponse<IndexBuildResponse> build(@RequestBody IndexBuildRequest indexBuildRequest) {
        //基础校验
        if (Objects.isNull(indexBuildRequest) || StringUtils.isBlank(indexBuildRequest.getRepoUrl())) {
            log.warn("answer index build arg ill:{}", indexBuildRequest);
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        log.info("build args:{}", indexBuildRequest);

        if (indexBuildRequest.getReBuild() == null) {
            indexBuildRequest.setReBuild(false);
        }

        Pair<String, String> repoInfo = AntCodeClient.getGroupAndProjectPathByRepoUrl(indexBuildRequest.getRepoUrl());
        if (repoInfo == null) {
            log.warn("answer index build repo ill:{}", indexBuildRequest);
            return BaseResponse.build(ResponseEnum.ANSWER_REPO_ARG_ERR);
        }

        if (SourceCode.SVAT.equalsIgnoreCase(indexBuildRequest.getSourceCode())) {

            final String codeSearchTaskName = svatDrmConfig.getCodeSearchTaskName();
            if (StringUtils.isNotBlank(codeSearchTaskName)) {
                JSONObject data = CodefuseSearchClient.sendIndexBuild(indexBuildRequest.getRepoUrl(), indexBuildRequest.getBranch(), codeSearchTaskName);
                if (data != null) {
                    IndexBuildResponse response = data.toJavaObject(IndexBuildResponse.class);
                    return BaseResponse.build(response);
                } else {
                    return BaseResponse.build(ResponseEnum.SVAT_REPO_INDEX_QUERY_FAIL);
                }
            }
            return BaseResponse.build(ResponseEnum.SVAT_REPO_INDEX_FAIL);
        } else {

            AntCodeClient.CommitInfo commitInfo = answerIndexService.repoCheck(repoInfo.getLeft(), repoInfo.getRight(), indexBuildRequest.getBranch());

            //索引构建
            return BaseResponse.build(answerIndexService.indexBuild(commitInfo,
                    indexBuildRequest.getRepoUrl(), indexBuildRequest.getPriority()));
        }
    }

    /**
     * 拉取待处理job列表
     * @param size
     * @return
     */
    @GetMapping("/job/pull")
    public BaseResponse<List<JobPullResponse>> pullJob(@RequestHeader(name = "codegpt_job_handler", required = false) String handlerInfo,
                                                       @RequestParam(required = false, defaultValue = "1") Integer size,
                                                       @RequestParam(required = false) String flag) {
        log.info("handler:{} flag:{} job pull args:{}", handlerInfo, flag, size);
        return BaseResponse.build(answerIndexService.pullJobs(flag, size, handlerInfo));
    }

    /**
     * 获取job处理结果回调
     * @param jobId
     * @param taskId
     * @return
     */
    @PostMapping("/job/result")
    public BaseResponse<JobPullResponse> jobResult(Long jobId, Long taskId, Integer status,
                                                   @RequestParam(required = false) String message) {
        log.info("job result jobId:{};taskId:{};status:{};message:{}", jobId, taskId, status, message);
        if (jobId == null
                || taskId == null) {
            return BaseResponse.build(ResponseEnum.ANSWER_JOB_RESULT_ARG_MISS);
        }

        boolean result = answerIndexService.jobResult(jobId, taskId, status, message);
        return result ? BaseResponse.buildSuccess() : BaseResponse.build(ResponseEnum.ANSWER_JOB_RESULT_HANDLE_FAIL);
    }

    @PostMapping("/callback")
    public BaseResponse antCodeCallback(@RequestBody JSONObject antCodeEvent) {

        if (antCodeEvent == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }

        ThreadPoolUtils.execute(ThreadPoolUtils.partIndexBuildPool, () -> answerIndexService.repoChangeHandle(antCodeEvent));

        return BaseResponse.buildSuccess();
    }

    /**
     * 获取问题列表
     * @param repoUrl
     * @param branch
     * @return
     */
    @GetMapping("/question")
    public BaseResponse repoQuestion(String repoUrl, String branch) {
        log.info("get question by repo:{} branch:{}", repoUrl, branch);
        Pair<String, String> repoInfo = AntCodeClient.getGroupAndProjectPathByRepoUrl(repoUrl);
        return BaseResponse.build(answerIndexService.getQuestion(repoInfo.getLeft(), repoInfo.getRight(), branch));

    }

    /**
     * 构建 wiki 任务
     * @param repoUrl
     * @param branch
     * @return
     */
    @PostMapping("/wiki")
    public BaseResponse<Long> buildRepoWiki(String repoUrl, String branch,
                                            @RequestParam(required = false) String scanAction) {

        log.info("build wiki repo:{} branch:{}", repoUrl, branch);
        Pair<String, String> repoInfo = AntCodeClient.getGroupAndProjectPathByRepoUrl(repoUrl);
        if (repoInfo == null) {
            log.warn("answer index build repo ill:{}", repoUrl);
            return BaseResponse.build(ResponseEnum.ANSWER_REPO_ARG_ERR);
        }

        if (StringUtils.isBlank(scanAction)) {
            scanAction = codeGPTDrmConfig.getWikiBuildScanAction();
        }
        long scanRecordId = CodeInsightClient.createBuildTask(repoUrl, branch, null, CodeInsightClient.TaskMask.wiki.getValue(),
                0L, false, codeGPTDrmConfig.getCodeInsightCallBackEnv(), scanAction);

        return scanRecordId > 0 ? BaseResponse.build(scanRecordId) : BaseResponse.build(ResponseEnum.ANSWER_BUILD_WIKI_FAILED);
    }

    /**
     * 获取仓库 wiki 信息
     * @param repoUrl
     * @param branch
     * @return
     */
    @GetMapping("/wiki/info")
    public BaseResponse repoWiki(String repoUrl, String branch) {

        if (StringUtils.isBlank(repoUrl)
                || StringUtils.isBlank(branch)) {
            return BaseResponse.build(ResponseEnum.ANSWER_REPO_ARG_ERR);
        }

        JSONObject body = new JSONObject();
        body.put("repo_url", repoUrl);
        body.put("repo_branch", branch);
        JSONArray result = BloopSearchClient.searchWiki(body);
        if (result != null) {
            List<BloopSearchClient.WikiResult> wikiResultList = result.toJavaList(BloopSearchClient.WikiResult.class);
            return BaseResponse.build(wikiResultList);
        }
        return BaseResponse.build(ResponseEnum.ANSWER_SEARCH_WIKI_ERROR);
    }


    /**
     * 获取仓库 wiki 信息
     * @param body
     * @return
     */
    @PostMapping("/wiki/info")
    public BaseResponse repoWiki(@RequestBody JSONObject body) {
        return BaseResponse.build(BloopSearchClient.searchWiki(body));
    }


    @PostMapping("/rebuild")
    public BaseResponse rebuildRepo(@RequestBody IndexBuildRequest indexBuildRequest) {

        Pair<String, String> repoInfo = AntCodeClient.getGroupAndProjectPathByRepoUrl(indexBuildRequest.getRepoUrl());
        if (repoInfo == null) {
            log.warn("answer index build repo ill:{}", indexBuildRequest);
            return BaseResponse.build(ResponseEnum.ANSWER_REPO_ARG_ERR);
        }

        AntCodeClient.CommitInfo commitInfo = answerIndexService.repoCheck(repoInfo.getLeft(), repoInfo.getRight(), indexBuildRequest.getBranch());

        answerIndexService.rebuild(commitInfo);
        return BaseResponse.buildSuccess();
    }

    /**
     * 重置失败的 job
     * @param request
     * @return
     */
    @PostMapping("/reset/fail/job")
    public BaseResponse rebuildRepo(@RequestBody JSONObject request) {

        List<Long> taskIdList = request.getObject("taskIdList", new TypeReference<List<Long>>(){}.getType());
        String failMessage = request.getString("failMessage");
        log.info("fail job reset task:{} fail message:{}", taskIdList, failMessage);
        return BaseResponse.build(answerIndexService.resetFailJobHandle(taskIdList, failMessage));

    }

    /**
     * 删除仓库索引
     * @param groupPath
     * @param projectPath
     * @param branch
     * @param deleteIndex
     * @return
     */
    @PostMapping("/remove/index")
    public BaseResponse removeRepo(String groupPath, String projectPath, String branch,
                                   @RequestParam(defaultValue = "false") Boolean deleteIndex) {

        AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getByRepoInfo(groupPath, projectPath, branch);
        Map<String, Object> result = Maps.newHashMap();
        if (taskDO != null) {
            int taskCount = answerIndexBuildTaskMapper.deleteByRepoInfo(groupPath, projectPath, branch);
            result.put("taskCount", taskCount);

            long jobCount = answerIndexBuildJobMapper.deleteById(taskDO.getId());
            result.put("jobCount", jobCount);

            if (deleteIndex) {
                Boolean removeResult = BloopSearchClient.removeIndex(groupPath + "/" + projectPath, branch);
                result.put("indexRemove", removeResult);
            }
        }
        return BaseResponse.build(result);
    }


    /**
     * 查询仓库索引状态
     * @param repoPath
     * @param branch
     * @return
     */
    @GetMapping("/status")
    public BaseResponse indexStatus(String repoPath, @RequestParam(defaultValue = "master") String branch, @RequestParam(defaultValue = "ANSWER") String sourceCode) {
        if (StringUtils.isBlank(repoPath)) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }

        if (SourceCode.SVAT.equalsIgnoreCase(sourceCode)) {
            final String codeSearchTaskName = svatDrmConfig.getCodeSearchTaskName();
            if (StringUtils.isNotBlank(codeSearchTaskName)) {
                JSONObject data = CodefuseSearchClient.queryIndexStatus(repoPath, branch, codeSearchTaskName);
                if (data != null) {
                    return BaseResponse.build(data);
                } else {
                    return BaseResponse.build(ResponseEnum.SVAT_REPO_INDEX_QUERY_FAIL);
                }
            }
            return BaseResponse.build(ResponseEnum.SVAT_REPO_INDEX_QUERY_FAIL);
        } else {
            Pair<Long, String> indexStatus = answerIndexService.getIndexStatus(repoPath, branch);
            JSONObject result = new JSONObject();
            result.put("taskId", indexStatus.getLeft());
            result.put("state", indexStatus.getRight());
            return BaseResponse.build(result);
        }
    }



    /**
     * 检查zsearch状态，并通知
     *
     * @param endTime
     * @param startTime
     * @param id
     * @return
     */
    @PostMapping(path = "/checkAndNotifyZSearchStatus")
    public BaseResponse<List<String>> checkAndNotifyZSearchStatus(@RequestParam(required = false) String endTime,
                                                                  @RequestParam(required = false) String startTime,
                                                                  @RequestParam(required = false) Long id) {

        log.info("checkAndNotifyZSearchStatus, endTime: {}, startTime: {}, id: {}", endTime, startTime, id);
        if (endTime == null && startTime == null && id == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, "endTime, startTime and id cannot be all null");
        }
        List<String> result = answerIndexService.checkAndNotifyZSearchStatus(id, startTime, endTime);
        return BaseResponse.build(result);

    }

    /**
     * 获取索引构建进度
     * @param repoURL
     * @param branch
     * @return
     */
    @GetMapping(path = "/build/progress")
    public BaseResponse buildProgress(String repoURL, String branch) {
        log.info("get build progress repoURL:{} branch:{}", repoURL, branch);
        if (StringUtils.isBlank(repoURL)
                || StringUtils.isBlank(branch)) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        return BaseResponse.build(answerIndexService.queryBuildProgress(repoURL, branch));
    }

    /**
     * 根据id获取任务信息 50个
     * @param id
     * @return
     */
    @GetMapping(path = "/getTaskById")
    public BaseResponse<List<AnswerIndexBuildTaskDO>> getTaskById(@RequestParam Long id) {
        if (id == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        return BaseResponse.build(answerIndexBuildTaskMapper.getTaskById(id));
    }


    @GetMapping(path = "/task/check")
    public BaseResponse taskCheck(String state, Integer limit) {
        log.info("index build task check state:{} limit:{}", state, limit);
        if (StringUtils.isBlank(state)) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        answerIndexService.repoTaskCheck(state, limit);
        return BaseResponse.buildSuccess();
    }

}
