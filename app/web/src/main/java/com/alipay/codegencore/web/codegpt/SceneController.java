package com.alipay.codegencore.web.codegpt;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import com.alipay.codegencore.dal.example.DocumentDOExample;
import com.alipay.codegencore.dal.example.SceneDOExample;
import com.alipay.codegencore.dal.mapper.DocumentDOMapper;
import com.alipay.codegencore.dal.mapper.SceneDOMapper;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.DocumentDO;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.AuditStatusSceneEnum;
import com.alipay.codegencore.model.enums.ControlTypeEnum;
import com.alipay.codegencore.model.enums.DocumentSourceEnum;
import com.alipay.codegencore.model.enums.DocumentStatusEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.yuque.YuQueBookResponseModel;
import com.alipay.codegencore.model.model.yuque.YuQueTocModel;
import com.alipay.codegencore.model.model.yuque.YuQueTokenInfoModel;
import com.alipay.codegencore.model.openai.SceneVO;
import com.alipay.codegencore.model.openai.UserSceneRecordsVO;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.PluginService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.UserSceneRecordsService;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.YuQueDocUtilService;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 场景管理
 *
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.web.codegpt
 * @CreateTime : 2023-07-11
 */
@Slf4j
@CodeTalkWebApi
@RestController
@RequestMapping("/webapi/scene")
public class SceneController {
    private static final Logger       LOGGER = LoggerFactory.getLogger(SceneController.class);
    @Resource
    private              SceneService sceneService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private UserSceneRecordsService userSceneRecordsService;
    @Resource
    private DocumentHandleService documentHandleService;
    @Resource
    private YuQueDocUtilService yuQueDocUtilService;

    @Resource
    private SecAgentHelper secAgentHelper;


    @Resource
    private PluginService pluginService;


    /**
     * 获取助手的权限信息
     *
     * @param sceneId 助手id
     * @return
     */
    @GetMapping(path = "/getSceneControlInfo")
    public PageResponse<List<UserSceneRecordsVO>> getSceneControlInfo(@RequestParam Long sceneId,
                                                                      @RequestParam(required = false) String query,
                                                                      @RequestParam(required = false) ControlTypeEnum controlTypeEnum,
                                                                      @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                      @RequestParam(value = "pageSize", defaultValue = "99999") int pageSize) {

        return userSceneRecordsService.getSceneControlInfo(sceneId, query, controlTypeEnum, pageNo, pageSize);
    }


    /**
     * 批量导入助手的权限信息
     *
     * @param sceneId 助手id
     * @param empIds  empId
     * @return
     */
    @PostMapping(path = "/batchInsertUserScene")
    public BaseResponse<List<String>> batchInsertUserScene(@RequestParam Long sceneId,
                                                           @RequestParam(defaultValue = "SEE") ControlTypeEnum controlTypeEnum,
                                                           @RequestBody List<String> empIds) {
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        List<String> errorEmpIds = userSceneRecordsService.batchInsertUserScene(sceneService.getSceneById(sceneId), empIds, controlTypeEnum);

        return BaseResponse.build(errorEmpIds);
    }

    /**
     * 修改助手的一个用户的信息
     *
     * @param sceneId     助手id
     * @param userId      用户id
     * @param controlType 权限类型
     * @return
     */
    @PostMapping(path = "/updateSceneControl")
    public BaseResponse<Boolean> updateSceneControl(@RequestParam Long sceneId, @RequestParam Long userId,
                                                    @RequestParam ControlTypeEnum controlType) {
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        Boolean update = userSceneRecordsService.updateSceneControl(sceneId, userId, controlType);
        return BaseResponse.build(update);
    }

    /**
     * 去除助手的一个用户权限
     *
     * @param sceneId 助手id
     * @param userId  用户id
     * @return
     */
    @PostMapping(path = "/deleteSceneUserControl")
    public BaseResponse<Boolean> deleteSceneUserControl(@RequestParam Long sceneId, @RequestParam Long userId) {
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        Boolean delete = userSceneRecordsService.deleteSceneUserControl(sceneId, userId);
        return BaseResponse.build(delete);
    }

    @PostMapping(path = "/overrideSceneUserControl")
    public BaseResponse<Boolean> overrideSceneUserControl(@RequestBody SceneVO sceneVO) {
        if (sceneVO == null ||
                sceneVO.getId() == null ||
                sceneVO.getControlTypeEnum() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneVO.getId(), userAuthDO.getId())) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        userSceneRecordsService.overrideSceneUserControl(sceneVO.getId(), sceneVO.getControlTypeEnum(), sceneVO.getEmpIdList());
        return BaseResponse.buildSuccess();
    }


    /**
     * 获取所有场景
     *
     * @return
     */
    @GetMapping(path = "/getAllScene")
    public BaseResponse<List<SceneDO>> getAllScene() {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(sceneService.getAllScene());
    }

    /**
     * 新增场景
     *
     * @param sceneDO
     * @return
     */
    @PostMapping(path = "/addScene")
    public BaseResponse<Long> addScene(@RequestBody SceneDO sceneDO) {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        UserAuthDO currentUser = userAclService.getCurrentUser();
        sceneDO.setUserId(currentUser.getId());
        return BaseResponse.build(sceneService.addScene(sceneDO));
    }

    /**
     * 更新场景信息
     *
     * @param sceneDO
     * @return
     */
    @PostMapping(path = "/updateScene")
    public BaseResponse<Boolean> updateScene(@RequestBody SceneDO sceneDO) {

        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(sceneService.adminUpdateSceneById(sceneDO));
    }

    /**
     * 删除场景
     *
     * @param id 场景id
     * @return
     */
    @GetMapping(path = "/deleteScene")
    public BaseResponse<Boolean> deleteSceneById(@RequestParam Long id) {

        return BaseResponse.build(sceneService.deleteSceneById(id));
    }

    /**
     * 物理删除助手
     *
     * @param id 场景id
     * @return
     */
    @GetMapping(path = "/physicalDeletion")
    public BaseResponse<Boolean> physicalDeletion(@RequestParam Long id) {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(sceneService.physicalDeletion(id));
    }

    /**
     * 获取场景的表单schema
     *
     * @param id 场景id
     * @return
     */
    @GetMapping(path = "/getSceneSchema")
    public BaseResponse<JSONObject> getSceneSchema(@RequestParam Long id) {

        return BaseResponse.build(sceneService.getSceneSchema(id));
    }

    /**
     * 上传文件到助手上
     * @param sceneId
     * @param documentList
     * @param token 语雀团队token
     * @param bookId 知识库bookId
     * @param segmentationStrategy 分段策略
     * @param docIdList 文档IdList
     * @param documentUid documentUid
     */
    @PostMapping(value = "/uploadDocument")
    public BaseResponse<List<DocumentDO>> uploadDocument(@RequestParam Long sceneId,
                                               @RequestParam(required = false) String token,
                                               @RequestParam(required = false) Long bookId,
                                               @RequestParam(required = false) List<Long> docIdList,
                                               @RequestParam(required = false) List<String> docSlugList,
                                               @RequestParam(required = false) List<MultipartFile> documentList,
                                               @RequestParam(required = false) String segmentationStrategy,
                                               @RequestParam(required = false) String documentUid) {

        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        boolean fileUpload = CollectionUtils.isNotEmpty(documentList);
        boolean yuQueUpload = StringUtils.isNotBlank(token) && bookId != null && (CollectionUtils.isNotEmpty(docIdList)||CollectionUtils.isNotEmpty(docSlugList));
        if (!fileUpload && !yuQueUpload) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"文件上传参数不完整");
        }
        if(fileUpload){
            documentHandleService.uploadSceneDocument(sceneId, documentList, userAuthDO, segmentationStrategy);
        }
        if(yuQueUpload){
            if(StringUtils.isNotBlank(documentUid)){
                // 更新document之前需要删除旧的文件
                documentHandleService.deleteSceneDocument(documentUid, sceneId);
            }
            if(documentHandleService.checkYuQueBookExist(sceneId,bookId)){
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"该知识库已被导入，可在编辑进行修改");
            }
            documentHandleService.uploadSceneYuQueBook(sceneId, userAuthDO, segmentationStrategy, token, bookId, docIdList,docSlugList);
        }
        return BaseResponse.build(documentHandleService.getDocumentBySceneId(sceneId));
    }


    /**
     * 助手文档解绑
     * @param sceneId 助手ID
     * @param documentUid 文档UID
     * @return
     */
    @PostMapping("/unbindDocument")
    public BaseResponse<Object> unbindDocument(@RequestParam Long sceneId, @RequestParam String documentUid) {
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        documentHandleService.deleteSceneDocument(documentUid,sceneId);
        return BaseResponse.buildSuccess();
    }
    /**
     * 助手绑定团队token
     * <AUTHOR>
     * @since 2024.01.17
     * @param sceneId 助手ID
     * @param token 团队token
     */
    @PostMapping("/bindGroupToken")
    public BaseResponse<YuQueTokenInfoModel> bindGroupToken(@RequestParam Long sceneId, @RequestParam String token){
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(sceneService.bindGroupToken(sceneId,token));
    }
    /**
     * 校验团队token权限以及有效性
     * <AUTHOR>
     * @since 2024.01.17
     * @param sceneId sceneId
     * @param token token
     */
    @GetMapping("/checkTokenAvailable")
    public BaseResponse<Boolean> checkTokenAvailable(@RequestParam Long sceneId, @RequestParam String token){
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(yuQueDocUtilService.checkTokenAvailable(token));
    }
    /**
     * 获取团队知识库列表
     * <AUTHOR>
     * @since 2024.01.17
     * @param sceneId sceneId
     * @param token token
     */
    @GetMapping("/getGroupBooks")
    public BaseResponse<List<YuQueBookResponseModel>> getGroupBooks(@RequestParam Long sceneId, @RequestParam String token){
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(yuQueDocUtilService.getGroupBooks(token));
    }
    /**
     * 获取知识库文档目录
     * <AUTHOR>
     * @since 2024.01.17
     * @param sceneId sceneId
     * @param token token
     * @param bookId bookId
     */
    @GetMapping("/getBookToc")
    public BaseResponse<List<YuQueTocModel>> getBookToc(@RequestParam Long sceneId, @RequestParam String token, @RequestParam Long bookId, @RequestParam(required = false) String documentUid){
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!sceneService.editableSceneUser(sceneId, userAuthDO.getId()) && !userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        DocumentDO document = documentHandleService.getDocumentByUid(documentUid);
        List<Long> docIds = null;
        List<String> docSlugList = null;
        if(document != null){
            docIds = documentHandleService.getDocIdsFromDocument(document.getId());
            docSlugList = documentHandleService.getDocSlugListFromDocument(document.getId());
        }
        return BaseResponse.build(yuQueDocUtilService.getYuQueBookToc(bookId,token,docIds,docSlugList));
    }

    @Resource
    private DocumentDOMapper documentDOMapper;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    /**
     * 手动触发语雀文档更新
     *
     * @return
     */
    @GetMapping("/updateYuQueDocument")
    public BaseResponse<List<YuQueTocModel>> updateYuQueDocument(@RequestParam(required = false) Long sceneId) {
        if (!userAclService.isAdmin()){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        LOGGER.info("手动开始更新语雀文档时间:{}", DateUtil.formatChineseDate(new Date(), false, true));

        SceneDO scene = sceneService.getSceneById(sceneId);
        if (scene ==null ){
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        List<DocumentDO> documentDOS;
        if (sceneId != null) {
            documentDOS = documentHandleService.getDocumentBySceneId(sceneId);
            if (CollectionUtils.isNotEmpty(documentDOS)){
                documentDOS = documentDOS.stream().filter(documentDO -> EnumUtil.equals(DocumentSourceEnum.YUQUE_BOOK, documentDO.getSource())).collect(
                        Collectors.toList());
            }
        }else {
            //查询所有ready状态的document
            DocumentDOExample documentDOExample = new DocumentDOExample();
            documentDOExample.createCriteria().andDocumentStatusEqualTo(DocumentStatusEnum.READY.name()).andSourceEqualTo(
                    DocumentSourceEnum.YUQUE_BOOK.name());
            documentDOS = documentDOMapper.selectByExample(documentDOExample);
        }
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        documentDOS.parallelStream().forEach(documentDO -> {
            try {
                documentHandleService.updateYuQueDocument(documentDO, documentChatConfig);
                LOGGER.info("更新语雀文档成功,documentUid:{}", documentDO.getUid());
            } catch (Exception e) {
                LOGGER.error("更新语雀文档失败,documentDO:{} message:{}", JSONObject.toJSONString(documentDO), e.getStackTrace());
                DocumentDOExample documentDOExampleDb = new DocumentDOExample();
                documentDOExampleDb.createCriteria().andUidEqualTo(documentDO.getUid());
                DocumentDO documentDODb = new DocumentDO();
                documentDODb.setDocumentStatus(DocumentStatusEnum.PARSE_FAILED.name());
                documentDOMapper.updateByExampleSelective(documentDODb, documentDOExampleDb);
            }
        });
        return BaseResponse.buildSuccess();
    }

    @Resource
    private SceneDOMapper sceneDOMapper;

    /**
     *
     * 兼容历史数据 3.13 下个版本可删除
     */
    @GetMapping("/updateFunctionCallConfig")
    public BaseResponse updateFunctionCallConfig(){
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        // 所有审核中的助手更改为审核通过
        SceneDOExample sceneDOExample = new SceneDOExample();
        sceneDOExample.createCriteria().andAuditStatusEqualTo(AuditStatusSceneEnum.RUNNING.getCode());
        List<SceneDO> sceneDOS = sceneDOMapper.selectByExample(sceneDOExample);
        sceneDOS.forEach(sceneDO -> {
            SceneDO upSceneDO = new SceneDO();
            upSceneDO.setId(sceneDO.getId());
            upSceneDO.setAuditStatus(AuditStatusSceneEnum.PAAS.getCode());
            SceneDOExample upSceneDOExample = new SceneDOExample();
            upSceneDOExample.createCriteria().andIdEqualTo(sceneDO.getId());
            sceneDOMapper.updateByExampleSelective(upSceneDO, upSceneDOExample);
        });
        return BaseResponse.buildSuccess();
    }

    /**
     * 向AgentSec同步agent和plugin信息
     *
     * <AUTHOR>
     * @since 2024.04.25
     * @return com.alipay.codegencore.model.response.BaseResponse
     */
    @GetMapping(path = "/synchronize")
    public BaseResponse synchronizeSec(){
        if(!userAclService.isAdmin()){
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }
        LOGGER.info("开始同步");
        List<SceneDO> allScene = sceneService.getAllScene();
        for (SceneDO sceneDO : allScene) {
            secAgentHelper.agentInit(sceneDO);
        }
        List<PluginDO> allPlugin = pluginService.getAllPlugin();
        for (PluginDO pluginDO : allPlugin) {
            UserAuthDO userAuthDO = userAclService.selectByUserId(pluginDO.getUserId());
            if(userAuthDO == null){
                LOGGER.info("插件的用户未找到，跳过同步");
                continue;
            }
            secAgentHelper.pluginInit(pluginDO,userAuthDO);
        }
        LOGGER.info("结束同步");
        return BaseResponse.buildSuccess();
    }
}
