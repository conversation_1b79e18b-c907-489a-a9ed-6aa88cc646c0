package com.alipay.codegencore.web.aop;

import cn.hutool.core.lang.Pair;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.common.limiter.RateLimitFactory;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * controller的aop切面,会切所有的controller接口
 */
@Aspect
@Component
public class ControllerAop {
    private static final Logger LOGGER = LoggerFactory.getLogger(ControllerAop.class);

    private List<Pattern> patternList = new ArrayList<>();

    /**
     * 初始化aop
     */
    @PostConstruct
    public void init() {
        try {
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:security/security-home.acl");
            InputStream inputStream = resource.getInputStream();
            byte[] byteArr = inputStream.readAllBytes();
            String fileStr = new String(byteArr);
            String[] fileArr = fileStr.split("\n");
            for (String str : fileArr) {
                // 当前行不是注释 且 当前行是openapi的规则
                if (!str.replace(" ", "").startsWith("##") && str.lastIndexOf(">ALL_VISIBLE") != -1) {
                    String pattern = str.split(">")[0];
                    pattern = "^" + pattern;
                    if (pattern.lastIndexOf("**") != -1) {
                        pattern = pattern.replace("**", ".*");
                    } else {
                        pattern = pattern + "$";
                    }
                    patternList.add(Pattern.compile(pattern));
                }
            }
        } catch (Exception e) {
            LOGGER.error("ControllerAop failed", e);
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "");
        }
    }


    @Resource
    private ResourceLoader resourceLoader;

    /**
     * 切类注解:RestController
     */
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController)")
    public void restControllerPointCut() {
    }

    /**
     * 切类注解:Controller
     */
    @Pointcut("@within(org.springframework.stereotype.Controller)")
    public void controllerPointCut() {
    }

    /**
     * 切面逻辑实现
     * @param jp
     * @return
     * @throws Throwable
     */
    @Around("controllerPointCut() || restControllerPointCut()")
    public Object aroundController(ProceedingJoinPoint jp) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String originalUri = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
        // 如果一个openapi不需要鉴权,那就不限流了
        String codegptUser = request.getHeader("codegpt_user");
        if (StringUtils.isBlank(codegptUser)) {
            return jp.proceed();
        }
        for (Pattern pattern : patternList) {
            Matcher matcher = pattern.matcher(originalUri);
            if (matcher.matches()) {
                // 匹配到的说明是openapi,限流只需要执行一次
                Pair<Boolean, Long> limitPair = RateLimitFactory.getInstance().tryAcquireOpenApi(codegptUser, originalUri);
                if (!limitPair.getKey()) {
                    throw new BizException(ResponseEnum.OPEN_API_REQUEST_LIMITING_ANOMALY, ResponseEnum.OPEN_API_REQUEST_LIMITING_ANOMALY.getErrorMsg() + ",触发限流规则id:" + limitPair.getValue());
                }
                break;
            }
        }
        return jp.proceed();
    }

}