/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.aop.handler;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.web.remote.AgentController;
import com.alipay.codegencore.web.remote.SessionController;
import com.alipay.codegencore.web.remote.vo.RemoteAgentBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @version GlobalExceptionHandler.java, v 0.1 2023年03月27日 15:57 xiaobin
 */
@Slf4j
@RestControllerAdvice(assignableTypes = {AgentController.class, SessionController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RemoteAgentExceptionHandler {

    /**
     * 业务异常抛出给用户侧
     *
     * @param e 业务异常
     * @return
     */
    @ExceptionHandler(BizException.class)
    public RemoteAgentBaseResponse handleBizException(BizException e) {
        log.warn("统一异常处理捕获BizException异常,msg:{}", e.getMessage());
        return RemoteAgentBaseResponse.buildFail(e.getErrorType());
    }

    /**
     * 兜底异常处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Throwable.class)
    public RemoteAgentBaseResponse handleThrowable(Throwable e) {
        log.error("统一异常处理捕获Exception异常", e);
        return RemoteAgentBaseResponse.buildFail(ResponseEnum.ERROR_THROW);
    }

}