package com.alipay.codegencore.web.common;

import com.alipay.codegencore.dal.example.PromptTemplateDOExample;
import com.alipay.codegencore.dal.mapper.PromptTemplateDOMapper;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.PromptTemplateDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserTypeEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.PromptTemplateService;
import com.alipay.codegencore.utils.thread.ContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.alipay.codegencore.model.contant.WebApiContents.CONTEXT_USER;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.09.23
 */
@RestController
@CodeTalkWebApi
@RequestMapping("/api/promptTemplate")
public class PromptTemplateController {
    @Resource
    private PromptTemplateService promptTemplateService;
    @Resource
    private PromptTemplateDOMapper promptTemplateDOMapper;

    /**
     * 获取prompt模版内容
     *
     * <AUTHOR>
     * @since 2024.09.23
     * @param name name
     * @return com.alipay.codegencore.model.response.BaseResponse<java.lang.String>
     */
    @GetMapping("/getPromptTemplate")
    public BaseResponse<String> getPromptTemplate(@RequestParam(name = "name") String name) {
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if(currentUser==null||currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
            throw new BizException(ResponseEnum.NO_AUTH,"无权限");
        }
        String promptTemplateText = promptTemplateService.getPromptTemplateText(name, false);
        if(StringUtils.isNotBlank(promptTemplateText)){
            return BaseResponse.build(promptTemplateText);
        }
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取prompt模版信息
     *
     * <AUTHOR>
     * @since 2024.09.23
     * @param name name
     * @return com.alipay.codegencore.model.response.BaseResponse<com.alipay.codegencore.model.domain.PromptTemplateDO>
     */
    @GetMapping("/getPromptTemplateInfo")
    public BaseResponse<PromptTemplateDO> getPromptTemplateFromDb(@RequestParam(name = "name") String name) {
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if(currentUser==null||currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
            throw new BizException(ResponseEnum.NO_AUTH,"无权限");
        }
        PromptTemplateDOExample promptTemplateDOExample = new PromptTemplateDOExample();
        promptTemplateDOExample.createCriteria().andNameEqualTo(name);
        List<PromptTemplateDO> promptTemplateDOS = promptTemplateDOMapper.selectByExample(promptTemplateDOExample);
        if(CollectionUtils.isNotEmpty(promptTemplateDOS)){
            return BaseResponse.build(promptTemplateDOS.get(0));
        }
        return BaseResponse.buildSuccess();
    }

    /**
     * 创建Prompt模版
     *
     * <AUTHOR>
     * @since 2024.09.23
     * @return com.alipay.codegencore.model.response.BaseResponse
     */
    @PostMapping("/createPromptTemplate")
    public BaseResponse<PromptTemplateDO> createPromptTemplate(@RequestBody PromptTemplateDO promptTemplateDO) {
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if(currentUser==null||currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
            throw new BizException(ResponseEnum.NO_AUTH,"无权限");
        }
        if(StringUtils.isBlank(promptTemplateDO.getName())||StringUtils.isBlank(promptTemplateDO.getTemplateText())){
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"name和templateText不能为空");
        }
        promptTemplateDOMapper.insertSelective(promptTemplateDO);
        return BaseResponse.build(promptTemplateDO);
    }


    /**
     * 更新Prompt模版
     *
     * <AUTHOR>
     * @since 2024.09.23
     * @return com.alipay.codegencore.model.response.BaseResponse
     */
    @PostMapping("/updatePromptTemplate")
    public BaseResponse<PromptTemplateDO> updatePromptTemplate(@RequestBody PromptTemplateDO promptTemplateDO) {
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if(currentUser==null||currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
            throw new BizException(ResponseEnum.NO_AUTH,"无权限");
        }
        if(StringUtils.isBlank(promptTemplateDO.getName())||StringUtils.isBlank(promptTemplateDO.getTemplateText())){
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"模版name和templateText不能为空");
        }
        PromptTemplateDOExample promptTemplateDOExample = new PromptTemplateDOExample();
        promptTemplateDOExample.createCriteria().andNameEqualTo(promptTemplateDO.getName());
        List<PromptTemplateDO> promptTemplateDOS = promptTemplateDOMapper.selectByExample(promptTemplateDOExample);
        if(CollectionUtils.isNotEmpty(promptTemplateDOS)&&!promptTemplateDOS.get(0).getTemplateText().equals(promptTemplateDO.getTemplateText())){
            promptTemplateDO.setOldTemplateText(promptTemplateDOS.get(0).getTemplateText());
        }
        promptTemplateDOMapper.updateByExampleSelective(promptTemplateDO,promptTemplateDOExample);
        return BaseResponse.build(promptTemplateDO);
    }
    /**
     * 删除Prompt模版
     *
     * <AUTHOR>
     * @since 2024.09.23
     * @param name name
     * @return com.alipay.codegencore.model.response.BaseResponse
     */
    @GetMapping("/deletePromptTemplate")
    public BaseResponse deletePromptTemplate(@RequestParam(name = "name") String name) {
        UserAuthDO currentUser = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if(currentUser==null||currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
            throw new BizException(ResponseEnum.NO_AUTH,"无权限");
        }
        PromptTemplateDOExample promptTemplateDOExample = new PromptTemplateDOExample();
        promptTemplateDOExample.createCriteria().andNameEqualTo(name);
        promptTemplateDOMapper.deleteByExample(promptTemplateDOExample);
        return BaseResponse.buildSuccess();
    }

}
