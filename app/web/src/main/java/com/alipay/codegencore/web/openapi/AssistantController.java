package com.alipay.codegencore.web.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.agentsecgateway.common.service.sdk.base.AgentSecSdkResult;
import com.alipay.agentsecgateway.common.service.sdk.secsdk.AgentSecSdk;
import com.alipay.agentsecgateway.common.service.sdk.secsdk.vo.ActionResult;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.contant.AlgoImplConfigKey;
import com.alipay.codegencore.model.domain.*;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ConversationTypeEnum;
import com.alipay.codegencore.model.enums.FileAnnotationTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.SegmentInfo;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.PluginServiceRequestContext;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.model.tool.learning.AssistantResultResponse;
import com.alipay.codegencore.model.model.tool.learning.FunctionCallConfig;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.CustomizeChatService;
import com.alipay.codegencore.service.codegpt.PluginService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.model.AlgoModelUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.FunctionCallService;
import com.alipay.codegencore.service.tool.learning.PluginWorkflowService;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.CommonTools;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.regex.Pattern;

import static com.alipay.codegencore.model.contant.WebApiContents.CONTEXT_USER;

/**
 * codefuse助手openapi
 */
@RestController
@RequestMapping("/api/assistant")
@Slf4j
public class AssistantController {

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    /**
     * 文件注释正则
     */
    private static final Pattern FILE_ANNOTATION_PATTERN = Pattern.compile("〔(\\d+)〕");

    @Resource
    private UserAclService userAclService;

    @Resource
    private SceneService sceneService;

    @Resource
    private ChatMessageService chatMessageService;

    @Resource
    private PluginService pluginService;

    @Resource
    private FunctionCallService functionCallService;

    @Resource
    private CustomizeChatService customizeChatService;
    @Resource
    private PluginWorkflowService pluginWorkflowService;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private UserAuthDOMapper userAuthDOMapper;

    @Resource
    private DocumentHandleService documentHandleService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource
    private AgentSecSdk agentSecSdk;

    @Resource
    private TokenService tokenService;

    /**
     * 助手对话
     * @param assistantId 助手id，该id可以在codefuse网页版上选中之后的浏览器地址上选取到
     */
    /**
     * 助手对话
     * @param assistantId 助手id，该id可以在codefuse网页版上选中之后的浏览器地址上选取到
     */
    @PostMapping(path = "/{assistantId}/conversation", produces = {"application/json;charset=UTF-8", "text/event-stream;charset=UTF-8"})
    public void conversation(HttpServletRequest httpServletRequest,
                             HttpServletResponse httpServletResponse,
                             @PathVariable("assistantId") Long assistantId,
                             @RequestBody ChatCompletionRequest assistantChatRequest,
                             @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                             @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                             @RequestHeader(value = "emp_id", required = false) String empId) throws IOException {
        // 集成agentSecSdk.threadCreate, 判断用户是否有agent使用权限, 根据agentSecSdk.threadCreate返回结果作相应处理
        TokenDO token = tokenService.getTokenSystem(codeGPTUser);
        String appName;
        if(StringUtils.isNotBlank(token.getAppName())){
            appName = token.getAppName();
        }else {
            appName = codeGPTUser;
        }
        AgentSecSdkResult<ActionResult> sdkResult = agentSecSdk.threadCreate(
                assistantId.toString(),        // Agent唯一标识
                "",            // 会话ID（可选，没有填空字符串）
                "app",    // 操作者类型，取值: user表示人, app表示应用
                appName
        );
        CHAT_LOGGER.info("agentSecSdk.threadCreate result:" + JSONObject.toJSONString(sdkResult));
        if(sdkResult.isSuccess() && ActionResult.BLOCK.equals(sdkResult.getData().getAction())){
            throw new BizException(ResponseEnum.NO_AUTH,"调用agent失败,缺少当前agent的权限");
        }
        //提前获取stream参数
        boolean isStream = assistantChatRequest.getStream();
        if(assistantChatRequest.getOnlyDecide() && isStream){
            CommonTools.writeResponse(null, 400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new Exception("onlyDecide can not be true when stream is true"));
            return;
        }
        assistantChatRequest.setStream(true);
        //设置工号
        if(StringUtils.isNotBlank(empId)){
            List<UserAuthDO> userAuthDOList = getUserAuthListByEmpId(empId);
            if(!userAuthDOList.isEmpty()){
                ContextUtil.set(CONTEXT_USER, userAuthDOList.get(0));
            }
        }

        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);

        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            log.info("assistant conversation request not authorized, user: {}", codeGPTUser);
            CommonTools.writeResponse(null, 403, httpServletResponse, ResponseEnum.NO_AUTH, new Exception("Not authorized!"));
            return;
        }

        SceneDO sceneDO = sceneService.getSceneById(assistantId);
        if(sceneDO==null){
            log.info("assistant: {} not existed, please check assistant id via https://codegpt-inc.alipay.com", assistantId);
            CommonTools.writeResponse(null, 400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new Exception("assistant not existed"));
            return;
        }
        //先从场景中获取system prompt
        if (StringUtils.isNotEmpty(sceneDO.getSystemPrompt())){
            assistantChatRequest.getMessages().add(0,new ChatMessage(ChatRoleEnum.SYSTEM.getName(), sceneDO.getSystemPrompt()));
        }

        List<SegmentInfo> segmentInfos = documentHandleService.recallSegmentBySceneId(sceneDO.getId(), assistantChatRequest.getMessages().get(assistantChatRequest.getMessages().size() - 1).getContent(),null);
        JSONObject docsInfo = ChatUtils.exactDocsInfoAndUpdateSegmentInfo(segmentInfos);
        ChatUtils.addDocSearchResultToChatRequest(segmentInfos,assistantChatRequest);

        String requestId = ShortUid.getUid();

        Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer = (pluginStreamPartResponse)-> {
            if(isStream){
                chatMessageService.processStreamResponse(pluginStreamPartResponse, docsInfo, httpServletResponse);
            }
        };

        Consumer<StreamResponseModel> streamResponseModelConsumer = (streamResponseModel)-> {
            if(!isStream){
                handleAssistantResult(streamResponseModel, httpServletResponse, assistantChatRequest.getOnlyDecide(),docsInfo);
            }
        };


        UserAuthDO userAuthDO = new UserAuthDO();
        if(assistantChatRequest.getChatRequestExtData()!=null){
            userAuthDO.setEmpId(assistantChatRequest.getChatRequestExtData().getEmpId());
        }

        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey)-> noneSerializationCacheManager.del(tBaseKey);
        PluginServiceRequestContext params = new PluginServiceRequestContext(null, requestId, codeGPTUser, userAuthDO, false, null, assistantChatRequest
                ,streamResponseModelConsumer, false);
        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        params.setPluginStreamPartResponseConsumer(pluginStreamPartResponseConsumer);
        // 流式会用到这个Handler
        params.setUniqueAnswerId(requestId);

        // 把自定义参数加入到工具流程中
        HashMap<String, Object> pluginParams = new HashMap<>();
        if(assistantChatRequest.getExtraInfo()!=null){
            pluginParams.putAll(assistantChatRequest.getExtraInfo());
        }
        params.setPluginParams(pluginParams);
        params.setSceneDO(sceneDO);


        ConversationTypeEnum conversationType = chatMessageService.getConversationType(sceneDO);
        // 添加插件指令判断逻辑
        if(assistantChatRequest.getPluginCommand()!=null){
            // 有指令则是固定匹配工具
            conversationType = ConversationTypeEnum.FIX_TOOL_CALL;
        }else {
            // 没有指令但是助手是固定匹配工具且助手绑定多个工具，需要指定一条工具指令
            FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(sceneDO);
            if (functionCallConfig.getCallFunctionEveryRound()&&ConversationTypeEnum.AUTO_TOOL_CALL.equals(conversationType)){
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "该助手是固定匹配，需要指定一条工具指令");
            }
        }
        if(ConversationTypeEnum.FIX_TOOL_CALL.equals(conversationType)){
            if(assistantChatRequest.getOnlyDecide()){
                log.info("assistant: {} not support function call, please set onlyDecide to false", assistantId);
                CommonTools.writeResponse(null, 400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new Exception("assistant not support function call, please set onlyDecide to false"));
                return;
            }

            params.setPluginIndex(0);
            PluginDO pluginDO = null;

            //获取插件，当前只支持单插件模式
            if(assistantChatRequest.getPluginCommand()!=null){
                // 匹配指令的工具
                pluginDO = pluginService.getPluginById(assistantChatRequest.getPluginCommand().getPluginId());
            }else {
                String pluginListStr = sceneDO.getPluginList();
                List<Long> pluginIdList = JSON.parseArray(pluginListStr, Long.class);
                pluginDO = pluginService.getPluginById(pluginIdList.get(0));
            }

            PluginInfo pluginInfo = new PluginInfo(pluginDO.getId(), pluginDO.getName(), pluginDO.getDescription());
            params.setPluginInfo(pluginInfo);
            params.setPluginDO(pluginDO);

            //流式传输，要把response设置为流式
            if(isStream){
                ChatUtils.setServletToEventStream(httpServletResponse);
            }
            pluginWorkflowService.streamChat(params, pluginDO);
        }else if(ConversationTypeEnum.AUTO_TOOL_CALL.equals(conversationType)){
            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(sceneService.getFunctionCallConfig(sceneDO).getFunctionCallModel());
            params.setAlgoBackendDO(algoBackendDO);
            params.setSceneDO(sceneDO);
            String pluginListStr = sceneDO.getPluginList();
            List<Long> pluginIdList = JSON.parseArray(pluginListStr, Long.class);
            List<PluginDO> pluginDO = pluginService.getPluginByIdList(pluginIdList,false);
            //流式传输，要把response设置为流式
            if(isStream){
                ChatUtils.setServletToEventStream(httpServletResponse);
            }
            functionCallService.streamChat(params, pluginDO);
        }else if (conversationType == ConversationTypeEnum.CUSTOMIZE) {
            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(sceneDO.getModel());
            Map<String, Object> implConfigMap = AlgoBackendUtil.getImplConfigMap(algoBackendDO);
            Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = (chatStreamPartResponse)-> {
                if(isStream){
                    NewPluginStreamPartResponse newPluginStreamPartResponse = new NewPluginStreamPartResponse(chatStreamPartResponse);
                    chatMessageService.processStreamResponse(newPluginStreamPartResponse, docsInfo, httpServletResponse);
                }
            };
            GptAlgModelServiceRequest chatParams = new GptAlgModelServiceRequest(requestId, codeGPTUser, false, algoBackendDO, assistantChatRequest
                    , streamResponseModelConsumer, true);
            FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(sceneDO);
            Map<String, Object> customizeChatConfig = functionCallConfig.getCustomizeChatConfig();
            if(customizeChatConfig.containsKey(AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME)){
                implConfigMap.put(AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME, customizeChatConfig.get(AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME));
            }else {
                implConfigMap.put(AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME, codeGPTDrmConfig.getPluginLLMStageFirstStreamDataWaitTime());
            }
            if(customizeChatConfig.containsKey(AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME)){
                implConfigMap.put(AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME, customizeChatConfig.get(AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME));
            }else {
                implConfigMap.put(AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME, codeGPTDrmConfig.getPluginCommonStreamDataWaitTime());
            }
            algoBackendDO.setImplConfig(JSON.toJSONString(implConfigMap));
            chatParams.setCustomizeUrl((String) customizeChatConfig.get("url"));
            chatParams.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
            chatParams.setChatStreamPartResponseHandler(chatStreamPartResponseConsumer);
            chatParams.setUserOriginalModel(algoBackendDO.getModel());
            chatParams.setUniqueAnswerId(requestId);
            chatParams.setModelEnv("auto");
            chatParams.setRequestTokenUser(codeGPTUser);
            chatParams.setAlgoBackendDO(algoBackendDO);
            //流式传输，要把response设置为流式
            if(isStream){
                ChatUtils.setServletToEventStream(httpServletResponse);
            }
            CHAT_LOGGER.info("chat completion request Customize chat api {}, user:{}, id:{}, content:{}", customizeChatConfig.get("url"), codeGPTUser, requestId, JSON.toJSONString(assistantChatRequest));
            customizeChatService.streamChat(chatParams);
        }else{
            if(assistantChatRequest.getOnlyDecide()){
                log.info("assistant: {} not support function call, please set onlyDecide to false", assistantId);
                CommonTools.writeResponse(null, 400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new Exception("assistant not support function call, please set onlyDecide to false"));
                return;
            }
            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(sceneDO.getModel());

            Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = (chatStreamPartResponse)-> {
                if(isStream){
                    NewPluginStreamPartResponse newPluginStreamPartResponse = new NewPluginStreamPartResponse(chatStreamPartResponse);
                    chatMessageService.processStreamResponse(newPluginStreamPartResponse, docsInfo, httpServletResponse);
                }
            };

            GptAlgModelServiceRequest chatParams = new GptAlgModelServiceRequest(requestId, codeGPTUser, false, algoBackendDO, assistantChatRequest
                    , streamResponseModelConsumer, true);
            // 流式会用到这个Handler
            chatParams.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
            chatParams.setChatStreamPartResponseHandler(chatStreamPartResponseConsumer);
            chatParams.setUserOriginalModel(algoBackendDO.getModel());
            chatParams.setUniqueAnswerId(requestId);
            chatParams.setModelEnv("auto");
            chatParams.setRequestTokenUser(codeGPTUser);

            CHAT_LOGGER.info("chat completion request model {}, user:{}, id:{}, content:{}", algoBackendDO.getModel(), codeGPTUser, requestId, JSON.toJSONString(assistantChatRequest));
            if(chatParams.getChatCompletionRequest().getMessages() != null){
                AlgoModelUtilService.updateDefaultSystemPrompt(chatParams.getChatCompletionRequest(), algoBackendDO);
            }
            //流式传输，要把response设置为流式
            if(isStream){
                ChatUtils.setServletToEventStream(httpServletResponse);
            }
            AlgoModelExecutor.getInstance().executorStreamChat(algoBackendDO, chatParams);
        }
    }

    private void handleAssistantResult(StreamResponseModel assistantResult,
                                       HttpServletResponse httpServletResponse,
                                       boolean onlyDecide,
                                       JSONObject docsInfo) {
        AssistantResultResponse assistantResultResponse = new AssistantResultResponse();
        FileAnnotationTypeEnum fileAnnotationType = FileAnnotationTypeEnum.valueOf(codeGPTDrmConfig.getFileAnnotationType());
        if(onlyDecide){
            ChatMessage chatMessage = assistantResult.getAnswerMessage();
            String contentWithFileAnnotation = chatMessageService.rewriteFileAnnotation(chatMessage.getContent(), docsInfo, fileAnnotationType);
            assistantResultResponse.setAnswer(contentWithFileAnnotation);
            assistantResultResponse.setFunctionCall(chatMessage.getFunctionCall());
        }else{
            String contentWithFileAnnotation = chatMessageService.rewriteFileAnnotation(assistantResult.getAnswerMessage().getContent(), docsInfo, fileAnnotationType);
            assistantResultResponse.setAnswer(contentWithFileAnnotation);
            if(assistantResult.getPluginLogGroup()!=null){
                assistantResultResponse.setPluginLogList(assistantResult.getPluginLogGroup().getPluginLogList());
            }
        }

        httpServletResponse.setContentType("application/json;charset=UTF-8");
        try {
            CommonTools.writeResponse(assistantResultResponse, 200, httpServletResponse, ResponseEnum.SUCCESS, null);
        } catch (IOException e) {
            log.error("write response error", e);
        }
    }

    private List<UserAuthDO> getUserAuthListByEmpId(String empId) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdEqualTo(empId);
        return userAuthDOMapper.selectByExample(userAuthDOExample);
    }
}
