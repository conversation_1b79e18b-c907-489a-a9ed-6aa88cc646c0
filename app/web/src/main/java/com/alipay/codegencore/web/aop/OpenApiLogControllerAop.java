package com.alipay.codegencore.web.aop;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.enums.RecordLogEnum;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.CollectLogUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * openapi接口拦截器，记录请求日志
 *
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.web.aop
 * @CreateTime : 2023-09-08
 */
@Aspect
@Component
public class OpenApiLogControllerAop {
    private static final Logger logger = LoggerFactory.getLogger(OpenApiLogControllerAop.class);

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    /**
     * completion openapi log aspect
     */
    @Pointcut("execution(* com.alipay.codegencore.web.openapi.ChatController.completion(..))")
    public void completionPointCutMethod() {
    }

    /**
     * embeddings openapi log aspect
     */
    @Pointcut("execution(* com.alipay.codegencore.web.openapi.ChatController.embeddings(..))")
    public void embeddingsPointCutMethod() {
    }

    /**
     * completion
     *
     * @param point
     * @throws Throwable
     */
    @Around("completionPointCutMethod()")
    public void completion(ProceedingJoinPoint point) throws Throwable {
        long waitStartTime = System.currentTimeMillis();
        point.proceed();
        long during = System.currentTimeMillis() - waitStartTime;
        int status = 0;
        Object[] args = point.getArgs();
        Map<String,Object> recordInfo = new HashMap<>();
        for (Object arg : args) {
            if (arg instanceof HttpServletResponse) {
                status = ((HttpServletResponse) arg).getStatus();
            }
            if (arg instanceof ChatCompletionRequest) {
                ChatCompletionRequest chatCompletionRequest = (ChatCompletionRequest) arg;
                recordInfo.put("stream", chatCompletionRequest.getStream());
            }
        }
        recordInfo.put("model", args[2]);
        recordInfo.put("user", args[3]);
        recordInfo.put("during", during);
        recordInfo.put("operate", "completion");
        recordInfo.put("responseStatus", status);

        CollectLogUtils.printCollectLog(RecordLogEnum.API_ACCESS, recordInfo);
    }

    /**
     * embeddings
     *
     * @param point
     * @return
     * @throws Throwable
     */
    @Around("embeddingsPointCutMethod()")
    public Object embeddings(ProceedingJoinPoint point) throws Throwable {
        long waitStartTime = System.currentTimeMillis();
        Object proceed = point.proceed();
        long during = System.currentTimeMillis() - waitStartTime;
        Object[] args = point.getArgs();
        Map<String,Object> recordInfo = new HashMap<>();
        recordInfo.put("user", args[1]);
        recordInfo.put("during", during);
        recordInfo.put("operate", "embeddings");
        recordInfo.put("responseStatus", JSON.parseObject(JSON.toJSONString(proceed)).get("errorCode"));
        CollectLogUtils.printCollectLog(RecordLogEnum.API_ACCESS, recordInfo);
        return proceed;
    }
}
