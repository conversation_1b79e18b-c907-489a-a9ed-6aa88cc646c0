package com.alipay.codegencore.web.openapi;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.model.ModelAvailableSimply;
import com.alipay.codegencore.model.model.ModelCurrentAvailableInfo;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.UserAclService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.05.21
 */
@RestController
@RequestMapping("api/algoBackend")
@Slf4j
public class AlgoBackendOpenApiController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlgoBackendOpenApiController.class);

    @Resource
    private UserAclService userAclService;

    @Resource
    private AlgoBackendService algoBackendService;

    /**
     * 获取模型可用性信息
     *
     * @param modelName
     * @return
     */
    @GetMapping(path = "/getModelAvailableInfo")
    public BaseResponse<ModelCurrentAvailableInfo> getModelAvailableInfo(
            HttpServletRequest httpServletRequest,
            @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
            @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
            @RequestParam(value = "modelName", required = false) String modelName) {
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            log.info("assistant conversation request not authorized, user: {}", codeGPTUser);
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(algoBackendService.getSingleModelAvailableInfo(modelName));
    }
    @PostMapping(path = "/checkModelsAvailable")
    public BaseResponse<List<ModelAvailableSimply>> checkModelsAvailable(HttpServletRequest httpServletRequest,
                                                                         @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                                         @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                                                         @RequestParam(value = "models", required = true) List<String> models){
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            log.info("assistant conversation request not authorized, user: {}", codeGPTUser);
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(algoBackendService.checkModelsAvailable(models));
    }
}
