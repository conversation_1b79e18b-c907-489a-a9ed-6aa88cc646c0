/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.aop.handler;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.common.tracer.util.TracerContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @version GlobalExceptionHandler.java, v 0.1 2023年03月27日 15:57 xiaobin
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");


    /**
     * 业务异常抛出给用户侧
     *
     * @param e 业务异常
     * @return
     */
    @ExceptionHandler(BizException.class)
    public BaseResponse<String> handleException(BizException e) {
        log.warn("统一异常处理捕获BizException异常,msg:{}", e.getMessage());
        OTHERS_LOGGER.warn("统一异常处理捕获BizException异常", e);
        BaseResponse<String> response = BaseResponse.build(e.getErrorType(), e);
        response.setTraceId(TracerContextUtil.getTraceId());
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        if("true".equalsIgnoreCase(request.getHeader(AppConstants.HTTP_HEADER_DEBUG))){
            response.setStackTrace(ExceptionUtils.getStackTrace(e));
        }

        return response;
    }

    /**
     * 通用入参校验异常
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public BaseResponse<String> handlerValidation(MethodArgumentNotValidException e) {
        log.warn("参数非法：{}", e.getMessage());
        BindingResult bindingResult = e.getBindingResult();
        if (Objects.nonNull(bindingResult)) {
            List<ObjectError> errorList = bindingResult.getAllErrors();
            if (CollectionUtils.isNotEmpty(errorList)) {
                return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, errorList.get(0).getDefaultMessage());
            }
        }
        return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, e.getMessage());
    }

    /**
     * 兜底异常处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Throwable.class)
    public BaseResponse<String> handleException(Exception e) {
        log.error("统一异常处理捕获Exception异常", e);

        BaseResponse<String> response = BaseResponse.build(ResponseEnum.ERROR_THROW);
        response.setTraceId(TracerContextUtil.getTraceId());
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        if("true".equalsIgnoreCase(request.getHeader(AppConstants.HTTP_HEADER_DEBUG))){
            response.setStackTrace(ExceptionUtils.getStackTrace(e));
        }
        return response;
    }

    /**
     * 对象传入的JSON格式错误异常
     * @param e
     * @return
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public BaseResponse<String> handleException(HttpMessageNotReadableException e) {
        log.error("统一异常处理捕获HttpMessageNotReadableException异常", e);
        BaseResponse<String> response = BaseResponse.build(ResponseEnum.HTTP_MESSAGE_ERROR);
        response.setTraceId(TracerContextUtil.getTraceId());
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        if("true".equalsIgnoreCase(request.getHeader(AppConstants.HTTP_HEADER_DEBUG))){
            response.setStackTrace(ExceptionUtils.getStackTrace(e));
        }
        return response;
    }
}