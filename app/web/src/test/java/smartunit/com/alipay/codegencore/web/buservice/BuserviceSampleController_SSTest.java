/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.web.buservice;

import com.alipay.codegencore.web.buservice.BuserviceSampleController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static org.junit.Assert.assertEquals;
import static org.smartunit.runtime.SmartAssertions.verifyException;
import static org.smartunit.shaded.org.mockito.Mockito.*;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class BuserviceSampleController_SSTest extends BuserviceSampleController_SSTest_scaffolding {
// allCoveredLines:[26, 41, 43, 54, 55, 67, 80, 92]

  @Test(timeout = 4000)
  public void test_checkPermission_last0_0()  throws Throwable  {
      //caseID:ac232abb8cbba2d33271c457db29cec3
      //CoveredLines: [26, 92]
      //Input_0_HttpServletRequest: {}
      
      BuserviceSampleController buserviceSampleController0 = new BuserviceSampleController();
      //mock httpServletRequest0
      HttpServletRequest httpServletRequest0 = mock(HttpServletRequest.class, CALLS_REAL_METHODS);
      
      //Call method: checkPermission
      // Undeclared exception!
      try { 
        buserviceSampleController0.checkPermission(httpServletRequest0);
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.web.buservice.BuserviceSampleController", e);
         assertEquals("java.lang.NullPointerException", e.getClass().getName());
      }
  }

  @Test(timeout = 4000)
  public void test_getCurrentUserFromFilter_last1_1()  throws Throwable  {
      //caseID:7c965c3d94667e14f9b581759fee5913
      //CoveredLines: [26, 54, 55]
      
      BuserviceSampleController buserviceSampleController0 = new BuserviceSampleController();
      
      //Call method: getCurrentUserFromFilter
      // Undeclared exception!
      try { 
        buserviceSampleController0.getCurrentUserFromFilter();
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.web.buservice.BuserviceSampleController", e);
         assertEquals("java.lang.NullPointerException", e.getClass().getName());
      }
  }

  @Test(timeout = 4000)
  public void test_getFromRequest_2()  throws Throwable  {
      //caseID:960b7268564fd436d4e2af2c9870f3eb
      //CoveredLines: [26, 41, 43]
      //Input_0_HttpServletRequest: {getCookies=cookieArray0, getParameter=\"Cause: \" \"1\" \"IE_MEDIUM_SECURITY\" \"0\", getHeader=\"\"}
      //Input_1_HttpServletResponse: {}
      
      BuserviceSampleController buserviceSampleController0 = new BuserviceSampleController();
      Cookie[] cookieArray0 = new Cookie[5];
      //mock cookie0
      Cookie cookie0 = mock(Cookie.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("/error").when(cookie0).getName();
      cookieArray0[0] = cookie0;
      cookieArray0[1] = cookieArray0[0];
      cookieArray0[2] = cookieArray0[0];
      cookieArray0[3] = cookieArray0[2];
      cookieArray0[4] = cookieArray0[3];
      //mock httpServletRequest0
      HttpServletRequest httpServletRequest0 = mock(HttpServletRequest.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("Cause: ", "1", "IE_MEDIUM_SECURITY", "0").when(httpServletRequest0).getParameter(anyString());
      doReturn(cookieArray0).when(httpServletRequest0).getCookies();
      doReturn("").when(httpServletRequest0).getHeader(anyString());
      //mock httpServletResponse0
      HttpServletResponse httpServletResponse0 = mock(HttpServletResponse.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: getFromRequest
      // Undeclared exception!
      try { 
        buserviceSampleController0.getFromRequest(httpServletRequest0, httpServletResponse0);
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.web.buservice.BuserviceSampleController", e);
         assertEquals("java.lang.NullPointerException", e.getClass().getName());
      }
  }

  @Test(timeout = 4000)
  public void test_testacl_last3_3()  throws Throwable  {
      //caseID:75cb81762c0d5c17ab22afa89a6c2649
      //CoveredLines: [26, 67]
      //Assert: assertEquals("acl paas", method_result);
      
      BuserviceSampleController buserviceSampleController0 = new BuserviceSampleController();
      
      //Call method: testacl
      String string0 = buserviceSampleController0.testacl();
      
      //Test Result Assert
      assertEquals("acl paas", string0);
  }

  @Test(timeout = 4000)
  public void test_testaclJSON_last4_4()  throws Throwable  {
      //caseID:96a90bacd1e7c45c532c1b340ac9d0da
      //CoveredLines: [26, 80]
      //Assert: assertEquals("acl paas", method_result);
      
      BuserviceSampleController buserviceSampleController0 = new BuserviceSampleController();
      
      //Call method: testaclJSON
      String string0 = buserviceSampleController0.testaclJSON();
      
      //Test Result Assert
      assertEquals("acl paas", string0);
  }
}
