package com.alipay.codegencore.model.model;

import java.math.BigDecimal;

/**
 * 文档片段对象
 */
public class SegmentInfo {

    private String documentUid;

    // document 在文档库中的分割顺序编号，从 1 开始递增
    private String documentIdx;

    private String segmentUid;

    private String segmentContent;

    private String documentName;

    // 当前片段引用文档的URL
    private String quoteDocumentUrl;

    private BigDecimal similarity;

    public SegmentInfo() {
    }

    public SegmentInfo(EmbeddingResponseModel embeddingResponseModel){
        this.documentUid = embeddingResponseModel.getFileUid();
        this.segmentUid = embeddingResponseModel.getPartUid();
        this.segmentContent = embeddingResponseModel.getOriginalStr();
        this.quoteDocumentUrl = embeddingResponseModel.getDcoUrl();
    }

    public String getDocumentUid() {
        return documentUid;
    }

    public void setDocumentUid(String documentUid) {
        this.documentUid = documentUid;
    }

    public String getDocumentIdx() {
        return documentIdx;
    }

    public void setDocumentIdx(String documentIdx) {
        this.documentIdx = documentIdx;
    }

    public String getSegmentUid() {
        return segmentUid;
    }

    public void setSegmentUid(String segmentUid) {
        this.segmentUid = segmentUid;
    }

    public String getSegmentContent() {
        return segmentContent;
    }

    public void setSegmentContent(String segmentContent) {
        this.segmentContent = segmentContent;
    }

    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public String getQuoteDocumentUrl() {
        return quoteDocumentUrl;
    }

    public void setQuoteDocumentUrl(String quoteDocumentUrl) {
        this.quoteDocumentUrl = quoteDocumentUrl;
    }

    public BigDecimal getSimilarity() {
        return similarity;
    }

    public void setSimilarity(BigDecimal similarity) {
        this.similarity = similarity;
    }
}
