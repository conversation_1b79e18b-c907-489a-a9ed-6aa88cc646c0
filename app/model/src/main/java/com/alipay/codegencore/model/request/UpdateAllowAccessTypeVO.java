package com.alipay.codegencore.model.request;

import com.alipay.codegencore.model.enums.AllowAccessTypeEnum;

// 定义一个更新允许访问类型的类
public class UpdateAllowAccessTypeVO {

    // 用户ID
    private Long userId;
    // 允许访问类型
    private AllowAccessTypeEnum allowAccessType;

    // 获取用户ID的方法
    public Long getUserId() {
        return userId;
    }

    // 设置用户ID的方法
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    // 获取允许访问类型的方法
    public AllowAccessTypeEnum getAllowAccessType() {
        return allowAccessType;
    }

    // 设置允许访问类型的方法
    public void setAllowAccessType(Integer allowAccessType) {
        this.allowAccessType = AllowAccessTypeEnum.getEnumByValue(allowAccessType);
    }

}

