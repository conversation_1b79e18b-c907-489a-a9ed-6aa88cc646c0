/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version $Id: ReplaysUrlInfo.java, v 0.1 2021-09-24 18:11 wb-tzg858080 Exp $$
 */
public class ReplaysUrlInfo extends ToString {
    /**
     * 标题
     */
    private String title;
    /**
     * 链接
     */
    private String url;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param title title
     * @param url url
     * @return null
     */
    public ReplaysUrlInfo(String title, String url) {
        this.title = title;
        this.url = url;
    }

    public ReplaysUrlInfo() {
    }
}
