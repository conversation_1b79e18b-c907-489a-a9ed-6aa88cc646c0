/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: LinksReplaysResultModel.java, v 0.1 2021-09-24 18:01 wb-tzg858080 Exp $$
 */
public class LinksReplaysResultInfo extends BeanStringSwitcherImpl {
    /**
     * 错误信息
     */
    private  String errMsg  ;
    /**
     * 诊断链接
     */
    private List<ReplaysUrlInfo> urlInfos ;
    /**
     * 诊断详情
     */
    private  List<ReplayDetailInfo> details;

    public List<ReplaysUrlInfo> getUrlInfos() {
        return urlInfos;
    }

    public void setUrlInfos(List<ReplaysUrlInfo> urlInfos) {
        this.urlInfos = urlInfos;
    }

    public List<ReplayDetailInfo> getDetails() {
        return details;
    }

    public void setDetails(List<ReplayDetailInfo> details) {
        this.details = details;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @return null
     */
    public LinksReplaysResultInfo() {
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param errMsg errMsg
     * @param urlInfos urlInfos
     * @return null
     */
    public LinksReplaysResultInfo(String errMsg, List<ReplaysUrlInfo> urlInfos) {
        this.errMsg = errMsg;
        this.urlInfos = urlInfos;
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param urlInfos urlInfos
     * @param details details
     * @return null
     */
    public LinksReplaysResultInfo(List<ReplaysUrlInfo> urlInfos, List<ReplayDetailInfo> details) {
        this.urlInfos = urlInfos;
        this.details = details ;
    }
}
