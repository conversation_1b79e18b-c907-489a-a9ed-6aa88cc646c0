package com.alipay.codegencore.model.openai;

import java.io.Serializable;
import java.util.List;

/**
 * @Description AntLLM入参解读
 * @Date 2023/6/7 14:17
 * @Created by Yanghaiwang
 */
public class AntLLMOpenAPIRequest implements Serializable {
    /**
     * 选填-会话id（多轮对话使用）；建议调用方自己生成uuid并传入
     * 如果不传，后台也会每次生成一个uuid并在出参中返回
     */
    private String sessionId;
    /**
     * 选填-用户id，内网工号外网2088，先阶段不建议外网
     */
    private String userId;
    /**
     * 选填-对话唯一id，当reqType是chat时，不用传入
     * 每次chat，后台会生成一个唯一的uuid作为chatId
     * 当reqType是feedback时，传入用户反馈的那次对话对应的chatId
     */
    private String chatId;
    /**
     * 必填-请求类型-当前只有chat和feedback两个合法取值，前者对话后者反馈
     */
    private String reqType;
    /**
     * 必填-当reqType是feedback时，填入反馈内容
     */
    private String feedback;
    /**
     * 必填-用户prompt；当reqType=chat时，该字段为必传项
     */
    private String query;
    /**
     * 选填-当reqType=chat时，调用llm模型id，除非要单独指定版本
     * 否则建议此项为空，由我们后台填入最新模型id
     */
    private String modelId;
    /**
     * 选填-同上modelId
     */
    private String modelVersion;
    /**
     * 选填-是否进行采样，建议HTTP同步调用时候传true，流式调用不用传（传了暂时也不生效）
     */
    private Boolean doSample;
    /**
     * 选填-调节下一个标记概率的值。HTTP同步时填入，建议不填走后台默认值
     */
    private Float temperature;
    /**
     * 选填-为top k过滤而保留的最高概率词汇标记的数量。HTTP同步时填入，建议不填走后台默认值
     */
    private Integer topK;
    /**
     * 选填-已知生成各个词的总概率是1（即默认是1.0）如果top p小于1
     * 则从高到低累加直到top p，取这前N个词作为候选。
     * HTTP同步时填入，建议不填走后台默认值
     */
    private Float topP;
    /**
     * 选填-集束搜索beam_search的集束数量
     * 必须大于等于1，HTTP同步时填入，建议不填走后台默认值
     * 流式不要传（传了暂时也不生效）
     */
    private Integer beamWidth;
    /**
     * 选填-生成的tokens的最大长度；对应于prompt的长度+maxOutputLength。
     * 当设置maxOutputLength时，则其作用被maxOutputLength覆盖。
     * 建议不填走后台默认值
     */
    private Integer maxLength;
    /**
     * 选填-要生成的最大数量的tokens，忽略prompt中的tokens数量
     * 建议不填走后台默认值
     */
    private Integer maxOutputLength;
    /**
     * 必填-调用方应用名
     */
    private String appName;
    /**
     * 必填-调用方应用token；流式直接填该值
     * HTTP同步参考第2章，传入Header
     */
    private String appToken;
    /**
     * 选填-traceId（适用于rest接口或websocket接口时，无法自动传入traceId）
     * 建议传入，方便后台定位日志（但linkd展示可能有问题）
     */
    private String traceId;
    /**
     * 选填-rpcId，同上traceId
     */
    private String rpcId;
    /**
     * 选填-传入true，会忽略内容安全在流式下发时候的校验
     */
    private Boolean ignoreInfoSec = true;
    /**
     * 多个场景复用了一个app_name和token，可以传入sceneName字段区分开
     */
    private String sceneName;
    /**
     * 字符串列表，一旦生成的 tokens 包含其中的内容，将停止生成并返回结果
     */
    private List<String> stopSequences;

    public List<String> getStopSequences() {
        return stopSequences;
    }

    public void setStopSequences(List<String> stopSequences) {
        this.stopSequences = stopSequences;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public Boolean getIgnoreInfoSec() {
        return ignoreInfoSec;
    }

    public void setIgnoreInfoSec(Boolean ignoreInfoSec) {
        this.ignoreInfoSec = ignoreInfoSec;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public String getReqType() {
        return reqType;
    }

    public void setReqType(String reqType) {
        this.reqType = reqType;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getModelVersion() {
        return modelVersion;
    }

    public void setModelVersion(String modelVersion) {
        this.modelVersion = modelVersion;
    }

    public Boolean getDoSample() {
        return doSample;
    }

    public void setDoSample(Boolean doSample) {
        this.doSample = doSample;
    }

    public Float getTemperature() {
        return temperature;
    }

    public void setTemperature(Float temperature) {
        this.temperature = temperature;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public Float getTopP() {
        return topP;
    }

    public void setTopP(Float topP) {
        this.topP = topP;
    }

    public Integer getBeamWidth() {
        return beamWidth;
    }

    public void setBeamWidth(Integer beamWidth) {
        this.beamWidth = beamWidth;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }

    public Integer getMaxOutputLength() {
        return maxOutputLength;
    }

    public void setMaxOutputLength(Integer maxOutputLength) {
        this.maxOutputLength = maxOutputLength;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getRpcId() {
        return rpcId;
    }

    public void setRpcId(String rpcId) {
        this.rpcId = rpcId;
    }
}