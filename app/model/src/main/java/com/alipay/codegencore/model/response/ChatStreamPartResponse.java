package com.alipay.codegencore.model.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * chatgpt流式输出时的输出格式
 * <AUTHOR>
 */
public class ChatStreamPartResponse {
    /**
     * Unique id assigned to this chat completion.
     */
    private String id;
    /**
     * The type of object returned, should be "chat.completion"
     */
    private String object;
    /**
     * The creation time in epoch seconds.
     */
    private int created;
    /**
     * The GPT-3.5 model used.
     */
    private String model;
    /**
     * 是否清除content默认不清除
     */
    private Boolean clear;

    private String traceId;
    /**
     * A list of all generated parts.
     */
    private List<Choice> choices;
    /**
     * 消息类型
     */
    private String type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public int getCreated() {
        return created;
    }

    public void setCreated(int created) {
        this.created = created;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public List<Choice> getChoices() {
        return choices;
    }

    public void setChoices(List<Choice> choices) {
        this.choices = choices;
    }

    public Boolean getClear() {
        return clear;
    }

    public void setClear(Boolean clear) {
        this.clear = clear;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public static class Choice{
        /**
         *
         */
        private ChatMessage delta;
        /**
         * This index of this completion in the returned list.
         */
        private int index;
        @JSONField(name = "finish_reason")
        @JsonProperty("finish_reason")
        private String finishReason;

        /**
         * 审核结果数据
         */
        private CheckResultModel checkResultModel;

        public CheckResultModel getCheckResultModel() {
            return checkResultModel;
        }

        public void setCheckResultModel(CheckResultModel checkResultModel) {
            this.checkResultModel = checkResultModel;
        }

        public ChatMessage getDelta() {
            return delta;
        }

        public void setDelta(ChatMessage delta) {
            this.delta = delta;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public String getFinishReason() {
            return finishReason;
        }

        public void setFinishReason(String finishReason) {
            this.finishReason = finishReason;
        }
    }
}