/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.codegencore.model.enums;

/**
 * <AUTHOR>
 * @version IterationTypeEnum.java, v 0.1 2024年04月16日 下午12:48 lqb01337046
 */
public enum IterationTypeEnum {
    /**
     * 快速发布/日常发布
     **/
    FAST("日常发布"),
    /**
     * 紧急发布
     **/
    EMERGENCY("紧急发布"),
    /**
     * jar类型的迭代
     **/
    JAR("jar类型的迭代"),
    /**
     * 多分支发布的迭代,不合并，不打tag
     */
    MULTI_VERSION("分支多版本迭代"),
    /**
     * 分支发布,合并，打tag
     */
    BRANCH("分支发布"),
    /**
     * EIM 多个迭代EI 托管到一个多分支迭代EIM中。
     * 大家的迭代分支合成一个EIM分支，一起预发、一起灰度、一起生产
     */
    MULTI_BRANCH("多分支迭代"),
    /**
     * 原LinkE+单分支发布
     */
    LONELY_PPL("原LinkE+单分支发布");

    private String desc;

    IterationTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
