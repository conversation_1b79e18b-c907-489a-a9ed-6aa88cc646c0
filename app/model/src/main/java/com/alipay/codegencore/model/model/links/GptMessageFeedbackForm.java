/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version $Id: GptMessageFeedbackForm.java, v 0.1 2023-05-30 12:39 wb-tzg858080 Exp $$
 */
public class GptMessageFeedbackForm extends BaseForm {

    /**
     * 是否赞同
     */
    private Boolean agreed;

    /**
     * 反馈内容
     */
    private GptMessageFeedbackContent content;

    public Boolean getAgreed() {
        return agreed;
    }

    public void setAgreed(<PERSON><PERSON><PERSON> agreed) {
        this.agreed = agreed;
    }

    public GptMessageFeedbackContent getContent() {
        return content;
    }

    public void setContent(GptMessageFeedbackContent content) {
        this.content = content;
    }
}
