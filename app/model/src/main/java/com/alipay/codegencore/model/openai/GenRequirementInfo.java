package com.alipay.codegencore.model.openai;

import javax.validation.constraints.NotBlank;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0 2024/7/17 19:17
 */
public class GenRequirementInfo {

    private String changeId;

    private String changeAsset;

    //用户输入的变更逻辑
    @NotBlank(message = "userChangeLogic不能为空")
    private String userChangeLogic;

    //平台组装的变更逻辑
    private String generateChangeLogic;

    //用户变更的具体字段
    private String changeContent;


    private String interfaceName;

    private String interfaceDesc;

    private String interfacePath;

    private String bundlePath;

    private String filePath;

    private String methodName;

    private String requestUrl;

    private String changeType;

    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    public String getChangeAsset() {
        return changeAsset;
    }

    public void setChangeAsset(String changeAsset) {
        this.changeAsset = changeAsset;
    }

    public String getUserChangeLogic() {
        return userChangeLogic;
    }

    public void setUserChangeLogic(String userChangeLogic) {
        this.userChangeLogic = userChangeLogic;
    }

    public String getGenerateChangeLogic() {
        return generateChangeLogic;
    }

    public void setGenerateChangeLogic(String generateChangeLogic) {
        this.generateChangeLogic = generateChangeLogic;
    }

    public String getChangeContent() {
        return changeContent;
    }

    public void setChangeContent(String changeContent) {
        this.changeContent = changeContent;
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
    }

    public String getInterfaceDesc() {
        return interfaceDesc;
    }

    public void setInterfaceDesc(String interfaceDesc) {
        this.interfaceDesc = interfaceDesc;
    }

    public String getInterfacePath() {
        return interfacePath;
    }

    public void setInterfacePath(String interfacePath) {
        this.interfacePath = interfacePath;
    }

    public String getBundlePath() {
        return bundlePath;
    }

    public void setBundlePath(String bundlePath) {
        this.bundlePath = bundlePath;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GenRequirementInfo.class.getSimpleName() + "[", "]")
                .add("changeId='" + changeId + "'")
                .add("changeAsset='" + changeAsset + "'")
                .add("userChangeLogic='" + userChangeLogic + "'")
                .add("generateChangeLogic='" + generateChangeLogic + "'")
                .add("changeContent='" + changeContent + "'")
                .add("interfaceName='" + interfaceName + "'")
                .add("interfaceDesc='" + interfaceDesc + "'")
                .add("interfacePath='" + interfacePath + "'")
                .add("bundlePath='" + bundlePath + "'")
                .add("filePath='" + filePath + "'")
                .add("methodName='" + methodName + "'")
                .add("requestUrl='" + requestUrl + "'")
                .add("changeType='" + changeType + "'")
                .toString();
    }
}
