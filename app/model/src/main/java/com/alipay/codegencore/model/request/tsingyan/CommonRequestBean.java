package com.alipay.codegencore.model.request.tsingyan;

/**
 * 通用请求bean
 * 负责抽象出对外的请求体，根据{@link CommonRequestBean#serviceName}来指定转发下游的具体服务
 * 保持和外网青燕参数一致，实际内网版本不需要rsa加密，固paramToken是真实请求只
 * <AUTHOR>
 * 创建时间 2022-10-10
 */
public class CommonRequestBean extends AbstractRequestBean {

    /**
     * 服务名
     */
    private String serviceName;
    /**
     * 请求时间
     */
    private String requestTime;
    /**
     * 参数
     */
    private String paramToken;
    /**
     * 签名
     */
    private String sign;

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(String requestTime) {
        this.requestTime = requestTime;
    }

    public String getParamToken() {
        return paramToken;
    }

    public void setParamToken(String paramToken) {
        this.paramToken = paramToken;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
