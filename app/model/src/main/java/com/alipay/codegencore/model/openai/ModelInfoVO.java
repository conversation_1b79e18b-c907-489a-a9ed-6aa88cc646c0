package com.alipay.codegencore.model.openai;

import com.alipay.codegencore.model.domain.AlgoBackendDO;

/**
 * openapi 模型信息VO
 *
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.model.openai
 * @CreateTime : 2023-06-26
 */
public class ModelInfoVO {

    /**
     * 模型名
     */
    private String model;
    /**
     * 实际模型
     */
    private String jump;
    /**
     * maxToken
     */
    private Integer maxToken;
    /**
     * maxRound
     */
    private Integer maxRound;
    /**
     * 模型描述
     */
    private String modelDescription;

    public ModelInfoVO() {
    }

    public ModelInfoVO(AlgoBackendDO algoBackendDO) {
        this.model = algoBackendDO.getModel();
        this.jump = algoBackendDO.getJump();
        this.modelDescription = algoBackendDO.getModelDescription();
        this.maxRound = algoBackendDO.getMaxRound();
        this.maxToken = algoBackendDO.getMaxToken();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getJump() {
        return jump;
    }

    public void setJump(String jump) {
        this.jump = jump;
    }

    public Integer getMaxToken() {
        return maxToken;
    }

    public void setMaxToken(Integer maxToken) {
        this.maxToken = maxToken;
    }

    public Integer getMaxRound() {
        return maxRound;
    }

    public void setMaxRound(Integer maxRound) {
        this.maxRound = maxRound;
    }

    public String getModelDescription() {
        return modelDescription;
    }

    public void setModelDescription(String modelDescription) {
        this.modelDescription = modelDescription;
    }
}
