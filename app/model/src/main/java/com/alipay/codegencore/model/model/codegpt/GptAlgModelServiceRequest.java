/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.codegpt;

import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.model.RepoInfoModel;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;

import java.util.function.Consumer;

/**
 * 请求LanguageModelService参数集合，后期可能能够精简
 *
 * <AUTHOR>
 * @version LanguageModelRequsetParams.java, v 0.1 2023年04月22日 12:01 xiaobin
 */
public class GptAlgModelServiceRequest {

    /**
     * 请求用户的ID(webapi)
     */
    private Long requestUserId;

    /**
     * 请求的tokenUser(openapi)
     */
    private String requestTokenUser;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户原始请求的模型名
     */
    private String userOriginalModel;

    /**
     * 数据库中的算法模型配置记录
     * 这个对象中的 model 名称可能和 userOriginalModel 不一致（如果 这个记录是 jump 之后的情况）
     */
    private AlgoBackendDO algoBackendDO;

    /**
     * 会话id
     */
    private String requestId;

    /**
     * 是否是压测
     */
    private boolean stressTest;

    /**
     * 会话请求参数
     */
    private ChatCompletionRequest chatCompletionRequest;

    /**
     * 会话完成后处理器
     */
    private Consumer<StreamResponseModel> resultHandler;

    /**
     * 流式会话的响应处理器
     */
    private Consumer<ChatStreamPartResponse> chatStreamPartResponseHandler;

    /**
     * 删除TBase的key
     */
    private Consumer<String> needDelTBaseKey;

    /**
     * 是否为重新生成
     */
    private boolean isRegenerate;

    /**
     * 回答的唯一id
     * sessionId + "_" + queryIndex + "_" + generationIndex
     */
    private String uniqueAnswerId;

    /**
     * 回答uid
     */
    private String answerUid;

    /**
     * 请求推理的环境地址， maya场景使用
     */
    private String modelEnv;

    /**
     * 仓库信息
     */
    private RepoInfoModel repoInfo;
    /**
     * 自定义接口url
     */
    private String customizeUrl;

    /**
     * 流式会话需要参数构造器
     *
     * @param requestId
     * @param userName
     * @param stressTest
     * @param request
     * @param resultHandler
     */
    public GptAlgModelServiceRequest(String requestId, String userName, boolean stressTest, AlgoBackendDO algoBackendDO, ChatCompletionRequest request,
                                     Consumer<StreamResponseModel> resultHandler, boolean isRegenerate) {
        this.requestId = requestId;
        this.userName = userName;
        this.stressTest = stressTest;
        this.algoBackendDO = algoBackendDO;
        this.chatCompletionRequest = request;
        this.resultHandler = resultHandler;
        this.isRegenerate = isRegenerate;
    }

    /**
     * 流式会话需要参数构造器
     *
     * @param requestId
     * @param userName
     * @param stressTest
     * @param request
     * @param resultHandler
     */
    public GptAlgModelServiceRequest(String requestId, String userName, boolean stressTest, AlgoBackendDO algoBackendDO, ChatCompletionRequest request,
                                     Consumer<StreamResponseModel> resultHandler) {
        this.requestId = requestId;
        this.userName = userName;
        this.stressTest = stressTest;
        this.algoBackendDO = algoBackendDO;
        this.chatCompletionRequest = request;
        this.resultHandler = resultHandler;
    }

    /**
     * 非流式会话需要参数构造器
     *
     * @param requestId
     * @param userName
     * @param stressTest
     * @param chatCompletionRequest
     */
    public GptAlgModelServiceRequest(String requestId, String userName, boolean stressTest, AlgoBackendDO algoBackendDO, ChatCompletionRequest chatCompletionRequest) {
        this.requestId = requestId;
        this.userName = userName;
        this.stressTest = stressTest;
        this.algoBackendDO = algoBackendDO;
        this.chatCompletionRequest = chatCompletionRequest;
    }

    public GptAlgModelServiceRequest() {
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserOriginalModel() {
        return userOriginalModel;
    }

    public void setUserOriginalModel(String userOriginalModel) {
        this.userOriginalModel = userOriginalModel;
    }

    public AlgoBackendDO getAlgoBackendDO() {
        return algoBackendDO;
    }

    public void setAlgoBackendDO(AlgoBackendDO algoBackendDO) {
        this.algoBackendDO = algoBackendDO;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isStressTest() {
        return stressTest;
    }

    public void setStressTest(boolean stressTest) {
        this.stressTest = stressTest;
    }

    public ChatCompletionRequest getChatCompletionRequest() {
        return chatCompletionRequest;
    }

    public void setChatCompletionRequest(ChatCompletionRequest chatCompletionRequest) {
        this.chatCompletionRequest = chatCompletionRequest;
    }

    public Consumer<StreamResponseModel> getResultHandler() {
        return resultHandler;
    }

    public void setResultHandler(Consumer<StreamResponseModel> resultHandler) {
        this.resultHandler = resultHandler;
    }

    public boolean isRegenerate() {
        return isRegenerate;
    }

    public void setRegenerate(boolean regenerate) {
        isRegenerate = regenerate;
    }

    public Consumer<String> getNeedDelTBaseKey() {
        return needDelTBaseKey;
    }

    public void setNeedDelTBaseKey(Consumer<String> needDelTBaseKey) {
        this.needDelTBaseKey = needDelTBaseKey;
    }

    public String getUniqueAnswerId() {
        return uniqueAnswerId;
    }

    public void setUniqueAnswerId(String uniqueAnswerId) {
        this.uniqueAnswerId = uniqueAnswerId;
    }

    public Consumer<ChatStreamPartResponse> getChatStreamPartResponseHandler() {
        return chatStreamPartResponseHandler;
    }

    public void setChatStreamPartResponseHandler(Consumer<ChatStreamPartResponse> chatStreamPartResponseHandler) {
        this.chatStreamPartResponseHandler = chatStreamPartResponseHandler;
    }

    public Long getRequestUserId() {
        return requestUserId;
    }

    public void setRequestUserId(Long requestUserId) {
        this.requestUserId = requestUserId;
    }

    public String getRequestTokenUser() {
        return requestTokenUser;
    }

    public void setRequestTokenUser(String requestTokenUser) {
        this.requestTokenUser = requestTokenUser;
    }

    public String getModelEnv() {
        return modelEnv;
    }

    public void setModelEnv(String modelEnv) {
        this.modelEnv = modelEnv;
    }

    public RepoInfoModel getRepoInfo() {
        return repoInfo;
    }

    public void setRepoInfo(RepoInfoModel repoInfo) {
        this.repoInfo = repoInfo;
    }

    public String getAnswerUid() {
        return answerUid;
    }

    public void setAnswerUid(String answerUid) {
        this.answerUid = answerUid;
    }

    public String getCustomizeUrl() {
        return customizeUrl;
    }

    public void setCustomizeUrl(String customizeUrl) {
        this.customizeUrl = customizeUrl;
    }
}