package com.alipay.codegencore.model.request;

import com.alibaba.fastjson.JSONObject;

/**
 * 对话请求类型
 * <AUTHOR>
 */
public class CodeGPTQueryRequestBean {
    /**
     * 会话id
     */
    private String sessionUid;
    /**
     * 请求内容
     */
    private String content;
    /**
     * 是否是修改后的请求内容
     */
    private Boolean isModified;

    /**
     * 是否搜索仓库
     */
    private Boolean tryRepoSearch;

    /**
     * 扩展信息
     */
    private JSONObject extraInfo;

    /**
     * mock copilot 模式
     */
    private Boolean mockCopilot;

    public Boolean getModified() {
        return isModified != null && isModified;
    }

    public void setModified(Boolean modified) {
        isModified = modified;
    }

    public String getSessionUid() {
        return sessionUid;
    }

    public void setSessionUid(String sessionUid) {
        this.sessionUid = sessionUid;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getTryRepoSearch() {
        return tryRepoSearch;
    }

    public void setTryRepoSearch(Boolean tryRepoSearch) {
        this.tryRepoSearch = tryRepoSearch;
    }

    public Boolean getMockCopilot() {
        return mockCopilot;
    }

    public void setMockCopilot(Boolean mockCopilot) {
        this.mockCopilot = mockCopilot;
    }

    public JSONObject getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(JSONObject extraInfo) {
        this.extraInfo = extraInfo;
    }
}
