package com.alipay.codegencore.model.enums;

/**
 * 产品名字枚举
 *
 * <AUTHOR>
 * 创建时间 2023-03-07
 */
public enum ProductNameTypeEnum {
    /**
     * cloud ide 内部青燕
     */
    CLOUD_IDE_ANT_TSINGYAN(1),
    /**
     * cloud ide 外部青燕
     */
    CLOUD_IDE_TSINGYAN(2),
    /**
     * 外部青燕
     */
    TSINGYAN(3),
    /**
     * 蚂蚁内部青燕
     */
    ANT_TSINGYAN(4)
    ;

    ProductNameTypeEnum(int type) {
        this.type = type;
    }

    private int type;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
