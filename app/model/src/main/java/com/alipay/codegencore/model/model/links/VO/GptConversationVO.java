/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links.VO;


import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.model.links.Enum.GptConversationChannelEnum;
import com.alipay.codegencore.model.model.links.Enum.GptConversationStatusEnum;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: 2023-05-29 13:23ManagerImpl.java, v 0.1 2023-05-29 13:23 tanzhigang Exp $$
 */
public class GptConversationVO extends BaseVO {

    /**
     * 渠道
     */
    private GptConversationChannelEnum channel;

    /**
     * 渠道业务id
     */
    private String bizId;

    /**
     * 租户id
     */
    private String roomId;

    /**
     * 创建人
     */
    private String userId;

    /**
     * 状态
     */
    private GptConversationStatusEnum status;

    /**
     * 第一条消息
     */
    private GptMessageVO firstMessage;

    /**
     * 最后一条消息
     */
    private GptMessageVO lastMessage;

    /**
     * 租户名称
     */
    private String roomName;

    /**
     * gpt消息，只有openapi提供
     */
    private List<GptMessageVO> messages;

    /**
     * 最后一条消息时间
     */
    private Date gmtLastMessage;

    /**
     * 会话名称
     */
    private String title;

    /**
     * 人工会话id
     */
    private String conversationId;

    /**
     * 扩展信息
     */
    private JSONObject extInfo;

    public GptConversationChannelEnum getChannel() {
        return channel;
    }

    public void setChannel(GptConversationChannelEnum channel) {
        this.channel = channel;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public GptConversationStatusEnum getStatus() {
        return status;
    }

    public void setStatus(GptConversationStatusEnum status) {
        this.status = status;
    }

    public GptMessageVO getFirstMessage() {
        return firstMessage;
    }

    public void setFirstMessage(GptMessageVO firstMessage) {
        this.firstMessage = firstMessage;
    }

    public GptMessageVO getLastMessage() {
        return lastMessage;
    }

    public void setLastMessage(GptMessageVO lastMessage) {
        this.lastMessage = lastMessage;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public List<GptMessageVO> getMessages() {
        return messages;
    }

    public void setMessages(List<GptMessageVO> messages) {
        this.messages = messages;
    }

    public Date getGmtLastMessage() {
        return gmtLastMessage;
    }

    public void setGmtLastMessage(Date gmtLastMessage) {
        this.gmtLastMessage = gmtLastMessage;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public JSONObject getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(JSONObject extInfo) {
        this.extInfo = extInfo;
    }
}