/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: GptMessageFeedbackContent.java, v 0.1 2023-05-30 12:36 wb-tzg858080 Exp $$
 */
public class GptMessageFeedbackContent extends BeanStringSwitcherImpl {
    /**
     * 标签
     */
    private List<String> tags;
    /**
     * 描述
     */
    private String remark;

    /**
     * 反馈内容
     */
    private Map<String,Integer> checkFeedback;

    /**
     * 理解问题有误
     */
    private List<String> questionError;

    /**
     * 回复内容有误
     */
    private List<String> contentError;

    /**
     * 其他有害信息
     */
    private List<String> other;

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Map<String, Integer> getCheckFeedback() {
        return checkFeedback;
    }

    public void setCheckFeedback(Map<String, Integer> checkFeedback) {
        this.checkFeedback = checkFeedback;
    }

    public List<String> getQuestionError() {
        return questionError;
    }

    public void setQuestionError(List<String> questionError) {
        this.questionError = questionError;
    }

    public List<String> getContentError() {
        return contentError;
    }

    public void setContentError(List<String> contentError) {
        this.contentError = contentError;
    }

    public List<String> getOther() {
        return other;
    }

    public void setOther(List<String> other) {
        this.other = other;
    }
}
