package com.alipay.codegencore.model.openai;

import com.alipay.codegencore.model.domain.DocumentDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.enums.SceneAvailableStatus;
import com.alipay.codegencore.model.model.PluginCommand;

import java.util.List;

/**
 *
 * 用户收藏助手 打标
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.model.openai
 * @CreateTime : 2023-07-19
 */
public class UserSaveSceneVO extends SceneDO {

    /**
     * 用户是否收藏当前助手
     */
    private boolean userSaveScene;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 助手负责人的工号
     */
    private String ownerUserEmpId;
    /**
     * 助手负责人的花名(姓名)
     */
    private String ownerUserName;

    /**
     * 是否是默认助手
     */
    private Boolean defaultScene;

    /**
     * 助手绑定的文件详情
     */
    private List<DocumentDO> documentList;

    /**
     * 助手状态
     */
    private SceneAvailableStatus sceneAvailableStatus;
    /**
     * 是否是固定匹配插件
     */
    private Boolean callFunctionEveryRound;
    /**
     * 助手绑定的插件列表
     */
    private List<PluginCommand> pluginCommandList;


    public Boolean getCallFunctionEveryRound() {
        return callFunctionEveryRound;
    }

    public void setCallFunctionEveryRound(Boolean callFunctionEveryRound) {
        this.callFunctionEveryRound = callFunctionEveryRound;
    }

    public SceneAvailableStatus getSceneAvailableStatus() {
        return sceneAvailableStatus;
    }

    public void setSceneAvailableStatus(SceneAvailableStatus sceneAvailableStatus) {
        this.sceneAvailableStatus = sceneAvailableStatus;
    }

    public List<DocumentDO> getDocumentList() {
        return documentList;
    }

    public void setDocumentList(List<DocumentDO> documentList) {
        this.documentList = documentList;
    }

    public Boolean getDefaultScene() {
        return defaultScene;
    }

    public void setDefaultScene(Boolean defaultScene) {
        this.defaultScene = defaultScene;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public boolean isUserSaveScene() {
        return userSaveScene;
    }

    public void setUserSaveScene(boolean userSaveScene) {
        this.userSaveScene = userSaveScene;
    }

    public String getOwnerUserEmpId() {
        return ownerUserEmpId;
    }

    public void setOwnerUserEmpId(String ownerUserEmpId) {
        this.ownerUserEmpId = ownerUserEmpId;
    }

    public String getOwnerUserName() {
        return ownerUserName;
    }

    public void setOwnerUserName(String ownerUserName) {
        this.ownerUserName = ownerUserName;
    }

    public List<PluginCommand> getPluginCommandList() {
        return pluginCommandList;
    }

    public void setPluginCommandList(List<PluginCommand> pluginCommandList) {
        this.pluginCommandList = pluginCommandList;
    }

}
