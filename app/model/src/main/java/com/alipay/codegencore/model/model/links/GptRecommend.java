/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version GptRecommend.java, v 0.1 2023年11月08日 上午10:30 wb-tzg858080
 */
public class GptRecommend extends ToString {
    /**
     * 文档id
     */
    private String docId ;
    /**
     * 文档url
     */
    private String docUrl ;

    /**
     * 文档标题
     */
    private String title ;
    /**
     * 文档类型
     */
    private String type ;

    public String getDocId() {
        return docId;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getDocUrl() {
        return docUrl;
    }

    public void setDocUrl(String docUrl) {
        this.docUrl = docUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
