package com.alipay.codegencore.model.domain;

import java.util.Date;

public class UserFeedbackDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.gmt_create
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.gmt_modified
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.user_id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.content
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private String content;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.pictures
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private String pictures;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.status
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.acceptance_user
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private Long acceptanceUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_feedback.scene_id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    private Long sceneId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.id
     *
     * @return the value of cg_user_feedback.id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.id
     *
     * @param id the value for cg_user_feedback.id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.gmt_create
     *
     * @return the value of cg_user_feedback.gmt_create
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.gmt_create
     *
     * @param gmtCreate the value for cg_user_feedback.gmt_create
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.gmt_modified
     *
     * @return the value of cg_user_feedback.gmt_modified
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.gmt_modified
     *
     * @param gmtModified the value for cg_user_feedback.gmt_modified
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.user_id
     *
     * @return the value of cg_user_feedback.user_id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.user_id
     *
     * @param userId the value for cg_user_feedback.user_id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.content
     *
     * @return the value of cg_user_feedback.content
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.content
     *
     * @param content the value for cg_user_feedback.content
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.pictures
     *
     * @return the value of cg_user_feedback.pictures
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public String getPictures() {
        return pictures;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.pictures
     *
     * @param pictures the value for cg_user_feedback.pictures
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setPictures(String pictures) {
        this.pictures = pictures;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.status
     *
     * @return the value of cg_user_feedback.status
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.status
     *
     * @param status the value for cg_user_feedback.status
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.acceptance_user
     *
     * @return the value of cg_user_feedback.acceptance_user
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public Long getAcceptanceUser() {
        return acceptanceUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.acceptance_user
     *
     * @param acceptanceUser the value for cg_user_feedback.acceptance_user
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setAcceptanceUser(Long acceptanceUser) {
        this.acceptanceUser = acceptanceUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_feedback.scene_id
     *
     * @return the value of cg_user_feedback.scene_id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public Long getSceneId() {
        return sceneId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_feedback.scene_id
     *
     * @param sceneId the value for cg_user_feedback.scene_id
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    public void setSceneId(Long sceneId) {
        this.sceneId = sceneId;
    }
}