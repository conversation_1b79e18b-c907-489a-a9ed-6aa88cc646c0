package com.alipay.codegencore.model.request.answer;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.copilot.CodeReference;
import com.alipay.codegencore.model.openai.AnswerChatMessage;
import com.alipay.codegencore.model.openai.PluginContext;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.List;

/**
 * 问答请求
 */
public class AnswerRequest {

    private String userId;

    private String repoUrl;

    private String branch;

    private List<AnswerChatMessage> message;

    private JSONObject clientConfig;

    private List<CodeReference> references;

    private PluginContext context;


    public String getRepoUrl() {
        return repoUrl;
    }

    public void setRepoUrl(String repoUrl) {
        this.repoUrl = repoUrl;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<AnswerChatMessage> getMessage() {
        return message;
    }

    public void setMessage(List<AnswerChatMessage> message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public JSONObject getClientConfig() {
        return clientConfig;
    }

    public void setClientConfig(JSONObject clientConfig) {
        this.clientConfig = clientConfig;
    }

    public List<CodeReference> getReferences() {
        return references;
    }

    public void setReferences(List<CodeReference> references) {
        this.references = references;
    }

    public PluginContext getContext() {
        return context;
    }

    public void setContext(PluginContext context) {
        this.context = context;
    }
}
