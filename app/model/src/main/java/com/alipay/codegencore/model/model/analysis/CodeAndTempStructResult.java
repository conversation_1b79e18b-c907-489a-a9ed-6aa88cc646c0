package com.alipay.codegencore.model.model.analysis;

/**
 * 代码分析后的结果
 * 包含{@link CodeStructResult} 和{@link TempCodeAnalysisResultContext}两个子集
 *
 * <AUTHOR>
 * 创建时间 2022-08-17
 */
public class CodeAndTempStructResult extends AbstractCodeAnalysisResult{
    /**
     * 代码结构分析结果
     */
    private CodeStructResult codeStructResult;
    /**
     * 经过处理后的静态分析结果，用于代码补全流程
     */
    private TempCodeAnalysisResultContext tempCodeAnalysisResultContext;

    public CodeStructResult getCodeStructResult() {
        return codeStructResult;
    }

    public void setCodeStructResult(CodeStructResult codeStructResult) {
        this.codeStructResult = codeStructResult;
    }

    public TempCodeAnalysisResultContext getTempCodeAnalysisResultContext() {
        return tempCodeAnalysisResultContext;
    }

    public void setTempCodeAnalysisResultContext(TempCodeAnalysisResultContext tempCodeAnalysisResultContext) {
        this.tempCodeAnalysisResultContext = tempCodeAnalysisResultContext;
    }
}
