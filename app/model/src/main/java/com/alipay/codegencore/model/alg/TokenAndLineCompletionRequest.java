package com.alipay.codegencore.model.alg;

import java.util.Map;

/**
 * token/行级补全请求参数
 *
 * <AUTHOR>
 * 创建时间 2022-03-31
 */
public class TokenAndLineCompletionRequest {
    /**
     * token上下文
     */
//    private List<TokenModel> contexts;
    private String contexts;
    /**
     * traceId
     */
    private String traceid;
    /**
     * token候选集
     * eg."toString":{"0":["char", "string"], "1":["info", "char"]},
     */
    private Map<String, Map<String,String[]>> tokens;

//    public List<TokenModel> getContexts() {
//        return contexts;
//    }
//
//    public void setContexts(List<TokenModel> contexts) {
//        this.contexts = contexts;
//    }


    public String getContexts() {
        return contexts;
    }

    public void setContexts(String contexts) {
        this.contexts = contexts;
    }

    public String getTraceid() {
        return traceid;
    }

    public void setTraceid(String traceid) {
        this.traceid = traceid;
    }

    public Map<String, Map<String, String[]>> getTokens() {
        return tokens;
    }

    public void setTokens(Map<String, Map<String, String[]>> tokens) {
        this.tokens = tokens;
    }
}
