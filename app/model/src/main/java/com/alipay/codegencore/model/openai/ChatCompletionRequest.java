package com.alipay.codegencore.model.openai;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.codegencore.model.model.PluginCommand;
import com.alipay.codegencore.model.model.rag.DocSearchResultItem;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ChatCompletionRequest {

    /**
     * ID of the model to use. Currently, only gpt-3.5-turbo and gpt-3.5-turbo-0301 are supported.
     */
    private String model;

    /**
     * The messages to generate chat completions for, in the <a
     * href="https://platform.openai.com/docs/guides/chat/introduction">chat format</a>.<br>
     * see {@link ChatMessage}
     */
    private List<ChatMessage> messages;

    /**
     * 用于补全，不需要构建对话
     */
    private String prompt;

    /**
     * What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower
     * values like 0.2 will make it more focused and deterministic.<br>
     * We generally recommend altering this or top_p but not both.
     */
    private Double temperature;

        /**
     * An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens
     * with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.<br>
     * We generally recommend altering this or temperature but not both.
     */
    private Double topP;

    /**
     * 较小的topK值会使生成的文本更准确，较大的topK值则会增加生成文本的多样性。
     */
    @JSONField(serialize = false)
    private Integer topK;

    /**
     * How many chat completion chatCompletionChoices to generate for each input message.
     */
    private Integer n;

    /**
     * If set, partial message deltas will be sent, like in ChatGPT. Tokens will be sent as data-only <a
     * href="https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events#Event_stream_format">server-sent
     * events</a> as they become available, with the stream terminated by a data: [DONE] message.
     */
    private Boolean stream = false;

    /**
     * 最大长度为 4 的字符串列表，一旦生成的 tokens 包含其中的内容，将停止生成并返回结果
     */
    private List<String> stop;

    /**
     * The maximum number of tokens allowed for the generated answer. By default, the number of tokens the model can return will
     * be (4096 - prompt tokens).
     */
    private Integer maxTokens;

    /**
     * 最大轮数
     */
    @JSONField(serialize = false)
    private Integer maxRound;

    /**
     * 是否开启gptCache
     */
    @JSONField(serialize = false)
    private Boolean enableGptCache;

    @JSONField(serialize = false)
    private String streamProtocolVersion;


    /**
     * Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far,
     * increasing the model's likelihood to talk about new topics.
     */
    private Double presencePenalty;

    /**
     * Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far,
     * decreasing the model's likelihood to repeat the same line verbatim.
     */
    private Double frequencyPenalty;

    /**
     * Accepts a json object that maps tokens (specified by their token ID in the tokenizer) to an associated bias value from -100
     * to 100. Mathematically, the bias is added to the logits generated by the model prior to sampling. The exact effect will
     * vary per model, but values between -1 and 1 should decrease or increase likelihood of selection; values like -100 or 100
     * should result in a ban or exclusive selection of the relevant token.
     */
    private Map<String, Integer> logitBias;

    /**
     * A list of the available functions.
     */
    List<ChatFunction> functions;

    /**
     * 文档搜索结果
     */
    @JSONField(serialize = false)
    List<DocSearchResultItem> docs;

    /**
     * 可选值为字符串类型: none, auto, 或者一个对象 {"name": "my_function"}
     * none代表模型不调用函数，直接推理
     * auto代表模型可以选择调用函数或者直接推理
     * {"name": "my_function"}代表模型强制调用指定的函数
     * Controls how the model responds to function calls, as specified in the <a href="https://platform.openai.com/docs/api-reference/chat/create#chat/create-function_call">OpenAI documentation</a>.
     */
    Object functionCall;
    /**
     * A unique identifier representing your end-user, which will help OpenAI to monitor and detect abuse.
     */
    private String user;

    /**
     * 是否仅输出模型的决策
     */
    @JSONField(serialize = false)
    private Boolean onlyDecide = false;

    /**
     * 额外的业务字段
     */
    @JSONField(serialize = false)
    private ChatRequestExtData chatRequestExtData;

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    /**
     * 需要覆盖模型的参数配置
     */
    @JSONField(serialize = false)
    private JSONObject modelOverrideConfig;
    /**
     * 工具调用配置自定义参数
     */
    @JSONField(serialize = false)
    private JSONObject extraInfo;

    /**
     * 工具指令
     */
    private PluginCommand pluginCommand;

    public Integer getMaxRound() {
        return maxRound;
    }

    public Boolean getEnableGptCache() {
        return enableGptCache;
    }

    public void setEnableGptCache(Boolean enableGptCache) {
        this.enableGptCache = enableGptCache;
    }

    public String getStreamProtocolVersion() {
        return streamProtocolVersion;
    }

    public void setStreamProtocolVersion(String streamProtocolVersion) {
        this.streamProtocolVersion = streamProtocolVersion;
    }

    public void setMaxRound(Integer maxRound) {
        this.maxRound = maxRound;
    }

    public JSONObject getModelOverrideConfig() {
        return modelOverrideConfig;
    }

    public void setModelOverrideConfig(JSONObject modelOverrideConfig) {
        this.modelOverrideConfig = modelOverrideConfig;
    }

    public ChatRequestExtData getChatRequestExtData() {
        return chatRequestExtData;
    }

    public void setChatRequestExtData(ChatRequestExtData chatRequestExtData) {
        this.chatRequestExtData = chatRequestExtData;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<ChatMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<ChatMessage> messages) {
        this.messages = messages;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    @JSONField(name = "top_p")
    public Double getTopP() {
        return topP;
    }

    public void setTopP(Double topP) {
        this.topP = topP;
    }

    public Integer getN() {
        return n;
    }

    public void setN(Integer n) {
        this.n = n;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    public List<String> getStop() {
        return stop;
    }

    public void setStop(List<String> stop) {
        this.stop = stop;
    }

    @JSONField(name = "max_tokens")
    public Integer getMaxTokens() {
        return maxTokens;
    }

    @JsonProperty("maxTokens")
    @JsonAlias("max_tokens")
    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    @JSONField(name = "presence_penalty")
    public Double getPresencePenalty() {
        return presencePenalty;
    }

    @JsonProperty("presencePenalty")
    @JsonAlias("presence_penalty")
    public void setPresencePenalty(Double presencePenalty) {
        this.presencePenalty = presencePenalty;
    }

    @JSONField(name = "frequency_penalty")
    public Double getFrequencyPenalty() {
        return frequencyPenalty;
    }

    @JsonProperty("frequencyPenalty")
    @JsonAlias("frequency_penalty")
    public void setFrequencyPenalty(Double frequencyPenalty) {
        this.frequencyPenalty = frequencyPenalty;
    }

    @JSONField(name = "logit_bias")
    public Map<String, Integer> getLogitBias() {
        return logitBias;
    }

    @JsonProperty("logitBias")
    @JsonAlias("logit_bias")
    public void setLogitBias(Map<String, Integer> logitBias) {
        this.logitBias = logitBias;
    }

    public List<ChatFunction> getFunctions() {
        return functions;
    }

    public void setFunctions(List<ChatFunction> functions) {
        this.functions = functions;
    }

    public List<DocSearchResultItem> getDocs() {
        return docs;
    }

    public void setDocs(List<DocSearchResultItem> docs) {
        this.docs = docs;
    }

    @JSONField(name = "function_call")
    public Object getFunctionCall() {
        return functionCall;
    }

    @JsonProperty("functionCall")
    @JsonAlias("function_call")
    public void setFunctionCall(Object functionCall) {
        this.functionCall = functionCall;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public Boolean getOnlyDecide() {
        return onlyDecide;
    }

    public void setOnlyDecide(Boolean onlyDecide) {
        this.onlyDecide = onlyDecide;
    }

    public JSONObject getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(JSONObject extraInfo) {
        this.extraInfo = extraInfo;
    }

    public PluginCommand getPluginCommand() {
        return pluginCommand;
    }

    public void setPluginCommand(PluginCommand pluginCommand) {
        this.pluginCommand = pluginCommand;
    }
}
