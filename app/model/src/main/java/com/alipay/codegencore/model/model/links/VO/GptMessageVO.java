/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links.VO;


import com.alipay.codegencore.model.model.links.GptMessageContent;
import com.alipay.codegencore.model.model.links.Enum.GptMessageTypeEnum;

import java.util.List;


/**
 * <AUTHOR>
 * @version $Id: 2023-05-29 16:30ManagerImpl.java, v 0.1 2023-05-29 16:30 tanzhigang Exp $$
 */
public class GptMessageVO extends BaseVO {

    /**
     * 会话id
     */
    private String conversationId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 消息类型
     */
    private GptMessageTypeEnum type;

    /**
     * 消息内容
     */
    private GptMessageContent content;


    /**
     * 多数据源消息内容
     */
    private List<GptMessageContent> contents;

    /**
     * 回复id
     */
    private String replayMessageId;

    /**
     * 是否赞同
     */
    private Boolean agreed;

    /**
     * 反馈内容 openapi获取
     */
    private GptMessageFeedbackVO feedback;

    /**
     * gpt模型
     */
    private String gptModel;

    /**
     * 是否发送过
     */
    private Boolean hasBeenSend = false;

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public GptMessageTypeEnum getType() {
        return type;
    }

    public void setType(GptMessageTypeEnum type) {
        this.type = type;
    }

    public GptMessageContent getContent() {
        return content;
    }

    public void setContent(GptMessageContent content) {
        this.content = content;
    }

    public List<GptMessageContent> getContents() {
        return contents;
    }

    public void setContents(List<GptMessageContent> contents) {
        this.contents = contents;
    }

    public String getReplayMessageId() {
        return replayMessageId;
    }

    public void setReplayMessageId(String replayMessageId) {
        this.replayMessageId = replayMessageId;
    }

    public Boolean getAgreed() {
        return agreed;
    }

    public void setAgreed(Boolean agreed) {
        this.agreed = agreed;
    }

    public GptMessageFeedbackVO getFeedback() {
        return feedback;
    }

    public void setFeedback(GptMessageFeedbackVO feedback) {
        this.feedback = feedback;
    }

    public String getGptModel() {
        return gptModel;
    }

    public void setGptModel(String gptModel) {
        this.gptModel = gptModel;
    }

    public Boolean getHasBeenSend() {
        return hasBeenSend;
    }

    public void setHasBeenSend(Boolean hasBeenSend) {
        this.hasBeenSend = hasBeenSend;
    }
}