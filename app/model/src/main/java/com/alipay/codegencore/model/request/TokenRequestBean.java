/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.request;

import com.alipay.codegencore.model.domain.TokenDO;

/**
 * <AUTHOR>
 * @version TokenRequestBean.java, v 0.1 2023年08月14日 下午3:48 yhw01352860
 */
public class TokenRequestBean extends TokenDO {
    private String empId;

    private Boolean needEditToken = false;

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public Boolean getNeedEditToken() {
        return needEditToken;
    }

    public void setNeedEditToken(Boolean needEditToken) {
        this.needEditToken = needEditToken;
    }
}
