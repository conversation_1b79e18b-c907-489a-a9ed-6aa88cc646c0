package com.alipay.codegencore.model.response;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageInfo;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 新版插件流式消息
 */
public class NewPluginStreamPartResponse {
    private String id;
    private PluginInfo pluginInfo;
    private String stageName;
    private String finishReason;
    /**
     * 支持多插件，index表示插件的序号
     */
    private Integer pluginIndex;
    /**
     * 插件阶段序号
     */
    private Integer stageIndex;
    /**
     * 输出内容
     */
    private String content;
    /**
     * 插件阶段list，插件最开始输出，方便前端了解插件共有几个阶段
     */
    private List<String> stageList;
    /**
     * 安全审查结果，一般只有大模型调用阶段才有
     */
    private CheckResultModel checkResultModel;

    /**
     * 阶段类型
     */
    private String type;

    /**
     * 结构化的信息
     */
    private StageInfo stageInfo;

    private String traceId;
    /**
     * 是否清除content
     */
    private Boolean clear;
    /**
     * functionCall
     */
    private ChatFunctionCall chatFunctionCall;

    public ChatFunctionCall getChatFunctionCall() {
        return chatFunctionCall;
    }

    public void setChatFunctionCall(ChatFunctionCall chatFunctionCall) {
        this.chatFunctionCall = chatFunctionCall;
    }

    public NewPluginStreamPartResponse() {
    }

    /**
     * 由ChatStreamPartResponse构造
     * @param chatStreamPartResponse
     */
    public NewPluginStreamPartResponse(ChatStreamPartResponse chatStreamPartResponse){
        this.id = chatStreamPartResponse.getId();
        this.type = StringUtils.isBlank(chatStreamPartResponse.getType())?StageTypeEnum.ANSWER.getName():chatStreamPartResponse.getType();
        this.content = chatStreamPartResponse.getChoices().get(0).getDelta().getContent();
        this.checkResultModel = chatStreamPartResponse.getChoices().get(0).getCheckResultModel();
        this.clear = chatStreamPartResponse.getClear();
        this.traceId = chatStreamPartResponse.getTraceId();
        if (AppConstants.DEFAULT_STREAM_FINISH_REASON.equalsIgnoreCase(chatStreamPartResponse.getChoices().get(0).getFinishReason())){
            this.finishReason = ResponseEnum.SUCCESS.name();
        }else{
            this.finishReason = chatStreamPartResponse.getChoices().get(0).getFinishReason();
        }
    }

    /**
     * 构造函数
     * @param finishReason
     * @param pluginIndex
     * @param type
     * @param stageInfo
     */
    public NewPluginStreamPartResponse(String id, String finishReason, Integer pluginIndex, Integer stageIndex, String type, StageInfo stageInfo) {
        this.id = id;
        this.finishReason = finishReason;
        this.pluginIndex = pluginIndex;
        this.stageIndex = stageIndex;
        this.type = type;
        this.stageInfo = stageInfo;
        this.clear = false;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public PluginInfo getPluginInfo() {
        return pluginInfo;
    }

    public void setPluginInfo(PluginInfo pluginInfo) {
        this.pluginInfo = pluginInfo;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public String getFinishReason() {
        return finishReason;
    }

    public void setFinishReason(String finishReason) {
        this.finishReason = finishReason;
    }

    public Integer getPluginIndex() {
        return pluginIndex;
    }

    public void setPluginIndex(Integer pluginIndex) {
        this.pluginIndex = pluginIndex;
    }

    public Integer getStageIndex() {
        return stageIndex;
    }

    public void setStageIndex(Integer stageIndex) {
        this.stageIndex = stageIndex;
    }

    public List<String> getStageList() {
        return stageList;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setStageList(List<String> stageList) {
        this.stageList = stageList;
    }

    public CheckResultModel getCheckResultModel() {
        return checkResultModel;
    }

    public void setCheckResultModel(CheckResultModel checkResultModel) {
        this.checkResultModel = checkResultModel;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public StageInfo getStageInfo() {
        return stageInfo;
    }

    public void setStageInfo(StageInfo stageInfo) {
        this.stageInfo = stageInfo;
    }

    public Boolean getClear() {
        return clear;
    }

    public void setClear(Boolean clear) {
        this.clear = clear;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
}
