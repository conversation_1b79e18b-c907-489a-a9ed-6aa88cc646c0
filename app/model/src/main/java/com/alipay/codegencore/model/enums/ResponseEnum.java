package com.alipay.codegencore.model.enums;

/**
 * 返回值状态枚举
 * 0 : 成功
 * 1-10 : 服务框架类异常
 *
 * <AUTHOR>
 * 创建时间 2021-11-10
 */
public enum ResponseEnum {

    SUCCESS(0, "成功"),
    HTTP_ERROR(4, "http请求异常"),

    NO_AUTH(6, "没有当前操作权限"),

    ILLEGAL_PARAMETER(12, "非法参数"),
    SESSION_TITLE_LENTH_ILLEGAL(13, "会话名称长度异常"),
    USER_SESSION_NOT_MATCH(14, "用户与会话不匹配"),
    MESSAGE_IS_NOT_EXIST(15, "消息不存在"),
    UPDATE_DATABASE_FAILED(16, "更新数据库失败"),
    SESSION_IS_NOT_EXIST(17, "会话不存在"),
    SESSION_QUERY_STATE_ILLEGAL(18, "会话状态异常，提问时上一条消息还是问题"),
    SESSION_HAS_MESSAGE_CAN_NOT_UPDATE_MODEL(19, "会话中已有消息，无法更新，请刷新页面之后查看"),
    SESSION_CREATE_FAIL(20, "会话创建失败"),

    /**
     * 安全审核相关异常
     */
    CHECK_FAILED(21, "安全审核不通过"),
    QUESTION_DATA_CHECK_FAILED(22, "问题数据审核不通过"),
    QUESTION_CONTENT_FAILED(23, "问题内容审核不通过"),
    QUESTION_DATA_CONTENT_FAILED(24, "问题内容和数据审核不通过"),
    ANSWER_CONTENT_FAILED(25, "答案内容审核不通过"),


    /**
     * 当前在线用户超出限制
     */
    ONLINE_USER_ANOMALY(26, "当前在线用户过多，请稍后再试"),
    CONTENT_TITLE_NULL(27,"ai模型生成标题失败 标题为空"),
    AI_CALL_ERROR(28,"AI模型调用发生异常，请稍后重试"),

    ACCOUNT_ARREARS(29,"账户欠费"),
    USER_NOT_LOGIN(30, "用户未登录"),


    CALCULATE_TOKEN_FAILED(31,"计算token数量失败"),
    STREAM_PART_NOT_START_WITH_DATA_PREFIX(32,"流式输出格式非法，没有以data: 开头，不符合sse协议"),
    STREAM_DATA_CAN_NOT_BE_DESERIALIZED(34,"流式输出data字段无法反序列化"),
    STREAM_DELTA_PROCESS_FAILED(35,"处理流式输出某一个数据包的时候失败"),
    STREAM_THROW_EXCEPTION(36, "处理stream时出现异常"),

    MAYA_RESPONSE_ERROR(40, "MAYA返回其他错误码， 需要做进行细化"),
    // MAYA换返回异常， 规则为400 + MAYA错误码
    MAYA_ERROR_TIMEOUT(401,"MAYA异常，服务超时，可能是由于会话上文过长，可以尝试重试，或者启动新会话"),
    MAYA_ERROR_SCENE_ERR(402,"MAYA异常，可能是服务繁忙（需要进一步细化）"),
    MAYA_ERROR_PARAM_ERR(403,"MAYA异常，参数异常"),
    MAYA_ERROR_SYSTEM_ERR(404,"MAYA异常，系统异常"),
    MAYA_ERROR_SERVICE_ERR(405,"MAYA异常，服务端异常"),
    MAYA_ERROR_FLOW_CONTROL(406,"MAYA异常，FLOW_CONTROL异常（需要进一步细化）"),
    MAYA_ERROR_OTHERS_ERR(407,"MAYA异常，其他异常（需要进一步细化）"),
    MAYA_ERROR_UNKNOWN(440,"MAYA异常，未知异常UNKNOWN（需要进一步细化）"),
    MAYA_ERROR_INTERRUPTED(441,"MAYA异常，推理中断"),
    MAYA_ERROR_NULL_RESULT(442,"MAYA异常，推理结果为null"),
    MAYA_ERROR_CANCELLED(443,"MAYA异常，推理被取消"),
    MAYA_ERROR_SERVER_NOT_FOUND(444,"MAYA异常，当前生产环境未部署，请确认模型是否已经部署到生产环境。如果模型部署在其他环境，请切换环境后再重试"),

    MODEL_NOT_EXISTED(43, "模型不存在"),

    NOT_ALLOW_USER_CLOSE_CHECK(44, "不允许用户关闭审查"),

    ANSWER_OUTPUT_TIME_OUT(45, "答案输出超时。模型推理服务没有在允许的时间内输出结果，可能是推理服务状态异常或繁忙。请稍后重试，如果持续出现可联系管理员排查解决"),

    MAYA_CLIENT_EXCEPTION(46, "MAYA客户端报错"),

    FUNCTION_NOT_EXIST(47, "function不存在"),

    MAYA_RESPONSE_IS_EMPTY(48, "MAYA返回内容为空"),

    REPEAT_QUESTIONS(50,"AI正在回复,请稍后刷新页面重新尝试"),

    MODEL_UNSERVICEABLE(51,"模型已下线"),

    QUESTION_LENGTH_EXCEEDS_LIMIT(52, "当前输入问题过长，请缩短问题长度重新提问"),

    ONLY_ASSISTANT_HAS_PLUGIN_LOG(56, "只有模型回答的uid才有插件日志"),

    SCENE_REPEAT(57, "名字重复,请重新输入"),

    USER_SCENE_NOT_MATCH(58, "用户与助手不匹配"),

    SCENE_NOT_ENABLE(59, "场景未启用 请启用后重试"),

    SCENE_PLUGIN_NOT_PRESENCE(60, "场景或插件不存在,请重试"),

    PLUGIN_CHECK_CONFIG(61, "校验yaml失败"),


    /**
     * 用户原因导致异常
     */
    USER_CANCELED(63, "\uD83D\uDC4C 我先不想了，有需要可以随时问我"),

    PRE_NOT_ALLOW_USE_COMMON_POWER(64, "预发环境不允许使用commonPower接口,请使用生产环境"),

    REFRESH_WEB_RETRY(65, "请刷新页面重试"),

    CURRENT_TOKEN_LIMITING_ANOMALY(66, "使用的token当前并发太高限流"),

    CURRENT_ALGO_LIMITING_ANOMALY(67, "请求的模型当前并发太高限流"),

    OSS_OPERATION_FAILED(68, "OSS操作失败"),

    SESSION_ALREADY_BINDING_FILE(69, "当前会话已经绑定文件,暂不支持多个文件"),

    UPLOAD_FILE_FAILED(70, "上传文件失败"),

    FILE_TYPE_NOT_SUPPORTED(71, "该文件类型不支持"),

    FILE_TOO_BIG(72, "该文件太大"),

    FILE_SUMMARY_FAILED(73, "概要总结失败"),

    FILE_EMBEDDING_FAILED(74, "文件embedding失败"),

    NEED_UPLOAD_FILE_FAILED(75, "当前会话需要上传文件"),

    FILE_CONTENT_TOO_MANY(76, "文件内容太多"),

    SESSION_DELETE_PRE_FILE(77, "正在删除上一个文件,请稍后重试"),

    FILE_UID_IS_ILLEGAL(78, "文件UID不存在"),

    FILE_SUMMARY_ALREADY_RETURN_USER(79, "该文件总结已经返回过了"),

    FILE_ANALYSIS_FAILED(80, "文件解析失败"),

    MQ_TIME_OUT(81, "获取mq返回数据超时"),

    SCENE_TYPE_ERROR(82, "助手类型错误"),

    USER_HTTP_REQUEST_LIMITING_ANOMALY(83, "webapi请求频率太高"),

    OPEN_API_REQUEST_LIMITING_ANOMALY(84, "openapi请求频率太高"),

    PAUSE(85, "暂停"),

    CREATE_DIMA_WORKITEM_FAILED(86, "创建dima工作项失败"),

    PAUSE_CONVERSATION(87, "暂停对话未完成"),

    DEPLOYMENT_ENVIRONMENT_INCONSISTENCY(88,"校验网络环境与application.name失败"),

    OPEN_API_REQUEST_MODEL_LIMITING_ANOMALY(89, "openapi请求模型频率太高"),
    WEB_API_REQUEST_MODEL_LIMITING_ANOMALY(90, "webapi请求模型频率太高"),

    DOCUMENT_NOT_READY(91, "文档尚未准备好"),
    DOCUMENT_PARSE_FAILED(93, "文档解析失败"),
    ZSEARCH_QUERY_FAILED(94, "zsearch查询失败"),

    SCENE_MODE_NOT_EXIST(95, "Agent 配置未完成，请编辑完成后再启用"),

    //仓库问答错误码
    ANSWER_REPO_NOT_FOUND(120, "仓库不存在/不可用"),
    ANSWER_BUILD_TASK_CREATE_FAIL(121, "索引任务构建失败"),
    ANSWER_JOB_RESULT_ARG_MISS(122, "job结果回调参数缺失"),
    ANSWER_JOB_RESULT_HANDLE_FAIL(123, "job结果处理失败"),
    ANSWER_REPO_NOT_ALLOW(124, "代码仓库暂不支持"),
    ANSWER_REPO_BUILD_TOO_OFTEN(125, "代码仓库构建过于频繁"),
    ANSWER_REPO_FAIL(126, "仓库问答失败"),
    ANSWER_REPO_ARG_ERR(127, "代码仓库信息不正确，请详细检查是否提供正确的代码仓库信息"),
    ANSWER_REPO_INDEX_BUILDING(128, "首次提问需要10秒左右构建仓库索引，大仓库时间稍久一些，请稍等一会再试~"),
    ANSWER_REPO_NOT_INDEX(129, "代码仓库未索引"),
    ANSWER_BUILD_TYPE_NOT_SUPPORT(131, "索引构建方式不支持"),
    ANSWER_BUILD_WIKI_FAILED(132, "wiki任务创建失败"),
    ANSWER_BUILD_WIKI_TASK_NOT_FOUND(132, "仓库对应索引任务不存在"),
    ANSWER_SEARCH_WIKI_ERROR(133, "仓库wiki查询失败"),
    ANSWER_WIKI_GENERATING(134, "仓库wiki生成中，稍后重试"),
    ANSWER_REPO_NO_PERMISSION(135, "问答用户没有仓库权限，请先申请仓库权限"),

    ANSWER_PR_REPORT_ERROR(136, "PR报告获取失败"),

    // 自定义接口问答错误码
    ANSWER_CUSTOMIZE_FAIL(130, "自定义接口问答失败"),


    MODEL_CONFIG_ERROR(901, "模型配置错误"),

    MODEL_SCENE_CHAIN_NAME_EMPTY_ERROR(902, "sceneName或者chainName为空"),

    MODEL_SCENE_CHAIN_NAME_INVALID_ERROR(903, "sceneName或者chainName配置错误"),

    MODEL_NO_AVAILABLE_SERVER_ERROR(904, "当前生产环境未部署，请确认模型是否已经部署到生产环境。如果模型部署在其他环境，请切换环境后再重试"),

    NO_MODEL_HEALTH_DEGREE_INFO(905, "没有可用的模型健康度信息， 可尝试使用一次模型来更新健康度信息"),

    YUQUE_TOKEN_UNAVAILABLE(906,"语雀token不可用"),

    IDEA_EVO_OUTPUT_FORMAT_ERROR(201, "输出格式错误，无法解析"),


    SVAT_MODEL_NOT_GEN_PLAN(400, "模型无法生成计划"),
    SVAT_MODEL_NOT_GEN_CODE(401, "模型无法生成代码"),
    SVAT_SEARCH_CODE_TIMEOUT(402, "查询相关相似代码超时"),
    SVAT_SEARCH_CODE_EXCEPTION(403, "查询相关相似代码未知异常"),
    SVAT_SEARCH_CODE_FAIL(404, "查询相关相似代码失败"),
    SVAT_SEARCH_CODE_RESULT_ILL(405, "查询相关相似代码结果格式错误"),
    SVAT_GEN_PLAN_EXCEPTION(406, "计划生成异常"),
    SVAT_GEN_CODE_NOT_FILE_CONTENT(407, "获取目标代码文件内容失败"),
    SVAT_REPO_INFO_ILL(408, "仓库信息错误"),
    SVAT_GEN_TIMEOUT(409, "生成超时"),
    SVAT_GEN_FAIL(410, "生成失败"),
    SVAT_CODE_OVER_MAX_LINES(411, "代码文件行超过最大限制（%d tokens）：%d tokens"),
    SVAT_REPO_NOT_INDEX(412, "代码仓库没有构建索引"),
    SVAT_REQ_ARG_INVALID(413, "入参无效"),
    SVAT_INDEX_BUILDING(414, "仓库索引构建中，稍后重试"),
    SVAT_QUERY_FUZZINESS(415, "需求描述不清晰，无法生成研发步骤"),
    SVAT_REPO_INDEX_FAIL(416, "仓库构建索引失败"),
    SVAT_REPO_INDEX_QUERY_FAIL(417, "索引状态查询失败"),

    /**
     * 对象传入的JSON格式错误
     */
    HTTP_MESSAGE_ERROR(998, "参数类型不匹配，请检查参数或JSON格式"),
    /**
     * 统一错误码
     */
    ERROR_THROW(999, "未分类错误（重试或许有帮助，如果不成功请联系管理员排查解决）");


    private int errorCode;
    private String errorMsg;

    ResponseEnum(int errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    /**
     * 通过code获取ResponseEnum
     * @param code
     * @return
     */
    public static ResponseEnum getByCode(int code) {
        for(ResponseEnum e: ResponseEnum.values()) {
            if(e.errorCode == code) {
                return e;
            }
        }

        return ERROR_THROW;
    }
    /**
     * 通过name获取ResponseEnum
     * <AUTHOR>
     * @since 2023.12.27
     * @param name name
     * @return com.alipay.codegencore.model.enums.ResponseEnum
     */
    public static ResponseEnum getByName(String name) {
        for(ResponseEnum e: ResponseEnum.values()) {
            if(e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }

        return ERROR_THROW;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}

