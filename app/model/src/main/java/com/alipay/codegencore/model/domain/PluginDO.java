package com.alipay.codegencore.model.domain;

import java.util.Date;

public class PluginDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.gmt_create
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.gmt_modified
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.name
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.description
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.workflow_config
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private String workflowConfig;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.enable
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Integer enable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.deleted
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Integer deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.usage_count
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Long usageCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.owner_user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Long ownerUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.icon_url
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private String iconUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.icon_background_color
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private String iconBackgroundColor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.use_instructions
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private String useInstructions;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.type
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.aci_info
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private String aciInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_plugin.visable_user
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Integer visableUser;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.id
     *
     * @return the value of cg_plugin.id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.id
     *
     * @param id the value for cg_plugin.id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.gmt_create
     *
     * @return the value of cg_plugin.gmt_create
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.gmt_create
     *
     * @param gmtCreate the value for cg_plugin.gmt_create
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.gmt_modified
     *
     * @return the value of cg_plugin.gmt_modified
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.gmt_modified
     *
     * @param gmtModified the value for cg_plugin.gmt_modified
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.name
     *
     * @return the value of cg_plugin.name
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.name
     *
     * @param name the value for cg_plugin.name
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.description
     *
     * @return the value of cg_plugin.description
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.description
     *
     * @param description the value for cg_plugin.description
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.workflow_config
     *
     * @return the value of cg_plugin.workflow_config
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getWorkflowConfig() {
        return workflowConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.workflow_config
     *
     * @param workflowConfig the value for cg_plugin.workflow_config
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setWorkflowConfig(String workflowConfig) {
        this.workflowConfig = workflowConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.enable
     *
     * @return the value of cg_plugin.enable
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Integer getEnable() {
        return enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.enable
     *
     * @param enable the value for cg_plugin.enable
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.deleted
     *
     * @return the value of cg_plugin.deleted
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Integer getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.deleted
     *
     * @param deleted the value for cg_plugin.deleted
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.user_id
     *
     * @return the value of cg_plugin.user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.user_id
     *
     * @param userId the value for cg_plugin.user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.usage_count
     *
     * @return the value of cg_plugin.usage_count
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Long getUsageCount() {
        return usageCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.usage_count
     *
     * @param usageCount the value for cg_plugin.usage_count
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.owner_user_id
     *
     * @return the value of cg_plugin.owner_user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Long getOwnerUserId() {
        return ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.owner_user_id
     *
     * @param ownerUserId the value for cg_plugin.owner_user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.icon_url
     *
     * @return the value of cg_plugin.icon_url
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getIconUrl() {
        return iconUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.icon_url
     *
     * @param iconUrl the value for cg_plugin.icon_url
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.icon_background_color
     *
     * @return the value of cg_plugin.icon_background_color
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getIconBackgroundColor() {
        return iconBackgroundColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.icon_background_color
     *
     * @param iconBackgroundColor the value for cg_plugin.icon_background_color
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setIconBackgroundColor(String iconBackgroundColor) {
        this.iconBackgroundColor = iconBackgroundColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.use_instructions
     *
     * @return the value of cg_plugin.use_instructions
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getUseInstructions() {
        return useInstructions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.use_instructions
     *
     * @param useInstructions the value for cg_plugin.use_instructions
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setUseInstructions(String useInstructions) {
        this.useInstructions = useInstructions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.type
     *
     * @return the value of cg_plugin.type
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.type
     *
     * @param type the value for cg_plugin.type
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.aci_info
     *
     * @return the value of cg_plugin.aci_info
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getAciInfo() {
        return aciInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.aci_info
     *
     * @param aciInfo the value for cg_plugin.aci_info
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setAciInfo(String aciInfo) {
        this.aciInfo = aciInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_plugin.visable_user
     *
     * @return the value of cg_plugin.visable_user
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Integer getVisableUser() {
        return visableUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_plugin.visable_user
     *
     * @param visableUser the value for cg_plugin.visable_user
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setVisableUser(Integer visableUser) {
        this.visableUser = visableUser;
    }
}