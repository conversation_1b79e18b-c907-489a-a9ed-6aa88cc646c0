package com.alipay.codegencore.model.model;

/**
 * 模型可用性信息简化版
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.05.24
 */
public class ModelAvailableSimply {
    /**
     * 模型名称
     */
    private String model;
    /**
     * 预发环境是否可用
     */
    private Boolean pre;
    /**
     * 生产环境是否可用
     */
    private Boolean prod;

    public ModelAvailableSimply(String model) {
        this.model = model;
    }

    public ModelAvailableSimply() {
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Boolean getPre() {
        return pre;
    }

    public void setPre(Boolean pre) {
        this.pre = pre;
    }

    public Boolean getProd() {
        return prod;
    }

    public void setProd(Boolean prod) {
        this.prod = prod;
    }
}
