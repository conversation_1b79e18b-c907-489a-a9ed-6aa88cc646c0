package com.alipay.codegencore.model.model;

/**
 * 补全配置,仅支持行补全
 * 待大模型准备妥当后会切换至{@link CodeGptConfigModel}
 * <AUTHOR>
 * 创建时间 2023-02-03
 */
@Deprecated
public class CompletionConfigModel {
    /**
     * 补全输入的最大行数
     * 超过此值，输入会被截断
     */
    private int promptLineMaxNum = 50;

    /**
     * 远程超时时间(单位：ms）
     */
    private long remoteTimeOut = 400L;
    /**
     * 定时任务间隔时间（单位：分钟）
     */
    private int intervalTime = 30;

    /**
     * 代码补全展示名默认长度
     * (对应在线模式的 {@link CodeCompletionFacadeImpl#CODE_COMPLETION_DISPLAY_LENGTH)}
     */
    private int completionDisplayLength = 60;

    /**
     * 补全数量
     * (在线模式暂时没有对应参数)
     */
    private int completionNum = 3;

    /**
     * 补全token长度
     * (对应在线模式的 {@link CodeCompletionFacadeImpl#LINE_TOKEN_LENGTH))
     */
    private int completionTokenLength = 32;
    /**
     * 过滤补全请求的正则表达式
     */
    private String completionRegular = "[a-zA-Z0-9\\_\\.\\>\\<\\+\\-\\@\\=\\(\\{\\[\\*\\/ ]";

    /**
     * 远程gpt模型配置
     */
    private AntGptConfigModel antGptConfigModel = new AntGptConfigModel();
    /**
     * 本地gpt模型配置
     */
    private LocalGptConfigModel localGptConfigModel = new LocalGptConfigModel();

    public AntGptConfigModel getAntGptConfigModel() {
        return antGptConfigModel;
    }

    public void setAntGptConfigModel(AntGptConfigModel antGptConfigModel) {
        this.antGptConfigModel = antGptConfigModel;
    }

    public LocalGptConfigModel getLocalGptConfigModel() {
        return localGptConfigModel;
    }

    public void setLocalGptConfigModel(LocalGptConfigModel localGptConfigModel) {
        this.localGptConfigModel = localGptConfigModel;
    }

    public String getCompletionRegular() {
        return completionRegular;
    }

    public void setCompletionRegular(String completionRegular) {
        this.completionRegular = completionRegular;
    }

    public int getPromptLineMaxNum() {
        return promptLineMaxNum;
    }

    public void setPromptLineMaxNum(int promptLineMaxNum) {
        this.promptLineMaxNum = promptLineMaxNum;
    }

    public long getRemoteTimeOut() {
        return remoteTimeOut;
    }

    public void setRemoteTimeOut(long remoteTimeOut) {
        this.remoteTimeOut = remoteTimeOut;
    }

    public int getIntervalTime() {
        return intervalTime;
    }

    public void setIntervalTime(int intervalTime) {
        this.intervalTime = intervalTime;
    }

    public int getCompletionDisplayLength() {
        return completionDisplayLength;
    }

    public void setCompletionDisplayLength(int completionDisplayLength) {
        this.completionDisplayLength = completionDisplayLength;
    }

    public int getCompletionNum() {
        return completionNum;
    }

    public void setCompletionNum(int completionNum) {
        this.completionNum = completionNum;
    }

    public int getCompletionTokenLength() {
        return completionTokenLength;
    }

    public void setCompletionTokenLength(int completionTokenLength) {
        this.completionTokenLength = completionTokenLength;
    }
}
