package com.alipay.codegencore.model.model.codegpt;

import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLogGroup;
import com.alipay.codegencore.model.openai.ChatMessage;

/**
 * 流式请求完成的结果model
 */
public class StreamResponseModel {

    /**
     * AI回复的的uid
     */
    private String answerUid;
    /**
     * AI的回复内容
     */
    private ChatMessage answerMessage;

    /**
     * 是否命中缓存
     */
    private Boolean cached;
    /**
     * 命中缓存中query详情
     */
    private String hitQuery;
    /**
     * 审核结果
     */
    private CheckResultModel checkResultModel;
    /**
     * Ai回复异常状态
     */
    private String serviceAbnormalResp;
    private PluginLogGroup pluginLogGroup;

    private String messageStatus;

    private String runtimeInfo;
    /**
     * 回复内容是否需要置空
     */
    private Boolean clear;
    /**
     * 异常信息
     */
    private String errorMsg;

    public String getServiceAbnormalResp() {
        return serviceAbnormalResp;
    }

    public void setServiceAbnormalResp(String serviceAbnormalResp) {
        this.serviceAbnormalResp = serviceAbnormalResp;
    }

    public String getAnswerUid() {
        return answerUid;
    }

    public void setAnswerUid(String answerUid) {
        this.answerUid = answerUid;
    }

    public ChatMessage getAnswerMessage() {
        return answerMessage;
    }

    public void setAnswerMessage(ChatMessage answerMessage) {
        this.answerMessage = answerMessage;
    }

    public Boolean getCached() {
        return cached;
    }

    public void setCached(Boolean cached) {
        this.cached = cached;
    }

    public String getHitQuery() {
        return hitQuery;
    }

    public void setHitQuery(String hitQuery) {
        this.hitQuery = hitQuery;
    }

    public CheckResultModel getCheckResultModel() {
        return checkResultModel;
    }

    public void setCheckResultModel(CheckResultModel checkResultModel) {
        this.checkResultModel = checkResultModel;
    }

    public PluginLogGroup getPluginLogGroup() {
        return pluginLogGroup;
    }

    public void setPluginLogGroup(PluginLogGroup pluginLogGroup) {
        this.pluginLogGroup = pluginLogGroup;
    }

    public String getMessageStatus() {
        return messageStatus;
    }

    public void setMessageStatus(String messageStatus) {
        this.messageStatus = messageStatus;
    }

    public String getRuntimeInfo() {
        return runtimeInfo;
    }

    public void setRuntimeInfo(String runtimeInfo) {
        this.runtimeInfo = runtimeInfo;
    }

    public Boolean getClear() {
        return clear;
    }

    public void setClear(Boolean clear) {
        this.clear = clear;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
