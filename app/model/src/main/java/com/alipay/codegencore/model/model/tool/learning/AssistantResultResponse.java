package com.alipay.codegencore.model.model.tool.learning;

import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLog;
import com.alipay.codegencore.model.openai.ChatFunctionCall;

import java.util.List;

/**
 * 助手调用结果
 */
public class AssistantResultResponse {
    private String answer;
    private ChatFunctionCall functionCall;
    private List<PluginLog> pluginLogList;

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public ChatFunctionCall getFunctionCall() {
        return functionCall;
    }

    public void setFunctionCall(ChatFunctionCall functionCall) {
        this.functionCall = functionCall;
    }

    public List<PluginLog> getPluginLogList() {
        return pluginLogList;
    }

    public void setPluginLogList(List<PluginLog> pluginLogList) {
        this.pluginLogList = pluginLogList;
    }
}
