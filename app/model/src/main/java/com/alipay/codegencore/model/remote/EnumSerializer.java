package com.alipay.codegencore.model.remote;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/14 18:07
 */
public class EnumSerializer implements ObjectSerializer {

    @Override
    public void write(JSONSerializer jsonSerializer, Object o, Object o1, Type type, int i) throws IOException {
        SerializeWriter serializeWriter = jsonSerializer.getWriter();
        if (o == null) {
            serializeWriter.writeNull();
            return;
        }
        serializeWriter.write("\"" + StringUtils.lowerCase(o.toString()) + "\"");
    }

}
