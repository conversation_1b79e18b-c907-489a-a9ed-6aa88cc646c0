package com.alipay.codegencore.model.domain;

import java.util.Date;

public class ChatMessageDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.id
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.gmt_create
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.gmt_modified
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.session_uid
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String sessionUid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.user_id
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.role
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String role;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.content
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String content;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.query_index
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Long queryIndex;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.generation_index
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Long generationIndex;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.vote
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Long vote;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.comment
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String comment;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.uid
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String uid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.deleted
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Byte deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.review_result
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String reviewResult;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.hit_cache
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Byte hitCache;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.hit_query
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String hitQuery;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.service_abnormal_resp
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String serviceAbnormalResp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.languages
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String languages;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.plugin_log
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String pluginLog;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.runtime_info
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String runtimeInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.version
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String version;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.status
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.clear
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private Boolean clear;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.error_msg
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String errorMsg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_message.parent
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    private String parent;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.id
     *
     * @return the value of cg_chat_message.id
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.id
     *
     * @param id the value for cg_chat_message.id
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.gmt_create
     *
     * @return the value of cg_chat_message.gmt_create
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.gmt_create
     *
     * @param gmtCreate the value for cg_chat_message.gmt_create
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.gmt_modified
     *
     * @return the value of cg_chat_message.gmt_modified
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.gmt_modified
     *
     * @param gmtModified the value for cg_chat_message.gmt_modified
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.session_uid
     *
     * @return the value of cg_chat_message.session_uid
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getSessionUid() {
        return sessionUid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.session_uid
     *
     * @param sessionUid the value for cg_chat_message.session_uid
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setSessionUid(String sessionUid) {
        this.sessionUid = sessionUid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.user_id
     *
     * @return the value of cg_chat_message.user_id
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.user_id
     *
     * @param userId the value for cg_chat_message.user_id
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.role
     *
     * @return the value of cg_chat_message.role
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getRole() {
        return role;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.role
     *
     * @param role the value for cg_chat_message.role
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setRole(String role) {
        this.role = role;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.content
     *
     * @return the value of cg_chat_message.content
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.content
     *
     * @param content the value for cg_chat_message.content
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.query_index
     *
     * @return the value of cg_chat_message.query_index
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Long getQueryIndex() {
        return queryIndex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.query_index
     *
     * @param queryIndex the value for cg_chat_message.query_index
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setQueryIndex(Long queryIndex) {
        this.queryIndex = queryIndex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.generation_index
     *
     * @return the value of cg_chat_message.generation_index
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Long getGenerationIndex() {
        return generationIndex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.generation_index
     *
     * @param generationIndex the value for cg_chat_message.generation_index
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setGenerationIndex(Long generationIndex) {
        this.generationIndex = generationIndex;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.vote
     *
     * @return the value of cg_chat_message.vote
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Long getVote() {
        return vote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.vote
     *
     * @param vote the value for cg_chat_message.vote
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setVote(Long vote) {
        this.vote = vote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.comment
     *
     * @return the value of cg_chat_message.comment
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getComment() {
        return comment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.comment
     *
     * @param comment the value for cg_chat_message.comment
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setComment(String comment) {
        this.comment = comment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.uid
     *
     * @return the value of cg_chat_message.uid
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getUid() {
        return uid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.uid
     *
     * @param uid the value for cg_chat_message.uid
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setUid(String uid) {
        this.uid = uid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.deleted
     *
     * @return the value of cg_chat_message.deleted
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Byte getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.deleted
     *
     * @param deleted the value for cg_chat_message.deleted
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.review_result
     *
     * @return the value of cg_chat_message.review_result
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getReviewResult() {
        return reviewResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.review_result
     *
     * @param reviewResult the value for cg_chat_message.review_result
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setReviewResult(String reviewResult) {
        this.reviewResult = reviewResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.hit_cache
     *
     * @return the value of cg_chat_message.hit_cache
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Byte getHitCache() {
        return hitCache;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.hit_cache
     *
     * @param hitCache the value for cg_chat_message.hit_cache
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setHitCache(Byte hitCache) {
        this.hitCache = hitCache;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.hit_query
     *
     * @return the value of cg_chat_message.hit_query
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getHitQuery() {
        return hitQuery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.hit_query
     *
     * @param hitQuery the value for cg_chat_message.hit_query
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setHitQuery(String hitQuery) {
        this.hitQuery = hitQuery;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.service_abnormal_resp
     *
     * @return the value of cg_chat_message.service_abnormal_resp
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getServiceAbnormalResp() {
        return serviceAbnormalResp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.service_abnormal_resp
     *
     * @param serviceAbnormalResp the value for cg_chat_message.service_abnormal_resp
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setServiceAbnormalResp(String serviceAbnormalResp) {
        this.serviceAbnormalResp = serviceAbnormalResp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.languages
     *
     * @return the value of cg_chat_message.languages
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getLanguages() {
        return languages;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.languages
     *
     * @param languages the value for cg_chat_message.languages
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setLanguages(String languages) {
        this.languages = languages;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.plugin_log
     *
     * @return the value of cg_chat_message.plugin_log
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getPluginLog() {
        return pluginLog;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.plugin_log
     *
     * @param pluginLog the value for cg_chat_message.plugin_log
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setPluginLog(String pluginLog) {
        this.pluginLog = pluginLog;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.runtime_info
     *
     * @return the value of cg_chat_message.runtime_info
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getRuntimeInfo() {
        return runtimeInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.runtime_info
     *
     * @param runtimeInfo the value for cg_chat_message.runtime_info
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setRuntimeInfo(String runtimeInfo) {
        this.runtimeInfo = runtimeInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.version
     *
     * @return the value of cg_chat_message.version
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.version
     *
     * @param version the value for cg_chat_message.version
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.status
     *
     * @return the value of cg_chat_message.status
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.status
     *
     * @param status the value for cg_chat_message.status
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.clear
     *
     * @return the value of cg_chat_message.clear
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Boolean getClear() {
        return clear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.clear
     *
     * @param clear the value for cg_chat_message.clear
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setClear(Boolean clear) {
        this.clear = clear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.error_msg
     *
     * @return the value of cg_chat_message.error_msg
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.error_msg
     *
     * @param errorMsg the value for cg_chat_message.error_msg
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_message.parent
     *
     * @return the value of cg_chat_message.parent
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getParent() {
        return parent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_message.parent
     *
     * @param parent the value for cg_chat_message.parent
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setParent(String parent) {
        this.parent = parent;
    }
}