package com.alipay.codegencore.model.model;

import com.alipay.codegencore.model.enums.ContentCheckSceneCodeEnum;

/**
 * infosec异步更新数据库model
 */
public class InfoSecAsyncUpdateDbModel {
    private String messageUid;
    private ContentCheckSceneCodeEnum contentCheckSceneCode;
    private ReviewResultModel reviewResultModel;
    private Long dateTimeMillis;

    public InfoSecAsyncUpdateDbModel() {
    }

    /**
     * 包含全部参数的构造器
     * @param messageUid
     * @param contentCheckSceneCode
     * @param reviewResultModel
     * @param dateTimeMillis
     */
    public InfoSecAsyncUpdateDbModel(String messageUid, ContentCheckSceneCodeEnum contentCheckSceneCode, ReviewResultModel reviewResultModel, Long dateTimeMillis) {
        this.messageUid = messageUid;
        this.contentCheckSceneCode = contentCheckSceneCode;
        this.reviewResultModel = reviewResultModel;
        this.dateTimeMillis = dateTimeMillis;
    }

    public String getMessageUid() {
        return messageUid;
    }

    public void setMessageUid(String messageUid) {
        this.messageUid = messageUid;
    }

    public ContentCheckSceneCodeEnum getContentCheckSceneCode() {
        return contentCheckSceneCode;
    }

    public void setContentCheckSceneCode(ContentCheckSceneCodeEnum contentCheckSceneCode) {
        this.contentCheckSceneCode = contentCheckSceneCode;
    }

    public ReviewResultModel getReviewResultModel() {
        return reviewResultModel;
    }

    public void setReviewResultModel(ReviewResultModel reviewResultModel) {
        this.reviewResultModel = reviewResultModel;
    }

    public Long getDateTimeMillis() {
        return dateTimeMillis;
    }

    public void setDateTimeMillis(Long dateTimeMillis) {
        this.dateTimeMillis = dateTimeMillis;
    }
}
