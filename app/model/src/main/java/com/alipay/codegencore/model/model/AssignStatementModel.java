package com.alipay.codegencore.model.model;

import java.io.Serializable;

/**
 * 赋值语句规则
 *
 * <AUTHOR>
 * 创建时间 2022-05-18
 */
public class AssignStatementModel implements Serializable {
    /**
     * 防止字段变化导致反序列化失败
     */
    private static final long serialVersionUID = 1L;
    /**
     * 赋值目标对象class名
     * eg: UserInfo  代表一定要赋值给UserInfo类对象
     */
    private String targetClassName;
    /**
     * 赋值表达式信息
     * eg:UserInfoQueryService.findUserInfo  代表执行的是UserInfoQueryService对象的findUserInfo方法
     */
    private String expressInfo;
    /**
     * content保留行 起始line
     */
    private int startLine;


    public int getStartLine() {
        return startLine;
    }

    public void setStartLine(int startLine) {
        this.startLine = startLine;
    }


    public String getTargetClassName() {
        return targetClassName;
    }

    public void setTargetClassName(String targetClassName) {
        this.targetClassName = targetClassName;
    }

    public String getExpressInfo() {
        return expressInfo;
    }

    public void setExpressInfo(String expressInfo) {
        this.expressInfo = expressInfo;
    }
}
