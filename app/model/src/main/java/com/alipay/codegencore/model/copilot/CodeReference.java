package com.alipay.codegencore.model.copilot;

import com.alipay.codegencore.model.enums.CodeReferenceTypeEnum;

/**
 * CodeReference类表示代码参考，包括URL、名称和类型
 */
public class CodeReference {
    private String url;
    /**
     * 名称
     */
    private String name;
    /**
     * 类型枚举
     */
    private CodeReferenceTypeEnum type;

    /**
     * 获取URL地址
     *
     * @return URL地址
     */
    public String getUrl() {
        return url;
    }

    /**
     * 设置URL地址
     *
     * @param url URL地址
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 获取名称
     *
     * @return 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取类型
     *
     * @return 类型枚举
     */
    public CodeReferenceTypeEnum getType() {
        return type;
    }

    /**
     * 设置类型
     *
     * @param type 类型枚举
     */
    public void setType(CodeReferenceTypeEnum type) {
        this.type = type;
    }
}