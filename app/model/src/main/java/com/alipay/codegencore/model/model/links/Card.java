/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: Card.java, v 0.1 2022-07-07 9:54 wb-tzg858080 Exp $$
 */
public class Card extends ToString {

    private List<CardItem> items;

    private List<LinkItem> buttons ;

    public List<CardItem> getItems() {
        return items;
    }

    public void setItems(List<CardItem> items) {
        this.items = items;
    }

    public List<LinkItem> getButtons() {
        return buttons;
    }

    public void setButtons(List<LinkItem> buttons) {
        this.buttons = buttons;
    }
}
