package com.alipay.codegencore.model.util;


/**
 * Document 表 ExtInfo 的key
 */
public class DocumentExtInfoKey {

    // 解析后的文本存储在OSS上的UID
    public static final String DOCUMENT_CONTENT_OSS_UID = "document_content_oss_uid";
    // 解析后的文本拆分embedding之后存储在OSS上的UID
    public static final String DOCUMENT_SEGMENT_OSS_UID = "document_segment_oss_uid";

    // 原始文档的OSS链接(UPLOAD的文件才有)
    public static final String FILE_OSS_URL = "fileOssUrl";
    // 原始文档后缀(.pdf/.txt等,UPLOAD的文件才有)
    public static final String FILE_TYPE = "fileType";

    // URL导入的才有
    public static final String ORIGIN_URL = "originUrl";

    // 语雀的团队token
    public static final String YUQUE_TOKEN = "yuqueToken";
    // 团队名称
    public static final String TEAM_NAME = "teamName";
    // 团队ID
    public static final String TEAM_ID = "teamId";
    // 知识库ID
    public static final String BOOK_ID = "bookId";
    // 文档ID列表
    public static final String DOC_ID_LIST = "docIdList";
    // 最后更新时间
    public static final String LAST_UPDATE_TIME = "lastUpdateTime";
    //上传的源文件名
    public static final String OSS_SOURCE_FILE_NAME = "ossSourceFileName";
    // 文档slug列表
    public static final String DOC_SLUG_LIST = "docSlugList";

}
