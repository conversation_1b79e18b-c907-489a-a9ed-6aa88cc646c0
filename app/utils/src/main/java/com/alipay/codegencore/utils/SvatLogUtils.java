package com.alipay.codegencore.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * <AUTHOR>
 * @version 1.0 2024/10/17 14:30
 */
public class SvatLogUtils {

    /**
     * 日志名称
     */
    private static final String LOG_NAME = "SVAT-DIGEST";

    /**
     * 格式化日志
     *
     * 业务类型
     * 结果
     * 耗时
     */
    private static final String LOG_FORMAT = "{},{},{}";

    private static final Logger logger = LoggerFactory.getLogger(LOG_NAME);

    /**
     * 记录日志
     * @param bizType 业务类型
     * @param resultType 结果类型
     * @param cost 耗时
     */
    public static void log(BizType bizType, ResultType resultType, long cost) {
        if (bizType == null) {
            bizType = BizType.UNKNOWN;
        }
        if (resultType == null) {
            resultType = ResultType.SUCCESS;
        }
        logger.info(LOG_FORMAT, bizType.name(), resultType.name(), cost);
    }

    /**
     * 记录日志
     * @param bizType 业务类型
     * @param result 结果
     * @param cost 耗时
     */
    public static void log(BizType bizType, boolean result, long cost) {
        log(bizType, result ? ResultType.SUCCESS : ResultType.FAIL, cost);
    }

    /**
     * 业务类型
     */
    public enum BizType {
        SEARCH, PLAN_GEN, CODE_GEN, MODEL, UNKNOWN
    }

    /**
     * 结果类型
     */
    public enum ResultType {
        SUCCESS, FAIL
    }


}
