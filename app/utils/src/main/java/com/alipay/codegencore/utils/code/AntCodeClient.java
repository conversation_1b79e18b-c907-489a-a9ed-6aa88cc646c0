package com.alipay.codegencore.utils.code;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.copilot.CodeSymbol;
import com.alipay.codegencore.utils.http.GetBuilder;
import com.alipay.codegencore.utils.http.HttpClient;
import com.google.api.client.util.Lists;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ant code 操作客户端
 */
public class AntCodeClient {

    private static final Logger logger = LoggerFactory.getLogger(AntCodeClient.class);

    /**
     * ant code host
     */
    public static final String ANT_CODE_HOST = "ANT_CODE_HOST";

    /**
     * ant code token
     */
    public static final String ANT_CODE_TOKEN = "ANT_CODE_TOKEN";

    /**
     * 获取仓库下的 Commits 记录
     * <a href="https://yuque.antfin-inc.com/antcode/help/fh49do">...</a>
     */
    private static final String GET_COMMITS_BY_PROJECT = "/api/v3/projects/%s/repository/commits";

    /**
     * 获取仓库下文件列表
     * <a href="https://yuque.antfin-inc.com/antcode/help/sv2iec">...</a>
     */
    private static final String GET_FILE_LIST_BY_PROJECT = "/api/v3/projects/%s/repository/files_list";

    /**
     * 获取两 Commit 之间变更文件概况
     */
    private static final String GET_FILE_COMPARE_BETWEEN_TWO_COMMIT = "/api/v3/projects/%s/repository/compare/name_status";

    /**
     * 获取文件内容
     */
    private static final String GET_FILE_CONTENT = "/api/v3/projects/%s/repository/files";

    /**
     * 文件搜索
     */
    private static final String FILE_SEARCH = "/api/v3/projects/%s/repository/files_search";


    /**
     * symbol搜索，注意是webapi，需要cookie
     */
    private static final String SYMBOL_SEARCH = "/webapi/projects/%s/repository/symbols/%s";

    /**
     * 搜索代码group
     */
    private static final String LIST_GROUP = "/api/v3/groups";
    /**
     * 搜索仓库
     */
    private static final String LIST_PROJECT = "/api/v3/projects";

    /**
     * 获取仓库分支列表
     */
    private static final String LIST_BRANCH = "/api/v3/projects/%s/repository/branches";

    /**
     * 获取人员对于仓库的权限等级
     */
    private static final String MEMBER_REPO_ACCESS_LEVEL = "/api/v3/projects/%s/members/access_level";

    /**
     * 请求token
     */
    private static final String HEADER_PRIVATE_TOKEN = "PRIVATE-TOKEN";

    /**
     * 默认的仓库分支
     */
    public static final String DEFAULT_BRANCH = "master";

    /**
     * ant code project id 默认分割符
     */
    private static final String PROJECT_ID_SPLIT = "%2F";

    /**
     * ant code 仓库 git 协议前缀
     * eg： *******************:common_release/amtaigcgateway.git
     */
    private static final String REPO_URL_GIT_PREFIX = "git@";

    /**
     * ant code 仓库 git 协议前缀
     * eg： <a href="https://code.alipay.com/common_release/amtaigcgateway.git">...</a>
     */
    private static final String REPO_URL_HTTPS_PREFIX = "https://";

    /**
     * ant code 仓库 git 协议前缀
     * eg： <a href="https://code.alipay.com/common_release/amtaigcgateway.git">...</a>
     */
    private static final String REPO_URL_HTTP_PREFIX = "http://";

    /**
     * 仓库地址后缀
     */
    private static final String REPO_URL_SUFIX = ".git";

    /**
     * 默认的文件变更类型：A：新增；
     */
    private static final String DEFAULT_COMPARE_STATUS = "A";

    /**
     * 正则表达式匹配带有token的URL
     */
    private static final String TOKEN_REGEX = "(https?://)[^@]+@";

    /**
     * ant code 域名
     */
    private static String host;

    /**
     * ant code token
     */
    private static String token;

    /**
     * ant code client init
     * @param host
     * @param token
     */
    public static void init(String host, String token) {
        AntCodeClient.host = host;
        AntCodeClient.token = token;
    }


    /**
     * 获取 repoRef
     * @param repoURL
     * @return
     */
    public static String getRepoRef(String repoURL) {
        if (StringUtils.isBlank(repoURL)) {
            return null;
        }
        String repoURLNoSuffix = StringUtils.substringBefore(repoURL, REPO_URL_SUFIX);
        if (StringUtils.startsWith(repoURLNoSuffix, REPO_URL_GIT_PREFIX)) {
            return StringUtils.replace(repoURLNoSuffix, REPO_URL_GIT_PREFIX, "");
        } else if (StringUtils.startsWith(repoURLNoSuffix, REPO_URL_HTTP_PREFIX)) {
            return StringUtils.replace(repoURLNoSuffix, REPO_URL_HTTP_PREFIX, "");
        } else if (StringUtils.startsWith(repoURLNoSuffix, REPO_URL_HTTPS_PREFIX)) {
            return StringUtils.replace(repoURLNoSuffix, REPO_URL_HTTPS_PREFIX, "");
        } else {
            logger.warn("antCode protocol illegal repo url:{}", repoURL);
            return null;
        }
    }

    /**
     * 是否为 master 分支
     * @param branch
     * @return
     */
    public static boolean isMaster(String branch) {
        return DEFAULT_BRANCH.equalsIgnoreCase(branch);
    }


    /**
     * 用户是否允许访问仓库
     * @param workNo
     * @param groupPath
     * @param projectPath
     * @return
     */
    public static boolean memberAllowAccess(String workNo, String groupPath, String projectPath) {

        String level = memberAccessLevel(workNo, groupPath, projectPath);

        return AccessLevel.AllowAccess(NumberUtils.toInt(level));
    }

    /**
     * 获取用户对于仓库的权限等级
     * @param workNo
     * @param groupPath
     * @param projectPath
     * @return
     */
    public static String memberAccessLevel(String workNo, String groupPath, String projectPath) {

        if (StringUtils.isBlank(workNo)
                || StringUtils.isBlank(groupPath)
                || StringUtils.isBlank(projectPath)) {
            return "0";
        }

        try {
            String response = sendRequest(MEMBER_REPO_ACCESS_LEVEL, groupPath, projectPath,
                    getBuilder -> getBuilder.addParameter("extern_uid", workNo));

            if (StringUtils.isBlank(response)) {
                return "0";
            }

            JSONObject responseObject = JSON.parseObject(response);
            if (responseObject.getBoolean("status")) {
                return responseObject.getString("data");
            }
        } catch (Exception e) {
            logger.error("get member:{} access:{}/{} level failed", workNo, groupPath, projectPath, e);
        }
        return "0";
    }

    /**
     * 检查分支是否存在可用
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    public static boolean branchCheck(String groupPath, String projectPath, String branch) {
        String apiPath = LIST_BRANCH + "/" + branch;
        try {
            String response = sendRequest(apiPath, groupPath, projectPath, null);
            JSONObject resObj = JSON.parseObject(response);
            return Optional.ofNullable(resObj).map(item -> item.containsKey("name")).orElse(false);
        } catch (Exception e) {
            logger.error("仓库:{}/{} 分支:{} 检查失败", groupPath, projectPath, branch, e);
        }
        return false;
    }

    /**
     * 检查 commit 是否存在
     * @param groupPath
     * @param projectPath
     * @param commitId
     * @return
     */
    public static boolean commitCheck(String groupPath, String projectPath, String commitId) {
        String apiPath = GET_COMMITS_BY_PROJECT + "/" + commitId;
        try {
            String response = sendRequest(apiPath, groupPath, projectPath, null);
            JSONObject resObj = JSON.parseObject(response);
            return Optional.ofNullable(resObj).map(item -> item.containsKey("tree_hash")).orElse(false);
        } catch (Exception e) {
            logger.error("仓库:{}/{} commit:{} 检查失败", groupPath, projectPath, commitId, e);
        }
        return false;
    }

    /**
     * 获取指定仓库的最后的一条提交信息，如果返回null，
     * 则证明仓库不存在或者不可用，不需要继续进行索引构建
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    public static CommitInfo getProjectLastCommitInfo(String groupPath, String projectPath, String branch) {
        if (StringUtils.isBlank(groupPath) || StringUtils.isBlank(projectPath)) {
            return null;
        }

        final String useBranch = StringUtils.defaultIfBlank(branch, DEFAULT_BRANCH);

        String response = sendRequest(GET_COMMITS_BY_PROJECT, groupPath, projectPath,
                getBuilder -> {
                    getBuilder.addParameter("ref_name", useBranch)
                            .addParameter("page", "1")
                            .addParameter("per_page", "1");
                });
        if (StringUtils.isBlank(response)) {
            return null;
        }

        //看仓库是否不存在，如果没有这个 commit_name 就认为仓库不可用
        if (!response.contains("committer_name")) {
            return null;
        }

        JSONArray commitList = JSON.parseArray(response);
        if (CollectionUtils.isEmpty(commitList)) {
            return null;
        }

        JSONObject commitObject = commitList.getJSONObject(0);

        CommitInfo commitInfo = new CommitInfo();
        commitInfo.setCommitId(commitObject.getString("id"));
        commitInfo.setCommitDate(commitObject.getString("committed_date"));
        commitInfo.setGroupPath(groupPath);
        commitInfo.setProjectPath(projectPath);
        commitInfo.setBranch(useBranch);
        logger.info("antCode get last commit info:{}", commitInfo);
        return commitInfo;
    }


    public static String defaultIfBlank(String branch) {
        return StringUtils.defaultIfBlank(branch, DEFAULT_BRANCH);
    }

    /**
     * 获取指定仓库的文件列表
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    public static List<FileCompare> getFileListByProject(String groupPath, String projectPath, String branch) {
        logger.info("antCode get file list by project:{};{};{}", groupPath, projectPath, branch);

        if (StringUtils.isBlank(groupPath) || StringUtils.isBlank(projectPath)) {
            return List.of();
        }

        final String useBranch = StringUtils.defaultIfBlank(branch, DEFAULT_BRANCH);

        String response = sendRequest(GET_FILE_LIST_BY_PROJECT, groupPath, projectPath,
                getBuilder -> getBuilder.addParameter("ref_name", useBranch));
        if (StringUtils.isBlank(response)) {
            return List.of();
        }

        try {
            List<String> fileList = JSON.parseArray(response, String.class);
            if (CollectionUtils.isNotEmpty(fileList)) {
                return fileList.stream().map(filePath -> new FileCompare(filePath, DEFAULT_COMPARE_STATUS)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.warn("antCode response convert json ex", e);
        }

        return List.of();
    }

    public static List<FileCompare> getFileComparesBetweenTwoCommit(String groupPath, String projectPath,
                                                                    String fromCommitId, String toCommitId) {
        logger.info("antCode get file compare between commit:{};{} projectId:{};{}", fromCommitId, toCommitId, groupPath, projectPath);

        if (StringUtils.isBlank(groupPath) || StringUtils.isBlank(projectPath)
                || StringUtils.isBlank(fromCommitId) || StringUtils.isBlank(toCommitId)) {
            return List.of();
        }

        String response = sendRequest(GET_FILE_COMPARE_BETWEEN_TWO_COMMIT, groupPath, projectPath,
                getBuilder -> {
                    getBuilder.addParameter("from", fromCommitId)
                            .addParameter("to", toCommitId);
                });
        if (StringUtils.isBlank(response)) {
            return List.of();
        }

        try {
            JSONObject compare = JSON.parseObject(response);
            if (!compare.containsKey("name_status")) {
                return List.of();
            }

            JSONArray compareList = compare.getJSONArray("name_status");
            if (CollectionUtils.isEmpty(compareList)) {
                return List.of();
            }

            List<FileCompare> fileCompareList = Lists.newArrayList();
            for (int i = 0; i < compareList.size(); i++) {
                JSONObject compareItem = compareList.getJSONObject(i);
                FileCompare fileCompare = new FileCompare();
                fileCompare.setOldPath(compareItem.getString("from_path"));
                fileCompare.setNewPath(compareItem.getString("to_path"));
                fileCompare.setStatus(compareItem.getString("status"));
                fileCompareList.add(fileCompare);
            }
            return fileCompareList;
        } catch (Exception e) {
            logger.warn("antCode get file compare response ill", e);
        }

        return List.of();
    }

    /**
     * 从给定的仓库地址中解析出ant code的groupPath projectPath
     * 如果返回null，则表示仓库地址不合法
     * left : groupPath
     * right : projectPath
     * @param repoURL
     * @return
     */
    public static Pair<String, String> getGroupAndProjectPathByRepoUrl(String repoURL) {
        Triple<String, String, String> triple = getHostAndGroupAndProjectPathByRepoUrl(repoURL);
        if (triple == null) {
            return null;
        }
        return Pair.of(triple.getMiddle(), triple.getRight());
    }

    /**
     * 从仓库地址中提取仓库信息
     * left： host
     * middle： group
     * right： project
     * @param repoURL
     * @return
     */
    public static Triple<String, String, String> getHostAndGroupAndProjectPathByRepoUrl(String repoURL) {

        logger.debug("ant code repo url:{} handle", repoURL);
        if (StringUtils.isBlank(repoURL)) {
            return null;
        }
        String repoURLNoSuffix = StringUtils.substringBefore(repoURL, REPO_URL_SUFIX);
        if (StringUtils.startsWith(repoURLNoSuffix, REPO_URL_GIT_PREFIX)) {
            String repoNoProtocolHead = StringUtils.substring(repoURLNoSuffix, REPO_URL_GIT_PREFIX.length());
            String[] hostAndPath = StringUtils.split(repoNoProtocolHead, ':');
            if (ArrayUtils.isEmpty(hostAndPath) || hostAndPath.length < 2) {
                return null;
            }
            String[] paths = StringUtils.split(hostAndPath[1], '/');
            if (ArrayUtils.isEmpty(paths) || paths.length < 2) {
                return null;
            }
            return Triple.of(hostAndPath[0], paths[0], paths[1]);
        } else if (StringUtils.startsWith(repoURLNoSuffix, REPO_URL_HTTP_PREFIX)) {
            repoURLNoSuffix = removeTokenFromUrl(repoURLNoSuffix);
            return extract(repoURLNoSuffix, REPO_URL_HTTP_PREFIX);
        } else if (StringUtils.startsWith(repoURLNoSuffix, REPO_URL_HTTPS_PREFIX)) {
            repoURLNoSuffix = removeTokenFromUrl(repoURLNoSuffix);
            return extract(repoURLNoSuffix, REPO_URL_HTTPS_PREFIX);
        } else {
            logger.warn("antCode protocol illegal repo url:{}", repoURL);
            return null;
        }
    }

    /**
     * ex
     * @param repoURLNoSuffix
     * @param prefix
     * @return
     */
    private static Triple<String, String, String> extract(String repoURLNoSuffix, String prefix) {
        String repoNoProtocolHead = StringUtils.substring(repoURLNoSuffix, prefix.length());
        String[] hostAndPath = StringUtils.split(repoNoProtocolHead, '/');
        if (ArrayUtils.isEmpty(hostAndPath) || hostAndPath.length < 3) {
            return null;
        }
        return Triple.of(hostAndPath[0], hostAndPath[1], hostAndPath[2]);
    }


    /**
     * 搜索ant code group
     * @param keyword
     * @return
     */
    public static List<RepoGroupInfo> searchAntcodeGroup(String keyword){
        logger.info("antCode search group by keyword:{}", keyword);
        GetBuilder getBuilder = HttpClient.get(host + LIST_GROUP).header(HEADER_PRIVATE_TOKEN, token);
        getBuilder.addParameter("search", keyword);
        getBuilder.addParameter("per_page", "25");
        String response = getBuilder.syncExecute(5000);
        if (StringUtils.isBlank(response)) {
            return List.of();
        }

        try {
            JSONArray groupInfoList = JSON.parseArray(response);
            if (CollectionUtils.isNotEmpty(groupInfoList)) {
                return groupInfoList.stream().map(groupInfo -> {
                    JSONObject groupInfoJson = (JSONObject) groupInfo;
                    return new RepoGroupInfo(groupInfoJson.getString("name"), groupInfoJson.getInteger("id"));
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.warn("antCode response convert json ex", e);
        }
        return List.of();
    }

    /**
     * 搜索ant code 仓库
     * @param keyword 关键字
     * @return
     */
    public static List<String> searchAntcodeRepo(String repoGroup, String keyword, int pageSize) {
        logger.info("antCode search repo by keyword:{}", keyword);

        int repoGroupId = -1;
        List<RepoGroupInfo> repoGroupList = searchAntcodeGroup(repoGroup);
        if (CollectionUtils.isNotEmpty(repoGroupList)) {
            // 搜索repoGroup刚好等于repoGroup的id
            for (RepoGroupInfo repoGroupInfo : repoGroupList) {
                if (repoGroupInfo.getName().equals(repoGroup)) {
                    repoGroupId = repoGroupInfo.getId();
                    break;
                }
            }
        }
        if (repoGroupId == -1) {
            return List.of();
        }

        GetBuilder getBuilder = HttpClient.get(host + LIST_PROJECT).header(HEADER_PRIVATE_TOKEN, token);
        getBuilder.addParameter("search", keyword);
        getBuilder.addParameter("namespace_id", repoGroupId+"");
        getBuilder.addParameter("per_page", pageSize+"");
        String response = getBuilder.syncExecute(5000);

        if (StringUtils.isBlank(response)) {
            return List.of();
        }

        try {
            JSONArray repoInfoList = JSON.parseArray(response);
            if (CollectionUtils.isNotEmpty(repoInfoList)) {
                return repoInfoList.stream().map(repoInfo -> {
                    JSONObject repoInfoJson = (JSONObject) repoInfo;
                    return repoInfoJson.getString("name");
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.warn("antCode response convert json ex", e);
        }

        return List.of();
    }

    /**
     * 获取仓库分支列表
     * @param groupPath
     * @param projectPath
     * @param keyword
     * @return
     */
    public static List<String> getBranchList(String groupPath, String projectPath, String keyword, int limit) {
        logger.info("antCode get branch list by project:{};{} with keyword:{}", groupPath, projectPath, keyword);

        String response = sendRequest(LIST_BRANCH, groupPath, projectPath,null);
        if (StringUtils.isBlank(response)) {
            return List.of();
        }

        try {
            JSONArray branchInfoList = JSON.parseArray(response);
            if (CollectionUtils.isNotEmpty(branchInfoList)) {
                List<String> branchNameList = branchInfoList.stream().map(branchInfo -> {
                    JSONObject branchInfoJson = (JSONObject) branchInfo;
                    return branchInfoJson.getString("name");
                }).collect(Collectors.toList());

                if (StringUtils.isNotBlank(keyword)) {
                    branchNameList = branchNameList.stream().filter(branchName -> branchName.contains(keyword)).collect(Collectors.toList());
                }

                if(branchNameList.size() > limit){
                    branchNameList = branchNameList.subList(0, limit);
                }
                return branchNameList;
            }
        } catch (Exception e) {
            logger.warn("antCode response convert json ex", e);
        }

        return List.of();
    }

    /**
     * 构建ant code的project id 或者 id
     * @param groupPath
     * @param projectPath
     * @return
     */
    public static String buildProjectId(String groupPath, String projectPath) {
        return groupPath + PROJECT_ID_SPLIT + projectPath;
    }

    /**
     * 获取仓库信息
     * @param repoPath
     * @return
     */
    public static Pair<String, String> getRepoInfoByRepoPath(String repoPath) {
        String[] split = StringUtils.split(repoPath, File.separator);
        if (split.length >= 2) {
            return Pair.of(split[0], split[1]);
        }
        return null;
    }

    /**
     * 获取仓库地址
     * @param groupPath
     * @param projectPath
     * @return
     */
    public static String buildRepoURL(String groupPath, String projectPath) {
        return String.format("%s/%s/%s.git", host, groupPath, projectPath);
    }


    /**
     * 获取文件内容
     * @param repoPath
     * @param branch
     * @param filePath
     * @return
     */
    public static String getFileContent(String repoPath, String branch, String filePath) {
        String newRepoPath = StringUtils.replace(repoPath, "/", PROJECT_ID_SPLIT);
        logger.info("get file content repo:{} branch:{} path:{}", newRepoPath, branch, filePath);

        String response = sendRequest(GET_FILE_CONTENT, newRepoPath, getBuilder -> {
            getBuilder.addParameter("ref", branch)
                    .addParameter("file_path", filePath);
        });

        if (StringUtils.isBlank(response)) {
            return null;
        }

        try {
            JSONObject fileContentObject = JSON.parseObject(response);
            String fileContentBase64 = fileContentObject.getString("content");
            logger.info("get file content size: {}", StringUtils.length(fileContentBase64));
            if (StringUtils.isBlank(fileContentBase64)) {
                return null;
            }
            byte[] fileContentBytes = new Base64().decode(fileContentBase64);
            return new String(fileContentBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("ant code get file content exception", e);
        }
        return null;
    }

    /**
     * 文件搜索
     * @param repoPath
     * @param branch
     * @param query
     * @param limit
     * @return
     */
    public static List<String> fileSearch(String repoPath, String branch, String query, int limit){
        String newRepoPath = StringUtils.replace(repoPath, "/", PROJECT_ID_SPLIT);
        logger.info("file search repo:{} branch:{} query:{}", newRepoPath, branch, query);

        String response = sendRequest(FILE_SEARCH, newRepoPath, getBuilder -> {
            getBuilder.addParameter("ref_name", branch)
                    .addParameter("query", query)
                    .addParameter("limit", limit+"");
        });

        if (StringUtils.isBlank(response)) {
            return null;
        }

        try {
            List<String> fileSearchList = JSON.parseArray(response, String.class);
            if (CollectionUtils.isNotEmpty(fileSearchList)) {
                return fileSearchList;
            }
        } catch (Exception e) {
            logger.error("ant code file search exception", e);
        }
        logger.info("ant code file search empty");
        return new ArrayList<>();
    }

    /**
     * symbol搜索
     * @param repoPath
     * @param branch
     * @param query
     * @param cookie
     * @param caseSensitive
     * @param limit
     * @return
     */
    public static List<CodeSymbol> symbolSearch(String cookie, String repoPath, String branch, String query, boolean caseSensitive, int limit){
        String newRepoPath = StringUtils.replace(repoPath, "/", PROJECT_ID_SPLIT);
        logger.info("symbol search repo:{} branch:{} query:{} limit:{}", newRepoPath, branch, query, limit);

        String response = HttpClient.get(host+String.format(SYMBOL_SEARCH, newRepoPath, branch))
                .header("Cookie", cookie)
                .addParameter("query", query)
                .addParameter("first", limit+"")
                .addParameter("is_case_sensitive",caseSensitive+"")
                .syncExecute(5000);
        if (StringUtils.isBlank(response)) {
            return null;
        }

        try {
            List<CodeSymbol> codeSymbols = JSON.parseArray(response, CodeSymbol.class);
            if (CollectionUtils.isNotEmpty(codeSymbols)) {
                return codeSymbols;
            }
        } catch (Exception e) {
            logger.error("ant code symbol search exception", e);
        }
        logger.info("ant code symbol search empty");
        return new ArrayList<>();
    }

    /**
     * 代码内容分割行，保留空行
     * @param codeContent
     * @return
     */
    public static String[] splitCodeLine(String codeContent) {
        return StringUtils.splitPreserveAllTokens(codeContent, "\n");
    }

    /**
     * 发起调用
     * @param api
     * @param groupPath
     * @param projectPath
     * @param paramHandle
     * @return
     */
    private static String sendRequest(String api, String groupPath, String projectPath,
                                      Consumer<GetBuilder> paramHandle) {
        return sendRequest(api, buildProjectId(groupPath, projectPath), paramHandle);
    }

    /**
     * 发起调用
     * @param api
     * @param repoPath
     * @param paramHandle
     * @return
     */
    private static String sendRequest(String api, String repoPath, Consumer<GetBuilder> paramHandle) {
        String url = host + String.format(api, repoPath);
        logger.info("antCode api:{}", url);
        GetBuilder getBuilder = HttpClient.get(url).header(HEADER_PRIVATE_TOKEN, token);
        if (paramHandle != null) {
            paramHandle.accept(getBuilder);
        }
        String response = getBuilder.syncExecute(5000);
        logger.info("antCode response:{}", StringUtils.substring(response, 0, 100));
        return response;
    }

    /**
     * 文件比较
     */
    public static class FileCompare {

        private String oldPath;

        private String newPath;

        /**
         * M    修改
         * A    新增
         * D    删除
         * R    重命名
         */
        private String status;

        /**
         * 构造函数
         */
        public FileCompare() {
        }

        /**
         * 构造函数
         * @param newPath
         * @param status
         */
        public FileCompare(String newPath, String status) {
            this.newPath = newPath;
            this.status = status;
        }

        public String getOldPath() {
            return oldPath;
        }

        public void setOldPath(String oldPath) {
            this.oldPath = oldPath;
        }

        public String getNewPath() {
            return newPath;
        }

        public void setNewPath(String newPath) {
            this.newPath = newPath;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }

    /**
     * 代码 commit 信息
     */
    public static class CommitInfo {

        private String commitDate;

        private String commitId;

        private String groupPath;

        private String projectPath;

        private String branch;

        public String getCommitDate() {
            return commitDate;
        }

        public void setCommitDate(String commitDate) {
            this.commitDate = commitDate;
        }

        public String getCommitId() {
            return commitId;
        }

        public void setCommitId(String commitId) {
            this.commitId = commitId;
        }

        public String getGroupPath() {
            return groupPath;
        }

        public void setGroupPath(String groupPath) {
            this.groupPath = groupPath;
        }

        public String getProjectPath() {
            return projectPath;
        }

        public void setProjectPath(String projectPath) {
            this.projectPath = projectPath;
        }

        public String getBranch() {
            return branch;
        }

        public void setBranch(String branch) {
            this.branch = branch;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this);
        }
    }

    /**
     * 仓库组信息
     */
    public static class RepoGroupInfo {
        private String name;
        private Integer id;


        public RepoGroupInfo() {
        }

        public RepoGroupInfo(String name, Integer id) {
            this.name = name;
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }
    }

    /**
     * @description 从git url的http(s) URL 中移除 token
     *
     * <AUTHOR>
     * @since 2024.09.29
     * @param url url
     * @return java.lang.String
     */
    public static String removeTokenFromUrl(String url) {
        Pattern pattern = Pattern.compile(TOKEN_REGEX);
        Matcher matcher = pattern.matcher(url);

        // 替换为不带token的URL
        if (matcher.find()) {
            url = url.replace(matcher.group(), matcher.group(1)); // 保留http或https
        }

        // 如果不匹配，返回原始URL
        return url;
    }

    public enum AccessLevel {
        
        NULL(0),
        REPORTER(20),
        DEVELOPER(30),
        MASTER(40),
        OWNER(50)
        ;

        private int level;

        AccessLevel(int level) {
            this.level = level;
        }

        /**
         * 允许访问
         * @param level
         * @return
         */
        public static boolean AllowAccess(int level) {
            return level >= REPORTER.level;
        }
    }

}
