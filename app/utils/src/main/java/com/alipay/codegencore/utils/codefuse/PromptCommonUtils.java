package com.alipay.codegencore.utils.codefuse;

import freemarker.cache.NullCacheStorage;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0 2024/8/13 14:32
 */
public class PromptCommonUtils {


    private static final Logger logger = LoggerFactory.getLogger(PromptCommonUtils.class);

    /**
     * 因为不用缓存，所以都使用统一名称
     */
    private static final String DEFAULT_TEMPLATE_NAME = "default";

    /**
     * 模板配置
     */
    private static Configuration configuration;

    static {
        configuration = new Configuration(Configuration.VERSION_2_3_33);
        configuration.setDefaultEncoding("UTF-8");
        //不使用缓存
        configuration.setCacheStorage(NullCacheStorage.INSTANCE);
    }

    /**
     * 根据模板获取完整内容
     * @param templateText
     * @param params
     * @return
     */
    public static String buildPrompt(String templateText, Map<String, Object> params) {
        return buildPrompt(DEFAULT_TEMPLATE_NAME, templateText, params);
    }

    /**
     * 根据模板获取完整内容
     * @param templateName
     * @param templateText
     * @param params
     * @return
     */
    public static String buildPrompt(String templateName, String templateText, Map<String, Object> params) {

        try (StringWriter stringWriter = new StringWriter()) {
            Template template = new Template(templateName, new StringReader(templateText), configuration);
            template.process(params, stringWriter);
            return stringWriter.toString();
        } catch (IOException e) {
            logger.error("获取模板内容异常. name:{}; template:{}; params:{}", templateName, templateText, params, e);
        } catch (TemplateException e) {
            logger.error("模板数据处理异常. name:{}; template:{}; params:{}", templateName, templateText, params, e);
        }
        return null;

    }


}
