package com.alipay.codegencore.utils.thread;


import com.alipay.common.tracer.concurrent.TracerCallable;
import com.alipay.common.tracer.concurrent.TracerRunnable;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 线程池工具
 */
public class ThreadPoolUtils {

	private static final Logger logger = LoggerFactory.getLogger(ThreadPoolUtils.class);

	/**
	 * 通用异步模型调用线程
	 */
	public static final ThreadPoolExecutor indexBuildPool = new ThreadPoolExecutor(
			20, 60, 600L, TimeUnit.SECONDS,
			new ArrayBlockingQueue<>(200),
			new ThreadFactoryBuilder().setNameFormat("IndexBuild-%d").build(),
			(r, executor) -> logger.error("index build can't handle.{}", executor.getActiveCount()));

	/**
	 * 处理 antcode 回调，进行增量更新
	 */
	public static final ThreadPoolExecutor partIndexBuildPool = new ThreadPoolExecutor(
			10, 30, 600L, TimeUnit.SECONDS,
			new ArrayBlockingQueue<>(500),
			new ThreadFactoryBuilder().setNameFormat("PartIndexBuild-%d").build(),
			(r, executor) -> logger.error("ant code callback part index build can't handle.{}", executor.getActiveCount()));

	/**
	 * plan 和 code 生成
	 */
	public static final ThreadPoolExecutor actionGenCodePool = new ThreadPoolExecutor(
			5, 10, 600L, TimeUnit.SECONDS,
			new ArrayBlockingQueue<>(50),
			new ThreadFactoryBuilder().setNameFormat("actionGenCode-%d").build(),
			(r, executor) -> logger.error("action gen code failed.{}", executor.getActiveCount()));

	/**
	 * 无返回
	 * @param threadPool
	 * @param supplier
	 */
	public static void execute(ThreadPoolExecutor threadPool, VoidSupplier supplier) {
		threadPool.execute(new TracerRunnable() {
			@Override
			public void doRun() {
				supplier.exec();
			}
		});
	}

	/**
	 * 有返回
	 * @param threadPool
	 * @param supplier
	 * @return
	 * @param <T>
	 */
	public static <T> Future<T>  submit(ThreadPoolExecutor threadPool, Supplier<T> supplier) {
		return threadPool.submit(new TracerCallable<>() {

			@Override
			public T doCall() throws Exception {
				return supplier.get();
			}
		});
	}

	/**
	 * @param supplier
	 */
	public static void indexBuildExecute(VoidSupplier supplier) {
		execute(indexBuildPool, supplier);
	}

	/**
	 * @param supplier
	 * @return
	 * @param <T>
	 */
	public static <T> Future<T>  indexBuildSubmit(Supplier<T> supplier) {
		return submit(indexBuildPool, supplier);
	}

}
