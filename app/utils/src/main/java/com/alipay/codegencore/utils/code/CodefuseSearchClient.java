package com.alipay.codegencore.utils.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.answer.TaskState;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.codegencore.utils.http.GetBuilder;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.PostBuilder;
import com.alipay.common.tracer.util.TracerContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 2024/10/13 22:04
 */
public class CodefuseSearchClient {


    private static final Logger logger = LoggerFactory.getLogger(CodefuseSearchClient.class);


    /**
     * 初始化配置
     */
    public static final String CODEFUSE_SEARCH_CONFIG = "CODEFUSE_SEARCH_CONFIG";

    /**
     * 索引构建接口
     */
    private static final String INDEX_BUILD_PATH = "/openapi/index/build";
    /**
     * vat搜索接口
     */
    private static final String VAT_SEARCH_PATH = "/openapi/search/vat";

    /**
     * 查询索引构建状态
     */
    private static final String VAT_QUERY_INDEX_STATUS_PATH = "/openapi/index/status";

    /**
     * 请求头-用户
     */
    private static final String CODEFUSEARCH_USER_HEADER_KEY = "codefusesearch_user";
    /**
     * 请求头-token
     */
    private static final String CODEFUSEARCH_TOKEN_HEADER_KEY = "codefusesearch_token";

    /**
     * 预发环境host
     */
    private static final String PRE_HOST = "https://cfsearch-pre.antgroup-inc.cn";


    /**
     * host
     */
    private static String host;

    /**
     * 用户名
     */
    private static String user;

    /**
     * token
     */
    private static String token;

    /**
     * 初始化
     * @param config
     */
    public static void init(String config) {
        logger.info("init code search config:{}", config);
        if (StringUtils.isBlank(config)) {
            throw new RuntimeException("code search not config. please check!!!");
        }
        JSONObject configObject = JSON.parseObject(config);
        host = configObject.getString("host");
        if (VisableEnvUtil.isPrePub()) {
            host = PRE_HOST;
        }
        user = configObject.getString("user");
        token = configObject.getString("token");
    }

    /**
     * 查询索引构建状态
     * @return
     */
    public static JSONObject queryIndexStatus(String repoPath, String branch, String taskName) {
        logger.info("query index status path:{} branch:{} task:{}", repoPath, branch, taskName);
        GetBuilder getBuilder = HttpClient.get(host + VAT_QUERY_INDEX_STATUS_PATH)
                .addParameter("repoPath", repoPath)
                .addParameter("branch", branch)
                .addParameter("taskName", taskName)
                .header(AppConstants.HTTP_TRACE_ID_HEADER, TracerContextUtil.getTraceId())
                .header(CODEFUSEARCH_USER_HEADER_KEY, user)
                .header(CODEFUSEARCH_TOKEN_HEADER_KEY, token);

        try {
            String result = getBuilder.syncExecute(3000);
            logger.info("query index status result:{}", result);
            return stateHandle(result);
        } catch (Exception e) {
            logger.error("query index status failed. path:{} branch:{} task:{}", repoPath, branch, taskName, e);
        }
        return null;
    }

    /**
     * 发送索引构建任务
     * @param repoUrl
     * @param branch
     * @param taskName
     */
    public static JSONObject sendIndexBuild(String repoUrl, String branch, String taskName) {

        logger.info("index build url:{} branch:{} task:{}", repoUrl, branch, taskName);
        JSONObject params = new JSONObject();
        params.put("repoURL", repoUrl);
        params.put("branch", branch);
        params.put("taskName", taskName);
        PostBuilder postBuilder = HttpClient.post(host + INDEX_BUILD_PATH)
                .content(params.toJSONString())
                .header(AppConstants.HTTP_TRACE_ID_HEADER, TracerContextUtil.getTraceId())
                .header(CODEFUSEARCH_USER_HEADER_KEY, user)
                .header(CODEFUSEARCH_TOKEN_HEADER_KEY, token);

        try {
            String result = postBuilder.syncExecute(30000);
            logger.info("index build result:{}", result);
            return stateHandle(result);
        } catch (Exception e) {
            logger.error("index build failed. url:{} branch:{} task:{}", repoUrl, branch, taskName, e);
        }
        return null;
    }

    /**
     * vat搜索
     * @param repoURL
     * @param branch
     * @param query
     * @return
     */
    public static List<BloopSearchClient.CodeResult> vatSearch(String repoURL, String branch, String query) {
        logger.info("vat search, repoUrl: {} branch:{} query:{}", repoURL, branch, query);
        JSONObject repoInfo = new JSONObject();
        repoInfo.put("repoURL", repoURL);
        repoInfo.put("branch", branch);

        JSONObject params = new JSONObject();
        params.put("query", query);
        params.put("repoInfo", repoInfo);

        PostBuilder postBuilder = HttpClient.post(host + VAT_SEARCH_PATH)
                .content(params.toJSONString())
                .header(AppConstants.HTTP_TRACE_ID_HEADER, TracerContextUtil.getTraceId())
                .header(CODEFUSEARCH_USER_HEADER_KEY, user)
                .header(CODEFUSEARCH_TOKEN_HEADER_KEY, token);

        try {
            String result = postBuilder.syncExecute(30000);

            logger.info("index build result:{}", result);
            JSONObject resultObject = JSON.parseObject(result);
            if (resultObject.getInteger("errorCode") != 0) {
                logger.error("[SVAT] vat search failed. repoUrl:{} branch:{} query:{} result:{}", repoURL, branch, query, result);
                String errorMsg = resultObject.getString("errorMsg");
                throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL, errorMsg);
            }
            JSONObject searchResult = resultObject.getJSONObject("data");
            JSONArray codeChunkWithCompleteSchema = searchResult.getJSONArray("codeChunkWithCompleteSchema");
            JSONArray rankedChunks = searchResult.getJSONArray("rankedChunks");

            List<BloopSearchClient.CodeResult> results = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(codeChunkWithCompleteSchema)) {
                logger.info("vat search codeChunkWithCompleteSchema size: {}", codeChunkWithCompleteSchema.size());
                for (int i = 0; i < codeChunkWithCompleteSchema.size(); i++) {
                    JSONObject codeObject = codeChunkWithCompleteSchema.getJSONObject(i);
                    parseCodefuseSearchCodeChunk(results, codeObject);
                }
            }else{
                logger.info("vat search codeChunkWithCompleteSchema is empty, fall back to use rankedChunks. rankedChunks size: {} ", rankedChunks.size());
                for (int i = 0; i < rankedChunks.size(); i++) {
                    JSONObject scoredCodeObject = rankedChunks.getJSONObject(i);
                    JSONObject codeObject = scoredCodeObject.getJSONObject("codeChunk");
                    parseCodefuseSearchCodeChunk(results, codeObject);
                }
            }
            return results;

        } catch (Exception e) {
            logger.error("[SVAT] vat search exception. repoUrl:{} branch:{} query:{}", repoURL, branch, query, e);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_EXCEPTION, e);
        }
    }

    private static void parseCodefuseSearchCodeChunk(List<BloopSearchClient.CodeResult> results, JSONObject codeObject) {
        BloopSearchClient.CodeResult codeResult = new BloopSearchClient.CodeResult();
        codeResult.setPath(codeObject.getString("relativePath"));
        codeResult.setContent(codeObject.getString("snippet"));
        codeResult.setStartLine(codeObject.getInteger("startLine"));
        codeResult.setEndLine(codeObject.getInteger("endLine"));
        results.add(codeResult);
    }

    /**
     * 处理索引构建状态
     * @param result
     * @return
     */
    private static JSONObject stateHandle(String result) {
        JSONObject resultObject = JSON.parseObject(result);
        JSONObject data = resultObject.getJSONObject("data");
        if (data != null) {
            String state = data.getString("state");
            if ("SUCCESS".equalsIgnoreCase(state)) {
                data.put("state", TaskState.FINISH.name());
            } else if ("FAIL".equalsIgnoreCase(state)) {
                data.put("state", TaskState.FULL_BUILDING.name());
            }
        }
        return data;
    }

}
