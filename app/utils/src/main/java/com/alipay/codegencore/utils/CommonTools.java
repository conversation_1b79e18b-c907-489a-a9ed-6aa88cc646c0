package com.alipay.codegencore.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.CheckCodeModel;
import com.alipay.codegencore.model.model.analysis.TempCodeAnalysisResultContext;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.common.tracer.util.TracerContextUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.security.MessageDigest;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 通用工具（近端+服务端共用)
 *
 * <AUTHOR>
 * 创建时间 2022-06-08
 */
public class CommonTools {
    private static final char[] DIGITS_LOWER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',
            'e', 'f'};

    private static final String[] CHINESE_UNITS = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", "万亿"};
    private static final char[] CHINESE_NUMBERS = {'零', '一', '二', '三', '四', '五', '六', '七', '八', '九'};


    public static final Set<String> JAVA_KEY_WORLD = new HashSet<>();

    static {
        String[] keyworldArr = AppConstants.JAVA_KEY_WORLD.split(",");
        JAVA_KEY_WORLD.addAll(Arrays.asList(keyworldArr));
    }

    /**
     * 获取输入流的文件标识（用来校验文件完整性)
     * 备注:和apache的DigestUtils.sha512Hex方法实现一致，为了简化近端实现，故没有引入第三方jar
     *
     * @param inputStream
     * @return
     */
    public static String sha512Hex(InputStream inputStream) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-512");
            byte[] buffer = new byte[1024];
            for (int read = inputStream.read(buffer, 0, 1024); read > -1; read = inputStream.read(buffer, 0, 1024)) {
                digest.update(buffer, 0, read);
            }
            byte[] data = digest.digest();
            final int l = data.length;
            final char[] out = new char[l << 1];
            encodeHex(data, 0, data.length, DIGITS_LOWER, out, 0);
            return new String(out);
        } catch (Throwable throwable) {
            throw new IllegalArgumentException("文件md5标识获取失败", throwable);
        }
    }

    /**
     * 分桶计算（利用murmurhash算法)
     *
     * @param key 分流key
     * @param num 分流版本个数
     * @return
     */
    public static int divert(String key, int num) {
        int hashCode = hash32(key);
        return Math.abs(hashCode % num);
    }

    /**
     * 下载文件
     *
     * @param downloadUrl
     * @param filePath
     * @return
     * @throws Throwable
     */
    public static boolean downloadFile(String downloadUrl, String filePath) throws Throwable {
        URL url1 = new URL(downloadUrl);
        URLConnection conn = url1.openConnection();
        try (InputStream inStream = conn.getInputStream(); FileOutputStream fs = new FileOutputStream(filePath);) {
            byte[] buffer = new byte[1204];
            int byteread = 0;
            while ((byteread = inStream.read(buffer)) != -1) {
                fs.write(buffer, 0, byteread);
            }
        }
        return true;
    }

    /**
     * 解压缩
     * //todo 注意，此处仅是简单的解压缩。压缩文件内部不包含文件夹和多层嵌套。如果包含文件夹，还需 创建文件夹，如果包含嵌套，还需要判断父目录是否存在
     *
     * @param srcFile
     * @param destPath
     */
    public static List<File> unzip(File srcFile, String destPath) {
        ZipFile zipFile = null;
        List<File> targetFileList = new LinkedList<>();
        try {
            zipFile = new ZipFile(srcFile);
            Enumeration<? extends ZipEntry> entries = zipFile.entries();

            while (entries.hasMoreElements()) {
                ZipEntry zipEntry = entries.nextElement();
                File destFile = new File(destPath, zipEntry.getName());
                //如果已经解压过，则跳过
                if (destFile.exists()) {
                    targetFileList.add(destFile);
                    continue;
                }
                if (!isAvaliableFielName(zipEntry.getName())) {
                    continue;
                }
                destFile.createNewFile();
                InputStream inputStream = zipFile.getInputStream(zipEntry);
                FileUtils.copyToFile(inputStream, destFile);
                destFile.setExecutable(true);
                targetFileList.add(destFile);
            }
        } catch (Throwable throwable) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, throwable);
        } finally {
            IOUtils.closeQuietly(zipFile);
        }
        return targetFileList;
    }

    /**
     * 判断是否为有效的文件名
     * 压缩文件里面可能存有类似_MAC开头等文件，不需要保存
     *
     * @param name
     * @return
     */
    private static boolean isAvaliableFielName(String name) {
        if (name == null) {
            return false;
        }
        char c = name.charAt(0);
        return Character.isUpperCase(c) || Character.isLowerCase(c) || Character.isDigit(c);
    }

    /**
     * 获取展示名
     * 1:前面填充非完整token字符，组装成完整token
     * 2:截取补全数据的长度
     *
     * @param content
     * @param length
     * @return
     */
    public static String getDisplayName(String realLastLine, String content, int length) {
        if (content == null || content.trim().length() == 0) {
            return content;
        }
        StringBuilder result = new StringBuilder(content);
        //填充前缀
        if (realLastLine != null && realLastLine.trim().length() > 0) {
            char[] charArr = realLastLine.toCharArray();
            for (int i = charArr.length - 1; i >= 0; i--) {
                char c = charArr[i];
                if (Character.isUpperCase(c) || Character.isLowerCase(c) || Character.isDigit(c)) {
                    result.insert(0, c);
                } else {
                    break;
                }
            }

        }
        content = result.toString().trim().replace(" < ","<").replace(" > ",">");
        if (length > content.length()) {
            return content;
        }
        return content.substring(0, length) + "...";
    }

    /**
     * 字节数组编码
     *
     * @param data
     * @param dataOffset
     * @param dataLen
     * @param toDigits
     * @param out
     * @param outOffset
     */
    private static void encodeHex(final byte[] data, final int dataOffset, final int dataLen, final char[] toDigits,
                                  final char[] out, final int outOffset) {
        // two characters form the hex value.
        for (int i = dataOffset, j = outOffset; i < dataOffset + dataLen; i++) {
            out[j++] = toDigits[(0xF0 & data[i]) >>> 4];
            out[j++] = toDigits[0x0F & data[i]];
        }
    }

    /**
     * hash算法
     *
     * @param text
     * @return
     */
    private static int hash32(String text) {
        byte[] bytes = text.getBytes();
        return hash32(bytes, bytes.length, 0x9747b28c);
    }

    /**
     * hash32算法
     *
     * @param data
     * @param length
     * @param seed
     * @return
     */
    private static int hash32(final byte[] data, int length, int seed) {
        // 'm' and 'r' are mixing constants generated offline.
        // They're not really 'magic', they just happen to work well.
        final int m = 0x5bd1e995;
        final int r = 24;

        // Initialize the hash to a random value
        int h = seed ^ length;
        int length4 = length / 4;

        for (int i = 0; i < length4; i++) {
            final int i4 = i * 4;
            int k = (data[i4 + 0] & 0xff) + ((data[i4 + 1] & 0xff) << 8)
                    + ((data[i4 + 2] & 0xff) << 16) + ((data[i4 + 3] & 0xff) << 24);
            k *= m;
            k ^= k >>> r;
            k *= m;
            h *= m;
            h ^= k;
        }
        // Handle the last few bytes of the input array
        switch (length % 4) {
            case 3:
                h ^= (data[(length & ~3) + 2] & 0xff) << 16;
                break;
            case 2:
                h ^= (data[(length & ~3) + 1] & 0xff) << 8;
                break;
            case 1:
                h ^= (data[length & ~3] & 0xff);
                h *= m;
                break;
            default:
                break;
        }
        h ^= h >>> 13;
        h *= m;
        h ^= h >>> 15;
        return h;
    }


    /**
     * 结合realLastLine和补全结果，分析出校验对象
     * 场景1 ： userService.addUser => 单个CheckCodeModel。例如 UserService addUser
     * 场景2 : getUser().getId();=> 多个CheckCodeModel
     * //todo 当前仅覆盖step1场景
     * <ul>
     *     <li>dotIndex是.的第一个出现位置, leftBracketInex是(第一个出现位置。如果dotIndex和leftBracketInex都<1，那么不是 a.b() 或者 a() 格式，暂时不做校验</li>
     *     <li>a()包含两种情况。a()->函数调用, new A()->对象创建</li>
     * </ul>
     *
     * @param content
     * @return
     */
    public static List<CheckCodeModel> analysisCheckModelList(String content, String realLastLine, TempCodeAnalysisResultContext tempCodeAnalysisResultContext) {
        String realLine = realLastLine + content;
        if (realLine == null || realLine.trim().length() == 0 || tempCodeAnalysisResultContext.getWritingMethodBodyModel() == null) {
            return null;
        }
        realLine = realLine.trim();
        int dotIndex = realLine.indexOf(".");
        int leftBracketInex = realLine.indexOf("(");

        char[] charArr = realLine.toCharArray();

        if (dotIndex < 1 && leftBracketInex < 1) {
            return null;
        }
        List<CheckCodeModel> result = new LinkedList<>();
        if (dotIndex >= 1) {
            String referenceName = getTargetName(charArr, dotIndex, DirectionEnum.FRONT);
            String methodOrFieldName = getTargetName(charArr, dotIndex, DirectionEnum.BACK);
            if (referenceName == null || referenceName.trim().length() == 0) {
                return null;
            }
            String className = getClassNameFromCodeContext(referenceName, tempCodeAnalysisResultContext);
            CheckCodeModel checkCodeModel = new CheckCodeModel();
            checkCodeModel.setClassName(className);
            checkCodeModel.setReferenceName(referenceName);
            checkCodeModel.setMethodOrFieldName(methodOrFieldName);
            if (checkCodeModel.getClassName() != null || checkCodeModel.getReferenceName() != null) {
                result.add(checkCodeModel);
                return result;
            }
        }
        String methodOrFieldName = getTargetName(charArr, leftBracketInex, DirectionEnum.FRONT);
        if (methodOrFieldName == null || methodOrFieldName.trim().length() == 0 || JAVA_KEY_WORLD.contains(methodOrFieldName)) {
            return null;
        }
        CheckCodeModel checkCodeModel = new CheckCodeModel();
        if (Character.isUpperCase(methodOrFieldName.charAt(0))) {
            checkCodeModel.setClassName(methodOrFieldName);
        } else {
            checkCodeModel.setMethodOrFieldName(methodOrFieldName);
            checkCodeModel.setClassName(tempCodeAnalysisResultContext.getClassName());
        }
        result.add(checkCodeModel);
        return result;
    }

    /**
     * 初步校验
     * 场景1： 有referenceName但是没有查到className。说明referenceName补全不正确。过滤
     *
     * @param checkCodeModelList
     * @return
     */
    public static boolean checkCode(List<CheckCodeModel> checkCodeModelList) {
        for (CheckCodeModel checkCodeModel : checkCodeModelList) {
            if (checkCodeModel.getReferenceName() != null && checkCodeModel.getClassName() == null) {
                return false;
            }
        }
        return true;
    }

    /**
     * 写入servlet response
     * @param data
     * @param statusCode
     * @param response
     * @param responseEnum
     * @param throwable
     * @throws IOException
     */
    public static void writeResponse(Object data, int statusCode, HttpServletResponse response, ResponseEnum responseEnum, Throwable throwable) throws IOException {

        Map<String, Object> ret = new HashMap<>();
        ret.put("errorCode", responseEnum == null ? 0 : responseEnum.getErrorCode());
        ret.put("errorMsg", throwable == null ? null : throwable.getMessage());
        ret.put("traceId", TracerContextUtil.getTraceId());

        if(data instanceof ChatMessage){
            ChatMessage chatMessage = (ChatMessage) data;
            chatMessage.convertEmptyPropertyStringToNull();
            ret.put("data", chatMessage.getContent());
            ret.put("functionCall",((ChatMessage)data).getFunctionCall());
        }else if(data instanceof String){
            if(((String)data).startsWith(AppConstants.DEFAULT_CODEGPT_FUNCTION_CALL_START_TOKEN)){
                JSONObject functionResponse = parseFunctionResponse((String) data);
                ret.put("data", null);
                ret.put("functionCall",functionResponse);
            }else{
                ret.put("data", data);
            }
        }else{
            ret.put("data", data);
        }

        response.setStatus(statusCode);
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(JSON.toJSONString(ret, SerializerFeature.PrettyFormat));
        response.getWriter().write("\n");
        response.getWriter().flush();
    }


    /**
     * 从代码上下文中，查找reference的class名
     *
     * @param referenceName
     * @param tempCodeAnalysisResultContext
     * @return
     */
    private static String getClassNameFromCodeContext(String referenceName, TempCodeAnalysisResultContext tempCodeAnalysisResultContext) {
        Map<String, String> localVariableMap = tempCodeAnalysisResultContext.getLocalVariableMap();
        Map<String, String> fieldReferenceMap = tempCodeAnalysisResultContext.getFieldReferenceMap();
        //如果开头字符是大写，则说明补全是静态函数，referenceName即是className
        if (referenceName.length() > 0 && Character.isUpperCase(referenceName.charAt(0))) {
            return referenceName;
        }
        String result = null;
        if (localVariableMap != null) {
            result = localVariableMap.get(referenceName);
        }
        if (result == null && fieldReferenceMap != null) {
            result = fieldReferenceMap.get(referenceName);
        }
        //去掉所有<>内容，过滤掉范型（客户端已经过滤了，此处过滤为了兼容老版本客户端)
        if (result != null) {
            result = result.replaceAll(AppConstants.INVALID_TOKEN_STR, "");
        }
        return result;
    }


    /**
     * 根据direction方向，从index位置，向前/后遍历charArr，获取token字符
     *
     * @param charArr
     * @param index
     * @param directionEnum
     * @return
     */
    private static String getTargetName(char[] charArr, int index, DirectionEnum directionEnum) {
        StringBuilder sb = new StringBuilder();
        switch (directionEnum) {
            case FRONT:
                if (index - 1 < 0) {
                    break;
                }
                for (int i = index - 1; i >= 0; i--) {
                    char c = charArr[i];
                    if (Character.isUpperCase(c) || Character.isLowerCase(c) || Character.isDigit(c)) {
                        sb.insert(0, c);
                    } else {
                        break;
                    }
                }
                break;
            case BACK:
                if (index + 1 >= charArr.length) {
                    break;
                }
                for (int i = index + 1; i < charArr.length; i++) {
                    char c = charArr[i];
                    if (Character.isUpperCase(c) || Character.isLowerCase(c) || Character.isDigit(c)) {
                        sb.append(c);
                    } else {
                        break;
                    }
                }
                break;
            default:
                throw new BizException(ResponseEnum.ERROR_THROW, "无效的方向");

        }
        String result = sb.toString();
        return result.trim().length() == 0 ? null : result;
    }

    /**
     * 如果传入的是<6位数的,那么在前面补全0到6位数
     * param: 23239 return 023239
     * @return empId
     */
    public static String getEmpIdAdd0(String empId){
        if (empId.length() < AppConstants.EMP_ID_MIN_LENGTH) {
            return "0".repeat(AppConstants.EMP_ID_MIN_LENGTH - empId.length()) + empId;
        }
        return empId;
    }


    /**
     * int类型正整数转中文
     */
    public static String number2Chinese(int num) {

        char[] val = String.valueOf(num).toCharArray();
        int len = val.length;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < len; i++) {
            String m = val[i] + "";
            int n = Integer.parseInt(m);
            boolean isZero = n == 0;
            String unit = CHINESE_UNITS[(len - 1) - i];
            if (isZero) {
                boolean falg = false;
                for (int j = i; j < len; j++) {
                    String a = val[j] + "";
                    int b = Integer.parseInt(a);
                    if (b != 0) {
                        falg = true;
                    }
                }
                if (falg) {
                    if ('0' != val[i - 1]) {
                        if (((len - 1) - i) % 4 == 0) {
                            sb.append(unit);
                        }
                        sb.append(CHINESE_NUMBERS[n]);
                    }
                }
                else {
                    if (((len - 1) - i) % 4 == 0) {
                        sb.append(unit);
                        break;
                    }
                }
            }
            else {
                sb.append(CHINESE_NUMBERS[n]);
                sb.append(unit);
            }
        }
        if (sb.lastIndexOf("一十") == 0) { //10-19,“一十”都改成“十”
            return sb.substring(1);
        }
        if ("".equals(sb.toString())){
            return "零";
        }
        return sb.toString();
    }


    /**
     * 遍历顺序
     */
    private enum DirectionEnum {
        /**
         * 向前遍历
         */
        FRONT,
        /**
         * 向后遍历
         */
        BACK
    }
    /**
     * 解析#function开头的返回
     *
     * <AUTHOR>
     * @since 2024.03.12
     * @param functionResponse functionResponse
     * @return com.alibaba.fastjson.JSONObject
     */
    private static JSONObject parseFunctionResponse(String functionResponse){
        functionResponse = functionResponse.substring(AppConstants.DEFAULT_CODEGPT_FUNCTION_CALL_START_TOKEN.length());
        try{
            return JSON.parseObject(functionResponse);
        }catch (Exception e){
            throw new BizException(ResponseEnum.ERROR_THROW,"解析#function开头的返回失败:"+e.getMessage());
        }

    }


    /**
     * 将字符串转换为不区分大小写的正则表达式
     *
     * @param input 输入字符串
     * @return 不区分大小写的正则表达式
     */
    public static String toCaseInsensitiveRegex(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        StringBuilder regex = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (Character.isLetter(c)) {
                regex.append("[")
                        .append(Character.toLowerCase(c))
                        .append(Character.toUpperCase(c))
                        .append("]");
            } else {
                regex.append(c);
            }
        }
        return regex.toString();
    }
}
