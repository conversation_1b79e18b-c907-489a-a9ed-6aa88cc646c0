package com.alipay.codegencore.utils.http;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * Get方法构建器
 *
 * <AUTHOR>
 * 创建时间 2022-10-17
 */
public class GetBuilder extends AbstractRequestBuilder {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetBuilder.class);

    private Map<String, String> param;

    /**
     * 头部
     */
    private final List<String> HEADER = new ArrayList<>();

    /**
     * json格式参数
     */
    private String jsonContent;

    /**
     * get请求构造函数
     *
     * @param url
     * @param httpClient
     */
    public GetBuilder(String url, java.net.http.HttpClient httpClient) {
        super(url, httpClient);
    }

    /**
     * 增加json参数
     *
     * @param jsonContent
     * @return
     */
    public GetBuilder content(String jsonContent) {
        this.jsonContent = jsonContent;
        return this;
    }

    /**
     * 增加get参数
     *
     * @param key
     * @param value
     * @return
     */
    public GetBuilder addParameter(String key, String value) {
        if (param == null) {
            param = new HashMap<>();
        }
        param.put(key, value);
        return this;
    }

    /**
     * 增加头部
     *
     * @param key
     * @param value
     * @return
     */
    public GetBuilder header(String key, String value) {
        if (key == null || value == null) {
            throw new IllegalArgumentException("key and value can not be null!");
        }
        this.HEADER.add(key);
        this.HEADER.add(value);
        return this;
    }

    /**
     * 同步发起get请求
     *
     * @return
     */
    @Override
    public String syncExecute(long timeout) {
        try {
            return syncExecuteWithExceptionThrow(timeout);
        } catch (Throwable e) {
            LOGGER.error("请求远程服务Http(GET)异常。地址: {}", url, e);
            return null;
        }
    }

    /**
     * get方式暂时不支持流式调用
     *
     * @param timeout
     * @param streamDataListener
     */
    @Override
    protected void streamExecute(long timeout, StreamDataListener streamDataListener, BiConsumer<Integer, String> errorHandler) {
        throw new UnsupportedOperationException("get方式暂时不支持流式调用");
    }

    @Override
    public void streamExecuteWithResponseHandler(long timeout, StreamDataListener streamDataListener, BiConsumer<Integer, String> errorHandler, Consumer<HttpResponse.ResponseInfo> responseInfoConsumer) throws URISyntaxException {
        throw new UnsupportedOperationException("get方式暂时不支持流式调用");
    }

    /**
     * 同步发起get请求，可能会抛异常
     *
     * @param timeout
     * @return
     * @throws IOException
     * @throws InterruptedException
     * @throws URISyntaxException
     */
    @Override
    public String syncExecuteWithExceptionThrow(long timeout) throws IOException, InterruptedException, URISyntaxException {
        return syncExecuteWithFullResponse(timeout).body();
    }

    /**
     * 同步执行，执行报错会抛异常，返回完整的response
     *
     * @param timeout 超时时间
     * @return 完整的response
     */
    @Override
    protected HttpResponse<String> syncExecuteWithFullResponse(long timeout) throws IOException, InterruptedException, URISyntaxException {
        if (param != null && param.size() > 0) {
            StringBuilder urlBuilder = new StringBuilder(url);
            urlBuilder.append("?");
            for (Map.Entry<String, String> entry : param.entrySet()) {
                urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            url = urlBuilder.substring(0, urlBuilder.length() - 1);
        }
        HttpRequest.Builder httpRequestBuilder = HttpRequest.newBuilder().uri(new URI(url));
        if (CollectionUtils.isNotEmpty(HEADER)) {
            httpRequestBuilder.headers(HEADER.toArray(String[]::new));
        }
        HttpRequest.Builder builder;
        if (StringUtils.isBlank(jsonContent)) {
            builder = httpRequestBuilder.GET();
        } else {
            builder = httpRequestBuilder.method("GET",
                    HttpRequest.BodyPublishers.ofString(jsonContent));
        }
        HttpRequest httpRequest = builder.timeout(Duration.ofMillis(timeout)).build();
        return httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());
    }
}
