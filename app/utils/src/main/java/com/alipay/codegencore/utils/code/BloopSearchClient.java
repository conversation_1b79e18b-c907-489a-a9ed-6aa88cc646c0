package com.alipay.codegencore.utils.code;

import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpTimeoutException;
import java.util.List;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.utils.http.GetBuilder;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.PostBuilder;
import com.alipay.common.tracer.util.TracerContextUtil;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @version 1.0 2024/8/14 17:58
 */
public class BloopSearchClient {

    private static final Logger logger = LoggerFactory.getLogger(BloopSearchClient.class);

    /**
     * 查询相关文件
     */
    private static final String SEARCH_FILE_PATH = "/api/bloop/search";

    /**
     * 查询仓库 wiki
     */
    private static final String SEARCH_WIKI = "/api/bloop/repo/wiki/query";

    /**
     * 删除索引
     */
    private static final String REMOVE_INDEX = "/api/bloop/remove/index";


    /**
     * vat 新版搜索
     */
    private static final String VAT_SEARCH_V2 = "/api/bloop/search/vat/chunk";

    /**
     * 查询构建进度
     */
    private static final String GET_PROGRESS = "/api/bloop/repo/index/progress";

    /**
     * 查询构建详情
     */
    private static final String GET_DETAIL = "/api/bloop/repo/index/detail";

    /**
     * 删除仓库
     */
    private static final String CLEAN = "/api/bloop/repo/index/clean";

    /**
     * 仓库前缀
     */
    private static final String REPO_PREFIX = "code.alipay.com/";

    /**
     * 访问身份
     */
    private static final String USER = "SVAT";

    /**
     * trace id
     */
    private static final String HEADER_TRACE = "X-ANSWER-TRACE-ID";

    /**
     * 默认超时 30 秒
     */
    private static final int DEFAULT_TIME_OUT = 30000;

    /**
     * 服务 host 域名
     */
    private static String host = "https://antnluservice.alipay.com";

    /**
     * 初始化
     * @param host
     */
    public static void init(String host) {
        BloopSearchClient.host = host;
        logger.info("init host:{}", BloopSearchClient.host);
    }


    /**
     * 获取任务构建进度
     * @param recordId
     * @param repoURL
     * @param branch
     * @param commit
     * @return
     */
    public static double getTaskProgress(Long recordId, String repoURL, String branch, String commit) {
        logger.info("get record:{} progress repoURL:{} branch:{} commit:{}",
                recordId, repoURL, branch, commit);
        JSONObject requestBody = new JSONObject();
        requestBody.put("repo_ref", AntCodeClient.getRepoRef(repoURL));
        requestBody.put("branch", branch);
        requestBody.put("commit", commit);

        GetBuilder getBuilder = HttpClient.get(host + GET_PROGRESS)
                .content(requestBody.toJSONString());

        try {
            String body = getBuilder.syncExecute(3000);
            if (StringUtils.isBlank(body)) {
                return 0D;
            }
            JSONObject response = JSON.parseObject(body);
            if (!response.getBooleanValue("success")) {
                logger.error("get record:{} progress failed. {}", recordId, body);
                return 0D;
            }

            double data = response.getDoubleValue("data");
            logger.info("get record:{} progress: {}", recordId, data);
            return data;
        } catch (Exception e) {
            logger.error("get record:{} exception.", recordId, e);
        }
        return 0D;
    }

    /**
     * 获取任务构建详情
     *
     * @param recordId
     * @param repoURL
     * @param branch
     * @param commit
     * @return
     */
    public static JSONObject getTaskDetail(Long recordId, String repoURL, String branch, String commit) {
        logger.info("get record:{} progress repoURL:{} branch:{} commit:{}",
                recordId, repoURL, branch, commit);
        JSONObject requestBody = new JSONObject();
        requestBody.put("repo_ref", AntCodeClient.getRepoRef(repoURL));
        requestBody.put("branch", branch);
        requestBody.put("commit", commit);

        GetBuilder getBuilder = HttpClient.get(host + GET_DETAIL)
                .content(requestBody.toJSONString());

        try {
            String body = getBuilder.syncExecute(3000);
            if (StringUtils.isBlank(body)) {
                return null;
            }
            JSONObject response = JSON.parseObject(body);
            if (!response.getBooleanValue("success")) {
                logger.error("get record:{} progress failed. {}", recordId, body);
                return new JSONObject();
            }

            return response.getJSONObject("data").getJSONObject("statistics");
        } catch (Exception e) {
            logger.error("get record:{} exception.", recordId, e);
        }
        return null;
    }

    /**
     * 清理仓库
     *
     * @param recordId
     * @param repoURL
     * @param branch
     * @return
     */
    public static void cleanByProject(Long recordId, String repoURL, String branch) {
        logger.info("get record:{} progress repoURL:{} branch:{} ",
                recordId, repoURL, branch);
        JSONObject requestBody = new JSONObject();
        requestBody.put("repo_ref", AntCodeClient.getRepoRef(repoURL));
        requestBody.put("branch", branch);
        requestBody.put("scan_action", "cubesugar_pybloop_index");

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(host + CLEAN))
                    .method("DELETE", HttpRequest.BodyPublishers.ofString(JSONObject.toJSONString(requestBody)))
                    .build();
            HttpResponse<String> response = java.net.http.HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
            logger.info("cleanByProject  recordId：{} repoURL：{} branch：{} response:{}", recordId, repoURL, branch, response.body());

        } catch (Exception e) {
            logger.error("get record:{} exception.", recordId, e);
        }
    }

    /**
     * 删除索引
     * @param repoPath
     * @param branch
     * @return
     */
    public static Boolean removeIndex(String repoPath, String branch) {
        String requestUrl = host + REMOVE_INDEX;

        JSONObject requestBody = new JSONObject();
        requestBody.put("repo_ref", REPO_PREFIX + repoPath);
        requestBody.put("branch", branch);

        PostBuilder postBuilder = HttpClient.post(requestUrl)
                .content(requestBody.toJSONString())
                .header(HEADER_TRACE, TracerContextUtil.getTraceId());

        Stopwatch stopwatch = Stopwatch.createStarted();
        HttpResponse<String> httpResponse = null;
        try {
            httpResponse = postBuilder.syncExecuteWithFullResponse(DEFAULT_TIME_OUT);
            logger.info("remove index status:{} body:{}", httpResponse.statusCode(), httpResponse.body());
        } catch (HttpTimeoutException e1) {
            logger.error("remove index timeout.", e1);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_TIMEOUT);
        } catch (Exception e2) {
            logger.error("remove index found exception.", e2);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_EXCEPTION);
        } finally {
            stopwatch.stop();
            logger.info("remove index duration cost:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }

        return HttpStatus.SC_OK == httpResponse.statusCode();
    }

    /**
     * 所搜 v2 版本
     * @param repoUrl
     * @param branch
     * @param query
     * @param lexcialIndexConfig
     * @param lexcialRecallSize
     * @param embeddingIndexConfig
     * @param embeddingRecallSize
     * @param rerankName
     * @return
     */
    public static List<CodeResult> searchFileV2(String repoUrl, String branch, String query,
                                                String lexcialIndexConfig, Integer lexcialRecallSize,
                                                String embeddingIndexConfig, Integer embeddingRecallSize,
                                                String rerankName) {
        String requestUrl = host + VAT_SEARCH_V2;

        JSONObject requestBody = new JSONObject();
        requestBody.put("repo_url", repoUrl);
        requestBody.put("query", query);
        requestBody.put("branch", branch);
        requestBody.put("lexcial_index_config", lexcialIndexConfig);
        requestBody.put("lexcial_recall_size", lexcialRecallSize);
        requestBody.put("embedding_index_config", embeddingIndexConfig);
        requestBody.put("embedding_recall_size", embeddingRecallSize);
        requestBody.put("rerank_name", rerankName);

        Stopwatch stopwatch = Stopwatch.createStarted();
        PostBuilder postBuilder = HttpClient.post(requestUrl)
                .content(requestBody.toJSONString())
                .header(HEADER_TRACE, TracerContextUtil.getTraceId());

        HttpResponse<String> httpResponse = null;
        try {
            httpResponse = postBuilder.syncExecuteWithFullResponse(DEFAULT_TIME_OUT);
            logger.info("search code status:{} body:{}", httpResponse.statusCode(), httpResponse.body());
        } catch (HttpTimeoutException e1) {
            logger.error("send code search timeout.", e1);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_TIMEOUT);
        } catch (Exception e2) {
            logger.error("send code search found exception.", e2);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_EXCEPTION);
        } finally {
            stopwatch.stop();
            logger.info("search code duration cost:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }

        if (HttpStatus.SC_OK != httpResponse.statusCode()) {
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL);
        }

        List<CodeResult> codeResultList = Lists.newArrayList();
        try {
            JSONObject responseBody = JSON.parseObject(httpResponse.body());
            JSONArray codeChunkMerged = responseBody.getJSONArray("data");
            codeResultList = codeChunkMerged.toJavaList(CodeResult.class);
        } catch (Exception e) {
            logger.error("search code data ill", e);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_RESULT_ILL);
        }

        return codeResultList;
    }

    /**
     * 获取文件内容
     * @param repoPath
     * @param branch
     * @param query
     * @return
     */
    public static List<CodeResult> searchFile(String repoPath, String branch, String query) {

        String requestUrl = host + SEARCH_FILE_PATH;

        JSONObject requestBody = new JSONObject();
        requestBody.put("repo_ref", REPO_PREFIX + repoPath);
        List<ChatMessage> messages = Lists.newArrayList();
        messages.add(new ChatMessage(ChatRoleEnum.USER.getName(), query));
        requestBody.put("messages", messages);
        requestBody.put("branch", branch);
        requestBody.put("user", USER);

        Stopwatch stopwatch = Stopwatch.createStarted();
        PostBuilder postBuilder = HttpClient.post(requestUrl)
                .content(requestBody.toJSONString())
                .header(HEADER_TRACE, TracerContextUtil.getTraceId());

        HttpResponse<String> httpResponse = null;
        try {
            httpResponse = postBuilder.syncExecuteWithFullResponse(DEFAULT_TIME_OUT);
            logger.info("search code status:{} body:{}", httpResponse.statusCode(), httpResponse.body());
        } catch (HttpTimeoutException e1) {
            logger.error("send code search timeout.", e1);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_TIMEOUT);
        } catch (Exception e2) {
            logger.error("send code search found exception.", e2);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_EXCEPTION);
        } finally {
            stopwatch.stop();
            logger.info("search code duration cost:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }

        if (HttpStatus.SC_OK != httpResponse.statusCode()) {
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL);
        }

        List<CodeResult> codeResultList;
        try {
            JSONObject responseBody = JSON.parseObject(httpResponse.body());
            JSONArray codeChunkMerged = responseBody.getJSONArray("code_chunk_merged");
            codeResultList = codeChunkMerged.toJavaList(CodeResult.class);
        } catch (Exception e) {
            logger.error("search code data ill", e);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_RESULT_ILL);
        }

        return codeResultList;
    }

    /**
     * 查询指定仓库的 wiki 信息
     * @param bodyParams
     */
    public static JSONArray searchWiki(JSONObject bodyParams) {

        String requestUrl = host + SEARCH_WIKI;

        GetBuilder getBuilder = HttpClient.get(requestUrl)
                .content(bodyParams.toJSONString())
                .header(HEADER_TRACE, TracerContextUtil.getTraceId())
                .header("Content-Type", "application/json");

        try {
            String httpResponse = getBuilder.syncExecuteWithExceptionThrow(DEFAULT_TIME_OUT);
            logger.info("search wiki body:{}", httpResponse);

            JSONObject result = JSON.parseObject(httpResponse);
            if (result == null
                    || !result.getBooleanValue("success")) {
                throw new BizException(ResponseEnum.ANSWER_SEARCH_WIKI_ERROR);
            }

            return result.getJSONArray("data");
        } catch (Exception e) {
            logger.error("send wiki search exception.", e);
            throw new BizException(ResponseEnum.ANSWER_SEARCH_WIKI_ERROR);
        }
    }

    public static class WikiResult{

        private String repoUrl;

        private String repoBranch;

        private String repoCommit;

        private String repoWiki;

        public String getRepoUrl() {
            return repoUrl;
        }

        public void setRepoUrl(String repoUrl) {
            this.repoUrl = repoUrl;
        }

        public String getRepoBranch() {
            return repoBranch;
        }

        public void setRepoBranch(String repoBranch) {
            this.repoBranch = repoBranch;
        }

        public String getRepoCommit() {
            return repoCommit;
        }

        public void setRepoCommit(String repoCommit) {
            this.repoCommit = repoCommit;
        }

        public String getRepoWiki() {
            return repoWiki;
        }

        public void setRepoWiki(String repoWiki) {
            this.repoWiki = repoWiki;
        }
    }


    /**
     *
     */
    public static class CodeResult{

        private String path;

        private Integer startLine;

        private Integer endLine;

        private String snippet;

        private String content;

        private double lexcialScore;

        private double rerankScore;

        public CodeResult() {
        }

        public CodeResult(String path, String content) {
            this.path = path;
            this.content = content;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public Integer getStartLine() {
            return startLine;
        }

        public void setStartLine(Integer startLine) {
            this.startLine = startLine;
        }

        public Integer getEndLine() {
            return endLine;
        }

        public void setEndLine(Integer endLine) {
            this.endLine = endLine;
        }

        public String getSnippet() {
            return snippet;
        }

        public void setSnippet(String snippet) {
            this.snippet = snippet;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public double getLexcialScore() {
            return lexcialScore;
        }

        public void setLexcialScore(double lexcialScore) {
            this.lexcialScore = lexcialScore;
        }

        public double getRerankScore() {
            return rerankScore;
        }

        public void setRerankScore(double rerankScore) {
            this.rerankScore = rerankScore;
        }

        @Override
        public String toString() {
            return new StringJoiner(", ", CodeResult.class.getSimpleName() + "[", "]")
                    .add("path='" + path + "'")
                    .add("startLine=" + startLine)
                    .add("endLine=" + endLine)
                    .add("snippet='" + snippet + "'")
                    .add("content='" + content + "'")
                    .add("lexcialScore=" + lexcialScore)
                    .add("rerankScore=" + rerankScore)
                    .toString();
        }
    }

}
