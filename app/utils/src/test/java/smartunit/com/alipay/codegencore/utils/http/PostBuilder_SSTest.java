/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.http;

import com.alipay.codegencore.utils.http.PostBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.smartunit.shaded.org.mockito.Mockito.mock;
import static org.smartunit.shaded.org.mockito.Mockito.withSettings;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class PostBuilder_SSTest extends PostBuilder_SSTest_scaffolding {
// allCoveredLines:[40, 41, 50, 51, 64, 65, 71, 72, 73]

  @Test(timeout = 4000)
  public void test_content_0()  throws Throwable  {
      //caseID:96aa2efb4047287a14813c35c0930b3e
      //CoveredLines: [40, 41, 50, 51]
      //Input_0_String: LYR-8`01k8@e>*
      //Assert: assertSame(postBuilder0, method_result);
      
      //mock httpClient0
      java.net.http.HttpClient httpClient0 = mock(java.net.http.HttpClient.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      PostBuilder postBuilder0 = new PostBuilder("1", httpClient0);
      
      //Call method: content
      PostBuilder postBuilder1 = postBuilder0.content("LYR-8`01k8@e>*");
      
      //Test Result Assert
      assertSame(postBuilder0, postBuilder1);
  }

  @Test(timeout = 4000)
  public void test_syncExecute_1()  throws Throwable  {
      //caseID:28935bbe037b3ee453423211c35a3972
      //CoveredLines: [40, 41, 64, 65, 71, 72, 73]
      //Input_0_long: 1672
      //Assert: assertNull(method_result);
      
      //mock httpClient0
      java.net.http.HttpClient httpClient0 = mock(java.net.http.HttpClient.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      PostBuilder postBuilder0 = new PostBuilder("", httpClient0);
      
      //Call method: syncExecute
      String string0 = postBuilder0.syncExecute(1672L);
      
      //Test Result Assert
      assertNull(string0);
  }
}
