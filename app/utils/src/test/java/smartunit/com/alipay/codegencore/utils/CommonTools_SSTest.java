/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils;

import com.alipay.codegencore.model.model.CheckCodeModel;
import com.alipay.codegencore.model.model.analysis.MethodBodyModel;
import com.alipay.codegencore.model.model.analysis.TempCodeAnalysisResultContext;
import com.alipay.codegencore.utils.CommonTools;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.PrivateAccess;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import java.io.File;
import java.io.InputStream;
import java.util.*;

import static org.junit.Assert.*;
import static org.smartunit.runtime.SmartAssertions.verifyException;
import static org.smartunit.shaded.org.mockito.Mockito.*;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class CommonTools_SSTest extends CommonTools_SSTest_scaffolding {
// allCoveredLines:[26, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 70, 71, 83, 103, 104, 106, 126, 127, 129, 130, 142, 145, 146, 147, 149, 162, 163, 165, 167, 168, 169, 170, 171, 172, 179, 180, 181, 183, 199, 200, 201, 203, 212, 213, 227, 228, 231, 232, 234, 235, 236, 238, 239, 240, 241, 242, 245, 247, 249, 251, 252, 254, 255, 256, 257, 275, 276, 277, 279, 280, 281, 283, 285, 286, 288, 289, 290, 291, 292, 295, 296, 297, 298, 299, 300, 301, 302, 305, 306, 307, 309, 310, 311, 313, 314, 316, 317, 328, 329, 330, 332, 333, 345, 346, 348, 349, 351, 352, 353, 355, 356, 359, 360, 362, 375, 376, 378, 381, 382, 383, 384, 389, 391, 394, 395, 396, 397, 402, 407, 408]

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_00()  throws Throwable  {
      //caseID:98afb2f196962434f0616c0d5e802710
      //CoveredLines: [275, 276, 277]
      //Input_0_String: 
      //Input_1_String: 
      //Input_2_TempCodeAnalysisResultContext: {}
      //Assert: assertNull(method_result);
      
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("", "", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(list0);
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_01()  throws Throwable  {
      //caseID:60ccea47b586ac69a16ff17262a4ffc8
      //CoveredLines: [275, 276, 277]
      //Input_0_String: 1
      //Input_1_String: Funnels.longFunnel()
      //Input_2_TempCodeAnalysisResultContext: {}
      //Assert: assertNull(method_result);
      
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("1", "Funnels.longFunnel()", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(list0);
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_02()  throws Throwable  {
      //caseID:c56f203dbedf6194da811a76955f11e7
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 286]
      //Input_0_String: 24
      //Input_1_String: ^<cZ3sP4tTw*8p_J{}P
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0}
      //Assert: assertNull(method_result);
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("24", "^<cZ3sP4tTw*8p_J{}P", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(list0);
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_03()  throws Throwable  {
      //caseID:122f467d5424104479ce9406b3bdd58a
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 305, 306, 307, 375, 376, 378, 381, 382, 383, 389, 407, 408]
      //Input_0_String: 4`K `@f[(b:Ls[
      //Input_1_String: 3
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0}
      //Assert: assertNull(method_result);
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("4`K `@f[(b:Ls[", "3", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(list0);
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_04()  throws Throwable  {
      //caseID:b20d75309d3023c3ce4b6b0fdb02b5ec
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 305, 306, 309, 310, 311, 316, 317, 375, 376, 378, 381, 382, 383, 384, 389, 407, 408]
      //Input_0_String: (
      //Input_1_String: FRONT
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0}
      //Assert: assertNotNull(method_result);
      //Assert: assertFalse(method_result.isEmpty());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("(", "FRONT", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertFalse(list0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_05()  throws Throwable  {
      //caseID:2fd863c9e9a1f3c3ba5725ecb6a62d0d
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 305, 306, 309, 310, 313, 314, 316, 317, 375, 376, 378, 381, 382, 383, 384, 389, 407, 408]
      //Input_0_String: Funnels.sequentialFunnel(
      //Input_1_String: .
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getClassName=\"Funnels.sequentialFunnel(\"}
      //Assert: assertNotNull(method_result);
      //Assert: assertFalse(method_result.isEmpty());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("Funnels.sequentialFunnel(").when(tempCodeAnalysisResultContext0).getClassName();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("Funnels.sequentialFunnel(", ".", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertFalse(list0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_06()  throws Throwable  {
      //caseID:1e230b7df674b7286db41801f61f80af
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 290, 291, 292, 295, 296, 297, 298, 299, 300, 301, 302, 345, 346, 348, 349, 375, 376, 378, 381, 382, 383, 384, 389, 391, 394, 395, 396, 397, 402, 407, 408]
      //Input_0_String: 1
      //Input_1_String: Funnels.longFunnel()
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0}
      //Assert: assertNotNull(method_result);
      //Assert: assertEquals(1, method_result.size());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("1", "Funnels.longFunnel()", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertEquals(1, list0.size());
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_07()  throws Throwable  {
      //caseID:22ee9f529cf86063356d48897750a69f
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 290, 291, 292, 295, 296, 297, 298, 299, 300, 301, 302, 345, 346, 348, 351, 352, 355, 359, 362, 375, 376, 378, 381, 382, 383, 384, 389, 391, 394, 395, 396, 397, 402, 407, 408]
      //Input_0_String: 24
      //Input_1_String: com.alipay.codegencore.utils.CommonTools$1
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0}
      //Assert: assertNotNull(method_result);
      //Assert: assertEquals(1, method_result.size());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("24", "com.alipay.codegencore.utils.CommonTools$1", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertEquals(1, list0.size());
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_08()  throws Throwable  {
      //caseID:b35e20d46c410ccf337bd26c989da29a
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 290, 291, 292, 295, 296, 297, 298, 299, 300, 301, 302, 345, 346, 348, 351, 352, 353, 355, 356, 359, 362, 375, 376, 378, 381, 382, 383, 384, 389, 391, 394, 395, 396, 397, 402, 407, 408]
      //Input_0_String: 24
      //Input_1_String: c#iPdEG#a9cBC.jNYt9
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getFieldReferenceMap=map1, getLocalVariableMap=map0}
      //Assert: assertNotNull(method_result);
      //Assert: assertFalse(method_result.isEmpty());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock map1
      Map<String, String> map1 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map1).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(map0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("24", "c#iPdEG#a9cBC.jNYt9", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertFalse(list0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_09()  throws Throwable  {
      //caseID:f1938187e1ed055be95d76ae06e9faac
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 290, 291, 292, 295, 296, 297, 298, 299, 300, 301, 302, 345, 346, 348, 351, 352, 353, 355, 356, 359, 362, 375, 376, 378, 381, 382, 383, 384, 389, 391, 394, 395, 396, 397, 402, 407, 408]
      //Input_0_String: 24
      //Input_1_String: com.alipay.codegencore.utils.CommonTools$1
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getFieldReferenceMap=map1, getLocalVariableMap=map0}
      //Assert: assertNotNull(method_result);
      //Assert: assertFalse(method_result.isEmpty());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock map1
      Map<String, String> map1 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map1).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(map0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("24", "com.alipay.codegencore.utils.CommonTools$1", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertFalse(list0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_10()  throws Throwable  {
      //caseID:48803980f295943268b03a379389396f
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 290, 291, 292, 295, 296, 297, 298, 299, 300, 301, 302, 345, 346, 348, 351, 352, 353, 355, 359, 360, 362, 375, 376, 378, 381, 382, 383, 384, 389, 391, 394, 395, 396, 397, 402, 407, 408]
      //Input_0_String: 22
      //Input_1_String: com.alipay.codegencore.utils.CommonTools
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getFieldReferenceMap=map1, getLocalVariableMap=map0}
      //Assert: assertNotNull(method_result);
      //Assert: assertEquals(1, method_result.size());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("FRONT").when(map0).get(any());
      //mock map1
      Map<String, String> map1 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map1).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(map0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("22", "com.alipay.codegencore.utils.CommonTools", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertEquals(1, list0.size());
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_11()  throws Throwable  {
      //caseID:18758a2ed6c0cf82b2e744a2bf5dbb56
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 290, 291, 292, 295, 296, 297, 298, 299, 300, 301, 302, 345, 346, 348, 351, 352, 353, 355, 356, 359, 360, 362, 375, 376, 378, 381, 382, 383, 384, 389, 391, 394, 395, 396, 397, 402, 407, 408]
      //Input_0_String: com.google.common.base.Converter$FunctionBasedConverter
      //Input_1_String: 5
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getFieldReferenceMap=map1, getLocalVariableMap=map0}
      //Assert: assertNotNull(method_result);
      //Assert: assertEquals(1, method_result.size());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((String) null).when(map0).get(any());
      //mock map1
      Map<String, String> map1 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("com.alipay.codegencore.utils.CommonTools$1").when(map1).get(any());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map1).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(map0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("com.google.common.base.Converter$FunctionBasedConverter", "5", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertEquals(1, list0.size());
  }

  @Test(timeout = 4000)
  public void test_analysisCheckModelList_12()  throws Throwable  {
      //caseID:4205443bda7523bdb850927f5f93a2ac
      //CoveredLines: [275, 276, 279, 280, 281, 283, 285, 288, 289, 290, 291, 292, 295, 296, 297, 298, 299, 300, 301, 302, 345, 346, 348, 351, 352, 353, 355, 356, 359, 360, 362, 375, 376, 378, 381, 382, 383, 384, 389, 391, 394, 395, 396, 397, 402, 407, 408]
      //Input_0_String: Funnels.sequentialFunnel(
      //Input_1_String: The array of suffixes must not be null
      //Input_2_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getFieldReferenceMap=map0, getLocalVariableMap=hashMap0}
      //Assert: assertNotNull(method_result);
      //Assert: assertEquals(1, method_result.size());
      
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("Funnels.sequentialFunnel(").when(map0).get(any());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map0).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(hashMap0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: analysisCheckModelList
      List<CheckCodeModel> list0 = CommonTools.analysisCheckModelList("Funnels.sequentialFunnel(", "The array of suffixes must not be null", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(list0);
      
      //Test Result Assert
      assertEquals(1, list0.size());
  }

  @Test(timeout = 4000)
  public void test_checkCode_13()  throws Throwable  {
      //caseID:8688058a9cde49140cd65ccff4a2611d
      //CoveredLines: [328, 329, 330]
      //Input_0_List<CheckCodeModel>: stack0
      //Assert: assertFalse(method_result);
      
      Stack<CheckCodeModel> stack0 = new Stack<CheckCodeModel>();
      //mock checkCodeModel0
      CheckCodeModel checkCodeModel0 = mock(CheckCodeModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("-1").when(checkCodeModel0).getReferenceName();
      
      stack0.add(checkCodeModel0);
      
      //Call method: checkCode
      boolean boolean0 = CommonTools.checkCode(stack0);
      
      //Test Result Assert
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test_checkCode_14()  throws Throwable  {
      //caseID:a03a95f6aee1de6a0937cf09b365484e
      //CoveredLines: [328, 329, 332, 333]
      //Input_0_List<CheckCodeModel>: stack0
      //Assert: assertTrue(method_result);
      
      Stack<CheckCodeModel> stack0 = new Stack<CheckCodeModel>();
      //mock checkCodeModel0
      CheckCodeModel checkCodeModel0 = mock(CheckCodeModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("0").when(checkCodeModel0).getClassName();
      doReturn("zReN`mdetNf_!Ub").when(checkCodeModel0).getReferenceName();
      
      stack0.add(checkCodeModel0);
      
      //Call method: checkCode
      boolean boolean0 = CommonTools.checkCode(stack0);
      
      //Test Result Assert
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test_checkCode_15()  throws Throwable  {
      //caseID:a5a47c174bad81fd5a1101c41b3ec39a
      //CoveredLines: [328, 329, 332, 333]
      //Input_0_List<CheckCodeModel>: arrayList0
      //Assert: assertTrue(method_result);
      
      ArrayList<CheckCodeModel> arrayList0 = new ArrayList<CheckCodeModel>();
      //mock checkCodeModel0
      CheckCodeModel checkCodeModel0 = mock(CheckCodeModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      arrayList0.add(checkCodeModel0);
      
      //Call method: checkCode
      boolean boolean0 = CommonTools.checkCode(arrayList0);
      
      //Test Result Assert
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test_divert_16()  throws Throwable  {
      //caseID:c9ee44abaae8febffa9e65c38970e5fc
      //CoveredLines: [70, 71, 212, 213, 227, 228, 231, 232, 234, 245, 254, 255, 256, 257]
      //Input_0_String: 
      //Input_1_int: 0
      
      
      //Call method: divert
      // Undeclared exception!
      try { 
        CommonTools.divert("", 0);
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.utils.CommonTools", e);
         assertEquals("java.lang.ArithmeticException", e.getClass().getName());
         assertEquals("/ by zero", e.getMessage());
      }
  }

  @Test(timeout = 4000)
  public void test_divert_17()  throws Throwable  {
      //caseID:8f94d6bad658e0f6ca02dffe59875d49
      //CoveredLines: [70, 71, 212, 213, 227, 228, 231, 232, 234, 245, 251, 252, 254, 255, 256, 257]
      //Input_0_String: 8
      //Input_1_int: 0
      
      
      //Call method: divert
      // Undeclared exception!
      try { 
        CommonTools.divert("8", 0);
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.utils.CommonTools", e);
         assertEquals("java.lang.ArithmeticException", e.getClass().getName());
         assertEquals("/ by zero", e.getMessage());
      }
  }

  @Test(timeout = 4000)
  public void test_divert_18()  throws Throwable  {
      //caseID:9583c00c25da338566c6eafdd980735d
      //CoveredLines: [26, 70, 71, 212, 213, 227, 228, 231, 232, 234, 235, 236, 238, 239, 240, 241, 242, 245, 249, 251, 252, 254, 255, 256, 257]
      //Input_0_String: DiscreteDomain.longs()
      //Input_1_int: 14
      //Assert: assertEquals(0, method_result);
      
      CommonTools commonTools0 = new CommonTools();
      
      //Call method: divert
      int int0 = CommonTools.divert("DiscreteDomain.longs()", 14);
      
      //Test Result Assert
      assertEquals(12, int0);
  }

  @Test(timeout = 4000)
  public void test_divert_19()  throws Throwable  {
      //caseID:bbb3e06feeb303ef16c67b8274eac6b5
      //CoveredLines: [70, 71, 212, 213, 227, 228, 231, 232, 234, 235, 236, 238, 239, 240, 241, 242, 245, 247, 249, 251, 252, 254, 255, 256, 257]
      //Input_0_String: org.apache.commons.io.file.StandardDeleteOption
      //Input_1_int: 128
      //Assert: assertEquals(48, method_result);
      
      
      //Call method: divert
      int int0 = CommonTools.divert("org.apache.commons.io.file.StandardDeleteOption", 128);
      
      //Test Result Assert
      assertEquals(44, int0);
  }

  @Test(timeout = 4000)
  public void test_downloadFile_20()  throws Throwable  {
      //caseID:75ac9861464a351fda32b1842e2ac80f
      //CoveredLines: [83]
      //Input_0_String: 1024
      //Input_1_String: Functions.forSupplier(
      
      
      //Call method: downloadFile
      try { 
        CommonTools.downloadFile("1024", "Functions.forSupplier(");
      } catch(Throwable e) {
         verifyException("java.net.URL", e);
         assertEquals("java.net.MalformedURLException", e.getClass().getName());
         assertEquals("no protocol: 1024", e.getMessage());
      }
  }

  @Test(timeout = 4000)
  public void test_getClassNameFromCodeContext_21()  throws Throwable  {
      //caseID:edc133d187ef38f294598fc6ab30ec2f
      //CoveredLines: [26, 345, 346, 348, 351, 352, 353, 355, 359, 360, 362]
      //Input_0_String: 
      //Input_1_com.alipay.codegencore.model.model.analysis.TempCodeAnalysisResultContext: {getFieldReferenceMap=map1, getLocalVariableMap=map0}
      //Assert: assertEquals("12", method_result);
      //Assert: assertNotNull(method_result);
      
      CommonTools commonTools0 = new CommonTools();
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("12").when(map0).get(any());
      //mock map1
      Map<String, String> map1 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map1).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(map0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      
      //Call method: getClassNameFromCodeContext
      Object object0 = PrivateAccess.callMethod((Class<CommonTools>) CommonTools.class, commonTools0, "getClassNameFromCodeContext", (Object) "", (Class<?>) String.class, (Object) tempCodeAnalysisResultContext0, (Class<?>) TempCodeAnalysisResultContext.class);
      
      //Test Result Assert
      assertEquals("12", object0);
      
      //Test Result Assert
      assertNotNull(object0);
  }

  @Test(timeout = 4000)
  public void test_getDisplayName_22()  throws Throwable  {
      //caseID:848d231cf8ff1900da8db1ece1175651
      //CoveredLines: [162, 163]
      //Input_0_String: 2
      //Input_1_String: null
      //Input_2_int: 1955
      //Assert: assertNull(method_result);
      
      
      //Call method: getDisplayName
      String string0 = CommonTools.getDisplayName("2", (String) null, 1955);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_getDisplayName_23()  throws Throwable  {
      //caseID:129dab992939cf5d0e93aaef60b882f9
      //CoveredLines: [162, 163]
      //Input_0_String: <([^<>]*)>*
      //Input_1_String: 
      //Input_2_int: 0
      //Assert: assertEquals("", method_result);
      
      
      //Call method: getDisplayName
      String string0 = CommonTools.getDisplayName("<([^<>]*)>*", "", 0);
      
      //Test Result Assert
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test_getDisplayName_24()  throws Throwable  {
      //caseID:5516e046509fb8711f5bdfd6e0630d39
      //CoveredLines: [162, 165, 167, 179, 180, 181]
      //Input_0_String: null
      //Input_1_String: \u6587\u4EF6md5\u6807\u8BC6\u83B7\u53D6\u5931\u8D25
      //Input_2_int: 99
      //Assert: assertNotNull(method_result);
      //Assert: assertEquals("\u6587\u4EF6md5\u6807\u8BC6\u83B7\u53D6\u5931\u8D25", method_result);
      
      
      //Call method: getDisplayName
      String string0 = CommonTools.getDisplayName((String) null, "\u6587\u4EF6md5\u6807\u8BC6\u83B7\u53D6\u5931\u8D25", 99);
      
      //Test Result Assert
      assertNotNull(string0);
      
      //Test Result Assert
      assertEquals("\u6587\u4EF6md5\u6807\u8BC6\u83B7\u53D6\u5931\u8D25", string0);
  }

  @Test(timeout = 4000)
  public void test_getDisplayName_25()  throws Throwable  {
      //caseID:5cb0cf5306e6bf73355e952126cd5d72
      //CoveredLines: [162, 165, 167, 179, 180, 181]
      //Input_0_String: 
      //Input_1_String: D2w%syP\"%T/
      //Input_2_int: 2783
      //Assert: assertEquals("D2w%syP\"%T/", method_result);
      
      
      //Call method: getDisplayName
      String string0 = CommonTools.getDisplayName("", "D2w%syP\"%T/", 2783);
      
      //Test Result Assert
      assertEquals("D2w%syP\"%T/", string0);
  }

  @Test(timeout = 4000)
  public void test_getDisplayName_26()  throws Throwable  {
      //caseID:e122ebb306da3d65e149cbe6a61fd2ba
      //CoveredLines: [162, 165, 167, 168, 169, 170, 171, 172, 179, 180, 183]
      //Input_0_String: P72k5_Kh[`NO(@~!>6
      //Input_1_String: 1.0
      //Input_2_int: 4
      //Assert: assertEquals("61.0...", method_result);
      
      
      //Call method: getDisplayName
      String string0 = CommonTools.getDisplayName("P72k5_Kh[`NO(@~!>6", "1.0", 4);
      
      //Test Result Assert
      assertEquals("61.0...", string0);
  }

  @Test(timeout = 4000)
  public void test_getDisplayName_27()  throws Throwable  {
      //caseID:bb4e9f87e8076fe2ab48690d07cd1c25
      //CoveredLines: [162, 165, 167, 168, 169, 170, 171, 172, 179, 180, 183]
      //Input_0_String: 7
      //Input_1_String:  TB
      //Input_2_int: 0
      //Assert: assertEquals("...", method_result);
      
      
      //Call method: getDisplayName
      String string0 = CommonTools.getDisplayName("7", " TB", 0);
      
      //Test Result Assert
      assertEquals("...", string0);
  }

  @Test(timeout = 4000)
  public void test_getDisplayName_28()  throws Throwable  {
      //caseID:9b1f3aa6949006de4697d85b194607db
      //CoveredLines: [162, 165, 167, 168, 169, 170, 171, 172, 179, 180, 181]
      //Input_0_String: initialList
      //Input_1_String: 240
      //Input_2_int: 50
      //Assert: assertEquals("initialList240", method_result);
      
      
      //Call method: getDisplayName
      String string0 = CommonTools.getDisplayName("initialList", "240", 50);
      
      //Test Result Assert
      assertEquals("initialList240", string0);
  }

  @Test(timeout = 4000)
  public void test_getDisplayName_29()  throws Throwable  {
      //caseID:9355cb664d6d09a8e8dbfdc4c2a1b760
      //CoveredLines: [162, 165, 167, 168, 169, 170, 171, 172, 179, 180, 183]
      //Input_0_String: BACK
      //Input_1_String: com.google.common.collect.FilteredEntryMultimap$ValuePredicate
      //Input_2_int: 4
      //Assert: assertEquals("BACK...", method_result);
      
      
      //Call method: getDisplayName
      String string0 = CommonTools.getDisplayName("BACK", "com.google.common.collect.FilteredEntryMultimap$ValuePredicate", 4);
      
      //Test Result Assert
      assertEquals("BACK...", string0);
  }

  @Test(timeout = 4000)
  public void test_isAvaliableFielName_30()  throws Throwable  {
      //caseID:21541904a117d287144839b5893f73bc
      //CoveredLines: [26, 142, 145, 146, 147]
      //Input_0_String: O+kC}>L9
      //Assert: assertTrue(method_result);
      
      CommonTools commonTools0 = new CommonTools();
      
      //Call method: isAvaliableFielName
      boolean boolean0 = (boolean)PrivateAccess.callMethod((Class<CommonTools>) CommonTools.class, commonTools0, "isAvaliableFielName", (Object) "O+kC}>L9", (Class<?>) String.class);
      
      //Test Result Assert
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test_isAvaliableFielName_31()  throws Throwable  {
      //caseID:619a57f500ae095d44de8cb98d4a5c52
      //CoveredLines: [26, 142, 145, 146, 149]
      //Input_0_String: \u2002\u3000\r\u0085\u200A\u2005\u2000\u3000\u2029\u000B\u3000\u2008\u2003\u205F\u3000\u1680\t \u2006\u2001\u202F\u00A0\f\u2009\u3000\u2004\u3000\u3000\u2028\n\u2007\u3000
      //Assert: assertFalse(method_result);
      
      CommonTools commonTools0 = new CommonTools();
      
      //Call method: isAvaliableFielName
      boolean boolean0 = (boolean)PrivateAccess.callMethod((Class<CommonTools>) CommonTools.class, commonTools0, "isAvaliableFielName", (Object) "\u2002\u3000\r\u0085\u200A\u2005\u2000\u3000\u2029\u000B\u3000\u2008\u2003\u205F\u3000\u1680\t \u2006\u2001\u202F\u00A0\f\u2009\u3000\u2004\u3000\u3000\u2028\n\u2007\u3000", (Class<?>) String.class);
      
      //Test Result Assert
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test_isAvaliableFielName_32()  throws Throwable  {
      //caseID:7850a3e08efa9f259027e522fdac239a
      //CoveredLines: [26, 142, 145, 146, 147]
      //Input_0_String: uH~6
      //Assert: assertTrue(method_result);
      
      CommonTools commonTools0 = new CommonTools();
      
      //Call method: isAvaliableFielName
      boolean boolean0 = (boolean)PrivateAccess.callMethod((Class<CommonTools>) CommonTools.class, commonTools0, "isAvaliableFielName", (Object) "uH~6", (Class<?>) String.class);
      
      //Test Result Assert
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test_isAvaliableFielName_33()  throws Throwable  {
      //caseID:a63db1f968e38f7be66ca20a886da2b7
      //CoveredLines: [26, 142, 145, 146, 147]
      //Input_0_String: 7<vWnV)7$Q|>FY]j
      //Assert: assertTrue(method_result);
      
      CommonTools commonTools0 = new CommonTools();
      
      //Call method: isAvaliableFielName
      boolean boolean0 = (boolean)PrivateAccess.callMethod((Class<CommonTools>) CommonTools.class, commonTools0, "isAvaliableFielName", (Object) "7<vWnV)7$Q|>FY]j", (Class<?>) String.class);
      
      //Test Result Assert
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test_sha512Hex_34()  throws Throwable  {
      //caseID:7816fccd803d1b8f4ad1e8ea1177db17
      //CoveredLines: [46, 47, 48, 49, 56, 57]
      //Input_0_InputStream: {read=1073741824}
      
      //mock inputStream0
      InputStream inputStream0 = mock(InputStream.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(1073741824).when(inputStream0).read(any(byte[].class) , anyInt() , anyInt());
      
      //Call method: sha512Hex
      // Undeclared exception!
      try { 
        CommonTools.sha512Hex(inputStream0);
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.utils.CommonTools", e);
         assertEquals("org.smartunit.runtime.mock.java.lang.MockIllegalArgumentException", e.getClass().getName());
         assertEquals("\u6587\u4EF6md5\u6807\u8BC6\u83B7\u53D6\u5931\u8D25", e.getMessage());
      }
  }

  @Test(timeout = 4000)
  public void test_sha512Hex_35()  throws Throwable  {
      //caseID:51c722f959f42535d5c5bf82c3f44031
      //CoveredLines: [46, 47, 48, 49, 51, 52, 53, 54, 55, 199, 200, 201, 203]
      //Input_0_InputStream: {read=14 24 57 58 (-1)}
      //Assert: assertEquals("58e6b66d1e6a9858e3b2ff1c90333d804d80a98dad358bb666b0332013c0c0c7444d9cb7297eff3aeee7de66d01b3b180629f1b5258af19165abd5e013574b46", method_result);
      
      //mock inputStream0
      InputStream inputStream0 = mock(InputStream.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(14, 24, 57, 58, (-1)).when(inputStream0).read(any(byte[].class) , anyInt() , anyInt());
      
      //Call method: sha512Hex
      String string0 = CommonTools.sha512Hex(inputStream0);
      
      //Test Result Assert
      assertEquals("58e6b66d1e6a9858e3b2ff1c90333d804d80a98dad358bb666b0332013c0c0c7444d9cb7297eff3aeee7de66d01b3b180629f1b5258af19165abd5e013574b46", string0);
  }

  @Test(timeout = 4000)
  public void test_unzip_36()  throws Throwable  {
      //caseID:8d167cff427350b005877821e48ba92e
      //CoveredLines: [103, 104, 106, 126, 127, 129, 130]
      //Input_0_File: {}
      //Input_1_String: 52
      
      //mock file0
      File file0 = mock(File.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: unzip
      // Undeclared exception!
      try { 
        CommonTools.unzip(file0, "52");
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.utils.CommonTools", e);
         assertEquals("com.alipay.codegencore.model.exception.BizException", e.getClass().getName());
      }
  }
}
