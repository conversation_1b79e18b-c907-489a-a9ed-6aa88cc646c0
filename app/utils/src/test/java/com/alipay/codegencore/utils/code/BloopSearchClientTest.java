package com.alipay.codegencore.utils.code;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.List;

class BloopSearchClientTest {

    @Test
    void getTaskProgress() {

        Long recordId = 1316620224L;
        String repoURL = "http://gitlab.alipay-inc.com/common_release/quicksofastart.git";
        String branch = "dev/quicksofastart-1726736138203";
        String commit = "64c07355a02fe75eb12417517d3740f026898ae6";
        double progress = BloopSearchClient.getTaskProgress(recordId, repoURL, branch, commit);
        System.out.println(progress);
    }

    @Test
    void searchFileContainsSnippet() {
        AntCodeClient.init("https://code.alipay.com", "");
        List<BloopSearchClient.CodeResult> codeResultList = BloopSearchClient.searchFile(
                "common_release/antmobilecloudtest", "master", "如何判断测试任务应该使用那些设备的");
        Assert.assertNotNull(codeResultList);
    }

    @Test
    void searchFile() {
    }

    @Test
    void searchWiki() {

        BloopSearchClient.init("https://antnluservice.alipay.com");
        JSONObject body = new JSONObject();
        body.put("repo_url", "https://code.alipay.com/dqts/dqtsvr-bigfishweb.git");
        body.put("repo_branch", "master");
        body.put("short_version", true);
        JSONArray wikiResults = BloopSearchClient.searchWiki(body);
        Assert.assertNotNull(wikiResults);
    }


    @Test
    void searchFileV2() {
        AntCodeClient.init("https://code.alipay.com", "drrOuBhke-eFkPM3qF6T");

        String repoUrl = "https://code.alipay.com/common_release/trustiot";
        String branch = "vat_eval_c84eb104";
        String query = "在SceneVO.java中新增对sceneDTO.getFromTypeEnum()的判断，将枚举值转换为对应的值并赋给sceneVO的fromType属性，以支持新的数据类型转换功能；在ReGeoBatchConsumer.java中新增对reGeoResultBase.getRegeocodes()的null检查和大小一致性检查，优化异常情况下的任务状态处理，以优化异常处理逻辑。";
        String lexcialIndexConfig = "mm_chunk_level_codesage_512_path";
        Integer lexcialRecallSize = 20;
        String embeddingIndexConfig = "mm_chunk_level_codesage_512_path";
        Integer embeddingRecallSize = 20;
        String rerankName = "JINA_RERANKER_V2_BASE_MULTI";
        List<BloopSearchClient.CodeResult> codeResultList = BloopSearchClient.searchFileV2(repoUrl, branch,
                query, lexcialIndexConfig, lexcialRecallSize, embeddingIndexConfig, embeddingRecallSize, rerankName);
        Assert.assertNotNull(codeResultList);
    }
}