package com.alipay.codegencore.utils;

import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.StreamDataListener;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Flow;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * 创建时间 2023-01-10
 */
public class CommonToolsTest {

    @Test
    public void test1(){
        int abc = CommonTools.divert("abc", 30);
        System.out.println(abc);
    }

    @Test
    public void test2() throws Throwable{

        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        chatCompletionRequest.setStream(true);
        chatCompletionRequest.setModel("gpt-3.5-turbo");
        List<ChatMessage> list  =new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole("system");
        chatMessage.setContent("use chinese to chat");

        ChatMessage chatMessage1 = new ChatMessage();
        chatMessage1.setRole("user");
        chatMessage1.setContent("苹果公司是做什么的");
        list.add(chatMessage);
        list.add(chatMessage1);
        chatCompletionRequest.setMessages(list);
        HttpClient.post("https://www.baidu.com").content("test").streamExecute(5000, new StreamDataListener() {
            @Override
            public void onConnect(Flow.Subscription subscription) {
                System.out.println("连接正常");
            }

            @Override
            public void eachData(String data, Flow.Subscription subscription) {
                System.out.println("数据" + data);
            }

            @Override
            public void onError(Throwable throwable) {
                System.out.println("连接异常");
            }

            @Override
            public void onComplete() {
                System.out.println("连接完成");
            }
        }, (integer, s) -> {
            System.out.println("状态码" + integer);
            System.out.println("返回值" + s);
        });

        HttpClient.post("https://www.123213.com").content("test").streamExecute(5000, new StreamDataListener() {
            @Override
            public void onConnect(Flow.Subscription subscription) {
                System.out.println("连接正常");
            }

            @Override
            public void eachData(String data, Flow.Subscription subscription) {
                System.out.println("数据" + data);
            }

            @Override
            public void onError(Throwable throwable) {
                System.out.println("连接异常");
            }

            @Override
            public void onComplete() {
                System.out.println("连接完成");
            }
        }, (integer, s) -> {
            System.out.println("状态码" + integer);
            System.out.println("返回值" + s);
        });
    }

    @Test
    public void test_getEmpIdAdd0(){
        String s1 = CommonTools.getEmpIdAdd0("48077");
        String s2 = CommonTools.getEmpIdAdd0("348077");
        Assert.assertEquals(6, s1.length());
        Assert.assertEquals(6, s2.length());
    }

    @Test
    public void test_number2Chinese(){
        String s1=CommonTools.number2Chinese(1230);
        Assert.assertEquals(s1,"一千二百三十");
        String s2=CommonTools.number2Chinese(0);
        Assert.assertEquals(s2, "零");
        String s3 = CommonTools.number2Chinese(10);
        Assert.assertEquals(s3, "十");
        String s4 = CommonTools.number2Chinese(100);
        Assert.assertEquals(s4,"一百");
    }

    @Test
    public void test_writeResponse() {
        try{
            CommonTools.writeResponse(null, 400, null, ResponseEnum.ILLEGAL_PARAMETER, new Exception("onlyDecide can not be true when stream is true"));
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    @Test
    public void test_writeResponse_function(){
        try{
            CommonTools.writeResponse("#function{\"content\": null, \"name\": \"get_current_weather\", \"arguments\": \"{\\n  \\\"location\\\": \\\"Beijing\\\"\\n}\"}", 400, null, ResponseEnum.ILLEGAL_PARAMETER, new Exception("onlyDecide can not be true when stream is true"));
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Test
    public void test_writeResponse_chatMessage(){
        try{
            ChatMessage chatMessage = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), null, null, new ChatFunctionCall("query_weather", "{\\\"location\\\":\\\"Beijing\\\"}"));
            CommonTools.writeResponse(chatMessage, 400, null, ResponseEnum.ILLEGAL_PARAMETER, new Exception("onlyDecide can not be true when stream is true"));
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * [单测用例]测试场景：输入为null
     */
    @Test
    public void testToCaseInsensitiveRegexWithNullInput() {
        String input = null;
        String result = CommonTools.toCaseInsensitiveRegex(input);
        Assert.assertEquals("", result);
    }

    /**
     * [单测用例]测试场景：输入为空字符串
     */
    @Test
    public void testToCaseInsensitiveRegexWithEmptyInput() {
        String input = "";
        String result = CommonTools.toCaseInsensitiveRegex(input);
        Assert.assertEquals("", result);
    }

    /**
     * [单测用例]测试场景：输入为纯字母字符串
     */
    @Test
    public void testToCaseInsensitiveRegexWithAlphabeticInput() {
        String input = "HelloWorld";
        String result = CommonTools.toCaseInsensitiveRegex(input);
        Assert.assertEquals("[hH][eE][lL][lL][oO][wW][oO][rR][lL][dD]", result);
    }

    /**
     * [单测用例]测试场景：输入为纯数字字符串
     */
    @Test
    public void testToCaseInsensitiveRegexWithNumericInput() {
        String input = "123456";
        String result = CommonTools.toCaseInsensitiveRegex(input);
        Assert.assertEquals("123456", result);
    }

    /**
     * [单测用例]测试场景：输入为包含特殊字符的字符串
     */
    @Test
    public void testToCaseInsensitiveRegexWithSpecialCharacterInput() {
        String input = "Hello_World!";
        String result = CommonTools.toCaseInsensitiveRegex(input);
        Assert.assertEquals("[hH][eE][lL][lL][oO]_[wW][oO][rR][lL][dD]!", result);
    }
}
