package com.alipay.codegencore.utils.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.codegencore.model.copilot.CodeSymbol;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;

import java.util.List;

public class AntCodeClientTest {

    @Before
    public void init() {
        AntCodeClient.init("https://code.alipay.com", "");
    }

//    @Test
    public void getProjectLastCommitInfo() {
        String repoURL = "https://code.alipay.com/common_release/amtaigcgateway.git";
        AntCodeClient.getProjectLastCommitInfo("common_release", "amtaigcgateway", null);

        repoURL = "https://code.alipay.com/common_release/amtaigcgatewa.git";
        AntCodeClient.getProjectLastCommitInfo("common_release", "amtaigcgateway", null);
    }

//    @Test
    public void getGroupAndProjectPathByRepoUrl() {
        String repoURL = null;
        Pair<String, String> pair = AntCodeClient.getGroupAndProjectPathByRepoUrl(repoURL);
        Assert.assertNull(pair);

        repoURL = "*******************:common_release/amtaigcgateway.git";
        pair = AntCodeClient.getGroupAndProjectPathByRepoUrl(repoURL);
        Assert.assertNotNull(pair);
        Assert.assertEquals("common_release", pair.getLeft());
        Assert.assertEquals("amtaigcgateway", pair.getRight());

        repoURL = "https://code.alipay.com/common_release/amtaigcgateway.git";
        pair = AntCodeClient.getGroupAndProjectPathByRepoUrl(repoURL);
        Assert.assertNotNull(pair);
        Assert.assertEquals("common_release", pair.getLeft());
        Assert.assertEquals("amtaigcgateway", pair.getRight());

        repoURL = "file://code.alipay.com/common_release/amtaigcgateway.git";
        pair = AntCodeClient.getGroupAndProjectPathByRepoUrl(repoURL);
        Assert.assertNull(pair);

        repoURL = "https://amtaigcgateway.git";
        pair = AntCodeClient.getGroupAndProjectPathByRepoUrl(repoURL);
        Assert.assertNull(pair);
    }

//    @Test
    public void buildProjectId() {
        String projectId = AntCodeClient.buildProjectId("amtaigcgateway", "amtaigcgateway");
        Assert.assertEquals("amtaigcgateway%2Famtaigcgateway", projectId);
    }

//    @Test
    public void getFileListByPojectExceptino() {

        String groupPath = "common_release";
        String projectPath = "codegencore";
        String branch = "master";
        List<AntCodeClient.FileCompare> fileList = AntCodeClient.getFileListByProject(groupPath, projectPath, branch);
        Assert.assertNotEquals(fileList.size(), 0);

        projectPath = "amtaigcgateway";
        fileList = AntCodeClient.getFileListByProject(groupPath, projectPath, branch);
        Assert.assertNotEquals(fileList.size(), 0);
    }

//    @Test
    public void getFileComparesBetweenTwoCommit() {

        String groupPath = "security_release";
        String projectPath = "alphaseccompasslib";
        String fromCommitId = "44aaaeebb7cb09893de8646a79f5f3b8b750fbf5";
        String toCommitId = "344c60df525d3b5ab3a5c9abc18807e80163dfb9";

        List<AntCodeClient.FileCompare> fileCompareList =
                AntCodeClient.getFileComparesBetweenTwoCommit(groupPath,
                        projectPath, fromCommitId, toCommitId);
        Assert.assertNotEquals(fileCompareList.size(), 0);

        fromCommitId = "2ac21b175e325814d4cd01bfca3aeccaabf";
        fileCompareList =
                AntCodeClient.getFileComparesBetweenTwoCommit(groupPath,
                        projectPath, fromCommitId, toCommitId);
        Assert.assertEquals(fileCompareList.size(), 0);


        fromCommitId = null;
        fileCompareList =
                AntCodeClient.getFileComparesBetweenTwoCommit(groupPath,
                        projectPath, fromCommitId, toCommitId);
        Assert.assertEquals(fileCompareList.size(), 0);

    }

//    @Test
    public void testSearchAntcodeGroup(){
        String query = "wallet";
        List<AntCodeClient.RepoGroupInfo> antCodeGroups = AntCodeClient.searchAntcodeGroup(query);
        Assert.assertNotEquals(antCodeGroups.size(), 0);

        String shortName = "ope";
        antCodeGroups = AntCodeClient.searchAntcodeGroup(shortName);
        Assert.assertNotEquals(antCodeGroups.size(), 0);
    }

//    @Test
    public void testSearchAntcodeRepo(){
        String query = "wallet";
        String repoGroup = "wallet";
        List<String> antCodeRepos = AntCodeClient.searchAntcodeRepo(repoGroup, query, 50);
        Assert.assertNotEquals(antCodeRepos.size(), 0);

        String shortName = "wal";
        antCodeRepos = AntCodeClient.searchAntcodeRepo(repoGroup, shortName, 50);
        Assert.assertNotEquals(antCodeRepos.size(), 0);
    }


//    @Test
    public void testGetBranchList(){
        String repoGroup = "common_release";
        String repoName = "codegencore";
        String keyword = "yunchen";
        List<String> branchList = AntCodeClient.getBranchList(repoGroup, repoName, keyword, 50);
        Assert.assertNotEquals(branchList.size(), 0);

        String emptyKeyword = "";
        branchList = AntCodeClient.getBranchList(repoGroup, repoName, emptyKeyword, 50);
         Assert.assertNotEquals(branchList.size(), 0);
    }

//    @Test
    public void defaultIfBlank() {
        String b = AntCodeClient.defaultIfBlank("test");
        Assert.assertEquals(b, "test");
    }

//    @Test
    public void buildRepoURL() {
        String url = AntCodeClient.buildRepoURL("common_release", "amtaigcgateway");
        Assert.assertEquals(url, "https://code.alipay.com/common_release/amtaigcgateway.git");
    }

//    @Test
    public void getFileContent() {

        String content = AntCodeClient.getFileContent("common_release/hrworkprod", "vat_eval_8216a3f0", "app/service/workbench/src/main/java/com/alipay/hrworkprod/service/workbench/common/enums/DiamondKey.java");
        Assert.assertNotNull(content);

    }

//    @Test
    public void testFileSearch(){
        List<String> searchFilePaths = AntCodeClient.fileSearch("common_release/codegencore", "master", "ChatController", 20);
        System.out.println(searchFilePaths);

        Assert.assertTrue(CollectionUtils.isNotEmpty(searchFilePaths));
    }

//    @Test
    public void testSymbolSearch(){
        String cookie = "xxx";
        List<CodeSymbol> codeSymbols = AntCodeClient.symbolSearch(cookie,"common_release/codegencore", "master", "maya", false, 20);
        System.out.println(JSON.toJSONString(codeSymbols, SerializerFeature.PrettyFormat));
    }

//    @Test
    public void testMemberAccessLevel() {
        String workNo = "193163";
        String group = "common_release";
        String project = "codefusesearch";
        String level = AntCodeClient.memberAccessLevel(workNo, group, project);
        Assert.assertEquals(level, "30");
    }

//    @Test
    public void testMemberAccessLevel1() {
        String workNo = "193163";
        String group = "common_release";
        String project = "grootx";
        String level = AntCodeClient.memberAccessLevel(workNo, group, project);
        Assert.assertEquals(level, "40");
    }

//    @Test
    public void testMemberAccessLevel2() {
        String workNo = "193163";
        String group = "common_release";
        String project = "zdfmng";
        String level = AntCodeClient.memberAccessLevel(workNo, group, project);
        Assert.assertEquals(level, "0");
    }

//    @Test
    public void branchCheck() {
        String groupPath = "common_release";
        String projectPath = "grootx";
        String branch = "master";
        boolean result1 = AntCodeClient.branchCheck(groupPath, projectPath, branch);
        Assert.assertTrue(result1);
        branch = "rule";
        boolean result2 = AntCodeClient.branchCheck(groupPath, projectPath, branch);
        Assert.assertFalse(result2);
    }

//    @Test
    public void commitCheck() {
        String groupPath = "common_release";
        String projectPath = "grootx";
        String commit = "44e43456fad4e809bf88cad19c1812ead37a6a3d";
        boolean result1 = AntCodeClient.commitCheck(groupPath, projectPath, commit);
        Assert.assertTrue(result1);
        commit = "44e43456fad4e809bf88cad19c1812ead37a6a3c";
        boolean result2 = AntCodeClient.commitCheck(groupPath, projectPath, commit);
        Assert.assertFalse(result2);
    }

}