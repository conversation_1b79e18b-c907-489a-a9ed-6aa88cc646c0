package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.service.codegpt.IntentionRecognitionService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.ContentCheckService;
import com.alipay.codegencore.service.common.DataCheckService;
import com.alipay.codegencore.service.common.RcsmartCheckService;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.CommonUtils;
import com.alipay.codegencore.service.utils.FixedSizeQueue;
import com.alipay.common.tracer.core.async.SofaTracerCallable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 获取审核结果的实现类
 */
@Service
public class CheckServiceImpl implements CheckService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CheckService.class);

    @Resource
    private DataCheckService dataCheckService;

    @Resource
    private ContentCheckService contentCheckService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private IntentionRecognitionService intentionRecognitionService;

    @Resource
    private RcsmartCheckService rcsmartCheckService;

    @Resource
    private ExecutorService appThreadPool;

    @Resource
    private SecAgentHelper secAgentHelper;

    @Override
    public CheckResultModel getAnswerCheckResultLongContent(String messageUid, List<ChatMessage> chatMessageList, String longContent, ChatRequestExtData extData, String questionUid, boolean externalModel) {
        // 集成 agentSecSdk.chatResponse, 判断chat响应是否有恶意攻击, 根据返回结果做相应处理
        ReviewResultModel reviewResultModel = secAgentHelper.checkChatContent(messageUid, questionUid, extData, chatMessageList, longContent, ChatRoleEnum.ASSISTANT);
        if(reviewResultModel!=null&&!reviewResultModel.isRet()){
            return new CheckResultModel(ChatRoleEnum.ASSISTANT,false,null,reviewResultModel,null,null,null);
        }
        List<String> requestContentList = splitContent(longContent);
        CheckResultModel checkResultModel = null;
        for (int i = 1; i <= requestContentList.size(); i++) {
            checkResultModel = getCheckResult(messageUid, i, chatMessageList, requestContentList.get(i - 1), longContent, extData, ChatRoleEnum.ASSISTANT, questionUid,externalModel);
            if (!checkResultModel.isAllCheckRet()) {
                return checkResultModel;
            }
        }
        return checkResultModel;
    }

    @Override
    public CheckResultModel getQuestionCheckResult(String questionUid, List<ChatMessage> chatMessageList, ChatRequestExtData extData, boolean externalModel) {
        // 审核问题的时候1批全审
        String batchContent = chatMessageList.get(chatMessageList.size() - 1).getContent();
        // 集成 agentSecSdk.chatResponse, 判断chat响应是否有恶意攻击, 根据返回结果做相应处理
        ReviewResultModel reviewResultModel = secAgentHelper.checkChatContent(questionUid, questionUid, extData, chatMessageList, null, ChatRoleEnum.USER);
        if(reviewResultModel!=null&&!reviewResultModel.isRet()){
            return new CheckResultModel(ChatRoleEnum.USER,false,null,reviewResultModel,null,null,null);
        }

        return getCheckResult(questionUid, 1, chatMessageList, batchContent, null, extData, ChatRoleEnum.USER, questionUid,externalModel);
    }

    @Override
    public CheckResultModel getQuestionCheckResult(String questionUid, String prompt, ChatRequestExtData extData, boolean externalModel) {
        List<ChatMessage> chatMessageList = new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole(ChatRoleEnum.USER.getName());
        chatMessage.setContent(prompt);

        chatMessageList.add(chatMessage);
        // 集成 agentSecSdk.chatResponse, 判断chat响应是否有恶意攻击, 根据返回结果做相应处理
        ReviewResultModel reviewResultModel = secAgentHelper.checkChatContent(questionUid, questionUid, extData, chatMessageList, null, ChatRoleEnum.USER);
        if(reviewResultModel!=null&&!reviewResultModel.isRet()){
            return new CheckResultModel(ChatRoleEnum.USER,false,null,reviewResultModel,null,null,null);
        }

        return getQuestionCheckResult(questionUid, chatMessageList, extData, externalModel);
    }

    @Override
    public CheckResultModel getAnswerCheckResult(String questionUid, String answerUid, Integer batch, List<ChatMessage> chatMessageList, String content, String completeAnswer, ChatRequestExtData extData, boolean externalModel) {
        // 集成 agentSecSdk.chatResponse, 判断chat响应是否有恶意攻击, 根据返回结果做相应处理
        ReviewResultModel reviewResultModel = secAgentHelper.checkChatContent(answerUid, questionUid, extData, chatMessageList, content, ChatRoleEnum.ASSISTANT);
        if(reviewResultModel!=null&&!reviewResultModel.isRet()){
            return new CheckResultModel(ChatRoleEnum.ASSISTANT,false,null,reviewResultModel,null,null,null);
        }

        return getCheckResult(answerUid, batch, chatMessageList, content, completeAnswer, extData, ChatRoleEnum.ASSISTANT, questionUid,externalModel);
    }


    @Override
    public ReviewResultModel getInfoSecCheckRet(String content, ChatRoleEnum chatRoleEnum, Integer batch, ChatRequestExtData extData, String questionUid, String messageUid) {
        ReviewResultModel resultModel = new ReviewResultModel(true);
        List<String> infoSecCheckContentList = getInfoSecCheckContentSplitList(content);
        for (int i = 0; i < infoSecCheckContentList.size(); i++) {
            String infoSecCheckContent = infoSecCheckContentList.get(i);
            resultModel = contentCheckService.checkContent(infoSecCheckContent, chatRoleEnum, messageUid, batch, i + 1, extData, questionUid);
            if (!resultModel.isRet()) {
                break;
            }
        }
        return resultModel;
    }

    /**
     * 对内容进行送审，包含各个审核平台
     *
     * @param messageUid      当前审核消息的uid
     * @param batch           当前审核批次,从1开始
     * @param chatMessageList 送给AI的内容列表，用于问题审核
     * @param batchContent    当前送审批次的文本
     * @param completeAnswer  当前完整的答案，用于答案审核
     * @param extData         扩展字段，用于判断需要走那些审核
     * @param chatRoleEnum    审核的是问题/答案
     * @param questionUid     问题的uid
     * @return 审核结果
     */
    private CheckResultModel getCheckResult(String messageUid, Integer batch, List<ChatMessage> chatMessageList, String batchContent, String completeAnswer, ChatRequestExtData extData, ChatRoleEnum chatRoleEnum, String questionUid, boolean externalModel) {
        boolean keymapCheck = getCheckSwitch(extData, ReviewPlatformEnum.KEYMAP);
        boolean infoSecCheck = getCheckSwitch(extData, ReviewPlatformEnum.INFOSEC);
        boolean antDsrCheck = getCheckSwitch(extData, ReviewPlatformEnum.ANTDSR);
        boolean intentionCheck = getCheckSwitch(extData, ReviewPlatformEnum.INTENTION);
        boolean rcSmartCheck = getCheckSwitch(extData, ReviewPlatformEnum.RCSMART);
        if (allNotNeedCheck(extData) || batchContent == null) {
            return new CheckResultModel(chatRoleEnum,true, null, null, null,null,null);
        }
        Future<ReviewResultModel> keymapFuture = asyncRequest(externalModel, messageUid, batch, chatMessageList, batchContent, completeAnswer, extData, chatRoleEnum, questionUid, keymapCheck, infoSecCheck, antDsrCheck, intentionCheck, rcSmartCheck, ReviewPlatformEnum.KEYMAP);
        Future<ReviewResultModel> antDsrFuture = asyncRequest(externalModel, messageUid, batch, chatMessageList, batchContent, completeAnswer, extData, chatRoleEnum, questionUid, keymapCheck, infoSecCheck, antDsrCheck, intentionCheck, rcSmartCheck, ReviewPlatformEnum.ANTDSR);
        Future<ReviewResultModel> infoSecFuture = asyncRequest(externalModel, messageUid, batch, chatMessageList, batchContent, completeAnswer, extData, chatRoleEnum, questionUid, keymapCheck, infoSecCheck, antDsrCheck, intentionCheck, rcSmartCheck, ReviewPlatformEnum.INFOSEC);
        Future<ReviewResultModel> intentionFuture = asyncRequest(externalModel, messageUid, batch, chatMessageList, batchContent, completeAnswer, extData, chatRoleEnum, questionUid, keymapCheck, infoSecCheck, antDsrCheck, intentionCheck, rcSmartCheck, ReviewPlatformEnum.INTENTION);
        Future<ReviewResultModel> rcSmartFuture = asyncRequest(externalModel, messageUid, batch, chatMessageList, batchContent, completeAnswer, extData, chatRoleEnum, questionUid, keymapCheck, infoSecCheck, antDsrCheck, intentionCheck, rcSmartCheck, ReviewPlatformEnum.RCSMART);
        try {
            ReviewResultModel keymapCheckRet = getReviewResultModel(keymapFuture);
            ReviewResultModel antDsrCheckRet = getReviewResultModel(antDsrFuture);
            ReviewResultModel intentionCheckRet = getReviewResultModel(intentionFuture);
            ReviewResultModel infoSecCheckRet = getReviewResultModel(infoSecFuture);
            ReviewResultModel rcSmartCheckRet = getReviewResultModel(rcSmartFuture);
            boolean allCheckRet = getAllCheckRet(keymapCheckRet,antDsrCheckRet,intentionCheckRet,infoSecCheckRet,rcSmartCheckRet);
            return new CheckResultModel(chatRoleEnum, allCheckRet, keymapCheckRet, infoSecCheckRet, antDsrCheckRet, intentionCheckRet, rcSmartCheckRet);
        } catch (ExecutionException | InterruptedException e) {
            LOGGER.warn("安全审核异常",e);
            if (e.getCause() instanceof BizException) {
                throw (BizException) e.getCause();
            }
            throw new BizException(ResponseEnum.CHECK_FAILED);
        }
    }

    private boolean getAllCheckRet(ReviewResultModel keymapCheckRet, ReviewResultModel antDsrCheckRet, ReviewResultModel intentionCheckRet, ReviewResultModel infoSecCheckRet, ReviewResultModel rcSmartCheckRet) {
        boolean keymapCheck = keymapCheckRet == null || keymapCheckRet.isRet();
        boolean infoSecCheck = infoSecCheckRet == null || infoSecCheckRet.isRet();
        boolean antDsrCheck = antDsrCheckRet == null || antDsrCheckRet.isRet();
        boolean intentionCheck = intentionCheckRet == null || intentionCheckRet.isRet();
        boolean rcSmartCheck = rcSmartCheckRet == null || rcSmartCheckRet.isRet();
        List<ReviewPlatformEnum> noBlockingCheckPlatformList = codeGPTDrmConfig.getNoBlockingCheckPlatformList();
        return (noBlockingCheckPlatformList.contains(ReviewPlatformEnum.KEYMAP) || keymapCheck) &&
                (noBlockingCheckPlatformList.contains(ReviewPlatformEnum.INFOSEC) || infoSecCheck) &&
                (noBlockingCheckPlatformList.contains(ReviewPlatformEnum.ANTDSR) || antDsrCheck) &&
                (noBlockingCheckPlatformList.contains(ReviewPlatformEnum.INTENTION) || intentionCheck) &&
                (noBlockingCheckPlatformList.contains(ReviewPlatformEnum.RCSMART) || rcSmartCheck);
    }

    private ReviewResultModel getReviewResultModel(Future<ReviewResultModel> future) throws ExecutionException, InterruptedException {
        return future == null ? null : future.get();
    }

    private Future<ReviewResultModel> asyncRequest(boolean externalModel, String messageUid, Integer batch, List<ChatMessage> chatMessageList, String batchContent, String completeAnswer, ChatRequestExtData extData, ChatRoleEnum chatRoleEnum, String questionUid,
                                                   boolean keymapCheck, boolean infoSecCheck, boolean antDsrCheck, boolean intentionCheck, boolean rcSmartCheck, ReviewPlatformEnum reviewPlatformEnum) {
        // 内容安全平台审查
        if (ReviewPlatformEnum.INFOSEC == reviewPlatformEnum && infoSecCheck) {
            return appThreadPool.submit(new SofaTracerCallable<>(()-> processInfoSecCheckRet(extData, chatRoleEnum, chatMessageList, messageUid, questionUid, batch, completeAnswer)));
        }
        if (chatRoleEnum != ChatRoleEnum.USER) {
            return null;
        }
        if (ReviewPlatformEnum.KEYMAP == reviewPlatformEnum && keymapCheck) {
            return appThreadPool.submit(new SofaTracerCallable<>(() -> dataCheckService.checkData(messageUid, batch, batchContent)));
        } else if (ReviewPlatformEnum.ANTDSR == reviewPlatformEnum && antDsrCheck && externalModel) {
            // 外部模型openai的模型才需要单独走antDsr做数据安全审核,内部模型的数据安全审核放在infoSec的策略里面
            return appThreadPool.submit(new SofaTracerCallable<>(() -> dataCheckService.antDsrCheckData(messageUid, batch, batchContent, extData)));
        } else if (ReviewPlatformEnum.INTENTION == reviewPlatformEnum && intentionCheck) {
            return appThreadPool.submit(new SofaTracerCallable<>(() -> {
                boolean ret = intentionRecognitionService.isCodeDomain(messageUid,chatMessageList);
                return new ReviewResultModel(ret);
            }));
        } else if (ReviewPlatformEnum.RCSMART == reviewPlatformEnum && rcSmartCheck) {
            return appThreadPool.submit(new SofaTracerCallable<>(() -> rcsmartCheckService.rcsmartCheck(messageUid, batch, batchContent, chatRoleEnum)));
        }
        return null;
    }

    private boolean allNotNeedCheck(ChatRequestExtData extData) {
        if (extData == null) {
            return true;
        }
        return !extData.getRcSmartCheck() &&
                !extData.getIntentionCheck() &&
                !extData.getKeymapCheck() &&
                !extData.getAntDsrCheck() &&
                !extData.getInfoSecCheck();
    }

    private boolean getCheckSwitch(ChatRequestExtData extData, ReviewPlatformEnum reviewPlatformEnum) {
        if (extData == null) {
            return false;
        }
        if (ReviewPlatformEnum.KEYMAP == reviewPlatformEnum) {
            return extData.getKeymapCheck();
        } else if (ReviewPlatformEnum.ANTDSR == reviewPlatformEnum) {
            return extData.getAntDsrCheck();
        } else if (ReviewPlatformEnum.INFOSEC == reviewPlatformEnum) {
            return extData.getInfoSecCheck();
        } else if (ReviewPlatformEnum.INTENTION == reviewPlatformEnum) {
            return extData.getIntentionCheck();
        } else if (ReviewPlatformEnum.RCSMART == reviewPlatformEnum) {
            return extData.getRcSmartCheck();
        }
        return false;
    }

    private ReviewResultModel processInfoSecCheckRet(ChatRequestExtData extData, ChatRoleEnum chatRoleEnum, List<ChatMessage> chatMessageList, String messageUid, String questionUid, Integer batch, String completeAnswer) {
        // 对于标题对应的问题不需要送审,只需要审核答案
        if (chatRoleEnum == ChatRoleEnum.USER && extData.isContentIsTitle()) {
            return new ReviewResultModel(true);
        }
        ReviewResultModel resultModel;
        // 生成标题的话只需要审核AI回复
        if (chatRoleEnum == ChatRoleEnum.USER) {
            // 问题分批送审时,需要拼接当前会话前面的所有问题,然后超长的话分批
            String questionStr = getAllQuestionStr(chatMessageList);
            resultModel = getInfoSecCheckRet(questionStr, chatRoleEnum, batch, extData, questionUid, messageUid);
        } else {
            // 答案分批送审时,每一批都要拼上当前答案前面批次的内容,还要拼上当前答案的对应的问题,然后超长的话分批
            String nowQuestionContent = chatMessageList.get(chatMessageList.size() - 1).getContent();
            resultModel = getInfoSecCheckRet(nowQuestionContent + "#" + completeAnswer, chatRoleEnum, batch, extData, questionUid, messageUid);
        }
        return resultModel;
    }

    private List<String> getInfoSecCheckContentSplitList(String content) {
        JSONObject infoSecCheckLengthConfig = JSON.parseObject(codeGPTDrmConfig.getCheckLengthConfig()).getJSONObject(ReviewPlatformEnum.INFOSEC.name());
        if (!infoSecCheckLengthConfig.containsKey("maxLength") || !infoSecCheckLengthConfig.containsKey("hasPunctuationLength")) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"drm config is not exist");
        }
        Integer maxLength = infoSecCheckLengthConfig.getInteger("maxLength");
        Integer hasPunctuationLength = infoSecCheckLengthConfig.getInteger("hasPunctuationLength");
        List<String> ret = new ArrayList<>();
        if (StringUtils.isBlank(content)) {
            return ret;
        }
        if (content.length() <= maxLength) {
            ret.add(content);
            return ret;
        }
        StringBuilder sb = new StringBuilder();
        for (char c : content.toCharArray()) {
            sb.append(c);
            if (sb.length() > maxLength || (sb.length() >= hasPunctuationLength && CommonUtils.isPunctuation(String.valueOf(c)))) {
                ret.add(sb.toString());
                sb = new StringBuilder();
            }
        }
        if (StringUtils.isNotBlank(sb.toString())) {
            ret.add(sb.toString());
        }
        return ret;
    }

    private String getAllQuestionStr(List<ChatMessage> chatMessageList) {
        if (CollectionUtils.isEmpty(chatMessageList)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (ChatMessage chatMessage : chatMessageList) {
            if (!ChatRoleEnum.USER.getName().equalsIgnoreCase(chatMessage.getRole())) {
                continue;
            }
            sb.append(chatMessage.getContent()).append("#");
        }
        if (StringUtils.isNotBlank(sb.toString())) {
            sb.replace(sb.length() - 1, sb.length(), "");
        }
        return sb.toString();
    }


    private List<String> splitContent(String content) {
        Integer checkDataMaxSize = codeGPTDrmConfig.getCheckDataMaxSize();
        Integer checkDataSizeWithPunctuation = codeGPTDrmConfig.getCheckDataSizeWithPunctuation();
        StringBuilder sb = new StringBuilder();
        List<String> retList = new ArrayList<>();
        FixedSizeQueue<Character> fixedSizeQueue = new FixedSizeQueue<>(codeGPTDrmConfig.getOverlapLength());
        for (char c : content.toCharArray()) {
            String str = String.valueOf(c);
            sb.append(c);
            fixedSizeQueue.addLast(c);
            if (sb.length() > checkDataMaxSize || (CommonUtils.isPunctuation(str) && sb.length() >= checkDataSizeWithPunctuation)) {
                retList.add(sb.toString());
                sb = new StringBuilder(fixedSizeQueue.getQueueStr());
            }
        }
        if (retList.size() == 0 || !retList.get(retList.size() - 1).endsWith(sb.toString())) {
            retList.add(sb.toString());
        }
        return retList;
    }
}
