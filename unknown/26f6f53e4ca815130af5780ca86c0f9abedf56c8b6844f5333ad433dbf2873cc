package com.alipay.codegencore.utils.http;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * URL内容读取工具类
 */
public class URLReaderUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(URLReaderUtil.class);

    /**
     * 保留换行符读取一个url的文本内容,对>=2个的换行符替换为2个换行符
     * @param url
     * @return 文本内容
     */
    public static String readURL(String url) {
        try {
            Document document = Jsoup.connect(url).get();
            String text = getTextWithNewlines(document.body());
            // 匹配两个或更多的连续换行符及其后面的空白字符
            String pattern = "(\n\\s*){2,}";
            Pattern regex = Pattern.compile(pattern);
            Matcher matcher = regex.matcher(text);
            // 将匹配到的连续换行符及其后面的空白字符替换为两个换行符
            return matcher.replaceAll("\n\n");
        } catch (Exception e) {
            LOGGER.error("解析URL失败,url:" + url, e);
            throw new BizException(ResponseEnum.FILE_ANALYSIS_FAILED);
        }
    }

    /**
     * 获取元素及其子节点中所有文本内容，并在末尾添加换行符。
     *
     * @param element 要处理的元素
     * @return 处理后的字符串
     */
    private static String getTextWithNewlines(Element element) {
        StringBuilder sb = new StringBuilder();
        List<Node> nodes = element.childNodes();
        for (Node node : nodes) {
            if (node instanceof TextNode) {
                String str = ((TextNode) node).getWholeText();
                if (StringUtils.isNotBlank(str) && StringUtils.isNotBlank(str.trim())) {
                    sb.append(str).append(System.lineSeparator());
                }
            } else if (node instanceof Element) {
                sb.append(getTextWithNewlines((Element) node));
            }
        }
        return sb.toString();
    }


}
