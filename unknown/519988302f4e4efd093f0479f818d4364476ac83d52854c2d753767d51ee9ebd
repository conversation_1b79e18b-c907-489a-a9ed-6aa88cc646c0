package com.alipay.codegencore.service.impl.model;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.TokenDOExample;
import com.alipay.codegencore.dal.mapper.TokenDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.enums.ChatGPTModelEnum;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RateLimitTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.openai.*;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.codegpt.CostService;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.limiter.qpx.QpxLimiterFactory;
import com.alipay.codegencore.service.gptcache.bean.GptCacheResponse;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.StreamDataListener;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Flow;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * chatGPT语言模型
 *
 * <AUTHOR>
 */
@Service("chatGPTLanguageModelService")
@Slf4j
public class ChatGPTLanguageModelServiceImpl implements LanguageModelService {

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    private static final Logger LOGGER = LoggerFactory.getLogger( ChatGPTLanguageModelServiceImpl.class );

    @Resource
    private CheckService checkService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private CostService costService;

    @Resource
    private ConfigService configService;

    @Resource
    private CalculateTokenService calculateTokenService;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource
    private AlgoModelUtilService algoModelUtilService;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource
    private TokenDOMapper tokenDOMapper;

    private String defaultCodegptToken;

    /**
     * 暂存user即将需要扣掉的金额
     * key: user
     * value: 金额
     */
    private ConcurrentHashMap<String,BigDecimal> userNeedFeeMap = new ConcurrentHashMap<>();

    /**
     * 验证user是否还有钱扣
     * key: user
     * value: 是否有钱扣
     */
    private ConcurrentHashMap<String,Boolean> userBalanceMap = new ConcurrentHashMap<>();

    /**
     * 初始化默认的codegpt token
     */
    @PostConstruct
    public void init(){
        List<TokenDO> tokenDOList = tokenDOMapper.selectByExample(new TokenDOExample());
        for (TokenDO tokenDO : tokenDOList){
            if (AppConstants.CODEGPT_TOKEN_USER.equals(tokenDO.getUser())){
                defaultCodegptToken = tokenDO.getToken();
            }
            userBalanceMap.put(tokenDO.getUser(),Optional.ofNullable(tokenDO.getBalance()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0);
        }
        //自旋锁 用于chatgpt扣费操作
        appThreadPool.execute(()->cost());
    }

    @Override
    public boolean isServiceOk() {
        return true;
    }

    /**
     *每十秒加载一次map的值进行扣钱操作
     */
    private void cost() {
        HashMap<String,BigDecimal> userNeedFeeMapCopy = new HashMap<>();
        while (true) {
            try {
                synchronized (this) {
                    //先释放userNeedFeeMap
                    userNeedFeeMapCopy.putAll(userNeedFeeMap);
                    userNeedFeeMap.clear();
                }
                //进行扣费操作
                userNeedFeeMapCopy.entrySet().stream().forEach(x -> {
                    costService.cost(x.getKey(), x.getValue());
                    CHAT_LOGGER.info("cost results of the:user={},balance={}", x.getKey(), x.getValue());
                });
                //有可能会被其它操作更新金额。需要刷下map中的值
                List<TokenDO> tokenDOList = tokenDOMapper.selectByExample(new TokenDOExample());
                tokenDOList.stream().forEach(tokenDO ->
                        userBalanceMap.put(tokenDO.getUser(), Optional.ofNullable(tokenDO.getBalance()).orElse(BigDecimal.ZERO)
                                .compareTo(BigDecimal.ZERO) > 0)
                );
                userNeedFeeMapCopy.clear();

                Thread.sleep(codeGPTDrmConfig.getChatGptTimingFeeTime());
            } catch (Exception e) {
                LOGGER.error("function cost thread.sleep error{}", e.getMessage());
                continue;
            }
        }
    }


    private void sendRequest(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        ChatCompletionRequest chatCompletionRequest = JSON.parseObject(JSON.toJSONString(gptAlgModelServiceRequest.getChatCompletionRequest()), ChatCompletionRequest.class);

        if(codeGPTDrmConfig.isDocSearchResultAddToPrompt() && !CollectionUtils.isEmpty(gptAlgModelServiceRequest.getChatCompletionRequest().getDocs()) && !gptAlgModelServiceRequest.getAlgoBackendDO().getModel().equalsIgnoreCase("TOOL_GPT3")){
            List<ChatMessage> messageList = chatCompletionRequest.getMessages();
            ChatMessage userMessage = messageList.get(messageList.size()-1);
            String rewriteMessage = algoModelUtilService.addDocInfoToPrompt(gptAlgModelServiceRequest.getChatCompletionRequest().getDocs(), userMessage.getContent());
            userMessage.setContent(rewriteMessage);
            gptAlgModelServiceRequest.getChatCompletionRequest().setDocs(Collections.emptyList());
        }

        beforeModelRequest(chatCompletionRequest);
        AlgoBackendDO algoBackendDO = gptAlgModelServiceRequest.getAlgoBackendDO();

        algoModelUtilService.applyChatGPTDefaultValues(chatCompletionRequest, algoBackendDO);
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        String sessionUid = null;
        String bizId = null;
        if (chatRequestExtData != null) {
            sessionUid = chatRequestExtData.getSessionUid();
            bizId = chatRequestExtData.getBizId();
        }
        String requestId = gptAlgModelServiceRequest.getRequestId();
        String uniqueAnswerId = gptAlgModelServiceRequest.getUniqueAnswerId();
        LOGGER.info("开始发请求给OpenAI服务,requestId:{},bizId:{},sessionUid:{}", requestId, bizId, sessionUid);
        // 准备请求AI参数
        boolean stressTest = gptAlgModelServiceRequest.isStressTest();

        String serverUrl = AlgoBackendUtil.exactServerConfig(algoBackendDO);

        Pair<String, Map<String, String>> requestUrlAndHeaders = getRequestUrlAndHeaders(serverUrl, stressTest);

        // 配置响应结果
        String user = gptAlgModelServiceRequest.getUserName();
        // 流式响应缓存
        ChatStreamBuffer chatStreamBuffer = new ChatStreamBuffer();
        String streamInputId = String.format("%s%s", AppConstants.STREAM_INPUT_PREFIX, gptAlgModelServiceRequest.getUniqueAnswerId());
        final String[] responseModel = {null};
        try {
            // 执行http请求
            String finalSessionUid = sessionUid;
            String finalBizId = bizId;
            HttpClient.post(requestUrlAndHeaders.getKey())
                    // 请求体
                    .content(JSONObject.toJSONString(chatCompletionRequest))
                    // 请求头
                    .headers(requestUrlAndHeaders.getValue())
                    // 流式响应
                    .streamExecute(300000, new StreamDataListener() {
                        // 流式响应前处理
                        @Override
                        public void onConnect(Flow.Subscription subscription) {
                            LOGGER.info("链接OpenAI流式请求成功,sessionUid:{},bizId:{},requestId:{}", finalSessionUid, finalBizId, requestId);
                        }

                        // 流式响应中
                        @Override
                        public void eachData(String data, Flow.Subscription subscription) {
                            try {
                                if(algoModelUtilService.needCloseInputStream(streamInputId)){
                                    subscription.cancel();
                                }

                                if (StringUtils.isNotBlank(data)) {
                                    CHAT_LOGGER.debug("chatgpt handler get stream:{}", data);
                                    // 格式化data内容
                                    if (data.startsWith("data: ")) {
                                        data = data.substring(6);
                                    } else {
                                        CHAT_LOGGER.error("stream part not start with 'data: ': {}", data);
                                        // 结束会话
                                        algoModelUtilService.handleEveryStreamError(ResponseEnum.STREAM_PART_NOT_START_WITH_DATA_PREFIX.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.STREAM_PART_NOT_START_WITH_DATA_PREFIX);
                                        return;
                                    }

                                    // 判断响应是否已经结束
                                    if (AppConstants.COMPLETION_STREAM_API_END_MARK.equalsIgnoreCase(data)) {
                                        LOGGER.info("stream end, message: {}", AppConstants.COMPLETION_STREAM_API_END_MARK);
                                        return;
                                    }

                                    ChatStreamPartResponse chatStreamPartResponse = JSON.parseObject(data, ChatStreamPartResponse.class);
                                    if (chatStreamPartResponse == null) {
                                        CHAT_LOGGER.error("stream part can not be deserialized: {}", data);
                                        // 结束会话
                                        algoModelUtilService.handleEveryStreamError(ResponseEnum.STREAM_DATA_CAN_NOT_BE_DESERIALIZED.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.STREAM_DATA_CAN_NOT_BE_DESERIALIZED);
                                        return;
                                    }
                                    if (responseModel[0] == null) {
                                        responseModel[0] = chatStreamPartResponse.getModel();
                                    }
                                    ChatMessage delta = chatStreamPartResponse.getChoices().get(0).getDelta();
                                    String finishReason = chatStreamPartResponse.getChoices().get(0).getFinishReason();
                                    if (delta != null) {
                                        ChatUtils.handleEveryStreamData(delta, finishReason, noneSerializationCacheManager, uniqueAnswerId);
                                        chatStreamBuffer.buffer(delta);
                                    }
                                }
                            } catch (Throwable throwable) {
                                CHAT_LOGGER.error("stream error, data:{}", data, throwable);
                                algoModelUtilService.handleEveryStreamError(ResponseEnum.STREAM_DELTA_PROCESS_FAILED.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.STREAM_DELTA_PROCESS_FAILED);
                            }
                        }

                        // 处理异常消息
                        @Override
                        public void onError(Throwable throwable) {
                            Pattern pattern = Pattern.compile("^Stream.*cancelled$", Pattern.DOTALL);
                            Matcher matcher = pattern.matcher(throwable.getMessage());
                            if (matcher.find()) {
                                LOGGER.info("手动结束流");
                                return;
                            }
                            CHAT_LOGGER.error("chagpt stream request failed, id: {}, user:{}", requestId, user, throwable);
                            algoModelUtilService.handleEveryStreamError(ResponseEnum.STREAM_DELTA_PROCESS_FAILED.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.STREAM_DELTA_PROCESS_FAILED);
                        }

                        // 流式响应结束后处理
                        @Override
                        public void onComplete() {
                            LOGGER.debug("stream onComplete");
                        }
                    }, (statusCode, errorResponse) -> {
                        LOGGER.error("OpenAI Stream error,statusCode:{},errorResponse:{}", statusCode, errorResponse);
                        try {
                            ChatCompletionResult errorDetail = JSON.parseObject(errorResponse, ChatCompletionResult.class);

                            ChatMessage endMessage = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), errorDetail.getError().getMessage());
                            ChatUtils.handleEveryStreamData(endMessage, "server_abnormal_stop"
                                    , noneSerializationCacheManager, uniqueAnswerId);
                        } catch (Exception e) {
                            LOGGER.error("OpenAI Stream error flush failed", e);
                            algoModelUtilService.handleEveryStreamError(ResponseEnum.AI_CALL_ERROR.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.AI_CALL_ERROR);
                        }
                    });
            chatStreamBuffer.flush();
            BigDecimal costFee = costFee(chatStreamBuffer, chatCompletionRequest, user, requestId, responseModel[0]);
            synchronized (this){
                if (userNeedFeeMap.containsKey(user)){
                    userNeedFeeMap.put(user, userNeedFeeMap.get(user).add(costFee));
                }else {
                    userNeedFeeMap.put(user, costFee);
                }
            }
        } catch (Throwable e) {
            LOGGER.error("模型调用失败", e);
            algoModelUtilService.handleEveryStreamError(ResponseEnum.AI_CALL_ERROR.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.AI_CALL_ERROR);
        }
    }

    /**
     * 验证对应的账户是否有余额
     * @param user
     * @return
     */
    private boolean hasBalance(String user){
       return userBalanceMap.get(user);
    }


    /**
     * 对话
     *
     * @param params 对话请求
     * @return ai模型回答
     */
    @Override
    public ChatMessage chat(GptAlgModelServiceRequest params) {
        String user = params.getUserName();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        beforeModelRequest(chatCompletionRequest);
        String requestId = params.getRequestId();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        boolean stressTest = params.isStressTest();
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        if (!hasBalance(user)) {
            throw new BizException(ResponseEnum.ACCOUNT_ARREARS);
        }
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        if (chatRequestExtData != null) {
            chatRequestExtData.setAlgoBackendDO(algoBackendDO);
        }
        // 对用户输入的问题进行审核
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        // 用户的输入直接一批全审核
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(requestId, messages, chatRequestExtData,true);
        if (!requestCheckResultModel.isAllCheckRet()) {
            throw new BizException(ResponseEnum.CHECK_FAILED);
        }
        // 开始请求ai服务
        ChatMessage aiResult = getAiResult(params.getModelEnv(),requestId, user, stressTest, algoBackendDO, chatCompletionRequest);
        // 审核ai的输出
        if(aiResult!=null && aiResult.getContent()!=null){
            CheckResultModel resultCheckResultModel = checkService.getAnswerCheckResultLongContent(requestId, messages, aiResult.getContent(), chatRequestExtData, requestId,true);
            if (!resultCheckResultModel.isAllCheckRet()) {
                throw new BizException(ResponseEnum.CHECK_FAILED);
            }
        }

        return aiResult;
    }

    /**
     * 流式对话
     *
     * @param params    对话请求
     */
    @Override
    public void streamChatForServlet(GptAlgModelServiceRequest params) {
        // 获取参数信息
        String user = params.getUserName();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        String requestId = params.getRequestId();
        Consumer<StreamResponseModel> resultHandler = params.getResultHandler();
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = params.getChatStreamPartResponseHandler();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();

        algoModelUtilService.applyChatGPTDefaultValues(chatCompletionRequest, algoBackendDO);
        // 限流校验
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        // 账户额度校验
        if (!hasBalance(user)) {
            throw new BizException(ResponseEnum.ACCOUNT_ARREARS);
        }
        // 必须配置为流式请求参数
        if (!chatCompletionRequest.getStream()) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "stream must be true");
        }
        // n表示"针对每个输入消息，要生成多少个聊天完成选项"
        if (chatCompletionRequest.getN() != null && chatCompletionRequest.getN() > 1) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "n must be 1");
        }
        // 对用户输入的问题进行审核,输入内容一批全部审
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        List<ChatMessage> copyMessages = JSON.parseArray(JSON.toJSONString(messages),ChatMessage.class);
        // 获取额外配置参数
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        if (chatRequestExtData != null) {
            chatRequestExtData.setAlgoBackendDO(algoBackendDO);
        }
        // 检查提问是否符合安全审查规范
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(requestId, messages, chatRequestExtData,true);
        // 如果提问不合规, 则停止响应
        if (!requestCheckResultModel.isAllCheckRet()) {
            algoModelUtilService.processWhenQuestionFailCheck(requestCheckResultModel, chatStreamPartResponseConsumer, resultHandler);
            return;
        }
        // 设置后端请求model
        if(chatCompletionRequest.getModel() == null){
            chatCompletionRequest.setModel(AlgoBackendUtil.exactRequestModelModel(algoBackendDO));
        }

        // 开启gptCache并从gptCache写入TBase数据成功 不发送请求
        GptCacheResponse gptCacheResponse  = null;
        if(algoBackendDO.getEnableGptCache()){
            gptCacheResponse =  algoModelUtilService.getGPTCache(algoBackendDO, params);
        }
        if(gptCacheResponse !=  null  && StringUtils.isNotBlank(gptCacheResponse.getAnswer())){
            GptCacheResponse finalGptCacheResponse = gptCacheResponse;
            appThreadPool.execute(() -> algoModelUtilService.pushToTBase(finalGptCacheResponse, params));
        }else{
            CHAT_LOGGER.info("chagpt completion requestId:{}, user:{}, request: {}", requestId, user, JSON.toJSONString(chatCompletionRequest));
            appThreadPool.execute(()-> sendRequest(AlgoModelUtilService.copyParamWithoutServletResponse(params)));
        }

        algoModelUtilService.getChatDataFromTBase(params, copyMessages, requestCheckResultModel,algoBackendDO.getEnableGptCache(), new ChatStreamBuffer(),false);
    }

    private String getAllQuestion(List<ChatMessage> messages) {
        StringBuilder sb = new StringBuilder();
        for (ChatMessage chatMessage : messages) {
            sb.append(chatMessage.getContent());
        }
        return sb.toString();
    }

    private ChatMessage getAiResult(String modelEnv,String requestId, String user, boolean stressTest, AlgoBackendDO algoBackendDO, ChatCompletionRequest chatCompletionRequest) {
        String response = null;
        String serverUrl = AlgoBackendUtil.exactServerConfig(algoBackendDO);

        Pair<String, Map<String, String>> requestUrlAndHeaders = getRequestUrlAndHeaders(serverUrl, stressTest);

        //设置后端请求model
        if(chatCompletionRequest.getModel() == null){
            chatCompletionRequest.setModel(AlgoBackendUtil.exactRequestModelModel(algoBackendDO));
        }

        try {
            response = HttpClient.post(requestUrlAndHeaders.getKey())
                    .headers(requestUrlAndHeaders.getValue())
                    .content(JSONObject.toJSONString(chatCompletionRequest))
                    .syncExecuteWithExceptionThrow(180000L);
        } catch (Exception e) {
            CHAT_LOGGER.error("chagpt completion request failed, id: {}, user:{}", requestId, user, e);
            throw new RuntimeException(e);
        }

        if (response == null) {
            CHAT_LOGGER.info("chagpt completion get no result, id:{}, user:{}", requestId, user);
            return null;
        }

        ChatCompletionResult chatCompletionResult = null;
        try{
            chatCompletionResult = JSON.parseObject(response, ChatCompletionResult.class);
        } catch (Exception e) {
            CHAT_LOGGER.info("chagpt completion requestId:{}, user:{}, response is {}, can not be serialized correctly",
                    requestId, user, response);
            throw new BizException(ResponseEnum.AI_CALL_ERROR, "unexpected response: " + response);
        }

        if(chatCompletionResult.getError()!=null){
            CHAT_LOGGER.info("chagpt completion request failed, id: {}, user:{}, error:{}", requestId, user, chatCompletionResult.getError().getMessage());
            throw new BizException(ResponseEnum.AI_CALL_ERROR, chatCompletionResult.getError().getMessage());
        }

        //计算费用金额 在cost()方法内递归扣减
        BigDecimal costFee = costService.calculateNeedCostFee(ChatGPTModelEnum.getByModelName(chatCompletionResult.getModel()),
                chatCompletionResult.getUsage().getPromptTokens(), chatCompletionResult.getUsage().getCompletionTokens());
        synchronized (this){
            if (userNeedFeeMap.containsKey(user)){
                userNeedFeeMap.put(user, userNeedFeeMap.get(user).add(costFee));
            }else {
                userNeedFeeMap.put(user, costFee);
            }
        }
        CHAT_LOGGER.info("chagpt completion requestId:{}, user:{}, costFee:{}, result:{}", requestId, user, costFee, response);

        List<ChatCompletionChoice> choices = chatCompletionResult.getChoices();
        return choices.get(0).getMessage();
    }


    private BigDecimal costFee(ChatStreamBuffer chatStreamBuffer,
                         ChatCompletionRequest chatCompletionRequest,
                         String user, String requestId, String responseModel) {
        if (chatStreamBuffer.getContent()==null || StringUtils.isBlank(chatStreamBuffer.getContent().toString())) {
            return BigDecimal.ZERO;
        }

        // 获取提问
        String question = getAllQuestion(chatCompletionRequest.getMessages());
        // 计算提问消耗的token
        Long promptTokens = calculateTokenService.getTokenQty(question);
        // 计算回复消耗的token
        Long completionTokens = calculateTokenService.getTokenQty(chatStreamBuffer.getContent().toString());
        // 计算费用
        BigDecimal costFee = costService.calculateNeedCostFee(ChatGPTModelEnum.getByModelName(responseModel), promptTokens, completionTokens);
        CHAT_LOGGER.info("calculate Fee success, requestId:{}, user:{}, costFee:{}, responseModel:{}, request:{}, result:{}",
                requestId, user, costFee, responseModel, JSON.toJSONString(chatCompletionRequest), chatStreamBuffer.getContent().toString());
        return costFee;
    }

    private Pair<String, Map<String, String>> getRequestUrlAndHeaders(String serverUrl, boolean stressTest){
        String openaiApiKey = configService.getConfigByKey(AppConstants.CONFIG_KEY_OPENAI_API_KEY, false);
        String uri = stressTest ? "/openai/mockChatCompletion" : "/openai/ChatCompletion";
        String requestUrl = null;
        Map<String, String> headers = new HashMap<>();

        //特殊情况，如果服务器为codegencore开头，说明是在使用codegencore的openapi，会有些特殊处理
        if(serverUrl.startsWith("https://codegencore")){
            requestUrl = serverUrl;
            headers.put(AppConstants.HTTP_HEADER_CODEGPT_USER_KEY, AppConstants.CODEGPT_TOKEN_USER);
            headers.put(AppConstants.HTTP_HEADER_CODEGPT_TOKEN_KEY, defaultCodegptToken);
        }else{
            requestUrl = String.format("%s%s", serverUrl, uri);
            headers.put("Authorization", "Bearer " + openaiApiKey);
        }
        return Pair.of(requestUrl, headers);
    }

    private void beforeModelRequest(ChatCompletionRequest chatCompletionRequest){
        for(ChatMessage chatMessage : chatCompletionRequest.getMessages()){
            // 如果content为null，会导致后端报错，所以设置成空字符串
            if(chatMessage.getContent() ==  null){
                chatMessage.setContent("");
            }
        }
    }
}
