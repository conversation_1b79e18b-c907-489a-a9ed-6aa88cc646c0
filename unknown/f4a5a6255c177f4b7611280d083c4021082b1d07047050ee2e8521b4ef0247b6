package com.alipay.codegencore.model.request;

/**
 * 客户端请求抽象类
 * 客户端发起的请求需要包含以下所有属性
 *
 * <AUTHOR>
 * 创建时间 2022-03-01
 */
public abstract class AbstractClientModel {
    /**
     * 用户名
     */
    private String userLabel;
    /**
     * 用户目录
     */
    private String userDir;
    /**
     * 服务接口版本号
     */
    private String version;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUserDir() {
        return userDir;
    }

    public void setUserDir(String userDir) {
        this.userDir = userDir;
    }

    public String getUserLabel() {
        return userLabel;
    }

    public void setUserLabel(String userLabel) {
        this.userLabel = userLabel;
    }
}



