package com.alipay.codegencore.model.model.tool.learning.plugin.stage;

import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * 阶段中的结构化信息
 */
public class StageInfo {
    /**
     * 枚举类型，表示阶段信息类型
     */
    public enum StageInfoTypeEnum {
        INPUT, // 输入类型
        OUTPUT // 输出类型
    }


    // 输入
    private Object input;

    // 输出
    private Object output;

    public StageInfo() {
    }

    /**
     * 全参数构造函数
     * @param input
     * @param output
     */
    public StageInfo(Object input, Object output) {
        this.input = input;
        this.output = output;
    }

    /**
     * 根据输入输出构造StageInfo
     * @param stageType 阶段类型
     * @param stageInfoType 阶段信息类型，表示输入或者输出
     * @param infoFieldName 字段名
     * @param stageInfoData 字段数据
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws InstantiationException
     * @throws IllegalAccessException
     * @throws NoSuchFieldException
     */
    public StageInfo(StageTypeEnum stageType, StageInfoTypeEnum stageInfoType, String infoFieldName, Object stageInfoData) throws NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException, IntrospectionException {
        Class<?> clazz = stageInfoType == StageInfoTypeEnum.INPUT ? stageType.getInputSchema() : stageType.getOutputSchema();
        Object stageData = clazz.getDeclaredConstructor().newInstance();
        // 获得属性描述器
        PropertyDescriptor propertyDescriptor = new PropertyDescriptor(infoFieldName, clazz);
        // 获得set方法
        Method setMethod = propertyDescriptor.getWriteMethod();
        setMethod.invoke(stageData, stageInfoData);
        if (stageInfoType == StageInfoTypeEnum.INPUT) {
            input = stageData;
        } else {
            output = stageData;
        }
    }

    /**
     * 添加阶段信息
     * @param stageType 阶段类型
     * @param stageInfoType 阶段信息类型，表示输入或者输出
     * @param infoFieldName 字段名
     * @param stageInfoData 字段数据
     * @throws IllegalAccessException
     * @throws NoSuchFieldException
     */
    public void addInfo(StageTypeEnum stageType, StageInfoTypeEnum stageInfoType, String infoFieldName, Object stageInfoData) throws IllegalAccessException, IntrospectionException, InvocationTargetException {
        Class<?> clazz = stageInfoType == StageInfoTypeEnum.INPUT ? stageType.getInputSchema() : stageType.getOutputSchema();
        Object stageData = stageInfoType == StageInfoTypeEnum.INPUT ? input : output;
        // 获得属性描述器
        PropertyDescriptor propertyDescriptor = new PropertyDescriptor(infoFieldName, clazz);
        // 获得set方法
        Method setMethod = propertyDescriptor.getWriteMethod();
        setMethod.invoke(stageData, stageInfoData);
    }

    public Object getInput() {
        return input;
    }

    public void setInput(Object input) {
        this.input = input;
    }

    public Object getOutput() {
        return output;
    }

    public void setOutput(Object output) {
        this.output = output;
    }
}