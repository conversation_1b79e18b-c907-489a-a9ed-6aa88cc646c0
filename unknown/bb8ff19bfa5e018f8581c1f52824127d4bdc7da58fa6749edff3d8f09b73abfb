package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.CodeTemplateDOExample;
import com.alipay.codegencore.model.domain.CodeTemplateDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface CodeTemplateDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    long countByExample(CodeTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    int deleteByExample(CodeTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    @Delete({
            "delete from cg_code_template",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    @Insert({
            "insert into cg_code_template (biz_scene_enum, name, ",
            "rule, scene_desc)",
            "values (#{bizSceneEnum,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, ",
            "#{rule,jdbcType=VARCHAR}, #{sceneDesc,jdbcType=VARCHAR})"
    })
    @SelectKey(statement = "SELECT LAST_INSERT_ID() as id", keyProperty = "id", before = false, resultType = Long.class)
    int insert(CodeTemplateDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    int insertSelective(CodeTemplateDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    List<CodeTemplateDO> selectByExample(CodeTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    @Select({
            "select",
            "id, biz_scene_enum, name, rule, scene_desc",
            "from cg_code_template",
            "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("BaseResultMap")
    CodeTemplateDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    int updateByExampleSelective(@Param("record") CodeTemplateDO record, @Param("example") CodeTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    int updateByExample(@Param("record") CodeTemplateDO record, @Param("example") CodeTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    int updateByPrimaryKeySelective(CodeTemplateDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_code_template
     *
     * @mbg.generated Wed Apr 13 13:41:01 CST 2022
     */
    @Update({
            "update cg_code_template",
            "set biz_scene_enum = #{bizSceneEnum,jdbcType=TINYINT},",
            "name = #{name,jdbcType=VARCHAR},",
            "rule = #{rule,jdbcType=VARCHAR},",
            "scene_desc = #{sceneDesc,jdbcType=VARCHAR}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(CodeTemplateDO record);
}