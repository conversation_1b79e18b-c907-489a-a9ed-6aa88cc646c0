package com.alipay.codegencore.web.remote;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.web.remote.vo.RemoteAgentBaseResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/14 15:35
 */
@RestController
@RequestMapping("/v1/test")
public class RemoteTestController {

    @GetMapping("/{name}")
    public RemoteAgentBaseResponse test(@PathVariable("name") String name) {

        if (StringUtils.equals("jon", name)) {
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }

        return RemoteAgentBaseResponse.buildSuccess(name);
    }

}
