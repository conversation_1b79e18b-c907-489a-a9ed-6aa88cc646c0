package com.alipay.codegencore.model.model;

import java.util.List;

/**
 * 代码model
 *
 * <AUTHOR>
 * 创建时间 2022-01-14
 */
public class CodeModel implements Comparable<CodeModel>{
    /**
     * 源代码内容
     */
    private String content;
    /**
     * 生成代码所用的算法策略，为了防止后续策略变更导致序列化异常，此处为string类型
     * {@link com.alipay.codegencore.model.enums.AlgStrategyEnum}
     */
    private String algStrategyEnum;
    /**
     * 展示名
     */
    private String displayName;
    /**
     * 补全结果id
     */
    private Long id;
    /**
     * 待检查的代码列表
     * 由于可能包含多个待检查项，故为列表形式保存。例如 getUser().getId().
     */
    private List<CheckCodeModel> checkCodeModelList;
    /**
     * 模型得分
     */
    private double score = 0;


    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public List<CheckCodeModel> getCheckCodeModelList() {
        return checkCodeModelList;
    }

    public void setCheckCodeModelList(List<CheckCodeModel> checkCodeModelList) {
        this.checkCodeModelList = checkCodeModelList;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAlgStrategyEnum() {
        return algStrategyEnum;
    }

    public void setAlgStrategyEnum(String algStrategyEnum) {
        this.algStrategyEnum = algStrategyEnum;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public int compareTo(CodeModel o) {
        return Double.compare(o.score,this.score);
    }
}
