package com.alipay.codegencore.web.remote.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 16:33
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SessionCreateRequest {

    @JsonProperty(value = "metadata")
    private Map<String, Object> metadata;

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

}
