package com.alipay.codegencore.utils;

import com.alipay.codegencore.utils.http.HttpClient;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * GetBuilder 单测试
 */
public class PostBuilderTest extends AbstractTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostBuilderTest.class);


    @Test
    public void test_get_syncExecute() {
        String paramJson = "{\"biztype\":\"yuyanmonitorl\"}";
        String ret = HttpClient.post("https://collect.alipay.com/yuyan").content(paramJson)
                .header("Content-Type", "application/json;charset=UTF-8")
                .syncExecute(50000L);
        LOGGER.info("post ret:"+ret);
        Assert.assertTrue(StringUtils.isNotBlank(ret));
    }

}
