package com.alipay.codegencore.service.impl.answer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.mapper.AnswerIndexBuildJobMapper;
import com.alipay.codegencore.dal.mapper.AnswerIndexBuildTaskMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AnswerIndexBuildJobDO;
import com.alipay.codegencore.model.domain.AnswerIndexBuildTaskDO;
import com.alipay.codegencore.model.enums.AntcodePageTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ZsearchClientEnum;
import com.alipay.codegencore.model.enums.ZsearchIndexEnum;
import com.alipay.codegencore.model.enums.answer.IndexScopeType;
import com.alipay.codegencore.model.enums.answer.JobState;
import com.alipay.codegencore.model.enums.answer.SummaryStatus;
import com.alipay.codegencore.model.enums.answer.TaskState;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.links.CopilotButton;
import com.alipay.codegencore.model.response.answer.IndexBuildResponse;
import com.alipay.codegencore.model.response.answer.JobPullResponse;
import com.alipay.codegencore.service.answer.AnswerIndexService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.drm.SvatDrmConfig;
import com.alipay.codegencore.service.middle.tbase.TbaseCacheService;
import com.alipay.codegencore.service.middle.zsearch.ZSearchResult;
import com.alipay.codegencore.service.middle.zsearch.ZsearchCommonService;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.code.BloopSearchClient;
import com.alipay.codegencore.utils.code.CodeInsightClient;
import com.alipay.codegencore.utils.dingding.DingDingUtil;
import com.alipay.codegencore.utils.thread.ThreadPoolUtils;
import com.alipay.zsearch.core.query.BoolQueryBuilder;
import com.alipay.zsearch.core.query.QueryBuilders;
import com.alipay.zsearch.core.search.SearchSourceBuilder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanCopier;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 问答索引服务
 */
@Service
@Slf4j
public class AnswerIndexServiceImpl implements AnswerIndexService {

    /**
     * 索引构建,文件信息黑名单配置
     */
    private static final String INDEX_BUILD_FILE_REGEX_BLACKLIST_CONFIG = "INDEX_BUILD_FILE_REGEX_BLACKLIST_CONFIG";
    /**
     * 索引构建，文件信息白名单
     */
    private static final String INDEX_BUILD_FILE_REGEX_WHITELIST_CONFIG = "INDEX_BUILD_FILE_REGEX_WHITELIST_CONFIG";

    /**
     * 可以构建索引的仓库白名单配置
     * 配置多个正则匹配
     */
    private static final String INDEX_BUILD_REPO_WHITE_LIST = "INDEX_BUILD_REPO_WHITE_LIST";

    /**
     * 索引构建失败判断阈值配置
     */
    private static final String INDEX_BUILD_JOB_FAIL_THRESHOLD = "INDEX_BUILD_JOB_FAIL_THRESHOLD";

    /**
     * 默认超时时间 10分钟
     */
    private static final int DEFAULT_TIMEOUT_TEN_MINUTE = 600000;

    /**
     * 构建失败重试次数配置
     */
    private static final String INDEX_BUILD_FAIL_RETRY_COUNT = "INDEX_BUILD_FAIL_RETRY_COUNT";


    /**
     * 拉取job时锁key
     */
    private static final String PULL_JOB_LOCK_KEY = "PULL_JOB_LOCK_KEY";

    /**
     * 拉取待构建job等待标记
     */
    private static final String PULL_JOB_EMPTY_WAIT = "PULL_JOB_EMPTY_WAIT";

    /**
     * job队列
     */
    private static final String INDEX_BUILD_JOB_QUEUE = "INDEX_BUILD_JOB_QUEUE";

    /**
     * 增量索引构建请求
     */
    private static final String PART_BUILD_REQUEST_COUNT = "PART_BUILD_REQUEST_COUNT";

    /**
     * 索引构建 retry 次数
     */
    private static final String INDEX_BUILD_RETRY_COUNT_CACHE = "INDEX_BUILD_RETRY_COUNT:";

    /**
     * 以天为单位
     */
    private static final int PART_BUILD_REQUEST_EX = 3600;


    /**
     *
     */
    private static final String BUILD_TASK_CHECK_STATE_LIST = "BUILD_TASK_CHECK_STATE_LIST";

    /**
     * job 对象转换
     */
    private static final BeanCopier copier = BeanCopier.create(AnswerIndexBuildJobDO.class, JobPullResponse.class, false);

    /**
     * 检查批次分片数量
     */
    private static final int CHECK_JOB_BATCH_COUNT = 100;

    /**
     * 长超时时间的仓库列表
     */
    private static final String LONG_TIMEOUT_REPO_LIST = "LONG_TIMEOUT_REPO_LIST";

    @Autowired
    private AnswerIndexBuildTaskMapper answerIndexBuildTaskMapper;

    @Autowired
    private AnswerIndexBuildJobMapper answerIndexBuildJobMapper;

    @Autowired
    private ConfigService configService;

    @Autowired
    private TbaseCacheService tbaseCacheService;

    @Autowired
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource
    private ZsearchCommonService zsearchCommonService;

    @Autowired
    private SvatDrmConfig svatDrmConfig;


    @Override
    public IndexBuildResponse indexBuild(AntCodeClient.CommitInfo commitInfo, String repoURL, Integer priority) {

        final String lockKey = buildLockKey(commitInfo);
        boolean lock = tbaseCacheService.getLock(lockKey, 5000);
        if (!lock) {
            throw new BizException(ResponseEnum.ANSWER_REPO_BUILD_TOO_OFTEN);
        }

        try {
            //查询该仓库分支构建任务是否已经存在
            AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getByRepoInfo(commitInfo.getGroupPath(),
                    commitInfo.getProjectPath(), commitInfo.getBranch());

            if (taskDO == null) {
                taskDO = indexBuildTaskSave(repoURL, commitInfo, priority);
            }

            taskDO.setPriority(priority);
            return buildWithRepo(taskDO, commitInfo, TaskState.FULL_BUILDING.name(), repoURL);

        } finally {
            tbaseCacheService.releaseLock(lockKey);
        }
    }

    @Override
    public List<JobPullResponse> pullJobs(String flag, int size, String handleInfo) {

        //消费者身份认证
        if (!allowPull(flag)) {
            log.warn("not allow pull job");
            return List.of();
        }

        //这里异步进行下job check
        ThreadPoolUtils.indexBuildExecute(this::exceptionJobCheck);

        List<JobPullResponse> jobList = tbaseCacheService.lPopList(INDEX_BUILD_JOB_QUEUE, size, JobPullResponse.class);
        if (CollectionUtils.isNotEmpty(jobList)) {
            //更新job状态为构建中
            List<Long> jobIds = jobList.stream().map(JobPullResponse::getId).collect(Collectors.toList());
            int count = answerIndexBuildJobMapper.markJobBuilding(jobIds, handleInfo);
            log.info("update job size:{};{}; detail:{}", jobIds.size(), count, jobIds);

            return jobList;
        }

        if (PULL_JOB_EMPTY_WAIT.equals(tbaseCacheService.getCache(PULL_JOB_EMPTY_WAIT))) {
            log.info("no data, need wait");
            return List.of();
        }

        //加载部分为处理的job到队列
        boolean lock = tbaseCacheService.getLock(PULL_JOB_LOCK_KEY, 2000);
        if (!lock) {
            //没获取到就返回空列表,表示本轮轮空不处理
            return List.of();
        }

        try {

            //先查 task
            List<AnswerIndexBuildTaskDO> taskList = answerIndexBuildTaskMapper.queryBuildingTask(TaskState.FULL_BUILDING.name(),
                    IndexScopeType.FILE.name(), codeGPTDrmConfig.getPullJobTaskSize());
            log.info("full building tasks: {}", taskList.size());
            List<AnswerIndexBuildTaskDO> partBuildTaskList = answerIndexBuildTaskMapper.queryBuildingTask(TaskState.PART_BUILDING.name(),
                    null, codeGPTDrmConfig.getPullJobTaskSize() * 5);
            log.info("part building tasks: {}", partBuildTaskList.size());

            taskList.addAll(partBuildTaskList);
            if (CollectionUtils.isEmpty(taskList)) {
                log.info("not found need build index task");
                //如果没有需要构建索引的job,则锁定300秒内不再查表,直接返回空列表即可
                tbaseCacheService.putCache(PULL_JOB_EMPTY_WAIT, PULL_JOB_EMPTY_WAIT, 5);
                return List.of();
            }

            List<Long> taskIdList = taskList.stream().map(AnswerIndexBuildTaskDO::getId).collect(Collectors.toList());
            log.info("building task:{}", taskIdList);
            List<AnswerIndexBuildJobDO> jobDOList = answerIndexBuildJobMapper.getNeedBuildJobs(taskIdList, 500);
            if (CollectionUtils.isEmpty(jobDOList)) {
                log.info("not found need build index job");
                //如果没有需要构建索引的job,则锁定300秒内不再查表,直接返回空列表即可
                tbaseCacheService.putCache(PULL_JOB_EMPTY_WAIT, PULL_JOB_EMPTY_WAIT, 5);
                return List.of();
            }

            Map<Long, String> taskMap = taskList.stream().collect(Collectors.toMap(AnswerIndexBuildTaskDO::getId, AnswerIndexBuildTaskDO::getLastBuildCommit));
            JobPullResponse[] jobPulls = jobDOList.stream().map(jobDO -> {
                JobPullResponse jobPullResponse = new JobPullResponse();
                copier.copy(jobDO, jobPullResponse, null);
                if (StringUtils.isBlank(jobPullResponse.getLastCommitId())) {
                    jobPullResponse.setLastCommitId(taskMap.get(jobPullResponse.getTaskId()));
                }
                return jobPullResponse;
            }).toArray(JobPullResponse[]::new);

            Long rPushRes = tbaseCacheService.rPushList(INDEX_BUILD_JOB_QUEUE, jobPulls);
            log.info("lpush job size:{}, res:{}", jobPulls.length, rPushRes);
        } catch (Exception e) {
            log.error("pull job failed", e);
        } finally {
            tbaseCacheService.releaseLock(PULL_JOB_LOCK_KEY);
        }

        return List.of();
    }

    @Override
    public boolean jobResult(Long jobId, Long taskId, Integer status, String message) {

        AnswerIndexBuildJobDO answerIndexBuildJobDO = answerIndexBuildJobMapper.getById(jobId);
        if (answerIndexBuildJobDO == null) {
            log.error("job result {} not found", jobId);
            return false;
        }

        handleExceptionJob(List.of(answerIndexBuildJobDO), status, message);

        taskFinishCheck(List.of(taskId));

        return true;
    }

    @Override
    public boolean repoBuildState(String groupPath, String projectPath, String branch) {
        AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getByRepoInfo(groupPath, projectPath, branch);
        if (taskDO == null) {
            return false;
        }
        return !TaskState.INIT.equalsName(taskDO.getState());
    }

    @Override
    public AnswerIndexBuildTaskDO getTask(String groupPath, String projectPath, String branch) {
        return answerIndexBuildTaskMapper.getByRepoInfo(groupPath, projectPath, branch);
    }

    @Override
    public void repoChangeHandle(JSONObject pushEventObject) {

        JSONObject objectAttributes = pushEventObject.getJSONObject("object_attributes");
        if (objectAttributes == null) {
            return;
        }

        String eventType = objectAttributes.getString("event_type");
        log.info("ant code callback event:{}", eventType);
        if (StringUtils.isBlank(eventType) || !StringUtils.equals("Push", eventType)) {
            return;
        }

        String ref = objectAttributes.getString("ref");
        if (StringUtils.isBlank(ref)) {
            return;
        }
        String branch = ref.substring("ref/heads/".length() + 1);
        String commit = objectAttributes.getString("checkout_sha");
        JSONObject repositoryInfo = pushEventObject.getJSONObject("repository");
        if (repositoryInfo == null) {
            return;
        }
        String sshStyleRepoUrl = repositoryInfo.getString("git_ssh_url");
        Pair<String, String> gpPath = AntCodeClient.getGroupAndProjectPathByRepoUrl(sshStyleRepoUrl);

        log.info("repo:{} branch:{}", gpPath, branch);
        AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getByRepoInfo(gpPath.getLeft(),
                gpPath.getRight(), branch);
        if (taskDO == null || !TaskState.FINISH.equalsName(taskDO.getState())) {
            return;
        }

        AntCodeClient.CommitInfo commitInfo = new AntCodeClient.CommitInfo();
        commitInfo.setGroupPath(gpPath.getLeft());
        commitInfo.setProjectPath(gpPath.getRight());
        commitInfo.setBranch(branch);
        commitInfo.setCommitId(commit);

        buildWithRepo(taskDO, commitInfo, TaskState.PART_BUILDING.name(),
                AntCodeClient.buildRepoURL(commitInfo.getGroupPath(), commitInfo.getProjectPath()));

    }

    /**
     * 仓库校验
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    public AntCodeClient.CommitInfo repoCheck(String groupPath, String projectPath, String branch) {
        //仓库白名单校验
        List<Pattern> patternList = getPatternsByConfig(INDEX_BUILD_REPO_WHITE_LIST);
        if (CollectionUtils.isNotEmpty(patternList)) {
            String repoURL = AntCodeClient.buildRepoURL(groupPath, projectPath);
            boolean noneMatch = patternList.stream().noneMatch(pattern -> pattern.matcher(repoURL).matches());
            if (noneMatch) {
                throw new BizException(ResponseEnum.ANSWER_REPO_NOT_ALLOW);
            }
        }

        //仓库本身校验
        AntCodeClient.CommitInfo commitInfo = AntCodeClient.getProjectLastCommitInfo(groupPath, projectPath, branch);
        if (commitInfo == null) {
            throw new BizException(ResponseEnum.ANSWER_REPO_NOT_FOUND);
        }
        return commitInfo;
    }


    @Override
    public List<String> getQuestion(String groupPath, String projectPath, String branch) {

        AnswerIndexBuildTaskDO answerIndexBuildTaskDO = answerIndexBuildTaskMapper.getQuestion(groupPath, projectPath, branch);
        // 默认推荐回答
        if (Objects.isNull(answerIndexBuildTaskDO)
                || StringUtils.isBlank(answerIndexBuildTaskDO.getQuestion())) {
            String antcodeCopilotRecommendQuestionConfigStr = configService.getConfigByKey(AppConstants.CONFIG_KEY_ANTCODE_COPILOT_RECOMMEND_QUESTION_CONFIG, false);
            if(StringUtils.isBlank(antcodeCopilotRecommendQuestionConfigStr)){
                return List.of();
            }
            JSONObject recommendQuestionInfo = JSONObject.parseObject(antcodeCopilotRecommendQuestionConfigStr);
            if(recommendQuestionInfo == null){
                return List.of();
            }
            JSONArray recommendQuestions = recommendQuestionInfo.getJSONArray(AntcodePageTypeEnum.REPO.name().toUpperCase());
            if (recommendQuestions == null) {
                return new ArrayList<>();
            }
            List<CopilotButton> copilotButtons = JSON.parseArray(recommendQuestions.toJSONString(), CopilotButton.class);
            List<String> defaultQuestions = new ArrayList<>();
            for (CopilotButton copilotButton : copilotButtons) {
                defaultQuestions.add(copilotButton.getQuery());
            }
            return defaultQuestions;
        }

        String[] questions = StringUtils.split(answerIndexBuildTaskDO.getQuestion(), "\n");
        return Arrays.asList(questions);
    }

    @Override
    public void indexBuildCallback(JSONObject callbackData) {

        JSONObject data = callbackData.getJSONObject("data");
        if (data == null) {
            log.warn("callback not data, need check");
            return;
        }

        //先只关注 success 状态，其他错误状态先打印错误日志，后续扩展任务状态支持
        String status = data.getString("status");
        JSONObject extraData = callbackData.getJSONObject("extra_data");
        if (extraData == null
                || !extraData.containsKey(CodeInsightClient.BUILD_TASK_EX_ID)) {
            log.warn("callback not task id. need check");
            return;
        }

        Long taskId = extraData.getLong(CodeInsightClient.BUILD_TASK_EX_ID);
        log.info("callback task:{} status {}", taskId, status);
        if (CodeInsightClient.TaskStatus.success.equalsIgnoreCase(status)) {
            //更新任务状态
            int taskUpdateResult = answerIndexBuildTaskMapper.updateTaskFinish(taskId, new Timestamp(System.currentTimeMillis()));
            if (taskUpdateResult == 0) {
                log.error("update task:{} fail. please check!", taskId);
            }
        } else {
            if (CodeInsightClient.TaskStatus.needRetry(status)) {
                AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getById(taskId);
                indexBuildRepoRetry(taskDO);
            }
        }
    }

    @Override
    public void wikiBuildCallback(JSONObject callbackData) {
        if (callbackData == null) {
            log.warn("wiki callback data is null");
            return;
        }

        Long taskId = callbackData.getLong("taskId");
        String recommendQuestions = callbackData.getString("recommendQuestions");
        Boolean wikiResult = callbackData.getBoolean("wikiResult");

        if (taskId == null) {
            log.error("wiki callback taskId is null");
            return;
        }

        String summaryStatus = BooleanUtils.toBooleanDefaultIfNull(wikiResult, false) ?
                SummaryStatus.SUCCESS.name() : SummaryStatus.FAIL.name();

        int count = answerIndexBuildTaskMapper.updateWikiInfo(taskId, summaryStatus, recommendQuestions);
        if (count <= 0) {
            log.error("update task: {} wiki info: {};{} failed", taskId, wikiResult, recommendQuestions);
        }
    }

    @Override
    public List<Long> getRetryJobsByTask(Long taskId) {
        List<AnswerIndexBuildJobDO> answerIndexBuildJobDOS = answerIndexBuildJobMapper.queryFailJobs(taskId);
        List<Long> jobIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(answerIndexBuildJobDOS)) {
            for (AnswerIndexBuildJobDO answerIndexBuildJobDO : answerIndexBuildJobDOS) {
                String failMessage = answerIndexBuildJobDO.getFailMessage();
                if(StringUtils.isBlank(failMessage)||needRetry(failMessage)){
                    jobIdList.add(answerIndexBuildJobDO.getId());
                }
            }
        }
        return jobIdList;
    }

    @Override
    public void retryJobs(List<Long> jobIds, Long taskId, JobState targetState) {
        if(jobIds.size()>100){
            List<List<Long>> lists = Lists.partition(jobIds,100);
            for (List<Long> list : lists) {
                answerIndexBuildJobMapper.retryJobs(list, targetState.name());
            }
        }else {
            answerIndexBuildJobMapper.retryJobs(jobIds, targetState.name());
        }
        answerIndexBuildTaskMapper.updateTaskBuilding(taskId, TaskState.FULL_BUILDING.name(), null, null, new Timestamp(System.currentTimeMillis()));
    }

    @Override
    public List<AnswerIndexBuildJobDO> getJobByIds(List<Long> jobIds) {
        return answerIndexBuildJobMapper.getJobByIds(jobIds);
    }

    @Override
    public List<Triple<Long, Integer, Long>> resetFailJobHandle(List<Long> taskIds, String failMessage) {
        if (CollectionUtils.isEmpty(taskIds)) {
            throw new BizException(ResponseEnum.ERROR_THROW, "taskId list is empty");
        }
        if (StringUtils.isBlank(failMessage)) {
            throw new BizException(ResponseEnum.ERROR_THROW, "fail message is blank");
        }

        List<Triple<Long, Integer, Long>> result = Lists.newArrayList();
        taskIds.forEach(taskId -> {
            try {
                long jobCount = answerIndexBuildJobMapper.resetFailHandle(taskId, failMessage);
                int taskCount = 0;
                if (jobCount > 0) {
                    taskCount = answerIndexBuildTaskMapper.updateTaskBuilding(taskId, TaskState.PART_BUILDING.name(), null, null, new Timestamp(System.currentTimeMillis()));
                }
                result.add(Triple.of(taskId, taskCount, jobCount));
            } catch (Exception e) {
                log.error("task:{} reset found exception", taskId, e);
            }
        });

        return result;
    }

    @Override
    public Pair<Long, String> getIndexStatus(String repoPath, String branch) {
        Pair<String, String> repoInfo = AntCodeClient.getRepoInfoByRepoPath(repoPath);
        if (repoInfo == null) {
            throw new BizException(ResponseEnum.SVAT_REPO_INFO_ILL);
        }
        AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getByRepoInfo(repoInfo.getLeft(), repoInfo.getRight(), branch);
        if (taskDO == null) {
            throw new BizException(ResponseEnum.ANSWER_REPO_NOT_INDEX);
        }
        return Pair.of(taskDO.getId(), taskDO.getState());
    }

    @Override
    public List<AnswerIndexBuildTaskDO> getTaskByIdAndTime(Long id, String startTime, String endTime) {
        log.info("getTaskByIdAndTime:{} {} {}", id, startTime, endTime);

        try {
            return answerIndexBuildTaskMapper.getTaskByIdAndTime(id, startTime, endTime);
        } catch (Exception e) {
            log.info("getTaskByIdAndTime:{} ：{}： {},error：{}", id, startTime, endTime, e.getMessage());
            return null;
        }

    }

    @Override
    public List<String> checkAndNotifyZSearchStatus(Long id, String startTime, String endTime) {
        log.info("updateTaskStatus:{} {} {}", id, startTime, endTime);

        boolean lock = tbaseCacheService.getLock(AppConstants.REPO_FILE_CHECK_LOCK, 60 * 1000);
        if (!lock) {
            log.info("RepoIndexServiceListener.handle.getLock failed");
            return null;
        }
        List<String> message = new ArrayList<>();
        try {
            List<AnswerIndexBuildTaskDO> taskByIdAndTime = this.getTaskByIdAndTime(id, startTime, endTime);

            if (taskByIdAndTime == null || taskByIdAndTime.isEmpty()) {
                log.info("updateTaskStatus:{} ：{}： {}", id, startTime, endTime);
                return message;
            }
            LocalTime currentTime = LocalTime.now();
            LocalTime thresholdTime = LocalTime.of(22, 0);
            boolean isAfterTenPM = currentTime.isAfter(thresholdTime);

            AtomicInteger rebuildCount = new AtomicInteger();
            AtomicInteger cleanCount = new AtomicInteger();
            while (true) {

                taskByIdAndTime.forEach(answerIndexBuildTaskDO -> {
                    log.info("RepoIndexServiceListener.handle.answerIndexBuildTaskDO ：{}", JSONObject.toJSONString(answerIndexBuildTaskDO));

                    if (answerIndexBuildTaskDO.getTaskRecordId() == 0) {
                        log.info("RepoIndexServiceListener.handle.answerIndexBuildTaskDO.taskRecordId is 0");
                        return;
                    }
                    //获取仓库构建信息
                    JSONObject taskCount = BloopSearchClient.getTaskDetail(answerIndexBuildTaskDO.getTaskRecordId(),
                            answerIndexBuildTaskDO.getRepoUrl(), answerIndexBuildTaskDO.getBranch(),
                            answerIndexBuildTaskDO.getLastBuildCommit());
                    if (taskCount == null) {
                        log.info("RepoIndexServiceListener.handle.taskCount is null ");
                        return;
                    }
                    if (taskCount.isEmpty()) {
                        log.info("RepoIndexServiceListener.handle.taskCount is empty");
                        //获取仓库构建信息失败直接发起重新构建
                        rebuildCount.getAndIncrement();
                        if (codeGPTDrmConfig.isEnableAnswerIndexBuild() && isAfterTenPM) {
                            answerIndexBuildTaskDO.setState(TaskState.FULL_BUILDING.name());
                            indexBuildRepoRetry(answerIndexBuildTaskDO);

                        }
                        return;
                    }
                    Long success = taskCount.getLong("success");

                    if (success == 0L) {
                        log.info("updateTaskStatus success is 0 :{} ", answerIndexBuildTaskDO.getRepoUrl());
                        return;
                    }
                    //获取仓库ZSearchSize
                    ZSearchResult<HashMap> hashMapZSearchResult = getHashMapZSearchResult(answerIndexBuildTaskDO);

                    if (!hashMapZSearchResult.getItem().equals(success)) {
                        message.add(String.format("id :%s branches:%s projectSuccessSize:%s  ZSearchSize:%s repo_ref :%s",
                                answerIndexBuildTaskDO.getId(), answerIndexBuildTaskDO.getBranch(), success, hashMapZSearchResult.getItem(),
                                answerIndexBuildTaskDO.getRepoUrl()));
                        rebuildCount.getAndIncrement();
                        if (codeGPTDrmConfig.isEnableAnswerIndexBuild() && isAfterTenPM) {
                            if (cleanCount.intValue() < codeGPTDrmConfig.getAnswerIndexCleanSize() && success < hashMapZSearchResult
                                    .getItem()) {
                                log.info("checkAndNotifyZSearchStatus clean id:{} repoUrl:{}", answerIndexBuildTaskDO.getId(),
                                        answerIndexBuildTaskDO.getRepoUrl());
                                cleanCount.getAndIncrement();
                                BloopSearchClient.cleanByProject(answerIndexBuildTaskDO.getTaskRecordId(),
                                        answerIndexBuildTaskDO.getRepoUrl(),
                                        answerIndexBuildTaskDO.getBranch());
                                answerIndexBuildTaskDO.setState(TaskState.FULL_BUILDING.name());
                                indexBuildRepoRetry(answerIndexBuildTaskDO);
                            }
                            if (success > hashMapZSearchResult.getItem()) {
                                answerIndexBuildTaskDO.setState(TaskState.FULL_BUILDING.name());
                                indexBuildRepoRetry(answerIndexBuildTaskDO);
                            }

                        }

                    }
                });

                if (taskByIdAndTime.size() < 100) {
                    break;
                }
                log.info("RepoIndexServiceListener.handle.taskByIdAndTime.size() = 100");
                taskByIdAndTime = this.getTaskByIdAndTime(taskByIdAndTime.get(taskByIdAndTime.size() - 1).getId(), startTime, endTime);
            }
            //发送钉钉消息
            sendMessage(startTime, endTime, message, rebuildCount,isAfterTenPM,cleanCount);
        } finally {
            tbaseCacheService.releaseLock(AppConstants.REPO_FILE_CHECK_LOCK);
        }
        return message;
    }

    /**
     * 发送钉钉消息
     *
     * @param startTime
     * @param endTime
     * @param message
     * @param rebuildCount
     */
    private void sendMessage(String startTime, String endTime, List<String> message, AtomicInteger rebuildCount, boolean isAfterTenPM,AtomicInteger cleanCount) {
        if (!message.isEmpty()) {
            log.info("RepoIndexServiceListener.handle.message :{}", JSONObject.toJSONString(message));
            String format = String.format("时间:%s-%s 问题数据%s数量：%s clean仓库数:%s ZSearchSize与应插入的projectSize不一致仓库共:%s个 前5个：%s ", startTime,
                    endTime, isAfterTenPM ? "发起重建" : "", rebuildCount,cleanCount, message.size(),
                    JSONObject.toJSONString(message.size() > 5 ? message.subList(0, 5) : message));
            DingDingUtil.sendMessage(format);
        }
        else {
            DingDingUtil.sendMessage(
                    String.format("时间:%s-%s 问题数据%s数量：%s  任务检查无ZSearchCount不一致仓库", endTime, startTime, isAfterTenPM ? "发起重建" : "",
                            rebuildCount));
        }
    }

    /**
     * 获取ZSearchResult<HashMap>
     *
     * @param answerIndexBuildTaskDO
     * @return
     */
    private ZSearchResult<HashMap> getHashMapZSearchResult(AnswerIndexBuildTaskDO answerIndexBuildTaskDO) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Triple<String,String,String> hostAndGroupAndProjectPathByRepoUrl = AntCodeClient.getHostAndGroupAndProjectPathByRepoUrl(
                answerIndexBuildTaskDO.getRepoUrl());
        String repoRef = "code.alipay.com/" + hostAndGroupAndProjectPathByRepoUrl.getMiddle() + "/"
                + hostAndGroupAndProjectPathByRepoUrl.getRight();
        boolQueryBuilder.must(QueryBuilders.termQuery("repo_ref", repoRef));
        boolQueryBuilder.must(QueryBuilders.termsQuery("branches", answerIndexBuildTaskDO.getBranch()));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        ZSearchResult<HashMap> hashMapZSearchResult = zsearchCommonService.queryDataWithPage(
                ZsearchIndexEnum.PYBLOOP_CODE_PATH_REMOTE_BGE_SMALL_EN_384
                , searchSourceBuilder, 0, HashMap.class, ZsearchClientEnum.INDEPENDENT);

        log.info("RepoIndexServiceListener.handle.hashMapZSearchResult.getItem() :{}", hashMapZSearchResult.getItem());
        return hashMapZSearchResult;
    }


    @Override
    public void repoTaskCheck() {
        String stateLimitMapString = configService.getConfigByKey(BUILD_TASK_CHECK_STATE_LIST, false);
        log.info("repo task check:{}", stateLimitMapString);
        if (StringUtils.isBlank(stateLimitMapString)) {
            return;
        }

        JSONObject stateLimitMap = JSON.parseObject(stateLimitMapString);
        if (MapUtils.isEmpty(stateLimitMap)) {
            return;
        }

        Set<String> stateSet = stateLimitMap.keySet();
        for (String state : stateSet) {
            Integer limit = stateLimitMap.getInteger(state);
            log.info("开始任务检查 state:{} limit:{}", state, limit);
            repoTaskCheck(state, limit);
        }
    }


    @Override
    public void repoTaskCheck(String state, Integer limit) {
        //查询出来构建中的任务列表
        List<AnswerIndexBuildTaskDO> taskDOList = answerIndexBuildTaskMapper.queryBuildingTask(state, IndexScopeType.REPO.name(), limit);
        log.info("wait check task:{}", taskDOList.size());

        if (TaskState.INIT.equalsName(state)) {
            taskDOList.forEach(taskDO -> {
                log.info("task:{} is init. retry", taskDO.getId());
                if (CodeInsightClient.limited()) {
                    return;
                }
                taskDO.setState(TaskState.FULL_BUILDING.name());
                indexBuildRepoRetry(taskDO);
            });
        } else {

            taskDOList.forEach(taskDO -> {
                //查询任务状态
                if (taskDO.getTaskRecordId() <= 0) {
                    log.info("task:{} not record id. skip, retry", taskDO.getId());
                    if (CodeInsightClient.limited()) {
                        return;
                    }
                    indexBuildRepoRetry(taskDO);
                    return;
                }

                String taskStatus = CodeInsightClient.searchTaskStatus(taskDO.getTaskRecordId());
                log.info("task:{} search status:{}", taskDO, taskStatus);
                if (CodeInsightClient.TaskStatus.needRetry(taskStatus)) {
                    if (CodeInsightClient.limited()) {
                        return;
                    }
                    indexBuildRepoRetry(taskDO);
                } else if (CodeInsightClient.TaskStatus.isSuccess(taskStatus)) {
                    //更新任务状态
                    int taskUpdateResult = answerIndexBuildTaskMapper.updateTaskFinish(taskDO.getId(), new Timestamp(System.currentTimeMillis()));
                    log.info("task:{} taskStatus:{} update finish result:{}", taskDO.getId(), taskStatus, taskUpdateResult);
                }
            });
        }
    }

    @Override
    public JSONObject queryBuildProgress(String repoURL, String branch) {
        Pair<String, String> repoInfo = AntCodeClient.getGroupAndProjectPathByRepoUrl(repoURL);
        if (repoInfo == null) {
            throw new BizException(ResponseEnum.ANSWER_REPO_ARG_ERR);
        }
        AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getByRepoInfo(repoInfo.getLeft(), repoInfo.getRight(), branch);
        if (taskDO == null) {
            throw new BizException(ResponseEnum.ANSWER_REPO_NOT_INDEX);
        }
        double progress = BloopSearchClient.getTaskProgress(taskDO.getTaskRecordId(),
                taskDO.getRepoUrl(), branch, taskDO.getLastBuildCommit());
        JSONObject taskDetail = BloopSearchClient.getTaskDetail(taskDO.getTaskRecordId(),
                taskDO.getRepoUrl(), branch, taskDO.getLastBuildCommit());
        log.info("task:{} repoURL:{} branch:{} progress:{}", taskDO.getId(), repoURL, branch, progress);
        JSONObject result = new JSONObject();
        result.put("progress", progress);
        result.put("detail", taskDetail);
        result.put("recordId", taskDO.getTaskRecordId());
        result.put("taskId", taskDO.getId());
        result.put("repoURL", repoURL);
        result.put("branch", branch);
        return result;
    }

    @Override
    public void rebuild(AntCodeClient.CommitInfo commitInfo) {

        AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getByRepoInfo(commitInfo.getGroupPath(),
                commitInfo.getProjectPath(), commitInfo.getBranch());
        if (taskDO == null) {
            throw new BizException(ResponseEnum.ANSWER_REPO_NOT_INDEX);
        }
        taskDO.setState(TaskState.FULL_BUILDING.name());
        indexBuildRepoRetry(taskDO);
    }

    /**
     * 插入任务记录
     * @param repoURL
     * @param commitInfo
     */
    private AnswerIndexBuildTaskDO indexBuildTaskSave(String repoURL,
                                                      AntCodeClient.CommitInfo commitInfo,
                                                      Integer priority) {
        AnswerIndexBuildTaskDO taskDO = new AnswerIndexBuildTaskDO();
        taskDO.setBranch(commitInfo.getBranch());
        taskDO.setLastBuildCommit(commitInfo.getCommitId());
        taskDO.setState(TaskState.INIT.name());
        taskDO.setSummaryStatus(SummaryStatus.INIT.name());
        taskDO.setGroupPath(commitInfo.getGroupPath());
        taskDO.setProjectPath(commitInfo.getProjectPath());
        taskDO.setRepoUrl(repoURL);
        taskDO.setBuildScopeType(IndexScopeType.REPO.name());
        taskDO.setPriority(priority);
        try {
            answerIndexBuildTaskMapper.insertTask(taskDO);
            log.info("save index build taskId:{}", taskDO.getId());
        } catch (Exception e) {
            log.error("maybe reusable create task:{}", repoURL, e);
            taskDO = answerIndexBuildTaskMapper.getByRepoInfo(commitInfo.getGroupPath(),
                    commitInfo.getProjectPath(), commitInfo.getBranch());
            if (taskDO == null) {
                throw new BizException(ResponseEnum.ANSWER_BUILD_TASK_CREATE_FAIL);
            }
        }
        return taskDO;
    }

    /**
     * 将索引构建任务拆分成job
     * @param taskId
     * @param lastCommitId
     * @param taskState
     * @param commitInfo
     */
    private void indexBuildTaskSplitJob(Long taskId, String lastCommitId,
                                        String taskState, AntCodeClient.CommitInfo commitInfo,
                                        Boolean reBuild) {

        List<AntCodeClient.FileCompare> fileCompareList;
        if (TaskState.PART_BUILDING.equalsName(taskState)) {
            //有新增的commit
            fileCompareList = AntCodeClient.getFileComparesBetweenTwoCommit(commitInfo.getGroupPath(),
                    commitInfo.getProjectPath(), lastCommitId, commitInfo.getCommitId());
        } else {
            fileCompareList = AntCodeClient.getFileListByProject(commitInfo.getGroupPath(),
                    commitInfo.getProjectPath(), commitInfo.getBranch());
        }
        log.info("index build file compare task:{} state:{} count:{}", taskId, taskState, fileCompareList.size());

        //先支持后缀匹配,后面看是否改成正则匹配.进行过滤，过滤掉不需要索引构建的文件
        fileCompareList = filterIllegalFile(fileCompareList);

        if (CollectionUtils.isEmpty(fileCompareList)) {
            //无效提交，不处理
            return;
        }

        if (reBuild) {
            //清理下 job
            long count = answerIndexBuildJobMapper.deleteById(taskId);
            log.info("rebuild task:{} delete job count:{}", taskId, count);
        }

        //将JOB入库
        List<AnswerIndexBuildJobDO> jobDOList = fileCompareList.stream()
                .map(fileCompare -> {
                    AnswerIndexBuildJobDO jobDO = new AnswerIndexBuildJobDO();
                    jobDO.setBranch(commitInfo.getBranch());
                    jobDO.setFileStatus(fileCompare.getStatus());
                    jobDO.setGroupPath(commitInfo.getGroupPath());
                    jobDO.setNewFilePath(fileCompare.getNewPath());
                    jobDO.setOldFilePath(fileCompare.getOldPath());
                    jobDO.setProjectPath(commitInfo.getProjectPath());
                    jobDO.setLastCommitId(commitInfo.getCommitId());
                    jobDO.setState(JobState.INIT.name());
                    jobDO.setTaskId(taskId);
                    return jobDO;
                }).collect(Collectors.toList());
        List<List<AnswerIndexBuildJobDO>> jobDOListGroup = Lists.partition(jobDOList, 50);
        jobDOListGroup.forEach(jobList -> {
            try {
                int count = answerIndexBuildJobMapper.batchInsertJob(jobList);
                log.info("index build split job:{}", count);
            } catch (Exception e) {
                log.error("save job failed:{}", jobList, e);
            }
        });

        //全部成功后,更新任务状态
        int count = answerIndexBuildTaskMapper.updateTaskBuilding(taskId, taskState, commitInfo.getCommitId(), null, new Timestamp(System.currentTimeMillis()));
        log.info("index build update task:{} state:{} res:{}", taskId, taskState, count);
        if (count == 0) {
            log.error("task:{} state:{} update failed. please check!", taskId, taskState);
        }

        // 清理 job 队列缓存标记
        tbaseCacheService.delKey(PULL_JOB_EMPTY_WAIT);
    }



    /**
     * 构建分布式锁key
     * @param commitInfo
     * @return
     */
    private String buildLockKey(AntCodeClient.CommitInfo commitInfo) {
        return commitInfo.getGroupPath() + commitInfo.getProjectPath() + commitInfo.getBranch();
    }

    /**
     * 异常job检查
     * @return
     */
    private void exceptionJobCheck() {

        //加锁,2秒钟检查一次
        boolean lock = tbaseCacheService.getLock("JOB_CHECK_LOCK", codeGPTDrmConfig.getJobPullCheckLockTime());
        log.debug("begin exception job check lock:{}", lock);
        if (!lock) {
            return;
        }

        //查询构建中的任务
        int limit = codeGPTDrmConfig.getPullJobTaskSize() * 5;
        List<Long> buildingTaskIds = answerIndexBuildTaskMapper.queryBuildingTaskId(TaskState.FULL_BUILDING.name(),
                IndexScopeType.FILE.name(), limit);
        log.info("found full building task:{}", buildingTaskIds);
        List<Long> partBuildingTaskIds = answerIndexBuildTaskMapper.queryBuildingTaskId(TaskState.PART_BUILDING.name(),
                null, limit);
        log.info("found part building task:{}", partBuildingTaskIds);

        buildingTaskIds.addAll(partBuildingTaskIds);
        if (CollectionUtils.isEmpty(buildingTaskIds)) {
            return;
        }

        // 批量异常 job、task 检查
        batchHandleExceptionJob(buildingTaskIds);

        //任务结束检查
        taskFinishCheck(buildingTaskIds);

    }

    /**
     * 批次分片 job、task 检查处理
     * @param buildingTaskIds
     */
    private void batchHandleExceptionJob(List<Long> buildingTaskIds) {

        if (CollectionUtils.isEmpty(buildingTaskIds)) {
            return;
        }


        String jobFailThreshold = configService.getConfigByKey(INDEX_BUILD_JOB_FAIL_THRESHOLD, false);
        log.info("config job fail threshold:{} s", jobFailThreshold);
        final long timeout = System.currentTimeMillis() - NumberUtils.toInt(jobFailThreshold, DEFAULT_TIMEOUT_TEN_MINUTE);

        List<List<Long>> buildingTaskIdsGroup = Lists.partition(buildingTaskIds, codeGPTDrmConfig.getPullJobTaskSize());
        buildingTaskIdsGroup.forEach(taskIds -> {

            try {
                List<AnswerIndexBuildJobDO> jobDOList = answerIndexBuildJobMapper.queryBuildingTimeout(taskIds, new Timestamp(timeout));

                List<List<AnswerIndexBuildJobDO>> jobDOListGroup = Lists.partition(jobDOList, CHECK_JOB_BATCH_COUNT);
                jobDOListGroup.forEach(jobList -> handleExceptionJob(jobList, null, null));

            } catch (Exception e) {
                log.error("batch job handle failed. task:{}", taskIds, e);
            }

        });
    }

    /**
     * job 异常情况处理
     * @param jobDOList
     * @param status
     * @param failMessage
     */
    private void handleExceptionJob(List<AnswerIndexBuildJobDO> jobDOList, Integer status, String failMessage) {

        if (CollectionUtils.isEmpty(jobDOList)) {
            log.info("not jobs handle");
            return;
        }

        //failMessage 处理
        if (StringUtils.isNotBlank(failMessage)) {
            //数据库字段限制 500
            failMessage = StringUtils.substring(failMessage, 0, 450);
        }

        //status = 1 表示处理正确
        if (status != null && status == 1) {
            List<Long> finishJobIdList = jobDOList.stream().map(AnswerIndexBuildJobDO::getId).collect(Collectors.toList());
            log.info("mark finish jobs:{}", finishJobIdList);
            if (CollectionUtils.isNotEmpty(finishJobIdList)) {
                int finishCount = answerIndexBuildJobMapper.markJobBuildingState(finishJobIdList, JobState.FINISH.name(), null);
                log.info("finish jobs:{}", finishCount);
            }
        } else {
            String jobFileRetryCount = configService.getConfigByKey(INDEX_BUILD_FAIL_RETRY_COUNT, false);
            log.info("config job fail retry:{}", jobFileRetryCount);
            final int maxRetryCount = NumberUtils.toInt(jobFileRetryCount, 2);


            List<Long> failJobIdList = jobDOList.stream()
                    .filter(job -> job.getFailCount() >= maxRetryCount)
                    .map(AnswerIndexBuildJobDO::getId)
                    .collect(Collectors.toList());
            log.info("mark fail jobs:{}", failJobIdList);
            if (CollectionUtils.isNotEmpty(failJobIdList)) {
                int failCount = answerIndexBuildJobMapper.markJobBuildingState(failJobIdList, JobState.FAIL.name(), failMessage);
                log.info("fail jobs:{}", failCount);
            }

            List<Long> retryJobIdList = jobDOList.stream()
                    .filter(job -> job.getFailCount() < maxRetryCount)
                    .map(AnswerIndexBuildJobDO::getId)
                    .collect(Collectors.toList());
            log.info("mark init retry jobs:{}", retryJobIdList);
            if (CollectionUtils.isNotEmpty(retryJobIdList)) {
                int initCount = answerIndexBuildJobMapper.markJobBuildingState(retryJobIdList, JobState.INIT.name(), failMessage);
                log.info("init jobs:{}", initCount);
            }
        }
    }

    /**
     * 任务是否结束检查
     * @param taskIdList
     */
    private void taskFinishCheck(List<Long> taskIdList) {
        log.info("check tasks:{}", taskIdList);
        if (CollectionUtils.isEmpty(taskIdList)) {
            return;
        }

        taskIdList.forEach(taskId -> {
            int needBuildJobCount = answerIndexBuildJobMapper.countNeedBuildByTaskId(taskId);
            log.info("task:{} need build count:{}", taskId, needBuildJobCount);
            if (needBuildJobCount == 0) {
                int taskUpdateResult = answerIndexBuildTaskMapper.updateTaskFinish(taskId, new Timestamp(System.currentTimeMillis()));
                if (taskUpdateResult == 0) {
                    log.error("update task:{} fail. please check!", taskId);
                }
            }
        });
    }

    /**
     * 文件内容过滤
     * @param fileCompareList
     */
    private List<AntCodeClient.FileCompare> filterIllegalFile(List<AntCodeClient.FileCompare> fileCompareList) {

        List<AntCodeClient.FileCompare> currentList = fileCompareList;

        //先匹配白名单
        List<Pattern> whitePatternList = getPatternsByConfig(INDEX_BUILD_FILE_REGEX_WHITELIST_CONFIG);
        //后匹配黑名单
        //先支持后缀匹配,后面看是否改成正则匹配.进行过滤，过滤掉不需要索引构建的文件
        List<Pattern> blackPatternList = getPatternsByConfig(INDEX_BUILD_FILE_REGEX_BLACKLIST_CONFIG);
        if (CollectionUtils.isNotEmpty(blackPatternList)) {
            currentList = fileCompareList.stream()
                    .filter(fileCompare -> whitePatternList.stream().anyMatch(pattern -> pattern.matcher(fileCompare.getNewPath()).matches()))
                    .filter(fileCompare -> blackPatternList.stream().noneMatch(pattern -> pattern.matcher(fileCompare.getNewPath()).matches()))
                    .collect(Collectors.toList());
        }

        log.info("index build file compare filter:{}", currentList.size());
        return currentList;
    }


    /**
     * 从配置中加载正则配置
     * @param configKey
     * @return
     */
    private List<Pattern> getPatternsByConfig(String configKey) {

        String regexConfigs = configService.getConfigByKey(configKey, false);
        if (StringUtils.isBlank(regexConfigs)) {
            return List.of();
        }

        List<String> regexList;
        try {
            regexList = JSON.parseArray(regexConfigs, String.class);
        } catch (Exception e) {
            log.warn("pattern config error. {}", configKey, e);
            regexList = List.of(regexConfigs);
        }

        return regexList.stream()
                .filter(StringUtils::isNotBlank)
                .map(Pattern::compile)
                .collect(Collectors.toList());
    }

    /**
     * 是否允许消费 job
     * @param flag
     * @return
     */
    private boolean allowPull(String flag) {
        //消费者身份认证
        String drmFlag = codeGPTDrmConfig.getAnswerIndexJobPullFlag();
        log.info("answer index job pull flag:{}.", drmFlag);

        if (StringUtils.isBlank(drmFlag)) {
            return true;
        }

        return StringUtils.equals(drmFlag, flag);
    }

    private void indexBuildRepoRetry(AnswerIndexBuildTaskDO taskDO) {
        //这里增加重试次数配置，超过次数限制则告警
        boolean retryLimited = retryLimited(taskDO.getId());
        if (retryLimited) {
            return;
        }

        final String groupPath = taskDO.getGroupPath();
        final String projectPath = taskDO.getProjectPath();
        final String branch = taskDO.getBranch();
        final String commitId = taskDO.getLastBuildCommit();
        // 增加分支和 commit 校验
        if (!AntCodeClient.branchCheck(groupPath, projectPath, branch)) {
            log.warn("branch not found, skip retry. {}/{} {}",
                    groupPath, projectPath, branch);
            return;
        }

        if (!AntCodeClient.commitCheck(groupPath, projectPath, commitId)) {
            log.warn("commit not found. skip retry. {}/{} {}",
                    groupPath, projectPath, commitId);
            return;
        }

        AntCodeClient.CommitInfo commitInfo = new AntCodeClient.CommitInfo();
        commitInfo.setBranch(branch);
        commitInfo.setCommitId(commitId);
        boolean result = indexBuildWithRepo(commitInfo, taskDO.getId(), taskDO.getState(), taskDO.getRepoUrl());
        log.info("task:{} repo build result:{}", taskDO.getId(), result);
    }

    /**
     * 重试次数限制
     * @param taskId
     * @return
     */
    private boolean retryLimited(Long taskId) {
        int maxRetryCount = codeGPTDrmConfig.getIndexBuildRetryCount();
        if (maxRetryCount > 0) {
            String key = INDEX_BUILD_RETRY_COUNT_CACHE + taskId;
            //重试次数限制 1 小时内统计
            Long count = tbaseCacheService.incrbyex(key, 0, 0, -1);
            if (count != null && count > maxRetryCount) {
                log.error("task:{} 超过最大重试次数:{}-{}", taskId, count, maxRetryCount);
                return true;
            }
            //进行加一，这样保障超时时间后可以再次发起重试
            tbaseCacheService.incrbyex(key, 1, 0, 3600);
        }
        return false;
    }

    /**
     * repo 级索引构建
     * @param commitInfo
     * @param taskId
     * @param repoURL
     * @return
     */
    private boolean indexBuildWithRepo(AntCodeClient.CommitInfo commitInfo,
                                       Long taskId, String state, String repoURL) {

        long scanRecordId = CodeInsightClient.createBuildTask(repoURL, commitInfo.getBranch(), commitInfo.getCommitId(),
                CodeInsightClient.TaskMask.index.getValue(), taskId, true, codeGPTDrmConfig.getCodeInsightCallBackEnv(),
                getIndexScanAction(state), getTaskTimeout(repoURL));

        if (scanRecordId > 0) {
            int count = answerIndexBuildTaskMapper.updateTaskBuilding(taskId, state,
                    commitInfo.getCommitId(), scanRecordId, new Timestamp(System.currentTimeMillis()));
            log.info("index build update task:{} res:{}", taskId, count);
            if (count == 0) {
                log.error("task:{} update failed. please check!", taskId);
            }
            return true;
        }
        return false;
    }

    /**
     * 获取索引构建 scanAction
     * @param taskState
     * @return
     */
    private String getIndexScanAction(String taskState) {
        boolean fullBuild = TaskState.FULL_BUILDING.equalsName(taskState);
        if (fullBuild) {
            return codeGPTDrmConfig.getIndexBuildScanAction();
        }
        return CodeInsightClient.getPartScanAction(codeGPTDrmConfig.getIndexBuildScanAction());
    }

    /**
     * 获取 ironman 特殊任务的超时时间
     * @param repoURL
     * @return
     */
    private Integer getTaskTimeout(String repoURL) {
        String repoListString = configService.getConfigByKey(LONG_TIMEOUT_REPO_LIST, false);
        log.info("repo list config:{}", repoListString);
        if (StringUtils.isBlank(repoListString)) {
            return null;
        }

        List<String> repoList = JSON.parseArray(repoListString, String.class);
        if (CollectionUtils.isEmpty(repoList)) {
            return null;
        }

        if (repoList.contains(repoURL)) {
            return codeGPTDrmConfig.getIronmanTaskTimeout();
        }
        return null;
    }

    /**
     * 仓库级别构建
     * @param taskDO
     * @param commitInfo
     * @param repoURL
     * @return
     */
    private IndexBuildResponse buildWithRepo(AnswerIndexBuildTaskDO taskDO, AntCodeClient.CommitInfo commitInfo,
                                             String state, String repoURL) {
        log.info("index build with repo");
        Long taskId = taskDO.getId();

        //非显式重建，则判断索引状态
        if (TaskState.FULL_BUILDING.equalsName(state)
                && !TaskState.INIT.equalsName(taskDO.getState())) {
            return new IndexBuildResponse(taskId, taskDO.getState());
        }

        if (TaskState.PART_BUILDING.equalsName(state)
                && !TaskState.FINISH.equalsName(taskDO.getState())) {
            return new IndexBuildResponse(taskId, taskDO.getState());
        }

        boolean result = indexBuildWithRepo(commitInfo, taskId, state, repoURL);
        String resultState = result ? state : taskDO.getState();
        return new IndexBuildResponse(taskId, resultState);
    }

    /**
     * 判断是否需要重跑任务
     *
     * <AUTHOR>
     * @since 2024.08.02
     * @param failMessage failMessage
     * @return boolean
     */
    private boolean needRetry(String failMessage){
        String jobRetryFailMsg = codeGPTDrmConfig.getJobRetryFailMsg();
        if(StringUtils.isNotBlank(jobRetryFailMsg)){
            List<String> retryMsg = JSONArray.parseArray(jobRetryFailMsg, String.class);
            for (String retryJobFailMessage : retryMsg) {
                if(failMessage.contains(retryJobFailMessage)){
                    return true;
                }
            }
        }
        return false;
    }

}


