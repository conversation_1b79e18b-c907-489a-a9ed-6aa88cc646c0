package com.alipay.codegencore.service.impl.model;

import com.alipay.zcache.serialize.BytesObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Queue;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.impl.model
 * @CreateTime : 2024-05-10
 */
@Service
public class StreamDataQueueUtilService {

    private static final Logger LOGGER = LoggerFactory.getLogger(
            StreamDataQueueUtilService.class);

    private final ConcurrentHashMap<String,Queue<Serializable>> map = new ConcurrentHashMap<>();

    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    private final ConcurrentHashMap<String,Long> lastAccessTimestamp = new ConcurrentHashMap<>();

    private static final long EXPIRATION_THRESHOLD = TimeUnit.MINUTES.toMillis(30);

    private static final StreamDataQueueUtilService instance = new StreamDataQueueUtilService();

    private final ReentrantLock lock = new ReentrantLock();

    private StreamDataQueueUtilService() {
        scheduler.scheduleWithFixedDelay(this::cleanup, 30, 30, TimeUnit.MINUTES);
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
    }

    public static StreamDataQueueUtilService getInstance() {
        return instance;
    }

    /**
     * 清理过期的队列
     */
    private void cleanup() {
        long currentTime = System.currentTimeMillis();
        lastAccessTimestamp.entrySet().removeIf(entry -> {
            String key = entry.getKey();
            long lastAccessTime = entry.getValue();
            if (currentTime - lastAccessTime > EXPIRATION_THRESHOLD) {
                map.remove(key);
                return true;
            }
            return false;
        });
    }

    /**
     * 添加元素到队列
     *
     * @param key
     * @param serializables
     */
    public void rpush(String key, Serializable... serializables) {
        Queue<Serializable> queue = map.computeIfAbsent(key, k -> new ConcurrentLinkedQueue<>());
        lock.lock();
        try {
            for (Serializable element : serializables) {
                queue.offer(element);
            }
        } finally {
            lock.unlock();
        }

        recordAccessTime(key);
    }

    /**
     * 重新设置队列的元素
     *
     * @param key
     * @param serializables
     */
    public String seset(String key, Serializable... serializables) {
        Queue<Serializable> queue = map.computeIfAbsent(key, k -> new ConcurrentLinkedQueue<>());
        lock.lock();
        try {
            queue.clear();
            for (Serializable element : serializables) {
                queue.offer(element);
            }
        } finally {
            lock.unlock();
        }
        recordAccessTime(key);
        return "OK";
    }

    /**
     * 从队列中取出一个元素
     *
     * @param key
     * @return
     */
    public String lpop(String key) {
        try {
            Queue<Serializable> queue = map.get(key);
            BytesObject item = (queue != null) ? (BytesObject) queue.poll() : null;
            return (item != null) ? StringUtils.toEncodedString(item.getBytes(), StandardCharsets.UTF_8) : null;
        } catch (Exception e) {
            // 记录日志并返回null
            LOGGER.error("lpop error, key:{}", key, e);
            return null;
        }
    }

    /**
     * 删除队列元素
     *
     * @param key
     * @return
     */
    public String del(String key) {
        map.remove(key);
        return "OK";
    }

    /**
     * 记录访问时间
     *
     * @param key
     */
    private void recordAccessTime(String key) {
        lastAccessTimestamp.put(key, System.currentTimeMillis());
    }

    public void shutdown() {
        scheduler.shutdown();
    }

}
