package com.alipay.codegencore.service.impl;

import com.alipay.codegencore.dal.example.TokenDOExample;
import com.alipay.codegencore.dal.mapper.TokenManualMapper;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.enums.ChatGPTModelEnum;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.codegpt.CostService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 计费的service实现
 */
@Service
public class CostServiceImpl implements CostService {

    @Resource
    private TokenManualMapper tokenManualMapper;

    // 达芬奇模型价格: 0.00002$/token
    private static final BigDecimal OPENAI_ONE_TOKEN_PRICE_DAVINCI = new BigDecimal("0.00002");
    // gpt-3.5-turbo模型价格: 0.000002$/token
    private static final BigDecimal OPENAI_ONE_TOKEN_PRICE_GPT_35_TURBO = new BigDecimal("0.000002");
    // gpt-4模型问题价格: 0.00003$/token
    private static final BigDecimal OPENAI_ONE_TOKEN_PRICE_GPT4_USER = new BigDecimal("0.00003");
    // gpt-4模型回答价格: 0.00006$/token
    private static final BigDecimal OPENAI_ONE_TOKEN_PRICE_GPT4_ASSISTANT = new BigDecimal("0.00006");

    @Override
    public boolean hasBalance(String codeGPTUser) {
        return getBalance(codeGPTUser).compareTo(BigDecimal.ZERO) > 0;
    }

    @Override
    public BigDecimal getBalance(String codeGPTUser) {
        TokenDOExample tokenDOExample = new TokenDOExample();
        tokenDOExample.createCriteria().andUserEqualTo(codeGPTUser);
        List<TokenDO> tokenDOList = tokenManualMapper.selectByExample(tokenDOExample);
        if (CollectionUtils.isEmpty(tokenDOList)) {
            return BigDecimal.ZERO;
        }
        return tokenDOList.get(0).getBalance();
    }

    @Override
    public BigDecimal cost(String codeGPTUser, ChatGPTModelEnum modelEnum, long promptTokens, long completionTokens) {
        BigDecimal questionCost = getFee(modelEnum, promptTokens, ChatRoleEnum.USER);
        BigDecimal answerCost = getFee(modelEnum, completionTokens, ChatRoleEnum.ASSISTANT);
        BigDecimal allCost = questionCost.add(answerCost);
        tokenManualMapper.decBalance(codeGPTUser, allCost);
        return allCost;
    }

    @Override
    public BigDecimal calculateNeedCostFee(ChatGPTModelEnum modelEnum, long promptTokens, long completionTokens) {
        BigDecimal questionCost = getFee(modelEnum, promptTokens, ChatRoleEnum.USER);
        BigDecimal answerCost = getFee(modelEnum, completionTokens, ChatRoleEnum.ASSISTANT);
        BigDecimal allCost = questionCost.add(answerCost);
        return allCost;
    }

    @Override
    public int cost(String codeGPTUser, BigDecimal allCost) {
        return tokenManualMapper.decBalance(codeGPTUser, allCost);
    }


    @Override
    public boolean recharge(String codeGPTUser, Integer money) {
        return tokenManualMapper.incBalance(codeGPTUser, new BigDecimal(money)) == 1;
    }

    @Override
    public boolean decBalance(String codeGPTUser, Integer money) {
        return tokenManualMapper.decBalance(codeGPTUser, new BigDecimal(money)) == 1;
    }

    @Override
    public BigDecimal getFee(ChatGPTModelEnum model, long tokenNum, ChatRoleEnum chatRoleEnum) {
        // 未知模型的话,按照最贵的进行扣费
        BigDecimal oneTokenPrice = OPENAI_ONE_TOKEN_PRICE_GPT4_ASSISTANT;
        BigDecimal tokenNumDecimal = new BigDecimal(tokenNum);
        if (model == null) {
            return oneTokenPrice.multiply(tokenNumDecimal);
        }
        switch (model) {
            case DAVINCI:
                oneTokenPrice = OPENAI_ONE_TOKEN_PRICE_DAVINCI;
                break;
            case GPT_35:
                oneTokenPrice = OPENAI_ONE_TOKEN_PRICE_GPT_35_TURBO;
                break;
            case GPT4:
                if (ChatRoleEnum.USER == chatRoleEnum) {
                    oneTokenPrice = OPENAI_ONE_TOKEN_PRICE_GPT4_USER;
                } else if (ChatRoleEnum.ASSISTANT == chatRoleEnum) {
                    oneTokenPrice = OPENAI_ONE_TOKEN_PRICE_GPT4_ASSISTANT;
                } else {
                    throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
                }
                break;
            default:
               break;
        }
        return oneTokenPrice.multiply(tokenNumDecimal);
    }

}
