package com.alipay.codegencore.model.openai;

import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.enums.ControlTypeEnum;

import java.util.List;

/**
 * 插件VO和前端交互的
 */
public class PluginVO extends PluginDO {

    private String ownerUserEmpId;

    private String ownerUserName;

    private AciInfo aciConfig;

    /**
     * 使用当前插件的用户可见已启用的助手
     */
    private List<UserSaveSceneVO> useScene;
    /**
     * 权限变更传入工号列表
     */
    private List<String> empIdList;
    /**
     * 权限类型
     */
    private ControlTypeEnum controlTypeEnum;

    public List<UserSaveSceneVO> getUseScene() {
        return useScene;
    }

    public void setUseScene(List<UserSaveSceneVO> useScene) {
        this.useScene = useScene;
    }

    public String getOwnerUserEmpId() {
        return ownerUserEmpId;
    }

    public void setOwnerUserEmpId(String ownerUserEmpId) {
        this.ownerUserEmpId = ownerUserEmpId;
    }

    public String getOwnerUserName() {
        return ownerUserName;
    }

    public void setOwnerUserName(String ownerUserName) {
        this.ownerUserName = ownerUserName;
    }

    public AciInfo getAciConfig() {
        return aciConfig;
    }

    public void setAciConfig(AciInfo aciConfig) {
        this.aciConfig = aciConfig;
    }

    public List<String> getEmpIdList() {
        return empIdList;
    }

    public void setEmpIdList(List<String> empIdList) {
        this.empIdList = empIdList;
    }

    public ControlTypeEnum getControlTypeEnum() {
        return controlTypeEnum;
    }

    public void setControlTypeEnum(ControlTypeEnum controlTypeEnum) {
        this.controlTypeEnum = controlTypeEnum;
    }
}
