package com.alipay.codegencore.service.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023.10.20
 */
public class ShortUid {
    private static final Logger LOGGER = LoggerFactory.getLogger( ShortUid.class );
    /**
     * 获取随机UUID转换成bytes然后经过base64编码然后短版的UUID
     * <AUTHOR>
     * @since
     */
    public static String getUid() {
        String uuid = Base64.getUrlEncoder().encodeToString(toByteArray(UUID.randomUUID())).replace("=", "");
        if(uuid.length()==22){
            return uuid.replace("-",".");
        }else {
            LOGGER.error("UUID generate fail");
            throw new RuntimeException("Invalid UUID");
        }

    }
    /**
     * 将UUID转换成bytes
     * <AUTHOR>
     * @since
     */
    private static byte[] toByteArray(UUID uuid) {
        long mostSigBits = uuid.getMostSignificantBits();
        long leastSigBits = uuid.getLeastSignificantBits();
        byte[] uuidBytes = new byte[16];
        for (int i = 0; i < 8; i++) {
            uuidBytes[i] = (byte) (mostSigBits >>> (8 * (7 - i)));
            uuidBytes[8 + i] = (byte) (leastSigBits >>> (8 * (7 - i)));
        }
        return uuidBytes;
    }
}

