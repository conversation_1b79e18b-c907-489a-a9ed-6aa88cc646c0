package com.alipay.codegencore.model.model;

import java.math.BigDecimal;

/**
 * 文档切割响应类
 */
public class DocumentToChatResponse {

    private String prompt;

    private BigDecimal processingTime;

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public BigDecimal getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(BigDecimal processingTime) {
        this.processingTime = processingTime;
    }
}
