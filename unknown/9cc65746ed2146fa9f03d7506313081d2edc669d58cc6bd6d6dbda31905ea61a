/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links.Enum;

import org.apache.commons.lang3.StringUtils;

/**
 * gpt返回诊断工具信息appName
 *
 * <AUTHOR>
 * @version $Id: DiagToolAppEnum.java, v 0.1 2023-11-02 下午3:15 admin Exp $$
 */
public enum DiagToolAppEnum {
    /**
     * 云图工具
     */
    YUNTU("accagovernance"),
    /**
     * 聆图工具
     */
    ATECHS("atechs");
    /**
     * appName
     */
    public String code;

    DiagToolAppEnum(String code) {
        this.code = code;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code appName
     * @return DiagToolAppEnum
     */
    public static String getNameByCode(String code) {
        for (DiagToolAppEnum diagToolAppEnum : DiagToolAppEnum.values()) {
            if (StringUtils.equalsIgnoreCase(diagToolAppEnum.code, code)) {
                return diagToolAppEnum.name();
            }
        }
        return null;
    }

}