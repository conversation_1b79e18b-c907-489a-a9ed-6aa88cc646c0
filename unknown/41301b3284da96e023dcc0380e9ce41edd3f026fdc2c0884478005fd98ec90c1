package com.alipay.codegencore.service.common.limiter;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.mutable.MutablePair;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tbase.model.RateLimiterTuple;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.RateLimitDO;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.limit.CallerTargetPrefixEnum;
import com.alipay.codegencore.model.enums.limit.LimitTypeEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.utils.Md5Utils;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 限流器的工厂类
 */
public class RateLimitFactory {

    private static final Logger RATELIMIT_LOGGER = LoggerFactory.getLogger("RATELIMIT");
    // 单例对象
    private volatile static RateLimitFactory INSTANCE = null;
    private Map<String, Pair<Long, List<RateLimitDO>>> rateLimitDOMap = new HashMap<>();
    private Map<String, TokenDO> tokenDOMap = new HashMap<>();
    private Map<String, Pair<Long, AlgoBackendDO>> algoBackendDOMap = new HashMap<>();
    private static final String RATE_LIMIT_KEY_PREFIX = "CODEGENCORE_RATE_LIMIT_KEY:";
    // 限流器列表的缓存时间,修改限流器之后需要这个时间之后才能生效
    private static final Long ONE_MINUTE = 60 * 1000L;
    private static final Long ONE_HOUR = 60 * 60 * 1000L;
    private final RefreshableCommonTbaseCacheManager cacheManager;
    private final RateLimitService rateLimitService;
    private final TokenService tokenService;
    private final AlgoBackendService algoBackendService;


    private RateLimitFactory() {
        cacheManager = SpringUtil.getBean("defaultCacheManager");
        rateLimitService = SpringUtil.getBean("rateLimitService");
        tokenService = SpringUtil.getBean("tokenService");
        algoBackendService = SpringUtil.getBean("algoBackendService");
    }

    /**
     * 获取单例(不要在spring成员变量中使用，否则构造方法中的Spring bean可能未被初始化)
     *
     * @return RateLimitFactory对象
     */
    public static RateLimitFactory getInstance() {
        if (INSTANCE == null) {
            synchronized (RateLimitFactory.class) {
                if (INSTANCE == null) {
                    INSTANCE = new RateLimitFactory();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * openapi接口限流
     *
     * @param tokenUser
     * @param uri       原始的uri
     * @return
     */
    public Pair<Boolean, Long> tryAcquireOpenApi(String tokenUser, String uri) {
        if (StringUtils.isBlank(uri)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "uri为空");
        }
        if (StringUtils.isBlank(tokenUser)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "tokenUser为空");
        }
        Long tokenId = getTokenId(tokenUser);
        String caller = CallerTargetPrefixEnum.TOKEN_ID.getPrefix() + tokenId;
        String target = CallerTargetPrefixEnum.API_PATH.getPrefix() + uri;
        return tryAcquireAll(caller, target);
    }

    /**
     * webapi接口限流
     *
     * @param userId
     * @param uri
     * @return
     */
    public Pair<Boolean, Long> tryAcquireWebApi(Long userId, String uri) {
        if (StringUtils.isBlank(uri)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "uri为空");
        }
        if (null == userId) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "userId为空");
        }
        String caller = CallerTargetPrefixEnum.USER_ID.getPrefix() + userId;
        String target = CallerTargetPrefixEnum.API_PATH.getPrefix() + uri;
        return tryAcquireAll(caller, target);
    }

    /**
     * 模型限流,请求来源是openAPI的
     *
     * @param tokenUser
     * @param modelName
     * @return
     */
    public Pair<Boolean, Long> tryAcquireAlgoFromOpenApi(String tokenUser, String modelName) {
        if (StringUtils.isBlank(modelName)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "modelName为空");
        }
        if (StringUtils.isBlank(tokenUser)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "tokenUser为空");
        }
        Long tokenId = getTokenId(tokenUser);
        Long modelId = getModelId(modelName);
        String caller = CallerTargetPrefixEnum.TOKEN_ID.getPrefix() + tokenId;
        String target = CallerTargetPrefixEnum.MODEL_ID.getPrefix() + modelId;
        return tryAcquireAll(caller, target);
    }

    /**
     * 模型限流,请求来源是webAPI的
     *
     * @param userId
     * @param modelName
     * @return
     */
    public Pair<Boolean, Long> tryAcquireAlgoFromWebApi(Long userId, String modelName) {
        if (StringUtils.isBlank(modelName)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "modelName为空");
        }
        if (null == userId) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "userId为空");
        }
        Long modelId = getModelId(modelName);
        String caller = CallerTargetPrefixEnum.USER_ID.getPrefix() + userId;
        String target = CallerTargetPrefixEnum.MODEL_ID.getPrefix() + modelId;
        return tryAcquireAll(caller, target);
    }

    /**
     * 执行限流
     *
     * @param caller 真实的调用方
     * @param target 真实的被调用资源
     * @return
     */
    private Pair<Boolean, Long> tryAcquireAll(String caller, String target) {
        RATELIMIT_LOGGER.info("tryAcquireAll param,caller:{},target:{}", caller, target);
        List<RateLimitDO> rateLimitDOList = getRateLimitDOList(caller, target);
        if (CollectionUtils.isEmpty(rateLimitDOList)) {
            RATELIMIT_LOGGER.info("tryAcquireAll ret,限流通过,rateLimitDOList is null caller:{},target:{}", caller, target);
            return new MutablePair<>(true, null);
        }
        // markPriority越小,优先级越高
        Byte markPriority = null;
        List<Long> ignoreList = new ArrayList<>();
        List<String> limitKeyList = new ArrayList<>();
        for (RateLimitDO rateLimitDO : rateLimitDOList) {
            // 未启用的规则,直接跳过,所有配置都无效
            if (!rateLimitDO.getEnable()) {
                continue;
            }
            String ignoreListStr = rateLimitDO.getIgnoreList();
            if (StringUtils.isNotBlank(ignoreListStr)) {
                ignoreList.addAll(JSONArray.parseArray(ignoreListStr, Long.class));
            }
            // 当前规则在忽略列表里
            if (ignoreList.contains(rateLimitDO.getId())) {
                continue;
            }
            if (!rateLimitDO.getNeedLimit()) {
                // 当前限流池不需要限流,且是最后一个限流池,就算限流通过
                if (rateLimitDO.getStop()) {
                    break;
                }
                continue;
            }
            // 生成一个限流用的真实的limitKey
            String limitKey = getLimitKey(rateLimitDO, caller, target);
            limitKeyList.add(limitKey);
            // 当前的优先级更高的话,就替换为更高的优先级
            if (rateLimitDO.getMarkPriority() != null) {
                if (markPriority == null) {
                    markPriority = rateLimitDO.getMarkPriority();
                } else if (rateLimitDO.getMarkPriority() < markPriority) {
                    markPriority = rateLimitDO.getMarkPriority();
                }
            }
            boolean ret = operateTBaseTryAcquire(limitKey, markPriority, rateLimitDO);
            // 任何一个限流池不通过就拒绝,返回触发限流的规则id用于排查确认
            if (!ret) {
                RATELIMIT_LOGGER.info("tryAcquireAll ret:限流不通过,禁止访问资源,触发的规则id:{},限流的真实keyList:{},markPriority:{},规则idList:{},caller:{},target:{}",
                        rateLimitDO.getId(), JSON.toJSONString(limitKeyList), markPriority,
                        JSON.toJSONString(rateLimitDOList.stream().map(RateLimitDO::getId).collect(Collectors.toList())),
                        caller, target);
                return new MutablePair<>(false, rateLimitDO.getId());
            }
            // 当前是最后一个限流池
            if (rateLimitDO.getStop()) {
                break;
            }
        }
        RATELIMIT_LOGGER.info("tryAcquireAll ret:限流通过,允许访问资源,限流的真实keyList:{},markPriority:{},规则idList:{},caller:{},target:{}",
                JSON.toJSONString(limitKeyList), markPriority,
                JSON.toJSONString(rateLimitDOList.stream().map(RateLimitDO::getId).collect(Collectors.toList())),
                caller, target);
        return new MutablePair<>(true, null);
    }


    /**
     * 操作Tbase进行具体的限流动作执行
     *
     * @param limitKey     真实的限流key
     * @param markPriority 最高优先级
     * @param rateLimitDO  当前过的限流池
     * @return
     */
    private boolean operateTBaseTryAcquire(String limitKey, Byte markPriority, RateLimitDO rateLimitDO) {
        Integer windowTimeMills = rateLimitDO.getWindowTimeMills();
        Integer windowTotalQuota = rateLimitDO.getWindowTotalQuota();
        String priorityConfig = rateLimitDO.getPriorityConfig();
        // 优先级为空或者1=最高优先级,或者当前的优先级配置是空,则说明可以使用全部限流池的资源
        if (markPriority == null || markPriority == 1 || StringUtils.isBlank(priorityConfig)) {
            RateLimiterTuple rateLimiterTuple = cacheManager.ratelimiter(limitKey, windowTotalQuota, 1, "PX", windowTimeMills);
            return rateLimiterTuple.limited == 0;
        }
        /**
         * priorityConfig 如果不为空,则格式如下
         * {
         *     "1":"0",   //表示当优先级=1时,当前限流池资源>0%才能使用
         *     "2":"0.5", //表示当优先级=2时,当前限流池资源>50%才能使用
         *     "3":"0.8", //表示当优先级=3时,当前限流池资源>80%才能使用
         * }
         */
        JSONObject priorityConfigJson = JSON.parseObject(priorityConfig);
        BigDecimal minRatio = priorityConfigJson.getBigDecimal(markPriority.toString());
        // 获取当前限流池剩余的令牌数量
        RateLimiterTuple restTokenRet = cacheManager.ratelimiter(limitKey, windowTotalQuota, 0, "PX", windowTimeMills);
        // 当前限流池剩余的令牌数量 / 当前限流池的总令牌数量 = 剩余容量的比例
        BigDecimal remainingCapacity = new BigDecimal(restTokenRet.restToken).divide(new BigDecimal(windowTotalQuota), 4, RoundingMode.HALF_UP);
        // 剩余容量比例 > 可以priorityConfig中配置的比例,才能使用
        boolean canUseThisLimit = remainingCapacity.compareTo(minRatio) > 0;
        RATELIMIT_LOGGER.info("operateTBaseTryAcquire,当前限流池使用的优先级非空,可使用当前限流池:{},总容量:{},剩余容量:{},剩余比例:{},优先级:{},可使用的最小剩余比例:{}",
                canUseThisLimit, windowTotalQuota, restTokenRet.restToken, remainingCapacity, markPriority, minRatio);

        if (canUseThisLimit) {
            RateLimiterTuple rateLimiterTuple = cacheManager.ratelimiter(limitKey, windowTotalQuota, 1, "PX", windowTimeMills);
            return rateLimiterTuple.limited == 0;
        }
        return false;
    }

    /**
     * 生成一个真实的limitKey
     *
     * @param rateLimitDO
     * @param caller
     * @param target
     * @return 返回结果如下
     * <p>
     * case1:template=RULE_ID
     * CODEGENCORE_RATE_LIMIT_KEY:10001
     * <p>
     * case2:template=RULE_ID__CALLER
     * CODEGENCORE_RATE_LIMIT_KEY:10001__TOKEN_ID:10001
     * CODEGENCORE_RATE_LIMIT_KEY:10001__USER_ID:20009
     * <p>
     * case3:template=RULE_ID__TARGET
     * CODEGENCORE_RATE_LIMIT_KEY:10001__API_PATH:/a/test
     * CODEGENCORE_RATE_LIMIT_KEY:10001__MODEL_ID:10001
     */
    private String getLimitKey(RateLimitDO rateLimitDO, String caller, String target) {
        String limitKey = RATE_LIMIT_KEY_PREFIX + rateLimitDO.getTemplate();
        limitKey = limitKey.replace("RULE_ID", rateLimitDO.getId().toString());
        limitKey = limitKey.replace("CALLER", caller);
        limitKey = limitKey.replace("TARGET", target);
        return limitKey;
    }

    /**
     * 获取需要过的限流池列表
     *
     * @param caller 请求方
     * @param target 被调用资源
     * @return
     */
    private List<RateLimitDO> getRateLimitDOList(String caller, String target) {
        // 针对相同的参数列表,生成一个唯一的md5
        String uid = Md5Utils.md5(caller, target);
        // map的key为md5的值,val存写入时间和限流池列表
        if (rateLimitDOMap.containsKey(uid)) {
            Pair<Long, List<RateLimitDO>> pair = rateLimitDOMap.get(uid);
            // 当前时间距离写入时间<=1分钟,则直接使用这个内存中的限流池列表
            if (System.currentTimeMillis() - pair.getKey() <= ONE_MINUTE) {
                return pair.getValue();
            }
        }
        LimitTypeEnum limitTypeEnum = target.startsWith(CallerTargetPrefixEnum.API_PATH.getPrefix()) ? LimitTypeEnum.API : LimitTypeEnum.ALGO;
        // 调用方列表,值为["TOKEN_ID:*","TOKEN_ID:10001"]或者["USER_ID:*","USER_ID:10001"]
        List<String> callerList = new ArrayList<>();
        callerList.add(CallerTargetPrefixEnum.ALL_CALLER.getPrefix() + "*");
        callerList.add(caller);
        callerList.add(toCommonRule(caller));
        // 被调用方列表,值为["API_PATH:*","API_PATH:/test/a"]或者["MODEL_ID:*","MODEL_ID:10001"]
        List<String> targetList = new ArrayList<>();
        targetList.add(CallerTargetPrefixEnum.ALL_TARGET.getPrefix() + "*");
        targetList.add(target);
        targetList.add(toCommonRule(target));
        // 获取所有符合要求的,已启用的限流池列表,按照sorted排序返回
        List<RateLimitDO> rateLimitDOList = rateLimitService.getRateLimitListByParam(limitTypeEnum, callerList, targetList);
        // 记录到内存里去
        rateLimitDOMap.put(uid, new MutablePair<>(System.currentTimeMillis(), rateLimitDOList));
        return rateLimitDOList;
    }

    private String toCommonRule(String realCallerOrTarget) {
        return realCallerOrTarget.split(":")[0] + ":*";
    }

    /**
     * 根据tokenUser获取自增id
     *
     * @param tokenUser
     * @return
     */
    private Long getTokenId(String tokenUser) {
        if (!tokenDOMap.containsKey(tokenUser)) {
            TokenDO tokenDO = tokenService.getTokenSystem(tokenUser);
            if (tokenDO == null) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "tokenUser未启用或者不存在,tokenUser:" + tokenUser);
            }
            tokenDOMap.put(tokenUser, tokenDO);
        }
        return tokenDOMap.get(tokenUser).getId();
    }

    /**
     * 根据modelName获取模型的自增id
     *
     * @param modelName
     * @return
     */
    private Long getModelId(String modelName) {
        if (algoBackendDOMap.containsKey(modelName)) {
            Pair<Long, AlgoBackendDO> pair = algoBackendDOMap.get(modelName);
            // 当前时间距离写入时间<=1小时
            if (System.currentTimeMillis() - pair.getKey() <= ONE_HOUR) {
                return pair.getValue().getId();
            }
        }
        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(modelName);
        if (algoBackendDO == null) {
            throw new BizException(ResponseEnum.MODEL_NOT_EXISTED, ResponseEnum.MODEL_NOT_EXISTED.getErrorMsg() + ",modelName:" + modelName);
        }
        algoBackendDOMap.put(modelName, new MutablePair<>(System.currentTimeMillis(), algoBackendDO));
        return algoBackendDO.getId();
    }

}
