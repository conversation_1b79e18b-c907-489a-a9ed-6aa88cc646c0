package com.alipay.codegencore.model.response.linke;

import java.util.List;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.response.linke
 * @CreateTime : 2024-04-15
 */
public class DefaultPRValueVO {

    /**
     * 代表代码评审人员列表的默认值
     */
    private List<ApprovalUser> reviewers;
    /**
     * 目标分支的默认值
     */

    private String             targetBranch;
    /**
     * 源分支的默认值
     */
    private String             sourceBranch;
    /**
     * 标题的默认值
     */
    private String             title;
    /**
     * 消息的默认值
     */
    private String             message;
    /**
     * 迭代id
     */
    private String             iterationId;
    /**
     * 默认的工作项信息
     */
    private List<WorkItemVO>  items;

    public List<WorkItemVO> getItems() {
        return items;
    }

    public void setItems(List<WorkItemVO> items) {
        this.items = items;
    }

    public String getIterationId() {
        return iterationId;
    }

    public void setIterationId(String iterationId) {
        this.iterationId = iterationId;
    }

    public List<ApprovalUser> getReviewers() {
        return reviewers;
    }

    public void setReviewers(List<ApprovalUser> reviewers) {
        this.reviewers = reviewers;
    }

    public String getTargetBranch() {
        return targetBranch;
    }

    public void setTargetBranch(String targetBranch) {
        this.targetBranch = targetBranch;
    }

    public String getSourceBranch() {
        return sourceBranch;
    }

    public void setSourceBranch(String sourceBranch) {
        this.sourceBranch = sourceBranch;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
