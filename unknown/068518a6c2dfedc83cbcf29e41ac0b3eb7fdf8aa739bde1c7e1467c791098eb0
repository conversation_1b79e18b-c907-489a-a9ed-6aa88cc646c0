/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.http;

import com.alipay.codegencore.utils.http.GetBuilder;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.PostBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;

import static org.junit.Assert.assertNotNull;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class HttpClient_SSTest extends HttpClient_SSTest_scaffolding {
// allCoveredLines:[11, 24, 35]

  @Test(timeout = 4000)
  public void test_get_0()  throws Throwable  {
      //caseID:fc436c3c259574906aae393fa938f22d
      //CoveredLines: [11, 24]
      //Input_0_String: 1
      //Assert: assertNotNull(method_result);
      
      HttpClient httpClient0 = new HttpClient();
      
      //Call method: get
      GetBuilder getBuilder0 = HttpClient.get("1");
      
      //Test Result Assert
      assertNotNull(getBuilder0);
  }

  @Test(timeout = 4000)
  public void test_post_1()  throws Throwable  {
      //caseID:7f58547d5ad7ee5717e40360dac81a57
      //CoveredLines: [35]
      //Input_0_String: 
      //Assert: assertNotNull(method_result);
      
      
      //Call method: post
      PostBuilder postBuilder0 = HttpClient.post("");
      
      //Test Result Assert
      assertNotNull(postBuilder0);
  }
}
