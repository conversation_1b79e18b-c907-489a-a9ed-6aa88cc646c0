package com.alipay.codegencore.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DocumentDOExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public DocumentDOExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andUidIsNull() {
            addCriterion("uid is null");
            return (Criteria) this;
        }

        public Criteria andUidIsNotNull() {
            addCriterion("uid is not null");
            return (Criteria) this;
        }

        public Criteria andUidEqualTo(String value) {
            addCriterion("uid =", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotEqualTo(String value) {
            addCriterion("uid <>", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThan(String value) {
            addCriterion("uid >", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThanOrEqualTo(String value) {
            addCriterion("uid >=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThan(String value) {
            addCriterion("uid <", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThanOrEqualTo(String value) {
            addCriterion("uid <=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLike(String value) {
            addCriterion("uid like", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotLike(String value) {
            addCriterion("uid not like", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidIn(List<String> values) {
            addCriterion("uid in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotIn(List<String> values) {
            addCriterion("uid not in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidBetween(String value1, String value2) {
            addCriterion("uid between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotBetween(String value1, String value2) {
            addCriterion("uid not between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andDocumentNameIsNull() {
            addCriterion("document_name is null");
            return (Criteria) this;
        }

        public Criteria andDocumentNameIsNotNull() {
            addCriterion("document_name is not null");
            return (Criteria) this;
        }

        public Criteria andDocumentNameEqualTo(String value) {
            addCriterion("document_name =", value, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameNotEqualTo(String value) {
            addCriterion("document_name <>", value, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameGreaterThan(String value) {
            addCriterion("document_name >", value, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameGreaterThanOrEqualTo(String value) {
            addCriterion("document_name >=", value, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameLessThan(String value) {
            addCriterion("document_name <", value, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameLessThanOrEqualTo(String value) {
            addCriterion("document_name <=", value, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameLike(String value) {
            addCriterion("document_name like", value, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameNotLike(String value) {
            addCriterion("document_name not like", value, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameIn(List<String> values) {
            addCriterion("document_name in", values, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameNotIn(List<String> values) {
            addCriterion("document_name not in", values, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameBetween(String value1, String value2) {
            addCriterion("document_name between", value1, value2, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentNameNotBetween(String value1, String value2) {
            addCriterion("document_name not between", value1, value2, "documentName");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeIsNull() {
            addCriterion("document_size is null");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeIsNotNull() {
            addCriterion("document_size is not null");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeEqualTo(Long value) {
            addCriterion("document_size =", value, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeNotEqualTo(Long value) {
            addCriterion("document_size <>", value, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeGreaterThan(Long value) {
            addCriterion("document_size >", value, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeGreaterThanOrEqualTo(Long value) {
            addCriterion("document_size >=", value, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeLessThan(Long value) {
            addCriterion("document_size <", value, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeLessThanOrEqualTo(Long value) {
            addCriterion("document_size <=", value, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeIn(List<Long> values) {
            addCriterion("document_size in", values, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeNotIn(List<Long> values) {
            addCriterion("document_size not in", values, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeBetween(Long value1, Long value2) {
            addCriterion("document_size between", value1, value2, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentSizeNotBetween(Long value1, Long value2) {
            addCriterion("document_size not between", value1, value2, "documentSize");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusIsNull() {
            addCriterion("document_status is null");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusIsNotNull() {
            addCriterion("document_status is not null");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusEqualTo(String value) {
            addCriterion("document_status =", value, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusNotEqualTo(String value) {
            addCriterion("document_status <>", value, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusGreaterThan(String value) {
            addCriterion("document_status >", value, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusGreaterThanOrEqualTo(String value) {
            addCriterion("document_status >=", value, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusLessThan(String value) {
            addCriterion("document_status <", value, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusLessThanOrEqualTo(String value) {
            addCriterion("document_status <=", value, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusLike(String value) {
            addCriterion("document_status like", value, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusNotLike(String value) {
            addCriterion("document_status not like", value, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusIn(List<String> values) {
            addCriterion("document_status in", values, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusNotIn(List<String> values) {
            addCriterion("document_status not in", values, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusBetween(String value1, String value2) {
            addCriterion("document_status between", value1, value2, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andDocumentStatusNotBetween(String value1, String value2) {
            addCriterion("document_status not between", value1, value2, "documentStatus");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlIsNull() {
            addCriterion("content_oss_url is null");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlIsNotNull() {
            addCriterion("content_oss_url is not null");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlEqualTo(String value) {
            addCriterion("content_oss_url =", value, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlNotEqualTo(String value) {
            addCriterion("content_oss_url <>", value, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlGreaterThan(String value) {
            addCriterion("content_oss_url >", value, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlGreaterThanOrEqualTo(String value) {
            addCriterion("content_oss_url >=", value, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlLessThan(String value) {
            addCriterion("content_oss_url <", value, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlLessThanOrEqualTo(String value) {
            addCriterion("content_oss_url <=", value, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlLike(String value) {
            addCriterion("content_oss_url like", value, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlNotLike(String value) {
            addCriterion("content_oss_url not like", value, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlIn(List<String> values) {
            addCriterion("content_oss_url in", values, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlNotIn(List<String> values) {
            addCriterion("content_oss_url not in", values, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlBetween(String value1, String value2) {
            addCriterion("content_oss_url between", value1, value2, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentOssUrlNotBetween(String value1, String value2) {
            addCriterion("content_oss_url not between", value1, value2, "contentOssUrl");
            return (Criteria) this;
        }

        public Criteria andContentLengthIsNull() {
            addCriterion("content_length is null");
            return (Criteria) this;
        }

        public Criteria andContentLengthIsNotNull() {
            addCriterion("content_length is not null");
            return (Criteria) this;
        }

        public Criteria andContentLengthEqualTo(Long value) {
            addCriterion("content_length =", value, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthNotEqualTo(Long value) {
            addCriterion("content_length <>", value, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthGreaterThan(Long value) {
            addCriterion("content_length >", value, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthGreaterThanOrEqualTo(Long value) {
            addCriterion("content_length >=", value, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthLessThan(Long value) {
            addCriterion("content_length <", value, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthLessThanOrEqualTo(Long value) {
            addCriterion("content_length <=", value, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthIn(List<Long> values) {
            addCriterion("content_length in", values, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthNotIn(List<Long> values) {
            addCriterion("content_length not in", values, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthBetween(Long value1, Long value2) {
            addCriterion("content_length between", value1, value2, "contentLength");
            return (Criteria) this;
        }

        public Criteria andContentLengthNotBetween(Long value1, Long value2) {
            addCriterion("content_length not between", value1, value2, "contentLength");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlIsNull() {
            addCriterion("segment_oss_url is null");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlIsNotNull() {
            addCriterion("segment_oss_url is not null");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlEqualTo(String value) {
            addCriterion("segment_oss_url =", value, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlNotEqualTo(String value) {
            addCriterion("segment_oss_url <>", value, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlGreaterThan(String value) {
            addCriterion("segment_oss_url >", value, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlGreaterThanOrEqualTo(String value) {
            addCriterion("segment_oss_url >=", value, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlLessThan(String value) {
            addCriterion("segment_oss_url <", value, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlLessThanOrEqualTo(String value) {
            addCriterion("segment_oss_url <=", value, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlLike(String value) {
            addCriterion("segment_oss_url like", value, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlNotLike(String value) {
            addCriterion("segment_oss_url not like", value, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlIn(List<String> values) {
            addCriterion("segment_oss_url in", values, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlNotIn(List<String> values) {
            addCriterion("segment_oss_url not in", values, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlBetween(String value1, String value2) {
            addCriterion("segment_oss_url between", value1, value2, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSegmentOssUrlNotBetween(String value1, String value2) {
            addCriterion("segment_oss_url not between", value1, value2, "segmentOssUrl");
            return (Criteria) this;
        }

        public Criteria andSummaryIsNull() {
            addCriterion("summary is null");
            return (Criteria) this;
        }

        public Criteria andSummaryIsNotNull() {
            addCriterion("summary is not null");
            return (Criteria) this;
        }

        public Criteria andSummaryEqualTo(String value) {
            addCriterion("summary =", value, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryNotEqualTo(String value) {
            addCriterion("summary <>", value, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryGreaterThan(String value) {
            addCriterion("summary >", value, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryGreaterThanOrEqualTo(String value) {
            addCriterion("summary >=", value, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryLessThan(String value) {
            addCriterion("summary <", value, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryLessThanOrEqualTo(String value) {
            addCriterion("summary <=", value, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryLike(String value) {
            addCriterion("summary like", value, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryNotLike(String value) {
            addCriterion("summary not like", value, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryIn(List<String> values) {
            addCriterion("summary in", values, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryNotIn(List<String> values) {
            addCriterion("summary not in", values, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryBetween(String value1, String value2) {
            addCriterion("summary between", value1, value2, "summary");
            return (Criteria) this;
        }

        public Criteria andSummaryNotBetween(String value1, String value2) {
            addCriterion("summary not between", value1, value2, "summary");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andZsearchClientIsNull() {
            addCriterion("zsearch_client is null");
            return (Criteria) this;
        }

        public Criteria andZsearchClientIsNotNull() {
            addCriterion("zsearch_client is not null");
            return (Criteria) this;
        }

        public Criteria andZsearchClientEqualTo(String value) {
            addCriterion("zsearch_client =", value, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientNotEqualTo(String value) {
            addCriterion("zsearch_client <>", value, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientGreaterThan(String value) {
            addCriterion("zsearch_client >", value, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientGreaterThanOrEqualTo(String value) {
            addCriterion("zsearch_client >=", value, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientLessThan(String value) {
            addCriterion("zsearch_client <", value, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientLessThanOrEqualTo(String value) {
            addCriterion("zsearch_client <=", value, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientLike(String value) {
            addCriterion("zsearch_client like", value, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientNotLike(String value) {
            addCriterion("zsearch_client not like", value, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientIn(List<String> values) {
            addCriterion("zsearch_client in", values, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientNotIn(List<String> values) {
            addCriterion("zsearch_client not in", values, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientBetween(String value1, String value2) {
            addCriterion("zsearch_client between", value1, value2, "zsearchClient");
            return (Criteria) this;
        }

        public Criteria andZsearchClientNotBetween(String value1, String value2) {
            addCriterion("zsearch_client not between", value1, value2, "zsearchClient");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_document
     *
     * @mbg.generated do_not_delete_during_merge Tue Oct 15 14:27:02 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}