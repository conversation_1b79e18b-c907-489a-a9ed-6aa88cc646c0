/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.http;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class AbstractRequestBuilder_SSTest extends AbstractRequestBuilder_SSTest_scaffolding {
  @Test
  public void notGeneratedAnyTest() {
    // SmartUnit did not generate any tests
  }
}
