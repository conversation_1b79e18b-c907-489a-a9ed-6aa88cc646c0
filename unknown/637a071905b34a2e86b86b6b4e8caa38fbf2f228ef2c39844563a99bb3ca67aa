<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.alipay</groupId>
        <artifactId>codegencore-parent</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>codegencore-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.alipay</groupId>
            <artifactId>codegencore-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay</groupId>
            <artifactId>codegencore-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay</groupId>
            <artifactId>codegencore-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay</groupId>
            <artifactId>codegencore-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>ddcs-alipay-sofa-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>rpc-alipay-sofa-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>zcache-alipay-sofa-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>buservice-alipay-sofa-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.sofa</groupId>
            <artifactId>infosec-tr-sofa-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.antwork</groupId>
            <artifactId>antwork-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.antwork</groupId>
            <artifactId>antwork-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.arks</groupId>
            <artifactId>arks-client-java</artifactId>
            <!-- 版本信息看这里 https://yuque.antfin-inc.com/mdp/userguide/fscmu0 -->
        </dependency>
        <dependency>
            <groupId>com.alipay.arks</groupId>
            <artifactId>arks-client-grpc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.rcsmart</groupId>
            <artifactId>rcsmart-common-service-facade</artifactId>
        </dependency>

        <!-- ray dependency start -->
        <dependency>
            <groupId>com.antgroup.ray</groupId>
            <artifactId>serving-client-grpc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>hessian</groupId>
                    <artifactId>hessian</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--<dependency>
            <groupId>com.antgroup.ray</groupId>
            <artifactId>serving-client-sofaboot-rpc</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.antgroup.ray</groupId>
            <artifactId>serving-client-antvip</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>hessian</groupId>
                    <artifactId>hessian</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alipay.antassistant</groupId>
            <artifactId>antassistant-common-service-facade</artifactId>
        </dependency>
        <!-- 流式服务依赖，推荐该框架封装的websocket协议 -->
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>1.5.3</version>
        </dependency>
        <!-- web socket end -->
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser</artifactId>
            <version>2.1.16</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.zdatafront</groupId>
            <artifactId>zdatafront-common-service-facade</artifactId>
        </dependency>
        <!-- DI -->
        <dependency>
            <groupId>com.alipay.mdda</groupId>
            <artifactId>dipublic-common</artifactId>
            <version>1.0.0.20230911</version>
        </dependency>
        <!-- DI end -->
        <!-- URLFacadeV2.shorten -->
        <dependency>
            <groupId>com.alipay.chair.facade</groupId>
            <artifactId>basementurl-facade</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.search</groupId>
            <artifactId>search-client</artifactId>
        </dependency>
        <!-- URLFacadeV2.shorten end -->

        <dependency>
            <groupId>org.commonmark</groupId>
            <artifactId>commonmark</artifactId>
        </dependency>
        <!--  PcUICaseCommonFacade  -->
        <dependency>
            <groupId>com.alipay</groupId>
            <artifactId>rcqualitydataprod-common-service-facade</artifactId>
        </dependency>
        <!--    API GATEWAY        -->
        <dependency>
            <groupId>com.alipay.devapi</groupId>
            <artifactId>client-sdk</artifactId>
        </dependency>
        <!--    BackendAppQueryFacade  -->
        <dependency>
            <groupId>com.alipay</groupId>
            <artifactId>archdatacenter-facade</artifactId>
        </dependency>
        <!--    AgentSECSDK  -->
        <dependency>
            <groupId>com.alipay</groupId>
            <artifactId>agentsecgateway-common-service-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.fc.process</groupId>
            <artifactId>fcprocess-common-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.fc.common.lang</groupId>
            <artifactId>fc-common-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.common</groupId>
            <artifactId>alipay-common-error</artifactId>
        </dependency>



    </dependencies>

</project>