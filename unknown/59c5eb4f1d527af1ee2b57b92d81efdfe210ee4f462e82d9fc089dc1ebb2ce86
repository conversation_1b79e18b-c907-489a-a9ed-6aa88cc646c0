package com.alipay.codegencore.service.codegpt;

import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.UserAuthDO;

import java.util.List;

/**
 * <AUTHOR>  插件表service
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.service.codegpt.user
 * @CreateTime : 2023-07-11
 */
public interface PluginService {

    /**
     * 通过id查找插件
     *
     * @param id
     * @return
     */
    PluginDO getPluginById(Long id);

    /**
     * 通过id列表查找插件
     * @param idList id列表
     * @return 插件列表
     */
    List<PluginDO> getPluginByIdList(List<Long> idList,Boolean needEnable);

    /**
     * 通过名字查找插件
     *
     * @param pluginName 插件名
     * @return
     */
    PluginDO getPluginByName(String pluginName);

    /**
     * 获取所有插件
     *
     * @return
     */
    List<PluginDO> getAllPlugin();

    /**
     * 新增插件
     *
     * @param pluginDO
     * @return
     */
    Long addPlugin(PluginDO pluginDO);


    /**
     * 更新插件信息
     *
     * @param pluginDO
     * @return
     */
    Boolean updatePlugin(PluginDO pluginDO);


    /**
     * 增加插件使用次数
     *
     * @param pluginId
     * @return
     */
    Boolean addPluginUsageCount(Long pluginId);

    /**
     * 删除插件
     *
     * @param id 插件id
     * @return
     */
    Boolean deletePlugin(long id);

    /**
     * 获取用户插件
     *
     * @param userId
     * @param query
     * @return
     */
    List<PluginDO> getUserPlugin(Long userId, String query);

    /**
     * 获取已启用插件
     * @param query
     * @return
     */
    List<PluginDO> getEnablePlugin(String query);
    /**
     * 更新工具权限
     * <AUTHOR>
     * @since 2024.08.09
     * @param pluginDO pluginDO
     * @return java.lang.Boolean
     */
    void updatePluginAuth(PluginDO pluginDO, UserAuthDO userAuthDO);
    /**
     * 检查是否有编辑工具权限
     * <AUTHOR>
     * @since 2024.08.09
     * @param pluginId pluginId
     * @param userId userId
     * @return java.lang.Boolean
     */
    Boolean checkEditPluginAuth(Long pluginId, Long userId);
}
