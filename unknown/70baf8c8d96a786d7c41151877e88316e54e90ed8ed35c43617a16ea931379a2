package com.alipay.codegencore.model.model;

import java.util.Map;

/**
 * trie规则模型
 *
 * <AUTHOR>
 * 创建时间 2022-02-22
 */
public class TrieRuleModel {
    /**
     * trie树的代码数据
     */
    private String trieCode;
    /**
     * 字符串分隔节点阈值，超过此阈值，对当前token进行字符拆分
     */
    private int separateNodeThreshold;
    /**
     * 叶子节点的阈值，超过此阈值的节点，会保存cacheKey
     */
    private int leafNodeThreshold;
    /**
     * 动态参数
     */
    private Map<String,String> dynamicParamMap;
//    /**
//     * 字典code的解析类型
//     */
//    private TrieRuleTypeEnum trieRuleTypeEnum = TrieRuleTypeEnum.TOKEN;

//    public TrieRuleTypeEnum getTrieRuleTypeEnum() {
//        return trieRuleTypeEnum;
//    }
//
//    public void setTrieRuleTypeEnum(TrieRuleTypeEnum trieRuleTypeEnum) {
//        this.trieRuleTypeEnum = trieRuleTypeEnum;
//    }

    public Map<String, String> getDynamicParamMap() {
        return dynamicParamMap;
    }

    public void setDynamicParamMap(Map<String, String> dynamicParamMap) {
        this.dynamicParamMap = dynamicParamMap;
    }

    public String getTrieCode() {
        return trieCode;
    }

    public void setTrieCode(String trieCode) {
        this.trieCode = trieCode;
    }

    public int getSeparateNodeThreshold() {
        return separateNodeThreshold;
    }

    public void setSeparateNodeThreshold(int separateNodeThreshold) {
        this.separateNodeThreshold = separateNodeThreshold;
    }

    public int getLeafNodeThreshold() {
        return leafNodeThreshold;
    }

    public void setLeafNodeThreshold(int leafNodeThreshold) {
        this.leafNodeThreshold = leafNodeThreshold;
    }
}
