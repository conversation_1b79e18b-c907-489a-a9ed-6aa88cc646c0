package com.alipay.codegencore.utils.codescan;

import com.alipay.codegencore.model.model.analysis.AbstractCodeAnalysisResult;
import com.alipay.codegencore.model.model.analysis.MethodBodyModel;
import com.alipay.codegencore.model.model.analysis.MethodParamModel;
import com.alipay.codegencore.model.model.analysis.TempCodeAnalysisResultContext;
import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;

import java.util.List;
import java.util.Map;

/**
 * 简化版的代码分析能力。
 * 1：找到代码中的赋值语句，缓存赋值对象类型信息
 * 2：找到class内，field和method名字，缓存关联信息(结合implements/extends关键字)
 *
 * <AUTHOR>
 * 创建时间 2022-08-01
 */
public class SimpleCodeAnalysisServiceImpl implements CodeStaticScanService {

    private SimpleJavaParserVisitor SIMPLE_VISTOR = new SimpleJavaParserVisitor();

    /**
     * 分析源代码，根据不同的扫描类型返回不同结果
     *
     * @param content  源代码内容
     * @param scanType 扫描类型
     * @param clazz    结果对象
     * @param <T>
     * @return
     */
    @Override
    public <T> T analysisCode(String content, ScanTypeEnum scanType, Class<T> clazz) {
        CharStream charStream = CharStreams.fromString(content);
        JavaLexer javaLexer = new JavaLexer(charStream);
        CommonTokenStream commonTokenStream = new CommonTokenStream(javaLexer);
        JavaParser javaParser = new JavaParser(commonTokenStream);
        AbstractCodeAnalysisResult result = null;
        try {
            JavaParser.CompilationUnitContext compilationUnitContext = javaParser.compilationUnit();
            result = SIMPLE_VISTOR.visit(compilationUnitContext, scanType);
        } finally {
            javaParser.cleanData();
            javaLexer.cleanData();
        }
        return (T) result;
    }

    /**
     * 从{@link TempCodeAnalysisResultContext}中查询 referenceName对应的class类型
     * step1:从方法参数中查找
     * step2:从方法体内赋值语句中查找
     * step3:从类变量中查找
     *
     * @param referenceName
     * @param tempCodeAnalysisResultContext
     * @return
     */
    @Override
    public String getReferenceClassName(String referenceName, TempCodeAnalysisResultContext tempCodeAnalysisResultContext) {
        MethodBodyModel writingMethodBodyModel = tempCodeAnalysisResultContext.getWritingMethodBodyModel();
        if (writingMethodBodyModel != null) {
            List<MethodParamModel> paramList = writingMethodBodyModel.getParamList();
            if (paramList != null && paramList.size() != 0) {
                for (MethodParamModel paramModel : paramList) {
                    if (paramModel.getName().equals(referenceName)) {
                        return paramModel.getType();
                    }
                }
            }
        }
        String result = null;
        Map<String, String> localVariableMap = tempCodeAnalysisResultContext.getLocalVariableMap();
        if (localVariableMap != null && localVariableMap.size() != 0) {
            result = localVariableMap.get(referenceName);
            if (result != null) {
                return result;
            }
        }
        Map<String, String> fieldReferenceMap = tempCodeAnalysisResultContext.getFieldReferenceMap();
        if (fieldReferenceMap != null && fieldReferenceMap.size() != 0) {
            result = fieldReferenceMap.get(referenceName);
        }
        return result;
    }
}
