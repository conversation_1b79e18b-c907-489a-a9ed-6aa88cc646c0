/**
 * Alipay.com Inc. Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.web.common;

import com.alipay.codegencore.dal.example.TokenDOExample;
import com.alipay.codegencore.dal.mapper.TokenDOMapper;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.request.TokenRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.model.response.TokenResponse;
import com.alipay.codegencore.service.codegpt.CostService;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.BackendAppQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.List;

/**
 * token 管理
 *
 * <AUTHOR>
 * @version : TokenController.java, v 0.1 2020年11月27日 11:44 上午 yunchen Exp $
 */
@RestController
@CodeTalkWebApi
@RequestMapping("/admin/token")
public class TokenController {
    @Resource
    private TokenDOMapper  tokenDOMapper;
    @Resource
    private CostService    costService;
    @Resource
    private UserAclService userAclService;
    @Resource
    private TokenService   tokenService;
    @Resource
    private BackendAppQueryService backendAppQueryService;

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");


    /**
     * 创建token，需要指定userName和uriPatternList,token不需要指定，会自动生成
     *
     * @param tokenDO
     * @return
     */
    @PostMapping("/create")
    public BaseResponse addToken(@RequestBody TokenRequestBean tokenDO) {
        isAdmin();
        return tokenService.insert(tokenDO);
    }

    /**
     * 更新token信息
     *
     * @param tokenDO
     * @return
     */
    @PostMapping("/update")
    public BaseResponse update(@RequestBody TokenRequestBean tokenDO) {
        isAdmin();
        return tokenService.update(tokenDO);
    }

    /**
     * 删除特定用户的token
     *
     * @param user 用户
     * @return
     */
    @DeleteMapping("/delete")
    public BaseResponse deleteToken(@RequestParam(name = "user") String user) {
        isAdmin();
        TokenDOExample tokenDOExample = new TokenDOExample();
        TokenDOExample.Criteria criteria = tokenDOExample.createCriteria();
        criteria.andUserEqualTo(user);
        tokenDOMapper.deleteByExample(tokenDOExample);
        return BaseResponse.buildSuccess();
    }

    /**
     * 充值、扣减接口
     *
     * @param user  充值用户
     * @param money 充值金额,单位美元
     * @return 成功=true
     */
    @PostMapping("/recharge")
    public BaseResponse<Boolean> recharge(@RequestParam String user, @RequestParam Integer money) {
        isAdmin();
        boolean success = costService.recharge(user, money);
        if (success){
            CHAT_LOGGER.info("user update money->recharge:user={},money={}",user,money);
        }
        return BaseResponse.build(success);
    }

    /**
     * 扣钱接口,直接从指定用户下扣钱
     *
     * @param user  用户
     * @param money 扣多少美元
     * @return 成功=true
     */
    @PostMapping("/reduceFee")
    public BaseResponse<Boolean> reduceFee(@RequestParam String user, @RequestParam Integer money) {
        isAdmin();
        boolean success = costService.decBalance(user, money);
        if (success){
            CHAT_LOGGER.info("user update money->reduceFee:user={},money={}",user,money);
        }
        return BaseResponse.build(success);
    }

    /**
     * 获取账户的余额
     *
     * @param user 用户
     * @return 余额
     */
    @GetMapping("/getBalance")
    public BaseResponse<BigDecimal> getBalance(@RequestParam String user) {
        isAdmin();
        return BaseResponse.build(costService.getBalance(user));
    }

    /**
     * token列表
     *
     * @param pageNo
     * @param pageSize
     * @param filterField
     * @return
     */
    @GetMapping(path = "/tokenList")
    public PageResponse<List<TokenResponse>> tokenList(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                       @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                       @RequestParam(value = "filterField", required = false) String filterField) {
        isAdmin();
        return tokenService.tokenList(pageNo, pageSize, filterField);
    }

    /**
     * 获取token详情
     *
     * @param user
     * @param queryToken
     * @return
     */
    @GetMapping(path = "/getToken")
    public BaseResponse<TokenResponse> getToken(@RequestParam(value = "user", required = false) String user,
                                          @RequestParam(value = "id", required = false) Long id,
                                          @RequestParam(value = "queryToken", defaultValue = "false") boolean queryToken) {
        isAdmin();
        return tokenService.getToken(user, id, queryToken);
    }

    /**
     * 模糊查询应用名称下拉列表
     *
     * <AUTHOR>
     * @since 2024.03.11
     * @param search search
     * @return com.alipay.codegencore.model.response.BaseResponse<java.util.List<java.lang.String>>
     */
    @GetMapping(path = "/queryAppNames")
    public BaseResponse<List<String>> getAppNames(@RequestParam(value = "search") String search){
        isAdmin();
        return backendAppQueryService.searchAppName(search);

    }

    /**
     * 判断是否是管理员
     */
    private void isAdmin() {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
    }

}