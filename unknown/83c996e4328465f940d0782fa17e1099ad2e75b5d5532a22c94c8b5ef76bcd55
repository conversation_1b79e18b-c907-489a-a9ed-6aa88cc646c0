package com.alipay.codegencore.web;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.AnswerIndexBuildJobMapper;
import com.alipay.codegencore.dal.mapper.ChatSessionDOMapper;
import com.alipay.codegencore.dal.mapper.PluginDOMapper;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.service.answer.AnswerIndexService;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.GPTCacheService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.ContentCheckService;
import com.alipay.codegencore.service.common.DataCheckService;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.common.RcsmartCheckService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.AciOpenapiService;
import com.alipay.codegencore.service.impl.BackendAppQueryService;
import com.alipay.codegencore.service.middle.dingding.RobotSingleMessageService;
import com.alipay.codegencore.service.middle.msgbroker.CodegencoreModelHealthInspectionListener;
import com.alipay.codegencore.service.middle.zsearch.ZsearchCommonService;
import com.alipay.codegencore.service.tool.learning.FunctionCallService;
import com.alipay.codegencore.service.tool.learning.plugin.impl.AssetPlatformText2SqlPipelinePlugin;
import com.alipay.codegencore.service.utils.TbaseCacheService;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.StreamDataListener;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Flow;


/**
 * Restful 示例
 */
@RestController
@RequestMapping("/api/test")
public class TestController {
    private static final Logger LOGGER = LoggerFactory.getLogger(TestController.class);

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource
    private ContentCheckService contentCheckService;
    @Resource
    private DataCheckService dataCheckService;

    @Resource
    private CalculateTokenService calculateTokenService;

    @Resource
    private UserAuthDOMapper userAuthDOMapper;

    @Resource
    private MayaService mayaService;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource
    private RobotSingleMessageService robotSingleMessageService;

    @Resource
    private RcsmartCheckService rcsmartCheckService;

    @Resource
    private TbaseCacheService tbaseCacheService;

    @Resource
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private GPTCacheService gptCacheService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private AssetPlatformText2SqlPipelinePlugin assetPlatformText2SqlPlugin;

    @Resource
    private CodegencoreModelHealthInspectionListener codegencoreModelHealthInspectionListener;

    @Resource
    private FunctionCallService functionCallService;

    @Resource
    private PluginDOMapper pluginDOMapper;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private DocumentHandleService documentHandleService;

    @Resource
    private ZsearchCommonService zsearchCommonService;

    @Resource
    private BackendAppQueryService backendAppQueryService;

    @Autowired
    private com.alipay.codegencore.service.middle.tbase.TbaseCacheService tbaseCacheServiceimpl;

    @Resource
    private AciOpenapiService aciOpenapiService;
    @Resource
    private AnswerIndexBuildJobMapper answerIndexBuildJobMapper;

    @Resource
    private AnswerIndexService answerIndexService;

    private String ALG_LINE_URL = "http://zark.sh.global.alipay.com/deveffinlp/codecompletion_line_python/codecompletion_line";

    private String ALG_FRAGMENT_URL = "http://zark.sh.global.alipay.com/deveffinlp/codecompletion_snippet_java/codecompletion_snippet";


//    @GetMapping("/getCache")
//    public BaseResponse getCache(String cacheKey) {
//        Serializable result = tbaseCacheServiceimpl.getCache(cacheKey);
//        return BaseResponse.build(result);
//    }

    /**
     * 增/减 int类型的tbase的val
     * @param lockStr
     * @param key
     * @param addNum
     * @return
     */
//    @PostMapping("/andCacheVal")
//    public BaseResponse<Object> andCacheVal(@RequestParam(required = false) String lockStr, @RequestParam String key, @RequestParam Integer addNum){
//        if (StringUtils.isNotBlank(lockStr)) {
//            boolean lockRet = tbaseCacheService.getLock(lockStr, 5 * 1000, 2 * 1000);
//            if (!lockRet) {
//                return BaseResponse.build(ResponseEnum.HTTP_ERROR);
//            }
//        }
//        Serializable serializable = defaultCacheManager.get(key);
//        int count = serializable == null ? 0 : (Integer) serializable;
//        count += addNum;
//        defaultCacheManager.set(key, count);
//        if (StringUtils.isNotBlank(lockStr)) {
//            tbaseCacheService.releaseLock(lockStr);
//        }
//        return BaseResponse.build(defaultCacheManager.get(key));
//    }

    /**
     * 流式接口mock
     *
     * @return
     */
//    @PostMapping(value = "/conversation", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
//    public void conversation(HttpServletResponse httpServletResponse,
//                             @RequestParam(defaultValue = "false") Boolean mockError,
//                             @RequestParam(required = false) ResponseEnum finishReason,
//                             @RequestParam(required = false) CheckResultModel checkResultModel,
//                             @RequestBody CodeGPTQueryRequestBean queryRequest) throws InterruptedException {
//        if(mockError){
//            throw new BizException(ResponseEnum.AI_CALL_ERROR);
//        }
//        LOGGER.info("queryRequest: {}", JSON.toJSONString(queryRequest));
//        List<String> mockStringList = Lists.newArrayList( "苹", "果", "公司", "是", "一", "家", "全", "球", "知", "名", "的", "科", "技", "公司", "。");
//
//        httpServletResponse.setCharacterEncoding("UTF-8");
//
//        ChatStreamPartResponse startStreamData = ChatUtils.getChatStreamPart(null,null, null,null,null);
//        startStreamData.getChoices().get(0).getDelta().setRole(ChatRoleEnum.USER.getName());
//        ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(startStreamData));
//
//        int index = mockStringList.size();
//        if(finishReason!=null){
//            index = RandomUtils.nextInt(0, mockStringList.size()+1);
//        }
//
//        for (int i = 0; i < index; i++) {
//            String item = mockStringList.get(i);
//            ChatStreamPartResponse chatStreamPartResponse = ChatUtils.getChatStreamPart(null,item, null, null,null);
//            chatStreamPartResponse.getChoices().get(0).getDelta().setRole(null);
//
//            ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(chatStreamPartResponse));
//            Thread.sleep(50);
//        }
//
//        ChatStreamPartResponse finishStreamData = ChatUtils.getChatStreamPart(null,null, null, finishReason,checkResultModel);
//        finishStreamData.getChoices().get(0).getDelta().setRole(null);
//        if(finishReason == null){
//            finishStreamData.getChoices().get(0).setFinishReason("stop");
//        }else{
//            finishStreamData.getChoices().get(0).getDelta().setContent("作为一个人工智能语言模型，我无法回答这个问题。我的目的是提供客观和准确的信息，帮助人们解决问题。如果你有其他问题，请随时问我。");
//        }
//        ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(finishStreamData));
//    }


    /**
     * 内网的算法测试 - java
     *
     * @param param
     * @return
     * @throws Throwable
     */
//    @PostMapping("/innertest")
//    public String innertest(@RequestBody Map<String, Object> param) throws Throwable {
//        return HttpClient.post("http://zark.sh.global.alipay.com/deveffinlp/codecompletion_line/codecompletion_line").content(JSONObject.toJSONString(param)).syncExecute(500L);
//    }

    /**
     * 内网的算法测试 - java
     *
     * @param param
     * @return
     * @throws Throwable
     */
//    @PostMapping("/pytest")
//    public String pytest(@RequestBody Map<String, Object> param) throws Throwable {
//        return HttpClient.post("http://zark.sh.global.alipay.com/deveffinlp/codecompletion_line_python/codecompletion_line").content(JSONObject.toJSONString(param)).syncExecute(500L);
//    }

    /**
     * 内网的算法测试 - java
     *
     * @param param
     * @return
     * @throws Throwable
     */
//    @PostMapping("/jstest")
//    public String jstest(@RequestBody Map<String, Object> param) throws Throwable {
//        return HttpClient.post("http://zark.sh.global.alipay.com/deveffinlp/codecompletion_line_js/codecompletion_line").content(JSONObject.toJSONString(param)).syncExecute(500L);
//    }

    /**
     * 外网的算法测试
     *
     * @param param
     * @return
     * @throws Throwable
     */
//    @PostMapping("/outertest")
//    public String outertest(@RequestBody Map<String, Object> param) throws Throwable {
//        return HttpClient.post("http://zark.sh.global.alipay.com/deveffinlp/tsingyan_line/codecompletion_line").content(JSONObject.toJSONString(param)).syncExecute(500L);
//    }

    /**
     * 内网片段的算法测试
     *
     * @param param
     * @return
     * @throws Throwable
     */
//    @PostMapping("/inFragmentTest")
//    public String inFragmentTest(@RequestBody Map<String, Object> param) {
//        LOGGER.info("inFragmentTest param:{}", JSONObject.toJSONString(param));
//        return HttpClient.post(ALG_FRAGMENT_URL).content(JSONObject.toJSONString(param)).syncExecute(500L);
//    }


    /**
     * gpt-neox模型line补全测试
     *
     * @return
     * @throws Throwable
     */
//    @PostMapping("/getLineCompletion")
//    public String getLineCompletion(@RequestBody Map<String, Object> param) throws Throwable {
//        LOGGER.info("getLineCompletion param:{}", JSONObject.toJSONString(param));
//        return HttpClient.post(ALG_LINE_URL).content(JSONObject.toJSONString(param)).syncExecute(500L);
//    }


    /**
     * 测试内容审查接口
     *
     * @return 结果
     */
//    @PostMapping("/testContentCheck")
//    public BaseResponse<ReviewResultModel> testContentCheck(@RequestBody JSONObject param) {
//        LOGGER.info("testContentCheck param:{}", JSONObject.toJSONString(param));
//        String text = param.getString("text");
//        String messageId = param.getString("messageId");
//        String questionUid = param.getString("questionUid");
//        String bizId = param.getString("bizId");
//        Integer batch = param.getInteger("batch");
//        ChatRoleEnum chatRoleEnum = ChatRoleEnum.valueOf(param.getString("chatRoleEnum"));
//        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
//        chatRequestExtData.setBizId(bizId);
//        return BaseResponse.build(contentCheckService.checkContent(text, chatRoleEnum, messageId, batch,null, chatRequestExtData, questionUid));
//    }

    /**
     * 检测数据安全
     *
     * @param param 参数
     * @return ret
     */
//    @PostMapping("/checkData")
//    public BaseResponse<ReviewResultModel> checkData(@RequestBody JSONObject param) {
//        LOGGER.info("testContentCheck,param:{}", param.toJSONString());
//        String messageUid = param.getString("messageUid");
//        Integer batch = param.getInteger("batch");
//        String text = param.getString("text");
//        return BaseResponse.build(dataCheckService.checkData(messageUid, batch, text));
//    }

    /**
     * 检测数据安全
     *
     * @param param 参数
     * @return ret
     */
//    @PostMapping("/rcsmartCheck")
//    public BaseResponse<ReviewResultModel> rcsmartCheck(@RequestBody JSONObject param) {
//        LOGGER.info("rcsmartCheck,param:{}", param.toJSONString());
//        String messageUid = param.getString("messageUid");
//        Integer batch = param.getInteger("batch");
//        String text = param.getString("text");
//        ChatRoleEnum chatRoleEnum = EnumUtils.getEnumIgnoreCase(ChatRoleEnum.class,param.getString("chatRoleEnum"));
//        return BaseResponse.build(rcsmartCheckService.rcsmartCheck(messageUid,batch,text,chatRoleEnum));
//    }

    /**
     * 检查AI模型
     */
//    @PostMapping("/checkAIModel")
//    public BaseResponse checkAIModel() {
//        appThreadPool.execute(()->{
//            DingDingUtil.sendMessage("开始手动进行AI模型健康检查");
//            codegencoreModelHealthInspectionListener.handle(new UniformEvent());
//            DingDingUtil.sendMessage("健康检查结束");
//        });
//        return BaseResponse.buildSuccess();
//    }

    /**
     * 删除缓存
     *
     * @param keys 参数
     * @return ret
     */
//    @PostMapping("/removeCache")
//    public BaseResponse<String> removeCache(@RequestBody List<String> keys) {
//        for (String key : keys) {
//            refreshableCommonTbaseCacheManager.del(key);
//        }
//        return BaseResponse.buildSuccess();
//    }

    /**
     * 测试流式接口
     */
//    @GetMapping(path="/testStream", produces = {"application/json;charset=UTF-8", "text/event-stream;charset=UTF-8"})
//    public void testStream(HttpServletResponse httpServletResponse) {
//        try {
//            ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
//            chatCompletionRequest.setStream(true);
//            chatCompletionRequest.setModel("gpt-3.5-turbo");
//            List<ChatMessage> list = new ArrayList<>();
//            ChatMessage chatMessage = new ChatMessage();
//            chatMessage.setRole("system");
//            chatMessage.setContent("use chinese to chat");
//
//            ChatMessage chatMessage1 = new ChatMessage();
//            chatMessage1.setRole("user");
//            chatMessage1.setContent("苹果公司是做什么的");
//            list.add(chatMessage);
//            list.add(chatMessage1);
//
//
//            chatCompletionRequest.setMessages(list);
//
//            httpServletResponse.setCharacterEncoding("utf-8");
//            //此处去掉header密钥,真实测试手动添加
//            HttpClient.post("https://unifyaiproxy-pre.antgroup-inc.cn/openai/mockChatCompletion")
//                    .header("codegpt_user","xxx")
//                    .header("codegpt_token", "xxx")
//                    .content(JSONObject.toJSONString(chatCompletionRequest))
//                    .streamExecute(300000, new DefaultDataListener(httpServletResponse),(statusCode, errorResponse)->{
//                        LOGGER.error("testStream error,statusCode:{},errorResponse:{}", statusCode, errorResponse);
//                        httpServletResponse.setContentType("application/json;charset=UTF-8");
//                        httpServletResponse.setCharacterEncoding("UTF-8");
//                        httpServletResponse.setStatus(statusCode);
//                        try {
//                            httpServletResponse.getWriter().write(errorResponse);
//                            httpServletResponse.getWriter().flush();
//                        } catch (IOException e) {
//                            throw new RuntimeException(e);
//                        }
//                    });
//        } catch (Throwable throwable) {
//            LOGGER.error("testStream error", throwable);
//        }
//    }

    /**
     * 默认的stream流数据监听器
     */
    static class DefaultDataListener implements StreamDataListener {

        private final HttpServletResponse response;

        /**
         * 构造函数
         * @param response servlet响应
         */
        public DefaultDataListener(HttpServletResponse response) {
            this.response = response;
            response.setContentType("text/event-stream;charset=UTF-8");
        }

        @Override
        public void onConnect(Flow.Subscription subscription) {
            LOGGER.info("onConnect");
        }


        @Override
        public void eachData(String data, Flow.Subscription subscription) {
            try {
                LOGGER.info("eachData:{}", data);
                response.getWriter().write(data);
                response.getWriter().write("\n");
                response.getWriter().flush();
            } catch (Throwable throwable) {
                LOGGER.error("eachData error", throwable);
            }
        }

        @Override
        public void onError(Throwable throwable) {
            LOGGER.error("onError", throwable);
        }

        @Override
        public void onComplete() {
            LOGGER.info("onComplete");
        }
    }

    /**
     * 检测数据安全
     *
     * @param param 参数
     * @return ret
     */
//    @PostMapping("/checkDataAntDsr")
//    public BaseResponse<ReviewResultModel> checkDataAntDsr(@RequestBody JSONObject param) {
//        LOGGER.info("checkDataAntDsr,param:{}", param.toJSONString());
//        String messageUid = param.getString("messageUid");
//        Integer batch = param.getInteger("batch");
//        String text = param.getString("text");
//        ChatRequestExtData chatRequestExtData = JSON.parseObject(param.getJSONObject("chatRequestExtData").toJSONString(), ChatRequestExtData.class);
//        return BaseResponse.build(dataCheckService.antDsrCheckData(messageUid, batch, text, chatRequestExtData));
//    }

    /**
     * 获取token数量
     *
     * @param param 参数
     * @return token数量
     */
//    @GetMapping("/getTokenQty")
//    public BaseResponse<Long> getTokenQty(@RequestBody JSONObject param) {
//        return BaseResponse.build(calculateTokenService.getTokenQty(param.getString("text")));
//    }

    /**
     * 测试Maya接口
     * @param param
     * @return
     */
//    @PostMapping("/testMaya")
//    public BaseResponse<String> testMaya(@RequestBody JSONObject param) {
//        String data=param.getString("data");
//        return BaseResponse.build(mayaService.getInferenceResult(data, "gptneox", "2gpu", 40000,"data","res",false));
//    }

    /**
     * 移除gpt缓存
     * @param model 模型
     * @return 操作是否成功
     */
//    @PostMapping("/removeGptCache")
//    @CodeTalkWebApi
//    public BaseResponse<Boolean> removeGptCache(HttpServletRequest request,
//                                                HttpServletResponse response,
//                                                @RequestParam String model) {
//        if(!userAclService.isAdmin()){
//            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
//        }
//        return BaseResponse.build(gptCacheService.removeCache(model));
//    }

    /**
     * 获取gpt缓存
     * @param model 模型
     * @return 操作是否成功
     */
//    @PostMapping("/getGptCache")
//    @CodeTalkWebApi
//    public BaseResponse<GptCacheResponse> getGptCache(HttpServletRequest request,
//                                                      HttpServletResponse response,
//                                                      @RequestParam String model,
//                                                      @RequestBody List<ChatMessage> chatMessageList) {
//        if(!userAclService.isAdmin()){
//            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
//        }
//
//        AlgoBackendDO algoBackendDO=new AlgoBackendDO();
//        algoBackendDO.setModel(model);
//
//        GptAlgModelServiceRequest gptAlgModelServiceRequest=new GptAlgModelServiceRequest();
//        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
//        chatCompletionRequest.setMessages(chatMessageList);
//        gptAlgModelServiceRequest.setChatCompletionRequest(chatCompletionRequest);
//
//        return BaseResponse.build(gptCacheService.getCache(algoBackendDO, gptAlgModelServiceRequest, ShortUid.getUid()));
//    }

    /**
     * 存储gpt缓存
     * @param model 模型
     * @return 操作是否成功
     */
//    @PostMapping("/putGptCache")
//    @CodeTalkWebApi
//    public BaseResponse<Boolean> putGptCache(HttpServletRequest request,
//                                             HttpServletResponse response,
//                                             @RequestParam String model,
//                                             @RequestParam String answer,
//                                             @RequestBody List<ChatMessage> chatMessageList) {
//        if(!userAclService.isAdmin()){
//            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
//        }
//
//        return BaseResponse.build(gptCacheService.putCache(model, chatMessageList, answer, ShortUid.getUid()));
//    }

    /**
     * 测试接口，批量改变用户状态
     * key:
     * type: 1 删除用户,value是empId列表的字符串
     * type: 2 改变用户状态,value是对象,包含empId列表字符串和用户状态
     * type: 3 增加新用户,value是jsonArray数组,每个元素是一个jsonObject,包含empId和name
     *
     * @return
     */
//    @PostMapping("/chanUser")
//    public BaseResponse chanUser(@RequestBody Map<String, Object> param) {
//
//        Object type = param.get("type");
//        Object value = param.get("value");
//        if (type == null || value == null) {
//            return BaseResponse.buildSuccess();
//        }
//        switch (type.toString()) {
//            case "1":
//                List<String> delEmpIdList = Arrays.stream(value.toString().split(",")).collect(Collectors.toList());
//                UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
//                userAuthDOExample.createCriteria().andEmpIdIn(delEmpIdList);
//                List<UserAuthDO> userAuthDOS = userAuthDOMapper.selectByExample(userAuthDOExample);
//                if (userAuthDOS != null && userAuthDOS.size() != 0) {
//                    for (UserAuthDO userAuthDO : userAuthDOS) {
//                        refreshableCommonTbaseCacheManager.del(AppConstants.CACHE_PREFIX + userAuthDO.getEmpId());
//                        userAuthDOMapper.deleteByPrimaryKey(userAuthDO.getId());
//                    }
//                }
//                break;
//            case "2":
//                Map<String, Object> map = (Map<String, Object>) value;
//
//                UserStatusEnum userStatusEnum = UserStatusEnum.getUserStatusEnumByValue(Integer.valueOf(map.get("status").toString()));
//                if (userStatusEnum == UserStatusEnum.UNKNOWN) {
//                    return BaseResponse.build(ResponseEnum.ERROR_THROW);
//                }
//                List<String> updateEmpIdList = Arrays.stream(map.get("empIdList").toString().split(",")).collect(Collectors.toList());
//                UserAuthDOExample userAuthDOExample1 = new UserAuthDOExample();
//                userAuthDOExample1.createCriteria().andEmpIdIn(updateEmpIdList);
//                List<UserAuthDO> updateUserList = userAuthDOMapper.selectByExample(userAuthDOExample1);
//                if (updateUserList != null && updateUserList.size() != 0) {
//                    for (UserAuthDO userAuthDO : updateUserList) {
//                        refreshableCommonTbaseCacheManager.del(AppConstants.CACHE_PREFIX + userAuthDO.getEmpId());
//                        userAuthDO.setStatus(userStatusEnum);
//                        userAuthDOMapper.updateByPrimaryKeySelective(userAuthDO);
//                    }
//                }
//                break;
//            case "3":
//                List<Map<String,String>> result = (List<Map<String, String>>) value;
//
//                for (int i = 0; i < result.size(); i++) {
//                    Map<String, String> elementMap = result.get(i);
//                    String empId = elementMap.get("empId");
//                    String name = elementMap.get("name");
//                    if (empId == null || name == null) {
//                        return BaseResponse.build(ResponseEnum.ERROR_THROW, "empId 和 name 不能都为空");
//                    }
//                    if(empId.length()==5){
//                        empId = "0"+empId;
//                    }
//                    UserAuthDOExample userAuthDOExample2 = new UserAuthDOExample();
//                    userAuthDOExample2.createCriteria().andEmpIdEqualTo(empId);
//                    List<UserAuthDO> existUserList = userAuthDOMapper.selectByExample(userAuthDOExample2);
//                    if(existUserList == null || existUserList.size() == 0){
//                        UserAuthDO userAuthDO = new UserAuthDO();
//                        userAuthDO.setEmpId(empId.trim());
//                        userAuthDO.setUserName(name.trim());
//                        userAuthDO.setBuName("");
//                        String token = ShortUid.getUid();
//                        userAuthDO.setToken(token);
//                        userAuthDO.setAdmin((byte) 0);
//                        userAuthDO.setStatus(UserStatusEnum.ACTIVE);
//                        userAuthDOMapper.insertSelective(userAuthDO);
//                    }else{
//                        UserAuthDO userAuthDO = existUserList.get(0);
//                        userAuthDO.setStatus(UserStatusEnum.ACTIVE);
//                        userAuthDOMapper.updateByPrimaryKeySelective(userAuthDO);
//                        refreshableCommonTbaseCacheManager.del(AppConstants.CACHE_PREFIX + userAuthDO.getEmpId());
//                    }
//
//
//                }
//                break;
//            default:
//                break;
//        }
//        return BaseResponse.buildSuccess();
//    }

    /**
     * 获取用户各个平台是否需呀展示审核失败的提醒框
     * @param reviewPlatformEnum 平台
     * @param empId 工号
     * @return
     */
//    @GetMapping("/getUserReview")
//    public BaseResponse<Object> getUserReview(@RequestParam ReviewPlatformEnum reviewPlatformEnum,
//                                              @RequestParam(required = false) String empId){
//        if (StringUtils.isNotBlank(empId)) {
//            Serializable ret = refreshableCommonTbaseCacheManager.hget(AppConstants.USER_NEED_REVIEW+reviewPlatformEnum.name(), empId);
//            return BaseResponse.build(ret);
//        }
//        Map<String, Serializable> map = refreshableCommonTbaseCacheManager.hgetAll(AppConstants.USER_NEED_REVIEW+reviewPlatformEnum.name());
//        return BaseResponse.build(map);
//    }@GetMapping("/getUserReview")
//    public BaseResponse<Object> getUserReview(@RequestParam ReviewPlatformEnum reviewPlatformEnum,
//                                              @RequestParam(required = false) String empId){
//        if (StringUtils.isNotBlank(empId)) {
//            Serializable ret = refreshableCommonTbaseCacheManager.hget(AppConstants.USER_NEED_REVIEW+reviewPlatformEnum.name(), empId);
//            return BaseResponse.build(ret);
//        }
//        Map<String, Serializable> map = refreshableCommonTbaseCacheManager.hgetAll(AppConstants.USER_NEED_REVIEW+reviewPlatformEnum.name());
//        return BaseResponse.build(map);
//    }


    /**
     * 向缓存列表中添加一个元素
     * @param param
     * @return
     */
//    @PostMapping("/cache/rpush")
//    public BaseResponse<Long> rpush(@RequestBody JSONObject param) {
//        String key=param.getString("key");
//
//        long timeBeforeRpush = System.currentTimeMillis();
//
//        noneSerializationCacheManager.rpush(key, new BytesObject(param.getString("value").getBytes(StandardCharsets.UTF_8)));
//        LOGGER.info("rpush耗时：{}", System.currentTimeMillis() - timeBeforeRpush);
//        noneSerializationCacheManager.expire(key, 300);
//        return BaseResponse.buildSuccess();
//    }

    /**
     * 从缓存队列左边取出一个元素
     * @param param
     * @return
     */
//    @GetMapping("/cache/lpop")
//    public BaseResponse<String> lpop(@RequestBody JSONObject param) {
//        String key=param.getString("key");
//
//        long timeBeforeLpop = System.currentTimeMillis();
//        BytesObject result = (BytesObject)noneSerializationCacheManager.lpop(key);
//
//        if(result==null){
//            return BaseResponse.buildSuccess();
//        }
//        String deserialized = StringUtils.toEncodedString(result.getBytes(), StandardCharsets.UTF_8);
//        LOGGER.info("lpop耗时：{}", System.currentTimeMillis() - timeBeforeLpop);
//        return BaseResponse.build(deserialized);
//    }


    /**
     * 获取缓存内容
     * @param param
     * @return
     */
//    @PostMapping("/cache/get")
//    public BaseResponse<String> getTbase(@RequestBody JSONObject param) {
//        String key=param.getString("key");
//
//        BytesObject result = (BytesObject)noneSerializationCacheManager.get(key);
//
//        if(result==null){
//            return BaseResponse.buildSuccess();
//        }
//        String deserialized = StringUtils.toEncodedString(result.getBytes(), StandardCharsets.UTF_8);
//        return BaseResponse.build(deserialized);
//    }

    /**
     * 测试超时接口
     * @param second
     * @return
     * @throws InterruptedException
     */
//    @PostMapping("/testTimeOut")
//    public BaseResponse<String> testTimeOut(HttpServletResponse httpServletResponse, @RequestParam int second) throws InterruptedException {
//        Future<Boolean> future = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool, () -> {
//            boolean finished = true;
//            try {
//                TimeUnit.SECONDS.sleep(second);
//            } catch (InterruptedException e) {
//                finished = false;
//            }
//            return finished;
//        });
//
//        HttpConnectKeepAlive.keepAlive(future, httpServletResponse);
//
//        boolean result;
//        try {
//            result = future.get();
//        } catch (ExecutionException e) {
//            return BaseResponse.build(ResponseEnum.ERROR_THROW);
//        }
//
//        return BaseResponse.build(result + "");
//    }


    /**
     * 获取缓存内容
     * @param param
     * @return
     */
//    @PostMapping("/cache/set")
//    public BaseResponse<String> setTbase(@RequestBody JSONObject param) {
//        String key=param.getString("key");
//        String value = param.getString("value");
//
//        BytesObject bytesObject = new BytesObject(value.getBytes(StandardCharsets.UTF_8));
//        noneSerializationCacheManager.set(key, bytesObject);
//
//        return BaseResponse.buildSuccess();
//    }
    @Resource
    private ChatSessionDOMapper chatSessionDOMapper;
    @Resource
    private CheckService checkService;
    @Resource
    private ChatSessionManageService chatSessionManageService;

    /**
     * 把infoSec审核不通过的标题更新为原始标题
     * @return
     */
//    @PostMapping("/updateSessionTitle")
//    public BaseResponse<Object> updateSessionTitle() {
//        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
//        chatSessionDOExample.createCriteria().andDeletedEqualTo((byte) 0);
//        String orderByStr = "gmt_create desc";
//        chatSessionDOExample.setOrderByClause(orderByStr);
//        List<ChatSessionDO> chatSessionDOS = chatSessionDOMapper.selectByExample(chatSessionDOExample);
//        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
//        chatRequestExtData.setCodeGPTUser(AppConstants.CODEGPT_TOKEN_USER);
//        chatRequestExtData.setContentIsTitle(true);
//        int updateCount = 0;
//        Map<Long, UserAuthDO> userAuthDOMap = new HashMap<>();
//        for (int i = 0; i < chatSessionDOS.size(); i++) {
//            ChatSessionDO chatSessionDO = chatSessionDOS.get(i);
//            UserAuthDO userAuthDO;
//            if (userAuthDOMap.containsKey(chatSessionDO.getUserId())) {
//                userAuthDO = userAuthDOMap.get(chatSessionDO.getUserId());
//            } else {
//                userAuthDO = userAuthDOMapper.selectByPrimaryKey(chatSessionDO.getUserId());
//                userAuthDOMap.put(chatSessionDO.getUserId(),userAuthDO);
//            }
//            LOGGER.info("开始检查第:{}个会话,id:{},uid:{},title:{},empId:{}", i, chatSessionDO.getId(), chatSessionDO.getUid(), chatSessionDO.getTitle(), userAuthDO.getEmpId());
//            chatRequestExtData.setEmpId(userAuthDO.getEmpId());
//            ReviewResultModel reviewResultModel = checkService.getInfoSecCheckRet(chatSessionDO.getTitle(), ChatRoleEnum.ASSISTANT, 1, chatRequestExtData, chatSessionDO.getUid(), chatSessionDO.getUid());
//            // 没审核过的修改标题为默认标题
//            if (!reviewResultModel.isRet()) {
//                chatSessionManageService.updateSessionTitle(chatSessionDO.getUid(), AppConstants.SESSION_DEFAULT_TITLE);
//                updateCount++;
//            }
//        }
//        JSONObject ret = new JSONObject();
//        ret.put("allCount",chatSessionDOS.size());
//        ret.put("updateCount",updateCount);
//        return BaseResponse.build(ret);
//    }

    private List<UserAuthDO> getUserAuthListByEmpId(String empId) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdEqualTo(empId);
        return userAuthDOMapper.selectByExample(userAuthDOExample);
    }

    /**
     * 流式接口mock
     *
     * @return
     */
//    @PostMapping(value = "/pluginConversation", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
//    public void pluginConversation(HttpServletResponse httpServletResponse,
//                             @RequestParam(required = false) ResponseEnum finishReason,
//                             @RequestParam(required = false) String stageError) throws InterruptedException {
//        List<String> stageList = Lists.newArrayList("preRequest", "llm", "postRequest", "summary");
//
//        List<String> messageList = new ArrayList<>();
//        // config
//        messageList.add("config");
//        messageList.add("stop");
//        // 前置接口调用
//        messageList.add("preRequest");
//        messageList.add("前置接口调用中");
//        messageList.add("\n```json\n" + "{\"id\":\"1231123\",\"stage\":\"preRequst\",\"stageName\":\"前置接口调用\",\"content\":\"{\"params\":{\"number\":\"377266\",\"repos\":\"codegencore\",\"branch\":\"test\"},\"request_url\":\"http://example.alipay.com/getCode\"}" + "```\n");
//        messageList.add("前置接口调用完成");
//        messageList.add("\n```json\n" + "{\"errorCode\":\"0\",\"errorType\":\"SUCCESS\",\"preResponse\":{\"errorCode\":1,\"errorType\":\"SOME_ERROR\",\"errorMsg\":null,\"stackTrace\":null,\"data\":{\"RCSMART\":true,\"INFOSEC\":true,\"INTENTION\":true,\"ANTDSR\":true}}}" + "```\n");
//        messageList.add("stop");
//        // 模型调用
//        messageList.add("llm");
//        messageList.add("模型推理中");
//        List<String> mockStringList = Lists.newArrayList( "苹", "果", "公司", "是", "一", "家", "全", "球", "知", "名", "的", "科", "技", "公司", "。");
//        messageList.addAll(mockStringList);
//        messageList.add("模型推理完成");
//        messageList.add("stop");
//        // 后置接口调用
//        messageList.add("postRequest");
//        messageList.add("后置接口调用中");
//        messageList.add("\n```json\n" + "{\"id\":\"1231123\",\"stage\":\"preRequst\",\"stageName\":\"前置接口调用\",\"content\":\"```json\\n{\"params\":{\"number\":\"377266\",\"repos\":\"codegencore\",\"branch\":\"test\"},\"request_url\":\"http://example.alipay.com/getCode\"}}" + "```/n");
//        messageList.add("后置接口调用完成");
//        messageList.add("\n```json\n" + "{\"errorCode\":\"0\",\"errorType\":\"SUCCESS\",\"preResponse\":{\"errorCode\":1,\"errorType\":\"SOME_ERROR\",\"errorMsg\":null,\"stackTrace\":null,\"data\":{\"RCSMART\":true,\"INFOSEC\":true,\"INTENTION\":true,\"ANTDSR\":true}}}" + "```/n");
//        messageList.add("stop");
//        // 总结
//        messageList.add("summary");
//        messageList.add("总结");
//        messageList.add("场景插件总结。\n" + "\n```json/n" + "{\"errorCode\":\"0\",\"errorType\":\"SUCCESS\",\"preResponse\":{\"errorCode\":1,\"errorType\":\"SOME_ERROR\",\"errorMsg\":null,\"stackTrace\":null,\"data\":{\"RCSMART\":true,\"INFOSEC\":true,\"INTENTION\":true,\"ANTDSR\":true}}}" + "```\n");
//
//        Set<String> stageSet = new HashSet<>(Arrays.asList("preRequest", "llm", "postRequest", "summary", "config"));
//        Set<String> stageNameSet = new HashSet<>(Arrays.asList("前置接口调用中", "前置接口调用失败", "前置接口调用完成", "模型推理中", "模型推理失败", "模型推理完成", "后置接口调用中", "后置接口调用失败", "后置接口调用完成", "总结"));
//
//
//        httpServletResponse.setCharacterEncoding("UTF-8");
//
//        PluginInfo pluginInfo = new PluginInfo(123L, "测试插件");
//        String requestId = "3d8be8fd-a2a6-4d1a-84ef-0f8fe576efb1";
//
//        String stage = null;
//        String stageName = null;
//        for (String s : messageList) {
//            if (StringUtils.equals(s, "config")) {
//                PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(pluginInfo,null, requestId, null, "config", stageName, ResponseEnum.SUCCESS.name(),false, null, stageList);
//                ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//                Thread.sleep(50);
//                continue;
//            }
//            if (stageSet.contains(s)) {
//                stage = s;
//                continue;
//            }
//            if(!stageList.contains(stage)) {
//                continue;
//            }
//            if (stageNameSet.contains(s)) {
//                stageName = s;
//                continue;
//            }
//            if (StringUtils.equals(s, "stop")) {
//                // 报错相关
//                if (StringUtils.equals(stage, stageError)) {
//                    switch (stage) {
//                        case "preRequest" :
//                            stageName = "前置接口调用失败";
//                            break;
//                        case "llm" :
//                            stageName = "模型推理失败";
//                            break;
//                        case "postRequest":
//                            stageName = "后置接口调用失败";
//                            break;
//                        case "summary":
//                            stageName = "总结";
//                            break;
//                        default:
//                            break;
//                    }
//                    PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(pluginInfo,null, requestId, null, stage, stageName, finishReason.name(), false,null, null);
//                    ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//                    Thread.sleep(50);
//                    break;
//                }
//                // 正常结束
//                PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(pluginInfo,null, requestId, null, stage, stageName, ResponseEnum.SUCCESS.name(), false, null, null);
//                ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//                Thread.sleep(50);
//                continue;
//            }
//            PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(pluginInfo,null, requestId, s, stage, stageName, null, false, null, null);
//            ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//            Thread.sleep(50);
//        }
//    }

//    @PostMapping(value = "/multiPluginConversation", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
//    public void multiPluginConversation(HttpServletResponse httpServletResponse,
//                                   @RequestParam(required = false) ResponseEnum finishReason,
//                                   @RequestParam(required = false) String stageError,
//                                   @RequestParam(required = false) Integer pluginNum) throws InterruptedException {
//
//        List<String> messageList = new ArrayList<>();
//        // config
//        messageList.add("config");
//        messageList.add("stop");
//        // 前置接口调用
//        messageList.add("preRequest");
//        messageList.add("前置接口调用中");
//        messageList.add("\n```json\n" + "{\"id\":\"1231123\",\"stage\":\"preRequst\",\"stageName\":\"前置接口调用\",\"content\":\"{\"params\":{\"number\":\"377266\",\"repos\":\"codegencore\",\"branch\":\"test\"},\"request_url\":\"http://example.alipay.com/getCode\"}" + "```\n");
//        messageList.add("前置接口调用完成");
//        messageList.add("\n```json\n" + "{\"errorCode\":\"0\",\"errorType\":\"SUCCESS\",\"preResponse\":{\"errorCode\":1,\"errorType\":\"SOME_ERROR\",\"errorMsg\":null,\"stackTrace\":null,\"data\":{\"RCSMART\":true,\"INFOSEC\":true,\"INTENTION\":true,\"ANTDSR\":true}}}" + "```\n");
//        messageList.add("stop");
//        // 模型调用
//        messageList.add("llm");
//        messageList.add("模型推理中");
//        List<String> mockStringList = Lists.newArrayList( "苹", "果", "公司", "是", "一", "家", "全", "球", "知", "名", "的", "科", "技", "公司", "。");
//        messageList.addAll(mockStringList);
//        messageList.add("模型推理完成");
//        messageList.add("stop");
//        // 后置接口调用
//        messageList.add("postRequest");
//        messageList.add("后置接口调用中");
//        messageList.add("\n```json\n" + "{\"id\":\"1231123\",\"stage\":\"preRequst\",\"stageName\":\"后置接口调用中\",\"content\":\"```json\\n{\"params\":{\"number\":\"377266\",\"repos\":\"codegencore\",\"branch\":\"test\"},\"request_url\":\"http://example.alipay.com/getCode\"}}" + "```/n");
//        messageList.add("后置接口调用完成");
//        messageList.add("\n```json\n" + "{\"errorCode\":\"0\",\"errorType\":\"SUCCESS\",\"preResponse\":{\"errorCode\":1,\"errorType\":\"SOME_ERROR\",\"errorMsg\":null,\"stackTrace\":null,\"data\":{\"RCSMART\":true,\"INFOSEC\":true,\"INTENTION\":true,\"ANTDSR\":true}}}" + "```/n");
//        messageList.add("stop");
//        // 总结
//        messageList.add("summary");
//        messageList.add("总结");
//        messageList.add("场景插件总结。\n" + "\n```json/n" + "{\"errorCode\":\"0\",\"errorType\":\"SUCCESS\",\"preResponse\":{\"errorCode\":1,\"errorType\":\"SOME_ERROR\",\"errorMsg\":null,\"stackTrace\":null,\"data\":{\"RCSMART\":true,\"INFOSEC\":true,\"INTENTION\":true,\"ANTDSR\":true}}}" + "```\n");
//        messageList.add("stop");
//
//        List<String> stageList = Lists.newArrayList("preRequest", "llm", "postRequest", "summary");
//        Set<String> stageSet = new HashSet<>(Arrays.asList("preRequest", "llm", "postRequest", "summary", "config"));
//        Set<String> stageNameSet = new HashSet<>(Arrays.asList("前置接口调用中", "前置接口调用失败", "前置接口调用完成", "模型推理中", "模型推理失败", "模型推理完成", "后置接口调用中", "后置接口调用失败", "后置接口调用完成", "总结"));
//        httpServletResponse.setCharacterEncoding("UTF-8");
//        httpServletResponse.setContentType("text/event-stream;charset=UTF-8");
//        String requestId = "3d8be8fd-a2a6-4d1a-84ef-0f8fe576efb1";
//        PluginInfo pluginInfo = new PluginInfo(123L, "测试插件");
//
//        String stage = null;
//        String stageName = null;
//        // 多插件内容
//        for (int i = 0; i < pluginNum; i++) {
//            for (String s : messageList) {
//                if (StringUtils.equals(s, "config")) {
//                    PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(requestId, pluginInfo, i, null, "config", stageName, ResponseEnum.SUCCESS.name(),false, null, stageList);
//                    ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//                    Thread.sleep(50);
//                    continue;
//                }
//                if (stageSet.contains(s)) {
//                    stage = s;
//                    continue;
//                }
//                if(!stageList.contains(stage)) {
//                    continue;
//                }
//                if (stageNameSet.contains(s)) {
//                    stageName = s;
//                    continue;
//                }
//                if (StringUtils.equals(s, "stop")) {
//                    PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(requestId, pluginInfo, i,null, stage, stageName, ResponseEnum.SUCCESS.name(), false, null, null);
//                    ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//                    Thread.sleep(50);
//                    continue;
//                }
//                PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(requestId, pluginInfo, i, s, stage, stageName, null, false, null, null);
//                ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//                Thread.sleep(50);
//            }
//        }
//        // 多插件最后输出
//        for (String s : mockStringList) {
//            PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(requestId, null, null, s, null, null, null, false, null, null);
//            ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//            Thread.sleep(100);
//        }
//        PluginStreamPartResponse streamData = ChatUtils.getPluginStreamPart(pluginInfo, null, "-1", null, null, null, ResponseEnum.SUCCESS.name(), false, null, null);
//        ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(streamData));
//    }

    /**
     * 通过uid获取回答的插件日志
     * @param uid
     * @return
     */
//    @GetMapping("/getPluginLog")
//    public BaseResponse<JSONObject> getPluginLog(@RequestParam String uid) {
//        PluginInfo pluginInfo = new PluginInfo(123L, "测试插件");
//
//        PluginLogModel pluginLogModel = new PluginLogModel();
//        pluginLogModel.addStageLog("preRequest", true, "11111", pluginInfo);
//        pluginLogModel.addStageLog("llm", true, "11111", pluginInfo);
//        pluginLogModel.addStageLog("postRequest", true, "11111", pluginInfo);
//        pluginLogModel.addStageLog("summary", true, "11111", pluginInfo);
//        String pluginLogString = JSONObject.toJSONString(pluginLogModel);
//        JSONObject object = JSONObject.parseObject(pluginLogString);
//        return BaseResponse.build(object);
//    }

    /**
     * 生成式搜索线上服务
     */
    @PostMapping(path="/codegptSgchainPluginService")
    public Map<String, Object> codegptSgchainPluginServiceBefore(HttpServletRequest httpServletRequest,
                                                                 @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                                 @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken, @RequestBody JSONObject param) {
        Map<String, Object> errorMsg = checkOpenapiToken(httpServletRequest, codeGPTUser, codeGPTToken);
        if (errorMsg != null) return errorMsg;
        String url = "http://zark.sh.global.alipay.com/deveffinlp/codegpt_sgchain_service/codegpt_sgchain_plugin_service";
        LOGGER.info("request codegptSgchainPluginService, param: {}",JSONObject.toJSONString(param));
        return requestZarkService(url, param);
    }

    private Map<String, Object> requestZarkService(String url, JSONObject param){
        try {
            String data = HttpClient.post(url).content(JSONObject.toJSONString(param)).syncExecute(10000L);

            JSONObject response = JSONObject.parseObject(data);
            Map<String, Object> result = new HashMap<>();
            int errorCode = response.getInteger("errorCode");
            if(errorCode != 0){
                result.put("code", errorCode);
                result.put("msg", response.getString("errorMsg"));
                return result;
            }else{
                JSONObject serviceResult = response.getJSONObject("result");
                return serviceResult.getJSONObject("CodegptSgchainPluginService");
            }
        } catch (Throwable throwable) {
            LOGGER.error("request zark service error", throwable);
            Map<String, Object> result = new HashMap<>();
            result.put("code", "500");
            result.put("msg", String.format("request codegpt_sgchain_service error, %s", throwable.getMessage()));
            return result;
        }
    }

    /**
     * 资产平台text2sql前置接口
     */
    @PostMapping(path="/assetText2Sql")
    public Map<String, Object> assetText2Sql(HttpServletRequest httpServletRequest,
                                             @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                             @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken, @RequestBody JSONObject param) {
        Map<String, Object> errorMsg = checkOpenapiToken(httpServletRequest, codeGPTUser, codeGPTToken);
        if (errorMsg != null) return errorMsg;
        return assetPlatformText2SqlPlugin.preRequest(param);
    }
    /**
     * 资产平台text2sql后置接口
     */
    @PostMapping(path="/assetText2SqlExecutor")
    public Map<String, Object> assetText2SqlExecutor(HttpServletRequest httpServletRequest,
                                                     @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                     @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,@RequestBody JSONObject param) {
        Map<String, Object> errorMsg = checkOpenapiToken(httpServletRequest, codeGPTUser, codeGPTToken);
        if (errorMsg != null) return errorMsg;
        return assetPlatformText2SqlPlugin.postRequest(param);
    }
    /**
     * 校验token权限
     *
     * <AUTHOR>
     * @since 2024.11.08
     * @param httpServletRequest httpServletRequest
     * @param codeGPTUser codeGPTUser
     * @param codeGPTToken codeGPTToken
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    @Nullable
    private Map<String, Object> checkOpenapiToken(HttpServletRequest httpServletRequest,  String codeGPTUser,  String codeGPTToken) {
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            LOGGER.info("assistant conversation request not authorized, user: {}", codeGPTUser);
            HashMap<String, Object> errorMsg = new HashMap<>();
            errorMsg.put("code", "6");
            errorMsg.put("errorMsg", "权限不足");
            return errorMsg;
        }
        return null;
    }
    /**
     * ACi插件触发流水线接口
     *
     * <AUTHOR>
     * @since 2024.05.16
     * @param param param
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
//    @PostMapping(path = "/triggerPipeline")
//    public Map<String, String> triggerPipeline(@RequestBody JSONObject param) {
//        return aciOpenapiService.triggerAciPipeline(param);
//    }

    /**
     * function call mock
     */
    @PostMapping(path="/functionCallMock")
    public Map<String, Object> functionCallMock(HttpServletRequest httpServletRequest,
                                                @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken, @RequestBody JSONObject param) {
        Map<String, Object> errorMsg = checkOpenapiToken(httpServletRequest, codeGPTUser, codeGPTToken);
        if (errorMsg != null) return errorMsg;
        LOGGER.info("functionCallMock, param: {}",JSONObject.toJSONString(param));
        return new HashMap<>(){{put("result", "任务执行完成");}};
    }

    /**
     * 测试Function call
     */
//    @PostMapping(path="/testFunctionCall")
//    public BaseResponse<Object> testFunctionCall(@RequestBody ChatCompletionRequest chatRequest) {
//        PluginDOExample pluginDOExample = new PluginDOExample();
//        pluginDOExample.createCriteria().andIdIn(Arrays.asList(33L));
//
//        List<PluginDO> pluginDOList = pluginDOMapper.selectByExample(pluginDOExample);
//
//        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName("chatgpt");
//        PluginServiceRequestContext params = new PluginServiceRequestContext(null, ShortUid.getUid(), AppConstants.CODEGPT_TOKEN_USER, null, false, algoBackendDO, chatRequest
//                ,null, false);
//
//        // 流式会用到这个Handler
//        params.setUniqueAnswerId(ShortUid.getUid());
//
//        functionCallService.streamChat(params, pluginDOList);
//        return BaseResponse.buildSuccess();
//    }

    /**
     * 搜索function
     */
//    @PostMapping(path="/searchFunction")
//    public Map<String, Object> searchFunction(@RequestBody JSONObject param) {
//        LOGGER.info("searchFunction, param: {}",JSONObject.toJSONString(param));
//        List<ChatMessage> messages = param.getJSONArray("messages").toJavaList(ChatMessage.class);
//        List<ChatFunction> functions = param.getJSONArray("functions").toJavaList(ChatFunction.class);
//
//        LOGGER.info("searchFunction, message list size: {}, function list size: {}", messages.size(), functions.size());
//        Map<String,Object> result = new HashMap<>();
//        result.put("qualifiedFunctions", functions);
//        return result;
//    }

    /**
     * 机器人发送单条消息
     */
//    @PostMapping(path="/sendMessage")
//    public String sendMessage(@RequestBody List<String> userIds) {
//        DingDingMessageDO dingDingMessageDO = new DingDingMessageDO("消息列表展示的内容","这个是纯文本消息");
//        robotSingleMessageService.sendMessage(userIds,dingDingMessageDO);
//        dingDingMessageDO = new DingDingMessageDO("消息列表展示的内容","这个是含链接的消息","查看详情","https://open.dingtalk.com");
//        robotSingleMessageService.sendMessage(userIds,dingDingMessageDO);
//        return "ok";
//    }

    /**
     * 存储segment
     * @param segmentList
     * @return
     * @throws IOException
     */
//    @PostMapping(path="/saveSegment")
//    public BaseResponse saveSegment(@RequestBody List<EmbeddingResponseModel> segmentList) throws IOException {
//        zsearchCommonService.saveDataList(segmentList, ZsearchIndexEnum.CODEFUSE_DOCUMENT_SEGMENT, EmbeddingResponseModel::getPartUid,ZsearchClientEnum.SHARED);
//        return BaseResponse.buildSuccess();
//    }

    /**
     * 召回segment
     * @param param
     * @return
     * @throws IOException
     */
//    @PostMapping(path="/recallSegment")
//    public BaseResponse<List<SegmentInfo>> recallSegment(@RequestBody JSONObject param) {
//        String query = param.getString("query");
//        String sessionId = param.getString("sessionUid");
//        List<SegmentInfo> segmentInfoList = documentHandleService.recallSegment(sessionId, query);
//        return BaseResponse.build(segmentInfoList);
//    }


    /**
     * 获取应用详情
     *
     * <AUTHOR>
     * @param appName 应用名称
     * @return 应用详情
     */
//    @GetMapping(path = "/getAppDetail")
//    public BaseResponse<BackendAppDTO> getAppDetail(@RequestParam(value = "appName") String appName){
//        return backendAppQueryService.getAppDetail(appName);
//    }
    /**
     * 批量更改工具的可见性
     *
     * <AUTHOR>
     * @since 2024.08.16
     * @param param param
     * @return com.alipay.codegencore.model.response.BaseResponse
     */
//    @PostMapping(path = "/pluginAuthChange")
//    public BaseResponse changePluginAuth(@RequestBody JSONObject param){
//        List<Long> pluginIdList = param.getJSONArray("pluginIdList").toJavaList(Long.class);
//        PluginDOExample pluginDOExample = new PluginDOExample();
//        PluginDOExample.Criteria criteria = pluginDOExample.createCriteria();
//        criteria.andIdNotIn(pluginIdList);
//        PluginDO pluginDO = new PluginDO();
//        pluginDO.setVisableUser(1);
//        pluginDOMapper.updateByExampleSelective(pluginDO, pluginDOExample);
//        return BaseResponse.buildSuccess();
//    }
    /**
     * 批量删除小于此id(不包含此id)的job
     *
     * <AUTHOR>
     * @since 2024.08.16
     * @param jobId jobId
     * @return com.alipay.codegencore.model.response.BaseResponse
     */
//    @GetMapping(path = "/deleteJobs")
//    public BaseResponse deleteJobsById(@RequestParam(value = "jobId") Long jobId){
//        while (true){
//            List<Long> jobIdList = answerIndexBuildJobMapper.queryJobIdListLessThanId(jobId, 100);
//            if (CollectionUtils.isEmpty(jobIdList)){
//                break;
//            }
//            LOGGER.info("deleteJobsById, jobIdList: {}",JSONObject.toJSONString(jobIdList));
//            answerIndexBuildJobMapper.deleteByIdList(jobIdList);
//        }
//        return BaseResponse.buildSuccess();
//    }

//    @PostMapping(path = "/jobRetry")
//    public BaseResponse retryJobs(@RequestBody JSONObject param){
//        if(StringUtils.isBlank(param.getString("repository"))||StringUtils.isBlank(param.getString("branch"))){
//            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"repository or branch is blank");
//        }
//        String[] split = param.getString("repository").split("/");
//        String groupPath =  split[0];
//        String projectPath = split[1];
//        AnswerIndexBuildTaskDO task = answerIndexService.getTask(groupPath, projectPath, param.getString("branch"));
//        if(task == null){
//            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"没有找到task");
//        }
//        List<Long> retryJobs = answerIndexService.getRetryJobsByTask(task.getId());
//        LOGGER.info("need to retry Jobs:{}", JSONObject.toJSONString(retryJobs));
//        if(CollectionUtils.isEmpty(retryJobs)){
//            return BaseResponse.buildSuccess();
//        }
//        answerIndexService.retryJobs(retryJobs, task.getId(), JobState.INIT);
//        return BaseResponse.buildSuccess();
//    }
//    @PostMapping(path = "/getFailJobList")
//    public BaseResponse<List<Long>> getFailJobIdList(@RequestBody JSONObject param){
//        if(StringUtils.isBlank(param.getString("repository"))||StringUtils.isBlank(param.getString("branch"))){
//            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"repository or branch is blank");
//        }
//        String[] split = param.getString("repository").split("/");
//        String groupPath =  split[0];
//        String projectPath = split[1];
//        AnswerIndexBuildTaskDO task = answerIndexService.getTask(groupPath, projectPath, param.getString("branch"));
//        if(task == null){
//            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"没有找到task");
//        }
//        List<Long> retryJobs = answerIndexService.getRetryJobsByTask(task.getId());
//        LOGGER.info("get fail Jobs num is:{}", retryJobs.size());
//        return BaseResponse.build(retryJobs);
//    }

    /**
     * 获取失败任务，可重置状态
     * @param param
     * @return
     */
//    @PostMapping(path = "/getFailJobsDetail")
//    public BaseResponse<List<AnswerIndexBuildJobDO>> getFailJobs(@RequestBody JSONObject param){
//        if(!param.containsKey("jobIds")){
//            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"jobIds is blank");
//        }
//        List<Long> jobIds = param.getJSONArray("jobIds").toJavaList(Long.class);
//        boolean retryFailJobs = param.getBooleanValue("retryFailJobs");
//        if(CollectionUtils.isEmpty(jobIds)){
//            return BaseResponse.build(null);
//        }
//        LOGGER.info("get fail Jobs num is:{}", jobIds.size());
//        if(retryFailJobs){
//            answerIndexBuildJobMapper.retryJobs(jobIds, JobState.BUILDING.name());
//            LOGGER.info("set job state to building for id list: {}", JSON.toJSONString(jobIds));
//        }
//        return BaseResponse.build(answerIndexService.getJobByIds(jobIds));
//    }

    /**
     * 更新job状态
     * @param param
     * @return
     */
//    @PostMapping(path = "/updateJobState")
//    public BaseResponse<List<AnswerIndexBuildJobDO>> updateJobState(@RequestBody JSONObject param){
//        if(!param.containsKey("jobIds")){
//            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"jobIds is blank");
//        }
//        List<Long> jobIds = param.getJSONArray("jobIds").toJavaList(Long.class);
//        String targetState = param.getString("targetState");
//        if(CollectionUtils.isEmpty(jobIds)){
//            return BaseResponse.buildSuccess();
//        }
//        LOGGER.info("job id list size:{}", jobIds.size());
//        answerIndexBuildJobMapper.updateJobState(jobIds, targetState);
//        return BaseResponse.buildSuccess();
//    }

}
