package com.alipay.codegencore.model.model.tool.learning.plugin;

import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageLog;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema.FunctionCallStageOutput;
import com.alipay.codegencore.model.openai.ChatFunctionCall;

import java.util.ArrayList;
import java.util.List;


/**
 * 插件日志信息
 */
public class PluginLog {
    
    //插件信息
    private PluginInfo pluginInfo;
    
    //状态
    private boolean status;
    
    //阶段日志列表
    private List<StageLog> stageLogList;

    /**
     * 解析function call
     * @return
     */
    public ChatFunctionCall parseFunctionCall(){
        StageLog stageLog = stageLogList.get(0);
        if(stageLog==null || !StageTypeEnum.FUNCTION_CALL.getName().equalsIgnoreCase(stageLog.getType())){
            return null;
        }
        FunctionCallStageOutput functionCallStageOutput = (FunctionCallStageOutput) stageLog.getStageInfo().getOutput();
        return new ChatFunctionCall(functionCallStageOutput.getName(),functionCallStageOutput.getArguments());
    }

    public PluginLog() {
        stageLogList = new ArrayList<>();
    }

    public PluginInfo getPluginInfo() {
        return pluginInfo;
    }

    public void setPluginInfo(PluginInfo pluginInfo) {
        this.pluginInfo = pluginInfo;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public List<StageLog> getStageLogList() {
        return stageLogList;
    }

    public void setStageLogList(List<StageLog> stageLogList) {
        this.stageLogList = stageLogList;
    }
}
