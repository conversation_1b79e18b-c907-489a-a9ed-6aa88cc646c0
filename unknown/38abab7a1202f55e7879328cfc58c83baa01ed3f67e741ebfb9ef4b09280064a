package com.alipay.codegencore.model.remote;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:42
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MessageDelta {

    @JsonProperty(value = "content", required = true)
    private MessageContent content;

    public MessageContent getContent() {
        return content;
    }

    public void setContent(MessageContent content) {
        this.content = content;
    }

}
