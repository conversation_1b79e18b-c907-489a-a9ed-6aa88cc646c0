/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.impl.codegpt.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.TokenDOExample;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.TokenDOMapper;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.RateLimitTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.request.TokenRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.model.response.TokenResponse;
import com.alipay.codegencore.service.codegpt.DevInsightService;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version TokenServiceImpl.java, v 0.1 2023年08月10日 上午11:21 yhw01352860
 */
@Service("tokenService")
public class TokenServiceImpl implements TokenService {

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    @Autowired
    private TokenDOMapper     tokenDOMapper;
    @Autowired
    private CodeGPTDrmConfig  drmConfig;
    @Autowired
    private UserAclService    userAclService;
    @Autowired
    private UserAuthDOMapper  userAuthDOMapper;
    @Autowired
    private DevInsightService devInsightService;

    /**
     * token列表
     *
     * @param pageNo
     * @param pageSize
     * @param filterField
     * @return
     */
    @Override
    public PageResponse<List<TokenResponse>> tokenList(int pageNo, int pageSize, String filterField) {
        TokenDOExample tokenDOExample = new TokenDOExample();
        if (StringUtils.isNotEmpty(filterField)) {
            tokenDOExample.or().andOwnerUserIdEqualTo(filterField);
            tokenDOExample.or().andTokenEqualTo(filterField);
            tokenDOExample.or().andUserLike(filterField + "%");
        }
        Page<TokenDO> pageInfo = PageHelper.startPage(pageNo, pageSize);
        tokenDOExample.setOrderByClause("gmt_create DESC");
        List<TokenDO> userAuthDOS = tokenDOMapper.selectByExample(tokenDOExample);
        JSONObject qpxRateLimiterConfigJson = drmConfig.getQpxRateLimiterConfigJson();

        List<TokenResponse> responseList = new ArrayList<>();
        for (TokenDO response : userAuthDOS) {
            TokenResponse tokenResponse = new TokenResponse();
            BeanUtils.copyProperties(response, tokenResponse);
            tokenResponse.setToken(encryptionToken(tokenResponse.getToken()));
            tokenResponse.setRateLimiterDesc(getRateLimiterDesc(qpxRateLimiterConfigJson, response.getUser()));
            if (StringUtils.isNotEmpty(response.getOwnerUserId())){
                UserAuthDO userAuthDO = userAuthDOMapper.selectByPrimaryKey(Long.valueOf(response.getOwnerUserId()));
                updateTokenOwnerUser(tokenResponse, userAuthDO);
            }
            responseList.add(tokenResponse);
        }
        return PageResponse.build(ResponseEnum.SUCCESS, responseList, pageInfo.getTotal());
    }

    private void updateTokenOwnerUser(TokenResponse tokenResponse, UserAuthDO userAuthDO) {
        if (userAuthDO == null || tokenResponse == null) {
            return;
        }
        tokenResponse.setNickName(userAuthDO.getUserName());
        if (drmConfig.isIntranetApplication()) {
            tokenResponse.setEmpId(userAuthDO.getEmpId());
        } else {
            tokenResponse.setAlipayAccount(userAuthDO.getAlipayAccount());
            tokenResponse.setPhoneNumber(userAuthDO.getPhoneNumber());
        }
    }

    /**
     * 批拼装限流规则  分三种情况，根据if顺序。1、只有公共池 2、有私有池，但不允许使用公共池 3、私有池和公共池都可以使用
     *
     * @param qpxRateLimiterConfigJson
     * @param user
     * @return
     */
    private String getRateLimiterDesc(JSONObject qpxRateLimiterConfigJson, String user) {
        List<String> limiterDesc = new ArrayList<>();
        String limiterKey = RateLimitTypeEnum.TOKEN_USER.name() + "_" + user;
        boolean hasPrivatePool = qpxRateLimiterConfigJson.containsKey(limiterKey);
        //获取分钟数
        long windowTimeMills;
        //每分钟执行次数
        long windowTotalQuota;
        // 没有私有池,直接走公有池
        if (!hasPrivatePool) {
            JSONObject publicJson = JSON.parseObject(
                    qpxRateLimiterConfigJson.get(RateLimitTypeEnum.TOKEN_USER.name() + "_" + AppConstants.DEFAULT).toString());
            windowTimeMills = publicJson.getLongValue("windowTimeMills") / 1000;
            windowTotalQuota = publicJson.getLongValue("windowTotalQuota");
            limiterDesc.add("公有池：" + String.valueOf(windowTotalQuota).concat("次/").concat(String.valueOf(windowTimeMills)).concat("秒"));
        }
        else if (!drmConfig.getQpxRateLimiterAllowUseDefaultTokenUserList().contains(user)) { //有私有池 不允许用公共池
            JSONObject privateJson = JSON.parseObject(qpxRateLimiterConfigJson.get(limiterKey).toString());
            windowTimeMills = privateJson.getLongValue("windowTimeMills") / 1000;
            windowTotalQuota = privateJson.getLongValue("windowTotalQuota");
            limiterDesc.add("私有池：" + String.valueOf(windowTotalQuota).concat("次/").concat(String.valueOf(windowTimeMills)).concat("秒"));
        }
        else {
            JSONObject publicJson = JSON.parseObject(
                    qpxRateLimiterConfigJson.get(RateLimitTypeEnum.TOKEN_USER.name() + "_" + AppConstants.DEFAULT).toString());
            windowTimeMills = publicJson.getLongValue("windowTimeMills") / 1000;
            windowTotalQuota = publicJson.getLongValue("windowTotalQuota");
            limiterDesc.add("公有池：" + String.valueOf(windowTotalQuota).concat("次/").concat(String.valueOf(windowTimeMills)).concat("秒"));
            JSONObject privateJson = JSON.parseObject(qpxRateLimiterConfigJson.get(limiterKey).toString());
            windowTimeMills = privateJson.getLongValue("windowTimeMills") / 1000;
            windowTotalQuota = privateJson.getLongValue("windowTotalQuota");
            limiterDesc.add("私有池：" + String.valueOf(windowTotalQuota).concat("次/").concat(String.valueOf(windowTimeMills)).concat("秒"));
        }
        return JSON.toJSONString(limiterDesc);
    }

    /**
     * 查看token新信息
     *
     * @param user
     * @param queryToken
     * @return
     */
    @Override
    public BaseResponse<TokenResponse> getToken(String user, Long id, boolean queryToken) {
        TokenDOExample userAuthDOExample = new TokenDOExample();
        if (StringUtils.isEmpty(user) && id == null){
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if (id == null){
            userAuthDOExample.createCriteria().andUserEqualTo(user);
        }else {
            userAuthDOExample.createCriteria().andIdEqualTo(id);
        }
        List<TokenDO> tokenResponses = tokenDOMapper.selectByExample(userAuthDOExample);
        if (!CollectionUtils.isEmpty(tokenResponses)) {
            TokenDO tokenDO = tokenResponses.get(0);
            if (queryToken) {
                //记录是谁查看了token
                CHAT_LOGGER.info("user query token:empId={}", userAclService.getCurrentUser().getEmpId());
            }
            else {
                //需要对token做加密处理
                tokenDO.setToken(encryptionToken(tokenDO.getToken()));
            }
            TokenResponse tokenResponse = new TokenResponse();
            BeanUtils.copyProperties(tokenDO, tokenResponse);
            if (StringUtils.isNotEmpty(tokenDO.getOwnerUserId())){
                UserAuthDO userAuthDO = userAuthDOMapper.selectByPrimaryKey(Long.valueOf(tokenDO.getOwnerUserId()));
                updateTokenOwnerUser(tokenResponse, userAuthDO);
            }
            return BaseResponse.build(tokenResponse);
        }
        return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, "token is null");
    }

    @Override
    public TokenDO getTokenSystem(String user) {
        TokenDOExample userAuthDOExample = new TokenDOExample();
        userAuthDOExample.createCriteria().andUserEqualTo(user).andEnableStatusEqualTo((byte) 1);
        List<TokenDO> tokenResponses = tokenDOMapper.selectByExample(userAuthDOExample);
        if (!CollectionUtils.isEmpty(tokenResponses)) {
            return tokenResponses.get(0);
        }
        return null;
    }

    @Override
    public BaseResponse insert(TokenRequestBean tokenDO) {
        if (tokenDO == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, new IllegalArgumentException("tokenDO is null"));
        }
        if (StringUtils.isAnyEmpty(tokenDO.getUser(), tokenDO.getUriPatternList(), tokenDO.getEmpId())) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,
                    new IllegalArgumentException("user is null or url pattern list is null or empId is null!"));
        }
        //判断token负责人是否在存在用户表 不存在则需要添加
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdEqualTo(tokenDO.getEmpId());
        List<UserAuthDO> userAuthDOS = userAuthDOMapper.selectByExample(userAuthDOExample);
        String ownerUserId = getOwnerUserId(userAuthDOS, tokenDO.getEmpId());
        tokenDO.setOwnerUserId(ownerUserId);
        if (tokenDO.getBalance() == null) {
            tokenDO.setBalance(new BigDecimal(0));
        }else if (tokenDO.getBalance().compareTo(new BigDecimal("10000")) >= 0){
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, new IllegalArgumentException("The balance cannot be > 10000!"));
        }
        tokenDO.setToken(ShortUid.getUid());
        tokenDO.setGmtCreate(new Date());
        tokenDO.setGmtModified(new Date());
        int insert = tokenDOMapper.insert(tokenDO);
        if (insert == 1){
            CHAT_LOGGER.info("user add token:empId={}，tokenDO={}", userAclService.getCurrentUser().getEmpId(),JSON.toJSONString(tokenDO));
            return BaseResponse.build(tokenDO);
        }
        return BaseResponse.build(ResponseEnum.ERROR_THROW);
    }

    @Override
    public BaseResponse update(TokenRequestBean tokenDO) {
        if (tokenDO == null) {
            throw new IllegalArgumentException("tokenDO is null");
        }
        if (tokenDO.getId()  == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if (StringUtils.isAnyEmpty(tokenDO.getUser(), tokenDO.getUriPatternList())) {
            throw new IllegalArgumentException("user is null or url pattern list is null!");
        }
        //update接口不提供对金额的调整
        if (tokenDO.getBalance() != null){
            throw new IllegalArgumentException("not update Balance!");
        }
        //判断负责人是否发生改变
        TokenDOExample tokenDOExample = new TokenDOExample();
        tokenDOExample.createCriteria().andIdEqualTo(tokenDO.getId());
        if (StringUtils.isNotEmpty(tokenDO.getEmpId())) {
            UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
            userAuthDOExample.createCriteria().andEmpIdEqualTo(tokenDO.getEmpId());
            List<UserAuthDO> userAuthDOS = userAuthDOMapper.selectByExample(userAuthDOExample);
            tokenDO.setOwnerUserId(getOwnerUserId(userAuthDOS, tokenDO.getEmpId()));
        }
        if(tokenDO.getToken() != null){
            throw new IllegalArgumentException("token must is null!");
        }
        if (tokenDO.getNeedEditToken()){
            CHAT_LOGGER.info("user update token:empId={}，tokenDO={}", userAclService.getCurrentUser().getEmpId(),JSON.toJSONString(tokenDO));
            tokenDO.setToken(ShortUid.getUid());
        }
        tokenDO.setGmtModified(new Date());
        tokenDOMapper.updateByExampleSelective(tokenDO, tokenDOExample);
        return BaseResponse.build(tokenDO);
    }

    /**
     * 获取用户花名or姓名
     *
     * @param empId
     * @return
     */
    private String empInfo(String empId) {
        JSONArray users = devInsightService.queryUser(empId);
        if (users == null || users.size() == 0) {
            return null;
        }
        JSONObject json = JSON.parseObject(users.get(0).toString(), JSONObject.class);
        return StringUtils.isNotEmpty(json.getString("nickName")) ? json.getString("nickName") : json.getString("name");
    }

    /**
     * token加密处理
     *
     * @param token
     * @return
     */
    private String encryptionToken(String token) {
        String head = token.substring(0, 4);
        String tail = token.substring(token.length() - 4);
        return head + "********" + tail;
    }

    /**
     * 获取负责人id 如果负责人没有注册，则需要注册
     *
     * @param userAuthDOS
     * @param empId 工号
     * @return
     */
    private String getOwnerUserId(List<UserAuthDO> userAuthDOS, String empId) {
        String ownerUserId;
        if (CollectionUtils.isEmpty(userAuthDOS)) {
            String nickName = empInfo(empId);
            if (StringUtils.isEmpty(nickName)) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "nickName is null!");
            }
            UserAuthDO userAuthDO = userAclService.saveNewUser(nickName, empId, 10);
            ownerUserId = String.valueOf(userAuthDO.getId());
        }
        else {
            ownerUserId = String.valueOf(userAuthDOS.get(0).getId());
        }
        return ownerUserId;
    }

}
