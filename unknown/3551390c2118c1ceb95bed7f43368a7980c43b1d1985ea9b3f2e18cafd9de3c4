package com.alipay.codegencore.model.openai;

/**
 * 方法调用对象
 */
public class ChatFunctionCall {

    /**
     * The name of the function being called
     */
    String name;

    /**
     * The arguments of the call produced by the model, represented as a JsonNode for easy manipulation.
     */
    String arguments;

    public ChatFunctionCall() {
    }

    public ChatFunctionCall(String name, String arguments) {
        this.name = name;
        this.arguments = arguments;
    }

    /**
     * 将空字符串转换为null
     */
    public void convertEmptyPropertyStringToNull(){
        if ("".equals(name)) {
            name = null;
        }
        if ("".equals(arguments)) {
            arguments = null;
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getArguments() {
        return arguments;
    }

    public void setArguments(String arguments) {
        this.arguments = arguments;
    }
}