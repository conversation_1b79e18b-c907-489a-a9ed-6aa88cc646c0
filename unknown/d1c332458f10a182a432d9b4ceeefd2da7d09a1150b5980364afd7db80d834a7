/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version CopilotAnswer.java, v 0.1 2023年11月10日 上午10:41 wb-tzg858080
 */
public class CopilotAnswer extends ToString {
    private static final String EMPTY = "";
    /**
     * 消息id
     */
    private String id;

    /**
     * 插件
     */
    private LinksPluginInfo pluginInfo;

    /**
     * 模型推理出的参数，用不着的话忽视就行
     */
    private LinksStageInfo stageInfo;

    /**
     * 步骤列表
     */
    private List<String> stageList;

    /**
     * 运行结果
     */
    private List<LinksStageInfo> stageLogList;

    /**
     * 最后一条运行结果
     */
    private LinksStageInfo lastStateInfo;

    /**
     * form 数据
     */
    private Map<String,Object> form;

    /**
     * 结果数据
     */
    private String result;

    /**
     * 请求数据
     */
    private Map<String,Object> params;

    /**
     * 状态
     */
    private Boolean isForm = false;

    /**
     * 是否取消
     */
    private Boolean isCancel = false ;

    /**
     * 是否重新生成
     */
    private Boolean reGenerate = false;
    /**
     * 仓库问答信息
     */
    private JSONObject repoChatInfo;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LinksPluginInfo getPluginInfo() {
        return pluginInfo;
    }

    public void setPluginInfo(LinksPluginInfo pluginInfo) {
        this.pluginInfo = pluginInfo;
    }

    public LinksStageInfo getStageInfo() {
        return stageInfo;
    }

    public void setStageInfo(LinksStageInfo stageInfo) {
        this.stageInfo = stageInfo;
    }

    public List<String> getStageList() {
        return stageList;
    }

    public void setStageList(List<String> stageList) {
        this.stageList = stageList;
    }

    public List<LinksStageInfo> getStageLogList() {
        return stageLogList;
    }

    public void setStageLogList(List<LinksStageInfo> stageLogList) {
        this.stageLogList = stageLogList;
    }

    public LinksStageInfo getLastStateInfo() {
        return lastStateInfo;
    }

    public void setLastStateInfo(LinksStageInfo lastStateInfo) {
        this.lastStateInfo = lastStateInfo;
    }

    public Map<String, Object> getForm() {
        return form;
    }
    public void setForm(Map<String, Object> form) {
        this.form = form;
    }

    @JSONField(name = "isForm")
    public Boolean getIsForm() {
        return isForm;
    }

    @JSONField(name = "isForm")
    public void setIsForm(Boolean isForm) {
        this.isForm = isForm;
    }

    @JSONField(name = "isCancel")
    public Boolean getIsCancel() {
        return isCancel;
    }
    @JSONField(name = "isCancel")
    public void setIsCancel(Boolean cancel) {
        this.isCancel = cancel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    @JSONField(name = "reGenerate")
    public Boolean getReGenerate() {
        return reGenerate;
    }

    @JSONField(name = "reGenerate")
    public void setReGenerate(Boolean reGenerate) {
        this.reGenerate = reGenerate;
    }

    public JSONObject getRepoChatInfo() {
        return repoChatInfo;
    }

    public void setRepoChatInfo(JSONObject repoChatInfo) {
        this.repoChatInfo = repoChatInfo;
    }
}
