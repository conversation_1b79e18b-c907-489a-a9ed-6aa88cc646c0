package com.alipay.codegencore.service.common;

import com.alipay.antwork.facade.service.ChatMessageWriteFacade;
import com.alipay.infosec.content.service.facade.HoloxContentCheckService;
import com.alipay.rcsmart.common.facade.SyncApprovalFacade;
import com.alipay.sofa.rpc.api.annotation.RpcConsumer;
import com.alipay.zdatafront.common.service.facade.commonquery.CommonQueryService;
import org.springframework.context.annotation.Configuration;

/**
 * TR接口引入的配置
 */
@Configuration
public class RpcClientFactory {

    /**
     * 内容审查的tr
     */
    @RpcConsumer(value = "holoxContentCheckService",timeout = 6000)
    private HoloxContentCheckService holoxContentCheckService;

    /**
     * TechPlay提供的TR,用来回写异步审查结果
     */
    @RpcConsumer(value = "chatMessageWriteFacade")
    private ChatMessageWriteFacade chatMessageWriteFacade;

    /**
     * 安全合规的TR
     */
    @RpcConsumer(value = "syncApprovalFacade",timeout = 500)
    private SyncApprovalFacade syncApprovalFacade;

    /**
     * 对外tr服务接口TR
     */
    @RpcConsumer(value = "commonQueryService")
    private  CommonQueryService commonQueryService;

}
