/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: Answers.java, v 0.1 2022-07-07 11:35 wb-tzg858080 Exp $$
 */
public class SnippetListCard extends ToString {
    /**
     * 标题
     */
    private String title;
    /**
     * 回答列表
     */
    private List<LinkItem> answers;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<LinkItem> getAnswers() {
        return answers;
    }

    public void setAnswers(List<LinkItem> answers) {
        this.answers = answers;
    }
}
