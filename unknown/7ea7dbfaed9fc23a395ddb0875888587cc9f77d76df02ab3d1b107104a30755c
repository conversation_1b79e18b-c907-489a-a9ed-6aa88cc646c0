package com.alipay.codegencore.model.model;

import java.util.List;

/**
 * 蚂蚁+用的消息汇总数据
 */
public class MaYiDevMessageCountModel {

    private int totalQuestionCount;
    private int totalLikeOrObjectionCount;
    private List<EmpMessageCount> empList;

    public int getTotalQuestionCount() {
        return totalQuestionCount;
    }

    public void setTotalQuestionCount(int totalQuestionCount) {
        this.totalQuestionCount = totalQuestionCount;
    }

    public int getTotalLikeOrObjectionCount() {
        return totalLikeOrObjectionCount;
    }

    public void setTotalLikeOrObjectionCount(int totalLikeOrObjectionCount) {
        this.totalLikeOrObjectionCount = totalLikeOrObjectionCount;
    }

    public List<EmpMessageCount> getEmpList() {
        return empList;
    }

    public void setEmpList(List<EmpMessageCount> empList) {
        this.empList = empList;
    }

    /**
     * 根据工号分组后的消息数量
     */
    public static class EmpMessageCount {
        private String empId;
        private int questionCount;
        private int likeOrObjectionCount;

        public String getEmpId() {
            return empId;
        }

        public void setEmpId(String empId) {
            this.empId = empId;
        }

        public int getQuestionCount() {
            return questionCount;
        }

        public void setQuestionCount(int questionCount) {
            this.questionCount = questionCount;
        }

        public int getLikeOrObjectionCount() {
            return likeOrObjectionCount;
        }

        public void setLikeOrObjectionCount(int likeOrObjectionCount) {
            this.likeOrObjectionCount = likeOrObjectionCount;
        }
    }

}
