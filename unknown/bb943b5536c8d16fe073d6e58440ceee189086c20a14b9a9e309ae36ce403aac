<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.AlgoBackendDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.AlgoBackendDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="jump" jdbcType="VARCHAR" property="jump" />
    <result column="visable_user" jdbcType="INTEGER" property="visableUser" />
    <result column="visable_env" jdbcType="INTEGER" property="visableEnv" />
    <result column="max_token" jdbcType="INTEGER" property="maxToken" />
    <result column="max_round" jdbcType="INTEGER" property="maxRound" />
    <result column="impl" jdbcType="VARCHAR" property="impl" />
    <result column="impl_config" jdbcType="VARCHAR" property="implConfig" />
    <result column="need_health_check" jdbcType="TINYINT" property="needHealthCheck" />
    <result column="model_description" jdbcType="VARCHAR" property="modelDescription" />
    <result column="enable_gpt_cache" jdbcType="TINYINT" property="enableGptCache" />
    <result column="owner_user_id" jdbcType="INTEGER" property="ownerUserId" />
    <result column="model_base" jdbcType="TINYINT" property="modelBase" />
    <result column="usage_user_count" jdbcType="INTEGER" property="usageUserCount" />
    <result column="usage_session_count" jdbcType="INTEGER" property="usageSessionCount" />
    <result column="usage_message_count" jdbcType="INTEGER" property="usageMessageCount" />
    <result column="create_user_id" jdbcType="INTEGER" property="createUserId" />
    <result column="model_type" jdbcType="INTEGER" property="modelType" />
    <result column="support_plugin" jdbcType="TINYINT" property="supportPlugin" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    id, gmt_create, gmt_modified, enable, model, jump, visable_user, visable_env, max_token,
    max_round, impl, impl_config, need_health_check, model_description, enable_gpt_cache,
    owner_user_id, model_base, usage_user_count, usage_session_count, usage_message_count,
    create_user_id, model_type, support_plugin
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.AlgoBackendDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_algo_backend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.AlgoBackendDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    delete from cg_algo_backend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.AlgoBackendDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_algo_backend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="jump != null">
        jump,
      </if>
      <if test="visableUser != null">
        visable_user,
      </if>
      <if test="visableEnv != null">
        visable_env,
      </if>
      <if test="maxToken != null">
        max_token,
      </if>
      <if test="maxRound != null">
        max_round,
      </if>
      <if test="impl != null">
        impl,
      </if>
      <if test="implConfig != null">
        impl_config,
      </if>
      <if test="needHealthCheck != null">
        need_health_check,
      </if>
      <if test="modelDescription != null">
        model_description,
      </if>
      <if test="enableGptCache != null">
        enable_gpt_cache,
      </if>
      <if test="ownerUserId != null">
        owner_user_id,
      </if>
      <if test="modelBase != null">
        model_base,
      </if>
      <if test="usageUserCount != null">
        usage_user_count,
      </if>
      <if test="usageSessionCount != null">
        usage_session_count,
      </if>
      <if test="usageMessageCount != null">
        usage_message_count,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="modelType != null">
        model_type,
      </if>
      <if test="supportPlugin != null">
        support_plugin,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="jump != null">
        #{jump,jdbcType=VARCHAR},
      </if>
      <if test="visableUser != null">
        #{visableUser,jdbcType=INTEGER},
      </if>
      <if test="visableEnv != null">
        #{visableEnv,jdbcType=INTEGER},
      </if>
      <if test="maxToken != null">
        #{maxToken,jdbcType=INTEGER},
      </if>
      <if test="maxRound != null">
        #{maxRound,jdbcType=INTEGER},
      </if>
      <if test="impl != null">
        #{impl,jdbcType=VARCHAR},
      </if>
      <if test="implConfig != null">
        #{implConfig,jdbcType=VARCHAR},
      </if>
      <if test="needHealthCheck != null">
        #{needHealthCheck,jdbcType=TINYINT},
      </if>
      <if test="modelDescription != null">
        #{modelDescription,jdbcType=VARCHAR},
      </if>
      <if test="enableGptCache != null">
        #{enableGptCache,jdbcType=TINYINT},
      </if>
      <if test="ownerUserId != null">
        #{ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="modelBase != null">
        #{modelBase,jdbcType=TINYINT},
      </if>
      <if test="usageUserCount != null">
        #{usageUserCount,jdbcType=INTEGER},
      </if>
      <if test="usageSessionCount != null">
        #{usageSessionCount,jdbcType=INTEGER},
      </if>
      <if test="usageMessageCount != null">
        #{usageMessageCount,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="modelType != null">
        #{modelType,jdbcType=INTEGER},
      </if>
      <if test="supportPlugin != null">
        #{supportPlugin,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.AlgoBackendDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    select count(*) from cg_algo_backend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    update cg_algo_backend
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enable != null">
        enable = #{record.enable,jdbcType=TINYINT},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.jump != null">
        jump = #{record.jump,jdbcType=VARCHAR},
      </if>
      <if test="record.visableUser != null">
        visable_user = #{record.visableUser,jdbcType=INTEGER},
      </if>
      <if test="record.visableEnv != null">
        visable_env = #{record.visableEnv,jdbcType=INTEGER},
      </if>
      <if test="record.maxToken != null">
        max_token = #{record.maxToken,jdbcType=INTEGER},
      </if>
      <if test="record.maxRound != null">
        max_round = #{record.maxRound,jdbcType=INTEGER},
      </if>
      <if test="record.impl != null">
        impl = #{record.impl,jdbcType=VARCHAR},
      </if>
      <if test="record.implConfig != null">
        impl_config = #{record.implConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.needHealthCheck != null">
        need_health_check = #{record.needHealthCheck,jdbcType=TINYINT},
      </if>
      <if test="record.modelDescription != null">
        model_description = #{record.modelDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.enableGptCache != null">
        enable_gpt_cache = #{record.enableGptCache,jdbcType=TINYINT},
      </if>
      <if test="record.ownerUserId != null">
        owner_user_id = #{record.ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="record.modelBase != null">
        model_base = #{record.modelBase,jdbcType=TINYINT},
      </if>
      <if test="record.usageUserCount != null">
        usage_user_count = #{record.usageUserCount,jdbcType=INTEGER},
      </if>
      <if test="record.usageSessionCount != null">
        usage_session_count = #{record.usageSessionCount,jdbcType=INTEGER},
      </if>
      <if test="record.usageMessageCount != null">
        usage_message_count = #{record.usageMessageCount,jdbcType=INTEGER},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=INTEGER},
      </if>
      <if test="record.modelType != null">
        model_type = #{record.modelType,jdbcType=INTEGER},
      </if>
      <if test="record.supportPlugin != null">
        support_plugin = #{record.supportPlugin,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    update cg_algo_backend
    set id = #{record.id,jdbcType=BIGINT},
    gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
    gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
    enable = #{record.enable,jdbcType=TINYINT},
    model = #{record.model,jdbcType=VARCHAR},
    jump = #{record.jump,jdbcType=VARCHAR},
    visable_user = #{record.visableUser,jdbcType=INTEGER},
    visable_env = #{record.visableEnv,jdbcType=INTEGER},
    max_token = #{record.maxToken,jdbcType=INTEGER},
    max_round = #{record.maxRound,jdbcType=INTEGER},
    impl = #{record.impl,jdbcType=VARCHAR},
    impl_config = #{record.implConfig,jdbcType=VARCHAR},
    need_health_check = #{record.needHealthCheck,jdbcType=TINYINT},
    model_description = #{record.modelDescription,jdbcType=VARCHAR},
    enable_gpt_cache = #{record.enableGptCache,jdbcType=TINYINT},
    owner_user_id = #{record.ownerUserId,jdbcType=INTEGER},
    model_base = #{record.modelBase,jdbcType=TINYINT},
    usage_user_count = #{record.usageUserCount,jdbcType=INTEGER},
    usage_session_count = #{record.usageSessionCount,jdbcType=INTEGER},
    usage_message_count = #{record.usageMessageCount,jdbcType=INTEGER},
    create_user_id = #{record.createUserId,jdbcType=INTEGER},
    model_type = #{record.modelType,jdbcType=INTEGER},
    support_plugin = #{record.supportPlugin,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.AlgoBackendDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:53:53 CST 2024.
    -->
    update cg_algo_backend
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="jump != null">
        jump = #{jump,jdbcType=VARCHAR},
      </if>
      <if test="visableUser != null">
        visable_user = #{visableUser,jdbcType=INTEGER},
      </if>
      <if test="visableEnv != null">
        visable_env = #{visableEnv,jdbcType=INTEGER},
      </if>
      <if test="maxToken != null">
        max_token = #{maxToken,jdbcType=INTEGER},
      </if>
      <if test="maxRound != null">
        max_round = #{maxRound,jdbcType=INTEGER},
      </if>
      <if test="impl != null">
        impl = #{impl,jdbcType=VARCHAR},
      </if>
      <if test="implConfig != null">
        impl_config = #{implConfig,jdbcType=VARCHAR},
      </if>
      <if test="needHealthCheck != null">
        need_health_check = #{needHealthCheck,jdbcType=TINYINT},
      </if>
      <if test="modelDescription != null">
        model_description = #{modelDescription,jdbcType=VARCHAR},
      </if>
      <if test="enableGptCache != null">
        enable_gpt_cache = #{enableGptCache,jdbcType=TINYINT},
      </if>
      <if test="ownerUserId != null">
        owner_user_id = #{ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="modelBase != null">
        model_base = #{modelBase,jdbcType=TINYINT},
      </if>
      <if test="usageUserCount != null">
        usage_user_count = #{usageUserCount,jdbcType=INTEGER},
      </if>
      <if test="usageSessionCount != null">
        usage_session_count = #{usageSessionCount,jdbcType=INTEGER},
      </if>
      <if test="usageMessageCount != null">
        usage_message_count = #{usageMessageCount,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="modelType != null">
        model_type = #{modelType,jdbcType=INTEGER},
      </if>
      <if test="supportPlugin != null">
        support_plugin = #{supportPlugin,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>