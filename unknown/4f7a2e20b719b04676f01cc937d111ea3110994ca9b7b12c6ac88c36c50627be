package com.alipay.codegencore.web.filter;

import org.apache.commons.io.IOUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;

public class MultiReadHttpServletRequest extends HttpServletRequestWrapper {

        private byte[] body;

        public MultiReadHttpServletRequest(HttpServletRequest request) {
            super(request);
            try {
                InputStream inputStream = request.getInputStream();
                if (inputStream != null) {
                    body = IOUtils.toByteArray(inputStream);
                }
            } catch (IOException e) {
                // 处理异常
            }
        }

        @Override
        public ServletInputStream getInputStream() throws IOException {
            return new CachedServletInputStream();
        }

        @Override
        public BufferedReader getReader() throws IOException {
            return new BufferedReader(new InputStreamReader(getInputStream()));
        }

        public byte[] getBody() {
            return body;
        }

        private class CachedServletInputStream extends ServletInputStream {
            private ByteArrayInputStream inputStream;

            public CachedServletInputStream() {
                inputStream = new ByteArrayInputStream(body);
            }

            @Override
            public int read() throws IOException {
                return inputStream.read();
            }

            @Override
            public boolean isFinished() {
                return inputStream.available() == 0;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
                throw new RuntimeException("Not implemented");
            }
        }
    }