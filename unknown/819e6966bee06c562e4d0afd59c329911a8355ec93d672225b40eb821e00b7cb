package com.alipay.codegencore.dal.mapper;

import java.util.List;

import com.alipay.codegencore.dal.example.ChatMessageDOExample;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface ChatMessageDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    long countByExample(ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    int deleteByExample(ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    @Delete({
            "delete from cg_chat_message",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    @Insert({
            "insert into cg_chat_message (gmt_create, gmt_modified, ",
            "session_uid, user_id, ",
            "role, content, query_index, ",
            "generation_index, vote, ",
            "comment, uid, deleted, ",
            "review_result, hit_cache, ",
            "hit_query, service_abnormal_resp, ",
            "languages, plugin_log, ",
            "runtime_info, version, ",
            "status, clear, error_msg, ",
            "parent)",
            "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
            "#{sessionUid,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, ",
            "#{role,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{queryIndex,jdbcType=BIGINT}, ",
            "#{generationIndex,jdbcType=BIGINT}, #{vote,jdbcType=BIGINT}, ",
            "#{comment,jdbcType=VARCHAR}, #{uid,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, ",
            "#{reviewResult,jdbcType=VARCHAR}, #{hitCache,jdbcType=TINYINT}, ",
            "#{hitQuery,jdbcType=VARCHAR}, #{serviceAbnormalResp,jdbcType=VARCHAR}, ",
            "#{languages,jdbcType=VARCHAR}, #{pluginLog,jdbcType=VARCHAR}, ",
            "#{runtimeInfo,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, ",
            "#{status,jdbcType=VARCHAR}, #{clear,jdbcType=TINYINT}, #{errorMsg,jdbcType=VARCHAR}, ",
            "#{parent,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(ChatMessageDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    int insertSelective(ChatMessageDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    List<ChatMessageDO> selectByExample(ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    @Select({
            "select",
            "id, gmt_create, gmt_modified, session_uid, user_id, role, content, query_index, ",
            "generation_index, vote, comment, uid, deleted, review_result, hit_cache, hit_query, ",
            "service_abnormal_resp, languages, plugin_log, runtime_info, version, status, ",
            "clear, error_msg, parent",
            "from cg_chat_message",
            "where id = #{id,jdbcType=BIGINT}"
    })
    ChatMessageDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    int updateByExampleSelective(@Param("record") ChatMessageDO record, @Param("example") ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    int updateByExample(@Param("record") ChatMessageDO record, @Param("example") ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    int updateByPrimaryKeySelective(ChatMessageDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    @Update({
            "update cg_chat_message",
            "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
            "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
            "session_uid = #{sessionUid,jdbcType=VARCHAR},",
            "user_id = #{userId,jdbcType=BIGINT},",
            "role = #{role,jdbcType=VARCHAR},",
            "content = #{content,jdbcType=VARCHAR},",
            "query_index = #{queryIndex,jdbcType=BIGINT},",
            "generation_index = #{generationIndex,jdbcType=BIGINT},",
            "vote = #{vote,jdbcType=BIGINT},",
            "comment = #{comment,jdbcType=VARCHAR},",
            "uid = #{uid,jdbcType=VARCHAR},",
            "deleted = #{deleted,jdbcType=TINYINT},",
            "review_result = #{reviewResult,jdbcType=VARCHAR},",
            "hit_cache = #{hitCache,jdbcType=TINYINT},",
            "hit_query = #{hitQuery,jdbcType=VARCHAR},",
            "service_abnormal_resp = #{serviceAbnormalResp,jdbcType=VARCHAR},",
            "languages = #{languages,jdbcType=VARCHAR},",
            "plugin_log = #{pluginLog,jdbcType=VARCHAR},",
            "runtime_info = #{runtimeInfo,jdbcType=VARCHAR},",
            "version = #{version,jdbcType=VARCHAR},",
            "status = #{status,jdbcType=VARCHAR},",
            "clear = #{clear,jdbcType=TINYINT},",
            "error_msg = #{errorMsg,jdbcType=VARCHAR},",
            "parent = #{parent,jdbcType=VARCHAR}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(ChatMessageDO record);
}