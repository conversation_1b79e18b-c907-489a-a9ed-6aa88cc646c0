package com.alipay.codegencore.service.codegpt;

import com.alipay.codegencore.model.openai.ChatMessage;

import java.util.List;

/**
 * 意图识别的服务
 */
public interface IntentionRecognitionService {

    /**
     * 判断一个文本是不是代码领域的
     * @param chatMessageList 审核内容
     * @param messageUid 消息唯一ID
     * @return true=代码领域
     */
    boolean isCodeDomain(String messageUid, List<ChatMessage> chatMessageList);

}
