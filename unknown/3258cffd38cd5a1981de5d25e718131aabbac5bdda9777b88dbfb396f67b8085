/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore;

import com.alipay.codegencore.CodegencoreApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class CodegencoreApplication_SSTest extends CodegencoreApplication_SSTest_scaffolding {
// allCoveredLines:[20]

  @Test(timeout = 4000)
  public void test_setCurrentTimeMillis_0()  throws Throwable  {
      //caseID:c66fc595deacd7a35af85811a0cf7122
      //CoveredLines: [20]
      
      CodegencoreApplication codegencoreApplication0 = new CodegencoreApplication();
  }
}
