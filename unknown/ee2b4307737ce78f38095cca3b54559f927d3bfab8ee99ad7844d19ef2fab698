package com.alipay.codegencore.model.openai;

/**
 * @Description AntLLM出参对象
 * @Date 2023/6/7 14:16
 * @Created by Yanghaiwang
 */
public class AntLLMOpenAPIResponse {
    /**
     * 请求是否成功；如果为false，则调用方决定如何处理该错误。
     * 一般情况下，false一定是发生了物理上的error，需要透出给用户一个兜底回复。
     * 特殊的：流！式！协议下，当revoke=true时，表示是新内容被安全/合规判定为不能输出，
     * 此时，应该通知前端撤回前面已经输出的内容，替换为紧跟着后！台！发送的兜底回复。
     */
    private Boolean success;
    /**
     * 是否撤回前面的响应，true表示要撤回
     * 流式请求专用，内容合规/安全校验失败后，执行撤回
     */
    private Boolean revoke;
    /**
     * 当success为false时的错误信息
     */
    private String errorMsg;
    /**
     * 会话id，参考3.2的sessionId；如果请求中传入sessionId
     * 则该值与入参相同；否则后台会生成一个新的sessionId
     */
    private String sessionId;
    /**
     * 对话唯一id，对于入参reqType=chat时，每次请求后台都会生成一个代表本次对话的
     * 唯一id；当用户针对本次对话进行反馈时，请将该chatId在入参中传入，表示反馈的对象
     */
    private String chatId;
    /**
     * 当前消息对应的流式轮次
     * 流式请求专用，同一个请求的流式包从1递增，目的是帮助前端处理流式包的乱序（一般不会发生）
     * 同步请求默认为1
     */
    private int streamTurn;
    /**
     * 大模型响应结果
     */
    private String response;

    /**
     * 是否被安全拦截拒绝回答（仅tr和http需要关注，流式有revoke下发）
     */
    private Boolean rejected = Boolean.FALSE;
    public Boolean getRejected() {
        return rejected;
    }
    public void setRejected(Boolean rejected) {
        this.rejected = rejected;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Boolean getRevoke() {
        return revoke;
    }

    public void setRevoke(Boolean revoke) {
        this.revoke = revoke;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public int getStreamTurn() {
        return streamTurn;
    }

    public void setStreamTurn(int streamTurn) {
        this.streamTurn = streamTurn;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }
}
