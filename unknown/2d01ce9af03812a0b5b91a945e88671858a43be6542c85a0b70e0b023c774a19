<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.FileDataDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.FileDataDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="file_sha" jdbcType="VARCHAR" property="fileSha" />
    <result column="os_arch" jdbcType="TINYINT" property="osArch" />
    <result column="os_type" jdbcType="TINYINT" property="osType" />
    <result column="is_used" jdbcType="TINYINT" property="isUsed" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    id, file_name, version, group_name, file_sha, os_arch, os_type, is_used, download_url
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.FileDataDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_file_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.FileDataDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    delete from cg_file_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.FileDataDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_file_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileName != null">
        file_name,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="fileSha != null">
        file_sha,
      </if>
      <if test="osArch != null">
        os_arch,
      </if>
      <if test="osType != null">
        os_type,
      </if>
      <if test="isUsed != null">
        is_used,
      </if>
      <if test="downloadUrl != null">
        download_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="fileSha != null">
        #{fileSha,jdbcType=VARCHAR},
      </if>
      <if test="osArch != null">
        #{osArch,jdbcType=TINYINT},
      </if>
      <if test="osType != null">
        #{osType,jdbcType=TINYINT},
      </if>
      <if test="isUsed != null">
        #{isUsed,jdbcType=TINYINT},
      </if>
      <if test="downloadUrl != null">
        #{downloadUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.FileDataDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    select count(*) from cg_file_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    update cg_file_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileSha != null">
        file_sha = #{record.fileSha,jdbcType=VARCHAR},
      </if>
      <if test="record.osArch != null">
        os_arch = #{record.osArch,jdbcType=TINYINT},
      </if>
      <if test="record.osType != null">
        os_type = #{record.osType,jdbcType=TINYINT},
      </if>
      <if test="record.isUsed != null">
        is_used = #{record.isUsed,jdbcType=TINYINT},
      </if>
      <if test="record.downloadUrl != null">
        download_url = #{record.downloadUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    update cg_file_data
    set id = #{record.id,jdbcType=BIGINT},
    file_name = #{record.fileName,jdbcType=VARCHAR},
    version = #{record.version,jdbcType=VARCHAR},
    group_name = #{record.groupName,jdbcType=VARCHAR},
    file_sha = #{record.fileSha,jdbcType=VARCHAR},
    os_arch = #{record.osArch,jdbcType=TINYINT},
    os_type = #{record.osType,jdbcType=TINYINT},
    is_used = #{record.isUsed,jdbcType=TINYINT},
    download_url = #{record.downloadUrl,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.FileDataDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 01 10:51:18 CST 2022.
    -->
    update cg_file_data
    <set>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="fileSha != null">
        file_sha = #{fileSha,jdbcType=VARCHAR},
      </if>
      <if test="osArch != null">
        os_arch = #{osArch,jdbcType=TINYINT},
      </if>
      <if test="osType != null">
        os_type = #{osType,jdbcType=TINYINT},
      </if>
      <if test="isUsed != null">
        is_used = #{isUsed,jdbcType=TINYINT},
      </if>
      <if test="downloadUrl != null">
        download_url = #{downloadUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>