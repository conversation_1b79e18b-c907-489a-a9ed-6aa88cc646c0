package com.alipay.codegencore.model.request.answer;

import com.alipay.codegencore.model.enums.answer.SourceCode;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 * @version 1.0
 */
public class IndexBuildRequest {

    private String repoUrl;

    private String branch;

    private String token;

    private String buildScopeType;

    private Boolean reBuild = false;

    private Integer priority = 1;

    private String sourceCode = SourceCode.ANSWER.name();

    public String getRepoUrl() {
        return repoUrl;
    }

    public void setRepoUrl(String repoUrl) {
        this.repoUrl = repoUrl;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getBuildScopeType() {
        return buildScopeType;
    }

    public void setBuildScopeType(String buildScopeType) {
        this.buildScopeType = buildScopeType;
    }

    public Boolean getReBuild() {
        return reBuild;
    }

    public void setReBuild(Boolean reBuild) {
        this.reBuild = reBuild;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
