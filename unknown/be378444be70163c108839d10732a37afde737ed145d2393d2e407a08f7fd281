package com.alipay.codegencore.model.model.codegpt;

/**
 * @BelongsProject :codegpt模型
 * @BelongsPackage : com.alipay.codegencore.model.model.codegpt
 * <AUTHOR>
 * @CreateTime : 2023-04-12
 */
public class CodeGptUserModelItemModel {
    /**
     * 模型名
     */
    private String value;
    /**
     * 前端展示的模型名
     */
    private String label;

    /**
     * 模型类型 1=通用模型，2=用户定制模型
     */
    private Integer modelType;
    /**
     * 当前模型是否支持插件
     */
    private Boolean supportPlugin;
    /**
     * 构造函数
     */
    public CodeGptUserModelItemModel() {
    }

    /**
     * 构造函数
     *
     * @param value
     * @param label
     */
    public CodeGptUserModelItemModel(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public Boolean getSupportPlugin() {
        return supportPlugin;
    }

    public void setSupportPlugin(Boolean supportPlugin) {
        this.supportPlugin = supportPlugin;
    }
    /**
     * 构造函数
     *
     * @param value
     * @param label
     * @param modelType
     * @param supportPlugin
     */
    public CodeGptUserModelItemModel(String value, String label, Integer modelType, Boolean supportPlugin) {
        this.value = value;
        this.label = label;
        this.modelType = modelType;
        this.supportPlugin = supportPlugin;
    }



    public Integer getModelType() {
        return modelType;
    }

    public void setModelType(Integer modelType) {
        this.modelType = modelType;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
