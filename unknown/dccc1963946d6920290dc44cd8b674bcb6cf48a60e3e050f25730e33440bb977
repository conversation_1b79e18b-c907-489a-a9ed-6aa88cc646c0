<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- context:逆向工程主要配置信息
            id:名称
            defaultModelType:生成Model类型
                conditional:类似hierarchical
                flag:单表单Model
                hierarchical:主键生成XxxKey,Blod等单独生成,其他简单属性一个对象
            targetRuntime:设置生成的文件适用mybatis具体版本
                MyBatis3(default)
                MyBatis3Simple:无Example内容
    -->
    <context id="default" defaultModelType="flat" targetRuntime="MyBatis3">
        <!-- 自动识别数据库关键字,默认false -->
        <!--        <property name="autoDelimitKeywords" value="true"/>-->
        <!-- 格式化java代码 -->
        <!--        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>-->
        <!-- 格式化XML代码 -->
        <!--        <property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter"/>-->

        <!-- 生成的Java文件的编码 -->
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!--        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin" />-->
        <!--        &lt;!&ndash; 配置内置对象序列号接口 &ndash;&gt;-->
        <!--        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>-->
        <!--        &lt;!&ndash; 配置内置对象toString方法生成&ndash;&gt;-->
        <!--        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>-->

        <commentGenerator>
            <!-- 是否去除自动生成日期的注释 -->
            <property name="suppressDate" value="false"/>
            <!-- 是否去除所有自动生成的注释 -->
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>
        <!-- jdbc connect setting -->
        <!-- 这里用的测试库的配置，禁止外泄，实际使用时请替换成您的数据库的链接、用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="************************************************************************************************"
                        userId="obvip_mc_dev_1:gfinsight_dev_g0_3081:codegencore"
                        password="U5gB29m1"/>
        <!-- 类型处理器（可选） -->
        <javaTypeResolver>
            <!-- 是否强制Decimal和Numeric类型转换为BigDecimal,默认值false
                精度 > 0 || length > 18 -> java.math.BigDecimal
                精度 = 0 && 10 <= length <= 18 -> java.lang.Long
                精度 = 0 && 5 <= length <= 10 -> java.lang.Integer
                精度 = 0 && length < 5 -> java.lang.Short
             -->
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- Model生成
                targetPackage:生成实体类所在包
                targetProject:生成实体类所在硬盘位置
         -->
        <javaModelGenerator targetPackage="com.alipay.codegencore.dal.mybatis.model"
                            targetProject="src/main/java">
            <!-- 是否允许子包 -->
            <property name="enableSubPackages" value="true"/>
            <!-- 是否使用构造方法入参,默认false -->
            <!--            <property name="constructorBased" value="true"/>-->
            <!-- 是否清理从数据库中查询出的字符串左右两边的空白字符,默认false -->
            <!--            <property name="trimStrings" value="false"/>-->
            <!-- 建立modal对象属性是否不可改变 即生成的modal对象不会有setter方法，只有构造方法 -->
            <!--            <property name="immutable" value="false"/>-->
        </javaModelGenerator>
        <!-- SqlMap生成，如果使用 mapper + xml 需要添加该配置 -->

        <sqlMapGenerator targetPackage="sql"
                         targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- Mappers生成
            type:预定义Mapper生成器
                Mybatis3/Mybatis3Simple:
                    ANNOTATEDMAPPER:基于注解Mapper接口，无XML
                    MIXEDMAPPER：XML与注解混合式形式
                    XMLMAPPER：XML形式
         -->
        <javaClientGenerator type="MIXEDMAPPER" targetPackage="com.alipay.codegencore.dal.mybatis.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <!-- 表映射
                tableName:数据库对应表名,生成全部表则使用%,支持SQL通配符匹配多个表
                domainObjectName:生成Java实体类类名
         -->
        <!-- 单表 -->
<!--        <table tableName="cg_file_data" domainObjectName="domain.FileDataDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <ignoreColumn column="gmt_create"/>-->
<!--            <ignoreColumn column="gmt_modified"/>-->
<!--        </table>-->

<!--        <table tableName="cg_config" domainObjectName="domain.ConfigDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <ignoreColumn column="gmt_create"/>-->
<!--            <ignoreColumn column="gmt_modified"/>-->
<!--        </table>-->
<!--        <table tableName="cg_token" domainObjectName="domain.TokenDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="cg_chat_session" domainObjectName="domain.ChatSessionDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="scene_id" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="scene_test" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="oss_address_list" jdbcType="VARCHAR" javaType="String"/>-->
<!--            <columnOverride column="ext_info" jdbcType="VARCHAR" javaType="String"/>-->
<!--            <columnOverride column="document_uid_list" jdbcType="VARCHAR" javaType="String"/>-->
<!--        </table>-->
<!--        <table tableName="cg_chat_message" domainObjectName="domain.ChatMessageDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="content" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="comment" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="plugin_log" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="runtime_info" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="hit_query" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="clear" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--        </table>-->
<!--        <table tableName="cg_algo_backend" domainObjectName="domain.AlgoBackendDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="impl_config" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="enable_gpt_cache" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="model_base" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="enable" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="need_health_check" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--        </table>-->
<!--        <table tableName="links_gpt_conversation" domainObjectName="domain.GptConversationDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="title" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="ext_info" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="deleted" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--        </table>-->
<!--        <table tableName="links_gpt_message" domainObjectName="domain.GptMessageDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="ext_info" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="content" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="deleted" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--        </table>-->
<!--        <table tableName="links_gpt_message_feedback" domainObjectName="domain.GptMessageFeedbackDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="content" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="agreed" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="deleted" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--        </table>-->
<!--        <table tableName="cg_algo_backend" domainObjectName="domain.AlgoBackendDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="impl_config" jdbcType="VARCHAR"/>-->
<!--            <columnOverride column="enable_gpt_cache" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="model_base" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="enable" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="need_health_check" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            <columnOverride column="support_plugin" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--        </table>-->
<!--        <table tableName="cg_plugin" domainObjectName="domain.PluginDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="workflow_config" jdbcType="VARCHAR" javaType="String"/>-->
<!--            <columnOverride column="user_id" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="usage_count" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="owner_user_id" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="icon_url" jdbcType="VARCHAR" javaType="String"/>-->
<!--            <columnOverride column="use_instructions" jdbcType="VARCHAR" javaType="String"/>-->
<!--        </table>-->
<!--        <table tableName="cg_user_plugin_records" domainObjectName="domain.UserPluginRecordsDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--        </table>-->

        <table tableName="cg_scene" domainObjectName="domain.SceneDO">
            <!-- 用于在插入对象时返回主键 id -->
            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>
            <columnOverride column="system_prompt" jdbcType="VARCHAR" javaType="String"/>
            <columnOverride column="query_template_list_json" jdbcType="VARCHAR" javaType="String"/>
            <columnOverride column="user_id" jdbcType="BIGINT" javaType="Long"/>
            <columnOverride column="usage_count" jdbcType="BIGINT" javaType="Long"/>
            <columnOverride column="icon_url" jdbcType="VARCHAR" javaType="String"/>
            <columnOverride column="owner_user_id" jdbcType="BIGINT" javaType="Long"/>
            <columnOverride column="usage_user_count" jdbcType="BIGINT" javaType="Long"/>
            <columnOverride column="usage_message_count" jdbcType="BIGINT" javaType="Long"/>
            <columnOverride column="recommend_scene" jdbcType="TINYINT" javaType="Boolean"/>
            <columnOverride column="use_instructions" jdbcType="VARCHAR" javaType="String"/>
            <columnOverride column="ext_info" jdbcType="VARCHAR" javaType="String"/>
            <columnOverride column="document_uid_list" jdbcType="VARCHAR" javaType="String"/>
            <columnOverride column="yuque_token_list" jdbcType="VARCHAR" javaType="String"/>
            <columnOverride column="plugin_command" jdbcType="VARCHAR" javaType="String"/>
            <columnOverride column="plugin_enable" jdbcType="TINYINT" javaType="Boolean"/>
            <ignoreColumn column="detailed_description"/>
            <ignoreColumn column="instructions"/>
        </table>

<!--            <table tableName="cg_user_auth" domainObjectName="domain.UserAuthDO">-->
<!--                &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--                <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--                <columnOverride column="status" jdbcType="TINYINT" javaType="UserStatusEnum"/>-->
<!--                <columnOverride column="top_session_uids" jdbcType="VARCHAR" javaType="String"/>-->
<!--            </table>-->
<!--            <table tableName="cg_user_scene_records" domainObjectName="domain.UserSceneRecordsDO">-->
<!--                &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--                <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            </table>-->

<!--                                <table tableName="cg_algo_backend" domainObjectName="domain.AlgoBackendDO">-->
<!--                                    &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--                                    <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--                                </table>-->

<!--        <table tableName="cg_user_feedback" domainObjectName="UserFeedbackDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="content" jdbcType="VARCHAR" javaType="String"/>-->
<!--            <columnOverride column="pictures" jdbcType="VARCHAR" javaType="String"/>-->
<!--            <columnOverride column="Acceptance_user" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="user_id" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="scene_id" jdbcType="BIGINT" javaType="Long"/>-->
<!--        </table>-->

<!--            <table tableName="cg_rate_limit" domainObjectName="RateLimitDO">-->
<!--                &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--                <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--                <columnOverride column="priority_config" jdbcType="VARCHAR" javaType="String"/>-->
<!--                <columnOverride column="remark" jdbcType="VARCHAR" javaType="String"/>-->
<!--                <columnOverride column="create_user_id" jdbcType="BIGINT" javaType="Long"/>-->
<!--                <columnOverride column="need_limit" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--                <columnOverride column="stop" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--                <columnOverride column="enable" jdbcType="TINYINT" javaType="Boolean"/>-->
<!--            </table>-->
<!--        <table tableName="cg_document" domainObjectName="domain.DocumentDO">-->
<!--            &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--            <columnOverride column="document_size" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="content_length" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="summary" jdbcType="VARCHAR" javaType="String"/>-->
<!--            <columnOverride column="ext_info" jdbcType="VARCHAR" javaType="String"/>-->
<!--            <columnOverride column="create_user_id" jdbcType="BIGINT" javaType="Long"/>-->
<!--            <columnOverride column="update_user_id" jdbcType="BIGINT" javaType="Long"/>-->
<!--        </table>-->
<!--            <table tableName="cg_prompt_template" domainObjectName="domain.PromptTemplateDO">-->
<!--                &lt;!&ndash; 用于在插入对象时返回主键 id &ndash;&gt;-->
<!--                <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true"/>-->
<!--                <columnOverride column="template_text" jdbcType="VARCHAR" javaType="String"/>-->
<!--                <columnOverride column="old_template_text" jdbcType="VARCHAR" javaType="String"/>-->
<!--            </table>-->
    </context>
</generatorConfiguration>