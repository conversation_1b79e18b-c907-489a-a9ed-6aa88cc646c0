package com.alipay.codegencore.dal.typehandler;

import com.alipay.codegencore.model.enums.UserStatusEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 枚举类型转换器
 * 枚举 {@link com.alipay.codegencore.model.enums.UserStatusEnum} 转为 int值
 *
 * <AUTHOR>
 * 创建时间 2021-11-18
 */
@MappedJdbcTypes(JdbcType.TINYINT)
@MappedTypes(value = {UserStatusEnum.class})
public class UserStatusTypeHandler implements TypeHandler<UserStatusEnum> {
    /**
     * 将枚举转为 int值
     *
     * @param preparedStatement mybatis框架参数 {@link PreparedStatement}
     * @param i                 index 参数索引
     * @param userStatusEnum    枚举值 {@link UserStatusEnum}
     * @param jdbcType          mytais框架参数 {@link JdbcType}
     * @throws SQLException 抛出sql异常（mybatis框架自身的异常）
     */
    @Override
    public void setParameter(PreparedStatement preparedStatement, int i, UserStatusEnum userStatusEnum, JdbcType jdbcType) throws SQLException {
        preparedStatement.setInt(i, userStatusEnum.getValue());
    }

    /**
     * 根据枚举值获取枚举
     *
     * @param resultSet mybatis框架参数 {@link ResultSet}
     * @param name      字段名
     * @return 返回实际枚举值 {@link UserStatusEnum}
     * @throws SQLException 抛出sql异常（mybatis框架自身的异常）
     */
    @Override
    public UserStatusEnum getResult(ResultSet resultSet, String name) throws SQLException {
        return UserStatusEnum.getUserStatusEnumByValue(resultSet.getInt(name));
    }

    /**
     * 根据枚举值获取枚举
     *
     * @param resultSet mybatis框架参数 {@link ResultSet}
     * @param index     字段索引
     * @return 返回实际枚举值 {@link UserStatusEnum}
     * @throws SQLException 抛出sql异常（mybatis框架自身的异常）
     */
    @Override
    public UserStatusEnum getResult(ResultSet resultSet, int index) throws SQLException {
        return UserStatusEnum.getUserStatusEnumByValue(resultSet.getInt(index));
    }

    /**
     * 根据枚举值获取枚举
     *
     * @param callableStatement mybatis框架参数 {@link CallableStatement}
     * @param index             字段索引
     * @return 业务枚举值 {@link UserStatusEnum}
     * @throws SQLException 抛出sql异常（mybatis框架自身的异常）
     */
    @Override
    public UserStatusEnum getResult(CallableStatement callableStatement, int index) throws SQLException {
        return UserStatusEnum.getUserStatusEnumByValue(callableStatement.getInt(index));
    }


}
