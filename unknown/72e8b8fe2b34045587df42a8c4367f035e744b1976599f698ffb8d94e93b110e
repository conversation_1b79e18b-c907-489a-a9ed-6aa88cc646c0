package com.alipay.codegencore.service.impl;

import com.alipay.codegencore.dal.example.FileDataDOExample;
import com.alipay.codegencore.dal.mapper.FileDataDOMapper;
import com.alipay.codegencore.model.domain.FileDataDO;
import com.alipay.codegencore.service.FileDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文件数据操作实现类
 *
 * <AUTHOR>
 * 创建时间 2022-06-21
 */
@Service
public class FileDataServiceImpl implements FileDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FileDataServiceImpl.class);

    @Resource
    private FileDataDOMapper fileDataDOMapper;


    /**
     * 根据系统信息查询插件依赖的文件
     * @param osType
     * @param osArch
     * @return
     */
    @Override
    public List<FileDataDO> queryFileByOSInfo(int osType,int osArch) {

        try {
            FileDataDOExample fileDataDOExample = new FileDataDOExample();
            FileDataDOExample.Criteria criteria = fileDataDOExample.createCriteria();
            if (osType != 0) {
                criteria.andOsTypeIn(List.of(osType, 0));
            } else {
                criteria.andOsTypeIn(List.of(osType));
            }
            if (osArch != 0) {
                criteria.andOsArchIn(List.of(osArch, 0));
            } else {
                criteria.andOsArchIn(List.of(osArch));
            }
            criteria.andIsUsedEqualTo(1);
            return fileDataDOMapper.selectByExample(fileDataDOExample);
        } catch (Throwable e) {
            LOGGER.error("获取补全配置文件异常。",  e);
            return null;
        }

    }
}
