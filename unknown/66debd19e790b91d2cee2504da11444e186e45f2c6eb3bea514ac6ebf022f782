package com.alipay.codegencore.service.codegpt;

import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.gptcache.bean.GptCacheResponse;

import java.util.List;

/**
 * 负责处理gptCache的业务逻辑
 *
 * <AUTHOR>
 * 2023年06月05日19:43:21
 */
public interface GPTCacheService {

    /**
     * 是否为首轮
     * 1、regenerate的问题都不走cache
     * 2、只有首轮走cache
     * @param gptAlgModelServiceRequest 服务请求
     * @return 是否为首轮
     */
    boolean isFirstRound(GptAlgModelServiceRequest gptAlgModelServiceRequest);

    /**
     * 开关是否打开
     *
     * 1、Drm 总开关
     * 2、流量控制，随机种子,通过随机数来控制流量，DRM可以配置1-1000内的数量
     * 3、熔断逻辑,当连续请求错误5次熔断，熔断后1min后再试
     * @return 开关是否打开
     */
    boolean isEnableCache();

    /**
     * 从gptCache服务中获取cache
     * @param algoBackendDO 算法模型，为了获取model值
     * @param gptAlgModelServiceRequest 服务请求
     * @param requestId 请求id
     * @return cache服务中获取到的cache
     */
    GptCacheResponse getCache(AlgoBackendDO algoBackendDO, GptAlgModelServiceRequest gptAlgModelServiceRequest, String requestId);

    /**
     *
     * @param model 算法模型
     * @param query  问题
     * @param answer 答案
     * @param requestId 请求
     * @return 缓存到gptCache中是否成功
     */
    boolean putCache(String model, List<ChatMessage> query, String answer, String requestId);

    /**
     * 从gptCache中删除缓存
     * @param model 算法模型
     * @return 删除是否成功
     */
    boolean removeCache(String model);

}
