package com.alipay.codegencore.service.impl;

import cn.hutool.core.lang.mutable.MutablePair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ContentCheckSceneCodeEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.model.InfoSecAsyncUpdateDbModel;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.common.ProcessInfoSecAsyncMsgService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 处理infosec异步回调操作的service
 */
@Service
public class ProcessInfoSecAsyncMsgServiceImpl implements ProcessInfoSecAsyncMsgService {

    private final ExecutorService initOneThreadPool = new ThreadPoolExecutor(1,1,0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1), new ThreadPoolExecutor.AbortPolicy());

    private static final List<InfoSecAsyncUpdateDbModel> infoSecAsyncUpdateDbModelList = new ArrayList<>();

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");

    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private ChatSessionManageService chatSessionManageService;

    /**
     * 初始化线程池
     */
    @PostConstruct
    public void init() {
        initOneThreadPool.execute(()->{
            while (true) {
                JSONObject infoSecAsyncTime = JSON.parseObject(codeGPTDrmConfig.getInfoSecAsyncTime());
                Long loopWaitTime = infoSecAsyncTime.getLong("loopWaitTime");
                Long maxWaitTime = infoSecAsyncTime.getLong("maxWaitTime");
                if (CollectionUtils.isEmpty(infoSecAsyncUpdateDbModelList)) {
                    try {
                        Thread.sleep(loopWaitTime);
                    } catch (InterruptedException e) {
                        OTHERS_LOGGER.error("InterruptedException",e);
                    }
                    continue;
                }
                for (InfoSecAsyncUpdateDbModel infoSecAsyncUpdateDbModel : infoSecAsyncUpdateDbModelList) {
                    // 等待一段之间之后,再去更新数据库,确保数据已经落库
                    if (System.currentTimeMillis() - infoSecAsyncUpdateDbModel.getDateTimeMillis() < maxWaitTime) {
                        continue;
                    }
                    ContentCheckSceneCodeEnum contentCheckSceneCode = infoSecAsyncUpdateDbModel.getContentCheckSceneCode();
                    ReviewResultModel reviewResultModel = infoSecAsyncUpdateDbModel.getReviewResultModel();
                    String messageUid = infoSecAsyncUpdateDbModel.getMessageUid();
                    if (ContentCheckSceneCodeEnum.CTO_CODEFUSE_AITOPIC == contentCheckSceneCode) {
                        // 标题审核不通过
                        if (!reviewResultModel.isRet()) {
                            int cnt = chatSessionManageService.updateSessionTitle(messageUid, AppConstants.SESSION_DEFAULT_TITLE);
                            OTHERS_LOGGER.info("审核回调处理完成,标题审核不通过,cnt:{},infoSecAsyncUpdateDbModel:{}", cnt, JSON.toJSONString(infoSecAsyncUpdateDbModel));
                        }
                    } else if (ContentCheckSceneCodeEnum.CTO_CODEFUSE_QUESTION==contentCheckSceneCode || ContentCheckSceneCodeEnum.CTO_CODEFUSE_ANSWER==contentCheckSceneCode){
                        // codeFuse的问答数据写入审核结果到db,可能db还没有这条数(正在流式输出,尚未落库),也可能是api调用没有落库
                        int cnt = chatMessageService.updateContentReviewResult(messageUid, new MutablePair<>(ReviewPlatformEnum.INFOSEC,reviewResultModel));
                        OTHERS_LOGGER.info("审核回调处理完成,cnt:{},infoSecAsyncUpdateDbModel:{}", cnt, JSON.toJSONString(infoSecAsyncUpdateDbModel));
                    }
                }
            }
        });
    }

    @Override
    public void addInfoSecAsyncRequest(InfoSecAsyncUpdateDbModel infoSecAsyncUpdateDbModel) {
        infoSecAsyncUpdateDbModelList.add(infoSecAsyncUpdateDbModel);
    }
}
