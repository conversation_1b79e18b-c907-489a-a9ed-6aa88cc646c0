package com.alipay.codegencore.utils.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.PostBuilder;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpResponse;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.alipay.codegencore.model.contant.WebApiContents.TOKEN_DO;

/**
 * <AUTHOR>
 * @version 1.0 2024/7/25 15:09
 */
public class CodeInsightClient {

    private static final Logger logger = LoggerFactory.getLogger(CodeInsightClient.class);

    /**
     * 配置中存储的初始化信息
     */
    public static final String CONFIG = "CODE_INSIGHT_CONFIG";

    /**
     * 路由
     */
    private static final String ROUTE = "/api/v1";

    /**
     * token
     */
    private static final String HEADER_TOKEN = "Authorization";

    /**
     * task_id
     */
    public static final String BUILD_TASK_EX_ID = "task_id";

    /**
     * 创建任务被限流
     */
    private static final String TASK_LIMIT = "TASK_LIMIT";

    /**
     * 限流错误吗
     */
    private static final String LIMIT_ERROR_CODE = "ERR_CODEINSIGHT_CONCURRENCY_LIMIT";

    /**
     * 增量构建 action 后缀
     */
    private static final String SCAN_ACTION_PART_SUFFIX = "_push";

    /**
     *  host
     */
    private static String host;

    /**
     * token
     */
    private static String token;

    /**
     * callback
     */
    private static String callback;


    /**
     * 创建任务是否遇到限流
     */
    public static Cache<String, Boolean> limitCache = CacheBuilder.newBuilder()
            .expireAfterWrite( 2, TimeUnit.MINUTES)
            .build();

    /**
     * 判断是否被限流
     * @return
     */
    public static boolean limited() {
        Boolean limit = limitCache.getIfPresent(TASK_LIMIT);
        return BooleanUtils.toBoolean(limit);
    }

    /**
     * 初始化
     * @param config
     */
    public static void init(String config) {
        if (StringUtils.isBlank(config)) {
            throw new RuntimeException("code insight not config. please check!!!");
        }
        logger.info("code insight init: {}", config);

        JSONObject configObject = JSON.parseObject(config);

        CodeInsightClient.host = configObject.getString("host");
        CodeInsightClient.token = configObject.getString("token");
        CodeInsightClient.callback = configObject.getString("callback");
        logger.info("code insight client init host:{}", host);
    }


    /**
     * 查询任务状态
     * @param recordId
     * @return
     */
    public static String searchTaskStatus(Long recordId) {
        logger.info("search record:{} status", recordId);
        JSONObject requestBody = new JSONObject();
        requestBody.put("request_type", "get_scan_record");
        requestBody.put("scan_record_id", recordId);

        PostBuilder postBuilder = HttpClient.post(host + ROUTE)
                .header(HEADER_TOKEN, token)
                .content(requestBody.toJSONString());

        try {
            HttpResponse<String> httpResponse = postBuilder.syncExecuteWithFullResponse(3000);
            if (HttpStatus.SC_OK != httpResponse.statusCode()) {
                logger.error("search index build record:{} failed: {}; {}",
                        recordId, httpResponse.statusCode(), httpResponse.body());
                return TaskStatus.running.name();
            }

            JSONObject response = JSON.parseObject(httpResponse.body());
            if (response.getIntValue("code") != 1) {
                logger.error("search index build record:{} failed. {}", recordId, response);
                return TaskStatus.running.name();
            }

            JSONObject data = response.getJSONObject("data");
            if (data == null) {
                logger.error("search index build record:{} data is null. {}", recordId, data);
                return TaskStatus.running.name();
            }

            String status = data.getString("status");
            logger.info("search build record:{} status: {}", recordId, status);
            return status;
        } catch (Exception e) {
            logger.error("search index build record:{} exception.", recordId, e);
        }
        return TaskStatus.running.name();

    }

    /**
     * 获取增量 scanAction
     * @param scanAction
     * @return
     */
    public static String getPartScanAction(String scanAction) {
        return scanAction + SCAN_ACTION_PART_SUFFIX;
    }

    /**
     * 创建一个仓库索引构建任务
     * @param repoURL
     * @param branch
     * @param taskMask
     * @param taskId
     * @param retry
     * @return
     */
    public static long createBuildTask(String repoURL, String branch, String commitId,
                                       int taskMask, Long taskId,
                                       boolean retry, String callBackEnv,
                                       String scanAction) {
        return createBuildTask(repoURL, branch, commitId, taskMask, taskId, retry, callBackEnv, scanAction, null);
    }

    /**
     * 创建一个仓库索引构建任务
     * @param repoURL
     * @param branch
     * @param commitId
     * @param taskMask
     * @param taskId
     * @param retry
     * @return
     */
    public static long createBuildTask(String repoURL, String branch, String commitId,
                                       int taskMask, Long taskId,
                                       boolean retry, String callBackEnv,
                                       String scanAction, Integer taskTimeout) {
        logger.info("try build index for repo:{} branch:{} commitId:{} taskMask:{} taskId:{} retry:{} callBackEnv:{}",
                repoURL, branch, commitId, taskMask, taskId, retry, callBackEnv);
        if (limited()) {
            logger.warn("limited. wait retry");
            return -1L;
        }
        JSONObject requestBody = new JSONObject();
        requestBody.put("request_type", "trigger_ref_build");
        requestBody.put("scan_action", scanAction);
        requestBody.put("antcode_repo_url", repoURL);
        requestBody.put("antcode_repo_branch", branch);
        if (StringUtils.isNotBlank(commitId)) {
            requestBody.put("antcode_repo_commit", commitId);
        }

        JSONObject variables = new JSONObject();

        JSONObject pipelineExtraData = new JSONObject();
        pipelineExtraData.put("task_mask", taskMask);
        pipelineExtraData.put(BUILD_TASK_EX_ID, taskId);
        pipelineExtraData.put("callback_env", callBackEnv);
        TokenDO tokenDO = ContextUtil.get(TOKEN_DO, TokenDO.class);
        if (Objects.nonNull(tokenDO)) {
            pipelineExtraData.put("codegencore_user", tokenDO.getUser());
            pipelineExtraData.put("codegencore_app_name", tokenDO.getAppName());
            pipelineExtraData.put("codegencore_work_no", tokenDO.getOwnerUserId());
        }
        variables.put("pipeline_extra_data", pipelineExtraData);
        requestBody.put("variables", variables);
        if (taskTimeout != null) {
            requestBody.put("task_timeout", taskTimeout);
        }

        JSONObject webhook = new JSONObject();

        JSONArray event = new JSONArray();
        event.add("SCAN_RECORD");
        webhook.put("event", event);

        JSONObject webHookData = new JSONObject();
        webHookData.put(BUILD_TASK_EX_ID, taskId);
        webhook.put("extra_data", webHookData);
        webhook.put("retry", retry);
        webhook.put("url", callback);
        requestBody.put("webhook", webhook);

        PostBuilder postBuilder = HttpClient.post(host + ROUTE)
                .header(HEADER_TOKEN, token)
                .content(requestBody.toJSONString());

        try {
            HttpResponse<String> httpResponse = postBuilder.syncExecuteWithFullResponse(3000);
            if (HttpStatus.SC_OK != httpResponse.statusCode()) {
                logger.error("create repo index build task:{} failed: {}; {}",
                        taskId, httpResponse.statusCode(), httpResponse.body());
                return -1L;
            }

            JSONObject response = JSON.parseObject(httpResponse.body());
            if (!response.getBooleanValue("success")) {
                logger.error("create repo index build task:{} failed. {}", taskId, httpResponse.body());
                if (LIMIT_ERROR_CODE.equalsIgnoreCase(response.getString("errorCode"))) {
                    limitCache.put(TASK_LIMIT, true);
                }
                return -1L;
            }

            long scanRecordId = response.getLongValue("scan_record_id");
            logger.info("repo index build task:{} created. id: {}", taskId, scanRecordId);
            return scanRecordId;
        } catch (Exception e) {
            logger.error("create repo index build task:{} exception.", taskId, e);
        }

        return -1L;
    }

    /**
     * 任务构建类型
     */
    public enum TaskMask {

        index(1),
        wiki(2),
        all(3);

        private int value;

        TaskMask(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 任务状态
     */
    public enum TaskStatus {

        pending, running, success, failed, canceled, timeout;

        /**
         * 直接重试
         * @param status
         * @return
         */
        public static boolean needRetry(String status) {
            return failed.equalsIgnoreCase(status) || canceled.equalsIgnoreCase(status) || timeout.equalsIgnoreCase(status);
        }

        /**
         * 成功
         * @param status
         * @return
         */
        public static boolean isSuccess(String status) {
            return success.equalsIgnoreCase(status);
        }

        /**
         * 对比
         * @param name
         * @return
         */
        public boolean equalsIgnoreCase(String name) {
            return this.name().equalsIgnoreCase(name);
        }

    }


}
