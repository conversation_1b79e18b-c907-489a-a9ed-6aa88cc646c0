package com.alipay.codegencore.model.model.links;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.06.24
 */
public class CopilotWelcomeMessage {
    /**
     * 前缀
     */
    private String prefix;

    /**
     * 例子
     */
    private List<String> items;

    /**
     * 信息
     */
    private List<CopilotButton> buttons ;

    /**
     * 后缀
     */
    private String suffix;


    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public List<String> getItems() {
        return items;
    }

    public void setItems(List<String> items) {
        this.items = items;
    }

    public List<CopilotButton> getButtons() {
        return buttons;
    }

    public void setButtons(List<CopilotButton> buttons) {
        this.buttons = buttons;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }
}
