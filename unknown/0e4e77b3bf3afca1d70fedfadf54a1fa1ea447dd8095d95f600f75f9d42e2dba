package com.alipay.codegencore.utils.file;

import java.nio.ByteBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CodingErrorAction;

/**
 * 字符编码工具类
 */
public class CharsetUtil {

    /**
     * 猜测给定字节数组的字符集编码
     *
     * @param content 待猜测的字节数组
     * @return 返回猜测到的字符集编码，若无法猜测则返回默认值"utf8"
     */
    public static String guessCharset(byte[] content) {
        // 默认字符集编码为"utf8"
        String resultCharset = "utf8";
        // 列出一些可能的字符集编码
        String[] charsets = new String[]{"utf8", "gbk", "utf16", "utf32", "gb2312"};
        // 遍历可能的字符集编码
        for (String charset : charsets) {
            // 若可以通过其中某个字符集编码成功解码，则认为已经猜测到了该字符集编码
            if (testWithCharset(content, charset)) {
                resultCharset = charset;
                break;
            }
        }
        // 返回猜测到的字符集编码
        return resultCharset;
    }

    /**
     * 判断给定字节数组是否可以通过指定字符集编码正确解码
     *
     * @param content     待测试的字节数组
     * @param charsetName 指定的字符集编码
     * @return 如果可以通过指定字符集编码成功解码，则返回true；否则返回false
     */
    private static boolean testWithCharset(byte[] content, String charsetName) {
        // 根据指定的字符集编码创建一个新的字符集编码器
        final Charset UTF8 = Charset.forName(charsetName);
        final CharsetDecoder decoder = UTF8.newDecoder();
        // 设置解码器的各种行为
        decoder.onMalformedInput(CodingErrorAction.REPORT);
        decoder.onUnmappableCharacter(CodingErrorAction.REPORT);
        try {
            // 尝试通过解码器对字节数组进行解码
            decoder.decode(ByteBuffer.wrap(content));
        } catch (final CharacterCodingException e) {
            // 解码过程中出现异常，说明无法通过指定字符集编码成功解码，返回false
            return false;
        }
        // 解码成功，返回true
        return true;
    }

}

