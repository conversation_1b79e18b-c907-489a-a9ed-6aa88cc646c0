package com.alipay.codegencore.model.enums;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.model.enums
 * @CreateTime : 2023-07-12
 */
public enum SceneVisableUserEnum {
    /**
     * 仅当前用户可见
     */
    VISABLE_USER(1),

    /**
     * 所有用户可见
     */
    ALL(2),

    /**
     * 仅管理员可见
     */
    ADMIN(3),

    /**
     * 指定用户可见
     */
    APPOINT(4);

    private int code;

    SceneVisableUserEnum(int code) {
        this.code = code;
    }

    SceneVisableUserEnum getByCode(int code) {
        for(SceneVisableUserEnum e: SceneVisableUserEnum.values()) {
            if(e.code == code) {
                return e;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }
}
