/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package smartunit.com.alipay.codegencore.service.impl;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.model.openai.AntLLMOpenAPIRequest;
import com.alipay.codegencore.model.openai.AntLLMOpenAPIResponse;
import com.alipay.codegencore.service.utils.ShortUid;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.framing.CloseFrame;
import org.java_websocket.handshake.ServerHandshake;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version AntGLMLanguageModelServiceImplTest.java, v 0.1 2023年06月12日 下午1:48 yuhangqin.qyh
 */
public class AntGLMLanguageModelServiceImplTest {

    @Test
    public void testAsync() {
        AntLLMOpenAPIRequest antLLMOpenAPIRequest = new AntLLMOpenAPIRequest();
        antLLMOpenAPIRequest.setAppName("codeFuse");
        antLLMOpenAPIRequest.setReqType("chat");
        antLLMOpenAPIRequest.setQuery("你好");
        antLLMOpenAPIRequest.setAppToken("2c553211511c43f9");
        antLLMOpenAPIRequest.setFeedback("");

        String s = JSONObject.toJSONString(antLLMOpenAPIRequest);
        System.out.println(s);
    }

    @Test
    public void testStream() throws InterruptedException, URISyntaxException {
        // 连接时间
        int connTimeout = 15000;
        // 首包接收超时时间
        int readTimeout = 20000;
        CountDownLatch readLatch = new CountDownLatch(1);
        // 整体请求完成时间
        int closeTimeout = 200000;
        CountDownLatch closeLatch = new CountDownLatch(1);

        WebSocketClient wsClient = new WebSocketClient(
                new URI("ws://6.1.252.155:9987"), new Draft_6455(), null, connTimeout) {
            @Override
            public void onOpen(ServerHandshake handshakedata) {
                // 当连接建立成功后，会回调该方法
                System.out.println("ws on_open: " + JSONObject.toJSONString(handshakedata));

                // 此时，可以将用户的query请求进行send
                AntLLMOpenAPIRequest request = new AntLLMOpenAPIRequest();
                // 鉴权及调用信息
                request.setAppName("codeFuse"); // 必填-应用名
                request.setAppToken("2c553211511c43f9"); // 必填-应用token
                request.setTraceId("999999999"); // 选填-方便关联日志排查
                request.setRpcId("999999999"); // 选填-方便关联日志排查

                // 对话请求
                request.setReqType("chat"); // 必填-chat表示对话请求
                request.setQuery("你好"); // 必填-用户请求
                request.setDoSample(false); // 流式请求-这里填false
                request.setMaxOutputLength(10240); // 最大输出长度-按需设置

                // 会话及用户信息
                String sessionId = ShortUid.getUid();;
                request.setSessionId(sessionId); // 选填-会话id，多轮对话时通过该id表示会话关系；最好由调用方生成并传入，同一个会话传入相同id
                request.setUserId("401009"); // 选填-调用人的工号/id

                // 发送请求
                this.send(JSONObject.toJSONString(request));
            }

            @Override
            public void onMessage(String message) {
                // 接收到服务端发送的消息
                // 流式协议写会多次调用该方法
                // 可以在这里反序列化成对象后处理
                AntLLMOpenAPIResponse antLLMOpenAPIResponse = JSONObject.parseObject(message, AntLLMOpenAPIResponse.class);
                // 该latch目的是为了监控首包的耗时
                readLatch.countDown();

                // TODO: 具体是消息具体的处理逻辑，根据antLLMOpenAPIResponse里的字段进行处理
                System.out.println("ws on_message: " + JSONObject.toJSONString(antLLMOpenAPIResponse));
            }

            @Override
            public void onClose(int code, String reason, boolean remote) {
                // 当连接被关闭后，会回调该方法；当server关闭连接时，remote=true；当client调用close()关闭，remote=false
                // 只有当code=1000，表示是服务端已经处理完成，正常关闭
                // 其他code需要对异常进行处理
                // 该latch目的是为了监控整体请求的耗时
                closeLatch.countDown();

                // TODO: 给前端发送本次调用的结束标记
                System.out.println("ws on_close: code=" + code + ", reason=" + reason + ", remote = " + remote);
            }

            @Override
            public void onError(Exception ex) {
                // 请求过程中发生异常（一般是本地异常），会回调该方法；当error发生后，一般会调用onClose进行关闭连接，close_code不等于1000
                System.out.println("ws on_error: ex=" + ex.getMessage());
            }
        };

        // 建立链接
        boolean connectSuccess = wsClient.connectBlocking(connTimeout, TimeUnit.MILLISECONDS);

        // 超时处理
        if (!connectSuccess) {
            System.out.println("连接超时，处理失败；此时你的onError及onClose方法都会被调用到。");
            return;
        }

        // 首包超时时间
        boolean readSuccess = readLatch.await(readTimeout, TimeUnit.MILLISECONDS);
        if (!readSuccess) {
            // 首包超时主动关闭客户端连接，建议你在上面onClose中处理该异常
            wsClient.close(CloseFrame.TRY_AGAIN_LATER, "服务端没在规定时间内返回首包，客户端主动关闭。");
            System.out.println("首包接收超时，处理失败；此时你的onClose方法都会被调用到。");
            return;
        }

        // 整体超时时间
        boolean closeSuccess = closeLatch.await(closeTimeout, TimeUnit.MILLISECONDS);
        if (!closeSuccess) {
            // 整体超时主动关闭客户端连接，建议你在上面onClose中处理该异常
            wsClient.close(CloseFrame.TRY_AGAIN_LATER, "服务端没在规定时间内返回所有内容，客户端主动关闭。");
            System.out.println("整体接收超时，处理失败；此时你的onClose方法都会被调用到。");
            return;
        }
    }

}
