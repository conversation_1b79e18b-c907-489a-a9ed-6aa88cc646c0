package com.alipay.codegencore.service.tool.learning;

import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.model.codegpt.PluginServiceRequestContext;

import java.util.List;
import java.util.Map;

/**
 * 插件工作流服务
 *
 * <AUTHOR>
 */
public interface PluginWorkflowService {
    /**
     * 前置请求
     * @param queryParams 用户提问
     * @param paramSchemaList 参数schema列表
     * @param preRequestStageConfig 前置请求阶段配置
     * @param params 请求参数
     * @return 前置请求返回结果
     */
    Object preRequest(String pluginType, Integer stageIndex, PluginServiceRequestContext queryParams, List<Map<String, Object>> paramSchemaList, Map<String, Object> preRequestStageConfig, Map<String, Object> params, boolean isContinue, long requestTimeOut);

    /**
     * 大模型调用
     * @param queryParams 用户提问
     * @param llmStageConfig 大模型调用阶段配置
     * @param preResponse 前置请求返回结果
     * @param params 请求参数
     * @return 大模型调用返回结果
     */
    String llm(Integer stageIndex, PluginServiceRequestContext queryParams, Map<String, Object> llmStageConfig, Map<String, Object> params, Map<String, Object> preResponse, boolean llmStageEnd);

    /**
     * 后置请求
     * @param queryParams 用户提问
     * @param postRequestStageConfig 后置请求阶段配置
     * @param preResponse 前置请求返回结果
     * @param llmResult 大模型调用返回结果
     * @param params 请求参数
     * @return 后置请求返回结果
     */
    Map<String, Object> postRequest(Integer stageIndex, PluginServiceRequestContext queryParams, Map<String, Object> postRequestStageConfig, Map<String, Object> params, Map<String, Object> preResponse, String llmResult, long requestTimeOut);

    /**
     * 模型总结
     * @param queryParams 用户提问
     * @param summaryStageConfig 模型总结阶段配置
     * @param params 请求参数
     * @param preResponse 前置请求返回结果
     * @param llmResult 大模型调用返回结果
     * @param postResponse 后置请求返回结果
     * @return 模型总结返回结果
     */
    String summary( Integer stageIndex, PluginServiceRequestContext queryParams, Map<String, Object> summaryStageConfig, Map<String, Object> params, Map<String, Object> preResponse, String llmResult, Map<String, Object> postResponse);

    /**
     * 对话
     *
     * @param params 请求参数
     * @return 对话返回结果
     */
    String chat(PluginServiceRequestContext params);

    /**
     * 流式对话
     * @param queryParams 用户提问
     * @param pluginDO 数据库中的插件信息
     */
    void streamChat(PluginServiceRequestContext queryParams, PluginDO pluginDO);

    /**
     * 插件工作主流程
     * @param queryParams 请求参数
     * @param workFlowConfig 工作流配置
     */
    String pluginMainWorkflow(PluginServiceRequestContext queryParams,
                            Map<String, Object> workFlowConfig,
                            boolean decideByLLM,
                            boolean isContinue);
}
