package com.alipay.codegencore.model.alg;

import com.alipay.codegencore.model.model.CodeModel;
import com.alipay.codegencore.model.model.CodegenConfigMoel;
import com.alipay.codegencore.model.request.AbstractClientModel;
import com.alipay.codegencore.model.request.CompletionsRequestBean;

import java.util.List;
import java.util.Map;

/**
 * 代码推荐上下文
 *
 * <AUTHOR>
 * 创建时间 2022-01-07
 */
public class CodeRecommandContext extends AbstractClientModel {
    /**
     * 推荐请求
     */
    private CompletionsRequestBean completionsRequestBean;
    /**
     * 预测结果
     */
    private List<CodeModel> codeModelList;
    /**
     * ab实验结果
     */
    private Map<String, String> abValueMap;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 每行代码数据
     */
    private String[] lineArr;
    /**
     * 最后一行代码数据
     */
    private String lastLine;
    /**
     * 代码生成配置信息
     */
    private CodegenConfigMoel codegenConfigMoel;

    public CodegenConfigMoel getCodegenConfigMoel() {
        return codegenConfigMoel;
    }

    public void setCodegenConfigMoel(CodegenConfigMoel codegenConfigMoel) {
        this.codegenConfigMoel = codegenConfigMoel;
    }

    public String[] getLineArr() {
        return lineArr;
    }

    public void setLineArr(String[] lineArr) {
        this.lineArr = lineArr;
    }

    public String getLastLine() {
        return lastLine;
    }

    public void setLastLine(String lastLine) {
        this.lastLine = lastLine;
    }


    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }


    public CompletionsRequestBean getCompletionsRequestBean() {
        return completionsRequestBean;
    }

    public CodeRecommandContext setCompletionsRequestBean(CompletionsRequestBean completionsRequestBean) {
        this.completionsRequestBean = completionsRequestBean;
        return this;
    }

    public List<CodeModel> getCodeModelList() {
        return codeModelList;
    }

    public CodeRecommandContext setCodeModelList(List<CodeModel> codeModelList) {
        this.codeModelList = codeModelList;
        return this;
    }

    public Map<String, String> getAbValueMap() {
        return abValueMap;
    }

    public CodeRecommandContext setAbValueMap(Map<String, String> abValueMap) {
        this.abValueMap = abValueMap;
        return this;
    }

}
