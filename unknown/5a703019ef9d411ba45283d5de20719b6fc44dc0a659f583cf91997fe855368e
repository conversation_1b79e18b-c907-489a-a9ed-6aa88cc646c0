/**
 * Alipay.com Inc. Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.service.middle.zsearch;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import com.alipay.codegencore.model.enums.ZsearchClientEnum;
import com.alipay.codegencore.model.enums.ZsearchIndexEnum;
import com.alipay.zsearch.ZSearchRestClient;
import com.alipay.zsearch.core.query.QueryBuilder;
import com.alipay.zsearch.core.search.SearchSourceBuilder;

/**
 * zsearch服务
 *
 * <AUTHOR>
 * @version : ZsearchService.java, v 0.1 2020年11月23日 9:51 下午 yunchen Exp $
 */
public interface ZsearchCommonService {

    /**
     * 获取zsearch client
     *
     * @return
     */
    ZSearchRestClient getZSearchRestClient(ZsearchClientEnum endpoint);

    /**
     * 分批次存储列表
     *
     * @param dataList             数据列表
     * @param indexInfoEnum        zsearch索引
     * @param idGenerationFunction id方法
     * @param batchSize            每批存储的数量
     * @param <E>                  数据列席
     */
    <E> void saveDataListByBatch(List<E> dataList, ZsearchIndexEnum indexInfoEnum, Function<E,String> idGenerationFunction, int batchSize,
                                 ZsearchClientEnum clientEnum) throws IOException;

    /**
     * 向zsearch存储信息
     *
     * @param list
     * @param indexInfoEnum
     */
    <E> void saveDataList(List<E> list, ZsearchIndexEnum indexInfoEnum, Function<E,String> idGenerationFunction,
                          ZsearchClientEnum clientEnum) throws IOException;

    /**
     * 保存多条数据到zSearch
     *
     * @param map           key为数据id,val为数据对象
     * @param indexInfoEnum 索引
     * @param <E>           数据对象类型
     * @throws IOException 异常
     */
    <E> void saveDataMap(Map<String,E> map, ZsearchIndexEnum indexInfoEnum, ZsearchClientEnum clientEnum) throws IOException;

    /**
     * 保存单条数据到zSearch
     *
     * @param id            保存到zSearch的数据id
     * @param data          数据对象
     * @param indexInfoEnum 索引
     * @param <E>           数据对象类型
     * @throws IOException 异常
     */
    <E> void saveData(String id, E data, ZsearchIndexEnum indexInfoEnum, ZsearchClientEnum clientEnum) throws IOException;

    <E> void saveData(E data, ZsearchIndexEnum indexInfoEnum, Function<E,String> idGenerationFunction, ZsearchClientEnum clientEnum)
            throws IOException;

    /**
     * 从zsearch索引中查询代码数据
     *
     * @param zsearchIndexEnum 索引信息
     * @param partQueryBuilder 搜索条件
     * @param resultClass      结果类型
     * @param pageSize         每页数据量
     * @param <E>
     * @return
     */
    <E> List<E> queryData(ZsearchIndexEnum zsearchIndexEnum, SearchSourceBuilder partQueryBuilder, int pageSize, Class<E> resultClass,
                          ZsearchClientEnum clientEnum);

    /**
     * 从zsearch索引中查询代码数据
     *
     * @param zsearchIndexEnum 索引信息
     * @param partQueryBuilder 搜索条件
     * @param resultClass      结果类型
     * @param limit            每页数据量
     * @param <E>
     * @return
     */
    <E> List<E> queryDataWithLimit(ZsearchIndexEnum zsearchIndexEnum, SearchSourceBuilder partQueryBuilder, int limit, Class<E> resultClass,
                                   ZsearchClientEnum clientEnum);

    /**
     * 从zsearch索引中查询代码数据
     * 有总数 方便分页
     *
     * @param zsearchIndexEnum
     * @param partQueryBuilder
     * @param limit
     * @param resultClass
     * @param <E>
     * @return
     */
    <E> ZSearchResult<E> queryDataWithPage(ZsearchIndexEnum zsearchIndexEnum, SearchSourceBuilder partQueryBuilder, int limit,
                                           Class<E> resultClass, ZsearchClientEnum clientEnum);

    void deleteData(ZsearchIndexEnum zsearchIndexEnum, QueryBuilder queryBuilder, ZsearchClientEnum clientEnum) throws IOException;
}