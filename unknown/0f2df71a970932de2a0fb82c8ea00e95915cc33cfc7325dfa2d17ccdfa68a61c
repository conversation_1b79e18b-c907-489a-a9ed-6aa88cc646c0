package com.alipay.codegencore.web.codegpt;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.contant.WebApiContents;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.AuditStatusSceneEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.enums.UserStatusEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.codegpt.CodeGptUserModelItemModel;
import com.alipay.codegencore.model.openai.*;
import com.alipay.codegencore.model.request.UserLoginNotifyRequest;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.model.response.RegisterUserResponse;
import com.alipay.codegencore.model.response.aci.ACIPipelineTemplate;
import com.alipay.codegencore.model.response.aci.ACIProject;
import com.alipay.codegencore.model.tool.learning.WorkflowConfigCheckResult;
import com.alipay.codegencore.service.codegpt.PluginService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.UserFeedbackService;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.codegpt.user.OnlineUserService;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.AciOpenapiService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.PluginConfigService;
import com.alipay.codegencore.utils.ListUtils;
import com.alipay.codegencore.utils.dingding.DingDingUtil;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.fc.fcbuservice.sdk.common.domain.BuserviceUser;
import com.alipay.fc.fcbuservice.sdk.sso.BuserviceLoginUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.alipay.codegencore.model.contant.WebApiContents.CONTEXT_USER;

/**
 * @description: 用户controller
 * @author: junlong.njl
 * @create: 2021-08-19
 */
@Slf4j
@CodeTalkWebApi
@RestController
@RequestMapping("/webapi/user")
public class UserController {

    @Resource
    private UserAclService userAclService;

    @Resource
    private OnlineUserService onlineUserService;
    @Resource
    private UserAuthDOMapper userAuthDOMapper;
    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;
    @Resource
    private RefreshableCommonTbaseCacheManager defaultCacheManager;


    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private SceneService sceneService;

    @Resource
    private PluginService pluginService;

    @Resource
    PluginConfigService pluginConfigService;

    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;
    @Resource
    private UserFeedbackService userFeedbackService;

    @Resource
    private DocumentHandleService documentHandleService;

    @Resource
    private AciOpenapiService aciOpenapiService;

    /**
     * 获取当前登录的用户
     *
     * @return
     */
    @GetMapping("/queryCurrentLoginUser")
    public BaseResponse<UserAuthDO> queryCurrentLoginUser() {
        return BaseResponse.build(userAclService.getCurrentUser());
    }

    /**
     * 获取当前登录的用户
     *
     * @return
     */
    @PostMapping("/userLoginNotifyOtherProduce")
    public BaseResponse userLoginNotifyOtherProduce(@RequestBody UserLoginNotifyRequest request) {
        String pluginName = "CODEFUSE_PLUGIN";
        if (!pluginName.equals(request.getProductName())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        userAclService.notifyOtherProduce(request.getParams());
        return BaseResponse.buildSuccess();
    }


    /**
     * 登陆，返回当前用户信息
     * 如果是新用户，前端默认走排队流程
     *
     * @return
     */
    @PostMapping("/login")
    public BaseResponse<RegisterUserResponse> login() {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        long count = userAclService.queryUserCountByStatus(UserStatusEnum.VERIFY);
        RegisterUserResponse registerUserResponse = new RegisterUserResponse();
        registerUserResponse.setUserAuthDO(userAuthDO);
        registerUserResponse.setVerifyCount(count);
        registerUserResponse.setApprovalFlag(codeGPTDrmConfig.isDefaultApproval());
        return BaseResponse.build(registerUserResponse);
    }

    /**
     * 注册新用户
     * 如果已有此用户信息，不做处理
     *
     * @return
     */
    @PostMapping("/register")
    public BaseResponse<UserAuthDO> register(@RequestParam(defaultValue = "10") Integer allowAccessType) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        BuserviceUser loginUser = BuserviceLoginUtil.getSimpleUser(request, response, BuserviceUser.class);
        //如果没有buc用户信息，直接返回没权限
        if (loginUser == null) {
            return BaseResponse.build(ResponseEnum.NO_AUTH, "can't get user info");
        }
        UserAuthDO userAuthDO = userAclService.queryUserByEmpId(loginUser.getWorkNo());
        if (userAuthDO != null) {
            //此处也是为了测试以及演示用,如果不是激活用户并且自动审批按钮打开,10s后自动转为激活状态. 后续等产品确定流程后此逻辑会删除
            if (userAuthDO.getStatus() != UserStatusEnum.ACTIVE && codeGPTDrmConfig.isDefaultApproval()) {
                autoApproval(userAuthDO,loginUser.getWorkNo());
            }
            return BaseResponse.buildSuccess();
        }
        userAuthDO = userAclService.saveNewUser(StringUtils.isBlank(loginUser.getNickName()) ? loginUser.getRealName() : loginUser.getNickName(), loginUser.getWorkNo(), allowAccessType);
        //目前排队功能仅为演示使用，等待10s后并且自动审批按钮打开，会自动转为正常用户，防止卡内部用户, 后续等产品确定流程后此逻辑会删除
        autoApproval(userAuthDO,loginUser.getWorkNo());
        return BaseResponse.buildSuccess();
    }

    /**
     * 心跳检测
     */
    @GetMapping("/heartbeat")
    public BaseResponse<String> heartbeat() {
        UserAuthDO curUser = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);
        if (curUser == null) {
            return BaseResponse.build(ResponseEnum.USER_NOT_LOGIN);
        }
        onlineUserService.heartbeat(curUser);
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取在线用户人数
     */
    @GetMapping("/getOnlineUserNum")
    public BaseResponse<Long> getOnlineUserNum() {
        UserAuthDO curUser = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);
        if (curUser == null) {
            return BaseResponse.build(ResponseEnum.USER_NOT_LOGIN);
        }
        return BaseResponse.build(onlineUserService.getOnlineUserNum());
    }

    /**
     * 判断用户是否能够进入（在线用户数量小于阈值）
     */
    @GetMapping("/getUserRateLimiter")
    public BaseResponse<Boolean> getUserRateLimiter() {
        UserAuthDO curUser = ContextUtil.get(WebApiContents.CONTEXT_USER, UserAuthDO.class);
        return BaseResponse.build(onlineUserService.tryAcquireUserRateLimiter(curUser));
    }



    /**
     * 分配管理员权限
     * @param empId 工号
     * @return 是否成功
     */
    @PostMapping("/assignAdmin")
    public BaseResponse<Boolean> assignAdmin(@RequestParam String empId,
                                             @RequestParam(defaultValue = "false") Boolean cancelAdmin,
                                             @RequestParam(defaultValue = "1") byte adminType) {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }

        UserAuthDO userAuthDO = userAclService.queryUserByEmpId(empId);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        userAclService.assignAdmin(userAuthDO.getEmpId(), cancelAdmin, adminType);

        //清除缓存，下次请求会重新加载，用户就有管理员权限了
        defaultCacheManager.del(AppConstants.CACHE_PREFIX + empId);
        return BaseResponse.build(true);
    }


    /**
     * 附身登录接口
     *
     * @param empId     被代理用户工号
     * @param mockEmpId 代理用户工号
     * @return 成功=true
     */
    @PostMapping("/mock")
    public BaseResponse<Boolean> mock(@RequestParam String empId, @RequestParam String mockEmpId) {
        if (!userAclService.isAdmin()) {
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }
        onlineUserService.mockUser(empId, mockEmpId);
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取用户模型信息
     *
     * @return
     */
    @Deprecated
    @PostMapping(path = "/userModelInfo")
    public BaseResponse<List<CodeGptUserModelItemModel>> userModelInfo() {
        return BaseResponse.build(userAclService.getUserModelInfo());
    }

    /**
     * 获取用户可用的模型信息
     *
     * @return
     */
    @GetMapping(path = "/userAvailableModelInfo")
    public BaseResponse<List<CodeGptUserModelItemModel>> getUserAvailableModelInfo() {
        return BaseResponse.build(userAclService.getUserModelInfo());
    }

    /**
     * 修改用户各个审核平台结果是否需要提醒
     * @param status
     * @return
     */
    @PostMapping(path = "/updateUserReviewPlatform")
    public BaseResponse<Object> updateUserReviewPlatform(@RequestParam(value = "platform") ReviewPlatformEnum reviewPlatformEnum,
                                                           @RequestParam(defaultValue = "true") Boolean status) {
        if (!status && !AppConstants.ON.equalsIgnoreCase(codeGPTDrmConfig.getUserCodeFuseCheckSwitch())) {
           return BaseResponse.build(ResponseEnum.NOT_ALLOW_USER_CLOSE_CHECK);
        }
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        defaultCacheManager.hset(AppConstants.USER_NEED_REVIEW+reviewPlatformEnum.name(), userAuthDO.getEmpId(), status);
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取用户各个审核平台是否需要提醒,默认需要提醒
     * @return
     */
    @GetMapping("/getUserReviewPlatform")
    public BaseResponse<Map<ReviewPlatformEnum, Boolean>> getUserReviewPlatform() {
        Map<ReviewPlatformEnum, Boolean> retMap = new HashMap<>();
        retMap.put(ReviewPlatformEnum.INFOSEC, true);
        retMap.put(ReviewPlatformEnum.ANTDSR, true);
        retMap.put(ReviewPlatformEnum.INTENTION, true);
        retMap.put(ReviewPlatformEnum.RCSMART, true);
        // 如果不允许点击不再提醒,那么默认所有人都要提醒
        if (!AppConstants.ON.equalsIgnoreCase(codeGPTDrmConfig.getUserCodeFuseCheckSwitch())) {
            return BaseResponse.build(retMap);
        }
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        Serializable infoSecUserSwitch = defaultCacheManager.hget(AppConstants.USER_NEED_REVIEW + ReviewPlatformEnum.INFOSEC, userAuthDO.getEmpId());
        Serializable antDsrUserSwitch = defaultCacheManager.hget(AppConstants.USER_NEED_REVIEW + ReviewPlatformEnum.ANTDSR, userAuthDO.getEmpId());
        Serializable intentionUserSwitch = defaultCacheManager.hget(AppConstants.USER_NEED_REVIEW + ReviewPlatformEnum.INTENTION, userAuthDO.getEmpId());
        Serializable rcSmartUserSwitch = defaultCacheManager.hget(AppConstants.USER_NEED_REVIEW + ReviewPlatformEnum.RCSMART, userAuthDO.getEmpId());

        if (infoSecUserSwitch != null && !Boolean.parseBoolean(infoSecUserSwitch.toString())) {
            retMap.put(ReviewPlatformEnum.INFOSEC, false);
        }
        if (antDsrUserSwitch != null && !Boolean.parseBoolean(antDsrUserSwitch.toString())) {
            retMap.put(ReviewPlatformEnum.ANTDSR, false);
        }
        if (intentionUserSwitch != null && !Boolean.parseBoolean(intentionUserSwitch.toString())) {
            retMap.put(ReviewPlatformEnum.INTENTION, false);
        }
        if (rcSmartUserSwitch != null && !Boolean.parseBoolean(rcSmartUserSwitch.toString())) {
            retMap.put(ReviewPlatformEnum.RCSMART, false);
        }
        return BaseResponse.build(retMap);
    }

    /**
     * 获取用户是否能自己关闭审核提示功能
     * @return
     */
    @GetMapping("/getAllowUserCloseCheckSwitch")
    public BaseResponse<Boolean> getAllowUserCloseCheckSwitch() {
        return BaseResponse.build(AppConstants.ON.equalsIgnoreCase(codeGPTDrmConfig.getUserCodeFuseCheckSwitch()));
    }


    /**
     * 获取当前用户可编辑助手
     *
     * @param query    查询
     * @param pageNo   页数
     * @param pageSize 每页个数
     * @param sceneTag 助手标签
     * @return
     */
    @GetMapping(path = "/getEditableSceneByUser")
    public PageResponse<List<UserSaveSceneVO>> getEditableSceneByUser(@RequestParam(required = false) String query,
                                                            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                            @RequestParam(value = "sceneTag", required = false) String sceneTag) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        PageResponse<List<UserSaveSceneVO>> userScene = sceneService.getEditableSceneByUser(query, pageNo, pageSize, sceneTag);
        List<UserSaveSceneVO> data = (List<UserSaveSceneVO>) userScene.getData();
        makeSceneVO(data);
        return PageResponse.build(ResponseEnum.SUCCESS, data, userScene.getTotalCount());
    }

    /**
     * 获取我的助手列表
     *
     * <AUTHOR>
     * @since 2024.11.22
     * @param query query
     * @param pageNo pageNo
     * @param pageSize pageSize
     * @return com.alipay.codegencore.model.response.PageResponse<java.util.List<com.alipay.codegencore.model.openai.UserSaveSceneVO>>
     */
    @GetMapping(path = "/getMyScene")
    public PageResponse<List<UserSaveSceneVO>> getMyScene(@RequestParam(required = false) String query,
                                                          @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                           @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        PageResponse<List<UserSaveSceneVO>> userScene = sceneService.getMySceneByUser(query, pageNo, pageSize);
        makeSceneVO((List<UserSaveSceneVO>)userScene.getData());
        return PageResponse.build(ResponseEnum.SUCCESS, (List<UserSaveSceneVO>)userScene.getData(), userScene.getTotalCount());
    }

    /**
     * 获取用户有权限查看的所有已启用助手
     *
     * @param query          查询
     * @param pageNo         页数
     * @param pageSize       每页多少
     * @param save           收藏助手
     * @param recommendScene 推荐助手
     * @param sceneTag       助手标签
     * @return
     */
    @GetMapping(path = "/getAllSceneByEnable")
    public PageResponse<List<UserSaveSceneVO>> getAllSceneByEnable(@RequestParam(required = false) String query,
                                                              @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                              @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                              @RequestParam(defaultValue = "false") boolean save,
                                                              @RequestParam(defaultValue = "false") boolean recommendScene,
                                                              @RequestParam(value = "sceneTag", required = false) String sceneTag) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        List<UserSaveSceneVO> userScene = sceneService.getAllSceneByEnable(query);

        // 根据标签 筛选
        if (StringUtil.isNotEmpty(sceneTag)) {
            userScene = userScene.stream()
                    .filter(userSaveSceneVO -> StringUtil.equals(sceneTag, userSaveSceneVO.getSceneTag()))
                    .collect(Collectors.toList());
        }
        // 收藏助手
        if (save) {
            userScene = userScene.stream().filter(UserSaveSceneVO::isUserSaveScene).collect(Collectors.toList());
        }
        // 推荐助手
        if (recommendScene) {
            userScene = userScene.stream().filter(UserSaveSceneVO::getRecommendScene).collect(Collectors.toList());
        }
        // 隐藏语雀信息
        for (UserSaveSceneVO userSaveSceneVO : userScene) {
            userSaveSceneVO.setYuqueTokenList("[]");
        }
        List<UserSaveSceneVO> result = ListUtils.getPageList(userScene, pageSize, pageNo);
        makeSceneVO(result);
        return PageResponse.build(ResponseEnum.SUCCESS, result, (long) userScene.size());
    }


    /**
     * 用户创建助手
     *
     * @return
     */
    @PostMapping(path = "/userCreateScene")
    public BaseResponse<Long> userCreateScene(@RequestBody SceneVO scene) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        checkSceneFiled(scene);
        userAclService.createScene(scene, userAuthDO);
        return BaseResponse.buildSuccess();
    }

    /**
     * 快速创建助手
     *
     * @return
     */
    @PostMapping(path = "/createScene")
    public BaseResponse<Long> createScene(@RequestBody SceneVO scene) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        checkSceneFiled(scene);
        SceneDO sceneDO = new SceneDO();
        sceneDO.setName(scene.getName());
        sceneDO.setDescription(scene.getDescription());
        UserAuthDO userAuthDO1 = userAclService.queryUserByEmpId(scene.getOwnerUserEmpId());
        sceneDO.setOwnerUserId(userAuthDO1.getId());
        sceneDO.setIconUrl(scene.getIconUrl());
        sceneDO.setVisableUser(1);
        sceneDO.setEnable(0);
        // 创建助手 默认是仅自己可见 默认为审核通过
        sceneDO.setAuditStatus(2);
        sceneDO.setUserId(userAuthDO.getId());
        Long sceneId = sceneService.addScene(sceneDO);
        return BaseResponse.build(sceneId);
    }

    /**
     * 用户修改场景助手
     *
     * @return
     */
    @PostMapping(path = "/updateUserScene")
    public BaseResponse<Boolean> updateUserScene(@RequestBody SceneVO scene) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            return BaseResponse.build(ResponseEnum.USER_NOT_LOGIN);
        }
        if (scene.getId() == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        checkSceneFiled(scene);
        userAclService.updateScene(scene, userAuthDO);
        return BaseResponse.buildSuccess();
    }

    /**
     * 用户修改场景助手的状态或者权限
     *
     * @return
     */
    @PostMapping(path = "/updateSceneAuthStatus")
    public BaseResponse<Boolean> updateSceneAuthStatus(@RequestBody SceneVO scene) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            return BaseResponse.build(ResponseEnum.USER_NOT_LOGIN);
        }
        if (scene.getId() == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        // 增加插件端开关的逻辑
        if (scene.getEnable() == null && scene.getVisableUser() == null && scene.getPluginEnable() == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"开关状态和权限参数不能都为空");
        }
        userAclService.updateSceneAuthStatus(scene, userAuthDO);
        return BaseResponse.buildSuccess();
    }
    /**
     * 用户修改工具的权限
     *
     * @return
     */
    @PostMapping(path = "/updatePluginAuthStatus")
    public BaseResponse<Boolean> updatePluginAuthStatus(@RequestBody PluginVO plugin) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            return BaseResponse.build(ResponseEnum.USER_NOT_LOGIN);
        }
        if (plugin.getId() == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if (plugin.getVisableUser() == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,"权限参数不能都为空");
        }
        pluginService.updatePluginAuth(plugin, userAuthDO);
        return BaseResponse.buildSuccess();
    }

    /**
     * 用户收藏助手
     *
     * @param sceneId 助手id
     * @return
     */
    @PostMapping(path = "/userSaveScene")
    public BaseResponse<Boolean> userSaveScene(@RequestParam Long sceneId, @RequestParam(defaultValue = "false") Boolean cancel) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            return BaseResponse.build(ResponseEnum.USER_NOT_LOGIN);
        }
        SceneDO sceneById = sceneService.getSceneById(sceneId);
        if (sceneById == null){
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        return BaseResponse.build(userAclService.userSaveScene(sceneId, cancel));
    }

    /**
     * 用户置顶会话
     * @param sessionUid
     * @return
     */
    @PostMapping(value = "/topSession")
    public BaseResponse topSession(@RequestParam String sessionUid, @RequestParam(defaultValue = "false") Boolean cancel) {
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", sessionUid));
        }
        userAclService.topSession(sessionUid, cancel);
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取当前用户所有插件
     * @param query 模糊查询query
     * @param pageNo 页数
     * @param pageSize 每页个数
     * @param sortOrder 排序字段
     * @param enable 是否只查询已启用插件
     * @return
     */
    @GetMapping(path = "/getUserPlugin")
    public PageResponse<List<PluginVO>> getUserPlugin(@RequestParam(required = false) String query,
                                                      @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                      @RequestParam(value = "pageSize", defaultValue = "100") int pageSize,
                                                      @RequestParam(value = "sortOrder", defaultValue = "gmtCreate") String sortOrder,
                                                      @RequestParam(value = "sortType", defaultValue = "desc") String sortType,
                                                      @RequestParam(value = "enable", defaultValue = "false") Boolean enable) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        final List<PluginVO> pluginVOList = new ArrayList<>();
        List<PluginDO> userPluginList = pluginService.getUserPlugin(userAuthDO.getId(), query);
        userPluginList.forEach(userPlugin -> {
            PluginVO pluginVO = new PluginVO();
            BeanUtils.copyProperties(userPlugin, pluginVO);
            pluginVO.setAciConfig(JSONObject.parseObject(userPlugin.getAciInfo(), AciInfo.class));
            pluginVOList.add(pluginVO);
        });
        pluginVOList.forEach(pluginVO -> {
            if (pluginVO.getOwnerUserId() != null) {
                UserAuthDO userAuth = userAuthDOMapper.selectByPrimaryKey(pluginVO.getOwnerUserId());
                if(userAuth != null){
                    pluginVO.setOwnerUserEmpId(userAuth.getEmpId());
                    pluginVO.setOwnerUserName(userAuth.getUserName());
                }
            }
            if(StringUtils.isBlank(pluginVO.getType())){
                Map<String, Object> data = pluginConfigService.parseWorkFlowYaml(pluginVO.getWorkflowConfig());
                pluginVO.setType(pluginConfigService.getPluginTypeByInfo(data.get("info")));
            }
        });
        if (enable) {
            List<PluginVO> retList = pluginVOList.stream().filter(pluginDO -> pluginDO.getEnable() == 1).collect(Collectors.toList());
            List<PluginVO> result = ListUtils.getPageList(retList, pageSize, pageNo);
            return PageResponse.build(ResponseEnum.SUCCESS, result, (long) retList.size());
        }
        ListUtil.sortByProperty(pluginVOList, sortOrder);
        if (sortType.equalsIgnoreCase("desc")) {
            CollectionUtil.reverse(pluginVOList);
        }
        List<PluginVO> result = ListUtils.getPageList(pluginVOList, pageSize, pageNo);
        return PageResponse.build(ResponseEnum.SUCCESS, result, (long) pluginVOList.size());
    }

    /**
     * 根据助手id获取助手
     *
     * @param sceneId
     * @return
     */
    @GetMapping(path = "/getSceneById")
    public BaseResponse<UserSaveSceneVO> getSceneById(@RequestParam Long sceneId) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        UserSaveSceneVO userSaveSceneVO = sceneService.getUserAvailableSceneById(sceneId,false);
        makeSceneVO(Lists.newArrayList(userSaveSceneVO));
        return BaseResponse.build(userSaveSceneVO);
    }
    /**
     * 获取助手基础信息
     *
     *
     * <AUTHOR>
     * @since 2024.10.29
     * @param sceneId sceneId
     * @return com.alipay.codegencore.model.response.BaseResponse<com.alipay.codegencore.model.openai.UserSaveSceneVO>
     */
    @GetMapping(path = "/getSceneBaseInfoById")
    public BaseResponse<UserSaveSceneVO> getSceneBaseInfoById(@RequestParam Long sceneId) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        UserSaveSceneVO userSaveSceneVO = sceneService.getUserAvailableSceneById(sceneId,true);
        makeSceneVO(Lists.newArrayList(userSaveSceneVO));
        return BaseResponse.build(userSaveSceneVO);
    }

    /**
     * 用户创建插件
     *
     * @return
     */
    @PostMapping(path = "/userCreatePlugin")
    public BaseResponse<Long> userCreatePlugin(@RequestBody PluginVO pluginVO) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        checkPluginFiled(pluginVO);
        addDefaultAciInfo(pluginVO);
        pluginVO.setAciInfo(JSON.toJSONString(pluginVO.getAciConfig()));
        if(StringUtils.isNotBlank(pluginVO.getType())&& pluginVO.getType().equalsIgnoreCase("aci")){
            pluginVO.setWorkflowConfig(pluginConfigService.addAciInfoToWorkflowYaml(pluginVO.getWorkflowConfig(),pluginVO.getAciInfo()));
        }
        if (StringUtils.isNotBlank(pluginVO.getOwnerUserEmpId())) {
            codeFuseUserAuthService.insertUserAuth(Lists.newArrayList(pluginVO.getOwnerUserEmpId()));
            Long ownerUserId = codeFuseUserAuthService.empId2UserId(pluginVO.getOwnerUserEmpId());
            pluginVO.setOwnerUserId(ownerUserId);
        }
        return BaseResponse.build(pluginService.addPlugin(pluginVO));
    }

    /**
     * 校验插件配置文件
     *
     * @return
     */
    @PostMapping(path = "/checkPluginConfig")
    public WorkflowConfigCheckResult checkPluginConfig(@RequestBody JSONObject yamlText) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        WorkflowConfigCheckResult result = pluginConfigService.checkWorkFlowYaml(yamlText.getString("yamlText"));
        if (result.isSuccess()) {
            result.setErrorCode(ResponseEnum.SUCCESS.getErrorCode());
        } else {
            result.setErrorCode(ResponseEnum.PLUGIN_CHECK_CONFIG.getErrorCode());
        }
        return result;
    }

    /**
     * 用户修改插件
     *
     * @return
     */
    @PostMapping(path = "/updatePlugin")
    public BaseResponse<Boolean> updatePlugin(@RequestBody PluginVO pluginVO) {
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (pluginVO.getId() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        checkPluginFiled(pluginVO);
        addDefaultAciInfo(pluginVO);
        pluginVO.setAciInfo(JSON.toJSONString(pluginVO.getAciConfig()));
        if(StringUtils.isNotBlank(pluginVO.getType())&& pluginVO.getType().equalsIgnoreCase("aci")){
            pluginVO.setWorkflowConfig(pluginConfigService.addAciInfoToWorkflowYaml(pluginVO.getWorkflowConfig(),pluginVO.getAciInfo()));
        }
        if (StringUtils.isNotBlank(pluginVO.getOwnerUserEmpId())) {
            codeFuseUserAuthService.insertUserAuth(Lists.newArrayList(pluginVO.getOwnerUserEmpId()));
            Long ownerUserId = codeFuseUserAuthService.empId2UserId(pluginVO.getOwnerUserEmpId());
            pluginVO.setOwnerUserId(ownerUserId);
        }
        return BaseResponse.build(pluginService.updatePlugin(pluginVO));
    }

    /**
     * 用户发起审核
     *
     * @param sceneId 助手id
     * @return
     */
    @PostMapping(path = "/auditStatusScene")
    public BaseResponse<Boolean> auditStatusScene(@RequestParam Long sceneId) {

        UserAuthDO currentUser = userAclService.getCurrentUser();
        boolean hasAuth = sceneService.editableSceneUser(sceneId, currentUser.getId());
        if (!hasAuth) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        SceneDO sceneDO = new SceneDO();
        sceneDO.setId(sceneId);
        sceneDO.setAuditStatus(AuditStatusSceneEnum.RUNNING.getCode());
        SceneDO scene = sceneService.getSceneById(sceneId);
        if (ObjectUtil.isNull(scene)){
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        // 已经发起过审核 不在发起审核
        if (scene.getAuditStatus() != AuditStatusSceneEnum.NONE.getCode()){
           return BaseResponse.buildSuccess();
        }
        Boolean updateSceneById = sceneService.updateSceneById(sceneDO);

        DingDingUtil.sendMessage(String.format("用户:%s发起场景审核 花名%s 场景id:%s 场景名:%s", currentUser.getEmpId(),
                currentUser.getUserName(), sceneId,scene.getName()));
        return BaseResponse.build(updateSceneById);
    }

    /**
     * 清除所有用户数据
     * @return
     */
    @PostMapping("/clearUserAllData")
    public BaseResponse<Boolean> clearUserAllData() {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if (currentUser == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        userAclService.clearCurrentUserAllData();
        return BaseResponse.buildSuccess();
    }

    /**
     * 新增用户反馈
     *
     * @return
     */
    @PostMapping(path = "/userFeedback")
    public BaseResponse<Boolean> userFeedback(@RequestParam(value = "file",required = false) MultipartFile[] file, @RequestParam String content) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if (currentUser == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (StringUtil.isEmpty(content)){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        // 解决中文乱码问题 前端主动把content转成url编码 在这里再转为utf-8
        content = URLDecoder.decode(content, StandardCharsets.UTF_8);
        if (content.length() > AppConstants.FEEDBACK_MAX_LENGTH) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        return BaseResponse.build(userFeedbackService.addUserFeedback(file, content));
    }

    /**
     * 新增用户反馈
     *
     * @param userFeedBack fileUrl 前端同学文件上传oss后的url
     *                     content 反馈内容
     * @return
     */
    @PostMapping(path = "/feedBackViaUrl")
    public BaseResponse<Boolean> feedBackViaUrl(@RequestBody UserFeedBackVO userFeedBack) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if (currentUser == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (StringUtil.isEmpty(userFeedBack.getContent())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if (userFeedBack.getContent().length() > AppConstants.FEEDBACK_MAX_LENGTH) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        return BaseResponse.build(userFeedbackService.feedBackViaUrl(userFeedBack));
    }


    /**
     * 获取场景或插件头像列表
     *
     * @param type 类型，可选值为"scene"或"plugin"，默认为"scene"
     * @return 返回一个BaseResponse对象，其中包含一个字符串列表，列表中的元素为头像路径
     */
    @GetMapping("/getSceneOrPluginAvatarList")
    public BaseResponse<List<String>> getSceneOrPluginAvatarList(@RequestParam(defaultValue = "scene") String type) {
        JSONObject sceneAndPluginAvatarList = JSONObject.parseObject(codeGPTDrmConfig.getSceneAndPluginAvatarList());
        List<Object> jsonArray;
        if ("scene".equalsIgnoreCase(type)) {
            jsonArray = sceneAndPluginAvatarList.getJSONArray("scene").stream().sorted(Comparator.comparingInt(o -> ((JSONObject) o).getInteger("sort"))).collect(Collectors.toList());
        } else {
            jsonArray = sceneAndPluginAvatarList.getJSONArray("plugin").stream().sorted(Comparator.comparingInt(o -> ((JSONObject) o).getInteger("sort"))).collect(Collectors.toList());
        }
        return BaseResponse.build(jsonArray.stream().map(o -> ((JSONObject) o).getString("ossUrl")).collect(Collectors.toList()));
    }

    /**
     * 获取插件默认配置
     *
     * @param type
     * @return
     */
    @GetMapping("/getDefaultPluginConfig")
    public BaseResponse<String> getDefaultPluginConfig(@RequestParam(value = "type") String type){
        String pluginDefaultConfig = codeGPTDrmConfig.getPluginDefaultConfig();
        JSONObject configs = JSONObject.parseObject(pluginDefaultConfig);
        return BaseResponse.build(configs.getString(type));
    }

    /**
     * 模糊搜索ACI工程（仓库）
     *
     * @param name
     * @return
     */
    @GetMapping("/getACIProjectByName")
    public BaseResponse<List<ACIProject>> getProjectByName(@RequestParam(value = "name", required = false) String name){
        return BaseResponse.build(aciOpenapiService.getAciProjectByName(name));
    }


    /**
     * 获取分支
     *
     * <AUTHOR>
     * @since 2024.05.30
     * @param vcsRepoId vcsRepoId
     * @param platformId platformId
     * @return com.alipay.codegencore.model.response.BaseResponse<java.util.List<java.lang.String>>
     */
    @GetMapping("/getACIBranch")
    public BaseResponse<List<String>> getBranchByProject(@RequestParam(value = "vcsRepoId")String vcsRepoId,@RequestParam(value = "platformId")String platformId){
        return BaseResponse.build(aciOpenapiService.getBranchList(vcsRepoId,platformId));
    }
    /**
     * 通过projectId获取pipeline模版
     *
     * <AUTHOR>
     * @since 2024.05.30
     * @param projectId projectId
     * @return com.alipay.codegencore.model.response.BaseResponse<java.util.List<com.alipay.codegencore.model.response.aci.ACIPipelineTemplate>>
     */
    @GetMapping("/getPipelineTemplateByProjectId")
    public BaseResponse<List<ACIPipelineTemplate>> getPipelineTemplateByProjectId(@RequestParam(value = "projectId") String projectId){
        return BaseResponse.build(aciOpenapiService.getPipelineTemplates(projectId));
    }

    /**
     * 从流水线模版仓库选择模版
     *
     * <AUTHOR>
     * @since 2024.06.03
     * @param keyword keyword
     * @return com.alipay.codegencore.model.response.BaseResponse<java.util.List<com.alipay.codegencore.model.response.aci.ACIPipelineTemplate>>
     */
    @GetMapping("/getRepositoryPipelineTemplate")
    public BaseResponse<List<ACIPipelineTemplate>> getRepositoryPipelineTemplate(@RequestParam(value = "keyword") String keyword){
        return BaseResponse.build(aciOpenapiService.getRepositoryPipelineTemplates(keyword));
    }

    /**
     * 从aci组件库选择组件
     *
     * <AUTHOR>
     * @since 2024.06.14
     * @param query query
     * @return com.alipay.codegencore.model.response.BaseResponse<java.util.List<java.lang.String>>
     */
    @GetMapping("/getRepositoryComponent")
    public BaseResponse<List<String>> getRepositoryComponent(@RequestParam(value = "query", required = false) String query){
        return BaseResponse.build(aciOpenapiService.getRepositoryComponent(query));
    }


    /**
     * 自动审批,当drm自动审批开关开启时,等待3秒后自动把用户状态改为激活(内网临时用)
     * @param userAuthDO
     * @param empId
     */
    private void autoApproval(UserAuthDO userAuthDO,String empId) {
        if(!codeGPTDrmConfig.isDefaultApproval()){
            return;
        }
        final Long userId = userAuthDO.getId();
        appThreadPool.execute(() -> {
            try {
                Thread.sleep(3 * 1000);
                UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
                userAuthDOExample.createCriteria().andIdEqualTo(userId);
                UserAuthDO updateAuth = new UserAuthDO();
                updateAuth.setStatus(UserStatusEnum.ACTIVE);
                userAuthDOMapper.updateByExampleSelective(updateAuth, userAuthDOExample);
                defaultCacheManager.del(AppConstants.CACHE_PREFIX + empId);
            } catch (InterruptedException e) {
                log.error("线程执行异常", e);
            }
        });
    }

    /**
     * 校验助手传参
     *
     * @param scene
     */
    private void checkSceneFiled(SceneVO scene) {
        if (StringUtil.isEmpty(scene.getName()) || StringUtil.isEmpty(scene.getDescription())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if (scene.getName().length() > 40) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"助手名字太长");
        }
        if (scene.getDescription().length() > 1024) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"助手描述太长");
        }
    }

    /**
     * 校验插件传参
     *
     * @param pluginDO
     */
    private void checkPluginFiled(PluginDO pluginDO) {
        if (StringUtil.isEmpty(pluginDO.getWorkflowConfig()) || StringUtil.isEmpty(pluginDO.getName())
                || StringUtil.isEmpty(pluginDO.getDescription())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if (pluginDO.getName().length() > 40) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"插件名字太长");
        }
        if (pluginDO.getDescription().length() > 1024) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"插件描述太长");
        }
        if (!pluginConfigService.checkWorkFlowYaml(pluginDO.getWorkflowConfig()).isSuccess()) {
            throw new BizException(ResponseEnum.PLUGIN_CHECK_CONFIG);
        }
    }

    /**
     * 数据进行用户名的组装
     *
     * @param userSaveSceneVOS
     */
    private void makeSceneVO(List<UserSaveSceneVO> userSaveSceneVOS) {
        if (CollectionUtil.isEmpty(userSaveSceneVOS)){
            return;
        }
        // 对用户名进行组装
        List<Long> createUserIds = userSaveSceneVOS.stream().map(UserSaveSceneVO::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> ownerUserIds = userSaveSceneVOS.stream().map(UserSaveSceneVO::getOwnerUserId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(createUserIds) && CollectionUtil.isEmpty(ownerUserIds)){
            return;
        }
        List<UserAuthDO> createUserList = codeFuseUserAuthService.getUserByIds(createUserIds);
        List<UserAuthDO> ownerUserList = codeFuseUserAuthService.getUserByIds(ownerUserIds);
        Map<Long, UserAuthDO> userAuthDOMap = new HashMap<>();
        userAuthDOMap.putAll(createUserList.stream().collect(Collectors.toMap(UserAuthDO::getId, Function.identity())));
        userAuthDOMap.putAll(ownerUserList.stream().collect(Collectors.toMap(UserAuthDO::getId, Function.identity())));
        for (UserSaveSceneVO userSaveSceneVO : userSaveSceneVOS) {
            if (userAuthDOMap.containsKey(userSaveSceneVO.getUserId())) {
                userSaveSceneVO.setUserName(userAuthDOMap.get(userSaveSceneVO.getUserId()).getUserName());
            }
            if (userAuthDOMap.containsKey(userSaveSceneVO.getOwnerUserId())) {
                userSaveSceneVO.setOwnerUserName(userAuthDOMap.get(userSaveSceneVO.getOwnerUserId()).getUserName());
                userSaveSceneVO.setOwnerUserEmpId(userAuthDOMap.get(userSaveSceneVO.getOwnerUserId()).getEmpId());
            }
        }
    }

    /**
     * 添加aci默认仓库信息
     *
     * @param plugin
     */
    private void addDefaultAciInfo(PluginVO plugin){
        if(StringUtils.isNotBlank(plugin.getType())&&plugin.getType().equalsIgnoreCase("aci")){
            AciInfo aciConfig = plugin.getAciConfig();
            JSONObject defaultInfo = JSONObject.parseObject(codeGPTDrmConfig.getAciDefaultInfo());
            if(aciConfig!=null){
                if(!"customize".equalsIgnoreCase(aciConfig.getType()) && StringUtils.isBlank(aciConfig.getProjectId())){
                    // 设置默认仓库
                    aciConfig.setProjectName("默认仓库");
                    aciConfig.setProjectId(defaultInfo.getString("projectId"));
                    aciConfig.setBranch(defaultInfo.getString("branch"));
                }
            }else {
                throw new BizException(ResponseEnum.PLUGIN_CHECK_CONFIG);
            }
        }
    }
}
