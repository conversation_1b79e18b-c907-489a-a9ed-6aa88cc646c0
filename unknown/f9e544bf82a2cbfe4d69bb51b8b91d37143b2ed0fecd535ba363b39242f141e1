package com.alipay.codegencore.service.utils;

import com.alipay.codegencore.utils.search.SnowFlake;

/**
 * 单例服务管理
 *
 * <AUTHOR>
 * 创建时间 2022-02-15
 */
public class SingletonServerManager {


    /**
     * 获取id生成器
     *
     * @return
     */
    public static SnowFlake getIdGenerator() {
        return SingletonServerManagerHolder.ID_GENERATOR;
    }

    /**
     * 获取模版引擎
     *
     * @return
     */
//    public static VelocityEngine getVelocityEngine() {
//        return SingletonServerManagerHolder.VELOCITY_ENGINE;
//    }

    /**
     * 单例服务管理
     */
    private static class SingletonServerManagerHolder {

        /**
         * id生成器
         */
        public static SnowFlake ID_GENERATOR = createSnowFlake();
        /**
         * 模版引擎
         */
//        public static VelocityEngine VELOCITY_ENGINE = createVelocityEngine();

        /**
         * 创建模版引擎
         *
         * @return
         */
//        private static VelocityEngine createVelocityEngine() {
//            VelocityEngine velocity = new VelocityEngine();
//            try {
//                ExtendedProperties prop = new ExtendedProperties();
//                prop.addProperty(RuntimeConstants.RUNTIME_LOG_LOGSYSTEM_CLASS, JdkLogChute.class.getName());
//                prop.addProperty(JdkLogChute.RUNTIME_LOG_JDK_LOGGER, "GenerateToString");
//                prop.addProperty(RuntimeConstants.VM_PERM_ALLOW_INLINE_REPLACE_GLOBAL, "true");
//                //指定动态函数class集合
//                prop.addProperty(AppConstants.VELOCITY_PROP_KEY_USERDIRECTIVE, "com.alipay.codegencore.service.framework.VelocityToLowerFirstMethod");
//                velocity.setExtendedProperties(prop);
//                velocity.init();
//            } catch (Throwable throwable) {
//                throw new RuntimeException(throwable);
//            }
//            return velocity;
//        }

        /**
         * 创建id生成器
         *
         * @return
         */
        private static SnowFlake createSnowFlake() {
            return new SnowFlake();
        }
    }


}
