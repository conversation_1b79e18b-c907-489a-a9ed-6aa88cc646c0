package com.alipay.codegencore.utils.code;

import com.alibaba.fastjson.JSON;
import com.github.javaparser.ast.expr.Name;
import com.github.javaparser.ast.expr.SimpleName;
import junit.framework.TestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;

import java.io.FileNotFoundException;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public class JavaATSTest extends TestCase {

    static String fileName = "package com.alipay.codegencore.web.openapi;\n" +
            "\n" +
            "import cn.hutool.core.map.MapUtil;\n" +
            "import com.alibaba.fastjson.JSONObject;\n" +
            "import com.alibaba.fastjson.TypeReference;\n" +
            "import com.alibaba.fastjsonfordrm.JSON;\n" +
            "import com.alipay.codegencore.model.enums.ResponseEnum;\n" +
            "import com.alipay.codegencore.model.exception.BizException;\n" +
            "import com.alipay.codegencore.model.openai.*;\n" +
            "import com.alipay.codegencore.model.response.BaseResponse;\n" +
            "import com.alipay.codegencore.service.ideaevo.ActionGenCodeService;\n" +
            "import com.alipay.codegencore.service.middle.tbase.TbaseCacheService;\n" +
            "import com.alipay.codegencore.service.utils.Action2CodeUtil;\n" +
            "import com.alipay.codegencore.service.utils.HttpConnectKeepAlive;\n" +
            "import com.alipay.codegencore.service.utils.ShortUid;\n" +
            "import com.alipay.codegencore.utils.thread.ThreadPoolUtils;\n" +
            "import lombok.extern.slf4j.Slf4j;\n" +
            "import org.apache.commons.lang3.StringUtils;\n" +
            "import org.springframework.beans.factory.annotation.Autowired;\n" +
            "import org.springframework.web.bind.annotation.*;\n" +
            "\n" +
            "import javax.servlet.http.HttpServletResponse;\n" +
            "import java.util.HashMap;\n" +
            "import java.util.List;\n" +
            "import java.util.Map;\n" +
            "import java.util.concurrent.ExecutionException;\n" +
            "import java.util.concurrent.Future;\n" +
            "import java.util.concurrent.TimeUnit;\n" +
            "import java.util.concurrent.TimeoutException;\n" +
            "\n" +
            "/**\n" +
            " * <AUTHOR> +
            " * @version 1.0 2024/6/25 16:38\n" +
            " */\n" +
            "@Slf4j\n" +
            "@RestController\n" +
            "@RequestMapping(\"/v1/generate\")\n" +
            "public class ActionGenCodeController {\n" +
            "\n" +
            "\n" +
            "    @Autowired\n" +
            "    private ActionGenCodeService actionGenCodeService;\n" +
            "\n" +
            "    @Autowired\n" +
            "    private TbaseCacheService tbaseCacheService;\n" +
            "\n" +
            "    /**\n" +
            "     * 生成计划\n" +
            "     * @param modelEnv\n" +
            "     * @param genPlanRequest\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/plan\")\n" +
            "    public BaseResponse genPlan(@RequestHeader(name = \"modelEnv\", defaultValue = \"auto\") String modelEnv,\n" +
            "                                @RequestBody GenPlanRequest genPlanRequest) {\n" +
            "        log.info(\"session:{} plan gen. req:{}\", genPlanRequest.getSessionId(), genPlanRequest);\n" +
            "        return BaseResponse.build(actionGenCodeService.genPlan(genPlanRequest, modelEnv));\n" +
            "    }\n" +
            "\n" +
            "    /**\n" +
            "     * 生成代码\n" +
            "     * @param modelEnv\n" +
            "     * @param genCodeRequest\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/code\")\n" +
            "    public BaseResponse genCode(@RequestHeader(name = \"modelEnv\", defaultValue = \"auto\") String modelEnv,\n" +
            "                                @RequestBody GenCodeRequest genCodeRequest) {\n" +
            "        log.info(\"session:{} plan code. req:{}\", genCodeRequest.getSessionId(), genCodeRequest);\n" +
            "        return BaseResponse.build(actionGenCodeService.genCode(genCodeRequest, modelEnv));\n" +
            "    }\n" +
            "\n" +
            "    /**\n" +
            "     * 生成计划\n" +
            "     * @param modelEnv\n" +
            "     * @param genPlanRequest\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/plan/file\")\n" +
            "    public BaseResponse genPlanFile(HttpServletResponse httpServletResponse,\n" +
            "                                    @RequestHeader(name = \"modelEnv\", defaultValue = \"auto\") String modelEnv,\n" +
            "                                    @RequestHeader(name = \"maxTimeout\", defaultValue = \"0\") Long maxTimeout,\n" +
            "                                    @RequestBody GenPlanRequest genPlanRequest) {\n" +
            "        log.info(\"session:{} plan gen. req:{}\", genPlanRequest.getSessionId(), genPlanRequest);\n" +
            "\n" +
            "        Future<List<PlanFile>> planFileFuture = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,\n" +
            "                () -> actionGenCodeService.genPlanFile(genPlanRequest, modelEnv));\n" +
            "\n" +
            "        return keepAlive(planFileFuture, maxTimeout, httpServletResponse);\n" +
            "    }\n" +
            "\n" +
            "    /**\n" +
            "     * 生成代码\n" +
            "     * @param modelEnv\n" +
            "     * @param genCodeFileRequest\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/code/file\")\n" +
            "    public BaseResponse genCodeFile(HttpServletResponse httpServletResponse,\n" +
            "                                    @RequestHeader(name = \"modelEnv\", defaultValue = \"auto\") String modelEnv,\n" +
            "                                    @RequestHeader(name = \"maxTimeout\", defaultValue = \"0\") Long maxTimeout,\n" +
            "                                    @RequestBody GenCodeFileRequest genCodeFileRequest) {\n" +
            "        log.info(\"session:{} plan code file. req:{}\", genCodeFileRequest.getSessionId(), genCodeFileRequest);\n" +
            "\n" +
            "        Future<CodeInfoFile> codeInfoFileFuture = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,\n" +
            "                () -> actionGenCodeService.genCodeFile(genCodeFileRequest, modelEnv));\n" +
            "\n" +
            "        return keepAlive(codeInfoFileFuture, maxTimeout, httpServletResponse);\n" +
            "    }\n" +
            "\n" +
            "    /**\n" +
            "     * action gen code\n" +
            "     * @param modelEnv\n" +
            "     * @param genPlanRequest\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/evaluation/file\")\n" +
            "    public BaseResponse evaluationFile(HttpServletResponse httpServletResponse,\n" +
            "                                   @RequestHeader(name = \"modelEnv\", defaultValue = \"auto\") String modelEnv,\n" +
            "                                   @RequestHeader(name = \"maxTimeout\", defaultValue = \"0\") Long maxTimeout,\n" +
            "                                   @RequestBody GenPlanRequest genPlanRequest) {\n" +
            "        log.info(\"evaluation file: {}\", genPlanRequest);\n" +
            "\n" +
            "        Future<JSONObject> evaluationInfoFuture = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,\n" +
            "                () -> actionGenCodeService.evaluationFile(genPlanRequest, modelEnv));\n" +
            "\n" +
            "        return keepAlive(evaluationInfoFuture, maxTimeout, httpServletResponse);\n" +
            "\n" +
            "    }\n" +
            "\n" +
            "    /**\n" +
            "     * 相关代码文件查询\n" +
            "     * @param searchRequest\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/search\")\n" +
            "    public BaseResponse search(@RequestBody GenPlanRequest searchRequest) {\n" +
            "        log.info(\"session:{} search code file. req:{}\", searchRequest.getSessionId(), searchRequest);\n" +
            "\n" +
            "        if (searchRequest.getRepoInfo() == null\n" +
            "                || StringUtils.isBlank(searchRequest.getRepoInfo().getRepoPath())) {\n" +
            "            return BaseResponse.build(ResponseEnum.SVAT_REPO_INFO_ILL);\n" +
            "        }\n" +
            "\n" +
            "        return BaseResponse.build(actionGenCodeService.search(searchRequest));\n" +
            "    }\n" +
            "\n" +
            "    /**\n" +
            "     * action gen code\n" +
            "     * @param modelEnv\n" +
            "     * @param genPlanRequest\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/evaluation\")\n" +
            "    public BaseResponse evaluation(HttpServletResponse httpServletResponse,\n" +
            "                                   @RequestHeader(name = \"modelEnv\", defaultValue = \"auto\") String modelEnv,\n" +
            "                                   @RequestHeader(name = \"maxTimeout\", defaultValue = \"0\") Long maxTimeout,\n" +
            "                                   @RequestBody GenPlanRequest genPlanRequest) {\n" +
            "        log.info(\"session:{} action gen code.\", genPlanRequest.getSessionId());\n" +
            "\n" +
            "        Future<JSONObject> evaluationInfoFuture = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,\n" +
            "                () -> actionGenCodeService.evaluation(genPlanRequest, modelEnv));\n" +
            "\n" +
            "        return keepAlive(evaluationInfoFuture, maxTimeout, httpServletResponse);\n" +
            "\n" +
            "    }\n" +
            "\n" +
            "\n" +
            "    /**\n" +
            "     * 生成计划\n" +
            "     * @param modelEnv\n" +
            "     * @param requestInfo\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/codefuse/plan\")\n" +
            "    public Map<String, Object> genPlanForCodefuse(HttpServletResponse httpServletResponse,\n" +
            "                                                  @RequestHeader(name = \"modelEnv\", defaultValue = \"auto\") String modelEnv,\n" +
            "                                                  @RequestBody JSONObject requestInfo) {\n" +
            "        GenPlanRequest genPlanRequest = buildGenPlanRequest(requestInfo);\n" +
            "\n" +
            "        log.info(\"session:{} plan gen. req:{}\", genPlanRequest.getSessionId(), JSON.toJSONString(requestInfo));\n" +
            "\n" +
            "        Future<List<Plan>> planListFuture = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,\n" +
            "                () -> actionGenCodeService.genPlan(genPlanRequest, modelEnv));\n" +
            "\n" +
            "        HttpConnectKeepAlive.keepAlive(planListFuture, httpServletResponse);\n" +
            "\n" +
            "        List<Plan> planList;\n" +
            "        try {\n" +
            "            planList = planListFuture.get();\n" +
            "        } catch (Exception e) {\n" +
            "            log.error(\"plan gen failed\", e);\n" +
            "            throw new BizException(ResponseEnum.SVAT_GEN_PLAN_EXCEPTION);\n" +
            "        }\n" +
            "\n" +
            "        return new HashMap<>() {{\n" +
            "            put(\"sessionId\", genPlanRequest.getSessionId());\n" +
            "            put(\"plans\", planList);\n" +
            "        }};\n" +
            "    }\n" +
            "\n" +
            "\n" +
            "    /**\n" +
            "     * 生成代码\n" +
            "     * @param modelEnv\n" +
            "     * @param requestInfo\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping(\"/codefuse/code\")\n" +
            "    public Map<String, Object> genCodeForCodefuse(@RequestHeader(name = \"modelEnv\", defaultValue = \"auto\") String modelEnv,\n" +
            "                                @RequestBody JSONObject requestInfo) {\n" +
            "        GenCodeRequest genCodeRequest = buildGenCodeRequest(requestInfo);\n" +
            "        log.info(\"session:{} plan code.\", genCodeRequest.getSessionId());\n" +
            "        List<Plan> planList = com.alibaba.fastjson.JSON.parseObject(\n" +
            "                (String) tbaseCacheService.getCache(\"GENCODE:\" + genCodeRequest.getSessionId()),\n" +
            "                new TypeReference<List<Plan>>(){}.getType());\n" +
            "        genCodeRequest.setPlans(planList);\n" +
            "        List<CodeInfo> codeInfoList = actionGenCodeService.genCode(genCodeRequest, modelEnv);\n" +
            "        String answer = Action2CodeUtil.generateReport(planList, codeInfoList, genCodeRequest);\n" +
            "\n" +
            "        return MapUtil.of(\"answer\", answer);\n" +
            "    }\n" +
            "\n" +
            "    private GenPlanRequest buildGenPlanRequest(JSONObject requestInfo) {\n" +
            "        String query = requestInfo.getString(\"query\");\n" +
            "        String sessionId = ShortUid.getUid();\n" +
            "        JSONObject formData = requestInfo.getJSONObject(\"formData\");\n" +
            "        String repoPath = formData.getString(\"repoPath\");\n" +
            "        String branch = formData.getString(\"branch\");\n" +
            "        String appName = formData.getString(\"appName\");\n" +
            "\n" +
            "        GenPlanRequest genPlanRequest = new GenPlanRequest();\n" +
            "        genPlanRequest.setAppName(appName);\n" +
            "        RepoInfo repoInfo = new RepoInfo();\n" +
            "        repoInfo.setRepoPath(repoPath);\n" +
            "        repoInfo.setBranch(branch);\n" +
            "        genPlanRequest.setRepoInfo(repoInfo);\n" +
            "\n" +
            "        GenRequirementInfo genRequirementInfo = new GenRequirementInfo();\n" +
            "        genRequirementInfo.setUserChangeLogic(query);\n" +
            "        genRequirementInfo.setGenerateChangeLogic(query);\n" +
            "        genPlanRequest.setRequirementInfo(genRequirementInfo);\n" +
            "        genPlanRequest.setSessionId(sessionId);\n" +
            "        return genPlanRequest;\n" +
            "    }\n" +
            "\n" +
            "    private GenCodeRequest buildGenCodeRequest(JSONObject requestInfo){\n" +
            "        JSONObject params = requestInfo.getJSONObject(\"params\");\n" +
            "\n" +
            "        GenPlanRequest genPlanRequest = buildGenPlanRequest(params);\n" +
            "        GenCodeRequest genCodeRequest = new GenCodeRequest();\n" +
            "        genCodeRequest.setAppName(genPlanRequest.getAppName());\n" +
            "        genCodeRequest.setRepoInfo(genPlanRequest.getRepoInfo());\n" +
            "        genCodeRequest.setRequirementInfo(genPlanRequest.getRequirementInfo());\n" +
            "\n" +
            "        JSONObject preResponse = requestInfo.getJSONObject(\"preResponse\");\n" +
            "        String sessionId = preResponse.getString(\"sessionId\");\n" +
            "\n" +
            "        genCodeRequest.setSessionId(sessionId);\n" +
            "        return genCodeRequest;\n" +
            "    }\n" +
            "\n" +
            "    /**\n" +
            "     * 统一心跳处理\n" +
            "     * @param future\n" +
            "     * @param httpServletResponse\n" +
            "     * @return\n" +
            "     * @param <T>\n" +
            "     * @throws Throwable\n" +
            "     */\n" +
            "    private <T> BaseResponse<T> keepAlive(Future<T> future, long maxTimeout, HttpServletResponse httpServletResponse) {\n" +
            "\n" +
            "        HttpConnectKeepAlive.keepAlive(future, maxTimeout, httpServletResponse);\n" +
            "\n" +
            "        try {\n" +
            "            T t = future.get(10L, TimeUnit.SECONDS);\n" +
            "            return BaseResponse.build(t);\n" +
            "        } catch (TimeoutException e) {\n" +
            "            log.error(\"gen timeout.\", e);\n" +
            "            return BaseResponse.build(ResponseEnum.SVAT_GEN_TIMEOUT);\n" +
            "        } catch (ExecutionException e) {\n" +
            "            log.error(\"execution exception\", e);\n" +
            "            Throwable cause = e.getCause();\n" +
            "            //最多尝试 5 次获取业务失败原因\n" +
            "            for (int i = 0; i < 5; i++) {\n" +
            "                if (cause instanceof BizException) {\n" +
            "                    BizException bizException = (BizException) cause;\n" +
            "                    return BaseResponse.build(bizException.getErrorType(), bizException.getMessage());\n" +
            "                }\n" +
            "                cause = cause.getCause();\n" +
            "            }\n" +
            "            return BaseResponse.build(ResponseEnum.SVAT_GEN_FAIL);\n" +
            "        } catch (InterruptedException e) {\n" +
            "            log.error(\"interrupted exception\", e);\n" +
            "            return BaseResponse.build(ResponseEnum.SVAT_GEN_FAIL);\n" +
            "        } finally {\n" +
            "            if (!future.isDone()) {\n" +
            "                future.cancel(true);\n" +
            "            }\n" +
            "        }\n" +
            "    }\n" +
            "}\n";


    String chunks = "[\n" +
//            "            {\n" +
//            "                \"startLine\": 0,\n" +
//            "                \"endLine\": 80,\n" +
//            "                \"path\": \"app/core/component-backpressure/src/main/java/com/antfinancial/antchain/trustiot/core/component/backpressure/consumer/ReGeoBatchConsumer.java\",\n" +
//            "                \"content\": \"/*\\n * Ant Group\\n * Copyright (c) 2004-2022 All Rights Reserved.\\n */\\npackage com.antfinancial.antchain.trustiot.core.component.backpressure.consumer;\\n\\n\\n\\nimport java.util.List;\\nimport java.util.Map;\\nimport java.util.stream.Collectors;\\nimport com.antfinancial.antchain.trustiot.common.enums.CoordinateSystemEnum;\\nimport com.antfinancial.antchain.trustiot.common.model.amap.ReGeoCode;\\nimport com.antfinancial.antchain.trustiot.common.model.amap.ReGeoResultBase;\\nimport com.antfinancial.antchain.trustiot.common.service.LocationService;\\nimport com.antfinancial.antchain.trustiot.core.component.backpressure.event.AbstractBackPressureTask.TaskStatus;\\nimport com.antfinancial.antchain.trustiot.core.component.backpressure.event.ReGeoTask;\\nimport com.antfinancial.antchain.trustiot.core.component.backpressure.provider.PackTimeOutProvider;\\nimport lombok.extern.slf4j.Slf4j;\\n\\n/**\\n * <AUTHOR> * @version GpsReGeoBatchHandler.java, v 0.1 2022年04月12日 下午7:03 wb-szm789582\\n *\\n */\\n@Slf4j\\npublic class ReGeoBatchConsumer extends AbstractConsumer<ReGeoTask> {\\n\\n    private final LocationService locationService;\\n\\n    public static final int MAX_BATCH_SIZE = 20;\\n\\n    public ReGeoBatchConsumer(LocationService locationService,Boolean isSync ,PackTimeOutProvider packTimeOutProvider) {\\n        super(isSync, packTimeOutProvider);\\n        this.locationService =   locationService;\\n    }\\n\\n    public void doGpsReGeoBatchTask(List<ReGeoTask> subBatchReGeoTaskList, CoordinateSystemEnum coordinateSystemEnum) {\\n        String[] gpsBatch = subBatchReGeoTaskList.stream().map(ReGeoTask::getGps)\\n                .collect(Collectors.toList())\\n                .toArray(new String[subBatchReGeoTaskList.size()]);\\n\\n        ReGeoResultBase reGeoResultBase = locationService.gpsReGeoBatch(gpsBatch, coordinateSystemEnum);\\n        //如果请求的数据大小与返回不一致，所有的任务状态置为失败\\n        if(reGeoResultBase.getRegeocodes() == null || subBatchReGeoTaskList.size() != reGeoResultBase.getRegeocodes().size()){\\n            subBatchReGeoTaskList.forEach(t->t.setStatus(TaskStatus.FAILED));\\n            return;\\n        }\\n        for(int index = 0; index< reGeoResultBase.getRegeocodes().size() ; index ++) {\\n            ReGeoCode reGeoCode = reGeoResultBase.getRegeocodes().get(index);\\n\\n            ReGeoTask reGeoTask = subBatchReGeoTaskList.get(index);\\n            //回填逆地理转换结果 ReGeoCode\\n            reGeoTask.setReGeoCode(reGeoCode);\\n            reGeoTask.setStatus(TaskStatus.SUCCESS);\\n\\n        }\\n    }\\n\\n\\n    @Override\\n    public void doFlushPack() {\\n        // 为了方便后续的GPS处理 ，将reGeoTask按照CoordinateSystemEnum分组\\n        Map<CoordinateSystemEnum, List<ReGeoTask>> reGeoTaskMap = this.batchTaskList.stream().collect(Collectors.groupingBy(\\n                ReGeoTask::getCoordinateSystem));\\n        reGeoTaskMap.forEach((coordinateSystemEnum, reGeoTaskList) -> {\\n            this.doGpsReGeoBatchTask(reGeoTaskList, coordinateSystemEnum);\\n        });\\n    }\\n\\n    @Override\\n    public int maxBatchSize() {\\n        return MAX_BATCH_SIZE;\\n    }\\n\\n\\n\\n\\n}\\n\",\n" +
//            "                \"score\": 60.752167\n" +
//            "            },\n" +
//            "            {\n" +
//            "                \"startLine\": 0,\n" +
//            "                \"endLine\": 176,\n" +
//            "                \"path\": \"app/biz/service/src/main/java/com/antfinancial/antchain/trustiot/web/domain/register/stage/impl/ParamCheckStage.java\",\n" +
//            "                \"content\": \"/*\\n * Ant Group\\n * Copyright (c) 2004-2022 All Rights Reserved.\\n */\\npackage com.antfinancial.antchain.trustiot.web.domain.register.stage.impl;\\n\\nimport com.alipay.flab.pegasus_client.trusteddevice.model.KeyAlgType;\\nimport com.antfinancial.antchain.trustiot.common.accountservice.SessionManager;\\nimport com.antfinancial.antchain.trustiot.common.enums.DeviceFeatureEnum;\\nimport com.antfinancial.antchain.trustiot.common.enums.domain.DeviceRegisterStageEnum;\\nimport com.antfinancial.antchain.trustiot.common.enums.domain.StageFromTypeEnum;\\nimport com.antfinancial.antchain.trustiot.common.enums.domain.StageRegisterTypeEnum;\\nimport com.antfinancial.antchain.trustiot.common.model.device.DataModel;\\nimport com.antfinancial.antchain.trustiot.common.model.scene.SceneDTO;\\nimport com.antfinancial.antchain.trustiot.core.service.service.PegasusService;\\nimport com.antfinancial.antchain.trustiot.dal.dataobject.DeviceDO;\\nimport com.antfinancial.antchain.trustiot.util.AssertUtil;\\nimport com.antfinancial.antchain.trustiot.util.cons.SpecificErrorCode;\\nimport com.antfinancial.antchain.trustiot.web.domain.register.model.RegisterParamContext;\\nimport com.antfinancial.antchain.trustiot.web.domain.register.model.RegisterStageContext;\\nimport com.antfinancial.antchain.trustiot.web.domain.register.stage.RegisterStage;\\nimport com.antfinancial.antchain.trustiot.web.handler.VerifyHandler;\\nimport com.antfinancial.antchain.trustiot.web.model.device.Device;\\nimport com.antfinancial.antchain.trustiot.web.service.DeviceService;\\nimport lombok.extern.slf4j.Slf4j;\\nimport org.apache.commons.lang3.StringUtils;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.stereotype.Component;\\n\\n/**\\n * <AUTHOR> * @version ParamCheckStage.java, v 0.1 2022年05月16日 下午3:27 zzw01097420\\n * 1.参数校验阶段\\n */\\n@Component\\n@Slf4j\\npublic class ParamCheckStage implements RegisterStage {\\n\\n    @Autowired\\n    private VerifyHandler  verifyHandler;\\n    @Autowired\\n    private SessionManager sessionManager;\\n    @Autowired\\n    private DeviceService deviceService;\\n    @Autowired\\n    private PegasusService pegasusService;\\n\\n    /**\\n     * 处理器名称\\n     * @return\\n     */\\n    @Override\\n    public String name() {\\n        return DeviceRegisterStageEnum.PARAM_CHECK.getValue();\\n    }\\n\\n    /**\\n     * 判断是否需要使用此阶段\\n     * @param registerStageContext\\n     * @return\\n     */\\n    @Override\\n    public boolean needUseStage(RegisterStageContext registerStageContext) {\\n        return StageRegisterTypeEnum.ALL.equals(registerStageContext.getRegisterType())\\n                || StageRegisterTypeEnum.REGISTER.equals(registerStageContext.getRegisterType());\\n    }\\n\\n    /**\\n     * 处理上下文\\n     * @param registerStageContext\\n     */\\n    @Override\\n    public void processStage(RegisterStageContext registerStageContext) {\\n        //公共逻辑\\n        RegisterParamContext paramContext = registerStageContext.getRegisterParamContext();\\n\\n        // 校验scene，并根据scene.from_type修改上下文的fromType\\n        SceneDTO sceneDTO = verifyHandler.verifyScene(paramContext.getScene(), null);\\n        if(sceneDTO != null && sceneDTO.getFromTypeEnum() != null){\\n            registerStageContext.setFromType(sceneDTO.getFromTypeEnum());\\n        }\\n        if(paramContext.isMock()){\\n            registerStageContext.setFromType(StageFromTypeEnum.SERVER);\\n        }\\n\\n        //私有逻辑\\n        switch(registerStageContext.getFromType()){\\n            case MAAS:{\\n                //通过assetDataVersion 查找 customerDataModelId\\n                if (StringUtils.isNotBlank(paramContext.getAssetDataVersion())) {\\n                    DataModel customerDataModel = verifyHandler.getDataModelByTenantVersion(sessionManager.getUser().getTenant(),\\n                            paramContext.getAssetDataVersion());\\n                    paramContext.setDataModelId(customerDataModel.getDataModelId());\\n                }\\n                break;\\n            }\\n            case SERVER:\\n            case ANDROID:{\\n                // 如果显式的说要返回DeviceId则检查是否存在，如果存在则返回存在的值\\n                if (paramContext.isWithExistDeviceId()) {\\n                    String chainId = deviceService.getDeviceChainIdInternal(paramContext.getScene(), paramContext.getDeviceId());\\n                    if(StringUtils.isNotEmpty(chainId)){\\n                        registerStageContext.setChainDeviceId(chainId);\\n                        return;\\n                    }\\n                }\\n                break;\\n            }\\n            default: break;\\n        }\\n\\n        // 校验dataModel\\n        verifyHandler.verifyDataModelExist(paramContext.getDataModelId());\\n\\n        //私有逻辑\\n        switch(registerStageContext.getFromType()){\\n            //TODO 后面单独成处理器， 专供给几个四轮车客户使用（奇瑞，东风海博，地上铁，易靓好车）\\n            case SDK:{\\n                //解析及验证注册信息\\n                Device verifyRegisterDevice = verifyHandler.verifyRegisterInfo(paramContext.getScene(), paramContext.getDeviceId(),\\n                        paramContext.getSdkVersion(), paramContext.getContent(), paramContext.getSignature());\\n                //将解析的数据同步到上下文\\n                paramContext.fromVerifyRegisterInfo(verifyRegisterDevice);\\n                // 通过device注册的设备，特性默认均是signed_data\\n                paramContext.setDeviceFeature(DeviceFeatureEnum.SIGNED_DATA);\\n                break;\\n            }\\n            case MAAS:{\\n                //1 解析及验证注册信息适配 Maas设备\\n                //解析及验证注册信息\\n                Device verifyRegisterDevice = verifyHandler.verifyMaasRegisterInfo(RegisterParamContext.toDistributeByMaasReq(paramContext));\\n                //将解析的数据同步到上下文\\n                paramContext.fromVerifyMaasRegisterInfo(verifyRegisterDevice);\\n                break;\\n            }\\n            case SERVER:{\\n                DeviceDO existDeviceDO = deviceService.selectByDeviceIdScene(paramContext.getScene(), paramContext.getDeviceId());\\n                if(existDeviceDO != null){\\n                    registerStageContext.setRegistered(true);\\n                    return;\\n                }\\n                if (StringUtils.isNotBlank(paramContext.getDevicePublicKey())) {\\n                    // 查询预录入的imei表，填充设备属性\\n                    Device imeiVerifyResult = verifyHandler.verifyImei(paramContext.getDeviceIMEI(), paramContext.getScene());\\n                    paramContext.fromVerifyImei(imeiVerifyResult);\\n                    // 将设备公钥存入Iot中台\\n                    boolean registerTrustedDeviceFlag = pegasusService.registerTrustedDevice(paramContext.getScene(), paramContext.getDeviceId(), paramContext.getDevicePublicKey(), KeyAlgType.KEY_TYPE_ECC_256.name());\\n                    AssertUtil.requireTrue(registerTrustedDeviceFlag, SpecificErrorCode.REGISTER_TRUSTED_DEVICE_FAILED, \\\"设备注册失败\\\");\\n                }\\n                break;\\n            }\\n            case ANDROID:{\\n                DeviceDO existDeviceDO = deviceService.selectByDeviceIdScene(paramContext.getScene(), paramContext.getDeviceId());\\n                if(existDeviceDO != null){\\n                    registerStageContext.setRegistered(true);\\n                    return;\\n                }\\n                if (DeviceFeatureEnum.SIGNED_DATA.equals(paramContext.getDeviceFeature())) {\\n                    // 2021.830迭代改为在iot可信平台校验证书是否存在\\n                    boolean isCertExist = verifyHandler.verifyCertificateExist(paramContext.getDeviceId(), paramContext.getScene());\\n                    if (isCertExist){\\n                        return;\\n                    }\\n                    // 校验deviceId在中台是否已经存在\\n                    log.info(\\\"start check pegasusService.isDeviceExist(req.getScene(),req.getDeviceId()) scene:{} ,deviceId:{}\\\", paramContext.getScene(), paramContext.getDeviceId());\\n                    boolean result = pegasusService.isDeviceExist(paramContext.getScene(), paramContext.getDeviceId());\\n                    AssertUtil.requireTrue(result, SpecificErrorCode.DEVICE_ID_NOT_EXIST, \\\"check isDeviceExist fail: deviceId 未注册到iot中台\\\");\\n                }\\n                break;\\n            }\\n            default:\\n                break;\\n        }\\n    }\\n}\\n\",\n" +
//            "                \"score\": 58.208904\n" +
//            "            },\n" +
            "            {\n" +
            "                \"startLine\": 265,\n" +
            "                \"endLine\": 376,\n" +
            "                \"path\": \"app/biz/service/src/main/java/com/antfinancial/antchain/trustiot/web/service/impl/ImportTaskServiceImpl.java\",\n" +
            "                \"content\": \"                            importTaskErrContent.getRowKeyValueMap().get(identifyKey),\\n                            importTaskErrContent.getErrorCode(),\\n                            importTaskErrContent.getErrorMsg());\\n                    return list;\\n                }\\n        ).collect(Collectors.toList());\\n        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {\\n            ExcelUtil.createExcelDynamicHead(headInfo, ExcelTypeEnum.XLSX, \\\"导入失败记录明细\\\", dataList, baos);\\n            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(baos.toByteArray());\\n            ossManageHandler.storeFile(OssPathGeneratorUtil.resultXlsxFilePath(processContext.getCurrentImportTaskDO().getOssFileId()),\\n                    byteArrayInputStream);\\n        } catch (Exception e) {\\n            log.warn(\\\"write error detail failed\\\");\\n        }\\n    }\\n\\n    /**\\n     * 导入结果查询\\n     *\\n     * @param req\\n     * @return\\n     */\\n    @Override\\n    public QueryImportTaskResp queryImportResult(@Valid QueryImportTaskReq req) {\\n        //判断租户角色\\n        TrustIoTRoleType requestRoleType = TrustIoTRoleType.parseByName(sessionManager.getUser().getRole());\\n        AssertUtil.requireNotNull(requestRoleType, SpecificErrorCode.TENANT_ROLE_NOT_EXIST, \\\"租户角色不存在\\\");\\n        //CUSTOMER的租户要判断租户、场景一致性\\n        if (requestRoleType.getLevel() <= TrustIoTRoleType.CUSTOMER.getLevel()) {\\n            verifyHandler.verifyScene(req.getScene(), null);\\n        }\\n        ImportTaskDO importTaskDO = importTaskDAO.selectByTaskId(req.getRequestId());\\n        AssertUtil.requireNotNull(importTaskDO, LabelErrorCode.LABEL_IMPORT_TASK_NOT_EXIST, \\\"导入任务不存在\\\");\\n        boolean isSceneEquals = req.getScene().equals(importTaskDO.getScene());\\n        AssertUtil.requireTrue(isSceneEquals, LabelErrorCode.SCENE_NOT_MATCH, \\\"场景码与taskId不匹配\\\");\\n        QueryImportTaskResp resp = new QueryImportTaskResp();\\n        resp.setTotal(importTaskDO.getTotalCount());\\n        resp.setErrorMsg(importTaskDO.getErrMsg());\\n        ImportTaskStatusEnum importTaskStatusEnum = ImportTaskStatusEnum.parse(importTaskDO.getErrCode());\\n        switch (importTaskStatusEnum) {\\n            case FAILED:\\n                if (!ossManageHandler.hasFile(OssPathGeneratorUtil.resultXlsxFilePath(importTaskDO.getOssFileId()))) {\\n                    break;\\n                }\\n                //导入任务异常时，访问oss中存入的异常明细表格获取异常明细\\n                InputStream inputStream = ossManageHandler.getFile(OssPathGeneratorUtil.resultXlsxFilePath(importTaskDO.getOssFileId()));\\n                AtomicInteger errorCount = new AtomicInteger();\\n                ImportTypeEnum importTypeEnum = ImportTypeEnum.parse(importTaskDO.getImportType());\\n\\n                AbstractExcelProcessor abstractExcelProcessor = importTemplateHandler.getExcelProcessor(importTypeEnum);\\n                List<ImportTaskErrorContent> errorContentDetails = new ArrayList<>();\\n\\n                ExcelUtil.readExcelColumnAsc(inputStream, (Integer rowIndex, Map<String, String> errorResultValMap) -> {\\n                    String identify = abstractExcelProcessor.getIdentifyKey();\\n                    ImportTaskErrorContent importTaskErrorContent = ImportTaskErrorContent.fromResultMap(errorResultValMap, identify);\\n                    if (Objects.isNull(errorResultValMap.get(identify))) {\\n                        return;\\n                    }\\n                    errorCount.incrementAndGet();\\n                    //errorContentDetails只返回前一百条，查看更多失败记录请通过下载oss结果表\\n                    if (errorContentDetails.size() <= ValueConstants.MAX_IMPORT_ERROR_CONTENT_DETAILS) {\\n                        errorContentDetails.add(importTaskErrorContent);\\n                    }\\n                });\\n                resp.setErrorContentDetails(errorContentDetails);\\n                resp.setSuccess(importTaskDO.getTotalCount() - errorCount.longValue());\\n                resp.setFailed(errorCount.longValue());\\n                break;\\n            case RUNNING:\\n                //成功数量不一定准确，目前的导入任务由多线程完成，有可能stopIndex后面也有成功的行数据，此处仅做粗略的统计\\n                resp.setSuccess(importTaskDO.getStopIndex() - 1);\\n                resp.setFailed(0L);\\n                break;\\n            case SUCCESS:\\n                resp.setSuccess(importTaskDO.getTotalCount());\\n                resp.setFailed(0L);\\n                break;\\n        }\\n        resp.setStatus(importTaskStatusEnum);\\n        return resp;\\n    }\\n\\n    /**\\n     * 分页查询\\n     *\\n     * @param pageImportTaskReq\\n     * @return\\n     */\\n    @Override\\n    public PageImportTaskResp pageImportTask(@Valid PageImportTaskReq pageImportTaskReq) {\\n        PageList pageList = importTaskDAO.pageByCondition(pageImportTaskReq.getTenant(), pageImportTaskReq.getScene(),\\n                pageImportTaskReq.getImportType(),\\n                pageImportTaskReq.getTaskId(), pageImportTaskReq.getPageSize(), pageImportTaskReq.getPageIndex());\\n        return PageImportTaskResp.from(pageList);\\n    }\\n\\n    /**\\n     * 更新导入任务记录\\n     * @param taskId 任务id\\n     * @param errCode 错误码\\n     * @param errMsg 错误信息\\n     */\\n    @Override\\n    public void updateImportTask(String taskId, String errCode, String errMsg){\\n        ImportTaskDO importTaskDO = importTaskDAO.selectByTaskId(taskId);\\n        if(importTaskDO != null){\\n            this.updateImportTask(importTaskDO, errCode, errMsg, null);\\n        }\\n    }\\n}\\n\",\n" +
            "                \"score\": 46.514458\n" +
            "            },\n" +
            "            {\n" +
            "                \"startLine\": 142,\n" +
            "                \"endLine\": 287,\n" +
            "                \"path\": \"app/biz/service/src/main/java/com/antfinancial/antchain/trustiot/web/service/impl/LabelServiceImpl.java\",\n" +
            "                \"content\": \"        //        }\\n        //        // 校验数据的签名\\n        //        if (DeviceFeatureEnum.SIGNED_DATA.same(deviceDo.getDeviceFeature())) {\\n        //            verifySignature(collectContent, distributeDeviceDo, deviceDo);\\n        //        }\\n        //        // 校验数据的有效性\\n        //        List<JSONObject> contentList = verifyData(collectContent.getContent(), collectContent.getExtraData(),\\n        //                dataModelMap.get(deviceDo.getDataModelId()), labelTransferReq.getScene(), deviceDo, distributeDeviceDo);\\n        //        // 转为LabelEntity对象（LabelDO+chainDeviceId）\\n        //        List<LabelEntity> parserLabelEntityList = parserLabelEntity(contentList, scene, distributeDeviceDo, resultList,\\n        //                collectContent.getContent(), collectContent.getSignature(), collectContent.getChainDeviceId());\\n        //        log.info(\\\"dataChecker check success\\\");\\n        //        labelEntityList.addAll(parserLabelEntityList);\\n        //    }\\n        //    this.updateLabel(labelEntityList, resultList, scene, true);\\n        //    // 1. todo txTime未获取到，异步处理 查询执行结果\\n        //    return CollectLabelResp.builder().resultList(resultList).build();\\n        //});\\n        //return dealResult;\\n    }\\n\\n    @Override\\n    @DebugNotify(type = DebugTypeEnum.LABEL_TRANSFER)\\n    public CollectLabelResp transferRaw(@Valid LabelTransferRawReq labelTransferRawReq) {\\n        CollectStageContext collectStageContext = CollectStageContext.from(labelTransferRawReq);\\n        collectStageManager.collect(collectStageContext);\\n        return (CollectLabelResp)collectStageContext.getCollectResp();\\n        //todo 功能测试稳定后再删除清理注释代码\\n        ////使用API进度锁幂等处理\\n        //CollectLabelResp dealResult = apiProgressRecordProvider.start(OpenAPIConstants.LABEL_TRANSFER_RAW, labelTransferRawReq.getNonce(), labelTransferRawReq.toString(), () -> {\\n        //    String scene = labelTransferRawReq.getScene();\\n        //    Map<String, ContentParser> dataModelMap = new HashMap<>(labelTransferRawReq.getCollectLabelRawContentList().size());\\n        //    List<LabelEntity> labelEntityList = new ArrayList<>();\\n        //    List<LabelChainResult> resultList = new ArrayList<>(labelTransferRawReq.getCollectLabelRawContentList().size() * 4);\\n        //    for (CollectLabelRawContent collectRawContent : labelTransferRawReq.getCollectLabelRawContentList()) {\\n        //        //查询数据模型\\n        //        if (!dataModelMap.containsKey(collectRawContent.getDataModelId())) {\\n        //            DataModel dataModel = verifyHandler.getDataModelAndVerifyTenant(collectRawContent.getDataModelId(), null);\\n        //            dataModelMap.put(dataModel.getDataModelId(), new ContentParser(dataModel.getDataModel()));\\n        //        }\\n        //        // 校验数据的有效性\\n        //        List<JSONObject> contentList = verifyData(collectRawContent.getContent(), null,\\n        //                dataModelMap.get(collectRawContent.getDataModelId()), labelTransferRawReq.getScene(), null, null);\\n        //        // 转为LabelEntity对象（LabelDO+chainDeviceId）\\n        //        List<LabelEntity> parserLabelEntityList = parserLabelEntity(contentList, scene, null, resultList, collectRawContent.getContent(), null, null);\\n        //        log.info(\\\"dataChecker check success\\\");\\n        //        labelEntityList.addAll(parserLabelEntityList);\\n        //    }\\n        //    this.updateLabel(labelEntityList, resultList, scene, false);\\n        //    // 1. todo txTime未获取到，异步处理 查询执行结果\\n        //    return CollectLabelResp.builder().resultList(resultList).build();\\n        //});\\n        //return dealResult;\\n    }\\n\\n    public void verifySignature(CollectLabelContent collectContent, DistributeDeviceDO distributeDeviceDo, DeviceDO deviceDo) {\\n        log.info(\\\"Start verification in the loop\\\");\\n        // Iot中台 数据验真\\n        VerifyResult verifyResult = verifyHandler.verifySignatureResult(collectContent.getContent(),  collectContent.getSignature(),\\n                distributeDeviceDo.getScene(), distributeDeviceDo.getDeviceId());\\n        if (!verifyResult.isSuccess()) {\\n            DataCollectFailureEvent event = DataCollectFailureEvent.from(collectContent, distributeDeviceDo, deviceDo, distributeDeviceDo.getScene(),\\n                    DataCollectFailureEnum.VERIFY_SIGNATURE, verifyResult, DataCollectFailureSourceEnum.LABEL_TRACE);\\n            dispatchHandler.dispatchEvent(event);\\n        }\\n        AssertUtil.requireTrue(verifyResult.isSuccess(), SpecificErrorCode.DATA_VERIFICATION_FAILED, \\\"数据验真失败\\\");\\n        log.info(\\\"Complete one verification in the loop\\\");\\n    }\\n\\n    /**\\n     * 匹配数据模型，并返回JSONObject\\n     */\\n    public List<JSONObject> verifyData(String collectContent, String extraData, ContentParser contentParser, String scene, DeviceDO deviceDo, DistributeDeviceDO distributeDeviceDo) {\\n        List<JSONObject> contentList = new ArrayList<>();\\n        Object contentJson = JSON.parse(collectContent);\\n        if (contentJson instanceof JSONObject) {\\n            contentList.add((JSONObject) contentJson);\\n        } else if (contentJson instanceof JSONArray) {\\n            JSONArray contentJsonArray = (JSONArray) contentJson;\\n            for (int i = 0; i < contentJsonArray.size(); i++) {\\n                contentList.add(contentJsonArray.getJSONObject(i));\\n            }\\n        } else {\\n            log.warn(\\\"Uploaded data content format error\\\");\\n            throw new ServiceException(SpecificErrorCode.CONTENT_FORMAT_ERROR, \\\"上传数据content格式错误\\\");\\n        }\\n        // 验签完成后，往content中加入扩展的其他数据,用于数据模型校验\\n        appendExtraData(extraData, contentList);\\n        //与数据模型匹配并完成参数类型和必填项校验\\n        for (JSONObject content : contentList) {\\n            VerifyResult verifyResult = VerifyResult.fail();\\n            try{\\n                boolean validSuccess = contentParser.valid(content.toJSONString());\\n                if (validSuccess) {\\n                    verifyResult.setSuccess(true);\\n                } else {\\n                    verifyResult.setFailMsg(\\\"上传数据content与数据模型不匹配\\\");\\n                }\\n            }catch (Exception ex){\\n                log.warn(\\\"LabelServiceImpl.verifyData failed,exception:\\\", ex);\\n                verifyResult.setFailMsg(ex.getMessage());\\n            }\\n            if (!verifyResult.isSuccess()) {\\n                DataCollectFailureEvent event = DataCollectFailureEvent.from(CollectLabelContent.builder().content(content.toJSONString()).signature(StringUtils.EMPTY).build(), distributeDeviceDo, deviceDo, scene,\\n                        DataCollectFailureEnum.VERIFY_DATA, verifyResult, DataCollectFailureSourceEnum.LABEL_TRACE);\\n                dispatchHandler.dispatchEvent(event);\\n            }\\n            AssertUtil.requireTrue(verifyResult.isSuccess(), SpecificErrorCode.DATA_MODEL_NOT_MATCH, \\\"上传数据content与数据模型不匹配\\\");\\n        }\\n        log.info(\\\"content match the data model success, contentList.size:{}\\\", contentList.size());\\n        return contentList;\\n    }\\n\\n    public void appendExtraData(String extraDataContent, List<JSONObject> contentList) {\\n        if (StringUtils.isNotBlank(extraDataContent)&& JSON.parse(extraDataContent) instanceof JSONObject) {\\n            JSONObject extraData = JSONObject.parseObject(extraDataContent);\\n            for (JSONObject content : contentList) {\\n                 // 1. 校验 extraData是否与content的key重复，重复会导致content中的数据不可信\\n                boolean extraDataRepeat = CollectionUtils.containsAny(extraData.keySet(), content.keySet());\\n                AssertUtil.requireFalse(extraDataRepeat,SpecificErrorCode.DATA_MODEL_NOT_MATCH, \\\"extraData与设备上报数据 key重复\\\");\\n                // 2.把扩展数据直接 融入 content , 共同参与数据模型校验\\n                content.putAll(extraData);\\n            }\\n        }\\n    }\\n\\n    public List<LabelEntity> parserLabelEntity(List<JSONObject> contentList, String scene, DistributeDeviceDO disDeviceDO,\\n                                       List<LabelChainResult> resultList, String content, String signature, String chainDeviceId) {\\n        List<LabelEntity> labelEntityList = new ArrayList<>();\\n\\n        //校验chainDeviceId为空的情况下，是否包含DEVICE_ID，如果有则报错\\n        if(StringUtils.isEmpty(chainDeviceId)){\\n            contentList.parallelStream().forEach(contentJsonObject->{\\n                AssertUtil.requireFalse(contentJsonObject.containsKey(LabelDataChecker.DATA_DEVICE_ID), LabelErrorCode.LABEL_RAW_NOT_ALLOW_DEVICE_ID, \\\"raw接口content内容不允许包含DEVICE_ID\\\");\\n            });\\n        }\\n\\n        List<ParseLabelResult> results = contentList.parallelStream().map(contentJsonObject -> {\\n            ParseLabelResult parseLabelResult = new ParseLabelResult();\\n            try {\\n                // 1.校验 content中的 LABEL_ID 或ASSET_ID 是否 存在\\n                LabelDTO labelDTO = null;\\n                if (StringUtils.isNotBlank(contentJsonObject.getString(LabelDataChecker.DATA_LABEL_ID))) {\\n                    labelDTO = labelCacheDAO.findByLabelId(contentJsonObject.getString(LabelDataChecker.DATA_LABEL_ID));\\n                    AssertUtil.requireNotNull(labelDTO, LabelErrorCode.LABEL_ID_NOT_EXIST, \\\"label with this labelId is not exist\\\");\",\n" +
            "                \"score\": 45.91362\n" +
            "            },\n" +
            "            {\n" +
            "                \"startLine\": 67,\n" +
            "                \"endLine\": 216,\n" +
            "                \"path\": \"app/biz/service/src/main/java/com/antfinancial/antchain/trustiot/web/service/impl/LabelServiceImpl.java\",\n" +
            "                \"content\": \"import org.apache.commons.collections4.CollectionUtils;\\nimport org.apache.commons.lang3.StringUtils;\\nimport org.jboss.netty.util.internal.ConcurrentHashMap;\\nimport org.springframework.beans.BeanUtils;\\nimport org.springframework.context.ApplicationContext;\\nimport org.springframework.stereotype.Service;\\nimport org.springframework.validation.annotation.Validated;\\nimport javax.validation.Valid;\\nimport java.util.ArrayList;\\nimport java.util.Comparator;\\nimport java.util.List;\\nimport java.util.Map;\\nimport java.util.Objects;\\nimport java.util.stream.Collectors;\\n\\n/**\\n * @decription:\\n * @create: 2021-06-02\\n **/\\n@Service\\n@Validated\\n@AllArgsConstructor\\n@Slf4j\\npublic class LabelServiceImpl implements LabelService {\\n\\n    /**\\n     * version 为-1时，表示标签已销毁，禁止更新绑定信息和流转数据\\n     */\\n    private static final Long DESTROY_VERSION = -1L;\\n\\n    private final ApplicationContext applicationContext;\\n    private final ApiProgressRecordProvider apiProgressRecordProvider;\\n    private final DeviceDAO deviceDAO;\\n    private final DistributeDeviceDAO distributeDeviceDAO;\\n    private final DataModelCacheDAO dataModelDAO;\\n    private final LabelCacheDAO labelCacheDAO;\\n    private final LabelTraceDAO          labelTraceDAO;\\n    private final ContractAsyncProcessor contractAsyncProcessor;\\n    private final VerifyHandler          verifyHandler;\\n    private final DispatchHandler dispatchHandler;\\n    private final LabelSignedContentInfoDAO labelSignedContentInfoDAO;\\n    private final ContractContextProvider   contractContextProvider;\\n    private final ContractDataUpdateRecordDAO contractDataUpdateRecordDAO;\\n    private final ContractDataUpdateRecordService contractDataUpdateRecordService;\\n    private final CollectStageManager collectStageManager;\\n\\n    @Override\\n    @DebugNotify(type = DebugTypeEnum.LABEL_TRANSFER)\\n    public CollectLabelResp transfer(@Valid LabelTransferReq labelTransferReq) {\\n        CollectStageContext collectStageContext = CollectStageContext.from(labelTransferReq);\\n        collectStageManager.collect(collectStageContext);\\n        return (CollectLabelResp)collectStageContext.getCollectResp();\\n        //todo 功能测试稳定后再删除清理注释代码\\n        ////使用API进度锁幂等处理\\n        //CollectLabelResp dealResult = apiProgressRecordProvider.start(OpenAPIConstants.LABEL_TRANSFER, labelTransferReq.getNonce(), labelTransferReq.toString(), () -> {\\n        //    String scene = labelTransferReq.getScene();\\n        //    Map<String, ContentParser> dataModelMap = new HashMap<>(labelTransferReq.getCollectLabelContentList().size());\\n        //    List<LabelEntity> labelEntityList = new ArrayList<>();\\n        //    List<LabelChainResult> resultList = new ArrayList<>(labelTransferReq.getCollectLabelContentList().size() * 4);\\n        //    for (CollectLabelContent collectContent : labelTransferReq.getCollectLabelContentList()) {\\n        //        //查询设备\\n        //        DeviceDO deviceDo = deviceDAO.selectByChainId(collectContent.getChainDeviceId());\\n        //        AssertUtil.requireFalse(Objects.isNull(deviceDo),SpecificErrorCode.CHAIN_DEVICE_ID_NOT_EXIST, \\\"chain_deviceId不存在\\\");\\n        //        if (!DeviceStatusEnum.isDeviceNormal(deviceDo.getDeviceStatus())) {\\n        //            log.warn(\\\"The device is offline, ignore this data\\\");\\n        //            continue;\\n        //        }\\n        //        //查询发行设备\\n        //        DistributeDeviceDO distributeDeviceDo = distributeDeviceDAO.selectByDeviceIdScene(deviceDo.getDeviceId(), deviceDo.getScene());\\n        //        AssertUtil.requireNotNull(distributeDeviceDo, SpecificErrorCode.CHAIN_DEVICE_ID_HAS_NOT_DISTRIBUTED, \\\"设备未发行\\\");\\n        //        AssertUtil.requireTrue(StringUtils.equals(deviceDo.getScene(), scene),SpecificErrorCode.SCENE_NOT_MATCH, \\\"设备与场景码不匹配\\\");\\n        //        //查询数据模型\\n        //        if (!dataModelMap.containsKey(deviceDo.getDataModelId())) {\\n        //            DataModel dataModel = dataModelDAO.findByDataModelId(deviceDo.getDataModelId());\\n        //            dataModelMap.put(dataModel.getDataModelId(), new ContentParser(dataModel.getDataModel()));\\n        //        }\\n        //        // 校验数据的签名\\n        //        if (DeviceFeatureEnum.SIGNED_DATA.same(deviceDo.getDeviceFeature())) {\\n        //            verifySignature(collectContent, distributeDeviceDo, deviceDo);\\n        //        }\\n        //        // 校验数据的有效性\\n        //        List<JSONObject> contentList = verifyData(collectContent.getContent(), collectContent.getExtraData(),\\n        //                dataModelMap.get(deviceDo.getDataModelId()), labelTransferReq.getScene(), deviceDo, distributeDeviceDo);\\n        //        // 转为LabelEntity对象（LabelDO+chainDeviceId）\\n        //        List<LabelEntity> parserLabelEntityList = parserLabelEntity(contentList, scene, distributeDeviceDo, resultList,\\n        //                collectContent.getContent(), collectContent.getSignature(), collectContent.getChainDeviceId());\\n        //        log.info(\\\"dataChecker check success\\\");\\n        //        labelEntityList.addAll(parserLabelEntityList);\\n        //    }\\n        //    this.updateLabel(labelEntityList, resultList, scene, true);\\n        //    // 1. todo txTime未获取到，异步处理 查询执行结果\\n        //    return CollectLabelResp.builder().resultList(resultList).build();\\n        //});\\n        //return dealResult;\\n    }\\n\\n    @Override\\n    @DebugNotify(type = DebugTypeEnum.LABEL_TRANSFER)\\n    public CollectLabelResp transferRaw(@Valid LabelTransferRawReq labelTransferRawReq) {\\n        CollectStageContext collectStageContext = CollectStageContext.from(labelTransferRawReq);\\n        collectStageManager.collect(collectStageContext);\\n        return (CollectLabelResp)collectStageContext.getCollectResp();\\n        //todo 功能测试稳定后再删除清理注释代码\\n        ////使用API进度锁幂等处理\\n        //CollectLabelResp dealResult = apiProgressRecordProvider.start(OpenAPIConstants.LABEL_TRANSFER_RAW, labelTransferRawReq.getNonce(), labelTransferRawReq.toString(), () -> {\\n        //    String scene = labelTransferRawReq.getScene();\\n        //    Map<String, ContentParser> dataModelMap = new HashMap<>(labelTransferRawReq.getCollectLabelRawContentList().size());\\n        //    List<LabelEntity> labelEntityList = new ArrayList<>();\\n        //    List<LabelChainResult> resultList = new ArrayList<>(labelTransferRawReq.getCollectLabelRawContentList().size() * 4);\\n        //    for (CollectLabelRawContent collectRawContent : labelTransferRawReq.getCollectLabelRawContentList()) {\\n        //        //查询数据模型\\n        //        if (!dataModelMap.containsKey(collectRawContent.getDataModelId())) {\\n        //            DataModel dataModel = verifyHandler.getDataModelAndVerifyTenant(collectRawContent.getDataModelId(), null);\\n        //            dataModelMap.put(dataModel.getDataModelId(), new ContentParser(dataModel.getDataModel()));\\n        //        }\\n        //        // 校验数据的有效性\\n        //        List<JSONObject> contentList = verifyData(collectRawContent.getContent(), null,\\n        //                dataModelMap.get(collectRawContent.getDataModelId()), labelTransferRawReq.getScene(), null, null);\\n        //        // 转为LabelEntity对象（LabelDO+chainDeviceId）\\n        //        List<LabelEntity> parserLabelEntityList = parserLabelEntity(contentList, scene, null, resultList, collectRawContent.getContent(), null, null);\\n        //        log.info(\\\"dataChecker check success\\\");\\n        //        labelEntityList.addAll(parserLabelEntityList);\\n        //    }\\n        //    this.updateLabel(labelEntityList, resultList, scene, false);\\n        //    // 1. todo txTime未获取到，异步处理 查询执行结果\\n        //    return CollectLabelResp.builder().resultList(resultList).build();\\n        //});\\n        //return dealResult;\\n    }\\n\\n    public void verifySignature(CollectLabelContent collectContent, DistributeDeviceDO distributeDeviceDo, DeviceDO deviceDo) {\\n        log.info(\\\"Start verification in the loop\\\");\\n        // Iot中台 数据验真\\n        VerifyResult verifyResult = verifyHandler.verifySignatureResult(collectContent.getContent(),  collectContent.getSignature(),\\n                distributeDeviceDo.getScene(), distributeDeviceDo.getDeviceId());\\n        if (!verifyResult.isSuccess()) {\\n            DataCollectFailureEvent event = DataCollectFailureEvent.from(collectContent, distributeDeviceDo, deviceDo, distributeDeviceDo.getScene(),\\n                    DataCollectFailureEnum.VERIFY_SIGNATURE, verifyResult, DataCollectFailureSourceEnum.LABEL_TRACE);\\n            dispatchHandler.dispatchEvent(event);\\n        }\\n        AssertUtil.requireTrue(verifyResult.isSuccess(), SpecificErrorCode.DATA_VERIFICATION_FAILED, \\\"数据验真失败\\\");\\n        log.info(\\\"Complete one verification in the loop\\\");\\n    }\\n\\n    /**\\n     * 匹配数据模型，并返回JSONObject\\n     */\\n    public List<JSONObject> verifyData(String collectContent, String extraData, ContentParser contentParser, String scene, DeviceDO deviceDo, DistributeDeviceDO distributeDeviceDo) {\\n        List<JSONObject> contentList = new ArrayList<>();\",\n" +
            "                \"score\": 45.625713\n" +
            "            },\n" +
            "            {\n" +
            "                \"startLine\": 174,\n" +
            "                \"endLine\": 355,\n" +
            "                \"path\": \"app/biz/service/src/main/java/com/antfinancial/antchain/trustiot/web/service/impl/ImportTaskServiceImpl.java\",\n" +
            "                \"content\": \"        }\\n        //7. 释放isRunning状态\\n        importTaskDAO.updateRunningStatus(false, importTaskDO.getTaskId());\\n        log.info(\\\"handleImportTask end\\\");\\n        return true;\\n    }\\n\\n    public boolean doImportAsync(InputStream inputStream,\\n                                 ImportTaskProcessContext importContext,\\n                                 AbstractExcelProcessor abstractExcelProcessor,\\n                                 Map<Integer,ImportTaskErrorContent> rowIndexErrorMap) throws ExecutionException, InterruptedException {\\n        List<FutureTask<Boolean>> asyncTaskList = new ArrayList<>(importContext.getCurrentImportTaskDO().getTotalCount().intValue());\\n        ImportTaskDO curImportTask = importContext.getCurrentImportTaskDO();\\n        //获取起始行\\n        int startFrom = importContext.getCurrentImportTaskDO().getStopIndex().intValue();\\n\\n        ExcelUtil.readExcelColumnAsc(inputStream, importContext.getReqBody().getExcelType(),\\n                (Integer rowIndex, Map<String,String> keyValueMap) -> {\\n                    FutureTask<Boolean> asyncTask = new FutureTask<>(() -> {\\n                        //忽略起始行之前的数据\\n                        if (rowIndex < startFrom) {\\n                            return true;\\n                        }\\n                        try {\\n                            return abstractExcelProcessor.rowHandle(importContext, keyValueMap);\\n                        } catch (Exception e) {\\n                            log.warn(\\\"abstractExcelProcessor.rowHandle failed\\\", e);\\n                            rowIndexErrorMap.put(rowIndex, ImportTaskErrorContent.from(e, keyValueMap));\\n                        }\\n                        return false;\\n                    });\\n                    // 使用线程池并发处理\\n                    importTaskExecutor.submit(asyncTask);\\n                    asyncTaskList.add(asyncTask);\\n                });\\n        List<Boolean> allSuccessFlag = new ArrayList<>(curImportTask.getTotalCount().intValue());\\n        for (FutureTask<Boolean> futureTask : asyncTaskList) {\\n            // 调用get()等待子线程全部执行结束\\n            if (futureTask.get()) {\\n                allSuccessFlag.add(true);\\n            }\\n        }\\n        return allSuccessFlag.size() >= curImportTask.getTotalCount().intValue();\\n    }\\n\\n    private void updateImportTask(ImportTaskDO importTaskDO, String errCode, String errMsg, Long stopIndex) {\\n        ImportTaskDO importTaskDOModel = new ImportTaskDO();\\n        importTaskDOModel.setTaskId(importTaskDO.getTaskId());\\n        importTaskDOModel.setEndTime(new Date());\\n        importTaskDOModel.setErrCode(errCode);\\n        importTaskDOModel.setErrMsg(errMsg);\\n        importTaskDOModel.setStopIndex(stopIndex);\\n        importTaskDAO.updateByTaskId(importTaskDOModel);\\n    }\\n\\n    /**\\n     * 导入全部成功的处理\\n     * 1.修改import_task表的errorCode为success\\n     * 2.更新stopIndex为总行数totalCount\\n     **/\\n    private void endImportTaskSuccess(ImportTaskDO importTaskDO) {\\n        this.updateImportTask(importTaskDO, ImportTaskStatusEnum.SUCCESS.name(), null, importTaskDO.getTotalCount());\\n    }\\n\\n    /**\\n     * 导入存在失败记录时 :\\n     * 1. 写入首条失败信息到import_表的errorCode\\n     * 2. 写入首条失败信息的 rowIndex 到import_表的stopIndex\\n     * 3. 下次触发这个导入文件的消费请求时，忽略rowIndex之前的行, 而rowIndex行之后且导入成功的数据依赖 excelProcessor.rowHandle方法 内实现去重\\n     **/\\n    private void endImportTaskFailed(ImportTaskProcessContext processContext, Map<Integer,ImportTaskErrorContent> rowIndexErrorMap,\\n                                     AbstractExcelProcessor abstractExcelProcessor) {\\n        Entry<Integer,ImportTaskErrorContent> firstEntry = rowIndexErrorMap.entrySet().stream().findFirst().get();\\n        ErrorContent firstErrorContent = firstEntry.getValue();\\n        this.updateImportTask(processContext.getCurrentImportTaskDO(), firstErrorContent.getErrorCode(), firstErrorContent.getErrorMsg(),\\n                Long.valueOf(firstEntry.getKey()));\\n\\n        //失败记录明细写入oss\\n        String identifyKey = abstractExcelProcessor.getIdentifyKey();\\n        List<String> headList = new ArrayList<>();\\n        headList.add(ExcelConstants.ROW_INDEX);\\n        headList.add(identifyKey);\\n        headList.add(ExcelConstants.ERROR_CODE);\\n        headList.add(ExcelConstants.ERROR_MESSAGE);\\n        List<List<String>> headInfo = headList.stream().map(Arrays::asList).collect(Collectors.toList());\\n\\n        List<List<Object>> dataList = rowIndexErrorMap.entrySet().stream().map(\\n                entry -> {\\n                    ImportTaskErrorContent importTaskErrContent = entry.getValue();\\n                    Integer rowIndex = entry.getKey();\\n                    List<Object> list = Lists.newArrayList(rowIndex,\\n                            importTaskErrContent.getRowKeyValueMap().get(identifyKey),\\n                            importTaskErrContent.getErrorCode(),\\n                            importTaskErrContent.getErrorMsg());\\n                    return list;\\n                }\\n        ).collect(Collectors.toList());\\n        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {\\n            ExcelUtil.createExcelDynamicHead(headInfo, ExcelTypeEnum.XLSX, \\\"导入失败记录明细\\\", dataList, baos);\\n            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(baos.toByteArray());\\n            ossManageHandler.storeFile(OssPathGeneratorUtil.resultXlsxFilePath(processContext.getCurrentImportTaskDO().getOssFileId()),\\n                    byteArrayInputStream);\\n        } catch (Exception e) {\\n            log.warn(\\\"write error detail failed\\\");\\n        }\\n    }\\n\\n    /**\\n     * 导入结果查询\\n     *\\n     * @param req\\n     * @return\\n     */\\n    @Override\\n    public QueryImportTaskResp queryImportResult(@Valid QueryImportTaskReq req) {\\n        //判断租户角色\\n        TrustIoTRoleType requestRoleType = TrustIoTRoleType.parseByName(sessionManager.getUser().getRole());\\n        AssertUtil.requireNotNull(requestRoleType, SpecificErrorCode.TENANT_ROLE_NOT_EXIST, \\\"租户角色不存在\\\");\\n        //CUSTOMER的租户要判断租户、场景一致性\\n        if (requestRoleType.getLevel() <= TrustIoTRoleType.CUSTOMER.getLevel()) {\\n            verifyHandler.verifyScene(req.getScene(), null);\\n        }\\n        ImportTaskDO importTaskDO = importTaskDAO.selectByTaskId(req.getRequestId());\\n        AssertUtil.requireNotNull(importTaskDO, LabelErrorCode.LABEL_IMPORT_TASK_NOT_EXIST, \\\"导入任务不存在\\\");\\n        boolean isSceneEquals = req.getScene().equals(importTaskDO.getScene());\\n        AssertUtil.requireTrue(isSceneEquals, LabelErrorCode.SCENE_NOT_MATCH, \\\"场景码与taskId不匹配\\\");\\n        QueryImportTaskResp resp = new QueryImportTaskResp();\\n        resp.setTotal(importTaskDO.getTotalCount());\\n        resp.setErrorMsg(importTaskDO.getErrMsg());\\n        ImportTaskStatusEnum importTaskStatusEnum = ImportTaskStatusEnum.parse(importTaskDO.getErrCode());\\n        switch (importTaskStatusEnum) {\\n            case FAILED:\\n                if (!ossManageHandler.hasFile(OssPathGeneratorUtil.resultXlsxFilePath(importTaskDO.getOssFileId()))) {\\n                    break;\\n                }\\n                //导入任务异常时，访问oss中存入的异常明细表格获取异常明细\\n                InputStream inputStream = ossManageHandler.getFile(OssPathGeneratorUtil.resultXlsxFilePath(importTaskDO.getOssFileId()));\\n                AtomicInteger errorCount = new AtomicInteger();\\n                ImportTypeEnum importTypeEnum = ImportTypeEnum.parse(importTaskDO.getImportType());\\n\\n                AbstractExcelProcessor abstractExcelProcessor = importTemplateHandler.getExcelProcessor(importTypeEnum);\\n                List<ImportTaskErrorContent> errorContentDetails = new ArrayList<>();\\n\\n                ExcelUtil.readExcelColumnAsc(inputStream, (Integer rowIndex, Map<String, String> errorResultValMap) -> {\\n                    String identify = abstractExcelProcessor.getIdentifyKey();\\n                    ImportTaskErrorContent importTaskErrorContent = ImportTaskErrorContent.fromResultMap(errorResultValMap, identify);\\n                    if (Objects.isNull(errorResultValMap.get(identify))) {\\n                        return;\\n                    }\\n                    errorCount.incrementAndGet();\\n                    //errorContentDetails只返回前一百条，查看更多失败记录请通过下载oss结果表\\n                    if (errorContentDetails.size() <= ValueConstants.MAX_IMPORT_ERROR_CONTENT_DETAILS) {\\n                        errorContentDetails.add(importTaskErrorContent);\\n                    }\\n                });\\n                resp.setErrorContentDetails(errorContentDetails);\\n                resp.setSuccess(importTaskDO.getTotalCount() - errorCount.longValue());\\n                resp.setFailed(errorCount.longValue());\\n                break;\\n            case RUNNING:\\n                //成功数量不一定准确，目前的导入任务由多线程完成，有可能stopIndex后面也有成功的行数据，此处仅做粗略的统计\\n                resp.setSuccess(importTaskDO.getStopIndex() - 1);\\n                resp.setFailed(0L);\\n                break;\\n            case SUCCESS:\\n                resp.setSuccess(importTaskDO.getTotalCount());\\n                resp.setFailed(0L);\\n                break;\\n        }\\n        resp.setStatus(importTaskStatusEnum);\\n        return resp;\\n    }\\n\\n    /**\\n     * 分页查询\\n     *\\n     * @param pageImportTaskReq\\n     * @return\\n     */\\n    @Override\\n    public PageImportTaskResp pageImportTask(@Valid PageImportTaskReq pageImportTaskReq) {\",\n" +
            "                \"score\": 45.245644\n" +
            "            },\n" +
            "            {\n" +
            "                \"startLine\": 173,\n" +
            "                \"endLine\": 341,\n" +
            "                \"path\": \"app/biz/service/src/main/java/com/antfinancial/antchain/trustiot/web/service/impl/OssServiceImpl.java\",\n" +
            "                \"content\": \"        OssUploadInfoDO ossUploadInfoDO = GenerateUploadFileUrlReq.to(uploadFileUrlReq, tenant, uploadId, ossPath, uploadFileUrl);\\n        ossUploadInfoDAO.insert(ossUploadInfoDO);\\n\\n        return GenerateUploadFileUrlResp.from(uploadFileUrl, uploadId);\\n    }\\n\\n    /**\\n     * 确认上传成功\\n     * @param confirmReq 请求体\\n     * @return\\n     */\\n    @Override\\n    public void confirmUploadSuccess(@Valid ConfirmUploadSuccessReq confirmReq) {\\n        //根据uploadId判断记录是否存在\\n        OssUploadInfoDO ossUploadInfoDO = ossUploadInfoDAO.selectByUploadId(confirmReq.getUploadId());\\n        AssertUtil.requireNotNull(ossUploadInfoDO, SpecificErrorCode.OSS_UPLOAD_RECORD_NOT_EXIST, \\\"记录不存在\\\");\\n\\n        //判断租户是否一致\\n        verifyHandler.verifyTenant(ossUploadInfoDO.getTenant(), SpecificErrorCode.TENANT_NO_PERMISSION, \\\"记录不属于该租户\\\");\\n\\n        //业务过程幂等处理\\n        apiProgressRecordProvider.start(OpenAPIConstants.OSS_UPLOAD_FILE_CONFIRM_SUCCESS, confirmReq.getUploadId(), confirmReq.toString(), ()->{\\n            //判断oss中文件是否存在\\n            boolean hasOssFile = ossManageHandler.hasFile(ossUploadInfoDO.getOssPath());\\n            AssertUtil.requireTrue(hasOssFile, SpecificErrorCode.OSS_UPLOAD_FILE_NOT_EXIST, \\\"oss文件不存在\\\");\\n            //获取oss 元数据，根据content-length判断文件的大小作为文件大小\\n            long fileSize = ossManageHandler.getFileSize(ossUploadInfoDO.getOssPath());\\n            //判断是否超过了100G,超了记录日志\\n            if(fileSize > FILE_SIZE_100G){\\n                log.warn(\\\"OssService confirmUploadSuccess file size is too large, more than 100G, uploadId={}, ossPath={}\\\", confirmReq.getUploadId(), ossUploadInfoDO.getOssPath());\\n            }\\n            //是否超过5G，超过5G不处理\\n            AssertUtil.requireTrue(fileSize <= UPLOAD_FILE_SIZE_LIMIT, SpecificErrorCode.OSS_UPLOAD_FILE_SIZE_INVALID, \\\"oss文件大小超过5G\\\");\\n            //因当文件较大时，拷贝文件耗时较多，拷贝逻辑在MQ消费时进行\\n            //更新记录\\n            ossUploadInfoDO.setStatus(OssUploadStatusEnum.UPLOAD_SUCCESS.getValue());\\n            ossUploadInfoDO.setTotal(confirmReq.getTotal());\\n            ossUploadInfoDAO.updateById(ossUploadInfoDO);\\n            //发送MQ消息\\n            String mqMessage = JSON.toJSONString(CollectByOssFileMessage.from(ossUploadInfoDO));\\n            messageSender.sendCollectMessage(mqMessage, RocketMqTagEnum.COLLECT_BY_OSS_FILE.getValue());\\n            return Boolean.TRUE;\\n        });\\n    }\\n\\n    /**\\n     * 定时任务触发：处理停止的任务记录\\n     * 条件：用户已确认上传，但未处理，且最后修改时间在24h前\\n     */\\n    @Override\\n    public void handleStopRecords(){\\n        List<OssUploadInfoDO> ossUploadInfoDOS = selectAllStopRecord();\\n        if(CollectionUtils.isEmpty(ossUploadInfoDOS)){\\n            return;\\n        }\\n        ossUploadInfoDOS.forEach(ossUploadInfoDO -> SpringUtils.getBean(OssService.class).collectByOssFile(ossUploadInfoDO));\\n    }\\n\\n    /**\\n     * 根据uploadId处理上报数据\\n     * @param uploadId uploadId\\n     */\\n    @Override\\n    public void collectByOssFile(String uploadId){\\n        //根据uploadId判断记录是否存在\\n        OssUploadInfoDO ossUploadInfoDO = ossUploadInfoDAO.selectByUploadId(uploadId);\\n        AssertUtil.requireNotNull(ossUploadInfoDO, SpecificErrorCode.OSS_UPLOAD_RECORD_NOT_EXIST, \\\"记录不存在\\\");\\n        collectByOssFile(ossUploadInfoDO);\\n    }\\n\\n    /**\\n     * 根据记录处理上报数据\\n     * @param ossUploadInfoDO 上传记录详情\\n     */\\n    @Async\\n    @Override\\n    public void collectByOssFile(OssUploadInfoDO ossUploadInfoDO) {\\n        //判断oss中文件是否存在\\n        boolean hasOssFile = ossManageHandler.hasFile(ossUploadInfoDO.getOssPath());\\n        AssertUtil.requireTrue(hasOssFile, SpecificErrorCode.OSS_UPLOAD_FILE_NOT_EXIST, \\\"oss文件不存在\\\");\\n\\n        //获取拷贝文件路径\\n        String copyPath = String.format(\\\"upload_file_bak/%s\\\", ossUploadInfoDO.getOssPath());\\n        //因大文件时，拷贝文件较耗时，异步操作\\n        boolean hasBakOssFile = ossManageHandler.hasFile(copyPath);\\n        if (!hasBakOssFile) {\\n            log.info(\\\"OssService collectByOssFile copyObject start, recordInfo={}\\\", JSON.toJSONString(ossUploadInfoDO));\\n            ossManageHandler.copyObject(ossUploadInfoDO.getOssPath(), copyPath);\\n            log.info(\\\"OssService collectByOssFile copyObject end, recordInfo={}\\\", JSON.toJSONString(ossUploadInfoDO));\\n        }\\n\\n        log.info(\\\"OssService collectByOssFile processor start, recordInfo={}\\\", JSON.toJSONString(ossUploadInfoDO));\\n        //数据类型\\n        OssUploadDataTypeEnum dataTypeEnum = OssUploadDataTypeEnum.parse(ossUploadInfoDO.getDataType());\\n        OssService ossService = SpringUtils.getBean(OssService.class);\\n        AtomicLong totalCount = new AtomicLong();\\n        //根据数据类型，对应处理\\n        ossManageHandler.bufferReaderHandler(copyPath, ossUploadInfoDO.getStopIndex(), (lineIndex, lineContent) -> {\\n            totalCount.getAndIncrement();\\n            //异步处理单行数据\\n            ossService.parseAndSendMsg(dataTypeEnum, lineContent, lineIndex, ossUploadInfoDO);\\n            //固定步长更新oss_upload_info.stop_index\\n            updateStopIndex(lineIndex, ossUploadInfoDO.getId());\\n        });\\n        ossUploadInfoDAO.updateStatusById(OssUploadStatusEnum.PROCESS_COMPONENT.getValue(), ossUploadInfoDO.getId());\\n        log.info(\\\"OssService collectByOssFile processor end, recordInfo={}, totalSize={}\\\",\\n                JSON.toJSONString(ossUploadInfoDAO.selectByUploadId(ossUploadInfoDO.getUploadId())), totalCount.get());\\n    }\\n\\n    /**\\n     * 处理单行上报数据\\n     * @param dataTypeEnum\\n     * @param lineContent\\n     * @param lineIndex\\n     * @param ossUploadInfoDO\\n     */\\n    @Async(\\\"ossFileLineHandleExecutor\\\")\\n    @Override\\n    public void parseAndSendMsg(OssUploadDataTypeEnum dataTypeEnum, String lineContent, Long lineIndex, OssUploadInfoDO ossUploadInfoDO) {\\n        switch (dataTypeEnum) {\\n            case COLLECT_SINGLE: {\\n                //处理单条上报，流式获取处理数据\\n                CollectReq collectReq = parseLineContent(lineContent, CollectReq.class, ossUploadInfoDO, lineIndex);\\n                if (collectReq != null) {\\n                    String mqMessage = JSON.toJSONString(CollectByChainIdSingleMessage.from(collectReq, ossUploadInfoDO.getTenant()));\\n                    messageSender.sendCollectMessage(mqMessage, RocketMqTagEnum.COLLECT_SINGLE.getValue());\\n                }\\n                break;\\n            }\\n            case COLLECT_MUL: {\\n                //处理批量上报，流式获取处理数据\\n                CollectByChainIdMulReq collectMulReq = parseLineContent(lineContent, CollectByChainIdMulReq.class, ossUploadInfoDO,\\n                        lineIndex);\\n                if (collectMulReq != null) {\\n                    String mqMessage = JSON.toJSONString(CollectByChainIdMulMessage.from(collectMulReq, ossUploadInfoDO.getTenant()));\\n                    messageSender.sendCollectMessage(mqMessage, RocketMqTagEnum.COLLECT_MUL.getValue());\\n                }\\n                break;\\n            }\\n            case COLLECT_DEVICE_BIZ_DATA: {\\n                //处理业务数据上报，流式获取处理数据\\n                DeviceBizDataReq deviceBizDataReq = parseLineContent(lineContent, DeviceBizDataReq.class, ossUploadInfoDO, lineIndex);\\n                if (deviceBizDataReq != null) {\\n                    String mqMessage = JSON.toJSONString(CollectDeviceBizDataMessage.from(deviceBizDataReq, ossUploadInfoDO.getTenant()));\\n                    messageSender.sendCollectMessage(mqMessage, RocketMqTagEnum.COLLECT_DEVICE_BIZ_DATA.getValue());\\n                }\\n                break;\\n            }\\n            default:\\n                break;\\n        }\\n    }\\n\\n    /**\\n     * 固定步长更新oss_upload_info.stop_index\\n     * @param lineIndex 行号\\n     * @param recordId 记录id\\n     */\\n    private void updateStopIndex(Long lineIndex, Long recordId){\\n        if (lineIndex > 0 && lineIndex % STEP_LENGTH == 0) {\\n            ossUploadInfoDAO.updateStopIndexById(lineIndex, recordId);\\n        }\\n    }\\n\\n    /**\\n     * 解析单元格数据\\n     * @param lineContent 单元格内容\\n     * @param tClass 目标反序列化class\",\n" +
            "                \"score\": 43.114994\n" +
            "            }" +
//            "            {\n" +
//            "                \"startLine\": 0,\n" +
//            "                \"endLine\": 127,\n" +
//            "                \"path\": \"app/core/component-backpressure/src/main/java/com/antfinancial/antchain/trustiot/core/component/backpressure/consumer/AbstractConsumer.java\",\n" +
//            "                \"content\": \"/*\\n * Ant Group\\n * Copyright (c) 2004-2022 All Rights Reserved.\\n */\\npackage com.antfinancial.antchain.trustiot.core.component.backpressure.consumer;\\n\\nimport java.util.ArrayList;\\nimport java.util.List;\\nimport java.util.Objects;\\nimport java.util.concurrent.CountDownLatch;\\nimport java.util.concurrent.TimeUnit;\\nimport com.antfinancial.antchain.trustiot.core.component.backpressure.event.AbstractBackPressureTask;\\nimport com.antfinancial.antchain.trustiot.core.component.backpressure.provider.PackTimeOutProvider;\\nimport com.lmax.disruptor.EventHandler;\\nimport com.lmax.disruptor.LifecycleAware;\\nimport com.lmax.disruptor.TimeoutHandler;\\nimport lombok.RequiredArgsConstructor;\\nimport lombok.extern.slf4j.Slf4j;\\n\\n/**\\n * <AUTHOR> * @version BaseConsumer.java, v 0.1 2022年05月16日 下午5:20 wb-szm789582\\n */\\n@Slf4j\\n@RequiredArgsConstructor\\npublic abstract class AbstractConsumer<T extends AbstractBackPressureTask> implements EventHandler<T>, LifecycleAware, TimeoutHandler {\\n\\n    private final CountDownLatch shutdownLatch = new CountDownLatch(1);\\n\\n    protected final List<T> batchTaskList = new ArrayList<>();\\n\\n    protected final boolean isSync;\\n\\n    protected final PackTimeOutProvider packTimeOutProvider;\\n\\n    private  final String  logInfoPrefix = String.format(\\\"consumerName:%s, \\\", this.getClass().getSimpleName());\\n    @Override\\n    public void onStart() {\\n        log.info(logInfoPrefix + \\\" onStart\\\");\\n    }\\n\\n    @Override\\n    public void onShutdown() {\\n        log.info(logInfoPrefix + \\\" onShutdown\\\");\\n        shutdownLatch.countDown();\\n    }\\n\\n    public void awaitShutdown() throws InterruptedException {\\n        log.info(logInfoPrefix + \\\" awaitShutdown\\\");\\n        //单测时发现线程资源不足时，此处会无限等待，设一个10s的超时\\n        shutdownLatch.await(10, TimeUnit.SECONDS);\\n    }\\n\\n    @Override\\n    public void onEvent(final T event, final long sequence, final boolean endOfBatch) {\\n        // 1 装包：存入临时数组中\\n        synchronized (this.batchTaskList) {\\n            this.batchTaskList.add(event);\\n        }\\n        // 2 装包超时检查\\n        boolean isPackTimeOut = packTimeOutProvider.checkPackTimeOut();\\n\\n        // 3 判断是否达到最大容量 或 装包超时\\n        if (this.batchTaskList.size() >= this.maxBatchSize() || isPackTimeOut) {\\n            this.flushPack();\\n        }\\n    }\\n\\n    @Override\\n    public void onTimeout(long l) throws Exception {\\n        boolean isPackTimeOut = packTimeOutProvider.checkPackTimeOut();\\n        if (isPackTimeOut) {\\n            packTimeOutProvider.resetPackStartTime();\\n\\n            if (getPackedSize() > 0) {\\n                this.flushPack();\\n            }\\n        }\\n\\n    }\\n\\n    public int getPackedSize() {\\n        return this.batchTaskList.size();\\n    }\\n\\n    protected abstract void doFlushPack();\\n\\n    protected abstract int maxBatchSize();\\n\\n    private void flushPack() {\\n        log.info(logInfoPrefix + \\\"packed_size:{}\\\", getPackedSize());\\n\\n        try{\\n            this.doFlushPack();\\n        }finally {\\n            //避免doFlushPack方法中发生异常，保证以下两步正常执行\\n            this.completeBatchTaskList();\\n            //装包计时器归零\\n            packTimeOutProvider.resetPackStartTime();\\n        }\\n    }\\n\\n    private void completeBatchTaskList() {\\n        // 是否需要需要通过task唤醒业务线程\\n        if (this.isSync) {\\n            this.batchTaskList.forEach((task) -> {\\n                synchronized (task.getSyncLock()) {\\n                    task.getSyncLock().notifyAll();\\n                }\\n            });\\n        }\\n        // 是否存在消费成功回调任务\\n        this.batchTaskList.forEach((task) -> {\\n            if (Objects.nonNull(task.getConsumedCallBack())) {\\n                try {\\n                    task.getConsumedCallBack().call(task);\\n                } catch (Exception e) {\\n                    log.error(logInfoPrefix + \\\"ConsumeCallBack failed\\\", e);\\n                } finally {\\n                    task.clear();\\n                }\\n            }\\n        });\\n        this.batchTaskList.clear();\\n    }\\n}\\n\",\n" +
//            "                \"score\": 42.70901\n" +
//            "            }" +
            "        ]";

    public void testGetImports() throws FileNotFoundException {

        List<Name> imports = JavaATS.getImports(fileName);

        Assert.assertNotNull(imports);

    }

    public void testGetAlipayImports() throws FileNotFoundException {

        List<Name> imports = JavaATS.getAlipayImports(fileName);

        Assert.assertNotNull(imports);

    }

    public void testGetMethodInfo() {

        Optional<SimpleName> simpleName = JavaATS.getMethodInfo(fileName, "genCode");

        Assert.assertTrue(simpleName.isPresent());

    }


    public void testGetMethodContent() {

        String content = JavaATS.getMethodContent(fileName, "genCode");

        Assert.assertTrue(StringUtils.isNotBlank(content));

    }

    public void testGetMethodContent2() {

        String content = JavaATS.getMethodContent(fileName, "genCodeCode");

        Assert.assertTrue(StringUtils.isBlank(content));

    }

    public void testconfirmInterface() {

        String repoPath = "cloudide-platform/egumo";
        String branch = "branch-f48ac33f568e14d";
        String filePath = "app/web/src/main/java/com/alipay/egumo/web/home/<USER>/admin/workspace/AdminWorkspaceController.java";
        String method = "getAll";
        AntCodeClient.init("https://code.alipay.com", "");
        Collection<String> imports = JavaATS.confirmInterface(repoPath, branch, filePath, method);

        Assert.assertTrue(CollectionUtils.isNotEmpty(imports));

    }

    public void testconvertMethodChunk() {

        List<JavaATS.MethodChunk> chunkList = JSON.parseArray(chunks, JavaATS.MethodChunk.class);
        String repoPath = "common_release/trustiot";
        String branch = "vat_eval_c84eb104";
        AntCodeClient.init("https://code.alipay.com", "drrOuBhke-eFkPM3qF6T");
//        JavaATS.getFileMethods(repoPath, branch, );

    }
}