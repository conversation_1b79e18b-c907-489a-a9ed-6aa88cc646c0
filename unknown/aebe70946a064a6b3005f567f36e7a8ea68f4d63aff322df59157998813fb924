/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.response;

import com.alipay.codegencore.model.domain.TokenDO;

/**
 * <AUTHOR>
 * @version TokenResponse.java, v 0.1 2023年08月10日 下午2:16 yhw01352860
 */
public class TokenResponse extends TokenDO {
    /**
     * x限流规则
     */
    private String rateLimiterDesc;
    /**
     * x限流规则
     */
    private String nickName;
    /**
     * x限流规则
     */
    private String empId;

    private String phoneNumber;
    private String alipayAccount;

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getAlipayAccount() {
        return alipayAccount;
    }

    public void setAlipayAccount(String alipayAccount) {
        this.alipayAccount = alipayAccount;
    }

    public String getRateLimiterDesc() {
        return rateLimiterDesc;
    }

    public void setRateLimiterDesc(String rateLimiterDesc) {
        this.rateLimiterDesc = rateLimiterDesc;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }
}
