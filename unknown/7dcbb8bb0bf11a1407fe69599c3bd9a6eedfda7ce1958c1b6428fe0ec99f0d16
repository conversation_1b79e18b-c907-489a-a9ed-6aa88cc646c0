/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version WorkItemInfo.java, v 0.1 2024年04月16日 下午1:25 wb-tzg858080
 */
public class WorkItemInfo extends ToString {
    /**
     * 需求的URL
     */
    private String issueURL;

    /**
     * 需求的ID
     */
    private String issueId;

    /**
     * 分配给谁
     */
    private String assignTo;

    /**
     * 报告或创建此问题的人的名称。
     */
    private String author;

    /**
     * 需求类型
     */
    private String issueType;
    /**
     * 需求所在站点
     */
    private String issueSite;

    /**
     * 需求状态
     */
    private String issueStatus;

    /**
     * 需求标题
     */
    private String issueTitle;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 需求提供者
     */
    private String issueProvider;
    /**
     * 状态
     */
    private String status;

    public String getIssueURL() {
        return issueURL;
    }

    public void setIssueURL(String issueURL) {
        this.issueURL = issueURL;
    }

    public String getIssueId() {
        return issueId;
    }

    public void setIssueId(String issueId) {
        this.issueId = issueId;
    }

    public String getAssignTo() {
        return assignTo;
    }

    public void setAssignTo(String assignTo) {
        this.assignTo = assignTo;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getIssueType() {
        return issueType;
    }

    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    public String getIssueSite() {
        return issueSite;
    }

    public void setIssueSite(String issueSite) {
        this.issueSite = issueSite;
    }

    public String getIssueStatus() {
        return issueStatus;
    }

    public void setIssueStatus(String issueStatus) {
        this.issueStatus = issueStatus;
    }

    public String getIssueTitle() {
        return issueTitle;
    }

    public void setIssueTitle(String issueTitle) {
        this.issueTitle = issueTitle;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getIssueProvider() {
        return issueProvider;
    }

    public void setIssueProvider(String issueProvider) {
        this.issueProvider = issueProvider;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
