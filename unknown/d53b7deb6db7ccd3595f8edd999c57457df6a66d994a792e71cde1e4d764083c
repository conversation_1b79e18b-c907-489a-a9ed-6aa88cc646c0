package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RecordLogEnum;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.GPTCacheService;
import com.alipay.codegencore.service.gptcache.bean.GptCacheResponse;
import com.alipay.codegencore.service.gptcache.impl.GPTCacheRay;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.CollectLogUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 实现GPTCache缓存操作
 *
 * <AUTHOR>
 * 创建时间 2023年05月18日20:12:47
 */
@Service
public class GPTCacheServiceImpl implements GPTCacheService {
    private static final Logger LOGGER = LoggerFactory.getLogger("GPTCACHE");

    private static final int GPTCACHE_ENABLE_CODE = 1;
    private static final int MAX_ROUND_NUM = 1;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private GPTCacheRay gptCacheInterface;

    @Override
    public boolean isFirstRound(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        //1、regenerate的问题都不走cache
        if (gptAlgModelServiceRequest.isRegenerate()) {
            LOGGER.info("isEnableCache false, isRegenerate true");
            return false;
        }

        //2、非首轮不走cache
        int roundNum = (int) gptAlgModelServiceRequest
                .getChatCompletionRequest()
                .getMessages()
                .stream()
                .filter(e -> e!=null && e.getRole()!=null && !ChatRoleEnum.SYSTEM.getName().equalsIgnoreCase(e.getRole()))
                .count();

        if (roundNum > MAX_ROUND_NUM) {
            LOGGER.info("isEnableCache false, roundNum {}", roundNum);
            return false;
        }

        return true;
    }

    @Override
    public boolean isEnableCache() {
        //1、Drm 总开关
        int cacheCode = codeGPTDrmConfig.getGptCacheEnable();
        if (cacheCode != GPTCACHE_ENABLE_CODE) {
            LOGGER.info("isEnableCache false,  DRM cacheEnableCode {}", cacheCode);
            return false;
        }
        //2、流量控制，随机种子,通过随机数来控制流量，DRM可以配置1-1000内的数量
        int factor = codeGPTDrmConfig.getGptCacheFactor();
        int randomNum = (new Random()).nextInt(1000) + 1;
        LOGGER.info("get random num: {}, factor {}", randomNum, factor);
        return randomNum <= factor;
    }

    @Override
    public GptCacheResponse getCache(AlgoBackendDO algoBackendDO, GptAlgModelServiceRequest gptAlgModelServiceRequest, String requestId) {
        try {
            long promptSizeWithoutSystemMessage = gptAlgModelServiceRequest.
                    getChatCompletionRequest()
                    .getMessages()
                    .stream()
                    .filter(e -> e!=null && e.getRole()!=null && !ChatRoleEnum.SYSTEM.getName().equalsIgnoreCase(e.getRole()))
                    .count();

            if (promptSizeWithoutSystemMessage > 1) {
                return null;
            }
        } catch (Exception e) {
            printErrorLog(e, null);
        }

        GptCacheResponse gptCacheResponse = null;
        boolean isSuc = false;
        String errorInfo = null;
        long startTime = System.currentTimeMillis();
        String model = algoBackendDO.getModel();
        try {
            LOGGER.info("getCache request model {}, prompt {}, requestId {}", model, JSON.toJSONString(gptAlgModelServiceRequest.getChatCompletionRequest().getMessages()), requestId);
            JSONObject cache = gptCacheInterface.getCache(model, gptAlgModelServiceRequest.getChatCompletionRequest().getMessages());
            LOGGER.info("getCache request getCache response {}", cache);
            //cache服务请求不通
            if (cache == null) {
                printErrorLog(null, null);
                errorInfo = "return is null";
            } else {
                int errorCode = cache.getInteger("errorCode");
                if (errorCode != 0) {
                    //服务异常
                    printErrorLog(null, cache);
                    errorInfo = "errorCode is " + errorCode;
                } else {
                    String cacheHit = cache.getString("cacheHit");
                    if (!"true".equalsIgnoreCase(cacheHit)) {
                        //未命中缓存
                        printErrorLog(null, cache);
                        errorInfo = "cacheHit is not true";
                    } else {
                        if(!cache.containsKey("hit_query") || null == cache.get("hit_query")) {
                            errorInfo = "hit_query is empty";
                        } else {
                            String answer = cache.getString("answer");
                            JSONArray hitQueryArray = cache.getJSONArray("hit_query");
                            //把JSONArray转为List<ChatMessage>
                            List<ChatMessage> hitQueryList = hitQueryArray.toJavaList(ChatMessage.class);
                            gptCacheResponse = new GptCacheResponse(answer, hitQueryList);
                            isSuc = true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            errorInfo = e.getMessage();
            printErrorLog(e, null);
        }
        long during = System.currentTimeMillis() - startTime;
        Map<String, Object> detail = new HashMap<>();
        detail.put("serviceName", "gptCache");
        detail.put("operate", "get");
        detail.put("model", model);
        detail.put("status", isSuc);
        detail.put("during", during);
        detail.put("requestId", requestId);
        detail.put("errorInfo", errorInfo);
        CollectLogUtils.printCollectLog(RecordLogEnum.DEPENDENCY_DATA, detail);
        return gptCacheResponse;
    }

    @Override
    public boolean putCache(String model, List<ChatMessage> query, String realAnswer, String requestId) {
        boolean isSuc = false;
        long startMil = System.currentTimeMillis();
        String errorInfo = null;
        try {
            LOGGER.info("putCacheResp model {}, query {}, realAnswer {}, requestId {}", model, JSON.toJSONString(query), realAnswer, requestId);
            JSONObject jsonObject = gptCacheInterface.putCache(model, query, realAnswer);
            LOGGER.info("putCacheResp putCache response {} ", jsonObject);
            if (jsonObject == null) {
                printErrorLog(null, null);
                errorInfo = "return is null";
            } else {
                Integer errorCode = jsonObject.getInteger("errorCode");
                if (errorCode != 0) {
                    printErrorLog(null, jsonObject);
                    errorInfo = "errorCode is " + errorCode;
                } else {
                    String writeStatus = jsonObject.getString("writeStatus");
                    if(!"success".equalsIgnoreCase(writeStatus)) {
                        errorInfo = "writeStatus is " + writeStatus;
                    } else {
                        isSuc = true;
                    }
                }
            }
        } catch (Exception e) {
            errorInfo = e.getMessage();
            printErrorLog(e, null);
        }
        long during = System.currentTimeMillis() - startMil;
        Map<String, Object> detail = new HashMap<>();
        detail.put("serviceName", "gptCache");
        detail.put("operate", "put");
        detail.put("model", model);
        detail.put("status", isSuc);
        detail.put("during", during);
        detail.put("requestId", requestId);
        detail.put("errorInfo", errorInfo);
        CollectLogUtils.printCollectLog(RecordLogEnum.DEPENDENCY_DATA, detail);
        return isSuc;
    }

    /**
     * 从gptCache中删除缓存
     *
     * @param model 算法模型
     * @return 删除是否成功
     */
    @Override
    public boolean removeCache(String model) {
        try {
            LOGGER.info("removeCache model {}", model);
            JSONObject jsonObject = gptCacheInterface.removeCache(model);
            LOGGER.info("removeCache, model:{}, response {} ", model, jsonObject);
            if (jsonObject == null) {
                printErrorLog(null, null);
                return false;
            }

            Integer errorCode = jsonObject.getInteger("errorCode");
            if (errorCode != 0) {
                printErrorLog(null, jsonObject);
                return false;
            }

            String writeStatus = jsonObject.getString("writeStatus");
            if ("success".equalsIgnoreCase(writeStatus)) {
                return true;
            }
        } catch (Exception e) {
            printErrorLog(e, null);
        }
        return false;
    }

    private void printErrorLog(Exception e, JSONObject cache) {
        if (e == null) {
            LOGGER.error("######GPTCache response error cache: {}", cache);
            return;
        }
        LOGGER.error("######GPTCache response error cache: {}", cache, e);
    }
}
