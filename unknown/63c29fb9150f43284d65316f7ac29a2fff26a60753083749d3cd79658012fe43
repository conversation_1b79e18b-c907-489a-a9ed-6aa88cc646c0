# sofa boot
sofa.version=SOFABoot
# appname
spring.application.name=codegencore
# app port
server.port=8888
# zone
domain.name=@domainname@
inner.domain=${domain.name}
# log level
logging.level.com.alipay.codegencore=INFO
# log config
logging.path=/home/<USER>/logs
logging.root=${logging.path}
logging.config=classpath:log4j2-spring.xml
# sofa config
sofa_runtime_spring_major_version=4
# sofa config
com.alipay.sofa.ignore.unresolvable.placeholders=true

zsearch_endpoint=http://zsearch-et2.alipay.com:9999
#
sofa.boot.ddcs.enableAutoRegister=true
com.alipay.sofa.dds.config.obproxy-host=127.0.0.1
# buservice url
sofa.buservice.antbuserviceUrl=http://antbuservice.stable.alipay.net

# mybatis pagehelper
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql
pagehelper.page-size-zero=true


spring.servlet.multipart.max-file-size=300MB
spring.servlet.multipart.max-request-size=300MB

# appthread
app_thread_pool_num=8000

# ???? - java???
alg_generator_line=http://zark.sh.global.alipay.com/deveffinlp/codecompletion_line/codecompletion_line
# ???? - java????
alg_generator_fragment=http://zark.sh.global.alipay.com/deveffinlp/codecompletion_snippet_java/codecompletion_snippet
# disable timer log filter, enable async api
sofa.web.timer-log-filter-enable=false
# ???? - python???
alg_generator_python=http://zark.sh.global.alipay.com/deveffinlp/codecompletion_line_python/codecompletion_line
# ???? - js/ts???
alg_generator_js=http://zark.sh.global.alipay.com/deveffinlp/codecompletion_line_js/codecompletion_line
tool_search_url=http://zark.sh.global.alipay.com/deveffinlp/codegpt_sgchain_service/codegpt_tool_manager

keymap_biz_code = codegpt
keymap_scene_id = tachplayChatText
keymap_interface = /detectmng/api/detectservice/createSyncTask.json
# Allow circular dependencies
spring.main.allow-circular-references=true
# agent sdk endpoint
agentsecsdk_endpoint = https://agentsecgw-api.antgroup-inc.cn