package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.AlgStrategyEnum;
import com.alipay.codegencore.model.enums.LanguageEnum;
import com.alipay.codegencore.model.model.CheckCodeModel;
import com.alipay.codegencore.model.model.CodeModel;
import com.alipay.codegencore.model.model.CompletionsCodeModel;
import com.alipay.codegencore.model.model.analysis.TempCodeAnalysisResultContext;
import com.alipay.codegencore.model.request.CompletionsRequestBean;
import com.alipay.codegencore.model.request.tsingyan.CompletionRequestBean;
import com.alipay.codegencore.model.response.tsingyan.CompletionResultModel;
import com.alipay.codegencore.service.CodeRecommandService;
import com.alipay.codegencore.service.utils.CommonUtils;
import com.alipay.codegencore.service.utils.SingletonServerManager;
import com.alipay.codegencore.utils.CommonTools;
import com.alipay.codegencore.utils.codescan.CodeStaticScanService;
import com.alipay.codegencore.utils.codescan.ScanTypeEnum;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.alipay.codegencore.model.AppConstants.CODE_COMPLETION_DISPLAY_LENGTH;

/**
 * 代码推荐服务实现
 *
 * <AUTHOR>
 * 创建时间 2022-01-07
 */
@Service
public class CodeRecommandServiceImpl implements CodeRecommandService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CodeRecommandServiceImpl.class);

    @Resource
    private CodeStaticScanService codeStaticScanService;

    /**
     * 换行标识符
     */
    private static final String ENTER_SYMBOL = "\n";

    /**
     * java代码行补全地址
     */
    @AppConfig("alg_generator_line")
    private String ALG_LINE_URL;
    /**
     * python代码行补全地址
     */
    @AppConfig("alg_generator_python")
    private String ALG_PY_LINE_URL;
    /**
     * js代码行补全地址
     */
    @AppConfig("alg_generator_js")
    private String ALG_JS_LINE_URL;

    /**
     * 补全代码(linkIDE用）
     *
     * @param completionsRequestBean
     * @return
     */
    @Override
    public CompletionsCodeModel completions(CompletionsRequestBean completionsRequestBean) {
        CompletionsCodeModel completionsCodeModel = new CompletionsCodeModel();
        completionsCodeModel.setSessionId(SingletonServerManager.getIdGenerator().nextId());

        TempCodeAnalysisResultContext tempCodeAnalysisContext = codeStaticScanService.analysisCode(completionsRequestBean.getPrompt(),
                ScanTypeEnum.TEMP_REQUEST, TempCodeAnalysisResultContext.class);
        //如果还是null，直接返回
        if (tempCodeAnalysisContext == null) {
            return completionsCodeModel;
        }
        String prompt = completionsRequestBean.getPrompt();
        String[] lineArr = prompt.split(ENTER_SYMBOL);

        if (StringUtils.isBlank(completionsRequestBean.getPrompt()) || lineArr == null || lineArr.length == 0 || CommonUtils.isNoteLine(
                lineArr[lineArr.length - 1])) {
            return completionsCodeModel;
        }

//        List<CodeModel> codeModelList = lineStrategy.generateCode(completionsRequestBean.getPrompt(),completionsCodeModel.getSessionId(), lineArr, tempCodeAnalysisContext);
//上面暂时注释以前代码，用青燕接口，线上稳定后可直接删除注释代码
        CompletionRequestBean tyReq = new CompletionRequestBean();
        tyReq.setPrompt(completionsRequestBean.getPrompt());
        tyReq.setSessionId(completionsRequestBean.getSessionId());
        CompletionResultModel completionResultModel = completionFromTsingYan(tyReq, AppConstants.TSINGYAN_PLUGIN_CONFIG_MODEL.getCompletionConfigModel().getRemoteTimeOut());
        //如果范围值为null，那么直接返回
        if (completionResultModel == null) {
            completionsCodeModel.setCodeModelList(null);
            completionsCodeModel.setAlgStrategyCount(null);
            return completionsCodeModel;
        }
        //判断modelList，如果model长度为null或者size等于0，那么也是直接返回
        List<CodeModel> codeModelList = completionResultModel.getCodeModelList();
        if (completionResultModel == null || CollectionUtils.isEmpty(completionResultModel.getCodeModelList())) {
            completionsCodeModel.setCodeModelList(null);
            completionsCodeModel.setAlgStrategyCount(null);
            return completionsCodeModel;
        }

        if (codeModelList.size() > 3) {
            codeModelList = codeModelList.subList(0, 3);
        }

        //linkIDE环境，需要服务端分析并做简单校验
        List<CodeModel> result = new ArrayList<>();
        String realLastLine = lineArr[lineArr.length - 1];
        for (CodeModel codeModel : codeModelList) {
            List<CheckCodeModel> checkCodeModelList = CommonTools.analysisCheckModelList(codeModel.getContent(), realLastLine, tempCodeAnalysisContext);
            if (checkCodeModelList != null && !checkCodeModelList.isEmpty()) {
                //如果初步校验不通过，跳过此补全结果
                if (!CommonTools.checkCode(checkCodeModelList)) {
                    continue;
                }
                //初步校验通过，保存到返回值，在客户端进行下一步校验
                codeModel.setCheckCodeModelList(checkCodeModelList);
            }
            result.add(codeModel);
        }

        completionsCodeModel.setProcessingTime(completionResultModel.getProcessingTime());
        completionsCodeModel.setCodeModelList(result);
        completionsCodeModel.setAlgStrategyCount(algStrategyCount(result));
        return completionsCodeModel;
    }

    /**
     * 青燕代码补全
     *
     * @param completionRequestBean
     * @return
     */
    private CompletionResultModel completionFromTsingYan(CompletionRequestBean completionRequestBean, long timeOut) {
        try {
            if (StringUtils.isBlank(completionRequestBean.getPrompt())) {
                return null;
            }
            //组装算法请求参数
            Map<String, Object> param = assembleCompletionRequest(completionRequestBean);
            String[] lineArr = completionRequestBean.getPrompt().split(ENTER_SYMBOL);
            String realLastLine = lineArr[lineArr.length - 1];
            String algUrl;
            switch (LanguageEnum.valueOf(completionRequestBean.getLang())) {
                case JAVASCRIPT:
                case TYPESCRIPT:
                    algUrl = ALG_JS_LINE_URL;
                    break;
                case PYTHON:
                    algUrl = ALG_PY_LINE_URL;
                    break;
                default:
                    algUrl = ALG_LINE_URL;
                    break;

            }
            String resultJson = HttpClient.post(algUrl).content(JSONObject.toJSONString(param)).syncExecute(timeOut);
            if (StringUtils.isBlank(resultJson)) {
                LOGGER.error("算法服务请求异常(超时)。返回值为空");
                return null;
            }

            JSONObject httpResultJsonObj = JSONObject.parseObject(resultJson);
            if (httpResultJsonObj.getInteger("errorCode") != 0) {
                LOGGER.error("算法服务请求异常(errorCode不为0).请求参数: {} , 返回值: {}", JSONObject.toJSONString(param), resultJson);
                return null;
            }
            JSONArray rankResultJsonObj = httpResultJsonObj.getJSONObject("result").getJSONObject("LineRanks").getJSONArray(
                    "generatedText");
            JSONArray rankScoreJsonObj = httpResultJsonObj.getJSONObject("result").getJSONObject("LineRanks").getJSONArray("scores");
            Long processingTime = httpResultJsonObj.getJSONObject("result").getJSONObject("LineRanks").getLong("processingTime");
            if (rankResultJsonObj == null || rankResultJsonObj.size() == 0) {
                return null;
            }
            CompletionResultModel result = new CompletionResultModel();
            result.setProcessingTime(processingTime);
            result.setSessionId(completionRequestBean.getSessionId());
            List<CodeModel> codeModelList = new ArrayList<>(rankResultJsonObj.size());
            for (int i = 0; i < rankResultJsonObj.size(); i++) {
                String content = rankResultJsonObj.getString(i);
                CodeModel codeModel = new CodeModel();
                codeModel.setAlgStrategyEnum(AlgStrategyEnum.LINE.name());
                codeModel.setId(SingletonServerManager.getIdGenerator().nextId());
                codeModel.setContent(content);
                codeModel.setDisplayName(CommonTools.getDisplayName(realLastLine, content, CODE_COMPLETION_DISPLAY_LENGTH));
                //防止算法返回的score数组和result数组长度不一致，加个长度判断
                if (rankScoreJsonObj.size() >= i + 1) {
                    codeModel.setScore(rankScoreJsonObj.getDouble(i));
                } else {
                    codeModel.setScore(0.0);
                }

                codeModelList.add(codeModel);
            }
            Collections.sort(codeModelList);
            result.setCodeModelList(codeModelList);
            return result;
        } catch (Throwable e) {
            LOGGER.error("在线代码补全异常。参数: {}", JSONObject.toJSONString(completionRequestBean), e);
            return null;
        }
    }

    /***************************** 私有方法 ************************************/
    /**
     * 计算结果集数量
     * 大模型稳定后删除
     *
     * @param codeModelList
     * @return
     */
    @Deprecated
    private Map<String, Integer> algStrategyCount(List<CodeModel> codeModelList) {
        Map<String, Integer> result = new HashMap<>(codeModelList.size());
        result.put(AlgStrategyEnum.LINE.name(), codeModelList.size());
        return result;
    }


}
