package com.alipay.codegencore.service.impl.model;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.tbase.model.CasValue;
import com.alipay.antdld.agent.commons.org.apache.http.util.TextUtils;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RepoChatStreamDataTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.RepoInfoModel;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.model.codegpt.FinishParam;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.PluginServiceRequestContext;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.model.rag.DocSearchResultItem;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.request.CodegptRequestBean;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.gptcache.bean.GptCacheResponse;
import com.alipay.codegencore.service.impl.GPTCacheServiceImpl;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.ToolLearningUtil;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.mist.shade.google.common.base.Splitter;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zcache.serialize.BytesObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

import static com.alipay.codegencore.model.enums.ResponseEnum.AI_CALL_ERROR;

/**
 * 算法模型工具类service
 */

@Service
public class AlgoModelUtilService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AlgoModelUtilService.class);
    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");
    private static final String REPO_CHAT_STREAM_VERSION = "3.0";
    private static final String STREAM_TYPE = "version";
    private static final String REPO_CHAT_STREAM_SEARCH_KEYWORD = "keyword";
    private static final String REPO_CHAT_STREAM_SEARCH_FILE_LIST = "fileList";
    private static final String REPO_CHAT_STREAM_REFRESH = "refresh";
    private static final String REPO_CHAT_STREAM_RECOMMEND_QUESTION = "recommendQuestion";


    /**
     * 玛雅部署平台
     */
    private static final String MAYA = "MAYA";

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource
    private CheckService checkService;

    @Resource
    private GPTCacheServiceImpl gptCacheService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private ChatSessionManageService chatSessionManageService;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;

    @Resource
    private CalculateTokenService calculateTokenService;

    @Resource
    private StreamDataQueueUtilService streamDataQueueUtilService;


    /**
     * 1、从tbase中获取数据  24.8.9更改为MayaStreamModelHandler和ChatGptModelHubHandler从内存队列取数据
     * 2、分段送审
     * 3、flush数据给调用方
     * 4、写入gptCatch(如需要)
     * @param params
     * @param copyMessages
     * @param requestCheckResultModel
     * @param needGPTCache
     */
    public void getChatDataFromTBase(GptAlgModelServiceRequest params, List<ChatMessage> copyMessages, CheckResultModel requestCheckResultModel, boolean needGPTCache, ChatStreamBuffer streamBuffer,boolean isQueue) {
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        FinishParam finishParam = new FinishParam();
        finishParam.setRunTimeInfo(new JSONObject());
        finishParam.setCopyMessages(copyMessages);
        finishParam.setUser(params.getUserName());
        finishParam.setRequestCheckResultModel(requestCheckResultModel);
        finishParam.setChatStreamPartResponseConsumer(params.getChatStreamPartResponseHandler());
        finishParam.setResultHandler(params.getResultHandler());
        finishParam.setNeedDelTBaseKey(params.getNeedDelTBaseKey());
        finishParam.setModelEnv(params.getModelEnv());
        finishParam.setNeedGPTCache(needGPTCache);
        finishParam.setAlgoBackendDO(algoBackendDO);
        ChatRequestExtData chatRequestExtData = params.getChatCompletionRequest().getChatRequestExtData();
        finishParam.setChatRequestExtData(chatRequestExtData);
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = params.getChatStreamPartResponseHandler();
        String sessionUid = null;
        List<String> fileList = new ArrayList<>();
        StringBuilder originAnswer = new StringBuilder();
        String bizId = null;
        if (chatRequestExtData != null) {
            sessionUid = chatRequestExtData.getSessionUid();
            bizId = chatRequestExtData.getBizId();
        }
        boolean streamChat = params.getChatCompletionRequest().getStream();
        finishParam.setStreamChat(streamChat);
        String requestId = params.getRequestId();
        finishParam.setRequestId(params.getRequestId());
        String key = params.getUniqueAnswerId();
        finishParam.setKey(key);
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("unique key is {}", key);
        }

        int streamDataIndex = 1;
        int waitTimeOut = AlgoBackendUtil.exactFirstStreamDataWaitTimeConfig(algoBackendDO);
        long waitStartTime = System.currentTimeMillis();
        String answerUid = params.getAnswerUid();
        if (StringUtils.isBlank(answerUid)) {
            answerUid = ShortUid.getUid();
        }
        String streamAnswerId = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, key);
        finishParam.setAnswerUid(answerUid);
        finishParam.setStreamDataIndex(streamDataIndex);
        while (true) {
            if (streamDataIndex != 1) {
                waitTimeOut = AlgoBackendUtil.exactCommonStreamDataWaitTimeConfig(algoBackendDO);
            }
            if (System.currentTimeMillis() - waitStartTime > waitTimeOut) {
                if(params.getRepoInfo()!=null){
                    CHAT_LOGGER.warn("repo chat timeout,streamAnswerId:{}, waitTime:{}ms, sessionUid:{}, bizId:{}", streamAnswerId, System.currentTimeMillis() - waitStartTime, sessionUid, bizId);
                }else {
                    CHAT_LOGGER.warn("get stream data timeout, streamAnswerId:{}, waitTime:{}ms, sessionUid:{}, bizId:{}, model:{}", streamAnswerId, System.currentTimeMillis() - waitStartTime, sessionUid, bizId, algoBackendDO.getModel());
                }
                if (streamChat) {
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("streamChat so run stopStream : {}", streamAnswerId);
                    }
                    //stopStream重构
                    JSONObject stopStreamJson = new JSONObject();
                    stopStreamJson.put("finishReason",ResponseEnum.ANSWER_OUTPUT_TIME_OUT.name());
                    finishParam.setStreamJson(stopStreamJson);
                    finishParam.setStreamBuffer(streamBuffer);
                    finishParam.setStopStream(true);
                    processFinish(finishParam);
                    //stopStream(null, ResponseEnum.ANSWER_OUTPUT_TIME_OUT, null,  answerUid, null, null, chatStreamPartResponseConsumer, resultHandler);
                } else {
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("not streamChat so throw exception : {}", streamAnswerId);
                    }
                    algoModelHealthUtilService.costHealth(finishParam.getModelEnv(),
                            finishParam.getAlgoBackendDO().getModel(), ResponseEnum.ANSWER_OUTPUT_TIME_OUT.name());
                    throw new BizException(ResponseEnum.ANSWER_OUTPUT_TIME_OUT);
                }
                return;
            }
            String tbaseStreamStr = ChatUtils.safeTbaseLpop(noneSerializationCacheManager, streamAnswerId);

            String queueStreamStr = streamDataQueueUtilService.lpop(streamAnswerId);
            String streamStr = isQueue ? queueStreamStr : tbaseStreamStr;

            // 如果是使用队列存储 用户取消需要查看tbase中是否有finishReason
            if (isQueue && tbaseStreamStr != null) {
                LOGGER.info("tbaseStreamStr is not null, streamAnswerId:{}, streamDataIndex:{}, streamData:{}", streamAnswerId,
                        streamDataIndex, tbaseStreamStr);
                JSONObject tbaseStreamJson = JSON.parseObject(tbaseStreamStr);
                if (tbaseStreamJson.containsKey("finishReason")) {
                    finishParam.setStreamJson(tbaseStreamJson);
                    finishParam.setStreamBuffer(streamBuffer);
                    finishParam.setStopStream(true);
                    if(CollectionUtils.isNotEmpty(fileList)){
                        finishParam.getRunTimeInfo().put("fileList", fileList);
                    }
                    if(StringUtils.isNotBlank(originAnswer.toString())){
                        finishParam.getRunTimeInfo().put("originAnswerContent", originAnswer.toString());
                    }
                    OTHERS_LOGGER.info("repo chat info{},{},{}",finishParam.getRunTimeInfo().toJSONString(),originAnswer,JSON.toJSONString(fileList));
                    processFinish(finishParam);
                    return;
                }
            }

            if ((streamStr) == null) {
                try {
                    Thread.sleep(AlgoBackendUtil.exactStreamDataPollingStepConfig(algoBackendDO));
                } catch (InterruptedException e) {
                    LOGGER.warn("sleep failed", e);
                    continue;
                }
                continue;
            }
            waitStartTime = System.currentTimeMillis();
            OTHERS_LOGGER.info("get stream data from:{}, streamAnswerId:{}, streamDataIndex:{}, streamData:{}", isQueue ? "queue" : "tbase",
                    streamAnswerId, streamDataIndex, isQueue ? queueStreamStr : tbaseStreamStr);
            JSONObject streamJson = JSON.parseObject(streamStr);
            // 获取重写仓库问答流式的所需信息
            RepoInfoModel repoInfo = params.getRepoInfo();
            String repoHttpUrl = "";
            String branch = "";
            // 仓库问答中特殊流式数据包的type
            String repoChatStreamDataType = null;
            // 不走缓存的数据包不会被记录到数据库，但是会被刷新到servlet中
            boolean bufferStreamData = true;

            if(repoInfo!=null){
                String repoRef = String.format("%s/%s/%s", AppConstants.ANTCODE_GIT_DOMAIN, repoInfo.getRepoGroup(), repoInfo.getRepoName());
                repoHttpUrl = "https://" + repoRef;
                branch = AntCodeClient.defaultIfBlank(repoInfo.getBranch());
            }
            if(streamJson.containsKey("version")){
                // 处理仓库问答的流式协议
                if(REPO_CHAT_STREAM_VERSION.equalsIgnoreCase(streamJson.getString(STREAM_TYPE))){
                    Pair<String, Object> stringStringPair = rewriteRepoChat(streamJson, repoHttpUrl, branch);
                    if(stringStringPair!=null){
                        if(REPO_CHAT_STREAM_SEARCH_KEYWORD.equalsIgnoreCase(stringStringPair.getKey())){
                            finishParam.getRunTimeInfo().put("keywords", stringStringPair.getValue());
                        }else if(REPO_CHAT_STREAM_SEARCH_FILE_LIST.equalsIgnoreCase(stringStringPair.getKey())) {
                            fileList.addAll((List<String>)stringStringPair.getValue());
                        }else if(RepoChatStreamDataTypeEnum.ANSWER.name().equalsIgnoreCase(stringStringPair.getKey())){
                            originAnswer.append(stringStringPair.getValue());
                        }else if(REPO_CHAT_STREAM_REFRESH.equalsIgnoreCase(stringStringPair.getKey())){
                            finishParam.getRunTimeInfo().put("refreshAnswerContent", stringStringPair.getValue());
                            repoChatStreamDataType = REPO_CHAT_STREAM_REFRESH;
                            bufferStreamData = false;
                        }else if(REPO_CHAT_STREAM_RECOMMEND_QUESTION.equalsIgnoreCase(stringStringPair.getKey())){
                            finishParam.getRunTimeInfo().put("recommendQuestion", stringStringPair.getValue());
                            repoChatStreamDataType = REPO_CHAT_STREAM_RECOMMEND_QUESTION;
                            bufferStreamData = false;
                        }
                    }
                }
            }

            //结束为一个map
            String content = streamJson.getString("content");
            ChatFunctionCall functionCallDelta = streamJson.getObject("functionCall", ChatFunctionCall.class);
            finishParam.setFunctionCall(functionCallDelta);
            ChatMessage delta = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), content, null, functionCallDelta);
            if (bufferStreamData){
                streamBuffer.buffer(delta);
            }


            if(streamJson.containsKey("type")){
                //透出错误信息
                if(streamJson.getString("type").equals("closeOnFailed")){
                    //设置结束原因
                    String finishReason = streamJson.getString("finishReason");
                    streamJson.put("errorMsg", content);
                    if(repoInfo!=null&&!finishReason.equalsIgnoreCase(ResponseEnum.ANSWER_REPO_INDEX_BUILDING.name())&&!finishReason.equalsIgnoreCase(ResponseEnum.ANSWER_REPO_NOT_INDEX.name())){
                        CHAT_LOGGER.warn("repo chat error,streamAnswerId:{}, sessionUid:{},bizId{}, errorMsg:{}",streamAnswerId, sessionUid, bizId,content);
                    }else if (repoInfo!=null) {
                        CHAT_LOGGER.warn("repo chat block due to repo not index,streamAnswerId:{}, sessionUid:{},bizId{}, errorMsg:{}",streamAnswerId, sessionUid, bizId,content);
                    }
                    if(!streamBuffer.getTokenBuffer().isEmpty()){
                        streamBuffer.getTokenBuffer().remove(streamBuffer.getBufferSize()-1);
                    }
                    finishParam.setStreamJson(streamJson);
                    finishParam.setStreamBuffer(streamBuffer);
                    finishParam.setStopStream(false);
                    processFinish(finishParam);
                    return;
                }
            }

            if(streamJson.containsKey("finishReason")){
                finishParam.setStreamJson(streamJson);
                finishParam.setStreamBuffer(streamBuffer);
                finishParam.setStopStream(false);
                if(CollectionUtils.isNotEmpty(fileList)){
                    finishParam.getRunTimeInfo().put("fileList", fileList);
                }
                if(StringUtils.isNotBlank(originAnswer.toString())){
                    finishParam.getRunTimeInfo().put("originAnswerContent", originAnswer.toString());
                }
                OTHERS_LOGGER.info("repo chat info{},{},{}",finishParam.getRunTimeInfo().toJSONString(),originAnswer,JSON.toJSONString(fileList));
                processFinish(finishParam);
                return;
            } else {
                int bufferWindowLength = AlgoBackendUtil.getBufferWindowLength(algoBackendDO, streamDataIndex, params.getRepoInfo()!=null);

                int bufferSize = streamBuffer.getBufferSize();

                // 不走缓存的数据跳过缓存窗口检查
                if(bufferStreamData && bufferSize < bufferWindowLength) {
                    continue;
                }

                // functionCall的name取决于流失数据中的functionCall的name
                if(functionCallDelta!=null){
                    streamBuffer.flush();
                    ChatStreamPartResponse chatStreamPartResponse = ChatUtils.getChatStreamPart(answerUid, streamBuffer.getBufferContent(), functionCallDelta,null, null);
                    chatStreamPartResponseConsumer.accept(chatStreamPartResponse);
                }else{
                    // processBeforeEndData方法重构
                    CheckResultModel checkResult = checkService.getAnswerCheckResult(requestId, answerUid, streamDataIndex, copyMessages,
                            content, streamBuffer.getContent().toString() + streamBuffer.getBufferContent(), chatRequestExtData, false);

                    if (streamChat && !checkResult.isAllCheckRet()) {
                        finishParam.setStreamJson(streamJson);
                        finishParam.setStreamBuffer(streamBuffer);
                        finishParam.setStopStream(false);
                        finishParam.setNeedDelTBaseKey(null);
                        finishParam.setNeedGPTCache(false);
                        processFinish(finishParam);
                        return;
                    }else if (streamChat && checkResult.isAllCheckRet()) {
                        ChatStreamPartResponse chatStreamPartResponse = null;

                        if(bufferStreamData){
                            chatStreamPartResponse = ChatUtils.getChatStreamPart(answerUid, streamBuffer.getBufferContent(), null,null, null);
                        }else{
                            // 不走缓存的
                            chatStreamPartResponse = ChatUtils.getChatStreamPart(answerUid, delta.getContent(), null,null, null);
                        }

                        if (repoChatStreamDataType!=null) {
                            chatStreamPartResponse.setType(repoChatStreamDataType);
                        }
                        if (streamJson.containsKey("traceId")){
                            chatStreamPartResponse.setTraceId(streamJson.getString("traceId"));
                        }
                        chatStreamPartResponseConsumer.accept(chatStreamPartResponse);
                        streamBuffer.flush();
                    }else{
                        streamBuffer.flush();
                    }
                }
                streamDataIndex++;
            }

        }
    }

    /**
     * 处理结束逻辑：
     * 1， 发送消息给调用方
     * 2， 将对话写入数据库
     * 3， 结束流式传输
     * @param finishParam 结束逻辑所需参数
     */
    public void processFinish(FinishParam finishParam) {
        CHAT_LOGGER.info("process finish, requestId:{}, streamDataIndex:{}, streamData:{}, buffer content: {}", finishParam.getRequestId(), finishParam.getStreamDataIndex(), finishParam.getStreamJson().toJSONString(), finishParam.getStreamBuffer().getBufferContent());

        // 统一收口流式的记录和分析， 无业务逻辑
        resultCheckRecord(finishParam);
        String finishReason = finishParam.getStreamJson().getString("finishReason");
        boolean isUserCanceled = ResponseEnum.USER_CANCELED.name().equalsIgnoreCase(finishReason);
        if(isUserCanceled || finishParam.isStopStream()) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("start to cancel AIGC answer, {}", finishParam.getKey());
            }
            // 结束流式传输的处理
            // stopStream(requestCheckResultModel, ResponseEnum.USER_CANCELED, functionCall, answerUid, streamBuffer.getContent().toString(), null, chatStreamPartResponseConsumer, resultHandler);
            // stopStream重构
            String answerFlush = isUserCanceled ? null : getCheckFailedMsg(finishParam.getRequestCheckResultModel());
            Pair<ChatStreamPartResponse, StreamResponseModel> stopStreamResult = ChatUtils.getStopStreamResponse(finishParam.getRequestCheckResultModel(), ResponseEnum.getByName(finishReason), finishParam.getFunctionCall(), finishParam.getAnswerUid(), finishParam.getStreamBuffer().getContent().toString(), answerFlush);

            if (finishParam.getChatStreamPartResponseConsumer() != null) {
                // 返回结果给调用方
                finishParam.getChatStreamPartResponseConsumer().accept(stopStreamResult.getKey());
            }

            if (finishParam.getResultHandler() != null) {
                // 将对话存入数据库
                finishParam.getResultHandler().accept(stopStreamResult.getValue());
            }
            return;
        }

        String thisContent = finishParam.getStreamBuffer().getBufferContent();
        String argumentBufferContent = finishParam.getStreamBuffer().getFunctionCall().getArgumentBufferContent();
        // functionCall的name取决于流失数据中的functionCall的name
        ChatFunctionCall chatFunctionCallBuffer = null;
        if(finishParam.getStreamBuffer().getFunctionCall() != null){
            chatFunctionCallBuffer = finishParam.getStreamBuffer().getFunctionCall().toChatFunctionCall();
        }
        StreamResponseModel streamResponseModel = new StreamResponseModel();
        ChatStreamPartResponse chatStreamPartResponse = ChatUtils.getChatStreamPart(finishParam.getAnswerUid(), thisContent, chatFunctionCallBuffer,null, null);
        chatStreamPartResponse.getChoices().get(0).setFinishReason(finishReason);

        finishParam.getStreamBuffer().flush();

        if (hasCheckFailed(finishParam.getRequestCheckResultModel())) {
            chatStreamPartResponse.getChoices().get(0).setCheckResultModel(finishParam.getRequestCheckResultModel());
            chatStreamPartResponse.setClear(true);
            // 审核不通过，设置置空content标志位,errorMsg存入安全审核不通过信息
            streamResponseModel.setClear(true);
            streamResponseModel.setErrorMsg(getCheckFailedMsg(finishParam.getRequestCheckResultModel()));
        }

        CheckResultModel checkResult = checkService.getAnswerCheckResult(finishParam.getRequestId(), finishParam.getAnswerUid(), finishParam.getStreamDataIndex(), finishParam.getCopyMessages(),
                thisContent, finishParam.getStreamBuffer().getContent().toString() + finishParam.getStreamBuffer().getBufferContent(), finishParam.getChatRequestExtData(),false);
        if (!checkResult.isAllCheckRet()) {
            chatStreamPartResponse = ChatUtils.getChatStreamPart(finishParam.getAnswerUid(),getCheckFailedMsg(checkResult), finishParam.getFunctionCall(), checkResult.getResponseEnum(), checkResult);
            chatStreamPartResponse.setClear(true);
            // 审核不通过，设置置空content标志位,errorMsg存入安全审核不通过信息
            streamResponseModel.setClear(true);
            streamResponseModel.setErrorMsg(getCheckFailedMsg(checkResult));
            if (!finishParam.isStreamChat()) {
                throw new BizException(ResponseEnum.CHECK_FAILED);
            }
        }
        CHAT_LOGGER.info("completion processFinish,model:{},requestId:{},user:{} result:{}", finishParam.getAlgoBackendDO().getModel(), finishParam.getRequestId(), finishParam.getUser(), JSON.toJSONString(finishParam.getStreamBuffer().getContent()));
        streamResponseModel.setAnswerUid(finishParam.getAnswerUid());
        // 将content中的错误信息转移到errorMsg中
        streamResponseModel.setAnswerMessage(ChatUtils.streamBufferToChatMessage(finishParam.getStreamBuffer()));
        // 设置runTimeInfo
        if(finishParam.getRunTimeInfo() != null){
            streamResponseModel.setRuntimeInfo(finishParam.getRunTimeInfo().toJSONString());
        }
        if(finishParam.getStreamJson().containsKey("errorMsg")){
            chatStreamPartResponse.getChoices().get(0).getDelta().setContent(finishParam.getStreamJson().getString("errorMsg"));
            streamResponseModel.setErrorMsg(finishParam.getStreamJson().getString("errorMsg"));
            streamResponseModel.getAnswerMessage().setContent(finishParam.getStreamBuffer().getContent().toString());
        }else {
            if (finishParam.isNeedGPTCache()) {
                writeGPTCache(finishParam.getStreamJson(), finishParam.getCopyMessages(), finishParam.getStreamBuffer(), finishParam.getAlgoBackendDO(), finishParam.getRequestId());
                streamResponseModel.setCached(finishParam.getStreamJson().getBoolean("cached"));
                streamResponseModel.setHitQuery(finishParam.getStreamJson().getString("hitQuery"));
            }
        }
        if (finishParam.isStreamChat()) {
            if(finishParam.getChatStreamPartResponseConsumer() != null){
                finishParam.getChatStreamPartResponseConsumer().accept(chatStreamPartResponse);
            }
        }

        if (hasCheckFailed(finishParam.getRequestCheckResultModel())) {
            streamResponseModel.setCheckResultModel(finishParam.getRequestCheckResultModel());
        }
        if (hasCheckFailed(checkResult)) {
            streamResponseModel.setCheckResultModel(checkResult);
        }
        CHAT_LOGGER.info("streamResponseModel is {}",JSON.toJSONString(streamResponseModel));
        if (finishParam.getResultHandler() != null) {
            finishParam.getResultHandler().accept(streamResponseModel);
        }
        if (finishParam.getNeedDelTBaseKey() != null) {
            finishParam.getNeedDelTBaseKey().accept(finishParam.getKey());
        }

        if (!finishParam.isStreamChat() && finishParam.getStreamJson().containsKey("errorMsg")) {
            //非流式报错信息透出
            LOGGER.warn("biz exception openapi call for {}, finishReason {}", finishParam.getStreamJson().getString("errorMsg"), finishReason);
            throw new BizException(ResponseEnum.getByName(finishReason), finishParam.getStreamJson().getString("errorMsg"));
        }
    }

    /**
     * 记录算法健康状态
     *
     * @param finishParam 完成参数对象
     */
    private void resultCheckRecord(FinishParam finishParam) {
        String finishReason = finishParam.getStreamJson().getString("finishReason");
        String requestId = finishParam.getRequestId();
        if(StringUtils.isBlank(finishReason)) {
            LOGGER.warn("finishReason is empty, {}", requestId);
            return;
        }
        if(LOGGER.isInfoEnabled()){
            LOGGER.info("finishReason is {}, {}", finishReason, requestId);
        }

        // 特殊逻辑， 针对stop做success转换
        ResponseEnum re = null;
        if(finishReason.equalsIgnoreCase("stop")) {
            re = ResponseEnum.SUCCESS;
        } else {
            re = ResponseEnum.getByName(finishReason);
        }

        if(re.equals(ResponseEnum.SUCCESS) ) {
            // 调用算法健康工具服务的重置健康方法
            algoModelHealthUtilService.resetHealth(finishParam.getModelEnv(), finishParam.getAlgoBackendDO().getModel(), false);
        } else {
            // 调用算法健康工具服务的消耗健康方法
            algoModelHealthUtilService.costHealth(finishParam.getModelEnv(), finishParam.getAlgoBackendDO().getModel(), finishReason);
        }
    }


    /**
     * 回答写入gptCache
     * 1、只缓存maya返回的数据
     * 2、只缓存首轮
     * 3、gptCache必须开，而且流量因子达标，没有熔断
     */
    private void writeGPTCache(JSONObject streamJson, List<ChatMessage> copyMessages, ChatStreamBuffer streamBuffer, AlgoBackendDO algoBackendDO, String requestId) {
        try {
            long promptSize = copyMessages
                    .stream()
                    .filter(e -> e!=null && e.getRole()!=null && !ChatRoleEnum.SYSTEM.getName().equalsIgnoreCase(e.getRole()))
                    .count();
            //1、只缓存maya返回的数据，cache中取回的就不要回写了
            if (streamJson.containsKey("cached")) {
                return;
            }

            //2、只缓存首轮的数据
            if (promptSize != 1) {
                return;
            }

            //3、开关必须打开
            if (!gptCacheService.isEnableCache()) {
                return;
            }

            String answer = streamBuffer.getContent().toString();
            String model = algoBackendDO.getModel();
            appThreadPool.execute(() -> {
                CHAT_LOGGER.info("putCacheResp cache requestId {} model {}, query {}, realAnswer {}", requestId, model, JSON.toJSONString(copyMessages), answer);
                boolean result = gptCacheService.putCache(model, copyMessages, answer, requestId);
                CHAT_LOGGER.info("putCacheResp cache result requestId {}  writeGPTCache result {}", requestId, result);
            });
        } catch (Exception e) {
            CHAT_LOGGER.info("code completion writeGPTCache exception ", e);
        }
    }

    /**
     * 流式请求结束
     * @param answerUid 答案uid
     * @param chatStreamBuffer 答案的缓冲器
     * @param httpServletResponse 给调用方写入数据的HttpServlet
     * @param resultHandler 是否落库
     * @throws IOException
     */
    public static void onChatComplete(String answerUid, ChatStreamBuffer chatStreamBuffer, HttpServletResponse httpServletResponse, Consumer<StreamResponseModel> resultHandler) throws IOException {
        // 处理请求
        if (resultHandler != null) {
            StreamResponseModel streamResponseModel = new StreamResponseModel();
            streamResponseModel.setAnswerUid(answerUid);
            streamResponseModel.setAnswerMessage(ChatUtils.streamBufferToChatMessage(chatStreamBuffer));
            streamResponseModel.setCheckResultModel(chatStreamBuffer.getCheckResultModel());
            resultHandler.accept(streamResponseModel);
        }
        httpServletResponse.getWriter().close();
    }

    /**
     * 设置默认的systemPrompt
     * @param chatCompletionRequest 请求参数
     * @param algoBackendDO 算法模型
     */
    public static void updateDefaultSystemPrompt(ChatCompletionRequest chatCompletionRequest, AlgoBackendDO algoBackendDO) {
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        String defaultSystemPrompt = AlgoBackendUtil.exactSystemPromptConfig(algoBackendDO);
        // 默认值是空的不需要设置SystemPrompt
        if (StringUtils.isEmpty(defaultSystemPrompt)) {
            return;
        }
        // 如果有system,但是system的content是空的,则设置默认的content
        if (messages.get(0).getRole().equalsIgnoreCase(ChatRoleEnum.SYSTEM.getName())) {
            if(StringUtils.isEmpty(messages.get(0).getContent())){
                messages.get(0).setContent(AlgoBackendUtil.exactSystemPromptConfig(algoBackendDO));
            }
        } else {
            // 如果没有system则添加新的system到最开始
            ChatMessage system = new ChatMessage();
            system.setRole(ChatRoleEnum.SYSTEM.getName());
            system.setContent(defaultSystemPrompt);
            chatCompletionRequest.getMessages().add(0, system);
        }
    }

    /**
     * 将drm中的chatGPTRequest配置 copy到chatRequest里
     * drm中 use 判断是否使用drm配置
     * stream 属性忽略 需要代码里配置
     *
     * @param chatRequest
     * @param algoBackendDO
     */
    public void applyChatGPTDefaultValues(ChatCompletionRequest chatRequest, AlgoBackendDO algoBackendDO) {

        if (chatRequest.getFrequencyPenalty() == null) {
            chatRequest.setFrequencyPenalty(AlgoBackendUtil.exactFrequencyPenaltyConfig(algoBackendDO));
        }

        if (chatRequest.getPresencePenalty() == null) {
            chatRequest.setPresencePenalty(AlgoBackendUtil.exactPresencePenaltyConfig(algoBackendDO));
        }

        if (chatRequest.getN() == null) {
            chatRequest.setN(AlgoBackendUtil.exactNConfig(algoBackendDO));
        }

        if (chatRequest.getTemperature() == null && AlgoBackendUtil.exactTemperatureConfig(algoBackendDO) != null) {
            chatRequest.setTemperature(AlgoBackendUtil.exactTemperatureConfig(algoBackendDO).doubleValue());
        }

        if (chatRequest.getTopP() == null && AlgoBackendUtil.exactTopPConfig(algoBackendDO) != null) {
            chatRequest.setTopP(AlgoBackendUtil.exactTopPConfig(algoBackendDO).doubleValue());
        }

        // openai的system不能为null
        if (chatRequest.getMessages().get(0).getContent() == null) {
            chatRequest.getMessages().get(0).setContent("");
        }
    }

    /**
     * 判断一次问答时是否被用户取消
     * @param key tbase中用来标注取消输出流的key
     */
    public boolean isUserCanceled(String key) {

        boolean isUserCanceled = false;
        BytesObject streamData = (BytesObject)noneSerializationCacheManager.lpop(key);
        if (streamData == null) {
            return isUserCanceled;
        }

        String streamStr = null;
        try {
            streamStr = StringUtils.toEncodedString(streamData.getBytes(), StandardCharsets.UTF_8);
        } catch (Exception ex) {
            LOGGER.warn("tbase data transferred failed, {}, {}", key, ex);
        }

        if(StringUtils.isEmpty(streamStr)) {
            return isUserCanceled;
        }

        JSONObject streamJson = JSON.parseObject(streamStr);

        if(null == streamJson || !streamJson.containsKey("finishReason")) {
            return isUserCanceled;
        }

        if(!"userCanceled".equalsIgnoreCase(streamJson.getString("finishReason"))) {
            return isUserCanceled;
        }
        return true;
    }


    /**
     * 判断是否需要关闭流
     * @param streamInputId tbase中用来标注取消输出的key
     */
    public boolean needCloseInputStream(String streamInputId) {

        try{
            CasValue<Serializable> s = noneSerializationCacheManager.seget(streamInputId);
            if(null != s) {
                BytesObject tmp = (BytesObject) s.getValue();
                String streamStr = StringUtils.toEncodedString(tmp.getBytes(), StandardCharsets.UTF_8);
                if (LOGGER.isInfoEnabled()) {
                    LOGGER.info("found {} tbase is delete, and value : {}, value : {}, transfored :{} ", streamInputId, s, s.getValue().toString(), streamStr);
                }
                if (AppConstants.INPUT_CLOSE_TAG.equalsIgnoreCase(streamStr)) {
                    if (LOGGER.isInfoEnabled()) {
                        LOGGER.info("found {} tbase is delete, so close websocket", streamInputId);
                    }
                    return true;
                }
            }
        }catch (Exception e){
            LOGGER.error("check stream cancel from tbase error", e);
        }

        return false;
    }

    /**
     * 有任何一个维度审核失败就返回true
     * @param checkResultModel
     * @return
     */
    public String getCheckFailedMsg(CheckResultModel checkResultModel) {
        if (checkResultModel == null || checkResultModel.isAllCheckRet()) {
            return null;
        }
        JSONObject codeFuseCheckFailedMsg = JSONObject.parseObject(codeGPTDrmConfig.getCodeFuseCheckFailedMsg());
        ReviewPlatformEnum reviewPlatformEnum = null;
        for (Map.Entry<ReviewPlatformEnum, ReviewResultModel> entry : checkResultModel.getResultModelMap().entrySet()) {
            if (entry.getValue() != null && !entry.getValue().isRet()) {
                reviewPlatformEnum = entry.getKey();
                break;
            }
        }
        String checkFailedMsg;
        JSONObject roleFailedMsg = codeFuseCheckFailedMsg.getJSONObject(checkResultModel.getChatRoleEnum().name());
        if (reviewPlatformEnum == null || !roleFailedMsg.containsKey(reviewPlatformEnum.name())) {
            checkFailedMsg = roleFailedMsg.getString("DEFAULT");
        } else {
            checkFailedMsg = roleFailedMsg.getString(reviewPlatformEnum.name());
        }
        return checkFailedMsg;
    }

    /**
     * 有任何一个维度审核失败就返回true
     * @param checkResultModel
     * @return
     */
    public boolean hasCheckFailed(CheckResultModel checkResultModel) {
        if (checkResultModel == null) {
            return false;
        }
        if (!checkResultModel.isAllCheckRet()) {
            return true;
        }
        if (MapUtils.isEmpty(checkResultModel.getResultModelMap())) {
            return false;
        }
        for (ReviewResultModel resultModel : checkResultModel.getResultModelMap().values()) {
            if (resultModel != null && !resultModel.isRet()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 复制一个GptAlgModelServiceRequest对象,并且新的对象中的servletResponse设置为空
     * 这是一个浅拷贝,不会copy对象里面的对象。
     * @param param
     * @return
     */
    public static GptAlgModelServiceRequest copyParamWithoutServletResponse(GptAlgModelServiceRequest param){
        GptAlgModelServiceRequest requestParamWithoutServletResponse = new GptAlgModelServiceRequest();
        BeanUtils.copyProperties(param, requestParamWithoutServletResponse);
        requestParamWithoutServletResponse.setChatStreamPartResponseHandler(null);
        return requestParamWithoutServletResponse;
    }

    /**
     * 问题审核失败的时候进行的处理
     * @param requestCheckResultModel 问题审查结果
     * @param chatStreamPartResponseConsumer 用于前端交互的处理器
     * @param resultHandler 用于落库的处理器
     */
    public void processWhenQuestionFailCheck(CheckResultModel requestCheckResultModel, Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer, Consumer<StreamResponseModel> resultHandler){
        // stopStream重构
        FinishParam finishParam = new FinishParam();
        finishParam.setRequestCheckResultModel(requestCheckResultModel);
        finishParam.setChatStreamPartResponseConsumer(chatStreamPartResponseConsumer);
        finishParam.setResultHandler(resultHandler);
        finishParam.setStreamBuffer(new ChatStreamBuffer());
        finishParam.setStreamJson(new JSONObject());
        finishParam.setStreamDataIndex(0);
        finishParam.setNeedGPTCache(false);
        finishParam.setStopStream(true);
        processFinish(finishParam);
        //stopStream(requestCheckResultModel, null, null,null, null, getCheckFailedMsg(requestCheckResultModel), chatStreamPartResponseConsumer, resultHandler);
    }


    /**
     * 从gptCache中获取缓存
     * 1、开关必须打开，没有熔断，而且流量因子达标
     * 2、必须为首轮
     * 如果命中后就往TBase中模拟流式写入
     */
    public GptCacheResponse getGPTCache(AlgoBackendDO defaultAlgoConfig, GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        try {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("try getCache, requestId: {} ", gptAlgModelServiceRequest.getRequestId());
            }
            //1、开关必须为开
            if (!gptCacheService.isEnableCache()) {
                if (LOGGER.isInfoEnabled()) {
                    LOGGER.info("gpt cache disabled, requestId: {} ", gptAlgModelServiceRequest.getRequestId());
                }
                return null;
            }

            //2、非首轮，结束
            if (!gptCacheService.isFirstRound(gptAlgModelServiceRequest)) {
                if (LOGGER.isInfoEnabled()) {
                    LOGGER.info("gpt cache not necessary, not first round, requestId {}", gptAlgModelServiceRequest.getRequestId());
                }
                return null;
            }

            GptCacheResponse cacheResponse = gptCacheService.getCache(defaultAlgoConfig, gptAlgModelServiceRequest,
                    gptAlgModelServiceRequest.getRequestId());

            //3、未命中，结束
            if (cacheResponse == null || TextUtils.isEmpty(cacheResponse.getAnswer())) {
                if (LOGGER.isInfoEnabled()) {
                    LOGGER.info("getCache status: missed, requestId {} ", gptAlgModelServiceRequest.getRequestId());
                }
                return null;
            }
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("getCache status: hit, requestId: {}, cacheResponse: {}", gptAlgModelServiceRequest.getRequestId(),
                        JSON.toJSONString(cacheResponse));
            }

            return cacheResponse;
        } catch (Exception e) {
            LOGGER.error("getGPTCache exception skip cache, requestId: {}", gptAlgModelServiceRequest.getRequestId(), e);
        }

        return null;
    }

    /**
     * 将拿到的cache想TBase里push
     *
     * @param cacheResponse             gptCache返回的cache
     * @param gptAlgModelServiceRequest 模型服务请求
     */
    public void pushToTBase(GptCacheResponse cacheResponse, GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        //1、去往TBase中写，3个字符写一次
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("getCache and cacheHit {} hitQuery {}", cacheResponse.getAnswer(), cacheResponse.getHitQuery());
        }
        //所有写业务流的tbasekey(非openApi)：stream_answer_"sessionId"_"queryIndex"_"generateIndex"
        String key = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, gptAlgModelServiceRequest.getUniqueAnswerId());
        Iterable<String> chunks = Splitter.fixedLength(3).split(cacheResponse.getAnswer());
        chunks.forEach(str -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("content", str);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("cache push to key{},  TBase {}", key, jsonObject.toJSONString());
            }

            BytesObject bytesObject = new BytesObject(jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8));
            noneSerializationCacheManager.rpush(key, bytesObject);
        });

        //2、写结束字符
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("finishReason", "stop");
        jsonObject.put("cached", true);
        jsonObject.put("hitQuery", cacheResponse.getHitQuery());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("cache push to TBase {}", jsonObject);
        }
        noneSerializationCacheManager.rpush(key, new BytesObject(jsonObject.toString().getBytes(StandardCharsets.UTF_8)));
        noneSerializationCacheManager.expire(key, 12 * 60 * 60);
    }

    /**
     * 将拿到的cache放入队列
     *
     * @param cacheResponse             gptCache返回的cache
     * @param gptAlgModelServiceRequest 模型服务请求
     */
    public void pushToQueue(GptCacheResponse cacheResponse, GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("getCache and cacheHit {} hitQuery {}", cacheResponse.getAnswer(), cacheResponse.getHitQuery());
        }
        String key = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, gptAlgModelServiceRequest.getUniqueAnswerId());
        Iterable<String> chunks = Splitter.fixedLength(3).split(cacheResponse.getAnswer());
        chunks.forEach(str -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("content", str);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("cache push to key{},  TBase {}", key, jsonObject.toJSONString());
            }
            BytesObject bytesObject = new BytesObject(jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8));
            streamDataQueueUtilService.rpush(key, bytesObject);
        });
        //2、写结束字符
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("finishReason", "stop");
        jsonObject.put("cached", true);
        jsonObject.put("hitQuery", cacheResponse.getHitQuery());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("cache push to TBase {}", jsonObject);
        }
        streamDataQueueUtilService.rpush(key, new BytesObject(jsonObject.toString().getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * 获取模型的审核相关扩展字段
     * @param sessionUid
     * @param modelName
     * @return
     */
    public ChatRequestExtData getCodeFuseChatRequestExtData(String empId, String sessionUid, String modelName) {
        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
        JSONObject codeFuseCheckSwitch = JSONObject.parseObject(codeGPTDrmConfig.getCodeFuseCheckSwitch());
        // 配置里面没有配置的模型默认需要走审核
        // 内网默认不走意图识别
        boolean intentionSwitch = !codeGPTDrmConfig.isIntranetApplication();
        boolean infoSecSwitch = true;
        boolean keymapSwitch = false;
        boolean antDsrSwitch = !codeGPTDrmConfig.isIntranetApplication();
        boolean rcSmartSwitch = !codeGPTDrmConfig.isIntranetApplication();
        if (codeFuseCheckSwitch.containsKey(modelName.toUpperCase())) {
            JSONObject modelCheckSwitch = codeFuseCheckSwitch.getJSONObject(modelName.toUpperCase());
            infoSecSwitch = modelCheckSwitch.getBoolean(ReviewPlatformEnum.INFOSEC.name());
            keymapSwitch = modelCheckSwitch.getBoolean(ReviewPlatformEnum.KEYMAP.name());
            antDsrSwitch = modelCheckSwitch.getBoolean(ReviewPlatformEnum.ANTDSR.name());
            intentionSwitch = modelCheckSwitch.getBoolean(ReviewPlatformEnum.INTENTION.name());
            rcSmartSwitch = modelCheckSwitch.getBoolean(ReviewPlatformEnum.RCSMART.name());
        }
        chatRequestExtData.setSessionUid(sessionUid);
        chatRequestExtData.setEmpId(empId);
        chatRequestExtData.setCodeGPTUser(AppConstants.CODEGPT_TOKEN_USER);
        chatRequestExtData.setInfoSecCheck(infoSecSwitch);
        chatRequestExtData.setKeymapCheck(keymapSwitch);
        chatRequestExtData.setAntDsrCheck(antDsrSwitch);
        chatRequestExtData.setIntentionCheck(intentionSwitch);
        chatRequestExtData.setRcSmartCheck(rcSmartSwitch);
        return chatRequestExtData;
    }

    /**
     * 更新审核开关
     * @param chatCompletionRequest
     * @param modeName
     */
    public void updateCheckSwitch(ChatCompletionRequest chatCompletionRequest, ChatSessionDO sessionDO, String modeName) {
        if (chatCompletionRequest == null || chatCompletionRequest.getChatRequestExtData() == null || sessionDO == null) {
            return;
        }
        ChatRequestExtData chatRequestExtData = getCodeFuseChatRequestExtData(chatCompletionRequest.getChatRequestExtData().getEmpId(), sessionDO.getUid(), modeName);
        chatCompletionRequest.setChatRequestExtData(chatRequestExtData);
    }

    /**
     * 获取会话上下文配置
     * @param queryParams
     * @return
     */
    public JSONObject getSessionContext(PluginServiceRequestContext queryParams){
        ChatSessionDO chatSessionDO = queryParams.getChatSessionDO();
        String extInfo = chatSessionDO != null ? queryParams.getChatSessionDO().getExtInfo() : null;
        JSONObject result = new JSONObject();
        if(StringUtils.isNotBlank(extInfo)){
            result = JSON.parseObject(extInfo);
        }

        result.put("messages", queryParams.getChatCompletionRequest().getMessages());
        result.put("query", ToolLearningUtil.getSimpleQuery(queryParams.getChatCompletionRequest().getMessages()));
        result.put("sessionUid", chatSessionDO != null ? chatSessionDO.getUid() : null);
        result.put("empId", queryParams.getUserAuthDO().getEmpId());
        return result;
    }

    /**
     * CodeGPTHandler将错误信息存入tbase以便透出
     * <AUTHOR>
     * @since 2023.12.20
     * @param gptAlgModelServiceRequestList gptAlgModelServiceRequestList
     * @param errorMsg errorMsg
     *
     */
    public void pushErrorMsgToTBase(List<GptAlgModelServiceRequest> gptAlgModelServiceRequestList, String errorMsg, ResponseEnum responseEnum){
        if(gptAlgModelServiceRequestList.isEmpty()){
            LOGGER.info("push ErrorMsg To TBase failed for can not find the unique answerId");
            return;
        }
        for (GptAlgModelServiceRequest gptAlgModelServiceRequest : gptAlgModelServiceRequestList) {
            handleEveryStreamError(errorMsg,noneSerializationCacheManager,gptAlgModelServiceRequest.getUniqueAnswerId(), responseEnum);
            if(null == gptAlgModelServiceRequest.getAlgoBackendDO() || StringUtils.isBlank(gptAlgModelServiceRequest.getAlgoBackendDO().getModel())) {
                LOGGER.warn("invalid model info");
            }
        }
    }



    /**
     * 把流式数据放入到队列中
     *
     * @param delta
     * @param finishReason
     * @param uniqueAnswerId
     */
    public void memoryQueueStreamData(ChatMessage delta, String finishReason, String uniqueAnswerId) {
        JSONObject contentJson = new JSONObject();
        if (delta == null) {
            contentJson.put("content", null);
        }
        else {
            contentJson.put("content", delta.getContent());
            if (delta.getFunctionCall() != null) {
                contentJson.put("functionCall", delta.getFunctionCall());
            }
        }
        if (StringUtils.isNotBlank(finishReason)) {
            contentJson.put("finishReason", finishReason);
        }
        BytesObject bytesObject = new BytesObject(contentJson.toJSONString().getBytes(StandardCharsets.UTF_8));
        String streamAnswerId = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, uniqueAnswerId);
        streamDataQueueUtilService.rpush(streamAnswerId, bytesObject);
    }
    /**
     * 流式错误放入队列
     *
     * @param errorMsg       errorMsg
     * @param uniqueAnswerId uniqueAnswerId
     * <AUTHOR>
     * @since 2024.01.30
     */
    public void memoryQueueStreamError(String errorMsg, String uniqueAnswerId, ResponseEnum responseEnum) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content", errorMsg);
        jsonObject.put("type", "closeOnFailed");
        jsonObject.put("finishReason", responseEnum == null ? ResponseEnum.ERROR_THROW.name() : responseEnum.name());
        BytesObject bytesObject = new BytesObject(jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8));
        String streamAnswerId = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, uniqueAnswerId);
        streamDataQueueUtilService.rpush(streamAnswerId, bytesObject);
    }

    /**
     * 流式错误统一透出
     * <AUTHOR>
     * @since 2024.01.30
     * @param errorMsg errorMsg
     * @param tbaseCacheManager tbaseCacheManager
     * @param uniqueAnswerId uniqueAnswerId
     */
    public void handleEveryStreamError(String errorMsg, RefreshableCommonTbaseCacheManager tbaseCacheManager, String uniqueAnswerId, ResponseEnum responseEnum){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content", errorMsg);
        jsonObject.put("type", "closeOnFailed");
        jsonObject.put("finishReason", responseEnum == null?ResponseEnum.ERROR_THROW.name():responseEnum.name());
        BytesObject bytesObject = new BytesObject(jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8));
        String streamAnswerId = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, uniqueAnswerId);
        tbaseCacheManager.rpush(streamAnswerId, bytesObject);
        tbaseCacheManager.expire(streamAnswerId,12 * 60 * 60);
    }

    public CodegptRequestBean beforeModelRequest(ChatCompletionRequest chatRequestBean, AlgoBackendDO algoBackendDO, String uniqueAnswerId){
        return beforeModelRequest(chatRequestBean, algoBackendDO, uniqueAnswerId, true);
    }


    public CodegptRequestBean beforeModelRequest(ChatCompletionRequest chatRequestBean, AlgoBackendDO algoBackendDO, String uniqueAnswerId, boolean overwriteContentForFunctionCall) {
        CodegptRequestBean codegptRequestBean = new CodegptRequestBean(chatRequestBean, algoBackendDO, uniqueAnswerId);

        Object prompt = codegptRequestBean.getPrompt();
        if(prompt instanceof List){ //对话模式
            //修改role字段，兼容模型
            List<ChatMessage> messageList = (List<ChatMessage>)prompt;
            for (ChatMessage chatMessage : messageList) {
                if (chatMessage.getRole().equalsIgnoreCase(ChatRoleEnum.USER.getName())) {
                    chatMessage.setRole(AppConstants.HUMAN);
                } else if (chatMessage.getRole().equalsIgnoreCase(ChatRoleEnum.ASSISTANT.getName())) {
                    chatMessage.setRole(AppConstants.BOT);
                    // 添加兜底逻辑，如果content字段不存在或null，算法侧会解析失败
                    if (chatMessage.getContent() == null) {
                        chatMessage.setContent("");
                    }
                    if(chatMessage.getFunctionCall()!=null && overwriteContentForFunctionCall){
                        Map<String, Object> functionCallContentInfo = new LinkedHashMap<>();
                        functionCallContentInfo.put("content", chatMessage.getContent());
                        functionCallContentInfo.put("name", chatMessage.getFunctionCall().getName());
                        functionCallContentInfo.put("arguments", chatMessage.getFunctionCall().getArguments());
                        chatMessage.setContent(AppConstants.DEFAULT_CODEGPT_FUNCTION_CALL_START_TOKEN + JSON.toJSONString(functionCallContentInfo, SerializerFeature.WRITE_MAP_NULL_FEATURES));
                    }
                } else if (chatMessage.getRole().equalsIgnoreCase(ChatRoleEnum.SYSTEM.getName())) {
                    chatMessage.setRole(AppConstants.SYSTEM);
                } else if(ChatRoleEnum.FUNCTION.getName().equalsIgnoreCase(chatMessage.getRole())){
                    chatMessage.setRole(AppConstants.FUNCTION);
                    Map<String, Object> functionCallContentInfo = new HashMap<>();
                    functionCallContentInfo.put("name", chatMessage.getName());
                    functionCallContentInfo.put("content", chatMessage.getContent());
                    chatMessage.setContent(JSON.toJSONString(functionCallContentInfo));
                }
            }

            ChatMessage userChatMessage = messageList.get(messageList.size()-1);
            if(codeGPTDrmConfig.isDocSearchResultAddToPrompt() && !CollectionUtils.isEmpty(chatRequestBean.getDocs()) && !algoBackendDO.getModel().equalsIgnoreCase("TOOL_GPT3")){
                userChatMessage.setContent(addDocInfoToPrompt(chatRequestBean.getDocs(), userChatMessage.getContent()));
                codegptRequestBean.setDocs(Collections.emptyList());
            }
        }

        truncationPrompt(codegptRequestBean, algoBackendDO);
        return codegptRequestBean;
    }

    /**
     * 把doc搜索结果加到prompt中去
     * @param docSearchResultList
     * @param query
     * @return
     */
    public String addDocInfoToPrompt(List<DocSearchResultItem> docSearchResultList, String query){
        LOGGER.info("addDocInfoToPrompt docSearchResultList size:{}", docSearchResultList.size());
        StringBuilder sb = new StringBuilder();
        sb.append("查询结果如下:\n");
        for(DocSearchResultItem docSearchResultItem : docSearchResultList){
            sb.append(String.format("Source 〔%s〕\n", docSearchResultItem.getId()))
                    .append(docSearchResultItem.getSegment())
                    .append("\n");
        }
        sb.append("\n说明: 使用提供的搜索结果，对给定的问题写一个全面的回复。\n")
                .append("搜索结果如果和问题不相关，则不用参考\n")
                .append("确保在引用上下文之后使用 〔〕这种特殊的括号引用对应的Source\n")
                .append("将代码部分放在```code```中。使用语言 zh-CN 回答。\n")
                .append("问题:")
                .append(query);
        return sb.toString();
    }

    public String afterModelResponse(String response, AlgoBackendDO algoBackendDO) {
        String generatedCode;
        Optional<JSONObject> responseJson = Optional.of(JSON.parseObject(response));
        if (MAYA.equals(AlgoBackendUtil.exactRequestModelModel(algoBackendDO))) {
            generatedCode = responseJson.map(retJson -> retJson.getJSONArray("generated_code").getJSONArray(0).getString(0)).orElse(null);
        } else {
            generatedCode = responseJson.map(retJson -> retJson.getJSONObject("result")).map(retJson -> retJson.getJSONObject("output"))
                    .map(cur -> cur.getString("generated_code")).orElse(null);
        }
        if (generatedCode == null) {
            LOGGER.warn("generated_code is null, {}", algoBackendDO.getModel());
            throw new BizException(AI_CALL_ERROR, "generated_code is null");
        }
        //临时处理，后续模型会修复
        if (generatedCode.contains("<human>: ")) {
            return generatedCode.split("<human>: ")[0];
        }
        if (generatedCode.contains("<bot>: ")) {
            return generatedCode.split("<bot>: ")[0];
        }
        return generatedCode;
    }

    public void truncationPrompt(CodegptRequestBean codeGeexRequestBean, AlgoBackendDO algoBackendDO) {

        Object prompt = codeGeexRequestBean.getPrompt();
        // 非对话模式不做处理
        if (!(prompt instanceof List)) {
            return;
        }
        // 对话模式进行裁剪prompt
        List<ChatMessage> promptMessageList = (List<ChatMessage>) prompt;

        //如果是单轮会话直接返回，不做截断处理
        if (promptMessageList.size() <= 2) {
            return;
        }
        ChatMessage systemMessage = null;
        if (AppConstants.SYSTEM.equalsIgnoreCase(promptMessageList.get(0).getRole())) {
            systemMessage = promptMessageList.get(0);
        }

        int offset = systemMessage == null ? 0 : 1;

        // 截取prompt,截取完之后确保chatMessagesCutList不含systemPrompt且长度<=maxRound
        List<ChatMessage> chatMessagesCutList = new ArrayList<>();
        if (promptMessageList.size() - offset > algoBackendDO.getMaxRound()) {
            List<ChatMessage> chatMessages = promptMessageList.subList(
                    promptMessageList.size() - algoBackendDO.getMaxRound(), promptMessageList.size());
            chatMessagesCutList.addAll(chatMessages);
        } else {
            chatMessagesCutList.addAll(promptMessageList.subList(offset, promptMessageList.size()));
        }
        List<ChatMessage> result = new ArrayList<>();
        int curTokens = 0;
        if (systemMessage != null) {
            curTokens += calculateTokenService.getTokenQty(systemMessage.getContent());
        }
        Collections.reverse(chatMessagesCutList);
        for (ChatMessage chatMessage : chatMessagesCutList) {
            curTokens += calculateTokenService.getTokenQty(chatMessage.getContent());
            if (curTokens < algoBackendDO.getMaxToken()) {
                result.add(chatMessage);
            } else {
                break;
            }
        }
        //如果单条问题超过最大token数，也将最新的问题以单轮会话传给算法模型
        if (result.isEmpty()) {
            result.add(chatMessagesCutList.get(0));
        }
        //如果第一条是回答，直接删除
        if (result.get(result.size() - 1).getRole().equals(AppConstants.BOT)) {
            result.remove(result.size() - 1);
        }

        if (systemMessage != null) {
            result.add(systemMessage);
        }
        Collections.reverse(result);
        codeGeexRequestBean.setPrompt(result);

    }


    /**
     * 重写仓库问答流式回复
     * @param repoStreamJson 仓库问答原始流式回复
     * @return
     */
    public Pair<String, Object> rewriteRepoChat(JSONObject repoStreamJson,
                                         String repoUrl,
                                         String branch){

        String type = repoStreamJson.getString("type");
        if(RepoChatStreamDataTypeEnum.ANSWER.name().equalsIgnoreCase(type)){
            String content = repoStreamJson.getString("content");
            return new Pair<>("answer", content);
        } else if (RepoChatStreamDataTypeEnum.SEARCH.name().equalsIgnoreCase(type)) {
            JSONObject stageInfo = repoStreamJson.getJSONObject("stageInfo");
            if (stageInfo==null){
                LOGGER.error("stageInfo is null");
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "仓库搜索阶段异常，出现非法数据，请重试或者联系管理员解决");
            }
            if (stageInfo.containsKey("keyword")) {
                String keywords = stageInfo.getString("keyword");
                //关闭关键词的展示
//                keywords = String.format(codeGPTDrmConfig.getRepoChatKeywordTemplate(), keywords);
//                repoStreamJson.put("content",keywords);
                return new Pair<>("keyword", keywords);
            }else if (stageInfo.containsKey("fileList")){
                List<String> fileList= (List<String>)stageInfo.get("fileList");
                //关闭文件列表的展示
//                StringBuilder fileMarkdownStr = new StringBuilder();
//                for (String filePath: fileList){
//                    String fileName = filePath.substring(filePath.lastIndexOf("/")+1);
//                    String fileUrl = String.format("%s/blob/%s/%s", repoUrl, branch, filePath);
//                    String singlefileMarkdownStr = String.format("[**%s**](%s)", fileName, fileUrl);
//                    fileMarkdownStr.append(String.format(codeGPTDrmConfig.getRepoChatFileReferenceTemplate(), singlefileMarkdownStr));
//                }
//                fileMarkdownStr.append("\n");
//                repoStreamJson.put("content",fileMarkdownStr.toString());
                return new Pair<>("fileList",fileList);
            }
        }else if(REPO_CHAT_STREAM_REFRESH.equalsIgnoreCase(type)){
            String content = repoStreamJson.getString("content");
            LOGGER.info("refresh content:{}",content);
            return new Pair<>("refresh", content);
        }else if(REPO_CHAT_STREAM_RECOMMEND_QUESTION.equalsIgnoreCase(type)){
            String content = repoStreamJson.getString("content");
            LOGGER.info("recommendQuestion content:{}",content);
            return new Pair<>("recommendQuestion", content);
        }
        return null;
    }

    /**
     * 处理自定义问答流式数据
     */
    public Pair<String, ResponseEnum> getCustomizeStreamContent(String data){
        if (data.startsWith("data: ")) {
            data = data.substring(6);
        } else {
            LOGGER.error("repo chat stream part not start with 'data: ': {}", data);
            return new Pair<>(null, ResponseEnum.STREAM_PART_NOT_START_WITH_DATA_PREFIX);
        }

        JSONObject chatStreamData = JSON.parseObject(data);
        if (chatStreamData == null){
            return null;
        }
        String choices = chatStreamData.getString("choices");
        List<JSONObject> messages = JSON.parseArray(choices).toJavaList(JSONObject.class);
        JSONObject delta = messages.get(0).getJSONObject("delta");
        if(ChatRoleEnum.ASSISTANT.name().equalsIgnoreCase(delta.getString("role"))){
            String content = delta.getString("content");
            return new Pair<>(content,null);
        }
        return null;
    }
}
