package com.alipay.codegencore.service.utils;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0 2024/7/10 15:19
 */
@Slf4j
public class HttpConnectKeepAlive {

    /**
     * 只有回车符在 flush 时，才会立即发送客户端
     */
    private static final char D = '\n';

    /**
     * 默认 100 毫秒检查一次任务执行
     */
    private static final int SLEEP_TIME = 100;

    /**
     * 间隔 60 秒钟发送一次保活包
     */
    private static final int KEEP_ALIVE_TIME = 60000;

    /**
     * 最大超时时间 1000秒，16 分钟
     */
    private static final long MAX_TIMEOUT = 1000000L;


    /**
     * 保活实现
     * @param response
     * @param future
     * @param <V>
     * @throws InterruptedException
     * @throws IOException
     */
    public static <V> void keepAlive(Future<V> future, HttpServletResponse response) {

        try {
            ServletOutputStream outputStream = response.getOutputStream();
            keepAlive(future, MAX_TIMEOUT, () -> {
                try {
                    outputStream.write(D);
                    outputStream.flush();
                } catch (IOException e) {
                    log.warn("send keep alive data failed", e);
                }
                return null;
            });
        } catch (IOException e) {
            log.warn("http keep alive failed", e);
        }

    }

    /**
     * 保活实现
     * @param response
     * @param future
     * @param <V>
     * @throws InterruptedException
     * @throws IOException
     */
    public static <V> void keepAlive(Future<V> future, long maxTimeout, HttpServletResponse response) {

        try {
            ServletOutputStream outputStream = response.getOutputStream();
            keepAlive(future, maxTimeout, () -> {
                try {
                    outputStream.write(D);
                    outputStream.flush();
                } catch (IOException e) {
                    log.warn("send keep alive data failed", e);
                }
                return null;
            });
        } catch (IOException e) {
            log.warn("http keep alive failed", e);
        }

    }

    /**
     * 保活实现
     * @param future
     * @param maxTimeout
     * @param keepAliveHandle
     * @param <V>
     * @throws InterruptedException
     */
    public static <V> void keepAlive(Future<V> future, long maxTimeout, Supplier<Void> keepAliveHandle) {

        if (maxTimeout <= 0) {
            maxTimeout = MAX_TIMEOUT;
        }

        int time = 0;
        while (!future.isDone()) {

            if (time > maxTimeout) {
                break;
            }

            try {
                TimeUnit.MILLISECONDS.sleep(SLEEP_TIME);
            } catch (InterruptedException e) {
                log.warn("sleep failed.", e);
                break;
            }

            time+=SLEEP_TIME;

            if (time%KEEP_ALIVE_TIME == 0) {
                keepAliveHandle.get();
                log.info("send keep alive data to client");
            }

        }

    }
}
