package com.alipay.codegencore.service.utils;

import com.alipay.basementurl.facade.CommonResult;
import com.alipay.basementurl.facade.ShortenRequest;
import com.alipay.basementurl.facade.ShortenResult;
import com.alipay.basementurl.facade.URLFacadeV2;
import com.alipay.sofa.rpc.api.annotation.RpcConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;



/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023.10.31
 */
@Configuration
public class ShortenLink {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShortenLink.class);
    @RpcConsumer
    URLFacadeV2 urlFacadeV2;
    /**
     * 长链接转短链接
     * <AUTHOR>
     * @since 2023.10.31
     * @param url url
     * @return java.lang.String
     */
    public  String createShort(String url){
        ShortenRequest req = new ShortenRequest();
        req.setApp("codegencore");
        req.setDomain("u.alipay.cn");
        req.setUrl(url);
        CommonResult<ShortenResult> result = urlFacadeV2.shorten(req);
        if(result.isSuccess()){
            return result.getResult().getUrl();
        }
        LOGGER.info("Shorten link failed:"+result.getError().getMessage());
        return null;
    }

}
