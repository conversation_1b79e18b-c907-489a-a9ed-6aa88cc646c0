package com.alipay.codegencore.model.model;

import java.util.List;

/**
 * 代码模版规则模型
 *
 * <AUTHOR>
 * 创建时间 2022-03-24
 */
public class CodeTemplateRuleModel {
    /**
     * 匹配规则 - 字典树规则(单个模版场景支持多个字典树规则
     */
    private List<TrieRuleModel> trieRuleModelList;

    /**
     * 匹配规则 - 方法定义关键字匹配
     */
    private MethodDefinitionRuleModel methodDefinitionRuleModel;
    /**
     * 匹配规则 - 方法内容匹配
     */
    private MethodContentRuleModel methodContentRuleModel;


    /**
     * 参数抽取 - 模版参数模型
     */
    private List<TemplateParamModel> templateParamModelList;



    public MethodContentRuleModel getMethodContentRuleModel() {
        return methodContentRuleModel;
    }

    public void setMethodContentRuleModel(MethodContentRuleModel methodContentRuleModel) {
        this.methodContentRuleModel = methodContentRuleModel;
    }

    public MethodDefinitionRuleModel getMethodDefinitionRuleModel() {
        return methodDefinitionRuleModel;
    }

    public void setMethodDefinitionRuleModel(MethodDefinitionRuleModel methodDefinitionRuleModel) {
        this.methodDefinitionRuleModel = methodDefinitionRuleModel;
    }

    public List<TrieRuleModel> getTrieRuleModelList() {
        return trieRuleModelList;
    }

    public void setTrieRuleModelList(List<TrieRuleModel> trieRuleModelList) {
        this.trieRuleModelList = trieRuleModelList;
    }

    public List<TemplateParamModel> getTemplateParamModelList() {
        return templateParamModelList;
    }

    public void setTemplateParamModelList(List<TemplateParamModel> templateParamModelList) {
        this.templateParamModelList = templateParamModelList;
    }
}
