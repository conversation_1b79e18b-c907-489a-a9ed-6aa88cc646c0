package com.alipay.codegencore.model.openai;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 19:36
 */
public class CodeInfo {

    private String filePath;

    private String oldCode;

    private String newCode;

    /**
     * 无参构造器
     */
    public CodeInfo() {
    }

    /**
     * 全参构造器
     * @param filePath
     * @param oldCode
     * @param newCode
     */
    public CodeInfo(String filePath, String oldCode, String newCode) {
        this.filePath = filePath;
        this.oldCode = oldCode;
        this.newCode = newCode;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getOldCode() {
        return oldCode;
    }

    public void setOldCode(String oldCode) {
        this.oldCode = oldCode;
    }

    public String getNewCode() {
        return newCode;
    }

    public void setNewCode(String newCode) {
        this.newCode = newCode;
    }
}
