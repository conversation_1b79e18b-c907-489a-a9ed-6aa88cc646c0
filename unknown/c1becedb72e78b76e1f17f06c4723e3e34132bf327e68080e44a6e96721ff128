/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.Map;

/**
 * <AUTHOR>
 * @version InputOut.java, v 0.1 2023年11月10日 上午10:57 wb-tzg858080
 */
public class Input extends ToString {

    /**
     * 表单数据
     */
    private Map<String,Object> formData;

    /**
     * 用户填写的最终参数
     */
    private Map<String,Object> userFill;

    /**
     * 请求内容
     */
    private Map<String,Object> requestBody;

    public Map<String, Object> getFormData() {
        return formData;
    }

    public void setFormData(Map<String, Object> formData) {
        this.formData = formData;
    }

    public Map<String, Object> getUserFill() {
        return userFill;
    }

    public void setUserFill(Map<String, Object> userFill) {
        this.userFill = userFill;
    }

    public Map<String, Object> getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(Map<String, Object> requestBody) {
        this.requestBody = requestBody;
    }
}
