package com.alipay.codegencore.service.impl.common;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import com.alipay.codegencore.dal.example.RateLimitDOExample;
import com.alipay.codegencore.dal.mapper.RateLimitDOMapper;
import com.alipay.codegencore.dal.mapper.RateLimitManualMapper;
import com.alipay.codegencore.model.domain.RateLimitDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.limit.LimitTypeEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.AlgoBackendModel;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.common.limiter.RateLimitService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 限流service实现类
 */
@Service("rateLimitService")
public class RateLimitServiceImpl implements RateLimitService {

    @Resource
    private RateLimitDOMapper rateLimitDOMapper;

    @Resource
    private UserAclService userAclService;

    @Resource
    private RateLimitManualMapper rateLimitManualMapper;


    @Override
    public List<RateLimitDO> getRateLimitListByParam(LimitTypeEnum limitType, List<String> callerList, List<String> targetList) {
        if (limitType == null || CollectionUtils.isEmpty(callerList) || CollectionUtils.isEmpty(targetList)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        RateLimitDOExample rateLimitDOExample = new RateLimitDOExample();
        rateLimitDOExample.createCriteria().andTypeEqualTo(limitType.name()).andCallerIn(callerList).andTargetIn(targetList);
        rateLimitDOExample.setOrderByClause("sorted ASC");
        List<RateLimitDO> rateLimitDOList = rateLimitDOMapper.selectByExample(rateLimitDOExample);
        if (CollectionUtils.isEmpty(rateLimitDOList)) {
            return new ArrayList<>();
        }
        return rateLimitDOList;
    }

    @Override
    public void insertSelective(RateLimitDO rateLimitDO) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        rateLimitDO.setCreateUserId(currentUser.getId());
        int i = rateLimitDOMapper.insertSelective(rateLimitDO);
        if (i != 1) {
            throw new BizException(ResponseEnum.UPDATE_DATABASE_FAILED);
        }
    }

    @Override
    public void updateByPrimaryKeySelective(RateLimitDO rateLimitDO) {
        if (rateLimitDO.getId() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        int i = rateLimitDOMapper.updateByPrimaryKeySelective(rateLimitDO);
        if (i != 1) {
            throw new BizException(ResponseEnum.UPDATE_DATABASE_FAILED);
        }
    }

    @Override
    public void deleteByPrimaryKey(Long id) {
        if (id == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        int i = rateLimitDOMapper.deleteByPrimaryKey(id);
        if (i != 1) {
            throw new BizException(ResponseEnum.UPDATE_DATABASE_FAILED);
        }
    }

    /**
     * 获取所有限流配置
     *
     * @param pageNo   页数
     * @param pageSize 每页数量
     * @return
     */
    @Override
    public PageResponse<List<RateLimitDO>> getAllRateLimit(int pageNo, int pageSize, String query, String type, String template,
                                                           Boolean needLimit, String sorted) {
        Page<AlgoBackendModel> pageInfo = PageHelper.startPage(pageNo, pageSize);
        RateLimitDOExample rateLimitDOExample = new RateLimitDOExample();
        List<Long> alLRateLimitId = rateLimitManualMapper.getAlLRateLimitId(query, type, template, needLimit, sorted);
        rateLimitDOExample.createCriteria().andIdIn(alLRateLimitId);
        if (StrUtil.equals("ASC", sorted)) {
            rateLimitDOExample.setOrderByClause("sorted ASC");
        }
        else if (StrUtil.equals("DESC", sorted)) {
            rateLimitDOExample.setOrderByClause("sorted DESC");
        }
        List<RateLimitDO> rateLimitDOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(alLRateLimitId)) {
            return PageResponse.build(ResponseEnum.SUCCESS, rateLimitDOList, 0L);
        }
        rateLimitDOList = rateLimitDOMapper.selectByExample(rateLimitDOExample);

        return PageResponse.build(ResponseEnum.SUCCESS, rateLimitDOList, pageInfo.getTotal());
    }

    /**
     * 根据id查询一条限流规则
     *
     * @param id 页数
     * @return
     */
    @Override
    public RateLimitDO getRateLimitById(Long id) {
        return rateLimitDOMapper.selectByPrimaryKey(id);
    }
}
