/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.aop.handler;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alipay.antq.common.utils.StringUtils;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.AllowAccessTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserStatusEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.common.limiter.RateLimitFactory;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.service.utils.TbaseCacheService;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.fc.fcbuservice.sdk.common.domain.BuserviceUser;
import com.alipay.fc.fcbuservice.sdk.sso.BuserviceLoginUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

import static com.alipay.codegencore.model.contant.WebApiContents.*;


/**
 * 获取用户信息,并设置到threadLocal中
 */
@Order(-1)
@Component
public class SetUserInfoHandler implements WebApiPreHandler {

    private static final Logger ONLINE_DEBUG_LOGGER = LoggerFactory.getLogger("ONLINEDEBUG");

    private static final Logger LOGGER = LoggerFactory.getLogger( SetUserInfoHandler.class );

    @Resource
    private UserAuthDOMapper userAuthDOMapper;

    @Resource
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private CodeGPTDrmConfig drmConfig;

    @Resource
    private TbaseCacheService tbaseCacheService;
    @Resource
    private UserAclService          userAclService;
    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;

    @Resource
    private TokenService tokenService;

    /**
     * 缓存超时时间
     */
    private static final int CACHE_TIMEOUT = 12 * 60 * 60;

    /**
     * 获取用户信息,并设置到threadLocal中
     *
     * @param request    request
     * @param response   response
     * @param methodName 方法名
     * @param args       参数
     */
    @Override
    public void execute(HttpServletRequest request, HttpServletResponse response, String methodName, Object[] args) {
        BuserviceUser loginUser = BuserviceLoginUtil.getSimpleUser(request, response, BuserviceUser.class);
        String requestUri = new UrlPathHelper().getLookupPathForRequest(request);
        String empId = null;
        if (loginUser == null) {
            String requestEmpId = checkRequestHeader(request, requestUri);
            if (requestEmpId == null) {
                throw new BizException(ResponseEnum.USER_NOT_LOGIN);
            }else {
                empId = requestEmpId;
            }
        }else {
            empId = loginUser.getWorkNo();
        }
        //通过缓存获取用户信息
        UserAuthDO userAuthDO = (UserAuthDO) defaultCacheManager.get(AppConstants.CACHE_PREFIX + empId);
        if (userAuthDO == null || userAuthDO.getStatus() != UserStatusEnum.ACTIVE || userAuthDO.getAllowAccessType() == null) { //仅在允许使用的情况下相信缓存，防止后台修改之后缓存不一致
            // 查询并新增用户的时候需要加锁，否则会插入重复的用户
            boolean lock = tbaseCacheService.getLock(AppConstants.INSERT_USER_PREFIX + empId, 5000,2000);
            if (!lock) {
                throw new BizException(ResponseEnum.REFRESH_WEB_RETRY);
            }
            List<UserAuthDO> userAuthDOList = getUserAuthListByEmpId(empId);
            if (userAuthDOList.isEmpty()) {
                if (!drmConfig.isDefaultCreateUser()) {
                    LOGGER.warn("userAuthDOList is empty, empId:{}", empId);
                    return;
                }
                if (loginUser != null){
                    userAuthDO = createUserAuthAndInsert(loginUser);
                }else{
                    //  已有用户不会进行直接返回
                    List<String> strings = codeFuseUserAuthService.insertUserAuth(Collections.singletonList(empId));
                    if (strings.isEmpty()){
                        userAuthDO =   getUserAuthListByEmpId(empId).get(0);
                    }else {
                        throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
                    }
                }
            } else {
                userAuthDO = userAuthDOList.get(0);
            }
            tbaseCacheService.releaseLock(AppConstants.INSERT_USER_PREFIX + empId);
            ONLINE_DEBUG_LOGGER.debug("no user info in tbase, get user info from database, requestUri:{}, userAuthDO:{}", requestUri, JSON.toJSONString(userAuthDO));
        }else{
            ONLINE_DEBUG_LOGGER.debug("get user info from tbase, requestUri:{}, userAuthDO:{}", requestUri, JSON.toJSONString(userAuthDO));
        }

        //设置到ThreadLocal中
        ContextUtil.set(ORIGIN_USER, userAuthDO);
        ONLINE_DEBUG_LOGGER.debug("set user info to context, requestUri:{}, userAuthDO:{}",requestUri, JSON.toJSONString(userAuthDO));
        defaultCacheManager.setex(AppConstants.CACHE_PREFIX + empId, CACHE_TIMEOUT, userAuthDO);
        //如果是mock用户，需要将mock用户信息设置到ThreadLocal中 （一般用于调试，让调试者获取他人权限）
        UserAuthDO proxyUser = getProxyUser(userAuthDO);
        ONLINE_DEBUG_LOGGER.debug("set proxy user to context, requestUri:{}, proxyUserId: {}", requestUri, proxyUser==null? null: JSON.toJSONString(proxyUser));
        ContextUtil.set(CONTEXT_USER, proxyUser != null ? proxyUser : userAuthDO);
        // 处理webapi的限流逻辑
        String originalUri = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
        checkLimitWebApi(userAuthDO.getId(), originalUri);

        // 外网版本需要对用户请求的接口进行鉴权，只有通过审批的用户才可以请求白名单接口
        if(!drmConfig.isIntranetApplication()){
            if(!isAllowAccessBizApi(requestUri, userAuthDO)) {
                throw new BizException(ResponseEnum.NO_AUTH);
            }
        }
    }

    /**
     * 【外网】判断用户是否有权限可以请求该接口
     * @param requestUri
     * @param userAuthDO
     * @return
     */
    private Boolean isAllowAccessBizApi(String requestUri, UserAuthDO userAuthDO) {
        if(isValidUser(userAuthDO)) {
            return true;
        }
        try {
            List<String> s = JSON.parseArray(drmConfig.getNeedCheckBizApi(), String.class);
            if (s.contains(requestUri)) {
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("isAllowAccessBizApi exception", e);
            return true;
        }

        return true;
    }

    /**
     * 【外网】用户必须是正常用户且allowAccessType=1或10
     * @param userAuthDO
     * @return
     */
    private Boolean isValidUser(UserAuthDO userAuthDO) {
        return null != userAuthDO && userAuthDO.getStatus() == UserStatusEnum.ACTIVE
                && (userAuthDO.getAllowAccessType() == 1 || userAuthDO.getAllowAccessType() == 10);
    }

    private void checkLimitWebApi(Long userId, String originalUri) {
        Pair<Boolean, Long> limitPair = RateLimitFactory.getInstance().tryAcquireWebApi(userId, originalUri);
        if (!limitPair.getKey()) {
            throw new BizException(ResponseEnum.USER_HTTP_REQUEST_LIMITING_ANOMALY, ResponseEnum.USER_HTTP_REQUEST_LIMITING_ANOMALY.getErrorMsg() + ",触发限流规则id:" + limitPair.getValue());
        }
    }

    private UserAuthDO createUserAuthAndInsert(BuserviceUser loginUser) {
        UserAuthDO userAuthDO = new UserAuthDO();
        userAuthDO.setEmpId(loginUser.getWorkNo());
        userAuthDO.setUserName(StringUtils.isBlank(loginUser.getNickName()) ? loginUser.getRealName() : loginUser.getNickName());
        userAuthDO.setBuName("");
        String token = ShortUid.getUid();
        userAuthDO.setToken(token);
        userAuthDO.setAdmin((byte) 0);
        userAuthDO.setStatus(UserStatusEnum.ACTIVE);
        if (!drmConfig.isIntranetApplication()) {
            userAuthDO.setAllowAccessType(AllowAccessTypeEnum.ALL.getValue());
        }
        userAuthDOMapper.insertSelective(userAuthDO);
        return userAuthDO;
    }

    /**
     * 获取mock用户信息
     *
     * @param userAuthDO 用户信息
     * @return mock的用户信息，如果没有返回null
     */
    private UserAuthDO getProxyUser(UserAuthDO userAuthDO) {
        UserAuthDO authDO = (UserAuthDO) defaultCacheManager.hget(TBASE_MOCK_USER_INFO_KEY, userAuthDO.getEmpId());
        if (authDO != null) {
            return getUserAuthListByEmpId(authDO.getEmpId()).get(0);
        }
        return null;
    }

    private List<UserAuthDO> getUserAuthListByEmpId(String empId) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdEqualTo(empId);
        return userAuthDOMapper.selectByExample(userAuthDOExample);
    }

    /**
     *校验request 的header是否携带参数 有的话校验合法返回调用empId
     * @param request
     * @param requestUri
     * @return
     */
    private String checkRequestHeader(HttpServletRequest request,String requestUri) {
        String user = request.getHeader("Codegpt_user");
        String userToken = request.getHeader("Codegpt_token");
        String empId = request.getHeader("Emp_id");
        if (user != null && userToken != null) {
            if (!userAclService.isAuthorizedByToken(user, userToken, requestUri)) {
                LOGGER.info("request not authorized, user: {},uri:{}", user,requestUri);
                throw new BizException(ResponseEnum.NO_AUTH);
            }
            if (empId == null){
                TokenDO tokenDO = userAclService.queryTokenOwner(user);
                if(tokenDO != null && tokenDO.getOwnerUserId()!=null){
                    UserAuthDO userAuthDO = userAuthDOMapper.selectByPrimaryKey(Long.valueOf(tokenDO.getOwnerUserId()));
                    if (userAuthDO != null){
                        empId = userAuthDO.getEmpId();
                    }
                }
            }

            if (empId != null) {
                LOGGER.info("request empId: {}, uri:{}", empId, requestUri);
                return getEmpIdAdd0(empId);
            }

        }
        return null;
    }

    /**
     * 如果传入的是<6位数的,那么在前面补全0到6位数
     *
     * @param empId 工号
     * @return 补全后的工号
     */
    public String getEmpIdAdd0(String empId) {
        if (empId.length() < 6) {
            return "0".repeat(6 - empId.length())
                    + empId;
        }
        return empId;
    }
}