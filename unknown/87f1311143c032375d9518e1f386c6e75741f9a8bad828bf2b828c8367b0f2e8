package com.alipay.codegencore.model.domain;

import java.util.Date;

public class SceneDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.gmt_create
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.gmt_modified
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.name
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.description
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.system_prompt
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String systemPrompt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.deleted
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Integer deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.query_template_list_json
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String queryTemplateListJson;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.mode
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Integer mode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.model
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String model;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.plugin_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String pluginList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.user_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.visable_user
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Integer visableUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.usage_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Long usageCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.audit_status
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Integer auditStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.enable
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Integer enable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.scene_tag
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String sceneTag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.visable_env
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Integer visableEnv;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.multi_round_support
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Byte multiRoundSupport;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.function_call_config
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String functionCallConfig;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.biz_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String bizId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.scene_type
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Byte sceneType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.icon_url
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String iconUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.icon_background_color
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String iconBackgroundColor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.usage_user_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Long usageUserCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.usage_message_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Long usageMessageCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.owner_user_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Long ownerUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.recommend_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Boolean recommendScene;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.scene_sort
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Integer sceneSort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.use_instructions
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String useInstructions;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.document_uid_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String documentUidList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.yuque_token_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String yuqueTokenList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.plugin_command
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String pluginCommand;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.plugin_enable
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private Boolean pluginEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_scene.scene_tips
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    private String sceneTips;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.id
     *
     * @return the value of cg_scene.id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.id
     *
     * @param id the value for cg_scene.id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.gmt_create
     *
     * @return the value of cg_scene.gmt_create
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.gmt_create
     *
     * @param gmtCreate the value for cg_scene.gmt_create
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.gmt_modified
     *
     * @return the value of cg_scene.gmt_modified
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.gmt_modified
     *
     * @param gmtModified the value for cg_scene.gmt_modified
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.name
     *
     * @return the value of cg_scene.name
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.name
     *
     * @param name the value for cg_scene.name
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.description
     *
     * @return the value of cg_scene.description
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.description
     *
     * @param description the value for cg_scene.description
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.system_prompt
     *
     * @return the value of cg_scene.system_prompt
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getSystemPrompt() {
        return systemPrompt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.system_prompt
     *
     * @param systemPrompt the value for cg_scene.system_prompt
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.deleted
     *
     * @return the value of cg_scene.deleted
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Integer getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.deleted
     *
     * @param deleted the value for cg_scene.deleted
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.query_template_list_json
     *
     * @return the value of cg_scene.query_template_list_json
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getQueryTemplateListJson() {
        return queryTemplateListJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.query_template_list_json
     *
     * @param queryTemplateListJson the value for cg_scene.query_template_list_json
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setQueryTemplateListJson(String queryTemplateListJson) {
        this.queryTemplateListJson = queryTemplateListJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.mode
     *
     * @return the value of cg_scene.mode
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Integer getMode() {
        return mode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.mode
     *
     * @param mode the value for cg_scene.mode
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setMode(Integer mode) {
        this.mode = mode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.model
     *
     * @return the value of cg_scene.model
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getModel() {
        return model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.model
     *
     * @param model the value for cg_scene.model
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.plugin_list
     *
     * @return the value of cg_scene.plugin_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getPluginList() {
        return pluginList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.plugin_list
     *
     * @param pluginList the value for cg_scene.plugin_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setPluginList(String pluginList) {
        this.pluginList = pluginList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.user_id
     *
     * @return the value of cg_scene.user_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.user_id
     *
     * @param userId the value for cg_scene.user_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.visable_user
     *
     * @return the value of cg_scene.visable_user
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Integer getVisableUser() {
        return visableUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.visable_user
     *
     * @param visableUser the value for cg_scene.visable_user
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setVisableUser(Integer visableUser) {
        this.visableUser = visableUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.usage_count
     *
     * @return the value of cg_scene.usage_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Long getUsageCount() {
        return usageCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.usage_count
     *
     * @param usageCount the value for cg_scene.usage_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.audit_status
     *
     * @return the value of cg_scene.audit_status
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Integer getAuditStatus() {
        return auditStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.audit_status
     *
     * @param auditStatus the value for cg_scene.audit_status
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.enable
     *
     * @return the value of cg_scene.enable
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Integer getEnable() {
        return enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.enable
     *
     * @param enable the value for cg_scene.enable
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.scene_tag
     *
     * @return the value of cg_scene.scene_tag
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getSceneTag() {
        return sceneTag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.scene_tag
     *
     * @param sceneTag the value for cg_scene.scene_tag
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setSceneTag(String sceneTag) {
        this.sceneTag = sceneTag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.visable_env
     *
     * @return the value of cg_scene.visable_env
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Integer getVisableEnv() {
        return visableEnv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.visable_env
     *
     * @param visableEnv the value for cg_scene.visable_env
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setVisableEnv(Integer visableEnv) {
        this.visableEnv = visableEnv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.multi_round_support
     *
     * @return the value of cg_scene.multi_round_support
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Byte getMultiRoundSupport() {
        return multiRoundSupport;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.multi_round_support
     *
     * @param multiRoundSupport the value for cg_scene.multi_round_support
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setMultiRoundSupport(Byte multiRoundSupport) {
        this.multiRoundSupport = multiRoundSupport;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.function_call_config
     *
     * @return the value of cg_scene.function_call_config
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getFunctionCallConfig() {
        return functionCallConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.function_call_config
     *
     * @param functionCallConfig the value for cg_scene.function_call_config
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setFunctionCallConfig(String functionCallConfig) {
        this.functionCallConfig = functionCallConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.biz_id
     *
     * @return the value of cg_scene.biz_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getBizId() {
        return bizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.biz_id
     *
     * @param bizId the value for cg_scene.biz_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.scene_type
     *
     * @return the value of cg_scene.scene_type
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Byte getSceneType() {
        return sceneType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.scene_type
     *
     * @param sceneType the value for cg_scene.scene_type
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setSceneType(Byte sceneType) {
        this.sceneType = sceneType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.icon_url
     *
     * @return the value of cg_scene.icon_url
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getIconUrl() {
        return iconUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.icon_url
     *
     * @param iconUrl the value for cg_scene.icon_url
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.icon_background_color
     *
     * @return the value of cg_scene.icon_background_color
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getIconBackgroundColor() {
        return iconBackgroundColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.icon_background_color
     *
     * @param iconBackgroundColor the value for cg_scene.icon_background_color
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setIconBackgroundColor(String iconBackgroundColor) {
        this.iconBackgroundColor = iconBackgroundColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.usage_user_count
     *
     * @return the value of cg_scene.usage_user_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Long getUsageUserCount() {
        return usageUserCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.usage_user_count
     *
     * @param usageUserCount the value for cg_scene.usage_user_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setUsageUserCount(Long usageUserCount) {
        this.usageUserCount = usageUserCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.usage_message_count
     *
     * @return the value of cg_scene.usage_message_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Long getUsageMessageCount() {
        return usageMessageCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.usage_message_count
     *
     * @param usageMessageCount the value for cg_scene.usage_message_count
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setUsageMessageCount(Long usageMessageCount) {
        this.usageMessageCount = usageMessageCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.owner_user_id
     *
     * @return the value of cg_scene.owner_user_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Long getOwnerUserId() {
        return ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.owner_user_id
     *
     * @param ownerUserId the value for cg_scene.owner_user_id
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.recommend_scene
     *
     * @return the value of cg_scene.recommend_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Boolean getRecommendScene() {
        return recommendScene;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.recommend_scene
     *
     * @param recommendScene the value for cg_scene.recommend_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setRecommendScene(Boolean recommendScene) {
        this.recommendScene = recommendScene;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.scene_sort
     *
     * @return the value of cg_scene.scene_sort
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Integer getSceneSort() {
        return sceneSort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.scene_sort
     *
     * @param sceneSort the value for cg_scene.scene_sort
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setSceneSort(Integer sceneSort) {
        this.sceneSort = sceneSort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.use_instructions
     *
     * @return the value of cg_scene.use_instructions
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getUseInstructions() {
        return useInstructions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.use_instructions
     *
     * @param useInstructions the value for cg_scene.use_instructions
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setUseInstructions(String useInstructions) {
        this.useInstructions = useInstructions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.document_uid_list
     *
     * @return the value of cg_scene.document_uid_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getDocumentUidList() {
        return documentUidList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.document_uid_list
     *
     * @param documentUidList the value for cg_scene.document_uid_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setDocumentUidList(String documentUidList) {
        this.documentUidList = documentUidList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.yuque_token_list
     *
     * @return the value of cg_scene.yuque_token_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getYuqueTokenList() {
        return yuqueTokenList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.yuque_token_list
     *
     * @param yuqueTokenList the value for cg_scene.yuque_token_list
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setYuqueTokenList(String yuqueTokenList) {
        this.yuqueTokenList = yuqueTokenList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.plugin_command
     *
     * @return the value of cg_scene.plugin_command
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getPluginCommand() {
        return pluginCommand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.plugin_command
     *
     * @param pluginCommand the value for cg_scene.plugin_command
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setPluginCommand(String pluginCommand) {
        this.pluginCommand = pluginCommand;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.plugin_enable
     *
     * @return the value of cg_scene.plugin_enable
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Boolean getPluginEnable() {
        return pluginEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.plugin_enable
     *
     * @param pluginEnable the value for cg_scene.plugin_enable
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setPluginEnable(Boolean pluginEnable) {
        this.pluginEnable = pluginEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_scene.scene_tips
     *
     * @return the value of cg_scene.scene_tips
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getSceneTips() {
        return sceneTips;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_scene.scene_tips
     *
     * @param sceneTips the value for cg_scene.scene_tips
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setSceneTips(String sceneTips) {
        this.sceneTips = sceneTips;
    }
}