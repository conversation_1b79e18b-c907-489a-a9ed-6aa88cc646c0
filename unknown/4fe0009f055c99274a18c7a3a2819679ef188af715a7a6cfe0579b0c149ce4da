<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sofa="http://schema.alipay.com/sofa/schema/service"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd


       http://schema.alipay.com/sofa/schema/service
       http://schema.alipay.com/sofa/sofa-service-4-0-0.xsd"
       default-autowire="byName">


    <bean id="tokenAuditService" class="com.alipay.codegencore.service.codegpt.user.TokenAuditService" />
    <sofa:service ref="tokenAuditService" unique-id="codegencore_facadeName" interface="com.alipay.fc.process.common.service.facade.callback.ProcessCallback">
        <sofa:binding.tr>
            <sofa:global-attrs filter="logFilter" />
        </sofa:binding.tr>
    </sofa:service>

    <!--本 xml 用于展示 SOFABoot 的 xsd 文件-->
</beans>