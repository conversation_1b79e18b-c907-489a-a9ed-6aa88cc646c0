package com.alipay.codegencore.utils.http;

import java.time.Duration;

/**
 * http客户端
 *
 * <AUTHOR>
 * 创建时间 2022-10-17
 */
public class HttpClient {
    /**
     * jdk的http客户端
     */
    private static final java.net.http.HttpClient CLIENT = HttpCoreHolder.CLIENT;

    /**
     * 构建get请求
     *
     * @param url
     * @return
     */
    public static GetBuilder get(String url) {
        return new GetBuilder(url, CLIENT);
    }


    /**
     * 构建post请求
     *
     * @param url
     * @return
     */
    public static PostBuilder post(String url) {
        return new PostBuilder(url, CLIENT);
    }

    /**
     * 单例模式
     */
    private static class HttpCoreHolder {
        /**
         * 原生 httpclient
         */
        private static final java.net.http.HttpClient CLIENT = initHttpClient();


        /**
         * 连接超时时间(由于流式调用需要连接国外服务,连接超时时间为1分钟)
         */
        private static final int CONNECT_TIMEOUT = 1000*60;


        /**
         * 初始化http客户端
         *
         * @return
         */
        private static java.net.http.HttpClient initHttpClient() {
            return java.net.http.HttpClient.newBuilder()
                    .connectTimeout(Duration.ofMillis(CONNECT_TIMEOUT))
                    .followRedirects(java.net.http.HttpClient.Redirect.NORMAL).build();
        }
    }
}
