package com.alipay.codegencore.model.domain;

import java.util.Date;

public class ChatSessionDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.gmt_create
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.gmt_modified
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.uid
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String uid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.title
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.deleted
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private Byte deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.user_id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.model
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String model;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.prompt
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String prompt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.model_config
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String modelConfig;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.scene_id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private Long sceneId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.scene_test
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private Boolean sceneTest;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.oss_address_list
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String ossAddressList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.ext_info
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String extInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.source_platform
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String sourcePlatform;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_chat_session.document_uid_list
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    private String documentUidList;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.id
     *
     * @return the value of cg_chat_session.id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.id
     *
     * @param id the value for cg_chat_session.id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.gmt_create
     *
     * @return the value of cg_chat_session.gmt_create
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.gmt_create
     *
     * @param gmtCreate the value for cg_chat_session.gmt_create
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.gmt_modified
     *
     * @return the value of cg_chat_session.gmt_modified
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.gmt_modified
     *
     * @param gmtModified the value for cg_chat_session.gmt_modified
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.uid
     *
     * @return the value of cg_chat_session.uid
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getUid() {
        return uid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.uid
     *
     * @param uid the value for cg_chat_session.uid
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setUid(String uid) {
        this.uid = uid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.title
     *
     * @return the value of cg_chat_session.title
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.title
     *
     * @param title the value for cg_chat_session.title
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.deleted
     *
     * @return the value of cg_chat_session.deleted
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Byte getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.deleted
     *
     * @param deleted the value for cg_chat_session.deleted
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.user_id
     *
     * @return the value of cg_chat_session.user_id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.user_id
     *
     * @param userId the value for cg_chat_session.user_id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.model
     *
     * @return the value of cg_chat_session.model
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getModel() {
        return model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.model
     *
     * @param model the value for cg_chat_session.model
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.prompt
     *
     * @return the value of cg_chat_session.prompt
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getPrompt() {
        return prompt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.prompt
     *
     * @param prompt the value for cg_chat_session.prompt
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.model_config
     *
     * @return the value of cg_chat_session.model_config
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getModelConfig() {
        return modelConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.model_config
     *
     * @param modelConfig the value for cg_chat_session.model_config
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setModelConfig(String modelConfig) {
        this.modelConfig = modelConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.scene_id
     *
     * @return the value of cg_chat_session.scene_id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Long getSceneId() {
        return sceneId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.scene_id
     *
     * @param sceneId the value for cg_chat_session.scene_id
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setSceneId(Long sceneId) {
        this.sceneId = sceneId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.scene_test
     *
     * @return the value of cg_chat_session.scene_test
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Boolean getSceneTest() {
        return sceneTest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.scene_test
     *
     * @param sceneTest the value for cg_chat_session.scene_test
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setSceneTest(Boolean sceneTest) {
        this.sceneTest = sceneTest;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.oss_address_list
     *
     * @return the value of cg_chat_session.oss_address_list
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getOssAddressList() {
        return ossAddressList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.oss_address_list
     *
     * @param ossAddressList the value for cg_chat_session.oss_address_list
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setOssAddressList(String ossAddressList) {
        this.ossAddressList = ossAddressList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.ext_info
     *
     * @return the value of cg_chat_session.ext_info
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getExtInfo() {
        return extInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.ext_info
     *
     * @param extInfo the value for cg_chat_session.ext_info
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.source_platform
     *
     * @return the value of cg_chat_session.source_platform
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getSourcePlatform() {
        return sourcePlatform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.source_platform
     *
     * @param sourcePlatform the value for cg_chat_session.source_platform
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setSourcePlatform(String sourcePlatform) {
        this.sourcePlatform = sourcePlatform;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_chat_session.document_uid_list
     *
     * @return the value of cg_chat_session.document_uid_list
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getDocumentUidList() {
        return documentUidList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_chat_session.document_uid_list
     *
     * @param documentUidList the value for cg_chat_session.document_uid_list
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setDocumentUidList(String documentUidList) {
        this.documentUidList = documentUidList;
    }
}