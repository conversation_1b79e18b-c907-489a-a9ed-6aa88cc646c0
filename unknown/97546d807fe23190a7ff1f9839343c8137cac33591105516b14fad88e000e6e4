package com.alipay.codegencore.utils.http;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Flow;

/**
 * 默认的流式数据消费者
 *
 * <AUTHOR>
 * 创建时间 2023-04-10
 */
public class DefaultStreamDataSubscriber implements  Flow.Subscriber<String>{
    private static final Logger LOGGER = LoggerFactory.getLogger( DefaultStreamDataSubscriber.class );
    /**
     * 消费者
     */
    private Flow.Subscription subscription;
    /**
     * 流式数据监听器
     */
    private StreamDataListener streamDataListener;

    /**
     * 构造函数,传入监听器
     * @param streamDataListener
     */
    public DefaultStreamDataSubscriber(StreamDataListener streamDataListener) {
        this.streamDataListener = streamDataListener;
    }

    /**
     * 和远程服务器开始建立连接
     * @param subscription a new subscription
     */
    @Override
    public void onSubscribe(Flow.Subscription subscription) {
        this.subscription = subscription;
        streamDataListener.onConnect(subscription);
        subscription.request(1);
    }

    /**
     * 逐条监听数据
     * @param item the item
     */
    @Override
    public void onNext(String item) {
        try {
            streamDataListener.eachData(item,subscription);
        }catch (BizException bizException){
            LOGGER.error("error handle stream data: {}", item, bizException);
            subscription.cancel();
            throw bizException;
        }catch (Exception e){
            LOGGER.error("error handle stream data: {}", item, e);
            subscription.cancel();
            throw new BizException(ResponseEnum.AI_CALL_ERROR, e);
        }
        subscription.request(1);
    }

    /**
     * 接收信息过程中发生异常
     * 如果接收器不为null,则cancel接收器,断开连接
     * @param throwable the exception
     */
    @Override
    public void onError(Throwable throwable) {
        streamDataListener.onError(throwable);
        if(subscription!=null){
            subscription.cancel();
        }
    }

    /**
     * 请求完成
     */
    @Override
    public void onComplete() {
        streamDataListener.onComplete();
    }
}
