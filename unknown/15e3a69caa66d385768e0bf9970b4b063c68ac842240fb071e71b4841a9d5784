package com.alipay.codegencore.model.response.tsingyan;

import com.alipay.codegencore.model.domain.FileDataDO;
import com.alipay.codegencore.model.model.CodeGptConfigModel;
import com.alipay.codegencore.model.model.CompletionConfigModel;
import com.alipay.codegencore.model.model.EventTrackConfigModel;

import java.io.Serializable;
import java.util.List;

/**
 * 代码补全配置数据模型
 *
 * <AUTHOR>
 * 创建时间 2022-10-24
 */
public class TsingyanConfigDataModel implements Serializable {
    /**
     * 防止字段变化导致反序列化失败
     */
    private static final long serialVersionUID = 1L;
    /**
     * 数据文件列表,只用于本地模型，待大模型上线后看产品意见是否删除
     */
    @Deprecated
    private List<FileDataDO> fileDataDOList;

    /**
     * 代码补全配置，行补全使用，大模型上线后过期，使用
     */
    @Deprecated
    private CompletionConfigModel completionConfigModel = new CompletionConfigModel();

    /**
     * 插件埋点配置
     */
    private EventTrackConfigModel eventTrackConfig = new EventTrackConfigModel();

    /**
     * 代码补全配置，适配大模型
     */
    private CodeGptConfigModel codeGptConfigModel = new CodeGptConfigModel();

    

    public CodeGptConfigModel getCodeGptConfigModel() {
        return codeGptConfigModel;
    }

    public void setCodeGptConfigModel(CodeGptConfigModel codeGptConfigModel) {
        this.codeGptConfigModel = codeGptConfigModel;
    }

    public CompletionConfigModel getCompletionConfigModel() {
        return completionConfigModel;
    }

    public void setCompletionConfigModel(CompletionConfigModel completionConfigModel) {
        this.completionConfigModel = completionConfigModel;
    }

    public EventTrackConfigModel getEventTrackConfig() {
        return eventTrackConfig;
    }

    public void setEventTrackConfig(EventTrackConfigModel eventTrackConfig) {
        this.eventTrackConfig = eventTrackConfig;
    }

    public List<FileDataDO> getFileDataDOList() {
        return fileDataDOList;
    }

    public void setFileDataDOList(List<FileDataDO> fileDataDOList) {
        this.fileDataDOList = fileDataDOList;
    }
}
