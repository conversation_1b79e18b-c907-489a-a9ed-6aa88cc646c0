package com.alipay.codegencore.model.enums;

/**
 * 客户端流量模型
 *
 * <AUTHOR>
 * 创建时间 2022-09-04
 */
public enum AlgFlowEnum {
    /**
     * 全部流量走在线
     */
    ONLINE(100, 0),
    /**
     * 中间流量-在线/离线各50%
     */
    MIDDLE_STATE(50, 50),
    /**
     * 全部流量走离线
     */
    OFFLINE(0, 100);

    AlgFlowEnum(int onlineFlow, int offlineFlow) {
        this.onlineFlow = onlineFlow;
        this.offlineFlow = offlineFlow;
    }

    /**
     * 在线流程
     */
    private int onlineFlow;
    /**
     * 离线流量
     */
    private int offlineFlow;



    public int getOnlineFlow() {
        return onlineFlow;
    }

    public void setOnlineFlow(int onlineFlow) {
        this.onlineFlow = onlineFlow;
    }

    public int getOfflineFlow() {
        return offlineFlow;
    }

    public void setOfflineFlow(int offlineFlow) {
        this.offlineFlow = offlineFlow;
    }

    /**
     * 根据name转化为枚举
     * @param name
     * @return
     */
    public static AlgFlowEnum getAlgFlowByName(String name) {
        for (AlgFlowEnum algFlowEnum : AlgFlowEnum.values()) {
            if (algFlowEnum.name().equals(name)) {
                return algFlowEnum;
            }
        }
        return ONLINE;
    }
}
