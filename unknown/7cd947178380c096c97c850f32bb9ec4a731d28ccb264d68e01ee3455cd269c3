package com.alipay.codegencore.service.codegpt.user;

import java.util.List;

import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.request.UpdateAllowAccessTypeVO;
import com.alipay.codegencore.model.response.PageResponse;

/**
 * UserAuth表service
 */
public interface CodeFuseUserAuthService {


    /**
     * 查看所有用户
     *
     * @param pageNo       分页
     * @param pageSize     分页
     * @param filterField  过滤条件 名字和工号字段模糊搜索
     * @param startTime    根据创建时间过滤 开始时间
     * @param endTime      结束时间
     * @param filterAdmin  过滤是否是admin
     * @param filterStatus 筛选 是否是正常用户
     * @return
     */
    PageResponse<List<UserAuthDO>> selectUserAuth(int pageNo, int pageSize, String filterField,String startTime,String endTime,String filterAdmin,String filterStatus);


    /**
     * 添加用户 直接通过审核
     * @param empId
     * @return
     */
    List<String> insertUserAuth(List<String> empId);

    /**
     * 批量更新用户状态
     *
     * @param userIdList 用户Id
     * @param cancel false 取消用户白名单 改为正在排队中
     * @return
     */
    void updateUserStatus(List<Long> userIdList,Boolean cancel);


    /**
     * 根据empId转换成内部自增用户Id
     * @param empId 用户empId
     * @return
     */
    Long empId2UserId(String empId);

    /**
     * 根据自增的UserId查下工号
     * @param userId 用户ID
     * @return 工号
     */
    String selectEmpIdByUserId(Long userId);

    /**
     * 根据id查询用户信息
     *
     * @param ids
     * @return
     */
    List<UserAuthDO> getUserByIds(List<Long> ids);

    /**
     * 根据empId查询用户信息
     *
     * @param empIds
     * @return
     */
    List<UserAuthDO> getUserByEmpIds(List<String> empIds);

    /**
     * 更新权限范围
     * @param userId
     * @param allowAccessType
     */
    void updateAllowAccessType(UpdateAllowAccessTypeVO updateAllowAccessTypeVO);

    /**
     * 根据id查询用户信息
     *
     * @param id
     * @return
     */
    UserAuthDO selectByPrimaryKey(Long id);

}
