/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.request;

import java.math.BigDecimal;

/**
  助手配置实体
  字段详细说明请参考 AlgoBackendFieldUtil 类
 * <AUTHOR>
 * @version SceneConfigVO.java, v 0.1 2023年09月19日 下午2:48 yhw01352860
 */
public class SceneConfigRequestBean {
    /**
    * 模型环境 pre-预发/prod-生产/auto-默认
    * */
    private String     modelEnv;
    /**
    * 上文最大token数
    * */
    private Integer    maxToken;
    /**
    * 上文最大消息数
    * */
    private Integer maxRound;
    /**
    * 请求maya服务的整体超时时间
    * */
    private Long requestTimeOut;
    /**
    * 较小的topK值会使生成的文本更准确，较大的topK值则会增加生成文本的多样性
    * */
    private Integer    topK;
    /**
    * 概率值，0-1。较小的topP值会使生成的文本更准确但可能较短，较大的topP值则会增加生成文本的长度和多样性
    * */
    private String topP;
    /**
    * 温度参数
    * */
    private String     temperature;
    /**
    * 批次的窗口时间，单位毫秒。
    * */
    private Long batchTimeWindow;
    /**
    * 批次的最大请求数
    * */
    private Integer batchSize;
    /**
    * 间隔时间
    * */
    private Long commonStreamDataWaitTime;
    /**
    * 惩罚参数
    * */
    private String repetitionPenalty;
    /**
    * 模型输出数据的最长token数
    * */
    private Long outSeqLength;
    /**
    * 首包超时时间
    * */
    private Long firstStreamDataWaitTime;
    /**
    * AntGLM的连接时间
    * */
    private Long connTimeout;
    /**
    * AntGLM的最大输出长度
    * */
    private Long maxOutputLength;
    /**
    * 模型级别默认的systemPrompt
    * */
    private String systemPrompt;

    public String getModelEnv() {
        return modelEnv;
    }

    public void setModelEnv(String modelEnv) {
        this.modelEnv = modelEnv;
    }

    public Integer getMaxToken() {
        return maxToken;
    }

    public void setMaxToken(Integer maxToken) {
        this.maxToken = maxToken;
    }

    public Integer getMaxRound() {
        return maxRound;
    }

    public void setMaxRound(Integer maxRound) {
        this.maxRound = maxRound;
    }

    public Long getRequestTimeOut() {
        return requestTimeOut;
    }

    public void setRequestTimeOut(Long requestTimeOut) {
        this.requestTimeOut = requestTimeOut;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public String getTopP() {
        return topP;
    }

    public void setTopP(String topP) {
        this.topP = topP;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getRepetitionPenalty() {
        return repetitionPenalty;
    }

    public void setRepetitionPenalty(String repetitionPenalty) {
        this.repetitionPenalty = repetitionPenalty;
    }

    public Long getBatchTimeWindow() {
        return batchTimeWindow;
    }

    public void setBatchTimeWindow(Long batchTimeWindow) {
        this.batchTimeWindow = batchTimeWindow;
    }

    public Integer getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(Integer batchSize) {
        this.batchSize = batchSize;
    }

    public Long getCommonStreamDataWaitTime() {
        return commonStreamDataWaitTime;
    }

    public void setCommonStreamDataWaitTime(Long commonStreamDataWaitTime) {
        this.commonStreamDataWaitTime = commonStreamDataWaitTime;
    }

    public Long getOutSeqLength() {
        return outSeqLength;
    }

    public void setOutSeqLength(Long outSeqLength) {
        this.outSeqLength = outSeqLength;
    }

    public Long getFirstStreamDataWaitTime() {
        return firstStreamDataWaitTime;
    }

    public void setFirstStreamDataWaitTime(Long firstStreamDataWaitTime) {
        this.firstStreamDataWaitTime = firstStreamDataWaitTime;
    }

    public Long getConnTimeout() {
        return connTimeout;
    }

    public void setConnTimeout(Long connTimeout) {
        this.connTimeout = connTimeout;
    }

    public Long getMaxOutputLength() {
        return maxOutputLength;
    }

    public void setMaxOutputLength(Long maxOutputLength) {
        this.maxOutputLength = maxOutputLength;
    }

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }
}
