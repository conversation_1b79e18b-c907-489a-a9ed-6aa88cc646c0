package com.alipay.codegencore.model.model.analysis;

import java.util.Set;

/**
 * 经过静态分析后的代码结构
 *
 * <AUTHOR>
 * 创建时间 2022-08-15
 */
public class CodeStructResult extends AbstractCodeAnalysisResult {
    /**
     * 字段/方法名列表
     */
    private Set<String> fieldOrMethodSet;

    public Set<String> getFieldOrMethodSet() {
        return fieldOrMethodSet;
    }

    public void setFieldOrMethodSet(Set<String> fieldOrMethodSet) {
        this.fieldOrMethodSet = fieldOrMethodSet;
    }
}
