info:
  name: common-search
  description: 可以根据用户的提问搜索相关信息，并据此得到给出更准确和丰富的回复
  version: "v1"

# 插件需要的参数，其中query是用户提问，所有的插件都需要，参数中就不配置了
params:
  - name: sessionUid
    schema:
      type: string
    required: true
    # ${}里面的内容会替换成相关真实值
    default: '${sessionUid}'
    description: xxx
  - name: customConfig
    schema:
      type: string
    required: false
    description: 是一个json string，代码搜索请加上searchMode， codeLang 这两个字段，文本搜索可以不需要这个参数
  - name: searchResult
    schema:
      type: string
    required: false
    description: 搜索结果，如果这个字段非空，则会使用此字段提供的搜索结果，不再调用搜索接口
stages:
  # 前置接口调用，其接口返回是一个json对象，而且是一个只有一层的kv接口
  # 前置接口的返回值会被赋值给preResponse变量，可以被下面的配置使用
  - name: preRequest
    # api列表，当前只支持一个，后续会支持多个，这样可以兼容chatgpt插件模式
    api_list:
      - name: knowledge-search
        role: main
        url: https://codegencore.alipay.com/api/test/codegptSgchainPluginService
        responses:
          '200':
            description: 成功返回
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    prompt:
                      type: string
                      description: 访问总结大模型的prompt
                    urlMessage:
                      type: string
                      description: 参考文件信息，连接部分这里用了markdown格式做了超链接，
                  required:
                    - prompt
                    - urlMessage
          '500':
            description: 失败返回
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    code:
                      type: string
                      description: 错误码
                    msg:
                      type: string
                      description: 错误信息
                  required:
                    - code
                    - msg
  # 模型调用阶段
  # 模型调用阶段的返回值会被赋值给llmResult变量，可以被下面的配置使用
  - name: llm
    modelName: COMMON-SEARCH
    promptTemplate: "{preResponse.prompt}"
  # 后置接口调用阶段
  # 后置接口参数表是固定的，为{"params":{..},  "preResponse":{}, "llmResult":"xxxx"}
  # 其中params是用户提供的参数，preResponse是前置接口的返回值，llmResult是模型调用的返回值
  # 后置接口的返回值会被赋值给postResponse变量，可以被下面的配置使用
  - name: postRequest
    url: https://codegencore.alipay.com/api/test/codegptSgchainPluginService
    responses:
      '200':
        description: 成功返回
        content:
          application/json:
            schema:
              type: object
              properties:
                postAnswer:
                  type: string
                  description: 最终修正之后的输出
              required:
                - postAnswer
      '500':
        description: 失败返回
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: 错误码
                msg:
                  type: string
                  description: 错误信息
              required:
                - code
                - msg
  - name: summary
    # template是一个markdown模板，其中可以通过{}使用变量，变量的值是上面阶段的信息或者一些默认变量
    template: "{postResponse.postAnswer}"