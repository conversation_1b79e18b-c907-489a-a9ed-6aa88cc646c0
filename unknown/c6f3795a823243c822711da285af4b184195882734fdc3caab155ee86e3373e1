package com.alipay.codegencore.model.remote;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:45
 */
public enum RemoteAgentStatus {
    /**
     * 当 Task 第一次创建或者当完成了 required_action 时，它们应该几乎立即转换为 in_progress 中状态
     */
    @JsonProperty(value = "queued")
    QUEUED,

    /**
     * 表示 Task 正在执行中
     */
    @JsonProperty(value = "in_progress")
    IN_PROGRESS,

    /**
     * 表示 Task 执行完毕
     */
    @JsonProperty(value = "completed")
    COMPLETED,

    /**
     * 表示 Task 等执行被暂停，需要用户进行一些操作，使得 Task 重新开始恢复执行
     */
    @JsonProperty(value = "requires_action")
    REQUIRES_ACTION,

    /**
     * 表示 Task 执行失败，可以通过 last_error 字段查看
     */
    @JsonProperty(value = "failed")
    FAILED,

    /**
     * 表示 Task 执行被取消，当用户重启 Task 并设置 cancel=true时，Task 应该被取消执行
     */
    @JsonProperty(value = "cancelled")
    CANCELLED
}
