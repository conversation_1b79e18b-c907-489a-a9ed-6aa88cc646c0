package com.alipay.codegencore.model.remote;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:39
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InProgressDetail {

    @JsonProperty(value = "type", required = true)
    private String type;

    @JsonProperty(value = "input", required = true)
    private String input;

    @JsonProperty(value = "output", required = true)
    private String output;

    @JsonProperty(value = "finish")
    private Boolean finish;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    public String getOutput() {
        return output;
    }

    public void setOutput(String output) {
        this.output = output;
    }

    public Boolean getFinish() {
        return finish;
    }

    public void setFinish(Boolean finish) {
        this.finish = finish;
    }

}
