package com.alipay.codegencore;

import com.alipay.codegencore.service.common.ConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 * @version : UtilInitComponentTest.java, v 0.1 2023年10月24日 10:09 baoping Exp $
 */
public class UtilInitComponentTest {

    @Mock
    private ConfigService configService;

    @InjectMocks
    private UtilInitComponent utilInitComponent;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testInit() {

        Mockito.doReturn("dingSecret", "dingAccessToken", "dingTestSecret", "dingTestAccessToken",
                "host", "token", "{\n" +
                        "     \"aosHost\": \"https://searcher.alipay.com\",\n" +
                        "     \"opHost\": \"https://techasst.antgroup-inc.cn\",\n" +
                        "     \"appName\": \"codexmuse\",\n" +
                        "     \"aosToken\": \"240b2c834c40b972d06831dda37027cc\",\n" +
                        "     \"opToken\": \"c3f1cf6602f811ef9aae02420b210e80\",\n" +
                        "     \"aosEmpId\": \"347214\",\n" +
                        "     \"opEmpId\": \"347214\",\n" +
                        "     \"aosCookie\": \"spanner=vvRvgmKy/U4Q5UnEK7NwXV1dLOOp9mZs\",\n" +
                        "     \"opCookie\": \"spanner=VDcNXwxPRp5GAclfXOecJOOA3vXV0z14\",\n" +
                        "     \"page\": 1,\n" +
                        "     \"pageSize\": 10,\n" +
                        "     \"type\": \"enum OR class OR interface OR controller OR dao OR impl\",\n" +
                        "     \"skipCache\": true,\n" +
                        "     \"version\": \"ai\"\n" +
                        "}").when(configService).getConfigByKey(Mockito.anyString(), Mockito.anyBoolean());
        utilInitComponent.init();
    }
}
