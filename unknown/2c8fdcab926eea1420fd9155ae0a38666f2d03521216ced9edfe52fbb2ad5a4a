package com.alipay.codegencore.service.utils;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.SegmentInfo;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.model.rag.DocSearchResultItem;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.response.PluginStreamPartResponse;
import com.alipay.codegencore.service.impl.model.StreamDataQueueUtilService;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zcache.serialize.BytesObject;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ChatUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger( ChatUtils.class );

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    /**
     * 线下环境请求chat接口
     * @param modelName
     * @param url
     * @param token
     * @param chatCompletionRequest
     * @return
     */
    public static String chatInDevAndTest(String modelName, String url, String token, ChatCompletionRequest chatCompletionRequest){
        String responseStr = null;
        try{
            chatCompletionRequest.setStream(false);
            responseStr = HttpClient.post(url)
                    .header(AppConstants.HTTP_HEADER_CODEGPT_USER_KEY, AppConstants.CODEGPT_TOKEN_USER)
                    .header(AppConstants.HTTP_HEADER_CODEGPT_TOKEN_KEY, token)
                    .content(JSON.toJSONString(chatCompletionRequest))
                    .syncExecuteWithExceptionThrow(60000L);
        }catch(Exception e){
            LOGGER.error("{} completion request failed, user:codegpt",modelName, e);
            throw new RuntimeException(e);
        }
        LOGGER.info("{} completion user:codegpt, result:{}",modelName, responseStr);
        JSONObject response = JSON.parseObject(responseStr);
        if(!response.containsKey("data") || response.getInteger("errorCode")!=0){
            throw new RuntimeException(String.format("response invalid: %s",responseStr));
        }
        return response.getString("data");
    }

    /**
     * 设置servlet为event-stream格式
     * @param servletResponse
     */
    public static final void setServletToEventStream(HttpServletResponse servletResponse){
        servletResponse.setContentType("text/event-stream");
        servletResponse.setCharacterEncoding("UTF-8");
    }

    /**
     * 组装流式数据包
     * @param answer 答案
     * @param errorType 错误类型
     * @return 流式数据包
     */
    public static ChatStreamPartResponse getChatStreamPart(String answerUid, String answer, ChatFunctionCall functionCallDelta, ResponseEnum errorType, CheckResultModel checkResultModel){
        if (StringUtils.isBlank(answerUid)) {
            answerUid = ShortUid.getUid();
        }
        ChatStreamPartResponse chatStreamPartResponse = new ChatStreamPartResponse();
        chatStreamPartResponse.setCreated((int) (System.currentTimeMillis() / 1000));
        chatStreamPartResponse.setId(answerUid);
        chatStreamPartResponse.setObject("com.alipay.generate.code");

        ChatStreamPartResponse.Choice chatCompletionChoice = new ChatStreamPartResponse.Choice();
        chatCompletionChoice.setIndex(0);
        chatCompletionChoice.setFinishReason(errorType!=null?errorType.name():null);
        chatCompletionChoice.setCheckResultModel(checkResultModel);
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole(ChatRoleEnum.ASSISTANT.getName());
        chatMessage.setFunctionCall(functionCallDelta);
        chatMessage.setContent(answer);
        chatCompletionChoice.setDelta(chatMessage);
        chatStreamPartResponse.setChoices(Lists.newArrayList(chatCompletionChoice));
        return chatStreamPartResponse;
    }

    /**
     * 组装插件回答数据包
     */
    public static PluginStreamPartResponse getPluginStreamPart(PluginInfo pluginInfo,Integer index, String requestId, String content, String stage, String stageName, String finishReason, boolean keyContent, CheckResultModel checkResultModel, List<String> stageList){
        if (StringUtils.isBlank(requestId)) {
            requestId = ShortUid.getUid();
        }

        return getPluginStreamPart(requestId, pluginInfo, index, content, stage, stageName, finishReason, keyContent, checkResultModel, stageList);
    }

    /**
     * 组装插件回答数据包
     * @param requestId
     * @param index
     * @param content
     * @param stage
     * @param stageName
     * @param finishReason
     * @param keyContent
     * @param checkResultModel
     * @param stageList
     * @return
     */
    public static PluginStreamPartResponse getPluginStreamPart(String requestId,
                                                               PluginInfo pluginInfo,
                                                               Integer index,
                                                               String content,
                                                               String stage,
                                                               String stageName,
                                                               String finishReason,
                                                               boolean keyContent,
                                                               CheckResultModel checkResultModel,
                                                               List<String> stageList){
        if (StringUtils.isBlank(requestId)) {
            requestId = ShortUid.getUid();
        }

        PluginStreamPartResponse pluginStreamPartResponse = new PluginStreamPartResponse();
        pluginStreamPartResponse.setId(requestId);
        pluginStreamPartResponse.setPluginInfo(pluginInfo);
        pluginStreamPartResponse.setIndex(index);
        pluginStreamPartResponse.setStage(stage);
        pluginStreamPartResponse.setStageName(stageName);
        pluginStreamPartResponse.setFinishReason(finishReason);
        pluginStreamPartResponse.setContent(content);
        pluginStreamPartResponse.setKeyContent(keyContent);
        pluginStreamPartResponse.setCheckResultModel(checkResultModel);
        pluginStreamPartResponse.setStageList(stageList);

        return pluginStreamPartResponse;
    }

    /**
     * 用sse数据格式刷新servlet缓冲器
     * @param httpServletResponse servlet
     * @param data 要发送的数据
     */
    public static void flushSseResponse(HttpServletResponse httpServletResponse, String data){
        try {
            httpServletResponse.getWriter().write("data: ");
            httpServletResponse.getWriter().write(data);
            httpServletResponse.getWriter().write("\n\n");
            httpServletResponse.getWriter().flush();
        } catch (IOException e) {
            LOGGER.error("stream error", e);
            throw new BizException(ResponseEnum.AI_CALL_ERROR, e);
        }
    }

    /**
     * 用sse数据格式刷新servlet缓冲器，不包含 data：
     * @param httpServletResponse
     * @param data
     */
    public static void flushSseResponseNoData(HttpServletResponse httpServletResponse, String data){
        try {
            httpServletResponse.getWriter().write(data);
            httpServletResponse.getWriter().write("\n");
            httpServletResponse.getWriter().flush();
        } catch (IOException e) {
            LOGGER.error("stream error", e);
            throw new BizException(ResponseEnum.AI_CALL_ERROR, e);
        }
    }

    /**
     * 判断是否需要刷新缓冲区
     * 两级策略
     * 1. 如果token中有句子结束标点,则判断缓冲区中的token数量是否超过句子最大长度
     * 2. 如果token中没有句子结束标点,则判断缓冲区中的token数量是否超过缓冲区最大长度
     *
     * @param tokenBuffer       缓冲区
     * @param sentenceLength 句子最大长度
     * @param bufferLength   缓冲区最大长度
     * @return 是否需要刷新缓冲区
     */
    public static boolean shouldFlushChatBuffer(String token, List<String> tokenBuffer, int sentenceLength, int bufferLength) {
        boolean tokenContainsPunctuation = CommonUtils.containsPunctuation(token);
        if (tokenContainsPunctuation && !tokenBuffer.isEmpty()) {
            return tokenBuffer.size() > sentenceLength;
        }
        return tokenBuffer.size() > bufferLength;
    }


    /**
     * 处理流式数据
     * @param delta 流式数据
     * @param tbaseCacheManager tbase缓存管理器
     * @param uniqueAnswerId 答案唯一id，用于获取对应的tbase缓存key
     */
    public static void handleEveryStreamData(ChatMessage delta, String finishReason, RefreshableCommonTbaseCacheManager tbaseCacheManager, String uniqueAnswerId){
        JSONObject contentJson = new JSONObject();
        if(delta == null){
            contentJson.put("content", null);
        }else{
            contentJson.put("content", delta.getContent());
            if(delta.getFunctionCall()!=null){
                contentJson.put("functionCall", delta.getFunctionCall());
            }
        }

        if (StringUtils.isNotBlank(finishReason)) {
            contentJson.put("finishReason", finishReason);
        }
        BytesObject bytesObject = new BytesObject(contentJson.toJSONString().getBytes(StandardCharsets.UTF_8));
        String streamAnswerId = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, uniqueAnswerId);
        tbaseCacheManager.rpush(streamAnswerId, bytesObject);
    }

    /**
     * 转发流式数据
     *
     * <AUTHOR>
     * @since 2024.08.20
     * @param data data
     * @param tbaseCacheManager tbaseCacheManager
     * @param uniqueAnswerId uniqueAnswerId
     */
    public static void handleForwardEveryStreamData(String data, RefreshableCommonTbaseCacheManager tbaseCacheManager, String uniqueAnswerId){
        BytesObject bytesObject = new BytesObject(data.getBytes(StandardCharsets.UTF_8));
        String streamAnswerId = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, uniqueAnswerId);
        tbaseCacheManager.rpush(streamAnswerId, bytesObject);
    }
    /**
     * 转发流式数据到队列
     *
     * <AUTHOR>
     * @since 2024.08.20
     * @param data data
     * @param streamDataQueueUtilService streamDataQueueUtilService
     * @param uniqueAnswerId uniqueAnswerId
     */
    public static void handleForwardQueueStreamData(String data, StreamDataQueueUtilService streamDataQueueUtilService, String uniqueAnswerId){
        BytesObject bytesObject = new BytesObject(data.getBytes(StandardCharsets.UTF_8));
        String streamAnswerId = String.format("%s%s", AppConstants.STREAM_DATA_PREFIX, uniqueAnswerId);
        streamDataQueueUtilService.rpush(streamAnswerId, bytesObject);
    }


    /**
     * 流式传输结束时，组装相关数据
     * @param checkResultModel 审核结果
     * @param finishReason 结束原因,空则从审核结果中取
     * @param answerUid 答案落库的uid
     * @param answerDb 落库的答案
     * @param answerFlush 返回给调用方的答案
     * @return 组装的数据，key为用于刷新给前端的数据，value为用于落库的数据
     */
    public static Pair<ChatStreamPartResponse, StreamResponseModel> getStopStreamResponse(CheckResultModel checkResultModel,
                                                                                   ResponseEnum finishReason,
                                                                                   ChatFunctionCall functionCall,
                                                                                   String answerUid,
                                                                                   String answerDb,
                                                                                   String answerFlush) {
        if (StringUtils.isBlank(answerUid)) {
            answerUid = ShortUid.getUid();
        }
        // 增加判空逻辑
        if (finishReason == null && checkResultModel != null) {
            finishReason = checkResultModel.getResponseEnum();
        }

        // 当cancel时候， 保证用户看到的/落库/审核一致，
        // 所以不需要把cancel的明文在对话框中告诉用户
        if (StringUtils.isBlank(answerFlush)
                && finishReason != null
                && finishReason != ResponseEnum.USER_CANCELED) {
            answerFlush = finishReason.getErrorMsg();
        }

        ChatStreamPartResponse chatStreamPartResponse = ChatUtils.getChatStreamPart(answerUid, answerFlush, functionCall, finishReason,checkResultModel);

        StreamResponseModel streamResponseModel = new StreamResponseModel();
        streamResponseModel.setAnswerUid(answerUid);
        streamResponseModel.setAnswerMessage(new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), answerDb));
        streamResponseModel.setCheckResultModel(checkResultModel);
        if(!checkResultModel.isAllCheckRet()){
            chatStreamPartResponse.setClear(true);
            streamResponseModel.setErrorMsg(answerFlush);
            streamResponseModel.setClear(true);
        }
        if (finishReason == ResponseEnum.ANSWER_OUTPUT_TIME_OUT
                || finishReason == ResponseEnum.USER_CANCELED ) {
            streamResponseModel.setServiceAbnormalResp(finishReason.toString());
        }
        // 对于取消不需要errorMsg,只对超时进行记录
        if(finishReason == ResponseEnum.ANSWER_OUTPUT_TIME_OUT){
            streamResponseModel.setErrorMsg(finishReason.toString());
        }

        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("web stream info : {}", JSON.toJSONString(chatStreamPartResponse));
        }

        return new Pair<>(chatStreamPartResponse, streamResponseModel);
    }

    /**
     * 将streamBuffer转换为ChatMessage
     * @param chatStreamBuffer
     * @return
     */
    public static ChatMessage streamBufferToChatMessage(ChatStreamBuffer chatStreamBuffer){
        String content = chatStreamBuffer.getContent().toString();
        if(content.startsWith(AppConstants.DEFAULT_CODEGPT_FUNCTION_CALL_START_TOKEN)){
            try {
                String realContent = content.substring(AppConstants.DEFAULT_CODEGPT_FUNCTION_CALL_START_TOKEN.length());
                JSONObject functionCallContentInfo = JSON.parseObject(realContent);
                String functionCallContent = functionCallContentInfo.getString("content");
                String functionName = functionCallContentInfo.getString("name");
                String arguments = functionCallContentInfo.getString("arguments");
                return new ChatMessage(ChatRoleEnum.ASSISTANT.getName(),
                        functionCallContent,
                        null,
                        new ChatFunctionCall(functionName, arguments));
            }catch (Exception e){
                LOGGER.error("parse function call error, content:{}", content, e);
                throw new BizException(ResponseEnum.AI_CALL_ERROR, e);
            }
        }

        ChatFunctionCall functionCall = chatStreamBuffer.getFunctionCall().toChatFunctionCall();

        return new ChatMessage(ChatRoleEnum.ASSISTANT.getName(),
                chatStreamBuffer.getContent().length() == 0 ? null : chatStreamBuffer.getContent().toString(),
                null,
                functionCall);
    }

    /**
     * 加了try catch的lpop
     *
     * @param noneSerializationCacheManager 缓存管理器
     * @param key                           缓存中的key
     * @return 返回弹出的元素的值，如果缓存中不存在该key或者该key对应的值为null，则返回null
     */
    public static String safeTbaseLpop(RefreshableCommonTbaseCacheManager noneSerializationCacheManager, String key) {
        try {
            // 从缓存中弹出指定key对应的列表的第一个元素
            BytesObject streamData = (BytesObject) noneSerializationCacheManager.lpop(key);
            if (streamData == null) {
                return null;
            }
            // 将弹出的元素转换为字符串并返回
            return StringUtils.toEncodedString(streamData.getBytes(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            // 记录日志并返回null
            LOGGER.error("tbase lpop error, key:{}", key, e);
            return null;
        }
    }

    /**
     * 把文档搜索结果添加进上下文
     * @param chatCompletionRequest
     */
    public static void addDocSearchResultToChatRequest(List<SegmentInfo> docSearchResult, ChatCompletionRequest chatCompletionRequest) {
        if (CollectionUtils.isEmpty(docSearchResult)) {
            return;
        }

        chatCompletionRequest.setDocs(docSearchResult.stream().map(DocSearchResultItem::new).collect(Collectors.toList()));
    }

    /**
     * 获取到文档列表并更新segmentInfo中的docIndex，注意docIndex的值从1开始
     * @param docSearchResult
     * @return
     */
    public static JSONObject exactDocsInfoAndUpdateSegmentInfo(List<SegmentInfo> docSearchResult){
        List<String> docUidList = new ArrayList<>();
        JSONObject docsInfo = new JSONObject();
        for (SegmentInfo segmentInfo : docSearchResult) {
            String docUid = segmentInfo.getDocumentUid();
            if(!docUidList.contains(docUid)){
                docUidList.add(docUid);

                JSONObject docInfo = new JSONObject();
                docInfo.put("uid", docUid);
                docInfo.put("title", segmentInfo.getDocumentName());
                docInfo.put("url", segmentInfo.getQuoteDocumentUrl());
                docsInfo.put(String.valueOf(docUidList.size()), docInfo);
            }
            int docIndex = docUidList.indexOf(docUid);
            //docInx从1开始，所以需要加1
            segmentInfo.setDocumentIdx(String.valueOf((docIndex+1)));
        }
        return docsInfo;
    }


}
