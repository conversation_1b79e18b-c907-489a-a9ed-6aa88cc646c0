package com.alipay.codegencore.model.openai;

/**
 * aci信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.05.31
 */
public class AciInfo {
    /**
     * 提供大模型识别插件的名字
     */
    private String name;
    /**
     * 提供大模型识别插件的描述
     */
    private String description;
    /**
     * aci插件类型
     */
    private String type;
    /**
     * aci插件流水线名
     */
    private String pipelineName;
    /**
     * aci插件流水线模板id
     */
    private String pipelineTemplateId;
    /**
     * aci插件工程名
     */
    private String projectName;
    /**
     * aci插件项目id
     */
    private String projectId;
    /**
     * aci插件项目分支
     */
    private String branch;
    /**
     * aci插件vcsId
     */
    private String repoId;
    /**
     * aci插件平台id
     */
    private String platformId;
    /**
     * aci插件组件名
     */
    private String component;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPipelineTemplateId() {
        return pipelineTemplateId;
    }

    public void setPipelineTemplateId(String pipelineTemplateId) {
        this.pipelineTemplateId = pipelineTemplateId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getPipelineName() {
        return pipelineName;
    }

    public void setPipelineName(String pipelineName) {
        this.pipelineName = pipelineName;
    }

    public String getRepoId() {
        return repoId;
    }

    public void setRepoId(String repoId) {
        this.repoId = repoId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }
}
