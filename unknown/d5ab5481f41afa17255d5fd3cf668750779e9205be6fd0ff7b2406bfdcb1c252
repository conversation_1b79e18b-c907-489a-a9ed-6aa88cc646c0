package com.alipay.codegencore.service.common.segment;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.SegmentationStrategyTypeEnum;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version : DelimiterSegmentationStrategy.java, v 0.1 2024年01月11日 14:13 baoping Exp $
 */
@Service
public class DelimiterSegmentationStrategy implements SegmentationStrategy{
    /**
     * 特殊正则表达式字符
     */
    private static final Pattern SPECIAL_REGEX_CHARS = Pattern.compile("[{}()\\[\\].+*?^$\\\\|]");

    /**
     * 默认分隔符
     */
    private static final List<String> DEFAULT_DELIMITERS = Arrays.asList("\n", "\n\n", ".", "?", "!", ";", "。", "？", "！", "；");

    /**
     * @param text
     * @param documentChatConfig
     * @return
     */
    @Override
    public List<String> segment(String text, JSONObject documentChatConfig) {
        int maxLength = documentChatConfig.getInteger("everyPartMaxSize");

        // 如果配置了分隔符，则使用配置的分隔符
        List<String> delimiters;
        if (documentChatConfig.getJSONArray("delimiters") != null) {
            delimiters = documentChatConfig.getJSONArray("delimiters").toJavaList(String.class);
        } else {
            delimiters = DEFAULT_DELIMITERS;
        }

        return splitTextByDelimiters(text, delimiters, maxLength);
    }

    @Override
    public SegmentationStrategyTypeEnum getType() {
        return SegmentationStrategyTypeEnum.DELIMITER_STRATEGY;
    }


    /**
     * 根据分隔符对文本进行分段
     * @param text 文本
     * @param delimiters 分隔符列表
     * @param maxLength 每段的最大长度
     * @return
     */
    private static List<String> splitTextByDelimiters(String text, List<String> delimiters, int maxLength) {
        List<String> segments = new ArrayList<>();

        // 转义正则表达式的特殊字符
        List<String> escapedDelimiters = new ArrayList<>();
        for (String delimiter : delimiters) {
            String escapedDelimiter = SPECIAL_REGEX_CHARS.matcher(delimiter).replaceAll("\\\\$0");
            escapedDelimiters.add(escapedDelimiter);
        }

        // 使用零宽断言保留分隔符
        String regex = "(?<=(" + String.join("|", escapedDelimiters) + "))|(?=(" + String.join("|", escapedDelimiters) + "))";

        // 使用正则表达式来切分字符串，保留分隔符
        String[] splitByDelimiters = text.split(regex);

        StringBuilder currentSegment = new StringBuilder();
        for (String segment : splitByDelimiters) {
            if (segment.isEmpty()) {
                continue;
            }
            segment = segment.replace("\n", "");
            if (currentSegment.length() + segment.length() <= maxLength) {
                currentSegment.append(segment);
            } else {
                if (currentSegment.length() > 0) {
                    segments.add(currentSegment.toString());
                    currentSegment.setLength(0);
                }

                // 如果单个片段超过最大长度，需要进一步分割
                while (segment.length() > maxLength) {
                    segments.add(segment.substring(0, maxLength));
                    segment = segment.substring(maxLength);
                }

                currentSegment.append(segment);
            }
        }

        if (currentSegment.length() > 0) {
            segments.add(currentSegment.toString());
        }

        return segments;
    }
}
