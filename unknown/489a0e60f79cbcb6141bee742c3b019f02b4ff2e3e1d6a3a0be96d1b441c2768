/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.codegpt;

import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 请求LanguageModelService参数集合，后期可能能够精简
 *
 * <AUTHOR>
 * @version LanguageModelRequsetParams.java, v 0.1 2023年04月22日 12:01 xiaobin
 */
public class PluginServiceRequestContext {

    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户信息
     */
    private UserAuthDO userAuthDO;

    /**
     * 用户原始请求的模型名
     */
    private String userOriginalModel;

    /**
     * 数据库中的算法模型配置记录
     * 这个对象中的 model 名称可能和 userOriginalModel 不一致（如果 这个记录是 jump 之后的情况）
     */
    private AlgoBackendDO algoBackendDO;

    /**
     * 当前会话的Uid
     */
    private ChatSessionDO chatSessionDO;

    /**
     * 当前消息 （消息表单使用）
     */
    private List<ChatMessageDO> chatMessageDOList;

    /**
     * 插件信息
     */
    private PluginDO pluginDO;

    /**
     * 消息的uid
     */
    private String requestId;

    /**
     * 是否是压测
     */
    private boolean stressTest;

    /**
     * 会话请求参数
     */
    private ChatCompletionRequest chatCompletionRequest;

    /**
     * 插件结果处理器
     */
    private Consumer<StreamResponseModel> pluginResultHandler;

    /**
     * 流式会话的响应处理器
     */
    private Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer;

    /**
     * 删除TBase的key
     */
    private Consumer<String> needDelTBaseKey;

    /**
     * 是否为重新生成
     */
    private boolean isRegenerate;

    /**
     * 回答的唯一id
     * sessionId + "_" + queryIndex + "_" + generationIndex
     */
    private String uniqueAnswerId;

    /**
     * 回答uid
     */
    private String answerUid;

    /**
     * 调用插件所需参数
     */
    private Map<String, Object> pluginParams;
    /**
     * 在插件调用中的顺序
     */
    private Integer pluginIndex;
    /**
     * 插件信息
     */
    private PluginInfo pluginInfo;
    /**
     * 场景信息
     */
    private SceneDO sceneDO;
    /**
     * 场景配置-下一个调用的方法信息
     */
    private ChatFunctionCall nextFunctionCall;

    /**
     * 流式会话需要参数构造器
     *
     * @param requestId
     * @param userName
     * @param stressTest
     * @param request
     * @param pluginResultHandler
     */
    public PluginServiceRequestContext(ChatSessionDO chatSessionDO,
                                       String requestId,
                                       String userName,
                                       UserAuthDO userAuthDO,
                                       boolean stressTest,
                                       AlgoBackendDO algoBackendDO,
                                       ChatCompletionRequest request,
                                       Consumer<StreamResponseModel> pluginResultHandler,
                                       boolean isRegenerate) {
        this.chatSessionDO = chatSessionDO;
        this.requestId = requestId;
        this.userAuthDO = userAuthDO;
        this.userName = userName;
        this.stressTest = stressTest;
        this.algoBackendDO = algoBackendDO;
        this.chatCompletionRequest = request;
        this.pluginResultHandler = pluginResultHandler;
        this.isRegenerate = isRegenerate;
    }

    /**
     * 继续流式对话参数构造器
     */
    public PluginServiceRequestContext(ChatSessionDO chatSessionDO,
                                       List<ChatMessageDO> chatMessageDOList,
                                       String requestId,
                                       String userName,
                                       UserAuthDO userAuthDO,
                                       ChatCompletionRequest request,
                                       Consumer<StreamResponseModel> pluginResultHandler
                                       ) {
        this.chatSessionDO = chatSessionDO;
        this.chatMessageDOList = chatMessageDOList;
        this.requestId = requestId;
        this.userAuthDO = userAuthDO;
        this.userName = userName;
        this.chatCompletionRequest = request;
        this.pluginResultHandler = pluginResultHandler;
    }

    /**
     * 根据模型请求参数构造插件请求
     * @param gptAlgModelServiceRequest 模型请求参数
     */
    public PluginServiceRequestContext(GptAlgModelServiceRequest gptAlgModelServiceRequest){
        this.userName = gptAlgModelServiceRequest.getUserName();
        this.userOriginalModel = gptAlgModelServiceRequest.getUserOriginalModel();
        this.algoBackendDO = gptAlgModelServiceRequest.getAlgoBackendDO();
        this.requestId = gptAlgModelServiceRequest.getRequestId();
        this.stressTest = gptAlgModelServiceRequest.isStressTest();
        this.chatCompletionRequest = gptAlgModelServiceRequest.getChatCompletionRequest();
        this.needDelTBaseKey = gptAlgModelServiceRequest.getNeedDelTBaseKey();
        this.isRegenerate = gptAlgModelServiceRequest.isRegenerate();
        this.uniqueAnswerId = gptAlgModelServiceRequest.getUniqueAnswerId();
    }

    public PluginServiceRequestContext() {
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public UserAuthDO getUserAuthDO() {
        return userAuthDO;
    }

    public void setUserAuthDO(UserAuthDO userAuthDO) {
        this.userAuthDO = userAuthDO;
    }

    public String getUserOriginalModel() {
        return userOriginalModel;
    }

    public void setUserOriginalModel(String userOriginalModel) {
        this.userOriginalModel = userOriginalModel;
    }

    public AlgoBackendDO getAlgoBackendDO() {
        return algoBackendDO;
    }

    public void setAlgoBackendDO(AlgoBackendDO algoBackendDO) {
        this.algoBackendDO = algoBackendDO;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean isStressTest() {
        return stressTest;
    }

    public void setStressTest(boolean stressTest) {
        this.stressTest = stressTest;
    }

    public ChatCompletionRequest getChatCompletionRequest() {
        return chatCompletionRequest;
    }

    public void setChatCompletionRequest(ChatCompletionRequest chatCompletionRequest) {
        this.chatCompletionRequest = chatCompletionRequest;
    }

    public Consumer<StreamResponseModel> getPluginResultHandler() {
        return pluginResultHandler;
    }

    public void setPluginResultHandler(Consumer<StreamResponseModel> pluginResultHandler) {
        this.pluginResultHandler = pluginResultHandler;
    }

    public boolean isRegenerate() {
        return isRegenerate;
    }

    public void setRegenerate(boolean regenerate) {
        isRegenerate = regenerate;
    }

    public Consumer<String> getNeedDelTBaseKey() {
        return needDelTBaseKey;
    }

    public void setNeedDelTBaseKey(Consumer<String> needDelTBaseKey) {
        this.needDelTBaseKey = needDelTBaseKey;
    }

    public String getUniqueAnswerId() {
        return uniqueAnswerId;
    }

    public void setUniqueAnswerId(String uniqueAnswerId) {
        this.uniqueAnswerId = uniqueAnswerId;
    }

    public String getAnswerUid() {
        return answerUid;
    }

    public void setAnswerUid(String answerUid) {
        this.answerUid = answerUid;
    }

    public Consumer<NewPluginStreamPartResponse> getPluginStreamPartResponseConsumer() {
        return pluginStreamPartResponseConsumer;
    }

    public void setPluginStreamPartResponseConsumer(Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer) {
        this.pluginStreamPartResponseConsumer = pluginStreamPartResponseConsumer;
    }

    public Map<String, Object> getPluginParams() {
        return pluginParams;
    }

    public void setPluginParams(Map<String, Object> pluginParams) {
        this.pluginParams = pluginParams;
    }

    public Integer getPluginIndex() {
        return pluginIndex;
    }

    public void setPluginIndex(Integer pluginIndex) {
        this.pluginIndex = pluginIndex;
    }

    public PluginInfo getPluginInfo() {
        return pluginInfo;
    }

    public void setPluginInfo(PluginInfo pluginInfo) {
        this.pluginInfo = pluginInfo;
    }

    public ChatSessionDO getChatSessionDO() {
        return chatSessionDO;
    }

    public void setChatSessionDO(ChatSessionDO chatSessionDO) {
        this.chatSessionDO = chatSessionDO;
    }

    public SceneDO getSceneDO() {
        return sceneDO;
    }

    public void setSceneDO(SceneDO sceneDO) {
        this.sceneDO = sceneDO;
    }

    public ChatFunctionCall getNextFunctionCall() {
        return nextFunctionCall;
    }

    public void setNextFunctionCall(ChatFunctionCall nextFunctionCall) {
        this.nextFunctionCall = nextFunctionCall;
    }

    public List<ChatMessageDO> getChatMessageDOList() {
        return chatMessageDOList;
    }

    public void setChatMessageDOList(List<ChatMessageDO> chatMessageDOList) {
        this.chatMessageDOList = chatMessageDOList;
    }

    public PluginDO getPluginDO() {
        return pluginDO;
    }

    public void setPluginDO(PluginDO pluginDO) {
        this.pluginDO = pluginDO;
    }
}