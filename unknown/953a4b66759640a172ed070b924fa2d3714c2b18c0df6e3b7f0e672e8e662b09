/**
 * Scaffolding file used to store all the setups needed to run 
 * tests automatically generated by smartunit
 */

package smartunit.com.alipay.codegencore.utils.codescan;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.smartunit.runtime.annotation.SmartSuiteClassExclude;
import org.smartunit.runtime.sandbox.Sandbox;

import static org.smartunit.shaded.org.mockito.Mockito.mock;
import static org.smartunit.shaded.org.mockito.Mockito.withSettings;
@SmartSuiteClassExclude
public class JavaParserBaseVisitor_SSTest_scaffolding {

  @org.junit.Rule 
  public org.smartunit.runtime.vnet.NonFunctionalRequirementRule nfr = new org.smartunit.runtime.vnet.NonFunctionalRequirementRule();

  private static final java.util.Properties defaultProperties = (java.util.Properties) java.lang.System.getProperties().clone(); 

  private final org.smartunit.runtime.thread.ThreadStopper threadStopper =  new org.smartunit.runtime.thread.ThreadStopper (org.smartunit.runtime.thread.KillSwitchHandler.getInstance(), 3000);


  @BeforeClass 
  public static void initSmartSuiteFramework() { 
    org.smartunit.runtime.RuntimeSettings.className = "com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor"; 
    org.smartunit.runtime.GuiSupport.initialize(); 
    org.smartunit.runtime.RuntimeSettings.maxNumberOfThreads = 100; 
    org.smartunit.runtime.RuntimeSettings.maxNumberOfIterationsPerLoop = Long.MAX_VALUE; 
    org.smartunit.runtime.RuntimeSettings.mockSystemIn = true; 
    org.smartunit.runtime.RuntimeSettings.sandboxMode = org.smartunit.runtime.sandbox.Sandbox.SandboxMode.RECOMMENDED; 
    org.smartunit.runtime.sandbox.Sandbox.initializeSecurityManagerForSUT(); 
    org.smartunit.runtime.classhandling.JDKClassResetter.init();
    setSystemProperties();
    initializeClasses();
    org.smartunit.runtime.Runtime.getInstance().resetRuntime(); 
    try { initMocksToAvoidTimeoutsInTheTests(); } catch(ClassNotFoundException e) {} 
  } 

  @AfterClass 
  public static void clearSmartSuiteFramework(){ 
    resetClasses(); 
    Sandbox.resetDefaultSecurityManager(); 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
  } 

  @Before 
  public void initTestCase(){ 
    threadStopper.storeCurrentThreads();
    threadStopper.startRecordingTime();
    org.smartunit.runtime.jvm.ShutdownHookHandler.getInstance().initHandler(); 
    org.smartunit.runtime.sandbox.Sandbox.goingToExecuteSUTCode(); 
    setSystemProperties(); 
    org.smartunit.runtime.GuiSupport.setHeadless(); 
    org.smartunit.runtime.Runtime.getInstance().resetRuntime(); 
    org.smartunit.runtime.agent.InstrumentingAgent.activate(); 
  } 

  @After 
  public void doneWithTestCase(){ 
    threadStopper.killAndJoinClientThreads();
    org.smartunit.runtime.jvm.ShutdownHookHandler.getInstance().safeExecuteAddedHooks(); 
    org.smartunit.runtime.classhandling.JDKClassResetter.reset(); 
    org.smartunit.runtime.classhandling.ClassStateSupport.resetCUT(); 
    org.smartunit.runtime.sandbox.Sandbox.doneWithExecutingSUTCode(); 
    org.smartunit.runtime.agent.InstrumentingAgent.deactivate(); 
    org.smartunit.runtime.GuiSupport.restoreHeadlessMode(); 
  } 

  public static void setSystemProperties() {
 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
    java.lang.System.setProperty("java.io.tmpdir", "/var/folders/2g/v426t5v53msd4lghgvvbxq3h0000gn/T/"); 
  }
  private static void initMocksToAvoidTimeoutsInTheTests() throws ClassNotFoundException { 
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationConstantRestContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationTypeBodyContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationTypeDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationTypeElementRestContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ArrayCreatorRestContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ArrayInitializerContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$BlockContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$BlockStatementContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$CatchClauseContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$CatchTypeContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassBodyContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassBodyDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassCreatorRestContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassOrInterfaceModifierContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassOrInterfaceTypeContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassTypeContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$CompilationUnitContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ConstantDeclaratorContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ConstructorDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$DefaultValueContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ElementValueArrayInitializerContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ElementValueContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ElementValuePairContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnhancedForControlContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnumBodyDeclarationsContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnumConstantContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnumConstantsContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnumDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ExplicitGenericInvocationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ExplicitGenericInvocationSuffixContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ExpressionContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ExpressionListContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FieldDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FinallyBlockContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FloatLiteralContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ForControlContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ForInitContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FormalParameterContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FormalParametersContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$GenericConstructorDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$GenericMethodDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$GuardedPatternContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$IdentifierContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ImportDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InnerCreatorContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$IntegerLiteralContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceBodyContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceBodyDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceCommonBodyDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceMethodDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceMethodModifierContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaBodyContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaExpressionContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaLVTIListContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaLVTIParameterContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaParametersContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LastFormalParameterContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LiteralContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LocalTypeDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$MemberDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$MethodBodyContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$MethodCallContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$MethodDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ModuleBodyContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ModuleDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ModuleDirectiveContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$NonWildcardTypeArgumentsContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$NonWildcardTypeArgumentsOrDiamondContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$PackageDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ParExpressionContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$PatternContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$PrimaryContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$PrimitiveTypeContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$QualifiedNameContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$QualifiedNameListContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ReceiverParameterContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordBodyContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordComponentContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordComponentListContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordHeaderContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RequiresModifierContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ResourceContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ResourceSpecificationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ResourcesContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$StatementContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SuperSuffixContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchBlockStatementGroupContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchExpressionContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchLabeledRuleContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchRuleOutcomeContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeArgumentContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeArgumentsContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeDeclarationContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeListContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeParametersContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeTypeOrVoidContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableDeclaratorContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableDeclaratorsContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableInitializerContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableModifierContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("org.antlr.v4.runtime.ParserRuleContext", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("org.antlr.v4.runtime.tree.ParseTree", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("org.antlr.v4.runtime.tree.RuleNode", false, JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
  }

  private static void initializeClasses() {
    org.smartunit.runtime.classhandling.ClassStateSupport.initializeClasses(JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader() , ""
    );
  } 

  private static void resetClasses() {
    org.smartunit.runtime.classhandling.ClassResetter.getInstance().setClassLoader(JavaParserBaseVisitor_SSTest_scaffolding.class.getClassLoader()); 

    org.smartunit.runtime.classhandling.ClassStateSupport.resetClasses();
  }
}
