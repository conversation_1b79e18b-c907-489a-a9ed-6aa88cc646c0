/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.common;

/**
 * <AUTHOR>
 * @version DimaOpenApiService.java, v 0.1 2023年11月03日 上午9:49 lqb01337046
 */
public interface DimaOpenApiService {

    /**
     * 再dima平台创建工作项
     *
     * @param empId 指定人empId
     * @param content 内容
     * @param title 标题
     * @return
     */
    Boolean createWorkItem(String empId,String content,String title);

}
