package com.alipay.codegencore.utils;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * md5生成的工具类
 */
public class Md5Utils {

    /**
     * 对于一组参数生成一个唯一ID(相同的参数重复调用生成的ID是相同的)
     * @param objArr
     * @return
     */
    public static String md5(String... objArr) {
        StringBuilder sb = new StringBuilder();
        for (String o : objArr) {
            sb.append(o);
        }
        return DigestUtils.md5Hex(sb.toString());
    }

}
