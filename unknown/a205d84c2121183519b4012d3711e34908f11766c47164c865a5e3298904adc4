package com.alipay.codegencore.model.domain;

import java.util.Date;

public class PromptTemplateDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_prompt_template.id
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_prompt_template.gmt_create
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_prompt_template.gmt_modified
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_prompt_template.name
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_prompt_template.template_text
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    private String templateText;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_prompt_template.description
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_prompt_template.old_template_text
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    private String oldTemplateText;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_prompt_template.id
     *
     * @return the value of cg_prompt_template.id
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_prompt_template.id
     *
     * @param id the value for cg_prompt_template.id
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_prompt_template.gmt_create
     *
     * @return the value of cg_prompt_template.gmt_create
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_prompt_template.gmt_create
     *
     * @param gmtCreate the value for cg_prompt_template.gmt_create
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_prompt_template.gmt_modified
     *
     * @return the value of cg_prompt_template.gmt_modified
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_prompt_template.gmt_modified
     *
     * @param gmtModified the value for cg_prompt_template.gmt_modified
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_prompt_template.name
     *
     * @return the value of cg_prompt_template.name
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_prompt_template.name
     *
     * @param name the value for cg_prompt_template.name
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_prompt_template.template_text
     *
     * @return the value of cg_prompt_template.template_text
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public String getTemplateText() {
        return templateText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_prompt_template.template_text
     *
     * @param templateText the value for cg_prompt_template.template_text
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public void setTemplateText(String templateText) {
        this.templateText = templateText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_prompt_template.description
     *
     * @return the value of cg_prompt_template.description
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_prompt_template.description
     *
     * @param description the value for cg_prompt_template.description
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_prompt_template.old_template_text
     *
     * @return the value of cg_prompt_template.old_template_text
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public String getOldTemplateText() {
        return oldTemplateText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_prompt_template.old_template_text
     *
     * @param oldTemplateText the value for cg_prompt_template.old_template_text
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    public void setOldTemplateText(String oldTemplateText) {
        this.oldTemplateText = oldTemplateText;
    }
}