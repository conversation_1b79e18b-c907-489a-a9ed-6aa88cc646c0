package com.alipay.codegencore.model.model;

import com.alipay.codegencore.model.enums.AlgStrategyEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 代码生成配置模型
 *
 * <AUTHOR>
 * 创建时间 2022-03-03
 */
public class CodegenConfigMoel {
    /**
     * 生成的代码模型数量
     * 默认生成数量不超过3
     */
    private int maxGenCodeModelNum = 3;
    /**
     * 补全结果排序规则
     * 目前暂时以策略类型进行排序，后续根据实际情况调整
     */
    private Map<String, Integer> sortDataMap;
    /**
     * 是否收集本地文件
     * (当服务器
     */
    private boolean isCollectFile = false;
    /**
     * token补全长度设置
     */
    private int lineTokenLength = 32;
    /**
     * 展示名默认长度
     */
    private int displayNameLength = 50;


    public CodegenConfigMoel() {
        Map<String, Integer> sortDataMap = new HashMap<String, Integer>();
        sortDataMap.put(AlgStrategyEnum.BIZ_SCENE.name(), 1);
        sortDataMap.put(AlgStrategyEnum.LINE.name(), 2 );
        this.sortDataMap = sortDataMap;
    }

    public int getDisplayNameLength() {
        return displayNameLength;
    }

    public void setDisplayNameLength(int displayNameLength) {
        this.displayNameLength = displayNameLength;
    }

    public int getLineTokenLength() {
        return lineTokenLength;
    }

    public void setLineTokenLength(int lineTokenLength) {
        this.lineTokenLength = lineTokenLength;
    }

    public boolean isCollectFile() {
        return isCollectFile;
    }

    public void setCollectFile(boolean collectFile) {
        isCollectFile = collectFile;
    }

    public Map<String, Integer> getSortDataMap() {
        return sortDataMap;
    }

    public void setSortDataMap(Map<String, Integer> sortDataMap) {
        this.sortDataMap = sortDataMap;
    }

    public int getMaxGenCodeModelNum() {
        return maxGenCodeModelNum;
    }

    public void setMaxGenCodeModelNum(int maxGenCodeModelNum) {
        this.maxGenCodeModelNum = maxGenCodeModelNum;
    }

}
