package com.alipay.codegencore.utils.code;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.PostBuilder;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.AntPathMatcher;

import java.io.File;
import java.net.http.HttpResponse;
import java.net.http.HttpTimeoutException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 19:40
 */
public class CodeSearchClient {

    private static final Logger log = LoggerFactory.getLogger(CodeSearchClient.class);

    /**
     * 配置中存储的初始化信息
     */
    public static final String CODE_SEARCH_CONFIG = "CODE_SEARCH_CONFIG";

    /**
     * 查询用户需求的相似代码片段
     */
    private static final String SEARCH_KEYWORD = "/openapi/search/code_search";

    /**
     * 查询用户需求指定的文件的相似代码片段
     */
    private static final String SEARCH_FILE_PATH = "/aos/openapi/search/";

    /**
     * 代码路径后缀分割
     */
    private static final String CODE_PATH_SPLIT = ".";

    /**
     * 全局配置
     */
    private static Config config;

    /**
     * ant 文件路径匹配
     */
    private static AntPathMatcher antPathMatcher = new AntPathMatcher();

    /**
     * 初始化 code search
     * @param dbConfig
     */
    public static void init(String dbConfig) {
        if (StringUtils.isBlank(dbConfig)) {
            throw new RuntimeException("code search not config. please check!!!");
        }
        log.info("code search init: {}", dbConfig);
        CodeSearchClient.config = JSON.parseObject(dbConfig, Config.class);

        antPathMatcher.setCachePatterns(true);
    }

    /**
     * 根据用户需求查询相似代码片段
     * @param searchRequestData
     * @return
     */
    public static List<Item> searchRelatedCodeByQuery(SearchRequestData searchRequestData) {

        String requestUrl = config.opHost + SEARCH_KEYWORD;
        final String sessionId = searchRequestData.getSessionId();

        Map<String, String> headParam = new HashMap<>();
        headParam.put("Content-Type", "application/json");
        headParam.put("App-Name", config.appName);
        headParam.put("Api-Token", config.opToken);
        headParam.put("Emp-Id", config.opEmpId);
        headParam.put("Request-Id", sessionId);
        headParam.put("Cookie", config.opCookie);

        log.info("search related code session:{}", sessionId);
        Stopwatch stopwatch = Stopwatch.createStarted();
        PostBuilder postBuilder = HttpClient.post(requestUrl)
                .content(JSON.toJSONString(searchRequestData))
                .headers(headParam);

        HttpResponse<String> httpResponse = null;
        try {
            httpResponse = postBuilder.syncExecuteWithFullResponse(config.timeout);
        } catch (HttpTimeoutException e1) {
            log.error("send code search timeout. session:{}", sessionId, e1);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_TIMEOUT);
        } catch (Exception e2) {
            log.error("send code search found exception. session:{}", sessionId, e2);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_EXCEPTION);
        } finally {
            stopwatch.stop();
            log.info("search related code duration cost:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
        log.info("search related code session:{} status:{} body:{}", sessionId, httpResponse.statusCode(), httpResponse.body());

        if (HttpStatus.SC_OK != httpResponse.statusCode()) {
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL);
        }

        SearchResponseData searchResponseData = null;
        try {
            searchResponseData = JSON.parseObject(httpResponse.body(), SearchResponseData.class);
        } catch (Exception e) {
            log.error("search code session:{} data ill", sessionId, e);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_RESULT_ILL);
        }

        if (Objects.nonNull(searchResponseData) && searchResponseData.getCode() == 0) {
            return searchResponseData.getData().getItems();
        }
        return List.of();
    }

    /**
     * 根据用户需求和指定的文件路径查询相似代码片段
     * @param searchByPathRequestData
     * @return
     */
    public static List<Item> searchRelatedCodeByQueryAndPath(SearchByPathRequestData searchByPathRequestData) {

        String requestUrl = config.aosHost + SEARCH_FILE_PATH;
        final String sessionId = searchByPathRequestData.getSessionId();

        Map<String, String> headParam = new HashMap<>();
        headParam.put("EMP-ID", config.aosEmpId);
        headParam.put("Content-Type", "application/json");
        headParam.put("PRIVATE-TOKEN", config.aosToken);
        headParam.put("Request-Id", sessionId);
        headParam.put("Cookie", config.aosCookie);

        log.info("search related code path session:{}", sessionId);

        JSONObject requestBody = new JSONObject();
        requestBody.put("page", config.page);
        requestBody.put("page_size", config.pageSize);
        requestBody.put("query", searchByPathRequestData.getQuery());
        requestBody.put("type", config.type);
        requestBody.put("skipCache", config.skipCache);
        requestBody.put("version", config.version);
        JSONObject filter = new JSONObject();
        filter.put("repo_path", pathHandle(searchByPathRequestData.getRepoPath()));
        filter.put("type", config.type);
        filter.put("file_path", pathHandle(searchByPathRequestData.getFilePath()));
        requestBody.put("filters", filter);
        Stopwatch stopwatch = Stopwatch.createStarted();
        PostBuilder postBuilder = HttpClient.post(requestUrl)
                .content(requestBody.toJSONString())
                .headers(headParam);

        HttpResponse<String> httpResponse = null;
        try {
            httpResponse = postBuilder.syncExecuteWithFullResponse(config.timeout);
        } catch (HttpTimeoutException e1) {
            log.error("send code path search timeout. session:{}", sessionId, e1);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_TIMEOUT);
        } catch (Exception e2) {
            log.error("send code path search found exception. session:{}", sessionId, e2);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_EXCEPTION);
        } finally {
            stopwatch.stop();
            log.info("search path code duration cost:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
        log.info("search related code path session:{} status:{} body:{}", sessionId, httpResponse.statusCode(), httpResponse.body());

        if (HttpStatus.SC_OK != httpResponse.statusCode()) {
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL);
        }

        List<Item> items = Lists.newArrayList();
        try {
            JSONObject responseJsonObject = JSON.parseObject(httpResponse.body());
            if (!responseJsonObject.containsKey("success")
                    || !responseJsonObject.getBoolean("success")) {
                throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL);
            }

            JSONObject dataJsonObject = responseJsonObject.getJSONObject("data");
            JSONArray itemsJsonArray = dataJsonObject.getJSONArray("items");

            for (int i = 0 ; i < itemsJsonArray.size(); i++) {
                JSONObject itemJson = itemsJsonArray.getJSONObject(i);
                if (Objects.nonNull(itemJson)) {
                    String metaString = itemJson.getString("meta");
                    JSONObject metaJsonObject = JSON.parseObject(metaString);
                    Item item = new Item();
                    item.setFilePath(metaJsonObject.getString("file_path"));
                    item.setType(itemJson.getString("type"));
                    item.setMethodContent(itemJson.getString("text"));
                    item.setSource("search");
                    items.add(item);
                }
            }
        } catch (Exception e) {
            log.error("search code path session:{} data ill", sessionId, e);
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_RESULT_ILL);
        }

        return items;
    }

    /**
     * 路径处理
     * @param path
     * @return
     */
    private static String pathHandle(String path) {
        return StringUtils.replace(path, "/", "\\\\/");
    }

    /**
     * 增加文件黑名单过滤
     * @param itemList
     * @return
     */
    public static List<Item> filter(List<Item> itemList) {
        log.info("search code filter before size:{}", itemList.size());
        List<String> blackPattern = config.blackPattern;
        if (CollectionUtils.isEmpty(blackPattern)) {
            return itemList;
        }

        for (String pattern : blackPattern) {
            Iterator<Item> iterator = itemList.iterator();
            while (iterator.hasNext()) {
                String filePath = iterator.next().getFilePath();
                if (antPathMatcher.match(pattern, filePath)) {
                    log.info("filter file:{} match pattern:{} will remove", filePath, pattern);
                    iterator.remove();
                }
            }
        }
        return itemList;
    }

    /**
     *  全局配置类
     */
    static class Config{

        public String aosHost;

        public String opHost;

        public String appName;

        public String aosToken;

        public String opToken;

        public String aosEmpId;

        public String opEmpId;

        public String aosCookie;

        public String opCookie;

        public int page = 1;

        public int pageSize = 10;

        public String type = "enum OR class OR interface OR controller OR dao OR impl";

        public boolean skipCache = true;

        public String version = "ai";

        public int timeout = 600000;

        public List<String> blackPattern = Lists.newArrayList();

    }

    /**
     * 通过路径查询相似代码请求
     */
    public static class SearchByPathRequestData {

        private String sessionId;

        private String query;

        private String filePath;

        private String repoPath;

        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getRepoPath() {
            return repoPath;
        }

        public void setRepoPath(String repoPath) {
            this.repoPath = repoPath;
        }
    }

    /**
     * 查询相似代码片段请求
     */
    public static class SearchRequestData {

        @JSONField(name = "query")
        private String query;

        @JSONField(name = "repo_path")
        private String repoPath;

        @JSONField(name = "method_name")
        private String methodName;

        @JSONField(name = "file_path")
        private String filePath;

        @JSONField(name = "deploy_app_name")
        private String deployAppName;

        @JSONField(name = "session_id")
        private String sessionId;

        public String getQuery() {
            return query;
        }

        public void setQuery(String query) {
            this.query = query;
        }

        public String getRepoPath() {
            return repoPath;
        }

        public void setRepoPath(String repoPath) {
            this.repoPath = repoPath;
        }

        public String getMethodName() {
            return methodName;
        }

        public void setMethodName(String methodName) {
            this.methodName = methodName;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getDeployAppName() {
            return deployAppName;
        }

        public void setDeployAppName(String deployAppName) {
            this.deployAppName = deployAppName;
        }

        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }
    }

    /**
     * 查询相似代码片段响应
     */
    public static class SearchResponseData {

        @JSONField(name = "code")
        private int code;

        @JSONField(name = "data")
        private ResponseDataDetails data;

        @JSONField(name = "hostname")
        private String hostname;

        @JSONField(name = "msg")
        private String msg;

        @JSONField(name = "traceId")
        private String traceId;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public ResponseDataDetails getData() {
            return data;
        }

        public void setData(ResponseDataDetails data) {
            this.data = data;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public String getTraceId() {
            return traceId;
        }

        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }

        public static class ResponseDataDetails {

            @JSONField(name = "query")
            private String query;

            @JSONField(name = "total")
            private int total;

            @JSONField(name = "items")
            private List<Item> items;

            public String getQuery() {
                return query;
            }

            public void setQuery(String query) {
                this.query = query;
            }

            public int getTotal() {
                return total;
            }

            public void setTotal(int total) {
                this.total = total;
            }

            public List<Item> getItems() {
                return items;
            }

            public void setItems(List<Item> items) {
                this.items = items;
            }


        }
    }

    /**
     * 查询相似代码片段内容
     */
    public static class Item {

        @JSONField(name = "id")
        private Integer id;

        @JSONField(name = "file_path")
        private String filePath;

        @JSONField(name = "method_content")
        private String methodContent;

        @JSONField(name = "method_name")
        private String methodName;

        @JSONField(name = "type")
        private String type;

        @JSONField(name = "source")
        private String source;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getMethodContent() {
            return methodContent;
        }

        public void setMethodContent(String methodContent) {
            this.methodContent = methodContent;
        }

        public String getMethodName() {
            return methodName;
        }

        public void setMethodName(String methodName) {
            this.methodName = methodName;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

    }

    /**
     * 构建新的文件名, 用于标识唯一
     * 规则：文件名 + 唯一id编号
     * @param filePath
     * @param id
     * @return
     */
    public static String buildNewPath(String filePath, int id) {
        try {
            String suffix = StringUtils.substringAfter(filePath, CODE_PATH_SPLIT);
            return StringUtils.replace(filePath, CODE_PATH_SPLIT + suffix,
                    id + CODE_PATH_SPLIT + suffix);
        } catch (Exception e) {
            log.error("file path:{} ill", filePath, e);
        }
        return filePath;
    }

    /**
     * code生成时替换文件名
     * @param filePath
     * @param id
     * @return
     */
    public static String buildNewPathCode(String filePath, long id) {
        try {
            String prefix = StringUtils.substringBefore(filePath, File.separator);
            return StringUtils.replace(filePath, prefix + File.separator,
                    prefix + id + File.separator);
        } catch (Exception e) {
            log.error("file path:{} ill", filePath, e);
        }
        return filePath;
    }

}
