package com.alipay.codegencore.service.ideaevo.impl;

import com.alipay.codegencore.dal.mapper.ActionGenCodeSearchInfoMapper;
import com.alipay.codegencore.model.domain.ActionGenCodeSearchInfoDO;
import com.alipay.codegencore.service.ideaevo.ActionGenCodeSearchInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 2024/11/1 16:11
 */
@Service
public class ActionGenCodeSearchInfoServiceImpl implements ActionGenCodeSearchInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ActionGenCodeSearchInfoServiceImpl.class);

    @Autowired
    private ActionGenCodeSearchInfoMapper actionGenCodeSearchInfoMapper;

    @Override
    public void save(String sessionId, String query, String recall) {

        if (StringUtils.isBlank(sessionId)) {
            logger.error("sessionId is blank");
            return;
        }

        try {
            int count = actionGenCodeSearchInfoMapper.insert(sessionId, query, recall);
            logger.info("save actionGenCodeSearchInfoDO, session:{} count:{}", sessionId, count);
        } catch (Exception e) {
            logger.error("save actionGenCodeSearchInfoDO error. session:{} query:{} recall:{}",
                    sessionId, query, recall, e);
        }
    }

    @Override
    public List<ActionGenCodeSearchInfoDO> queryBySessionId(String sessionId) {

        if (StringUtils.isBlank(sessionId)) {
            logger.warn("sessionId is blank");
            return List.of();
        }

        try {
            return actionGenCodeSearchInfoMapper.getBySessionId(sessionId);
        } catch (Exception e) {
            logger.error("queryBySessionId error. session:{}", sessionId, e);
        }
        return List.of();
    }
}
