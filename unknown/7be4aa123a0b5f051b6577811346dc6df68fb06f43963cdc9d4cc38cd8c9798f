package com.alipay.codegencore.service.utils;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.request.AbstractClientModel;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 通用工具类
 *
 * <AUTHOR>
 * 创建时间 2021-12-27
 */
public class CommonUtils {

    // 中文标点符号
    public static final List<String> CHINESE_PUNCTUATION_END_MARKS = Lists.newArrayList("，","；","：","。", "！", "？",  "……", "…");
    // 英文标点符号
    public static final List<String> ENGLISH_PUNCTUATION_END_MARKS = Lists.newArrayList(",", ";", ":",".", "!", "?", "...");
    /**
     * 换行符
     */
    public static final String LINE_SEPARATOR = "\n";

    /**
     * 判断是否为注释行
     *
     * @param line
     * @return
     */
    public static boolean isNoteLine(String line) {
        if (StringUtils.isBlank(line)) {
            return false;
        }
        return line.trim().startsWith("/") || line.trim().startsWith("*");
    }

    /**
     * 检查近端请求
     * 如果userLabel/userDir为空，则设置为UNKNOWN
     *
     * @param abstractClientModel
     * @return
     */
    public static AbstractClientModel checkClientModel(AbstractClientModel abstractClientModel) {
        if (StringUtils.isBlank(abstractClientModel.getUserDir())) {
            abstractClientModel.setUserDir(AppConstants.UNKNOWN_LABEL);
        }
        if (StringUtils.isBlank(abstractClientModel.getUserLabel())) {
            abstractClientModel.setUserLabel(AppConstants.UNKNOWN_LABEL);
        }
        return abstractClientModel;
    }

    /**
     * 判断是否为标点符号
     * @param str 字符
     * @return true=标点
     */
    public static boolean isPunctuation(String str){
        return ENGLISH_PUNCTUATION_END_MARKS.contains(str) || CHINESE_PUNCTUATION_END_MARKS.contains(str) || LINE_SEPARATOR.equals(str);
    }

    /**
     * 字符串是否包含标点符号
     * @param string 字符串
     * @return true=里面包含符号
     */
    public static boolean containsPunctuation(String string) {
        boolean tokenContainsPunctuation = string.contains(CommonUtils.LINE_SEPARATOR);
        for (String endMark : CommonUtils.ENGLISH_PUNCTUATION_END_MARKS) {
            if (string.contains(endMark)) {
                tokenContainsPunctuation = true;
                break;
            }
        }

        for (String endMark : CommonUtils.CHINESE_PUNCTUATION_END_MARKS) {
            if (string.contains(endMark)) {
                tokenContainsPunctuation = true;
                break;
            }
        }
        return tokenContainsPunctuation;
    }
}
