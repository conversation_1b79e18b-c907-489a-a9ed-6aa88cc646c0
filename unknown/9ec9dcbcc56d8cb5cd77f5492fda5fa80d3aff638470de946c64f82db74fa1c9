package com.alipay.codegencore.utils;

import com.alipay.codegencore.utils.file.FileProcessUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 文件处理测试类
 */
public class FileProcessUtilsTest extends AbstractTestBase {

    @Test
    public void test_splitFile() {
        List<String> list = FileProcessUtils.splitFile("\n" +
                "[穷爸爸富爸爸 / 罗伯特・T・清崎 著]\n" +
                "\n" +
                "-----章节内容开始------\n" +
                "\n" +
                "第一卷 第一章\n" +
                "\n" +
                "序言\n" +
                "\n" +
                "这就是你所需要的学校真的让孩子们准备好应付真实的世界了吗？\n" +
                "“努力学习，得到好成绩，你就能找到高薪并且伴有很多其他好处的职位。”我父母过去常这么对我说。他们的生活目标就是供我和姐姐上大学，觉得这样我们就有了在生活中获得成功的最好机会。1976年，当我从佛罗里达州立大学会计专业以全班第～的成绩光荣地获得学位证书时，我的父母实现了他们的目标，并把这作为他们一生中最引以为自豪的成就。根据“大师计划”，我很快便被“八大”会计公司中的一家雇佣，于是我在很早就觉察到了我今后漫长的职业生涯直至退休将是一条不会有太多变化的平稳道路。\n" +
                "我丈夫迈克尔也走着同样的路。我们都来自努力工作的家庭，有着朴素的生活方式和极强的职业道德观。迈克尔也是以优异的成绩从名牌大学毕业的，他还先后深造过两次：一次是作为工程师，另一次是在法律学校。这之后，他便很快被华盛顿一所著名的法律公司聘用，专攻专利法。和我一样，他的未来看起来非常光明，事业的道路也已被很好的确定了，而且还有充分的退休保障。\n" +
                "虽然我们在事业上很成功，应该说已经达到甚至超出了父母们当初提出的希望，但生活却并不像他们当初为我们所描绘的那么一劳永逸。由于新经济时代的种种原因，我们都曾先后换了几次工作，这使得当初看起来如此诱人的职业养老金计划几乎成了泡影，我们的退休金只能靠自己挣了。\n" +
                "迈克尔和我婚姻美满并有三个好孩子。当我写这些话时候，其中两个正在大学，另一个也已开始念高中。我们花了许多钱希望使我们的孩子得到尽可能好的教育。\n" +
                "1996年的一天，最小的孩子带着破灭的幻想从学校归来，他说他已经厌倦了，不想再去学习。“为什么我要花时间去学那些我真实生活中一辈子也用不到的东西呢？”他抗议道。\n" +
                "我毫不思索地答道：“因为如果你学得不好就进不了大学。”",100);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    }

    @Test
    public void test_getFileContent() {
        MultipartFile multipartFileTxt = null;
        MultipartFile multipartFileDocx = null;
        MultipartFile multipartFilePdf = null;
        try {
            multipartFileTxt = new MockMultipartFile("file", "fileName.txt", "text/plain", "content".getBytes(StandardCharsets.UTF_8));

            XWPFDocument document = new XWPFDocument();
            XWPFParagraph paragraph = document.createParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText("Hello, POI!");
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            multipartFileDocx = new MockMultipartFile("file","example.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", outputStream.toByteArray());

            PDDocument documentPDF = new PDDocument();
            PDPage page = new PDPage();
            documentPDF.addPage(page);
            PDPageContentStream contentStream = new PDPageContentStream(documentPDF, page);
            contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
            contentStream.beginText();
            contentStream.newLineAtOffset(50, 700);
            contentStream.showText("Hello, PDFBox!");
            contentStream.endText();
            contentStream.close();
            ByteArrayOutputStream outputStreamPDF = new ByteArrayOutputStream();
            documentPDF.save(outputStreamPDF); // save the document to an OutputStream
            documentPDF.close(); // close the document
            multipartFilePdf = new MockMultipartFile("file","example.pdf", "application/pdf", outputStreamPDF.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        String txt = FileProcessUtils.getFileContent(multipartFileTxt);
        String docx = FileProcessUtils.getFileContent(multipartFileDocx);
        String pdf = FileProcessUtils.getFileContent(multipartFilePdf);
        Assert.assertTrue(StringUtils.isNotBlank(txt));
        Assert.assertTrue(StringUtils.isNotBlank(docx));
        Assert.assertTrue(StringUtils.isNotBlank(pdf));
    }
}
