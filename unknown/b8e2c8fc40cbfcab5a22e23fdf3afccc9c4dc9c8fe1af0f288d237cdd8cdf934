package com.alipay.codegencore.model.request;

import java.util.Map;

/**
 * 客户端异常信息
 *
 * <AUTHOR>
 * 创建时间 2022-03-07
 */
public class ErrorInfoRequestBean extends AbstractClientModel{
    /**
     * 异常信息
     */
    private String errrorMsg;
    /**
     * 其他信息。
     */
    private Map<String,String> extInfo;

    public Map<String, String> getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(Map<String, String> extInfo) {
        this.extInfo = extInfo;
    }

    public String getErrrorMsg() {
        return errrorMsg;
    }

    public void setErrrorMsg(String errrorMsg) {
        this.errrorMsg = errrorMsg;
    }
}
