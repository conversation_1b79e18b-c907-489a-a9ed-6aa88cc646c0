package com.alipay.codegencore;

import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.ApplicationArguments;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version : StartupValidatorTest.java, v 0.1 2023年11月28日 10:36 baoping Exp $
 */
public class StartupValidatorTest {
    @Mock
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @InjectMocks
    private StartupValidator startupValidator;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testRun_IntranetEnvironmentTure_ServiceNameConsistent() {
        ApplicationArguments args = mock(ApplicationArguments.class);
        when(codeGPTDrmConfig.isIntranetApplication()).thenReturn(true);
        ReflectionTestUtils.setField(startupValidator, "serviceName", "codegencore");
        startupValidator.run(args);
    }

    @Test
    public void testRun_IntranetEnvironmentTure_ApplicationNameInconsistent() {
        ApplicationArguments args = mock(ApplicationArguments.class);
        when(codeGPTDrmConfig.isIntranetApplication()).thenReturn(true);
        ReflectionTestUtils.setField(startupValidator, "serviceName", "tsingyancodegen");
        assertThrows(BizException.class, () -> startupValidator.run(args));
    }

    @Test
    public void testRun_IntranetEnvironmentFalse_ApplicationNameConsistent() {
        ApplicationArguments args = mock(ApplicationArguments.class);
        when(codeGPTDrmConfig.isIntranetApplication()).thenReturn(false);
        ReflectionTestUtils.setField(startupValidator, "serviceName", "tsingyancodegen");
        startupValidator.run(args);
    }

    @Test
    public void testRun_IntranetEnvironmentFalse_ApplicationNameInconsistent() {
        ApplicationArguments args = mock(ApplicationArguments.class);
        when(codeGPTDrmConfig.isIntranetApplication()).thenReturn(false);
        ReflectionTestUtils.setField(startupValidator, "serviceName", "codegencore");
        assertThrows(BizException.class, () -> startupValidator.run(args));
    }
}
