package com.alipay.codegencore.service.utils;

import org.apache.commons.lang3.StringUtils;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.text.TextContentRenderer;
/** 处理语雀文档内容的工具
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.01.12
 */
public class YuQueDocUtils {
    /**
     * markdown拓展语法处理
     * <AUTHOR>
     * @since 2024.01.12
     * @param text text
     * @return java.lang.String
     */
    public static String cleanTextContent(String text) {
        // 如果提供的文本为空，立即返回
        if (StringUtils.isBlank(text) || text.trim().isEmpty()) {
            return text;
        }

        // 使用正则表达式去除标签
        String noHtml = text.replaceAll("<[^>]*>", "");

        // 替换所有换行符和回车符为一个空格
        String noLineBreaks = noHtml.replaceAll("\\r\\n|\\r|\\n", " ");

        // 去除特定特殊符号
        String noSpecialChars = noLineBreaks.replaceAll("[|\\-]", "");

        // 删除多余的空格，将多个连续空格替换为一个空格
        String normalizedSpaces = noSpecialChars.replaceAll("\\s+", " ");

        // 删除Markdown中的删除符号
        String withoutStrikethrough = normalizedSpaces.replaceAll("~~(.*?)~~", "$1");

        // 返回清理后的文本
        return withoutStrikethrough.trim();
    }

    /**
     * 使用markdown库处理解析
     * <AUTHOR>
     * @since 2024.01.12
     * @param markdown markdown
     * @return java.lang.String
     */
    public static String convertMarkdownToPlainText(String markdown) {
        Parser parser = Parser.builder().build();
        Node document = parser.parse(markdown);
        TextContentRenderer renderer = TextContentRenderer.builder().build();
        return cleanTextContent(renderer.render(document));
    }

}
