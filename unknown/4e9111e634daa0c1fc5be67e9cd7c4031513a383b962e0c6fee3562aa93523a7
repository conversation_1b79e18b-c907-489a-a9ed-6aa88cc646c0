package com.alipay.codegencore.model.remote;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:36
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MessageContent {

    public enum MessageType {
        @JsonProperty("text")
        TEXT,
        @JsonProperty("image_url")
        IMAGE_URL,
        @JsonProperty("image_file")
        IMAGE_FILE
    }

    @JsonProperty(value = "type", required = true)
    @JSONField(name = "type", serializeUsing = EnumSerializer.class)
    private MessageType type;

    @JsonProperty(value = "value", required = true)
    private String value;

    @JsonProperty(value = "index")
    private Integer index;

    public MessageType getType() {
        return type;
    }

    public void setType(MessageType type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }
}
