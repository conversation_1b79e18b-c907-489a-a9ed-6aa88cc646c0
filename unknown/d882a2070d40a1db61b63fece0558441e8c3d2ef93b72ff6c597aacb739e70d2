package com.alipay.codegencore.service.common.segment;

import com.alipay.codegencore.model.enums.SegmentationStrategyTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version : SegmentationStrategyFactory.java, v 0.1 2024年01月11日 14:56 baoping Exp $
 */
@Component
public class SegmentationStrategyFactory {
    @Resource
    private List<SegmentationStrategy> segmentationStrategies;
    /**
     * 获取分段策略
     * @param type
     * @return
     */
    public  SegmentationStrategy getSegmentationStrategy(SegmentationStrategyTypeEnum type) {
        if(CollectionUtils.isEmpty(segmentationStrategies)){
            throw new IllegalArgumentException("segmentationStrategies is empty");
        }
        for (SegmentationStrategy segmentationStrategy : segmentationStrategies) {
            if(segmentationStrategy.getType() == type){
                return segmentationStrategy;
            }
        }
        throw new IllegalArgumentException("Unsupported segmentation strategy type");
    }
}
