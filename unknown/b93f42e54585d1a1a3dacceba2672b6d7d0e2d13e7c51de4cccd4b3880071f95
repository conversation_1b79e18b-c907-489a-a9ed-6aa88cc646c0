package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface UserAuthDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    long countByExample(UserAuthDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    int deleteByExample(UserAuthDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    @Delete({
        "delete from cg_user_auth",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    @Insert({
        "insert into cg_user_auth (gmt_create, gmt_modified, ",
        "user_name, token, ",
        "bu_name, emp_id, admin, ",
        "alipay_account, status, ",
        "phone_number, save_scenes, ",
        "top_session_uids, application_reason, ",
        "allow_access_type)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{userName,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR}, ",
        "#{buName,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{admin,jdbcType=TINYINT}, ",
        "#{alipayAccount,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, ",
        "#{phoneNumber,jdbcType=VARCHAR}, #{saveScenes,jdbcType=VARCHAR}, ",
        "#{topSessionUids,jdbcType=VARCHAR}, #{applicationReason,jdbcType=VARCHAR}, ",
        "#{allowAccessType,jdbcType=INTEGER})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(UserAuthDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    int insertSelective(UserAuthDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    List<UserAuthDO> selectByExample(UserAuthDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, user_name, token, bu_name, emp_id, admin, alipay_account, ",
        "status, phone_number, save_scenes, top_session_uids, application_reason, allow_access_type",
        "from cg_user_auth",
        "where id = #{id,jdbcType=BIGINT}"
    })
    UserAuthDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    int updateByExampleSelective(@Param("record") UserAuthDO record, @Param("example") UserAuthDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    int updateByExample(@Param("record") UserAuthDO record, @Param("example") UserAuthDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    int updateByPrimaryKeySelective(UserAuthDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_auth
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    @Update({
        "update cg_user_auth",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "user_name = #{userName,jdbcType=VARCHAR},",
          "token = #{token,jdbcType=VARCHAR},",
          "bu_name = #{buName,jdbcType=VARCHAR},",
          "emp_id = #{empId,jdbcType=VARCHAR},",
          "admin = #{admin,jdbcType=TINYINT},",
          "alipay_account = #{alipayAccount,jdbcType=VARCHAR},",
          "status = #{status,jdbcType=TINYINT},",
          "phone_number = #{phoneNumber,jdbcType=VARCHAR},",
          "save_scenes = #{saveScenes,jdbcType=VARCHAR},",
          "top_session_uids = #{topSessionUids,jdbcType=VARCHAR},",
          "application_reason = #{applicationReason,jdbcType=VARCHAR},",
          "allow_access_type = #{allowAccessType,jdbcType=INTEGER}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(UserAuthDO record);
}