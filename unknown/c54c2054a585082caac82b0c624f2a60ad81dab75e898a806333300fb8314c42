/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alipay.codegencore.model.model.links.Enum.OpenTypeEnum;

import java.util.List;

/**
 * 卡片按钮信息
 * <AUTHOR>
 * @version $Id: LinkItem.java, v 0.1 2022-07-06 19:09 wb-tzg858080 Exp $$
 */
public class LinkItem extends ToString {

    /**
     * 按钮名称
     */
    private String name ;
    /**
     * 链接地址
     */
    private String url;
    /**
     * 内容
     */
    private String  content ;

    /**
     * 钉钉端链接打开方式
     * slide 侧边栏
     * popup 弹出
     * web 浏览器
     */
    private OpenTypeEnum dingOpenType;

    /**
     * API|MODAL
     * API类型字段：url\method\params
     * MODAL类型字段：key\params
     */
    private String type;

    /**
     * 调用方式
     */
    private String method;

    /**
     * 接口调用模式（前端定义）
     */
    private String apiType;
    /**
     * 请求参数
     */
    private List<LinkParam> params;

    /**
     * 模态框唯一key
     */
    private String key;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public OpenTypeEnum getDingOpenType() {
        return dingOpenType;
    }

    public void setDingOpenType(OpenTypeEnum dingOpenType) {
        this.dingOpenType = dingOpenType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    public List<LinkParam> getParams() {
        return params;
    }

    public void setParams(List<LinkParam> params) {
        this.params = params;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public static class LinkParam{
        /**
         * 参数key
         */
        private String key;
        /**
         * 参数value
         */
        private String value;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
