package com.alipay.codegencore.model.domain;

import java.util.Date;

public class GptMessageDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.gmt_create
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.gmt_modified
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.deleted
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.mongo_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String mongoId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.conversation_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String conversationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.user_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.type
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.content
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String content;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.replay_message_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String replayMessageId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.gpt_model
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String gptModel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.source_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String sourceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message.ext_info
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    private String extInfo;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.id
     *
     * @return the value of links_gpt_message.id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.id
     *
     * @param id the value for links_gpt_message.id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.gmt_create
     *
     * @return the value of links_gpt_message.gmt_create
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.gmt_create
     *
     * @param gmtCreate the value for links_gpt_message.gmt_create
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.gmt_modified
     *
     * @return the value of links_gpt_message.gmt_modified
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.gmt_modified
     *
     * @param gmtModified the value for links_gpt_message.gmt_modified
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.deleted
     *
     * @return the value of links_gpt_message.deleted
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.deleted
     *
     * @param deleted the value for links_gpt_message.deleted
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.mongo_id
     *
     * @return the value of links_gpt_message.mongo_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getMongoId() {
        return mongoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.mongo_id
     *
     * @param mongoId the value for links_gpt_message.mongo_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setMongoId(String mongoId) {
        this.mongoId = mongoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.conversation_id
     *
     * @return the value of links_gpt_message.conversation_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getConversationId() {
        return conversationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.conversation_id
     *
     * @param conversationId the value for links_gpt_message.conversation_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.user_id
     *
     * @return the value of links_gpt_message.user_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.user_id
     *
     * @param userId the value for links_gpt_message.user_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.type
     *
     * @return the value of links_gpt_message.type
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.type
     *
     * @param type the value for links_gpt_message.type
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.content
     *
     * @return the value of links_gpt_message.content
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.content
     *
     * @param content the value for links_gpt_message.content
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.replay_message_id
     *
     * @return the value of links_gpt_message.replay_message_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getReplayMessageId() {
        return replayMessageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.replay_message_id
     *
     * @param replayMessageId the value for links_gpt_message.replay_message_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setReplayMessageId(String replayMessageId) {
        this.replayMessageId = replayMessageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.gpt_model
     *
     * @return the value of links_gpt_message.gpt_model
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getGptModel() {
        return gptModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.gpt_model
     *
     * @param gptModel the value for links_gpt_message.gpt_model
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setGptModel(String gptModel) {
        this.gptModel = gptModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.source_id
     *
     * @return the value of links_gpt_message.source_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getSourceId() {
        return sourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.source_id
     *
     * @param sourceId the value for links_gpt_message.source_id
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message.ext_info
     *
     * @return the value of links_gpt_message.ext_info
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public String getExtInfo() {
        return extInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message.ext_info
     *
     * @param extInfo the value for links_gpt_message.ext_info
     *
     * @mbg.generated Mon Jul 01 13:42:07 CST 2024
     */
    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
}