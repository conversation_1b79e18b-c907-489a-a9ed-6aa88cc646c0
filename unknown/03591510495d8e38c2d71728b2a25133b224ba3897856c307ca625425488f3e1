package com.alipay.codegencore.utils.keymap;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 数据安全平台keymap的工具类
 */
public class KeymapUtils {

    // token生成算法:https://yuque.antfin-inc.com/ioqia8/kg7h1z/yeaxwd7xpuf8uw4l?singleDoc#Ta9xx
    public static String genKeymapToken(String bizCode, String sceneId,Long ts) {
        if (bizCode == null || sceneId == null || ts == null) {
            return null;
        }
        return getMd5String32(bizCode + sceneId + ts);
    }

    private static String getMd5String32(String message) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] md5Array = md.digest(message.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Array) {
                sb.append(Integer.toHexString(b & 255 | 256), 1, 3);
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException var2) {
            throw new RuntimeException(var2);
        }
    }

}
