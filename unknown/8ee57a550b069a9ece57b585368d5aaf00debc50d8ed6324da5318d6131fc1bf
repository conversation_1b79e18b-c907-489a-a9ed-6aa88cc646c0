package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.PromptTemplateDOExample;
import com.alipay.codegencore.model.domain.PromptTemplateDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface PromptTemplateDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    long countByExample(PromptTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    int deleteByExample(PromptTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    @Delete({
        "delete from cg_prompt_template",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    @Insert({
        "insert into cg_prompt_template (gmt_create, gmt_modified, ",
        "name, template_text, ",
        "description, old_template_text)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{name,jdbcType=VARCHAR}, #{templateText,jdbcType=VARCHAR}, ",
        "#{description,jdbcType=VARCHAR}, #{oldTemplateText,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(PromptTemplateDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    int insertSelective(PromptTemplateDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    List<PromptTemplateDO> selectByExample(PromptTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, name, template_text, description, old_template_text",
        "from cg_prompt_template",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("com.alipay.codegencore.dal.mapper.PromptTemplateDOMapper.BaseResultMap")
    PromptTemplateDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    int updateByExampleSelective(@Param("record") PromptTemplateDO record, @Param("example") PromptTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    int updateByExample(@Param("record") PromptTemplateDO record, @Param("example") PromptTemplateDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    int updateByPrimaryKeySelective(PromptTemplateDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_prompt_template
     *
     * @mbg.generated Tue Sep 24 10:13:18 CST 2024
     */
    @Update({
        "update cg_prompt_template",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "name = #{name,jdbcType=VARCHAR},",
          "template_text = #{templateText,jdbcType=VARCHAR},",
          "description = #{description,jdbcType=VARCHAR},",
          "old_template_text = #{oldTemplateText,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(PromptTemplateDO record);
}