package com.alipay.codegencore.model.model;

import java.util.Map;

/**
 * 场景识别结果模型(方法体识别）
 *
 * <AUTHOR>
 * 创建时间 2022-05-19
 */
public class MethodContentSceneModel extends AbstractSceneRecognitionModel {

    /**
     * 赋值变量缓存，在赋值特定表达式识别场景时，可直接使用expressVariableMap来获取到模版参数
     * eg: key: class类型 - "UserInfo" ，  value: referenceName -"userInfo"
     */
    private Map<String, String> expressVariableMap;
    /**
     * 保留起始行
     */
    private int startLine;


    public int getStartLine() {
        return startLine;
    }

    public void setStartLine(int startLine) {
        this.startLine = startLine;
    }


    public Map<String, String> getExpressVariableMap() {
        return expressVariableMap;
    }

    public void setExpressVariableMap(Map<String, String> expressVariableMap) {
        this.expressVariableMap = expressVariableMap;
    }
}
