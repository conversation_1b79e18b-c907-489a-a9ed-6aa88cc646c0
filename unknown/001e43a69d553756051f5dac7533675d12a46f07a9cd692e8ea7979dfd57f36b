package com.alipay.codegencore.model.model;

import com.alipay.codegencore.model.domain.FileDataDO;

/**
 * 文件对象
 *
 * <AUTHOR>
 * 创建时间 2022-06-07
 */
public class FileModel extends FileDataDO {

    /**
     * 文件下载路径
     */
    private String downloadUrl;
    /**
     * 是否加载到本地内存
     */
    private boolean isLoadJar = false;

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public boolean isLoadJar() {
        return isLoadJar;
    }

    public void setLoadJar(boolean loadJar) {
        isLoadJar = loadJar;
    }
}
