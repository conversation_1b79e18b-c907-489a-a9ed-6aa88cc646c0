package com.alipay.codegencore.service.ideaevo.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.ideaevo.IdeaEvoService;
import com.alipay.codegencore.service.ideaevo.ModelRequestHandler;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * idea evo服务实现，负责代码规划生成和代码生成
 */
@Service
public class IdeaEvoServiceImpl implements IdeaEvoService {
    private static final Logger LOGGER = LoggerFactory.getLogger( IdeaEvoServiceImpl.class );

    private static final Pattern CODE_BLOCK_PATTERN = Pattern.compile("```(\\w+)?\\n([\\s\\S]*?)\\n```");

    /**
     * 系统来源
     */
    private static final String SOURCE = "ideaEvo";

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private AlgoBackendService algoBackendService;

    @Autowired
    private ModelRequestHandler modelRequestHandler;

    @Override
    public JSONObject aiCodePlan(JSONObject featureData, String modelEnv) {
        // 需求解析
        String similarityContent = parseCodePlanFeatures(featureData);

        String queryFinal = codeGPTDrmConfig.getIdeaEvoCodePlanPromptTemplate() + '\n' + similarityContent;

        Optional<String> data = modelRequestHandler.sendModel(codeGPTDrmConfig.getIdeaEvoCodePlanSystemPrompt(), SOURCE,
                queryFinal, modelEnv);
        if(data.isEmpty()){
            LOGGER.warn("ideaEvo解析代码规划结果失败, 模型输出: {}", data);
            throw new BizException(ResponseEnum.IDEA_EVO_OUTPUT_FORMAT_ERROR);
        }
        data = ModelRequestHandler.parseCodeBlocks(data.get());
        if(data.isEmpty()){
            LOGGER.warn("ideaEvo解析代码规划结果失败, 模型输出: {}", data);
            throw new BizException(ResponseEnum.IDEA_EVO_OUTPUT_FORMAT_ERROR);
        }
        JSONObject result = null;
        try {
            result = JSON.parseObject(data.get());
        }catch (Exception e){
            LOGGER.error("解析代码规划结果失败",e);
            throw new BizException(ResponseEnum.IDEA_EVO_OUTPUT_FORMAT_ERROR);
        }
        // 返回结果
        return result;
    }

    @Override
    public JSONArray aiCodeGen(JSONObject featureData, String modelEnv) {
        // 需求解析
        String similarityContent = parseCodeGenFeatures(featureData);

        // 拼接
        String queryFinal = codeGPTDrmConfig.getIdeaEvoCodeGenPromptTemplate() + '\n' + similarityContent;

        // 返回结果
        Optional<String> message = modelRequestHandler.sendModel(codeGPTDrmConfig.getIdeaEvoCodeGenSystemPrompt(),
                SOURCE, queryFinal, modelEnv);
        if(message.isEmpty()){
            LOGGER.warn("ideaEvo解析代码生成结果失败, 模型输出: {}", message);
            throw new BizException(ResponseEnum.IDEA_EVO_OUTPUT_FORMAT_ERROR);
        }
        message = ModelRequestHandler.parseCodeBlocks(message.get());
        if(message.isEmpty()){
            LOGGER.warn("ideaEvo解析代码生成结果失败, 模型输出: {}", message);
            throw new BizException(ResponseEnum.IDEA_EVO_OUTPUT_FORMAT_ERROR);
        }
        JSONArray result = null;
        try {
            result = JSON.parseArray(message.get());
        }catch (Exception e){
            LOGGER.error("解析代码生成结果失败",e);
            throw new BizException(ResponseEnum.IDEA_EVO_OUTPUT_FORMAT_ERROR);
        }
        return result;
    }

    private String parseCodeGenFeatures(JSONObject features) {
        /*
         * 解析query下面的用户需求和相似代码片段，相似代码路径数据
         */

        // 用户需求
        String userRequest = features.getString("user_request");
        LOGGER.info("user_request: {}", userRequest);

        // 路径 计划生成 相似代码 拼接
        List<String> similarityList = new ArrayList<>();
        int index = 1;
        JSONArray similarityContent = features.getJSONArray("similarity_content");
        for (int i = 0; i < similarityContent.size(); i++) {
            JSONObject data = similarityContent.getJSONObject(i);
            String path = data.getString("path");
            String plan = data.getString("plan");
            String similarityCode = data.getString("similarity_code");
            similarityList.add("文件" + index + ":" + path);
            similarityList.add("文件" + index + "修改计划:" + plan);
            similarityList.add("文件" + index + "相似代码片段:" + similarityCode);
            index++;
        }

        // 相似结果拼接
        String similarityContentStr = String.join("\n", similarityList);

        // 用户需求 + 相似结果拼接
        String resData = userRequest + "\n" + similarityContentStr;

        return resData;
    }

    private String parseCodePlanFeatures(JSONObject features) {
        /*
         * 解析query下面的用户需求和相似代码片段，相似代码路径数据
         */

        // 用户需求
        String userRequest = features.getString("user_request");
        LOGGER.info("user_request: {}", userRequest);

        // 相似代码结果 list
        List<String> similarityList = new ArrayList<>();
        int index = 1;
        JSONArray similarityContent = features.getJSONArray("similarity_content");
        for (int i = 0; i < similarityContent.size(); i++) {
            JSONObject data = similarityContent.getJSONObject(i);
            String path = data.getString("path");
            String similarityCode = data.getString("similarity_code");
            similarityList.add("文件" + index + ":" + path);
            similarityList.add("文件" + index + "相似代码片段:" + similarityCode);
            index++;
        }

        // 相似结果拼接
        String similarityContentStr = String.join("\n", similarityList);

        // 用户需求 + 相似结果拼接

        return userRequest + "\n" + similarityContentStr;
    }

}
