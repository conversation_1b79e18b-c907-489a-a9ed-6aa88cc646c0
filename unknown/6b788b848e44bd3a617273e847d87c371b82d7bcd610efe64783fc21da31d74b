package com.alipay.codegencore.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @version : SceneTypeEnum.java, v 0.1 2023年10月25日 16:34 baoping Exp $
 */
public enum SceneTypeEnum {
    /**
     * 默认场景
     */
    DEFAULTCHAT(1),
    /**
     * 上传文档场景
     */
    DOCUMENTCHAT(2),
    /**
     * 会话级别提交表单场景
     */
    FROMCHAT(3),
    /**
     * 仓库级别提交表单场景
     */
    REPOCHAT(4);

    private int value;

    SceneTypeEnum(int value) {
        this.value = value;
    }

    @JsonValue
    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    /**
     * 通过value，将int转化为{@link SceneTypeEnum}
     *
     * @param value
     * @return
     */
    public static SceneTypeEnum getSceneTypeEnumByValue(int value) {
        for (SceneTypeEnum sceneTypeEnum : SceneTypeEnum.values()) {
            if (sceneTypeEnum.getValue() == value) {
                return sceneTypeEnum;
            }
        }
        return DEFAULTCHAT;
    }
}
