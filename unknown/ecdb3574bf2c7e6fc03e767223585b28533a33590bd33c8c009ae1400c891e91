package com.alipay.codegencore.dal.mapper;

import java.util.List;

import com.alipay.codegencore.dal.example.UserFeedbackDOExample;
import com.alipay.codegencore.model.domain.UserFeedbackDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface UserFeedbackDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    long countByExample(UserFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    int deleteByExample(UserFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    @Delete({
            "delete from cg_user_feedback",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    @Insert({
            "insert into cg_user_feedback (gmt_create, gmt_modified, ",
            "user_id, content, ",
            "pictures, status, ",
            "acceptance_user, scene_id)",
            "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
            "#{userId,jdbcType=BIGINT}, #{content,jdbcType=VARCHAR}, ",
            "#{pictures,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, ",
            "#{acceptanceUser,jdbcType=BIGINT}, #{sceneId,jdbcType=BIGINT})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(UserFeedbackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    int insertSelective(UserFeedbackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    List<UserFeedbackDO> selectByExample(UserFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    @Select({
            "select",
            "id, gmt_create, gmt_modified, user_id, content, pictures, status, acceptance_user, ",
            "scene_id",
            "from cg_user_feedback",
            "where id = #{id,jdbcType=BIGINT}"
    })
    UserFeedbackDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    int updateByExampleSelective(@Param("record") UserFeedbackDO record, @Param("example") UserFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    int updateByExample(@Param("record") UserFeedbackDO record, @Param("example") UserFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    int updateByPrimaryKeySelective(UserFeedbackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_feedback
     *
     * @mbg.generated Tue Nov 14 10:39:51 CST 2023
     */
    @Update({
            "update cg_user_feedback",
            "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
            "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
            "user_id = #{userId,jdbcType=BIGINT},",
            "content = #{content,jdbcType=VARCHAR},",
            "pictures = #{pictures,jdbcType=VARCHAR},",
            "status = #{status,jdbcType=TINYINT},",
            "acceptance_user = #{acceptanceUser,jdbcType=BIGINT},",
            "scene_id = #{sceneId,jdbcType=BIGINT}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(UserFeedbackDO record);
}