/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.codegencore.web.links;

import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.links.GptConversationModel;
import com.alipay.codegencore.model.model.links.LinksResult;
import com.alipay.codegencore.model.model.links.WorkItemInfo;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.linke.ApprovalUser;
import com.alipay.codegencore.model.response.linke.WorkItemVO;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.LinksApiService;
import com.alipay.codegencore.web.codegpt.ChatMessageFormController;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version LinkeCopilotApi.java, v 0.1 2024年04月15日 下午4:10 wb-tzg858080
 */
@RestController
@CodeTalkWebApi
@RequestMapping("/cors")
public class LinkeCopilotApi{
    @Resource
    private LinksApiService linksApiService;
    @Resource
    private UserAclService userAclService;
    @Resource
    private ChatMessageFormController chatMessageFormController;

    /**
     * 获取应用信息
     *
     * @param conversationId 会话ID
     * @param query 检索内容
     * @return 应用列表
     */
    @GetMapping("/copilot/conversation/{conversationId}/getAppNames")
    public LinksResult<List<String>> getAppNames(@PathVariable String conversationId, @RequestParam(required = false) String query) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        GptConversationModel conversation = linksApiService.getConversationById(conversationId,true);
        String copilotSessionId = conversation.getCopilotSessionId();
        BaseResponse<List<String>> appNames = chatMessageFormController.getAppNames(query, currentUser.getEmpId(), copilotSessionId);
        return LinksResult.success(appNames.getData());
    }

    /**
     * 获取工作项列表
     *
     * @param conversationId 会话ID
     * @param query          检索内容
     * @return 获取工作项列表
     */
    @GetMapping("/copilot/conversation/{conversationId}/getWorkItemList")
    public LinksResult<List<WorkItemInfo>> getWorkItemList(@PathVariable String conversationId, @RequestParam(required = false) String query) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        GptConversationModel conversation = linksApiService.getConversationById(conversationId,true);
        String copilotSessionId = conversation.getCopilotSessionId();
        BaseResponse<List<WorkItemVO>> workItemList = chatMessageFormController.getWorkItemList(currentUser.getEmpId(), query, copilotSessionId);
        List<WorkItemInfo> result = new ArrayList<>();
        for (WorkItemVO workItemVO : workItemList.getData()) {
            WorkItemInfo workItemInfo = new WorkItemInfo();
            BeanUtils.copyProperties(workItemVO, workItemInfo);
            workItemInfo.setIssueURL(workItemVO.getIssueUrl());
            result.add(workItemInfo);
        }
        return LinksResult.success(result);
    }
    /**
     * 获取评审人列表
     *
     * @param conversationId 会话ID
     * @param query          检索内容
     * @return 获取工作项列表
     */
    @GetMapping("/copilot/conversation/{conversationId}/getApprovalUser")
    public LinksResult<List<ApprovalUser>> getApprovalUser(@PathVariable String conversationId, @RequestParam(required = false) String query) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        GptConversationModel conversation = linksApiService.getConversationById(conversationId,true);
        String copilotSessionId = conversation.getCopilotSessionId();
        BaseResponse<List<ApprovalUser>> approvalUser = chatMessageFormController.getApprovalUser(currentUser.getEmpId(), query, copilotSessionId);
        return LinksResult.success(approvalUser.getData());
    }

    /**
     * 获取分支列表`
     *
     * @param conversationId 会话ID
     * @return 获取工作项列表
     */
    @GetMapping("/copilot/conversation/{conversationId}/getBranchList")
    public LinksResult<List<String>> getBranchList(@PathVariable String conversationId, @RequestParam(required = false) String query) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        GptConversationModel conversation = linksApiService.getConversationById(conversationId,true);
        String copilotSessionId = conversation.getCopilotSessionId();
        BaseResponse<List<String>> branchList = chatMessageFormController.getBranchList(currentUser.getEmpId(), query, copilotSessionId);
        return LinksResult.success(branchList.getData());
    }


}
