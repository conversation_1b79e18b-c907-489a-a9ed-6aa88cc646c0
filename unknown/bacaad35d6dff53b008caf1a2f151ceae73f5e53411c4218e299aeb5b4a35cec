package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.request.aci.ACIPipelineExecuteRequest;
import com.alipay.codegencore.model.request.aci.ACIPipelineParameter;
import com.alipay.codegencore.model.response.aci.ACIPipelineDetail;
import com.alipay.codegencore.model.response.aci.ACIPipelineTemplate;
import com.alipay.codegencore.model.response.aci.ACIProject;
import com.alipay.codegencore.service.codegpt.DevInsightService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.utils.http.GetBuilder;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.devapi.sdk.SignUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.05.15
 */
@Service
public class AciOpenapiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AciOpenapiService.class);
    // API_GATEWAY域名，预发环境: https://devapi-prepub.alipay.com
    private final static String API_GATEWAY_HOST = "https://devapi.alipay.com";
    private final static String ACI_PIPELINE_API = "/aci/api/v1/pipeline";
    private final static String ACI_PROJECT_API = "/aci/api/v1/project";
    private final static String ACI_VCS_REPO_API = "/aci/api/v1/vcs-repo";
    private final static String ACI_PIPELINE_TEMPLATE = "/aci/api/v1/pipeline-template";
    private final static String ACI_PIPELINE_REPOSITORY = "/aci/api/v1/repository";
    private final static String ACI_COMPONENT_API = "/aci/api/v1/componentByPage";

    @Resource
    private ConfigService configService;
    @Resource
    private UserAclService userAclService;
    @Resource
    private DevInsightService devInsightService;



    /**
     * 触发ACI流水线
     *
     * <AUTHOR>
     * @since 2024.05.15
     * @param params params
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    public Map<String,String> triggerAciPipeline(JSONObject params) {
        LOGGER.info("trigger Pipeline params:{}",JSONObject.toJSONString(params));
        Map<String,String> result = new HashMap<>();
        ACIPipelineExecuteRequest request = new ACIPipelineExecuteRequest();
        String account = null;
        if (params != null) {
            String empId = params.getString("empId");
            if(StringUtils.isNotBlank(empId)){
                JSONObject user = JSONObject.parseObject(devInsightService.queryUser(empId).get(0).toString());
                if(user!=null){
                    account = user.getString("account");
                }
            }else {
                result.put("errorMsg","empId不能为空");
                return result;
            }
            if(StringUtils.isBlank(account)){
                result.put("errorMsg","empId无效");
                return result;
            }
            request.setPipelineTemplateId(params.getString("pipelineTemplateId"));
            request.setProjectId( params.getString("projectId"));
            request.setBranch(params.getString("branch"));
            request.setYmlPath(params.getString("ymlPath"));
            request.setYmlString(params.getString("ymlString"));
            if(params.containsKey("parameters")){
                List<ACIPipelineParameter> parameters = JSONArray.parseArray(params.getString("parameters"), ACIPipelineParameter.class);
                request.setParameters(parameters);
            }
        }else {
            result.put("errorMsg","参数不能为空");
            return result;
        }

        LOGGER.info("trigger Pipeline request:{}",JSONObject.toJSONString(request));
        String accessKey = configService.getConfigByKey("linkeAccessKey", false);
        String accessSecret = configService.getConfigByKey("linkeAccessSecret", false);
        JSONObject langChainResponse = null;

        long timestamp = System.currentTimeMillis();
        try {
            if(!"common".equalsIgnoreCase(params.getString("type"))){
                String response = HttpClient.post(API_GATEWAY_HOST + ACI_PIPELINE_API)
                        .header("AccessKey", accessKey)
                        .header("Signature", SignUtil.sign(accessKey, accessSecret, timestamp))
                        .header("Timestamp", String.valueOf(timestamp))
                        .header("Operator",account)
                        .content(JSONObject.toJSONString(request))
                        .syncExecuteWithExceptionThrow(10000);
                langChainResponse = JSONObject.parseObject(response);
            }else {
                JSONObject repositoryRequest = new JSONObject();
                repositoryRequest.put("projectId",params.getString("projectId"));
                repositoryRequest.put("branch",params.getString("branch"));
                repositoryRequest.put("tryType","try");
                String response = HttpClient.post(API_GATEWAY_HOST + ACI_PIPELINE_REPOSITORY+"/pipeline/"+request.getPipelineTemplateId())
                        .header("AccessKey", accessKey)
                        .header("Signature", SignUtil.sign(accessKey, accessSecret, timestamp))
                        .header("Timestamp", String.valueOf(timestamp))
                        .header("Operator",account)
                        .content(repositoryRequest.toJSONString())
                        .syncExecuteWithExceptionThrow(10000);
                langChainResponse = JSONObject.parseObject(response);
            }
            if(langChainResponse.getBoolean("success")){
                ACIPipelineDetail detail = langChainResponse.getObject("data", ACIPipelineDetail.class);
                LOGGER.info("triggerAciPipeline success! pipeline execute detail: {}",JSONObject.toJSONString(detail));
                result.put("pipelineExecuteId",detail.getId());
            }else {
                LOGGER.info("triggerAciPipeline failed! for: {}",langChainResponse.getString("errorMessage"));
                result.put("errorMsg",langChainResponse.getString("errorMessage"));
            }
        }catch (Exception e){
            LOGGER.error("triggerAciPipeline error:",e);
            result.put("errorMsg",e.getMessage());
        }
        return result;
    }

    /**
     * 获取ACI项目
     *
     * <AUTHOR>
     * @since 2024.05.16
     * @param title 模糊搜索工程名字
     * @return java.lang.String
     */
    public List<ACIProject> getAciProjectByName(String title){
        String accessKey = configService.getConfigByKey("linkeAccessKey", false);
        String accessSecret = configService.getConfigByKey("linkeAccessSecret", false);
        JSONObject langChainResponse = null;
        List<ACIProject> result = new ArrayList<>();
        long timestamp = System.currentTimeMillis();
        try {
            GetBuilder getBuilder = HttpClient.get(API_GATEWAY_HOST + ACI_PROJECT_API)
                    .header("AccessKey", accessKey)
                    .header("Signature", SignUtil.sign(accessKey, accessSecret, timestamp))
                    .header("Timestamp", String.valueOf(timestamp))
                    .header("Operator", getCurrentUser())
                    .addParameter("current", "1")
                    .addParameter("pageSize", "5");
            if(StringUtils.isNotBlank(title)){
                getBuilder.addParameter("title",title);
            }
            String response = getBuilder.syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
            if (langChainResponse!=null&&langChainResponse.getBoolean("success")) {
                result = JSONObject.parseObject(langChainResponse.getString("data")).getJSONArray("list").toJavaList(ACIProject.class);
            }else {
                LOGGER.info("getAciVcsRepoId failed! for: {}",langChainResponse.getString("errorMessage"));
            }
        }catch (Exception e){
            LOGGER.error("getAciVcsRepoId error:",e);
        }
        return result;
    }

    /**
     * 获取ACI项目分支列表
     *
     * <AUTHOR>
     * @since 2024.05.16
     * @param vcsRepoId vcsRepoId
     * @param platformId platformId
     * @return java.util.List<java.lang.String>
     */
    public List<String> getBranchList(String vcsRepoId,String platformId){
        String accessKey = configService.getConfigByKey("linkeAccessKey", false);
        String accessSecret = configService.getConfigByKey("linkeAccessSecret", false);
        JSONObject langChainResponse = null;
        List<String> branchList = new ArrayList<>();
        long timestamp = System.currentTimeMillis();
        try{
            String response = HttpClient.get(API_GATEWAY_HOST + ACI_VCS_REPO_API + "/" + vcsRepoId + "/branch")
                    .header("AccessKey", accessKey)
                    .header("Signature", SignUtil.sign(accessKey, accessSecret, timestamp))
                    .header("Timestamp", String.valueOf(timestamp))
                    .header("Operator", getCurrentUser())
                    .addParameter("vcsPlatformId",platformId)
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
            if(langChainResponse!=null&&langChainResponse.getBoolean("success")){
                branchList = JSONArray.parseArray(langChainResponse.getString("data"), String.class);
            }else {
                LOGGER.info("getBranchList failed! for: {}",langChainResponse.getString("errorMessage"));
            }

        }catch (Exception e){
            LOGGER.error("getBranchList error:",e);
        }
        return branchList;

    }

    /**
     * 查询ACI流水线模板
     *
     * <AUTHOR>
     * @since 2024.05.16
     * @param projectId projectId
     * @return java.util.List<com.alipay.codegencore.model.response.aci.ACIPipelineTemplate>
     */
    public List<ACIPipelineTemplate> getPipelineTemplates(String projectId) {
        String accessKey = configService.getConfigByKey("linkeAccessKey", false);
        String accessSecret = configService.getConfigByKey("linkeAccessSecret", false);
        JSONObject langChainResponse = null;
        List<ACIPipelineTemplate> pipelineTemplates = new ArrayList<>();
        long timestamp = System.currentTimeMillis();
        try {
            String response = HttpClient.get(API_GATEWAY_HOST + ACI_PIPELINE_TEMPLATE)
                    .header("AccessKey", accessKey)
                    .header("Signature", SignUtil.sign(accessKey, accessSecret, timestamp))
                    .header("Timestamp", String.valueOf(timestamp))
                    .header("Operator", getCurrentUser())
                    .addParameter("projectId", projectId)
                    .addParameter("current","1")
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
            if (langChainResponse!=null && langChainResponse.getBoolean("success")) {
                pipelineTemplates = JSONArray.parseArray(langChainResponse.getJSONObject("data").getString("list"),ACIPipelineTemplate.class);
            } else {
                LOGGER.info("getPipelineTemplates failed! for: {}", langChainResponse.getString("errorMessage"));
            }
        } catch (Exception e) {
            LOGGER.error("getPipelineTemplates error:", e);
        }
        return pipelineTemplates;
    }

    /**
     * 查询ACI仓库流水线模板
     *
     * <AUTHOR>
     * @since 2024.05.30
     * @param title title
     * @return java.util.List<com.alipay.codegencore.model.response.aci.ACIPipelineTemplate>
     */
    public List<ACIPipelineTemplate> getRepositoryPipelineTemplates(String title) {
        String accessKey = configService.getConfigByKey("linkeAccessKey", false);
        String accessSecret = configService.getConfigByKey("linkeAccessSecret", false);
        JSONObject langChainResponse = null;
        List<ACIPipelineTemplate> pipelineTemplates = new ArrayList<>();
        long timestamp = System.currentTimeMillis();
        try {
            String response = HttpClient.get(API_GATEWAY_HOST + ACI_PIPELINE_REPOSITORY + "/new")
                    .header("AccessKey", accessKey)
                    .header("Signature", SignUtil.sign(accessKey, accessSecret, timestamp))
                    .header("Timestamp", String.valueOf(timestamp))
                    .header("Operator", getCurrentUser())
                    .addParameter("current","1")
                    .addParameter("pageSize","5")
                    .addParameter("keyword",title)
                    .addParameter("isMy","false")
                    .addParameter("isClosed","false")
                    .addParameter("onShelf","true")
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
            if (langChainResponse!=null && langChainResponse.getBoolean("success")) {
                pipelineTemplates = JSONArray.parseArray(langChainResponse.getJSONObject("data").getString("list"),ACIPipelineTemplate.class);
            } else {
                LOGGER.info("getPipelineTemplates failed! for: {}", langChainResponse.getString("errorMessage"));
            }
        } catch (Exception e) {
            LOGGER.error("getPipelineTemplates error:", e);
        }
        return pipelineTemplates;
    }
    /**
     * 根据名称或描述搜索组件
     *
     * <AUTHOR>
     * @since 2024.06.14
     * @param query query
     * @return java.util.List<java.lang.String>
     */
    public List<String> getRepositoryComponent(String query) {
        String accessKey = configService.getConfigByKey("linkeAccessKey", false);
        String accessSecret = configService.getConfigByKey("linkeAccessSecret", false);
        JSONObject langChainResponse = null;
        List<String> componentList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("current",1);
        request.put("pageSize",5);
        request.put("onShelf",true);
        request.put("nameordescription",query);
        List<String> order = new ArrayList<>();
        order.add("-official");
        order.add("-gmtCreate");
        request.put("order",order);
        long timestamp = System.currentTimeMillis();
        try {
            String response = HttpClient.post(API_GATEWAY_HOST + ACI_COMPONENT_API)
                    .header("AccessKey", accessKey)
                    .header("Signature", SignUtil.sign(accessKey, accessSecret, timestamp))
                    .header("Timestamp", String.valueOf(timestamp))
                    .header("Operator", getCurrentUser())
                    .content(request.toJSONString())
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
            if (langChainResponse!=null && langChainResponse.getBoolean("success")) {
                List<JSONObject> components = JSONArray.parseArray(langChainResponse.getJSONObject("data").getString("list"), JSONObject.class);
                for (JSONObject component : components) {
                    componentList.add(component.getString("name"));
                }
            } else {
                LOGGER.info("get ACIComponent failed! for: {}", langChainResponse.getString("errorMessage"));
            }
        } catch (Exception e) {
            LOGGER.error("get ACIComponent error:", e);
        }
        return componentList;
    }


    /**
     * 获取当前用户的域账号
     *
     * <AUTHOR>
     * @since 2024.05.30
     * @return java.lang.String
     */
    private String getCurrentUser(){
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser!=null&&StringUtils.isNotBlank(currentUser.getEmpId())){
            JSONObject user = JSONObject.parseObject(devInsightService.queryUser(currentUser.getEmpId()).get(0).toString());
            if(user!=null){
                return user.getString("account");
            }
        }
        return null;
    }
}
