package com.alipay.codegencore.model.openai;

/**
 * OpenAI error details
 */
public class OpenAiErrorDetail {
    public OpenAiErrorDetail() {
    }

    /**
     * 全参数构造器
     * @param message
     * @param type
     * @param param
     * @param code
     */
    public OpenAiErrorDetail(String message, String type, String param, String code) {
        this.message = message;
        this.type = type;
        this.param = param;
        this.code = code;
    }

    /**
     * Human-readable error message
     */
    private String message;

    /**
     * OpenAI error type, for example "invalid_request_error"
     * https://platform.openai.com/docs/guides/error-codes/python-library-error-types
     */
    private String type;

    private String param;

    /**
     * OpenAI error code, for example "invalid_api_key"
     */
    private String code;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}