package com.alipay.codegencore.model.request.copilot;

/**
 * Request object for Copilot recommendation messages.
 */
public class CopilotRecommendMessageRequest {
    // Path to the repository
    private String repoPath;

    // Branch name
    private String branch;

    // Type of the page
    private String pageType;

    public String getRepoPath() {
        return repoPath;
    }

    public void setRepoPath(String repoPath) {
        this.repoPath = repoPath;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }
}
