package com.alipay.codegencore.dal.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

/**
 * 手写的TokenMapper,自动生成时无需覆盖
 */
public interface TokenManualMapper extends TokenDOMapper {

    @Update({
            "update cg_token set balance = balance + #{incBalance,jdbcType=DECIMAL} " +
                    "where user = #{user,jdbcType=VARCHAR}"
    })
    int incBalance(@Param("user") String user, @Param("incBalance") BigDecimal incBalance);

    @Update({
            "update cg_token set balance = balance - #{decBalance,jdbcType=DECIMAL} " +
                    "where user = #{user,jdbcType=VARCHAR}"
    })
    int decBalance(@Param("user") String user, @Param("decBalance") BigDecimal decBalance);

}
