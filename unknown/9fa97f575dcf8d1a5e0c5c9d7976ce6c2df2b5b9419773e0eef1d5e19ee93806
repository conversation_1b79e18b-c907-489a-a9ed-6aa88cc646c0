package com.alipay.codegencore.model.model.analysis;

import java.util.Map;

/**
 * 临时的代码分析结果
 * 针对单次补全请求进行深度分析后结果，请求线程结束后会擦除
 *
 * <AUTHOR>
 * 创建时间 2022-08-03
 */
public class TempCodeAnalysisResultContext extends AbstractCodeAnalysisResult {
    /**
     * 类字段引用信息映射.(类变量)
     * 例如 int NUM = 1;
     * key: NUM
     * value: int
     */
    private Map<String, String> fieldReferenceMap;

    /**
     * 最后一个方法内引用变量信息映射.(方法体内)
     * 例如 int NUM = 1;
     * key: NUM
     * value: int
     */
    private Map<String, String> localVariableMap;
    /**
     * 正在编写的方法
     */
    private MethodBodyModel writingMethodBodyModel;

    public MethodBodyModel getWritingMethodBodyModel() {
        return writingMethodBodyModel;
    }

    public void setWritingMethodBodyModel(MethodBodyModel writingMethodBodyModel) {
        this.writingMethodBodyModel = writingMethodBodyModel;
    }

    public Map<String, String> getFieldReferenceMap() {
        return fieldReferenceMap;
    }

    public void setFieldReferenceMap(Map<String, String> fieldReferenceMap) {
        this.fieldReferenceMap = fieldReferenceMap;
    }

    public Map<String, String> getLocalVariableMap() {
        return localVariableMap;
    }

    public void setLocalVariableMap(Map<String, String> localVariableMap) {
        this.localVariableMap = localVariableMap;
    }


}
