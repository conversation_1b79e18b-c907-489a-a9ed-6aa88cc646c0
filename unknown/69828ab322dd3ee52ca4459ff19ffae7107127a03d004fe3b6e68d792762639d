/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;


import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: FormItemInfo.java, v 0.1 2021-09-24 13:50 wb-tzg858080 Exp $$
 */
public class FormItemInfo extends ToString {
    /**
     * 字段类型 TEXT,SELECT,TIME
     */
    private FormItemType type ;
    /**
     * 显示名称
     */
    private  String showName ;
    /**
     * 返回的key
     */
    private String key;
    /**
     * 显示的值
     */
    private Object value;
    /**
     * 选项值
     */
    private List<FormSelectValue> selectValues;
    /**
     * 是否必填
     */
    private boolean isNeed;
    /**
     * 提示信息
     */
    private String tip;
    /**
     * 是否显示
     */
    private boolean disabled;

    /**
     * 页码
     */
    private int pageNum;

    /**
     * 每页大小
     */
    private int pageSize;

    /**
     * 总数
     */
    private int total;

    /**
     * 字段id
     */
    private Long id;

    public FormItemType getType() {
        return type;
    }

    public void setType(FormItemType type) {
        this.type = type;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public List<FormSelectValue> getSelectValues() {
        return selectValues;
    }

    public void setSelectValues(List<FormSelectValue> selectValues) {
        this.selectValues = selectValues;
    }

    public boolean isNeed() {
        return isNeed;
    }

    public void setNeed(boolean need) {
        isNeed = need;
    }

    public String getTip() {
        return tip;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 构造器
     * @param type
     * @param showName
     * @param key
     * @param value
     * @param selectValues
     * @param isNeed
     * @param tip
     * @param disabled
     */
    public FormItemInfo(FormItemType type, String showName, String key, Object value, List<FormSelectValue> selectValues, boolean isNeed, String tip, boolean disabled) {
        this.type = type;
        this.showName = showName;
        this.key = key;
        this.value = value;
        this.selectValues = selectValues;
        this.isNeed = isNeed;
        this.tip = tip;
        this.disabled = disabled;
    }

    public FormItemInfo() {
    }
}
