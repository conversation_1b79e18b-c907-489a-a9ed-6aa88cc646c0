/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.codegencore.service.middle.agentsecsdk;

import com.alipay.agentsecgateway.common.service.sdk.secsdk.AgentSecSdk;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * AgentSecSdkConfig 负责初始化 agentSecSdk
 * Agent平台接入agentSecSdk，可以对Agent操作的安全审核和管控，以增强AI Agent的安全性
 */
@Configuration
public class AgentSecSdkConfig {
    // 线上环境地址：https://agentsecgw-api.antgroup-inc.cn
    // dev环境地址：https://agentsecgateway.test.alipay.net
    // 预发环境地址: https://agentsecgw-api-pre.antgroup-inc.cn
    @AppConfig("agentsecsdk_endpoint")
    private String agentsecsdkEndpoint;

    @Resource
    private ConfigService configService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    /**
     * 初始化AgentSecSdk配置
     *
     * <AUTHOR>
     * @since 2024.04.24
     * @return com.alipay.agentsecgateway.common.service.sdk.secsdk.AgentSecSdk
     */
    @Bean
    public AgentSecSdk agentSecSdk() {
        String platformId = "codefuse";
        String accessKey = configService.getConfigByKey("agentSecSdkAccessKey",false);
        String secretKey = configService.getConfigByKey("agentSecSdkSecretKey",false);
        String sdkUrl = agentsecsdkEndpoint;

        return AgentSecSdk.getInstance(platformId, accessKey, secretKey, sdkUrl, codeGPTDrmConfig.getAgentSecSdkTimeout());
    }

}