package com.alipay.codegencore.model.request;

import com.alipay.codegencore.model.enums.UserStatusEnum;

import java.util.List;

/**
 * 更新用户的VO
 */
public class UpdateUserVO {

    /**
     * 用户ID
     */
    private List<Long> userIdList;
    /**
     * 1=更新为审核中
     * 2=审核通过
     */
    private UserStatusEnum newStatus;

    public List<Long> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Long> userIdList) {
        this.userIdList = userIdList;
    }

    public UserStatusEnum getNewStatus() {
        return newStatus;
    }

    public void setNewStatus(UserStatusEnum newStatus) {
        this.newStatus = newStatus;
    }
}
