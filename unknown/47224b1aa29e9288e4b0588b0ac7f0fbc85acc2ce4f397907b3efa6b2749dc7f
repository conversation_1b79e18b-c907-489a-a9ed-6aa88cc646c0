/*
  Alipay.com Inc.
  Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.model.links.Enum.DiagToolAppEnum;
import com.alipay.codegencore.model.model.links.Enum.GptMessageContentTypeEnum;
import com.alipay.codegencore.model.model.links.Enum.GptSourceEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $Id: GptMessageContent.java, v 0.1 2023-05-29 16:42 wb-tzg858080 Exp $$
 */
public class GptMessageContent extends BeanStringSwitcherImpl {
    /**
     * 消息类型
     */
    private GptMessageContentTypeEnum type;

    /**
     * 文本内容
     */
    private String text;
    /**
     * 总结行文字
     */
    private String summarize;
    /**
     * 来源
     */
    private List<ScriptLink> scriptLinks;

    /**
     * 推荐问题
     */
    private List<GptRecommend> recommends;

    /**
     * 状态
     */
    private String  status ;

    /**
     * 文本内容
     */
    private EntityReference reference;

    /**
     * 是否转人工
     */
    private Boolean isTransferLabor;

    /**
     * 诊断工具信息
     */
    private DiagTool diagTool;

    /**
     * 是否同意
     */
    private Boolean agreed ;

    /**
     * 仅当关闭GC才有此字段，refs不为null
     */
    private GptRef refs;

    /**
     * Copilot- 欢迎语
     */
    private CopilotWelcomeMessage welcomeMessage;

    /**
     * Copilot 回复内容
     */
    private CopilotAnswer copilotAnswer;

    /**
     * 站点助手的命令
     */
    private CopilotCommand copilotCommand ;

    /**
     * 知识库内容
     */
    private List<GptKnowledge> knowledges;

    /**
     * 是否安全
     */
    private Boolean isSecure = true ;

    /**
     * 自动工诊断具参数
     */
    private Map<String,Object> autoDiagnoseParams;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 是默认回复
     */
    private Boolean isDefaultReply;

    /**
     * 查询内容
     */
    private String queryContent;

    /**
     * GPT答案来源
     */
    private GptSourceEnum gptSource;


    /**
     * 多数据源消息内容
     */
    private List<GptMessageContent> contents;
    /**
     * 仓库信息
     */
    private JSONObject repoChatInfo;

    public GptMessageContentTypeEnum getType() {
        return type;
    }

    public void setType(GptMessageContentTypeEnum type) {
        this.type = type;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getSummarize() {
        return summarize;
    }

    public void setSummarize(String summarize) {
        this.summarize = summarize;
    }

    public List<ScriptLink> getScriptLinks() {
        return scriptLinks;
    }

    public void setScriptLinks(List<ScriptLink> scriptLinks) {
        this.scriptLinks = scriptLinks;
    }

    public List<GptRecommend> getRecommends() {
        return recommends;
    }

    public void setRecommends(List<GptRecommend> recommends) {
        this.recommends = recommends;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public EntityReference getReference() {
        return reference;
    }

    public void setReference(EntityReference reference) {
        this.reference = reference;
    }

    public Boolean getTransferLabor() {
        return isTransferLabor;
    }

    public void setTransferLabor(Boolean transferLabor) {
        isTransferLabor = transferLabor;
    }

    public DiagTool getDiagTool() {
        return diagTool;
    }

    public void setDiagTool(DiagTool diagTool) {
        this.diagTool = diagTool;
    }

    public Boolean getAgreed() {
        return agreed;
    }

    public void setAgreed(Boolean agreed) {
        this.agreed = agreed;
    }

    public GptRef getRefs() {
        return refs;
    }

    public void setRefs(GptRef refs) {
        this.refs = refs;
    }

    public CopilotWelcomeMessage getWelcomeMessage() {
        return welcomeMessage;
    }

    public void setWelcomeMessage(CopilotWelcomeMessage welcomeMessage) {
        this.welcomeMessage = welcomeMessage;
    }

    public CopilotAnswer getCopilotAnswer() {
        return copilotAnswer;
    }

    public void setCopilotAnswer(CopilotAnswer copilotAnswer) {
        this.copilotAnswer = copilotAnswer;
    }

    public CopilotCommand getCopilotCommand() {
        return copilotCommand;
    }

    public void setCopilotCommand(CopilotCommand copilotCommand) {
        this.copilotCommand = copilotCommand;
    }

    public List<GptKnowledge> getKnowledges() {
        return knowledges;
    }

    public void setKnowledges(List<GptKnowledge> knowledges) {
        this.knowledges = knowledges;
    }

    public Boolean getSecure() {
        return isSecure;
    }

    public void setSecure(Boolean secure) {
        isSecure = secure;
    }

    public Map<String, Object> getAutoDiagnoseParams() {
        return autoDiagnoseParams;
    }

    public void setAutoDiagnoseParams(Map<String, Object> autoDiagnoseParams) {
        this.autoDiagnoseParams = autoDiagnoseParams;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public Boolean getDefaultReply() {
        return isDefaultReply;
    }

    public void setDefaultReply(Boolean defaultReply) {
        isDefaultReply = defaultReply;
    }

    public String getQueryContent() {
        return queryContent;
    }

    public void setQueryContent(String queryContent) {
        this.queryContent = queryContent;
    }

    public GptSourceEnum getGptSource() {
        return gptSource;
    }

    public void setGptSource(GptSourceEnum gptSource) {
        this.gptSource = gptSource;
    }

    public List<GptMessageContent> getContents() {
        return contents;
    }

    public void setContents(List<GptMessageContent> contents) {
        this.contents = contents;
    }

    public JSONObject getRepoChatInfo() {
        return repoChatInfo;
    }

    public void setRepoChatInfo(JSONObject repoChatInfo) {
        this.repoChatInfo = repoChatInfo;
    }

    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param text text
     * @return com.alipay.codegencore.model.model.links.GptMessageContent
     */
    public static GptMessageContent getText(String text) {
        GptMessageContent gptMessageContent = new GptMessageContent();
        gptMessageContent.setType(GptMessageContentTypeEnum.TEXT);
        gptMessageContent.setText(text);
        return gptMessageContent;
    }

    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param summarize summarize
     * @param scriptLinks scriptLinks
     * @param diagTool diagTool
     * @param refs refs
     * @return com.alipay.codegencore.model.model.links.GptMessageContent
     */
    public static GptMessageContent getAiReply(String summarize, List<ScriptLink> scriptLinks, DiagTool diagTool,GptRef refs) {
        // 转换成links常用工具AppName
        if(diagTool != null){
            diagTool.setAppName(DiagToolAppEnum.getNameByCode(diagTool.getAppName()));
        }
        GptMessageContent gptMessageContent = new GptMessageContent();
        gptMessageContent.setType(GptMessageContentTypeEnum.AI_REPLY);
        gptMessageContent.setSummarize(summarize);
        gptMessageContent.setScriptLinks(scriptLinks);
        gptMessageContent.setDiagTool(diagTool);
        gptMessageContent.setRefs(refs);
        return gptMessageContent;
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param welcomeMessage welcomeMessage
     * @return com.alipay.codegencore.model.model.links.GptMessageContent
     */
    public static GptMessageContent getCopilotWelcomeMessage(CopilotWelcomeMessage welcomeMessage) {
        GptMessageContent gptMessageContent = new GptMessageContent();
        gptMessageContent.setType(GptMessageContentTypeEnum.COPILOT_WELCOME);
        gptMessageContent.setWelcomeMessage(welcomeMessage);
        return gptMessageContent;
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param copilotAnswer copilotAnswer
     * @return com.alipay.codegencore.model.model.links.GptMessageContent
     */
    public static GptMessageContent getCopilotAnswer(CopilotAnswer copilotAnswer,String command) {
        GptMessageContent gptMessageContent = new GptMessageContent();
        if(StringUtils.isNotBlank(command)){
            CopilotCommand copilotCommand = new CopilotCommand();
            copilotCommand.setCommand(command);
            gptMessageContent.setCopilotCommand(copilotCommand);
        }
        gptMessageContent.setType(GptMessageContentTypeEnum.COPILOT_ANSWER);
        gptMessageContent.setCopilotAnswer(copilotAnswer);
        return gptMessageContent;
    }
}
