<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.ChatSessionDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.ChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="prompt" jdbcType="VARCHAR" property="prompt" />
    <result column="model_config" jdbcType="VARCHAR" property="modelConfig" />
    <result column="scene_id" jdbcType="BIGINT" property="sceneId" />
    <result column="scene_test" jdbcType="TINYINT" property="sceneTest" />
    <result column="oss_address_list" jdbcType="VARCHAR" property="ossAddressList" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="source_platform" jdbcType="VARCHAR" property="sourcePlatform" />
    <result column="document_uid_list" jdbcType="VARCHAR" property="documentUidList" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    id, gmt_create, gmt_modified, uid, title, deleted, user_id, model, prompt, model_config, 
    scene_id, scene_test, oss_address_list, ext_info, source_platform, document_uid_list
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.ChatSessionDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_chat_session
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.ChatSessionDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    delete from cg_chat_session
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.ChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_chat_session
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="prompt != null">
        prompt,
      </if>
      <if test="modelConfig != null">
        model_config,
      </if>
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="sceneTest != null">
        scene_test,
      </if>
      <if test="ossAddressList != null">
        oss_address_list,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="sourcePlatform != null">
        source_platform,
      </if>
      <if test="documentUidList != null">
        document_uid_list,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="prompt != null">
        #{prompt,jdbcType=VARCHAR},
      </if>
      <if test="modelConfig != null">
        #{modelConfig,jdbcType=VARCHAR},
      </if>
      <if test="sceneId != null">
        #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="sceneTest != null">
        #{sceneTest,jdbcType=TINYINT},
      </if>
      <if test="ossAddressList != null">
        #{ossAddressList,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="sourcePlatform != null">
        #{sourcePlatform,jdbcType=VARCHAR},
      </if>
      <if test="documentUidList != null">
        #{documentUidList,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.ChatSessionDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    select count(*) from cg_chat_session
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    update cg_chat_session
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.uid != null">
        uid = #{record.uid,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.prompt != null">
        prompt = #{record.prompt,jdbcType=VARCHAR},
      </if>
      <if test="record.modelConfig != null">
        model_config = #{record.modelConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.sceneId != null">
        scene_id = #{record.sceneId,jdbcType=BIGINT},
      </if>
      <if test="record.sceneTest != null">
        scene_test = #{record.sceneTest,jdbcType=TINYINT},
      </if>
      <if test="record.ossAddressList != null">
        oss_address_list = #{record.ossAddressList,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePlatform != null">
        source_platform = #{record.sourcePlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.documentUidList != null">
        document_uid_list = #{record.documentUidList,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    update cg_chat_session
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      uid = #{record.uid,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      model = #{record.model,jdbcType=VARCHAR},
      prompt = #{record.prompt,jdbcType=VARCHAR},
      model_config = #{record.modelConfig,jdbcType=VARCHAR},
      scene_id = #{record.sceneId,jdbcType=BIGINT},
      scene_test = #{record.sceneTest,jdbcType=TINYINT},
      oss_address_list = #{record.ossAddressList,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      source_platform = #{record.sourcePlatform,jdbcType=VARCHAR},
      document_uid_list = #{record.documentUidList,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.ChatSessionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:32:25 CST 2024.
    -->
    update cg_chat_session
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="prompt != null">
        prompt = #{prompt,jdbcType=VARCHAR},
      </if>
      <if test="modelConfig != null">
        model_config = #{modelConfig,jdbcType=VARCHAR},
      </if>
      <if test="sceneId != null">
        scene_id = #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="sceneTest != null">
        scene_test = #{sceneTest,jdbcType=TINYINT},
      </if>
      <if test="ossAddressList != null">
        oss_address_list = #{ossAddressList,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="sourcePlatform != null">
        source_platform = #{sourcePlatform,jdbcType=VARCHAR},
      </if>
      <if test="documentUidList != null">
        document_uid_list = #{documentUidList,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>