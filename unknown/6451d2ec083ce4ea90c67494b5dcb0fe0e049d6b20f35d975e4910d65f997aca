package com.alipay.codegencore.model.model.tool.learning;

import com.alipay.codegencore.model.enums.LackParamStrategyEnum;

import java.util.Map;

/**
 * function call配置
 * <AUTHOR>
 */
public class FunctionCallConfig {
    /**
     * 用于function call 的模型
     */
    private String functionCallModel;
    /**
     * function call是否开启多轮支持
     */
    private Boolean functionCallMultiRoundEnabled;
    /**
     * 缺参策略
     */
    private LackParamStrategyEnum lackParamStrategy;
    /**
     * 是否关闭工具搜索
     */
    private Boolean toolSearchEnabled;
    /**
     * 多插件首轮调用function
     */
    private String firstFunctionName;
    /**
     * next调用function，key是上一个function value是下一个function
     */
    private Map<String, String> nextFunctionName;
    /**
     * 场景对话开始表单的jsonSchema信息
     */
    private String sceneJsonSchema;
    /**
     * 获取对话开始表单jsonSchema信息的url，http请求
     */
    private String sceneJsonSchemaUrl;
    /**
     * 场景对话开始表单的sceneUiSchema信息
     */
    private String sceneUiSchema;
    /**
     * 获取对话开始表单sceneUiSchema信息的url，http请求
     */
    private String sceneUiSchemaUrl;
    /**
     * 初始化提交表单按钮名称
     */
    private String emptyFormButtonName;
    /**
     * 提交表单按钮名称
     */
    private String formButtonName;

    /**
     * 是否可以直接发起回话
     */
    private Boolean canStartDirectly;
    /**
     * 文本分段策略类型
     */
    private String segmentationStrategyType;
    /**
     * 0 除第一轮问答，其他直接模型回答 1 每一轮都是插件调用
     */
    private Boolean callFunctionEveryRound;

    /**
     * 仓库问答
     */
    private Boolean repoChat;

    private Map<String,Object> customizeChatConfig;


    public FunctionCallConfig() {
    }

    /**
     * 构造函数
     * @param functionCallModel
     */
    public FunctionCallConfig(String functionCallModel) {
        this.functionCallModel = functionCallModel;
    }

    public Boolean getCallFunctionEveryRound() {
        return callFunctionEveryRound;
    }

    public void setCallFunctionEveryRound(Boolean callFunctionEveryRound) {
        this.callFunctionEveryRound = callFunctionEveryRound;
    }

    public String getFunctionCallModel() {
        return functionCallModel;
    }

    public void setFunctionCallModel(String functionCallModel) {
        this.functionCallModel = functionCallModel;
    }

    public LackParamStrategyEnum getLackParamStrategy() {
        return lackParamStrategy;
    }

    public void setLackParamStrategy(LackParamStrategyEnum lackParamStrategy) {
        this.lackParamStrategy = lackParamStrategy;
    }

    public Boolean getToolSearchEnabled() {
        return toolSearchEnabled;
    }

    public void setToolSearchEnabled(Boolean toolSearchEnabled) {
        this.toolSearchEnabled = toolSearchEnabled;
    }

    public Boolean getFunctionCallMultiRoundEnabled() {
        return functionCallMultiRoundEnabled;
    }

    public void setFunctionCallMultiRoundEnabled(Boolean functionCallMultiRoundEnabled) {
        this.functionCallMultiRoundEnabled = functionCallMultiRoundEnabled;
    }

    public String getFirstFunctionName() {
        return firstFunctionName;
    }

    public void setFirstFunctionName(String firstFunctionName) {
        this.firstFunctionName = firstFunctionName;
    }

    public Map<String, String> getNextFunctionName() {
        return nextFunctionName;
    }

    public void setNextFunctionName(Map<String, String> nextFunctionName) {
        this.nextFunctionName = nextFunctionName;
    }

    public String getSceneJsonSchema() {
        return sceneJsonSchema;
    }

    public void setSceneJsonSchema(String sceneJsonSchema) {
        this.sceneJsonSchema = sceneJsonSchema;
    }

    public String getSceneJsonSchemaUrl() {
        return sceneJsonSchemaUrl;
    }

    public void setSceneJsonSchemaUrl(String sceneJsonSchemaUrl) {
        this.sceneJsonSchemaUrl = sceneJsonSchemaUrl;
    }

    public String getSceneUiSchema() {
        return sceneUiSchema;
    }

    public void setSceneUiSchema(String sceneUiSchema) {
        this.sceneUiSchema = sceneUiSchema;
    }

    public String getSceneUiSchemaUrl() {
        return sceneUiSchemaUrl;
    }

    public void setSceneUiSchemaUrl(String sceneUiSchemaUrl) {
        this.sceneUiSchemaUrl = sceneUiSchemaUrl;
    }

    public String getEmptyFormButtonName() {
        return emptyFormButtonName;
    }

    public void setEmptyFormButtonName(String emptyFormButtonName) {
        this.emptyFormButtonName = emptyFormButtonName;
    }

    public String getFormButtonName() {
        return formButtonName;
    }

    public void setFormButtonName(String formButtonName) {
        this.formButtonName = formButtonName;
    }

    public Boolean getCanStartDirectly() {
        return canStartDirectly;
    }

    public void setCanStartDirectly(Boolean canStartDirectly) {
        this.canStartDirectly = canStartDirectly;
    }

    public String getSegmentationStrategyType() {
        return segmentationStrategyType;
    }

    public void setSegmentationStrategyType(String segmentationStrategyType) {
        this.segmentationStrategyType = segmentationStrategyType;
    }

    public Boolean getRepoChat() {
        return repoChat;
    }

    public void setRepoChat(Boolean repoChat) {
        this.repoChat = repoChat;
    }

    public Map<String, Object> getCustomizeChatConfig() {
        return customizeChatConfig;
    }

    public void setCustomizeChatConfig(Map<String, Object> customizeChatConfig) {
        this.customizeChatConfig = customizeChatConfig;
    }
}
