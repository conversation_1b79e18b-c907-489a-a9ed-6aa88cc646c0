package com.alipay.codegencore.model.alg;

/**
 * ab实验常量配置
 *
 * <AUTHOR>
 * 创建时间 2022-07-14
 */
public class AbTestConstants {

    /**
     * 大模型补全实验-支持非完整token补全业务场景
     */
    public static final String SCENEKEY_CLIENT_FLOW = "CLIENT_ALG_FLOW";

    /**
     * 非完整token补全
     */
    public static final String AB_CLIENT_FLOW_VER_A = "ONLINE";
    /**
     * 完整token补全
     */
    public static final String AB_CLIENT_FLOW_VER_B = "OFFLINE";
    /**
     * 非完成token补全实验 - A版本数据
     */
    private static final String AB_CLIENT_FLOW_VER_A_DATA = "{\"value\":\""+AB_CLIENT_FLOW_VER_A+"\",\"flow\":%s}";
    /**
     * 非完成token补全实验 - B版本数据
     */
    private static final String AB_CLIENT_FLOW_VER_B_DATA = "{\"value\":\""+AB_CLIENT_FLOW_VER_B+"\",\"flow\":%s}";

    /**
     * 客户端流量配置
     * A:在线流量
     * B:离线流量
     */
    public static final String AB_CLIENT_FLOW_DATA = "[{\"sceneKey\":\"" + SCENEKEY_CLIENT_FLOW + "\",\"defaultValue\":\"" + AB_CLIENT_FLOW_VER_A + "\",\"expVerArr\":["+AB_CLIENT_FLOW_VER_A_DATA+","+AB_CLIENT_FLOW_VER_B_DATA+"]}]";


}
