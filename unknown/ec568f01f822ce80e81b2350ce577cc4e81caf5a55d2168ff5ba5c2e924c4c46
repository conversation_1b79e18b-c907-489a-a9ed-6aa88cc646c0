/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links.VO;

import com.alipay.codegencore.model.model.links.GptMessageFeedbackContent;

/**
 * <AUTHOR>
 * @version $Id: 2023-05-30 12:22ManagerImpl.java, v 0.1 2023-05-30 12:22 tanzhigang Exp $$
 */
public class GptMessageFeedbackVO extends BaseVO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 是否赞同
     */
    private Boolean agreed;

    /**
     * 反馈内容
     */
    private GptMessageFeedbackContent content;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public Boolean getAgreed() {
        return agreed;
    }

    public void setAgreed(<PERSON><PERSON><PERSON> agreed) {
        this.agreed = agreed;
    }

    public GptMessageFeedbackContent getContent() {
        return content;
    }

    public void setContent(GptMessageFeedbackContent content) {
        this.content = content;
    }
}