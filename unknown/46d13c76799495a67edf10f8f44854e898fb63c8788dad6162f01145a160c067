package com.alipay.codegencore.model.model;

import java.util.List;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.model
 * @CreateTime : 2024-02-21
 */
public class ModelAvailableServer {
    /**
     * chainName
     */
    private String      chainName;
    /**
     * 预发
     */
    private ArrangeInfo pre;
    /**
     * 生产
     */
    private ArrangeInfo prod;
    /**
     * 发布域
     */
    private String      publishDomain;
    /**
     * sceneName
     */
    private String      sceneName;

    public ArrangeInfo getPre() {
        return pre;
    }

    public void setPre(ArrangeInfo pre) {
        this.pre = pre;
    }

    public ArrangeInfo getProd() {
        return prod;
    }

    public void setProd(ArrangeInfo prod) {
        this.prod = prod;
    }

    /**
     * 部署信息
     */
    public class ArrangeInfo {
        /**
         * summary
         */
        private SummaryInfo      summary;
        /**
         * checkTime
         */
        private String           checkTime;
        /**
         * 检查历史
         */
        private CheckHistoryInfo checkHistory;

        public SummaryInfo getSummary() {
            return summary;
        }

        public void setSummary(SummaryInfo summary) {
            this.summary = summary;
        }

        public String getCheckTime() {
            return checkTime;
        }

        public void setCheckTime(String checkTime) {
            this.checkTime = checkTime;
        }

        public CheckHistoryInfo getCheckHistory() {
            return checkHistory;
        }

        public void setCheckHistory(CheckHistoryInfo checkHistory) {
            this.checkHistory = checkHistory;
        }

        /**
         * summary
         */
        public class SummaryInfo {
            private boolean      isAvailable;
            private List<String> availableSever;

            public boolean isAvailable() {
                return isAvailable;
            }

            public void setAvailable(boolean available) {
                isAvailable = available;
            }

            public List<String> getAvailableSever() {
                return availableSever;
            }

            public void setAvailableSever(List<String> availableSever) {
                this.availableSever = availableSever;
            }
        }

        /**
         * 检查历史
         */
        public class CheckHistoryInfo {
            private String lastAvailable;
            private String lastUnavailable;

            public String getLastAvailable() {
                return lastAvailable;
            }

            public void setLastAvailable(String lastAvailable) {
                this.lastAvailable = lastAvailable;
            }

            public String getLastUnavailable() {
                return lastUnavailable;
            }

            public void setLastUnavailable(String lastUnavailable) {
                this.lastUnavailable = lastUnavailable;
            }
        }
    }

    public String getChainName() {
        return chainName;
    }

    public void setChainName(String chainName) {
        this.chainName = chainName;
    }

    public String getPublishDomain() {
        return publishDomain;
    }

    public void setPublishDomain(String publishDomain) {
        this.publishDomain = publishDomain;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

}
