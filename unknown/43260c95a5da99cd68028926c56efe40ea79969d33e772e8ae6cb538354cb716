/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version $Id: ReplayDetail.java, v 0.1 2021-09-29 9:51 wb-tzg858080 Exp $$
 */

public class ReplayDetailInfo extends ToString {
    /**
     * 问题原因
     */
    private String question;

    /**
     * 解决方案
     */
    private String plan;

    /**
     * 标题
     */
    private String title ;

    /**
     *  问题原因类型
     */
    private String  questionType ;

    /**
     * 解决方案类型
     */
    private String  planType ;

    /**
     * 表格前描述 问题原因
     */
    private String causeBeforeTableDes ;

    /**
     * 原因列表
     */
    private String causeColumns ;

    /**
     * 是否竖表   问题原因
     */
    private Boolean verticalTableProblem ;

    /**
     * 表格后描述 问题原因
     */
    private String causeAfterTableDes ;

    /**
     * 表格前描述 解决方案
     */
    private String requestBeforeTableDes ;

    /**
     * 是否竖表   解决方案
     */
    private Boolean verticalTableRequest ;

    /**
     * 请求列表
     */
    private String requestColumns ;


    /**
     * 表格后描述  解决方案
     */
    private String requestAfterTableDes ;

    /**
     * 问题原因需要折叠字段  -气泡展示
     */
    private String needFoldColumnsProblem ;

    /**
     * 每页展示条数   默认8条
     */
    private Integer pageSize ;

    /**
     * 每页展示条数   默认8条
     */
    private Long ruleId ;

    /**
     * 表格头名称
     */
    private String diagTableTitle;

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getPlan() {
        return plan;
    }

    public void setPlan(String plan) {
        this.plan = plan;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getPlanType() {
        return planType;
    }

    public void setPlanType(String planType) {
        this.planType = planType;
    }

    public String getCauseBeforeTableDes() {
        return causeBeforeTableDes;
    }

    public void setCauseBeforeTableDes(String causeBeforeTableDes) {
        this.causeBeforeTableDes = causeBeforeTableDes;
    }

    public String getCauseColumns() {
        return causeColumns;
    }

    public void setCauseColumns(String causeColumns) {
        this.causeColumns = causeColumns;
    }

    public Boolean getVerticalTableProblem() {
        return verticalTableProblem;
    }

    public void setVerticalTableProblem(Boolean verticalTableProblem) {
        this.verticalTableProblem = verticalTableProblem;
    }

    public String getCauseAfterTableDes() {
        return causeAfterTableDes;
    }

    public void setCauseAfterTableDes(String causeAfterTableDes) {
        this.causeAfterTableDes = causeAfterTableDes;
    }

    public String getRequestBeforeTableDes() {
        return requestBeforeTableDes;
    }

    public void setRequestBeforeTableDes(String requestBeforeTableDes) {
        this.requestBeforeTableDes = requestBeforeTableDes;
    }

    public Boolean getVerticalTableRequest() {
        return verticalTableRequest;
    }

    public void setVerticalTableRequest(Boolean verticalTableRequest) {
        this.verticalTableRequest = verticalTableRequest;
    }

    public String getRequestColumns() {
        return requestColumns;
    }

    public void setRequestColumns(String requestColumns) {
        this.requestColumns = requestColumns;
    }

    public String getRequestAfterTableDes() {
        return requestAfterTableDes;
    }

    public void setRequestAfterTableDes(String requestAfterTableDes) {
        this.requestAfterTableDes = requestAfterTableDes;
    }

    public String getNeedFoldColumnsProblem() {
        return needFoldColumnsProblem;
    }

    public void setNeedFoldColumnsProblem(String needFoldColumnsProblem) {
        this.needFoldColumnsProblem = needFoldColumnsProblem;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getDiagTableTitle() {
        return diagTableTitle;
    }

    public void setDiagTableTitle(String diagTableTitle) {
        this.diagTableTitle = diagTableTitle;
    }

    public ReplayDetailInfo() {
    }

    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param question question
     * @param plan plan
     * @return null
     */
    public ReplayDetailInfo(String question, String plan) {
        this.question = question;
        this.plan = plan;
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param question question
     * @param plan plan
     * @param title title
     * @return null
     */
    public ReplayDetailInfo(String question, String plan, String title) {
        this.question = question;
        this.plan = plan;
        this.title = title;
    }

    /**
     * 构造诊断结果详情
     *
     * @param question               问题原因
     * @param plan                   解决方案
     * @param title                  标题
     * @param questionType           问题原因类型
     * @param planType               解决方案类型
     * @param causeBeforeTableDes    表格前描述 问题原因
     * @param causeColumns           原因列表
     * @param verticalTableProblem   是否竖表   问题原因
     * @param causeAfterTableDes     表格后描述 问题原因
     * @param requestBeforeTableDes  表格前描述 解决方案
     * @param verticalTableRequest   是否竖表   解决方案
     * @param requestColumns         请求列表
     * @param requestAfterTableDes   表格后描述  解决方案
     * @param needFoldColumnsProblem 问题原因需要折叠字段  -气泡展示
     * @param pageSize               每页展示条数   默认8条
     * @param ruleId                 每页展示条数   默认8条
     * @param diagTableTitle         表格头名称
     */
    public ReplayDetailInfo(String question, String plan, String title, String questionType, String planType,
                            String causeBeforeTableDes , String causeColumns , Boolean verticalTableProblem , String causeAfterTableDes ,
                            String requestBeforeTableDes , Boolean verticalTableRequest , String requestColumns, String requestAfterTableDes ,
                            String needFoldColumnsProblem, Integer pageSize, Long ruleId, String diagTableTitle) {
        this.question = question;
        this.plan = plan;
        this.title = title;
        this.questionType = questionType;
        this.planType = planType;
        this.causeBeforeTableDes = causeBeforeTableDes;
        this.causeColumns = causeColumns;
        this.verticalTableProblem = verticalTableProblem;
        this.causeAfterTableDes = causeAfterTableDes;
        this.requestBeforeTableDes = requestBeforeTableDes;
        this.verticalTableRequest = verticalTableRequest;
        this.requestColumns = requestColumns;
        this.requestAfterTableDes = requestAfterTableDes;
        this.needFoldColumnsProblem = needFoldColumnsProblem;
        this.pageSize = pageSize;
        this.ruleId = ruleId;
        this.diagTableTitle = diagTableTitle;

    }
}
