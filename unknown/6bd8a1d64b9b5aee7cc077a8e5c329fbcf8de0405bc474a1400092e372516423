package com.alipay.codegencore.model.enums;

/**
 * 会话角色
 * <AUTHOR>
 */
public enum ChatRoleEnum {

    /**
     * 系统角色
     */
    SYSTEM("system"),
    /**
     * 用户
     */
    USER("user"),
    /**
     * ai模型
     */
    ASSISTANT("assistant"),
    /**
     * 方法调用
     */
    FUNCTION("function");

    ChatRoleEnum(String name){
        this.name=name;
    }

    /**
     * 名称
     */
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据name获取枚举
     * @param name
     * @return
     */
    public static ChatRoleEnum getChatRoleEnumByName(String name) {
        for (ChatRoleEnum chatRoleEnum : values()) {
            if (chatRoleEnum.name.equals(name)) {
                return chatRoleEnum;
            }
        }
        return null;
    }
}
