/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2019 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links.Enum;

import com.alipay.codegencore.model.model.links.ResultCode;

/**
 * 通用结果码
 *
 * <AUTHOR>
 * @version $Id: CommonResultEnum.java, v 0.1 2019-04-22 19:33 zhi.huangcz Exp $$
 */
public enum CommonResultEnum implements ResultCode {
    /** 成功**/
    SUCCESS("SUCCESS", "处理成功"),
    /**
     * 创建语雀文档失败
     */
    LARK_DOC_CREATE_FAILED("LARK_DOC_CREATE_FAILED", "创建语雀文档失败"),
    /**
     * 查询语雀文档失败
     */
    LARK_DOC_CREATE_QUERY_FAILED("LARK_DOC_CREATE_FAILED", "查询语雀文档失败"),
    /** 非法参数**/
    INVALID_PARAMS("INVALID_PARAMS", "非法参数：%s"),
    /**
     * 非法参数：未在指定参数范围里
     */
    INVALID_PARAMS_NOT_IN_RANGE("INVALID_PARAMS_NOT_IN_RANGE", "非法参数：未在指定参数范围里"),
    /**
     * 非法参数：所有数据项都为空
     */
    INVALID_PARAMS_ALL_EMPTY("INVALID_PARAMS_ALL_EMPTY", "非法参数：所有数据项都为空"),
    /**
     * 参数类型转换错误
     */
    PARAM_TYPE_TRANSFER_ERROR("PARAM_TYPE_TRANSFER_ERROR", "参数类型转换错误"),
    /**系统异常，请稍后再试 **/
    SYSTEM_ERROR("SYSTEM_ERROR", "系统异常，请稍后再试"),
    /**
     * Hessian序列化失败
     */
    HESSIAN_ENCODE_FAILED("HESSIAN_ENCODE_FAILED", "Hessian序列化失败"),
    /**
     * Hessian反序列化失败
     */
    HESSIAN_DECODE_FAILED("HESSIAN_DECODE_FAILED", "Hessian反序列化失败"),
    /**
     * EPAAS请求失败
     */
    EPAAS_REQUEST_FAILED("EPAAS_REQUEST_FAILED", "EPAAS请求失败：%s"),
    /**
     * 输入流写入输出流失败
     */
    IS_2_OS_FAILED("IS_2_OS_FAILED", "输入流写入输出流失败"),
    /**
     * 不支持的文件类型
     */
    NOT_SUPPORTED_FILE("NOT_SUPPORTED_FILE", "不支持的文件类型"),
    /**
     * MongoId不能为空
     */
    MONGO_ID_BLANK("MONGO_ID_BLANK", "MongoId不能为空"),
    /**
     * 不安全富文本
     */
    RICH_TEXT_UNSAFE("RICH_TEXT_UNSAFE", "内容存在不安全信息（%s）"),

    /**
     * 法务协议未签署
     */
    GPT_USER_AGREEMENT_ERROR("GPT_USER_AGREEMENT_ERROR","%s");

    /** 结果码 */
    private String code;
    /** 结果消息 */
    private String message;

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 构造函数
     *
     * @param code 结果码
     * @param message 结果消息
     */
    private CommonResultEnum(String code, String message) {
        this.code = this.getClass().getSimpleName() + "#" + code;
        this.message = message;
    }

    /**
     * 是否
     *
     * @return
     */
    @Override
    public boolean isSuccessCode() {
        return SUCCESS.getCode().equalsIgnoreCase(this.getCode());
    }
}