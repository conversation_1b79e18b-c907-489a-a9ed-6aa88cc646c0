/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.links;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.UpdateSessionContextRequestModel;
import com.alipay.codegencore.model.model.links.*;
import com.alipay.codegencore.model.model.links.VO.GptConversationVO;
import com.alipay.codegencore.model.model.links.VO.GptMessageVO;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.LinksApiService;
import com.alipay.codegencore.service.tool.learning.plugin.CodeFuseCallLinke;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CopilotConversationApi.java, v 0.1 2023年11月30日 上午11:14 wb-tzg858080
 */
@RestController
@CodeTalkWebApi
@RequestMapping("/cors")
public class CopilotConversationApi{

    private static final Logger LOGGER = LoggerFactory.getLogger( CopilotConversationApi.class );
    private static final Logger OTHERS_INFO = LoggerFactory.getLogger("OTHERSINFO");
    private static final int MAX_PAGE_SIZE = 100;
    @Resource
    private LinksApiService linksApiService;
    @Resource
    private UserAclService userAclService;

    @Resource
    private CodeFuseCallLinke codeFuseCallLinke;
    @Resource
    private ChatSessionManageService chatSessionManageService;

    /**
     * 创建会话
     *
     * @param copilotId 助手id
     * @param forceCreate 是否强制创建，即使已有会话存在
     * @return 会话信息
     */
    @PostMapping("/copilot/{copilotId}/createConversation")
    public LinksResult<String> createConversation(@PathVariable String copilotId,@RequestParam(required = false, defaultValue = "false") boolean forceCreate ) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        GptConversationModel conversation = linksApiService.createConversation(copilotId, currentUser.getId(),forceCreate);
        return LinksResult.success(conversation.getConversationId());
    }

    /**
     * 获取站点助手会话消息
     *
     * @param conversationId 会话id
     * @return 会话结果
     */
    @GetMapping("/copilot/conversation/{conversationId}")
    public LinksResult<GptConversationVO> getConversation(@PathVariable String conversationId) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        // 会话权限校验
        if(!linksApiService.checkUserConversationAuth(String.valueOf(currentUser.getId().longValue()),conversationId)){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        GptConversationModel gptConversation = linksApiService.getConversationById(conversationId,true);
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(gptConversation.getMongoId());

        List<GptMessageModel> gptMessageModels = linksApiService.queryMessageByConversationId(conversationId);
        if(CollectionUtils.isEmpty(gptMessageModels)){
            return LinksResult.success(null);
        }
        GptConversationVO gptConversationVO = new GptConversationVO();
        LinksCovertUtil.GptConversationModelCoverToVO(gptConversation, gptConversationVO);

        // 设置扩展信息
        JSONObject extraInfo = JSON.parseObject(chatSessionDO.getExtInfo());
        gptConversationVO.setExtInfo(extraInfo);

        List<GptMessageVO> messages = new ArrayList<>();
        for (GptMessageModel gptMessageModel : gptMessageModels) {
            GptMessageVO gptMessageVO = new GptMessageVO();
            LinksCovertUtil.GptMessageModelCoverToVO(gptMessageModel, gptMessageVO);
            messages.add(gptMessageVO);
        }
        gptConversationVO.setMessages(messages);
        return LinksResult.success(gptConversationVO);
    }

    /**
     * 删除所有的会话信息
     *
     * @param copilotId 助手id
     * @return 是否删除成功
     */
    @PostMapping("/copilot/{copilotId}/deleteAll")
    public LinksResult<String> deleteAll(@PathVariable String copilotId) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        linksApiService.deleteAllCopilotConversation(copilotId, currentUser.getId());
        return LinksResult.success();
    }

    /**
     * 删除会话
     *
     * @param conversationId 会话id
     * @return 是否成功
     */
    @PostMapping("/copilot/conversation/{conversationId}/delete")
    public LinksResult<String> delete(@PathVariable String conversationId) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(!linksApiService.checkUserConversationAuth(String.valueOf(currentUser.getId().longValue()),conversationId)){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        linksApiService.deleteCopilotConversation(conversationId);
        return LinksResult.success();
    }

    /**
     * 获取会话列表
     *
     * @param copilotId 助手id
     * @param page      页码
     * @param size      每页条目数
     * @return 会话内容
     */
    @GetMapping("/copilot/{copilotId}/conversations")
    public PageResult<GptConversationVO> getConversations(@PathVariable String copilotId,
                                                          @RequestParam(required = false) String query,
                                                          @RequestParam(required = false, defaultValue = "0") Integer page,
                                                          @RequestParam(required = false, defaultValue = "20") Integer size,
                                                          @RequestParam(required = false) String repoPath) {
        if (size != null && size > MAX_PAGE_SIZE) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"最大只支持100条数据");
        }
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        CopilotConfig copilotConfig = linksApiService.getCopilotConfig(copilotId);
        if(copilotConfig == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"copilotId is not exist");
        }
        Integer sceneId = copilotConfig.getSceneId();
        PageResult<ChatSessionDO> result = linksApiService.getChatSessionUidByCopilot(sceneId.longValue(), currentUser.getId(), query, repoPath, page, size);
        if (result == null || CollectionUtils.isEmpty(result.getList())) {
            LOGGER.info("getConversations result is empty");
            return new PageResult<>(new ArrayList<>(), 0, page, size);
        }
        List<String> mongoIdList = result.getList().stream().map(ChatSessionDO::getUid).collect(Collectors.toList());
        List<GptConversationModel> conversationModels = linksApiService.getCopilotConversationList(mongoIdList);
        OTHERS_INFO.info("过滤前 mongoIdList: {}" ,JSONObject.toJSONString(mongoIdList));

        // 通过repoPath过滤会话，如果repoPath不为空，则只返回repoPath对应的会话或者无repoPath信息的会话
        Map<String, ChatSessionDO> chatSessionDOMap = result.getList().stream().collect(Collectors.toMap(ChatSessionDO::getUid, chatSessionDO -> chatSessionDO));
        List<GptConversationVO> resultVO = new ArrayList<>();
        for (GptConversationModel gptConversationModel : conversationModels) {
            String conversationId = gptConversationModel.getId();
            GptConversationVO gptConversationVO = new GptConversationVO();
            BeanUtils.copyProperties(gptConversationModel, gptConversationVO);
            ChatSessionDO chatSessionDO = chatSessionDOMap.get(gptConversationModel.getMongoId());
            JSONObject extraInfo = JSON.parseObject(chatSessionDO.getExtInfo());
            gptConversationVO.setExtInfo(extraInfo);

            GptMessageModel firstUserMessage = linksApiService.getFirstUserMessage(conversationId, currentUser.getId());
            if(firstUserMessage != null){
                GptMessageVO firstGptMessageVO = new GptMessageVO();
                LinksCovertUtil.GptMessageModelCoverToVO(firstUserMessage, firstGptMessageVO);
                gptConversationVO.setFirstMessage(firstGptMessageVO);
            }else {
                gptConversationVO.setFirstMessage(null);
                // 过滤掉没有用户消息的会话
                continue;
            }
            GptMessageModel lastBotMessage = linksApiService.getLastBotMessage(conversationId, currentUser.getId());
            if(lastBotMessage != null){
                GptMessageVO lastGptMessageVO = new GptMessageVO();
                LinksCovertUtil.GptMessageModelCoverToVO(lastBotMessage, lastGptMessageVO);
                gptConversationVO.setLastMessage(lastGptMessageVO);
                gptConversationVO.setGmtLastMessage(lastBotMessage.getGmtModified());
            }else {
                gptConversationVO.setLastMessage(null);
            }

            resultVO.add(gptConversationVO);
        }
        List<String> listSendBack = resultVO.stream().map(GptConversationVO::getConversationId).collect(Collectors.toList());
        OTHERS_INFO.info("最终返回的 mongoIdList: {}" ,JSONObject.toJSONString(listSendBack));
        return new PageResult<>(resultVO, result.getTotal(), result.getPage(), result.getPageSize());
    }

    /**
     * 批量设置会话的上下文
     *
     * @return
     */
    @PostMapping(path = "/message/form/upSessionContextBatch")
    public BaseResponse<Long> setSessionContextBatch(@RequestBody UpdateSessionContextRequestModel body) {
        codeFuseCallLinke.setSessionContextBatch(body);
        ChatSessionDO chatSession = chatSessionManageService.getChatSession(body.getSessionId());
        GptConversationModel conversation = linksApiService.getConversationById(body.getSessionId(), true);
        GptConversationExtInfo extInfo = conversation.getExtInfo();
        if(StringUtils.isNotBlank(chatSession.getExtInfo())){
            extInfo.setExtInfo(JSON.parseObject(chatSession.getExtInfo()));
        }
        linksApiService.updateCopilotConversation(conversation);
        return BaseResponse.buildSuccess();
    }

}
