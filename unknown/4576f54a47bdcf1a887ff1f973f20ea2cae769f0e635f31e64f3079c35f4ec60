package com.alipay.codegencore.model.model;

/**
 * codegpt配置，支持大模型
 * 后续待大模型稳定后.{@link CompletionConfigModel}对象会下线
 * <AUTHOR>
 * 创建时间 2023-03-06
 */
public class CodeGptConfigModel {
    /**
     * 远程超时时间(单位：ms）
     */
    private long remoteTimeOut = 10000L;
    /**
     * 过滤补全请求的正则表达式
     */
    private String completionRegular = "[a-zA-Z0-9\\_\\.\\>\\<\\+\\-\\@\\=\\(\\{\\[\\*\\/ ]";

    /**
     * 定时任务间隔时间（单位：毫秒）
     */
    private long intervalTime = 1000*60*30;

    public long getIntervalTime() {
        return intervalTime;
    }

    public void setIntervalTime(long intervalTime) {
        this.intervalTime = intervalTime;
    }

    public long getRemoteTimeOut() {
        return remoteTimeOut;
    }

    public void setRemoteTimeOut(long remoteTimeOut) {
        this.remoteTimeOut = remoteTimeOut;
    }

    public String getCompletionRegular() {
        return completionRegular;
    }

    public void setCompletionRegular(String completionRegular) {
        this.completionRegular = completionRegular;
    }
}
