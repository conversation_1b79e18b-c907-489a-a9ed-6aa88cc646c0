package com.alipay.codegencore.dal.mapper;

import java.util.List;

import com.alipay.codegencore.dal.example.AlgoBackendDOExample;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface AlgoBackendDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    long countByExample(AlgoBackendDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    int deleteByExample(AlgoBackendDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    @Delete({
            "delete from cg_algo_backend",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    @Insert({
            "insert into cg_algo_backend (gmt_create, gmt_modified, ",
            "enable, model, jump, ",
            "visable_user, visable_env, ",
            "max_token, max_round, ",
            "impl, impl_config, ",
            "need_health_check, model_description, ",
            "enable_gpt_cache, owner_user_id, ",
            "model_base, usage_user_count, ",
            "usage_session_count, usage_message_count, ",
            "create_user_id, model_type, ",
            "support_plugin)",
            "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
            "#{enable,jdbcType=TINYINT}, #{model,jdbcType=VARCHAR}, #{jump,jdbcType=VARCHAR}, ",
            "#{visableUser,jdbcType=INTEGER}, #{visableEnv,jdbcType=INTEGER}, ",
            "#{maxToken,jdbcType=INTEGER}, #{maxRound,jdbcType=INTEGER}, ",
            "#{impl,jdbcType=VARCHAR}, #{implConfig,jdbcType=VARCHAR}, ",
            "#{needHealthCheck,jdbcType=TINYINT}, #{modelDescription,jdbcType=VARCHAR}, ",
            "#{enableGptCache,jdbcType=TINYINT}, #{ownerUserId,jdbcType=INTEGER}, ",
            "#{modelBase,jdbcType=TINYINT}, #{usageUserCount,jdbcType=INTEGER}, ",
            "#{usageSessionCount,jdbcType=INTEGER}, #{usageMessageCount,jdbcType=INTEGER}, ",
            "#{createUserId,jdbcType=INTEGER}, #{modelType,jdbcType=INTEGER}, ",
            "#{supportPlugin,jdbcType=TINYINT})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(AlgoBackendDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    int insertSelective(AlgoBackendDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    List<AlgoBackendDO> selectByExample(AlgoBackendDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    @Select({
            "select",
            "id, gmt_create, gmt_modified, enable, model, jump, visable_user, visable_env, ",
            "max_token, max_round, impl, impl_config, need_health_check, model_description, ",
            "enable_gpt_cache, owner_user_id, model_base, usage_user_count, usage_session_count, ",
            "usage_message_count, create_user_id, model_type, support_plugin",
            "from cg_algo_backend",
            "where id = #{id,jdbcType=BIGINT}"
    })
    AlgoBackendDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    int updateByExampleSelective(@Param("record") AlgoBackendDO record, @Param("example") AlgoBackendDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    int updateByExample(@Param("record") AlgoBackendDO record, @Param("example") AlgoBackendDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    int updateByPrimaryKeySelective(AlgoBackendDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    @Update({
            "update cg_algo_backend",
            "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
            "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
            "enable = #{enable,jdbcType=TINYINT},",
            "model = #{model,jdbcType=VARCHAR},",
            "jump = #{jump,jdbcType=VARCHAR},",
            "visable_user = #{visableUser,jdbcType=INTEGER},",
            "visable_env = #{visableEnv,jdbcType=INTEGER},",
            "max_token = #{maxToken,jdbcType=INTEGER},",
            "max_round = #{maxRound,jdbcType=INTEGER},",
            "impl = #{impl,jdbcType=VARCHAR},",
            "impl_config = #{implConfig,jdbcType=VARCHAR},",
            "need_health_check = #{needHealthCheck,jdbcType=TINYINT},",
            "model_description = #{modelDescription,jdbcType=VARCHAR},",
            "enable_gpt_cache = #{enableGptCache,jdbcType=TINYINT},",
            "owner_user_id = #{ownerUserId,jdbcType=INTEGER},",
            "model_base = #{modelBase,jdbcType=TINYINT},",
            "usage_user_count = #{usageUserCount,jdbcType=INTEGER},",
            "usage_session_count = #{usageSessionCount,jdbcType=INTEGER},",
            "usage_message_count = #{usageMessageCount,jdbcType=INTEGER},",
            "create_user_id = #{createUserId,jdbcType=INTEGER},",
            "model_type = #{modelType,jdbcType=INTEGER},",
            "support_plugin = #{supportPlugin,jdbcType=TINYINT}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(AlgoBackendDO record);
}