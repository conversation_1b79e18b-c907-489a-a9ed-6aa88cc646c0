package com.alipay.codegencore.service.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.handler
 * @CreateTime : 2023-08-31
 */
public class ChatGptModelHubHandler extends AbstractAlgLanguageHandler {

    private LanguageModelService hubChatGptModelService;

    /**
     * 抽象handler构造函数
     */
    public ChatGptModelHubHandler() {
        this.hubChatGptModelService = SpringUtil.getBean("HubChatGptModelService");
    }

    /**
     * 对话
     * @param gptAlgModelServiceRequest
     * @return
     */
    @Override
    public ChatMessage chat(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        return hubChatGptModelService.chat(gptAlgModelServiceRequest);
    }

    /**
     * 流式对话
     * @param gptAlgModelServiceRequest
     * @return
     */
    @Override
    public Flux<String> chatOnStream(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        hubChatGptModelService.streamChatForServlet(gptAlgModelServiceRequest);
        return null;
    }
}
