/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version GptMessageExtInfo.java, v 0.1 2024年02月02日 下午3:42 guojunke.gjk
 */
public class GptMessageExtInfo extends BeanStringSwitcherImpl {

    /**
     * 服务群会话id
     */
    private String sid;

    /**
     * 服务群消息id
     */
    private String messageId;

    /**
     * 接收者用户id
     */
    private String receiverUserId;

    /**
     * 原始发送人消息ID
     */
    private String originalSenderId;

    /**
     * 是否 已发送
     */
    private Boolean hasBeenSend  = false;

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getReceiverUserId() {
        return receiverUserId;
    }

    public void setReceiverUserId(String receiverUserId) {
        this.receiverUserId = receiverUserId;
    }

    public String getOriginalSenderId() {
        return originalSenderId;
    }

    public void setOriginalSenderId(String originalSenderId) {
        this.originalSenderId = originalSenderId;
    }

    public Boolean getHasBeenSend() {
        return hasBeenSend;
    }

    public void setHasBeenSend(Boolean hasBeenSend) {
        this.hasBeenSend = hasBeenSend;
    }
}
