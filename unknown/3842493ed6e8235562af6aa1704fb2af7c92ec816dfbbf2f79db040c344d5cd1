package com.alipay.codegencore.web.linkide;

import com.alipay.codegencore.model.model.CompletionsCodeModel;
import com.alipay.codegencore.model.request.CompletionsRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代码补全推荐controller(对外)
 *
 * <AUTHOR>
 * 创建时间 2022-01-06
 */
@RestController
@RequestMapping("/api/code")
public class RecommandController {

    /**
     * 代码补全推荐
     *
     * @param completionsRequestBean
     * @return
     */
    @PostMapping("/completions")
    public BaseResponse<CompletionsCodeModel> completions(@RequestBody CompletionsRequestBean completionsRequestBean) {
        return BaseResponse.buildSuccess();
    }



}
