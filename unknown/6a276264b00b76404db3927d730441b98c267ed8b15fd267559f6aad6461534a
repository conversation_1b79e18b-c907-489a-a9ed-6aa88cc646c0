package com.alipay.codegencore.model.model;

import java.util.List;
import java.util.Map;

/**
 * 场景识别结果模型(trie识别）
 * <AUTHOR>
 * 创建时间 2022-04-14
 */
public class TrieSceneModel extends AbstractSceneRecognitionModel {

    /**
     * 动态参数
     */
    private Map<String,String> dynamicParamMap;
    /**
     * 可变参数
     */
    private List<String> variableParamList;
    public Map<String, String> getDynamicParamMap() {
        return dynamicParamMap;
    }

    public void setDynamicParamMap(Map<String, String> dynamicParamMap) {
        this.dynamicParamMap = dynamicParamMap;
    }

    public List<String> getVariableParamList() {
        return variableParamList;
    }

    public void setVariableParamList(List<String> variableParamList) {
        this.variableParamList = variableParamList;
    }

}
