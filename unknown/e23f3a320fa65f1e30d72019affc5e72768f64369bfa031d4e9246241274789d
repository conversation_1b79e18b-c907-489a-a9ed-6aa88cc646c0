package com.alipay.codegencore.utils.file;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 文件处理工具
 */
public class FileProcessUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileProcessUtils.class);

    /**
     * 特殊正则表达式字符
     */
    private static final Pattern SPECIAL_REGEX_CHARS = Pattern.compile("[{}()\\[\\].+*?^$\\\\|]");

    /**
     * 文件解析为文本
     *
     * @param file
     * @return
     */
    public static String getFileContent(MultipartFile file) {
        String fileExtension = StringUtils.getFilenameExtension(file.getOriginalFilename());
        try (InputStream inputStream = file.getInputStream()) {
            return getFileContent(fileExtension, inputStream);
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("splitFile failed", e);
            throw new BizException(ResponseEnum.UPLOAD_FILE_FAILED);
        }
    }

    /**
     * 文件解析为文本
     *
     * @param inputStream
     * @return
     */
    public static String getFileContent(String fileExtension, InputStream inputStream) throws IOException {
        String fileContent;
        if ("pdf".equalsIgnoreCase(fileExtension)) {
            try (PDDocument document = Loader.loadPDF(inputStream)) {
                PDFTextStripper stripper = new PDFTextStripper();
                fileContent = stripper.getText(document);
            }
        } else if ("docx".equalsIgnoreCase(fileExtension)) {
            try (XWPFDocument docx = new XWPFDocument(inputStream);
                 XWPFWordExtractor extractor = new XWPFWordExtractor(docx)) {
                fileContent = extractor.getText();
            }
        } else if ("txt".equalsIgnoreCase(fileExtension)) {
            byte[] bytes = inputStream.readAllBytes();
            String charset = CharsetUtil.guessCharset(bytes);
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(bytes), charset))) {
                String line;
                StringBuilder sb = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    sb.append(line).append("\n");
                }
                fileContent = sb.toString();
            }
        } else {
            throw new BizException(ResponseEnum.FILE_TYPE_NOT_SUPPORTED);
        }
        return fileContent;
    }

    /**
     * 拆分一个文件内容
     *
     * @param fileContent
     * @param everyPartMaxSize 每个片段最大size
     * @return
     */
    public static List<String> splitFile(String fileContent, Integer everyPartMaxSize) {
        List<String> delimiters = Arrays.asList("\n", "\n\n", ".", "?", "!", ";", "。", "？", "！", "；");
        return splitTextByDelimiters(fileContent, delimiters, everyPartMaxSize);
    }

    /**
     * 根据分隔符对文本进行分段
     * @param text 文本
     * @param delimiters 分隔符列表
     * @param maxLength 每段的最大长度
     * @return
     */
    private static List<String> splitTextByDelimiters(String text, List<String> delimiters, int maxLength) {
        List<String> parts = new ArrayList<>();

        // 转义正则表达式的特殊字符
        List<String> escapedDelimiters = new ArrayList<>();
        for (String delimiter : delimiters) {
            String escapedDelimiter = SPECIAL_REGEX_CHARS.matcher(delimiter).replaceAll("\\\\$0");
            escapedDelimiters.add(escapedDelimiter);
        }

        // 使用零宽断言保留分隔符
        String regex = "(?<=(" + String.join("|", escapedDelimiters) + "))|(?=(" + String.join("|", escapedDelimiters) + "))";

        // 使用正则表达式来切分字符串，保留分隔符
        String[] splitByDelimiters = text.split(regex);

        StringBuilder currentPart = new StringBuilder();
        for (String segment : splitByDelimiters) {
            if (segment.isEmpty()) {
                continue;
            }

            if (currentPart.length() + segment.length() <= maxLength) {
                currentPart.append(segment);
            } else {
                if (currentPart.length() > 0) {
                    parts.add(currentPart.toString());
                    currentPart.setLength(0);
                }

                // 如果单个片段超过最大长度，需要进一步分割
                while (segment.length() > maxLength) {
                    parts.add(segment.substring(0, maxLength));
                    segment = segment.substring(maxLength);
                }

                currentPart.append(segment);
            }
        }

        if (currentPart.length() > 0) {
            parts.add(currentPart.toString());
        }

        return parts;
    }

}
