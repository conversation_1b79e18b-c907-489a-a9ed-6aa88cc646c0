/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.aop;

import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.codegencore.web.aop.handler.WebApiPreHandler;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version ControllerAop.java, v 0.1 2023年03月28日 20:24 xiaobin
 */
@Aspect
@Component
public class WebApiControllerAop {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebApiControllerAop.class);

    /**
     * 将所有的WebApiPreHandler实现类注入
     */
    @Resource
    private List<WebApiPreHandler> preHandlers;

    /**
     * 初始化,将preHandlers按照order排序
     */
    @PostConstruct
    public void init() {
        if (preHandlers == null) {
            preHandlers = new ArrayList<>(0);
            return;
        }
        preHandlers.sort(AnnotationAwareOrderComparator.INSTANCE);
    }

    /**
     * 所有有CodeTalkWebApi注解的方法都会被拦截
     */
    @Pointcut("@within(com.alipay.codegencore.model.annocation.CodeTalkWebApi)")
    public void requestMustLoginPointCut() {
    }

    @Pointcut("@annotation(com.alipay.codegencore.model.annocation.CodeTalkWebApi)")
    public void requestMustLoginPointCutMethod() {
    }

    /**
     * 对指定条件下的方法进行拦截，进行前置处理。处理器为WebApiPreHandler实现类
     *
     * @param jp
     * @return
     * @throws Throwable
     */
    @Around("requestMustLoginPointCut() || requestMustLoginPointCutMethod()")
    public Object aroundController(ProceedingJoinPoint jp) throws Throwable {
        Object result;
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            for (WebApiPreHandler preHandler : preHandlers) {
                preHandler.execute(request, response, jp.getSignature().getName(), jp.getArgs());
            }
            result = jp.proceed();
        } catch (Throwable e) {
            LOGGER.error("aroundController error", e);
            throw e;
        } finally {
            ContextUtil.remove();
        }
        return result;
    }

}