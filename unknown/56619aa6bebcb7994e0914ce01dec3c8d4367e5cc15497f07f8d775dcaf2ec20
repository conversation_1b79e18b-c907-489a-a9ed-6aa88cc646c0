/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.Date;

/**
 * <AUTHOR>
 * @version GptKnowledge.java, v 0.1 2024年01月02日 下午3:22 wb-tzg858080
 */
public class GptKnowledge {
    /**
     * 知识点Id
     */
    private String conversationId;
    /**
     * 类型（知识库会话融合）： snippet：知识库/conversation：会话 yuque:语雀
     */
    private String type = "conversation";

    /**
     * 发起时间
     */
    private Date startTime;

    /**
     * 发起人会话内容
     */
    private String startUserContent;

    /**
     * 会话内容
     */
    private String content;

    /**
     * 知识点子类型 RICH 富文本 MARKDOWN  REPLAYS排查工具
     */
    private String editorType;

    /**
     * suggestSnippetId ,links 维护
     */
    private String suggestSnippetId;
    /**
     * 排查工具类型对应的系统
     */
    private String outSystem;

    /**
     * 排查工具对象的业务id
     */
    private String outBizId;

    /**
     * 会话URL
     */
    private String conversationUrl;

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getStartUserContent() {
        return startUserContent;
    }

    public void setStartUserContent(String startUserContent) {
        this.startUserContent = startUserContent;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getEditorType() {
        return editorType;
    }

    public void setEditorType(String editorType) {
        this.editorType = editorType;
    }

    public String getSuggestSnippetId() {
        return suggestSnippetId;
    }

    public void setSuggestSnippetId(String suggestSnippetId) {
        this.suggestSnippetId = suggestSnippetId;
    }

    public String getOutSystem() {
        return outSystem;
    }

    public void setOutSystem(String outSystem) {
        this.outSystem = outSystem;
    }

    public String getOutBizId() {
        return outBizId;
    }

    public void setOutBizId(String outBizId) {
        this.outBizId = outBizId;
    }

    public String getConversationUrl() {
        return conversationUrl;
    }

    public void setConversationUrl(String conversationUrl) {
        this.conversationUrl = conversationUrl;
    }
}
