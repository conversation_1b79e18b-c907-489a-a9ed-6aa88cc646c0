package com.alipay.codegencore.service.common;

import java.io.InputStream;

/**
 * OSS服务接口定义。
 */
public interface OssService {
    /**
     * 将输入流中的数据上传到指定的OSS对象存储桶中，并设置对象过期时间为expireDays天。
     *
     * @param filePath    上传到OSS时使用的文件名。
     * @param inputStream 待上传的输入流。
     * @param expireDays  对象在OSS中保存的最长时间，以天为单位，null表示不过期。
     * @param contentType 文件类型,设置这个参数可以确保生成的链接浏览器访问的时候是下载行为还是打开行为
     * @return 返回一个签名后的url地址，用于访问该文件。
     */
    String putObject(String filePath, InputStream inputStream, Integer expireDays, String contentType);

    /**
     * 获取指定OSS对象存储桶中fileName对应的文件内容。
     *
     * @param filePath 文件在OSS中的唯一标识符。
     * @return 返回一个包含文件内容的字符串。
     */
    String getString(String filePath);

    /**
     * 获取指定OSS对象存储桶中fileName对应的文件内容。
     *
     * @param filePath 文件在OSS中的唯一标识符。
     * @return 返回一个包含文件内容的字符串。
     */
    InputStream getInputStream(String filePath);


    /**
     * 删除文件
     * @param filePath 文件路径
     */
    void deleteFile(String filePath);

    Boolean isExist(String filePath);
}
