package com.alipay.codegencore.web.buservice;

import com.alipay.fc.fcbuservice.sdk.acl.AclManagerUtil;
import com.alipay.fc.fcbuservice.sdk.common.domain.BuserviceLoginUser;
import com.alipay.fc.fcbuservice.sdk.sso.BuserviceLoginUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * Buservice 相关能力示例
 * <ul> BuserviceLoginUser UserModel
 * <li> private String id; //  buserviceUserId </li>
 * <li> private String channel;// BUC 域帐号类型, INNER  内部帐号类型 </li>
 * <li> private String staffNo; // 左不补0 </li>
 * <li> private String workNo; //  左补0 </li>
 * <li> private String nickName; // 昵称 </li>
 * <li> private String operatorName;  // 域帐号 </li>
 * </ul>
 */
@Controller
public class BuserviceSampleController {

    /**
     * 获取当前的用户，适用于三种场景
     * <ul>
     * <li> 任意位置获取当前登录用户 </li>
     * <li> ALL_VISIBLE 的地址，还需要获取当前登录用户 </li>
     * <li> 在Filter 之前获取当前登录用户 </li>
     * </ul>
     * <p> 本地测试方式: 应用启动后绑定hosts : 127.0.0.1 local.alipay.net
     * <p> 使用隐身窗口测试，地址  http://local.alipay.net:8888/buservice/user
     */
    @GetMapping("/buservice/user")
    @ResponseBody
    public String getFromRequest(HttpServletRequest request, HttpServletResponse response) {
        BuserviceLoginUser buserviceUser = BuserviceLoginUtil.getSimpleUser(request,response);

        return buserviceUser.getOperatorName();
    }

    /**
     * 获取当前filter 透传的 登录用户，【必须进过filter】 才可以使用
     * <p> 本地测试方式: 应用启动后绑定hosts : 127.0.0.1 local.alipay.net
     * <p> 使用隐身窗口测试，地址  http://local.alipay.net:8888/buservice/filterUser
     */
    @GetMapping("/buservice/filterUser")
    @ResponseBody
    public String getCurrentUserFromFilter() {
        BuserviceLoginUser buserviceUser = BuserviceLoginUtil.getCurrentUserFromFilter();
        return buserviceUser.getOperatorName();
    }


    /**
     * 使用acl文件判断鉴权, @see app/web/resources/security/security-home.acl
     * <p> 本地测试方式: 应用启动后绑定hosts : 127.0.0.1 local.alipay.net
     * <p> 测试地址 http://local.alipay.net:8888/buservice/testacl
     */
    @GetMapping("/buservice/testacl")
    @ResponseBody
    public String testacl() {
        return "acl paas";
    }


    /**
     * 使用acl文件判断鉴权, @see app/web/resources/security/security-home.acl
     * json 内容返回200 的响应，json判断逻辑具体见文档：https://yuque.antfin.com/antbuservice/1.0.0/qa#6SR9y
     * <p> 本地测试方式: 应用启动后绑定hosts : 127.0.0.1 local.alipay.net
     * <p> 测试地址  http://local.alipay.net:8888/buservice/testacl.json
     */
    @GetMapping("/buservice/testacl.json")
    @ResponseBody
    public String testaclJSON() {
        return "acl paas";
    }


    /**
     * 使用acl文件判断鉴权
     * <p> 本地测试方式: 应用启动后绑定hosts : 127.0.0.1 local.alipay.net
     * <p> 测试地址  http://local.alipay.net:8888/buservice/permission
     */
    @GetMapping("/buservice/permission")
    @ResponseBody
    public Map<String ,Boolean> checkPermission(HttpServletRequest request) {
        return AclManagerUtil.checkPermission(BuserviceLoginUtil.getCurrentUserFromFilter().getId(), request, "TEST_PERMISSION");
    }

}
