/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 * @version $Id: BeanStringSwitcherImpl.java, v 0.1 2020-05-08 16:57 zhi.huangcz Exp $$
 */
public class BeanStringSwitcherImpl extends ToString implements BeanStringSwitcher {
    /**
     * 转为JSON字符串
     *
     * @return
     */
    public String toJson() {
        return JSON.toJSONString(this);
    }

    /**
     * 字符串转为对象
     *
     * @param json
     * @return
     */
    public <T> T toBean(String json) {
        T bean = (T) JSON.parseObject(json, this.getClass());
        return bean;
    }
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01 
     * @return java.lang.String
     */
    public String toString() {
        return this.toJson();
    }
}