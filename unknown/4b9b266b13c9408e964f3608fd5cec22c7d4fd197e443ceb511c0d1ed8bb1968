/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.codescan;

import com.alipay.codegencore.utils.codescan.JavaParser;
import com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor;
import com.alipay.codegencore.utils.codescan.ScanTypeEnum;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ParseTree;
import org.antlr.v4.runtime.tree.RuleNode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import static org.junit.Assert.*;
import static org.smartunit.shaded.org.mockito.Mockito.*;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class JavaParserBaseVisitor_SSTest extends JavaParserBaseVisitor_SSTest_scaffolding {
// allCoveredLines:[14, 24, 35, 46, 57, 68, 90, 101, 123, 156, 167, 178, 189, 200, 211, 222, 233, 244, 255, 266, 277, 288, 299, 310, 321, 332, 365, 376, 387, 409, 420, 431, 453, 464, 475, 486, 497, 508, 519, 541, 552, 563, 574, 585, 596, 607, 618, 640, 662, 673, 684, 695, 706, 728, 761, 772, 783, 794, 805, 816, 827, 838, 849, 860, 871, 882, 893, 915, 926, 937, 948, 959, 970, 981, 992, 1003, 1014, 1036, 1047, 1058, 1069, 1080, 1091, 1102, 1113, 1124, 1135, 1146, 1157, 1168, 1179, 1190, 1201, 1212, 1245, 1256, 1267, 1278, 1300, 1311, 1322, 1344, 1355, 1366, 1377]

  @Test(timeout = 4000)
  public void test_visit_000()  throws Throwable  {
      //caseID:0c092ca4b878dadb78c5382101e1a807
      //CoveredLines: [14, 24]
      //Input_0_ParseTree: {}
      //Input_1_ScanTypeEnum: ScanTypeEnum.TEMP_REQUEST
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      ScanTypeEnum scanTypeEnum0 = ScanTypeEnum.TEMP_REQUEST;
      
      //Call method: visit
      Integer integer0 = javaParserBaseVisitor0.visit(parseTree0, scanTypeEnum0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitAnnotation_001()  throws Throwable  {
      //caseID:7dec74d1428be8aed20d24ba214f1f21
      //CoveredLines: [14, 640]
      //Input_0_JavaParser.AnnotationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_AnnotationContext0
      JavaParser.AnnotationContext javaParser_AnnotationContext0 = mock(JavaParser.AnnotationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitAnnotation
      Object object0 = javaParserBaseVisitor0.visitAnnotation(javaParser_AnnotationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitAnnotationConstantRest_002()  throws Throwable  {
      //caseID:098aa2e805dd1f6323d556e63a93f11e
      //CoveredLines: [14, 761]
      //Input_0_JavaParser.AnnotationConstantRestContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_AnnotationConstantRestContext0
      JavaParser.AnnotationConstantRestContext javaParser_AnnotationConstantRestContext0 = mock(JavaParser.AnnotationConstantRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitAnnotationConstantRest
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitAnnotationConstantRest(javaParser_AnnotationConstantRestContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitAnnotationTypeBody_003()  throws Throwable  {
      //caseID:f4749dc99f1c9db080be00a465735255
      //CoveredLines: [14, 706]
      //Input_0_JavaParser.AnnotationTypeBodyContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_AnnotationTypeBodyContext0
      JavaParser.AnnotationTypeBodyContext javaParser_AnnotationTypeBodyContext0 = mock(JavaParser.AnnotationTypeBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitAnnotationTypeBody
      Object object0 = javaParserBaseVisitor0.visitAnnotationTypeBody(javaParser_AnnotationTypeBodyContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitAnnotationTypeDeclaration_004()  throws Throwable  {
      //caseID:fdb5d7f6433cdd3174fb1b20f1f19678
      //CoveredLines: [14, 695]
      //Input_0_JavaParser.AnnotationTypeDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_AnnotationTypeDeclarationContext0
      JavaParser.AnnotationTypeDeclarationContext javaParser_AnnotationTypeDeclarationContext0 = mock(JavaParser.AnnotationTypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitAnnotationTypeDeclaration
      Integer integer0 = javaParserBaseVisitor0.visitAnnotationTypeDeclaration(javaParser_AnnotationTypeDeclarationContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitAnnotationTypeElementRest_005()  throws Throwable  {
      //caseID:274459931689f64ba4222d2865ed6654
      //CoveredLines: [14, 728]
      //Input_0_JavaParser.AnnotationTypeElementRestContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_AnnotationTypeElementRestContext0
      JavaParser.AnnotationTypeElementRestContext javaParser_AnnotationTypeElementRestContext0 = mock(JavaParser.AnnotationTypeElementRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitAnnotationTypeElementRest
      Integer integer0 = javaParserBaseVisitor0.visitAnnotationTypeElementRest(javaParser_AnnotationTypeElementRestContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitArrayCreatorRest_006()  throws Throwable  {
      //caseID:52ed2e1401bfde6ae6587243d5776206
      //CoveredLines: [14, 1256]
      //Input_0_JavaParser.ArrayCreatorRestContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ArrayCreatorRestContext0
      JavaParser.ArrayCreatorRestContext javaParser_ArrayCreatorRestContext0 = mock(JavaParser.ArrayCreatorRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitArrayCreatorRest
      Object object0 = javaParserBaseVisitor0.visitArrayCreatorRest(javaParser_ArrayCreatorRestContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitArrayInitializer_007()  throws Throwable  {
      //caseID:cdf3b7ad0dbd0103f21640c7c4197f9c
      //CoveredLines: [14, 464]
      //Input_0_JavaParser.ArrayInitializerContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ArrayInitializerContext0
      JavaParser.ArrayInitializerContext javaParser_ArrayInitializerContext0 = mock(JavaParser.ArrayInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_ArrayInitializerContext0).getChildCount();
      
      //Call method: visitArrayInitializer
      Object object0 = javaParserBaseVisitor0.visitArrayInitializer(javaParser_ArrayInitializerContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitBlock_008()  throws Throwable  {
      //caseID:2d21d7d009b25302091deb39b701be04
      //CoveredLines: [14, 882]
      //Input_0_JavaParser.BlockContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_BlockContext0
      JavaParser.BlockContext javaParser_BlockContext0 = mock(JavaParser.BlockContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_BlockContext0).getChildCount();
      
      //Call method: visitBlock
      String string0 = javaParserBaseVisitor0.visitBlock(javaParser_BlockContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitBlockStatement_009()  throws Throwable  {
      //caseID:e81e5991344835b207c6df9469da1376
      //CoveredLines: [14, 893]
      //Input_0_JavaParser.BlockStatementContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_BlockStatementContext0
      JavaParser.BlockStatementContext javaParser_BlockStatementContext0 = mock(JavaParser.BlockStatementContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitBlockStatement
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitBlockStatement(javaParser_BlockStatementContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitCatchClause_010()  throws Throwable  {
      //caseID:91783fc78c72ae0375e724df824f4f13
      //CoveredLines: [14, 948]
      //Input_0_JavaParser.CatchClauseContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_CatchClauseContext0
      JavaParser.CatchClauseContext javaParser_CatchClauseContext0 = mock(JavaParser.CatchClauseContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_CatchClauseContext0).getChildCount();
      
      //Call method: visitCatchClause
      Object object0 = javaParserBaseVisitor0.visitCatchClause(javaParser_CatchClauseContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitCatchType_011()  throws Throwable  {
      //caseID:d2ef3dd10239e358ded773b6705abfe5
      //CoveredLines: [14, 959]
      //Input_0_JavaParser.CatchTypeContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_CatchTypeContext0
      JavaParser.CatchTypeContext javaParser_CatchTypeContext0 = mock(JavaParser.CatchTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_CatchTypeContext0).getChildCount();
      
      //Call method: visitCatchType
      Object object0 = javaParserBaseVisitor0.visitCatchType(javaParser_CatchTypeContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitClassBody_012()  throws Throwable  {
      //caseID:f50dde1e68e851735adde6884b230ca6
      //CoveredLines: [14, 211]
      //Input_0_JavaParser.ClassBodyContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ClassBodyContext0
      JavaParser.ClassBodyContext javaParser_ClassBodyContext0 = mock(JavaParser.ClassBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_ClassBodyContext0).getChildCount();
      
      //Call method: visitClassBody
      Object object0 = javaParserBaseVisitor0.visitClassBody(javaParser_ClassBodyContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitClassBodyDeclaration_013()  throws Throwable  {
      //caseID:a756fa64b0a80aacd2f03f0b31d66d16
      //CoveredLines: [14, 233]
      //Input_0_JavaParser.ClassBodyDeclarationContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ClassBodyDeclarationContext0
      JavaParser.ClassBodyDeclarationContext javaParser_ClassBodyDeclarationContext0 = mock(JavaParser.ClassBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_ClassBodyDeclarationContext0).getChildCount();
      
      //Call method: visitClassBodyDeclaration
      String string0 = javaParserBaseVisitor0.visitClassBodyDeclaration(javaParser_ClassBodyDeclarationContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitClassCreatorRest_014()  throws Throwable  {
      //caseID:d5eeee737c251d680f3f81c43596f968
      //CoveredLines: [14, 1267]
      //Input_0_JavaParser.ClassCreatorRestContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ClassCreatorRestContext0
      JavaParser.ClassCreatorRestContext javaParser_ClassCreatorRestContext0 = mock(JavaParser.ClassCreatorRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_ClassCreatorRestContext0).getChildCount();
      
      //Call method: visitClassCreatorRest
      String string0 = javaParserBaseVisitor0.visitClassCreatorRest(javaParser_ClassCreatorRestContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitClassOrInterfaceModifier_015()  throws Throwable  {
      //caseID:e3fbd0a841503fa6a7fa30f921e902c9
      //CoveredLines: [14, 90]
      //Input_0_JavaParser.ClassOrInterfaceModifierContext: {getChildCount=1, getChild=parseTree0}
      //Assert: assertNotNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParserBaseVisitor0).when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_ClassOrInterfaceModifierContext0
      JavaParser.ClassOrInterfaceModifierContext javaParser_ClassOrInterfaceModifierContext0 = mock(JavaParser.ClassOrInterfaceModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0).when(javaParser_ClassOrInterfaceModifierContext0).getChild(anyInt());
      doReturn(1).when(javaParser_ClassOrInterfaceModifierContext0).getChildCount();
      
      //Call method: visitClassOrInterfaceModifier
      Object object0 = javaParserBaseVisitor0.visitClassOrInterfaceModifier(javaParser_ClassOrInterfaceModifierContext0);
      
      //Test Result Assert
      assertNotNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitClassOrInterfaceType_016()  throws Throwable  {
      //caseID:354638e7ed9dc072775f4763822f961c
      //CoveredLines: [14, 475]
      //Input_0_JavaParser.ClassOrInterfaceTypeContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ClassOrInterfaceTypeContext0
      JavaParser.ClassOrInterfaceTypeContext javaParser_ClassOrInterfaceTypeContext0 = mock(JavaParser.ClassOrInterfaceTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_ClassOrInterfaceTypeContext0).getChildCount();
      
      //Call method: visitClassOrInterfaceType
      String string0 = javaParserBaseVisitor0.visitClassOrInterfaceType(javaParser_ClassOrInterfaceTypeContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitClassType_017()  throws Throwable  {
      //caseID:91ecd460fbbfb36d2924a0f8ecd58a06
      //CoveredLines: [14, 1212]
      //Input_0_JavaParser.ClassTypeContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ClassTypeContext0
      JavaParser.ClassTypeContext javaParser_ClassTypeContext0 = mock(JavaParser.ClassTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitClassType
      Object object0 = javaParserBaseVisitor0.visitClassType(javaParser_ClassTypeContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitCompilationUnit_018()  throws Throwable  {
      //caseID:467e5e23701650efd20baa0761812c26
      //CoveredLines: [14, 35]
      //Input_0_JavaParser.CompilationUnitContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_CompilationUnitContext0
      JavaParser.CompilationUnitContext javaParser_CompilationUnitContext0 = mock(JavaParser.CompilationUnitContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitCompilationUnit
      String string0 = javaParserBaseVisitor0.visitCompilationUnit(javaParser_CompilationUnitContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitConstantDeclarator_019()  throws Throwable  {
      //caseID:f465b976322e6d6c653106e31628788a
      //CoveredLines: [14, 365]
      //Input_0_JavaParser.ConstantDeclaratorContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_ConstantDeclaratorContext0
      JavaParser.ConstantDeclaratorContext javaParser_ConstantDeclaratorContext0 = mock(JavaParser.ConstantDeclaratorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitConstantDeclarator
      Integer integer0 = javaParserBaseVisitor0.visitConstantDeclarator(javaParser_ConstantDeclaratorContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitConstructorDeclaration_020()  throws Throwable  {
      //caseID:60d0dbf9aae4d91b0a82e055252ce526
      //CoveredLines: [14, 310]
      //Input_0_JavaParser.ConstructorDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ConstructorDeclarationContext0
      JavaParser.ConstructorDeclarationContext javaParser_ConstructorDeclarationContext0 = mock(JavaParser.ConstructorDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitConstructorDeclaration
      Object object0 = javaParserBaseVisitor0.visitConstructorDeclaration(javaParser_ConstructorDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitDefaultValue_021()  throws Throwable  {
      //caseID:09fbb4f0761d1d1bf19a2b2b89e40655
      //CoveredLines: [14, 772]
      //Input_0_JavaParser.DefaultValueContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_DefaultValueContext0
      JavaParser.DefaultValueContext javaParser_DefaultValueContext0 = mock(JavaParser.DefaultValueContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitDefaultValue
      Object object0 = javaParserBaseVisitor0.visitDefaultValue(javaParser_DefaultValueContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitElementValue_022()  throws Throwable  {
      //caseID:7369aa0f545e7a4b54717e3ab515475e
      //CoveredLines: [14, 673]
      //Input_0_JavaParser.ElementValueContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_ElementValueContext0
      JavaParser.ElementValueContext javaParser_ElementValueContext0 = mock(JavaParser.ElementValueContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_ElementValueContext0).getChildCount();
      
      //Call method: visitElementValue
      Integer integer0 = javaParserBaseVisitor0.visitElementValue(javaParser_ElementValueContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitElementValueArrayInitializer_023()  throws Throwable  {
      //caseID:0c9240c62c5404b7db9364942dfc7d5b
      //CoveredLines: [14, 684]
      //Input_0_JavaParser.ElementValueArrayInitializerContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_ElementValueArrayInitializerContext0
      JavaParser.ElementValueArrayInitializerContext javaParser_ElementValueArrayInitializerContext0 = mock(JavaParser.ElementValueArrayInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitElementValueArrayInitializer
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitElementValueArrayInitializer(javaParser_ElementValueArrayInitializerContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitElementValuePair_024()  throws Throwable  {
      //caseID:a0b11cc5d8bf3029b3716962b87c6c83
      //CoveredLines: [14, 662]
      //Input_0_JavaParser.ElementValuePairContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ElementValuePairContext0
      JavaParser.ElementValuePairContext javaParser_ElementValuePairContext0 = mock(JavaParser.ElementValuePairContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_ElementValuePairContext0).getChildCount();
      
      //Call method: visitElementValuePair
      Object object0 = javaParserBaseVisitor0.visitElementValuePair(javaParser_ElementValuePairContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitEnhancedForControl_025()  throws Throwable  {
      //caseID:4d5ccc20123b4e951ccad3575ec6b41a
      //CoveredLines: [14, 1058]
      //Input_0_JavaParser.EnhancedForControlContext: {getChildCount=1, getChild=parseTree0}
      //Assert: assertEquals("1", method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_EnhancedForControlContext0
      JavaParser.EnhancedForControlContext javaParser_EnhancedForControlContext0 = mock(JavaParser.EnhancedForControlContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0).when(javaParser_EnhancedForControlContext0).getChild(anyInt());
      doReturn(1).when(javaParser_EnhancedForControlContext0).getChildCount();
      
      //Call method: visitEnhancedForControl
      String string0 = javaParserBaseVisitor0.visitEnhancedForControl(javaParser_EnhancedForControlContext0);
      
      //Test Result Assert
      assertEquals("1", string0);
  }

  @Test(timeout = 4000)
  public void test_visitEnumBodyDeclarations_026()  throws Throwable  {
      //caseID:db02ec7a3621791c78ce0734fdb57a83
      //CoveredLines: [14, 189]
      //Input_0_JavaParser.EnumBodyDeclarationsContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_EnumBodyDeclarationsContext0
      JavaParser.EnumBodyDeclarationsContext javaParser_EnumBodyDeclarationsContext0 = mock(JavaParser.EnumBodyDeclarationsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitEnumBodyDeclarations
      Integer integer0 = javaParserBaseVisitor0.visitEnumBodyDeclarations(javaParser_EnumBodyDeclarationsContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitEnumConstant_027()  throws Throwable  {
      //caseID:be09d93da728355d90729d740141b7f7
      //CoveredLines: [14, 178]
      //Input_0_JavaParser.EnumConstantContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_EnumConstantContext0
      JavaParser.EnumConstantContext javaParser_EnumConstantContext0 = mock(JavaParser.EnumConstantContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitEnumConstant
      Object object0 = javaParserBaseVisitor0.visitEnumConstant(javaParser_EnumConstantContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitEnumConstants_028()  throws Throwable  {
      //caseID:eef235d3ef5aaa94010ea0014849bf40
      //CoveredLines: [14, 167]
      //Input_0_JavaParser.EnumConstantsContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_EnumConstantsContext0
      JavaParser.EnumConstantsContext javaParser_EnumConstantsContext0 = mock(JavaParser.EnumConstantsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitEnumConstants
      Object object0 = javaParserBaseVisitor0.visitEnumConstants(javaParser_EnumConstantsContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitEnumDeclaration_029()  throws Throwable  {
      //caseID:158b2a675d604f4fa09953378e074107
      //CoveredLines: [14, 156]
      //Input_0_JavaParser.EnumDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_EnumDeclarationContext0
      JavaParser.EnumDeclarationContext javaParser_EnumDeclarationContext0 = mock(JavaParser.EnumDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitEnumDeclaration
      String string0 = javaParserBaseVisitor0.visitEnumDeclaration(javaParser_EnumDeclarationContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitExplicitGenericInvocation_030()  throws Throwable  {
      //caseID:9f904955d0e64afe6626955e8bb76b16
      //CoveredLines: [14, 1278]
      //Input_0_JavaParser.ExplicitGenericInvocationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ExplicitGenericInvocationContext0
      JavaParser.ExplicitGenericInvocationContext javaParser_ExplicitGenericInvocationContext0 = mock(JavaParser.ExplicitGenericInvocationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitExplicitGenericInvocation
      String string0 = javaParserBaseVisitor0.visitExplicitGenericInvocation(javaParser_ExplicitGenericInvocationContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitExplicitGenericInvocationSuffix_031()  throws Throwable  {
      //caseID:8b07bfecf3ac4973d860c66de53a327b
      //CoveredLines: [14, 1377]
      //Input_0_JavaParser.ExplicitGenericInvocationSuffixContext: {getChildCount=1, getChild=parseTree0}
      //Assert: assertEquals("1.0", method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1.0").when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_ExplicitGenericInvocationSuffixContext0
      JavaParser.ExplicitGenericInvocationSuffixContext javaParser_ExplicitGenericInvocationSuffixContext0 = mock(JavaParser.ExplicitGenericInvocationSuffixContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0).when(javaParser_ExplicitGenericInvocationSuffixContext0).getChild(anyInt());
      doReturn(1).when(javaParser_ExplicitGenericInvocationSuffixContext0).getChildCount();
      
      //Call method: visitExplicitGenericInvocationSuffix
      String string0 = javaParserBaseVisitor0.visitExplicitGenericInvocationSuffix(javaParser_ExplicitGenericInvocationSuffixContext0);
      
      //Test Result Assert
      assertEquals("1.0", string0);
  }

  @Test(timeout = 4000)
  public void test_visitExpression_032()  throws Throwable  {
      //caseID:df9201256dbb796daeb073802d9a2a1b
      //CoveredLines: [14, 1102]
      //Input_0_JavaParser.ExpressionContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ExpressionContext0
      JavaParser.ExpressionContext javaParser_ExpressionContext0 = mock(JavaParser.ExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitExpression
      String string0 = javaParserBaseVisitor0.visitExpression(javaParser_ExpressionContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitExpressionList_033()  throws Throwable  {
      //caseID:53aed68d31dcbf4d030c1a1b952113e5
      //CoveredLines: [14, 1080]
      //Input_0_JavaParser.ExpressionListContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ExpressionListContext0
      JavaParser.ExpressionListContext javaParser_ExpressionListContext0 = mock(JavaParser.ExpressionListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitExpressionList
      Object object0 = javaParserBaseVisitor0.visitExpressionList(javaParser_ExpressionListContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitFieldDeclaration_034()  throws Throwable  {
      //caseID:a827fc80748c7c301e0e6154676b8daf
      //CoveredLines: [14, 321]
      //Input_0_JavaParser.FieldDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_FieldDeclarationContext0
      JavaParser.FieldDeclarationContext javaParser_FieldDeclarationContext0 = mock(JavaParser.FieldDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitFieldDeclaration
      Object object0 = javaParserBaseVisitor0.visitFieldDeclaration(javaParser_FieldDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitFinallyBlock_035()  throws Throwable  {
      //caseID:6be5e27a7a0cc07214f895daf42a7317
      //CoveredLines: [14, 970]
      //Input_0_JavaParser.FinallyBlockContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_FinallyBlockContext0
      JavaParser.FinallyBlockContext javaParser_FinallyBlockContext0 = mock(JavaParser.FinallyBlockContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitFinallyBlock
      Object object0 = javaParserBaseVisitor0.visitFinallyBlock(javaParser_FinallyBlockContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitFloatLiteral_036()  throws Throwable  {
      //caseID:41f98fc73e7034f382f4bfb942426ba7
      //CoveredLines: [14, 277, 618]
      //Input_0_JavaParser.FloatLiteralContext: {getChildCount=2152, getChild=parseTree0 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746 parserRuleContext0, 1746}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      JavaParserBaseVisitor<String> javaParserBaseVisitor1 = new JavaParserBaseVisitor<String>();
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParserBaseVisitor0).when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parserRuleContext0
      ParserRuleContext parserRuleContext0 = mock(ParserRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = new JavaParser.TypeTypeOrVoidContext(parserRuleContext0, 1746);
      
      javaParserBaseVisitor1.visit(javaParser_TypeTypeOrVoidContext0);
      //mock javaParser_FloatLiteralContext0
      JavaParser.FloatLiteralContext javaParser_FloatLiteralContext0 = mock(JavaParser.FloatLiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0).when(javaParser_FloatLiteralContext0).getChild(anyInt());
      doReturn(2152).when(javaParser_FloatLiteralContext0).getChildCount();
      
      //Call method: visitFloatLiteral
      Object object0 = javaParserBaseVisitor0.visitFloatLiteral(javaParser_FloatLiteralContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitForControl_037()  throws Throwable  {
      //caseID:e7ddfcfe60998f32b1d26b7b89bfa8b9
      //CoveredLines: [14, 1036]
      //Input_0_JavaParser.ForControlContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ForControlContext0
      JavaParser.ForControlContext javaParser_ForControlContext0 = mock(JavaParser.ForControlContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitForControl
      String string0 = javaParserBaseVisitor0.visitForControl(javaParser_ForControlContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitForInit_038()  throws Throwable  {
      //caseID:4e1f6822379f67b52104436c33f04440
      //CoveredLines: [14, 1047]
      //Input_0_JavaParser.ForInitContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ForInitContext0
      JavaParser.ForInitContext javaParser_ForInitContext0 = mock(JavaParser.ForInitContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_ForInitContext0).getChildCount();
      
      //Call method: visitForInit
      Object object0 = javaParserBaseVisitor0.visitForInit(javaParser_ForInitContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitFormalParameter_039()  throws Throwable  {
      //caseID:9226360e260701c9a11381ace779c6c8
      //CoveredLines: [14, 541]
      //Input_0_JavaParser.FormalParameterContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_FormalParameterContext0
      JavaParser.FormalParameterContext javaParser_FormalParameterContext0 = mock(JavaParser.FormalParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitFormalParameter
      Object object0 = javaParserBaseVisitor0.visitFormalParameter(javaParser_FormalParameterContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitFormalParameters_040()  throws Throwable  {
      //caseID:0ae6073db4c710993b7bfad782666b4f
      //CoveredLines: [14, 508]
      //Input_0_JavaParser.FormalParametersContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_FormalParametersContext0
      JavaParser.FormalParametersContext javaParser_FormalParametersContext0 = mock(JavaParser.FormalParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitFormalParameters
      Integer integer0 = javaParserBaseVisitor0.visitFormalParameters(javaParser_FormalParametersContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitGenericConstructorDeclaration_041()  throws Throwable  {
      //caseID:c678b8026ae301166c7737af6018dcd4
      //CoveredLines: [14, 299]
      //Input_0_JavaParser.GenericConstructorDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_GenericConstructorDeclarationContext0
      JavaParser.GenericConstructorDeclarationContext javaParser_GenericConstructorDeclarationContext0 = mock(JavaParser.GenericConstructorDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitGenericConstructorDeclaration
      Object object0 = javaParserBaseVisitor0.visitGenericConstructorDeclaration(javaParser_GenericConstructorDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitGenericMethodDeclaration_042()  throws Throwable  {
      //caseID:7cf2a900764875dc85199e344ba87406
      //CoveredLines: [14, 288]
      //Input_0_JavaParser.GenericMethodDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_GenericMethodDeclarationContext0
      JavaParser.GenericMethodDeclarationContext javaParser_GenericMethodDeclarationContext0 = mock(JavaParser.GenericMethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitGenericMethodDeclaration
      Object object0 = javaParserBaseVisitor0.visitGenericMethodDeclaration(javaParser_GenericMethodDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitGuardedPattern_043()  throws Throwable  {
      //caseID:de6bf60992b0a70dbbee22d5ed901f36
      //CoveredLines: [14, 1190]
      //Input_0_JavaParser.GuardedPatternContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_GuardedPatternContext0
      JavaParser.GuardedPatternContext javaParser_GuardedPatternContext0 = mock(JavaParser.GuardedPatternContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitGuardedPattern
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitGuardedPattern(javaParser_GuardedPatternContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitIdentifier_044()  throws Throwable  {
      //caseID:c0552494085cea7fa1341db9129c2aa5
      //CoveredLines: [14, 915]
      //Input_0_JavaParser.IdentifierContext: {getChildCount=1160, getChild=javaParser_TypeTypeOrVoidContext0 (JavaParser.TypeTypeOrVoidContext) null}
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock parserRuleContext0
      ParserRuleContext parserRuleContext0 = mock(ParserRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      JavaParser.MethodDeclarationContext javaParser_MethodDeclarationContext0 = new JavaParser.MethodDeclarationContext(parserRuleContext0, 954);
      
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParser_MethodDeclarationContext0.typeTypeOrVoid();
      //mock javaParser_IdentifierContext0
      JavaParser.IdentifierContext javaParser_IdentifierContext0 = mock(JavaParser.IdentifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParser_TypeTypeOrVoidContext0, (JavaParser.TypeTypeOrVoidContext) null).when(javaParser_IdentifierContext0).getChild(anyInt());
      doReturn(1160).when(javaParser_IdentifierContext0).getChildCount();
      
      //Call method: visitIdentifier
      // Undeclared exception!
      try { 
        javaParserBaseVisitor0.visitIdentifier(javaParser_IdentifierContext0);
      } catch(Throwable e) {
         assertEquals("java.lang.NullPointerException", e.getClass().getName());
      }
  }

  @Test(timeout = 4000)
  public void test_visitImportDeclaration_045()  throws Throwable  {
      //caseID:bcca091b04e364ccd621705f935ce213
      //CoveredLines: [14, 57]
      //Input_0_JavaParser.ImportDeclarationContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_ImportDeclarationContext0
      JavaParser.ImportDeclarationContext javaParser_ImportDeclarationContext0 = mock(JavaParser.ImportDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_ImportDeclarationContext0).getChildCount();
      
      //Call method: visitImportDeclaration
      Integer integer0 = javaParserBaseVisitor0.visitImportDeclaration(javaParser_ImportDeclarationContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitInnerCreator_046()  throws Throwable  {
      //caseID:e4d578dadf11430f238f0ca038e8ea57
      //CoveredLines: [14, 1245]
      //Input_0_JavaParser.InnerCreatorContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_InnerCreatorContext0
      JavaParser.InnerCreatorContext javaParser_InnerCreatorContext0 = mock(JavaParser.InnerCreatorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitInnerCreator
      Object object0 = javaParserBaseVisitor0.visitInnerCreator(javaParser_InnerCreatorContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitIntegerLiteral_047()  throws Throwable  {
      //caseID:5fa87483c531b4b8b608d28dbc42abb7
      //CoveredLines: [14, 607]
      //Input_0_JavaParser.IntegerLiteralContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_IntegerLiteralContext0
      JavaParser.IntegerLiteralContext javaParser_IntegerLiteralContext0 = mock(JavaParser.IntegerLiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitIntegerLiteral
      Integer integer0 = javaParserBaseVisitor0.visitIntegerLiteral(javaParser_IntegerLiteralContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitInterfaceBody_048()  throws Throwable  {
      //caseID:37e59110cc0715041016d12284ac78c2
      //CoveredLines: [14, 222]
      //Input_0_JavaParser.InterfaceBodyContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_InterfaceBodyContext0
      JavaParser.InterfaceBodyContext javaParser_InterfaceBodyContext0 = mock(JavaParser.InterfaceBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_InterfaceBodyContext0).getChildCount();
      
      //Call method: visitInterfaceBody
      Object object0 = javaParserBaseVisitor0.visitInterfaceBody(javaParser_InterfaceBodyContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitInterfaceBodyDeclaration_049()  throws Throwable  {
      //caseID:1ee2d821de3b9c07a7f8d9cc7ac22f75
      //CoveredLines: [14, 332]
      //Input_0_JavaParser.InterfaceBodyDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_InterfaceBodyDeclarationContext0
      JavaParser.InterfaceBodyDeclarationContext javaParser_InterfaceBodyDeclarationContext0 = mock(JavaParser.InterfaceBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitInterfaceBodyDeclaration
      Integer integer0 = javaParserBaseVisitor0.visitInterfaceBodyDeclaration(javaParser_InterfaceBodyDeclarationContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitInterfaceCommonBodyDeclaration_050()  throws Throwable  {
      //caseID:1cf19db7d06efcc84345540d353320d2
      //CoveredLines: [14, 409]
      //Input_0_JavaParser.InterfaceCommonBodyDeclarationContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_InterfaceCommonBodyDeclarationContext0
      JavaParser.InterfaceCommonBodyDeclarationContext javaParser_InterfaceCommonBodyDeclarationContext0 = mock(JavaParser.InterfaceCommonBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_InterfaceCommonBodyDeclarationContext0).getChildCount();
      
      //Call method: visitInterfaceCommonBodyDeclaration
      Object object0 = javaParserBaseVisitor0.visitInterfaceCommonBodyDeclaration(javaParser_InterfaceCommonBodyDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitInterfaceDeclaration_051()  throws Throwable  {
      //caseID:ea2bd0693a76225376b50c9edcd1978a
      //CoveredLines: [14, 200]
      //Input_0_JavaParser.InterfaceDeclarationContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_InterfaceDeclarationContext0
      JavaParser.InterfaceDeclarationContext javaParser_InterfaceDeclarationContext0 = mock(JavaParser.InterfaceDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_InterfaceDeclarationContext0).getChildCount();
      
      //Call method: visitInterfaceDeclaration
      Object object0 = javaParserBaseVisitor0.visitInterfaceDeclaration(javaParser_InterfaceDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitInterfaceMethodDeclaration_052()  throws Throwable  {
      //caseID:8b40f70098c7f58b99d394a7be13183a
      //CoveredLines: [14, 376]
      //Input_0_JavaParser.InterfaceMethodDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_InterfaceMethodDeclarationContext0
      JavaParser.InterfaceMethodDeclarationContext javaParser_InterfaceMethodDeclarationContext0 = mock(JavaParser.InterfaceMethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitInterfaceMethodDeclaration
      String string0 = javaParserBaseVisitor0.visitInterfaceMethodDeclaration(javaParser_InterfaceMethodDeclarationContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitInterfaceMethodModifier_053()  throws Throwable  {
      //caseID:acfabca95b319653f858cfdfbcd042e0
      //CoveredLines: [14, 387]
      //Input_0_JavaParser.InterfaceMethodModifierContext: {getChildCount=(-4430)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_InterfaceMethodModifierContext0
      JavaParser.InterfaceMethodModifierContext javaParser_InterfaceMethodModifierContext0 = mock(JavaParser.InterfaceMethodModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-4430)).when(javaParser_InterfaceMethodModifierContext0).getChildCount();
      
      //Call method: visitInterfaceMethodModifier
      Object object0 = javaParserBaseVisitor0.visitInterfaceMethodModifier(javaParser_InterfaceMethodModifierContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitLambdaBody_054()  throws Throwable  {
      //caseID:6ec3ef8e649905d870ea95b378345a37
      //CoveredLines: [14, 1146]
      //Input_0_JavaParser.LambdaBodyContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_LambdaBodyContext0
      JavaParser.LambdaBodyContext javaParser_LambdaBodyContext0 = mock(JavaParser.LambdaBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitLambdaBody
      String string0 = javaParserBaseVisitor0.visitLambdaBody(javaParser_LambdaBodyContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitLambdaExpression_055()  throws Throwable  {
      //caseID:23b5ebfe514ff362f2134f1a359e1c03
      //CoveredLines: [14, 1124]
      //Input_0_JavaParser.LambdaExpressionContext: {getChildCount=1, getChild=parseTree0}
      //Assert: assertEquals("1", method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_LambdaExpressionContext0
      JavaParser.LambdaExpressionContext javaParser_LambdaExpressionContext0 = mock(JavaParser.LambdaExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0).when(javaParser_LambdaExpressionContext0).getChild(anyInt());
      doReturn(1).when(javaParser_LambdaExpressionContext0).getChildCount();
      
      //Call method: visitLambdaExpression
      String string0 = javaParserBaseVisitor0.visitLambdaExpression(javaParser_LambdaExpressionContext0);
      
      //Test Result Assert
      assertEquals("1", string0);
  }

  @Test(timeout = 4000)
  public void test_visitLambdaLVTIList_056()  throws Throwable  {
      //caseID:c89afaec8ea7f527dfbb8eab8456d859
      //CoveredLines: [14, 563]
      //Input_0_JavaParser.LambdaLVTIListContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_LambdaLVTIListContext0
      JavaParser.LambdaLVTIListContext javaParser_LambdaLVTIListContext0 = mock(JavaParser.LambdaLVTIListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_LambdaLVTIListContext0).getChildCount();
      
      //Call method: visitLambdaLVTIList
      String string0 = javaParserBaseVisitor0.visitLambdaLVTIList(javaParser_LambdaLVTIListContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitLambdaLVTIParameter_057()  throws Throwable  {
      //caseID:e563cff53bda338874025b0ec3491d6b
      //CoveredLines: [14, 574]
      //Input_0_JavaParser.LambdaLVTIParameterContext: {getChildCount=1, getChild=parseTree0}
      //Assert: assertEquals(1, method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      Integer integer0 = new Integer(1);
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_LambdaLVTIParameterContext0
      JavaParser.LambdaLVTIParameterContext javaParser_LambdaLVTIParameterContext0 = mock(JavaParser.LambdaLVTIParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0).when(javaParser_LambdaLVTIParameterContext0).getChild(anyInt());
      doReturn(1).when(javaParser_LambdaLVTIParameterContext0).getChildCount();
      
      //Call method: visitLambdaLVTIParameter
      Object object0 = javaParserBaseVisitor0.visitLambdaLVTIParameter(javaParser_LambdaLVTIParameterContext0);
      
      //Test Result Assert
      assertEquals(1, object0);
  }

  @Test(timeout = 4000)
  public void test_visitLambdaParameters_058()  throws Throwable  {
      //caseID:705d3e946dd21fc41bedb98b539e9774
      //CoveredLines: [14, 1135]
      //Input_0_JavaParser.LambdaParametersContext: {getChildCount=1055, getChild=parseTree0 (JavaParser.TypeTypeOrVoidContext) null}
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      Integer integer0 = new Integer(1503);
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_LambdaParametersContext0
      JavaParser.LambdaParametersContext javaParser_LambdaParametersContext0 = mock(JavaParser.LambdaParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0, (JavaParser.TypeTypeOrVoidContext) null).when(javaParser_LambdaParametersContext0).getChild(anyInt());
      doReturn(1055).when(javaParser_LambdaParametersContext0).getChildCount();
      
      //Call method: visitLambdaParameters
      // Undeclared exception!
      try { 
        javaParserBaseVisitor0.visitLambdaParameters(javaParser_LambdaParametersContext0);
      } catch(Throwable e) {
         assertEquals("java.lang.NullPointerException", e.getClass().getName());
      }
  }

  @Test(timeout = 4000)
  public void test_visitLastFormalParameter_059()  throws Throwable  {
      //caseID:c2dae4183a0433a7e63b0f44e2a7b28a
      //CoveredLines: [14, 552]
      //Input_0_JavaParser.LastFormalParameterContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_LastFormalParameterContext0
      JavaParser.LastFormalParameterContext javaParser_LastFormalParameterContext0 = mock(JavaParser.LastFormalParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_LastFormalParameterContext0).getChildCount();
      
      //Call method: visitLastFormalParameter
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitLastFormalParameter(javaParser_LastFormalParameterContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitLiteral_060()  throws Throwable  {
      //caseID:4a120184389c5759b09602fb421dcc22
      //CoveredLines: [14, 596]
      //Input_0_JavaParser.LiteralContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_LiteralContext0
      JavaParser.LiteralContext javaParser_LiteralContext0 = mock(JavaParser.LiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_LiteralContext0).getChildCount();
      
      //Call method: visitLiteral
      String string0 = javaParserBaseVisitor0.visitLiteral(javaParser_LiteralContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitLocalTypeDeclaration_061()  throws Throwable  {
      //caseID:4c3fb8ca40d98139654ac0ae56047bc8
      //CoveredLines: [14, 926]
      //Input_0_JavaParser.LocalTypeDeclarationContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_LocalTypeDeclarationContext0
      JavaParser.LocalTypeDeclarationContext javaParser_LocalTypeDeclarationContext0 = mock(JavaParser.LocalTypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_LocalTypeDeclarationContext0).getChildCount();
      
      //Call method: visitLocalTypeDeclaration
      Object object0 = javaParserBaseVisitor0.visitLocalTypeDeclaration(javaParser_LocalTypeDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitMemberDeclaration_062()  throws Throwable  {
      //caseID:1834e0f94a79e3114b78470144034410
      //CoveredLines: [14, 244]
      //Input_0_JavaParser.MemberDeclarationContext: {getChildCount=1, getChild=parseTree0}
      //Assert: assertEquals("com.alipay.codegencore.utils.codescan.JavaParser$PatternContext", method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("com.alipay.codegencore.utils.codescan.JavaParser$PatternContext").when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_MemberDeclarationContext0
      JavaParser.MemberDeclarationContext javaParser_MemberDeclarationContext0 = mock(JavaParser.MemberDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0).when(javaParser_MemberDeclarationContext0).getChild(anyInt());
      doReturn(1).when(javaParser_MemberDeclarationContext0).getChildCount();
      
      //Call method: visitMemberDeclaration
      Object object0 = javaParserBaseVisitor0.visitMemberDeclaration(javaParser_MemberDeclarationContext0);
      
      //Test Result Assert
      assertEquals("com.alipay.codegencore.utils.codescan.JavaParser$PatternContext", object0);
  }

  @Test(timeout = 4000)
  public void test_visitMethodBody_063()  throws Throwable  {
      //caseID:ade494d2dfd03b924830f1dabdcc8d74
      //CoveredLines: [14, 266]
      //Input_0_JavaParser.MethodBodyContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_MethodBodyContext0
      JavaParser.MethodBodyContext javaParser_MethodBodyContext0 = mock(JavaParser.MethodBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_MethodBodyContext0).getChildCount();
      
      //Call method: visitMethodBody
      String string0 = javaParserBaseVisitor0.visitMethodBody(javaParser_MethodBodyContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitMethodCall_064()  throws Throwable  {
      //caseID:501f3b3d8e97b600366bbebd0076c013
      //CoveredLines: [14, 1091]
      //Input_0_JavaParser.MethodCallContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_MethodCallContext0
      JavaParser.MethodCallContext javaParser_MethodCallContext0 = mock(JavaParser.MethodCallContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitMethodCall
      Object object0 = javaParserBaseVisitor0.visitMethodCall(javaParser_MethodCallContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitMethodDeclaration_065()  throws Throwable  {
      //caseID:8733d5def07020794ccc7b45cdf7ac9f
      //CoveredLines: [14, 255]
      //Input_0_JavaParser.MethodDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_MethodDeclarationContext0
      JavaParser.MethodDeclarationContext javaParser_MethodDeclarationContext0 = mock(JavaParser.MethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitMethodDeclaration
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitMethodDeclaration(javaParser_MethodDeclarationContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitModuleBody_066()  throws Throwable  {
      //caseID:c79b7686806e6b5f54cfc0a6c9d4acd0
      //CoveredLines: [14, 794]
      //Input_0_JavaParser.ModuleBodyContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_ModuleBodyContext0
      JavaParser.ModuleBodyContext javaParser_ModuleBodyContext0 = mock(JavaParser.ModuleBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_ModuleBodyContext0).getChildCount();
      
      //Call method: visitModuleBody
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitModuleBody(javaParser_ModuleBodyContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitModuleDeclaration_067()  throws Throwable  {
      //caseID:2101bbd08d242683f8cda6b832c0c706
      //CoveredLines: [14, 783]
      //Input_0_JavaParser.ModuleDeclarationContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ModuleDeclarationContext0
      JavaParser.ModuleDeclarationContext javaParser_ModuleDeclarationContext0 = mock(JavaParser.ModuleDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_ModuleDeclarationContext0).getChildCount();
      
      //Call method: visitModuleDeclaration
      String string0 = javaParserBaseVisitor0.visitModuleDeclaration(javaParser_ModuleDeclarationContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitModuleDirective_068()  throws Throwable  {
      //caseID:3c516d2d808456626052303d82df2b28
      //CoveredLines: [14, 805]
      //Input_0_JavaParser.ModuleDirectiveContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_ModuleDirectiveContext0
      JavaParser.ModuleDirectiveContext javaParser_ModuleDirectiveContext0 = mock(JavaParser.ModuleDirectiveContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitModuleDirective
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitModuleDirective(javaParser_ModuleDirectiveContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitNonWildcardTypeArguments_069()  throws Throwable  {
      //caseID:a0811e118605768502e7b01b4785f680
      //CoveredLines: [14, 1311]
      //Input_0_JavaParser.NonWildcardTypeArgumentsContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_NonWildcardTypeArgumentsContext0
      JavaParser.NonWildcardTypeArgumentsContext javaParser_NonWildcardTypeArgumentsContext0 = mock(JavaParser.NonWildcardTypeArgumentsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_NonWildcardTypeArgumentsContext0).getChildCount();
      
      //Call method: visitNonWildcardTypeArguments
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitNonWildcardTypeArguments(javaParser_NonWildcardTypeArgumentsContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitNonWildcardTypeArgumentsOrDiamond_070()  throws Throwable  {
      //caseID:997d72c86c31d36eef497646b3ccb1a5
      //CoveredLines: [14, 1300]
      //Input_0_JavaParser.NonWildcardTypeArgumentsOrDiamondContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_NonWildcardTypeArgumentsOrDiamondContext0
      JavaParser.NonWildcardTypeArgumentsOrDiamondContext javaParser_NonWildcardTypeArgumentsOrDiamondContext0 = mock(JavaParser.NonWildcardTypeArgumentsOrDiamondContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitNonWildcardTypeArgumentsOrDiamond
      Integer integer0 = javaParserBaseVisitor0.visitNonWildcardTypeArgumentsOrDiamond(javaParser_NonWildcardTypeArgumentsOrDiamondContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitPackageDeclaration_071()  throws Throwable  {
      //caseID:4c7db8ac5e482290fa953d70d92885b2
      //CoveredLines: [14, 46]
      //Input_0_JavaParser.PackageDeclarationContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_PackageDeclarationContext0
      JavaParser.PackageDeclarationContext javaParser_PackageDeclarationContext0 = mock(JavaParser.PackageDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_PackageDeclarationContext0).getChildCount();
      
      //Call method: visitPackageDeclaration
      Integer integer0 = javaParserBaseVisitor0.visitPackageDeclaration(javaParser_PackageDeclarationContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitParExpression_072()  throws Throwable  {
      //caseID:9fa10c87ee40e9a5ed49a58b97fd0137
      //CoveredLines: [14, 1069]
      //Input_0_JavaParser.ParExpressionContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ParExpressionContext0
      JavaParser.ParExpressionContext javaParser_ParExpressionContext0 = mock(JavaParser.ParExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitParExpression
      String string0 = javaParserBaseVisitor0.visitParExpression(javaParser_ParExpressionContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitPattern_073()  throws Throwable  {
      //caseID:ca22bbb925806c4e19c1e9a11a5612c5
      //CoveredLines: [14, 1113]
      //Input_0_JavaParser.PatternContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_PatternContext0
      JavaParser.PatternContext javaParser_PatternContext0 = mock(JavaParser.PatternContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_PatternContext0).getChildCount();
      
      //Call method: visitPattern
      String string0 = javaParserBaseVisitor0.visitPattern(javaParser_PatternContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitPrimary_074()  throws Throwable  {
      //caseID:6d0299222306409c4ec2d481e90eae9a
      //CoveredLines: [14, 1157]
      //Input_0_JavaParser.PrimaryContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_PrimaryContext0
      JavaParser.PrimaryContext javaParser_PrimaryContext0 = mock(JavaParser.PrimaryContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitPrimary
      String string0 = javaParserBaseVisitor0.visitPrimary(javaParser_PrimaryContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitPrimitiveType_075()  throws Throwable  {
      //caseID:4ef6e90e8b4497f4f44916c058a9f7b3
      //CoveredLines: [14, 1344]
      //Input_0_JavaParser.PrimitiveTypeContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_PrimitiveTypeContext0
      JavaParser.PrimitiveTypeContext javaParser_PrimitiveTypeContext0 = mock(JavaParser.PrimitiveTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_PrimitiveTypeContext0).getChildCount();
      
      //Call method: visitPrimitiveType
      Integer integer0 = javaParserBaseVisitor0.visitPrimitiveType(javaParser_PrimitiveTypeContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitQualifiedName_076()  throws Throwable  {
      //caseID:ec1746a1153b978c63507867f7e845d8
      //CoveredLines: [14, 585]
      //Input_0_JavaParser.QualifiedNameContext: {getChildCount=1318, getChild=parseTree0 parseTree19 parseTree18 parseTree17 parseTree16 parseTree15 parseTree14 parseTree13 parseTree12 parseTree11 parseTree10 parseTree9 parseTree8 parseTree7 parseTree6 parseTree5 parseTree4 parseTree3 parseTree2 parseTree1}
      //Assert: assertEquals("1", method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      Integer integer0 = new Integer((-1));
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor1 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_TypeTypeOrVoidContext0
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParserBaseVisitor0).when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree1
      ParseTree parseTree1 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParser_TypeTypeOrVoidContext0, "1", "1", integer0, "1", integer0, javaParserBaseVisitor1, integer0, integer0, integer0, integer0, "1.0", javaParserBaseVisitor0, integer0, javaParserBaseVisitor1, "", "4lJ[0&Y ", "1.0", "com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor", "1").when(parseTree1).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree2
      ParseTree parseTree2 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree2).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree3
      ParseTree parseTree3 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("").when(parseTree3).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree4
      ParseTree parseTree4 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree4).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree5
      ParseTree parseTree5 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree5).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree6
      ParseTree parseTree6 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("").when(parseTree6).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_TypeTypeOrVoidContext1
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext1 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock parseTree7
      ParseTree parseTree7 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParser_TypeTypeOrVoidContext1).when(parseTree7).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree8
      ParseTree parseTree8 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("_4hdOx~&Du]j").when(parseTree8).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree9
      ParseTree parseTree9 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree9).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree10
      ParseTree parseTree10 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree10).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree11
      ParseTree parseTree11 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree11).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree12
      ParseTree parseTree12 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree12).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree13
      ParseTree parseTree13 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree13).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree14
      ParseTree parseTree14 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("com.alipay.codegencore.utils.codescan.JavaParser$FloatLiteralContext").when(parseTree14).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree15
      ParseTree parseTree15 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree15).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree16
      ParseTree parseTree16 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree16).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree17
      ParseTree parseTree17 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree17).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree18
      ParseTree parseTree18 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree18).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree19
      ParseTree parseTree19 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(null).when(parseTree19).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_QualifiedNameContext0
      JavaParser.QualifiedNameContext javaParser_QualifiedNameContext0 = mock(JavaParser.QualifiedNameContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0, parseTree19, parseTree18, parseTree17, parseTree16, parseTree15, parseTree14, parseTree13, parseTree12, parseTree11, parseTree10, parseTree9, parseTree8, parseTree7, parseTree6, parseTree5, parseTree4, parseTree3, parseTree2, parseTree1).when(javaParser_QualifiedNameContext0).getChild(anyInt());
      doReturn(1318).when(javaParser_QualifiedNameContext0).getChildCount();
      
      //Call method: visitQualifiedName
      String string0 = javaParserBaseVisitor0.visitQualifiedName(javaParser_QualifiedNameContext0);
      
      //Test Result Assert
      assertEquals("1", string0);
  }

  @Test(timeout = 4000)
  public void test_visitQualifiedNameList_077()  throws Throwable  {
      //caseID:492863d0f8b51b0a3feee27f72b1b29c
      //CoveredLines: [14, 277, 497]
      //Input_0_JavaParser.QualifiedNameListContext: {getChildCount=3068, getChild=parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1 parserRuleContext0, 1}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock parserRuleContext0
      ParserRuleContext parserRuleContext0 = mock(ParserRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = new JavaParser.TypeTypeOrVoidContext(parserRuleContext0, 1);
      
      javaParserBaseVisitor0.visit(javaParser_TypeTypeOrVoidContext0);
      //mock javaParser_QualifiedNameListContext0
      JavaParser.QualifiedNameListContext javaParser_QualifiedNameListContext0 = mock(JavaParser.QualifiedNameListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0, javaParser_TypeTypeOrVoidContext0).when(javaParser_QualifiedNameListContext0).getChild(anyInt());
      doReturn(3068).when(javaParser_QualifiedNameListContext0).getChildCount();
      
      //Call method: visitQualifiedNameList
      Integer integer0 = javaParserBaseVisitor0.visitQualifiedNameList(javaParser_QualifiedNameListContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitReceiverParameter_078()  throws Throwable  {
      //caseID:76f9912080a9a9efbc0a5bde724df1b8
      //CoveredLines: [14, 519]
      //Input_0_JavaParser.ReceiverParameterContext: {getChildCount=1222, getChild=parseTree0 parseTree19 parseTree18 parseTree17 parseTree16 parseTree15 parseTree14 parseTree13 parseTree12 parseTree11 parseTree10 parseTree9 parseTree8 parseTree7 parseTree6 parseTree5 parseTree4 parseTree3 parseTree2 parseTree1}
      //Assert: assertEquals(593, method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      Integer integer0 = new Integer(593);
      //mock javaParser_TypeTypeOrVoidContext0
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      JavaParserBaseVisitor<String> javaParserBaseVisitor1 = new JavaParserBaseVisitor<String>();
      //mock ruleNode0
      RuleNode ruleNode0 = mock(RuleNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(ruleNode0).getChildCount();
      
      Object object0 = javaParserBaseVisitor1.visitChildren(ruleNode0);
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_TypeTypeOrVoidContext1
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext1 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock javaParser_TypeTypeOrVoidContext2
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext2 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock javaParser_TypeTypeOrVoidContext3
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext3 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock parseTree1
      ParseTree parseTree1 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("lJyU0", javaParser_TypeTypeOrVoidContext0, "19", "177", "1", object0, "1.0", "'xme*___J)43=Rw%g", integer0, javaParser_TypeTypeOrVoidContext3, null, javaParser_TypeTypeOrVoidContext2, "com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor", "", javaParser_TypeTypeOrVoidContext1, null, "com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor", object0, integer0, integer0).when(parseTree1).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree2
      ParseTree parseTree2 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor").when(parseTree2).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree3
      ParseTree parseTree3 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree3).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_TypeTypeOrVoidContext4
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext4 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock parseTree4
      ParseTree parseTree4 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParser_TypeTypeOrVoidContext4).when(parseTree4).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree5
      ParseTree parseTree5 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("177").when(parseTree5).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree6
      ParseTree parseTree6 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree6).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree7
      ParseTree parseTree7 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree7).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree8
      ParseTree parseTree8 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("679").when(parseTree8).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree9
      ParseTree parseTree9 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("Funnels.longFunnel()").when(parseTree9).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree10
      ParseTree parseTree10 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1.0").when(parseTree10).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree11
      ParseTree parseTree11 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree11).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree12
      ParseTree parseTree12 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("19").when(parseTree12).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree13
      ParseTree parseTree13 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(parseTree13).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree14
      ParseTree parseTree14 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree14).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree15
      ParseTree parseTree15 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree15).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree16
      ParseTree parseTree16 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("n&@IT)").when(parseTree16).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree17
      ParseTree parseTree17 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(parseTree17).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree18
      ParseTree parseTree18 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1.0").when(parseTree18).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree19
      ParseTree parseTree19 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree19).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_ReceiverParameterContext0
      JavaParser.ReceiverParameterContext javaParser_ReceiverParameterContext0 = mock(JavaParser.ReceiverParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0, parseTree19, parseTree18, parseTree17, parseTree16, parseTree15, parseTree14, parseTree13, parseTree12, parseTree11, parseTree10, parseTree9, parseTree8, parseTree7, parseTree6, parseTree5, parseTree4, parseTree3, parseTree2, parseTree1).when(javaParser_ReceiverParameterContext0).getChild(anyInt());
      doReturn(1222).when(javaParser_ReceiverParameterContext0).getChildCount();
      
      //Call method: visitReceiverParameter
      Object object1 = javaParserBaseVisitor0.visitReceiverParameter(javaParser_ReceiverParameterContext0);
      
      //Test Result Assert
      assertEquals(593, object1);
  }

  @Test(timeout = 4000)
  public void test_visitRecordBody_079()  throws Throwable  {
      //caseID:90c223c912d7cf39cfdc0b141c39b87d
      //CoveredLines: [14, 871]
      //Input_0_JavaParser.RecordBodyContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_RecordBodyContext0
      JavaParser.RecordBodyContext javaParser_RecordBodyContext0 = mock(JavaParser.RecordBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitRecordBody
      Object object0 = javaParserBaseVisitor0.visitRecordBody(javaParser_RecordBodyContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitRecordComponent_080()  throws Throwable  {
      //caseID:687a6a5ecf79828c518610a43b68b259
      //CoveredLines: [14, 860]
      //Input_0_JavaParser.RecordComponentContext: {getChildCount=402, getChild=parseTree0 parseTree19 parseTree18 parseTree17 parseTree16 parseTree15 parseTree14 parseTree13 parseTree12 parseTree11 parseTree10 parseTree9 parseTree8 parseTree7 parseTree6 parseTree5 parseTree4 parseTree3 parseTree2 parseTree1}
      //Assert: assertEquals("zgmp+r*e\"i8=Mb4V,j", method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_TypeTypeOrVoidContext0
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      Integer integer0 = new Integer(34);
      //mock javaParser_TypeTypeOrVoidContext1
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext1 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParserBaseVisitor0).when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_TypeTypeOrVoidContext2
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext2 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock parserRuleContext0
      ParserRuleContext parserRuleContext0 = mock(ParserRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext3 = new JavaParser.TypeTypeOrVoidContext(parserRuleContext0, 0);
      //mock parseTree1
      ParseTree parseTree1 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0, "1", javaParser_TypeTypeOrVoidContext3.invokingState, "Predicates.not(", javaParser_TypeTypeOrVoidContext1, "", "1", (JavaParser.TypeTypeOrVoidContext) null, javaParser_TypeTypeOrVoidContext0, "6\"DXQ+E+(]r~w6'vm", javaParser_TypeTypeOrVoidContext3, javaParser_TypeTypeOrVoidContext3, integer0, "ZI`", "1.0", ",up=", javaParser_TypeTypeOrVoidContext3, "com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor", javaParser_TypeTypeOrVoidContext2, "zgmp+r*e\"i8=Mb4V,j").when(parseTree1).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree2
      ParseTree parseTree2 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor").when(parseTree2).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree3
      ParseTree parseTree3 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("jYc~vCWl6ZRi").when(parseTree3).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree4
      ParseTree parseTree4 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(null).when(parseTree4).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree5
      ParseTree parseTree5 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("]xBff").when(parseTree5).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree6
      ParseTree parseTree6 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree6).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree7
      ParseTree parseTree7 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("com.alipay.codegencore.utils.codescan.JavaParser$MethodCallContext").when(parseTree7).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree8
      ParseTree parseTree8 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("E3").when(parseTree8).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree9
      ParseTree parseTree9 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree9).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree10
      ParseTree parseTree10 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1.0").when(parseTree10).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree11
      ParseTree parseTree11 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("").when(parseTree11).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree12
      ParseTree parseTree12 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1.0").when(parseTree12).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree13
      ParseTree parseTree13 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParser_TypeTypeOrVoidContext3.invokingState).when(parseTree13).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree14
      ParseTree parseTree14 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParser_TypeTypeOrVoidContext3).when(parseTree14).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree15
      ParseTree parseTree15 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(integer0).when(parseTree15).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree16
      ParseTree parseTree16 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1").when(parseTree16).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree17
      ParseTree parseTree17 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("com.alipay.codegencore.utils.codescan.JavaParserBaseVisitor").when(parseTree17).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree18
      ParseTree parseTree18 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParserBaseVisitor0).when(parseTree18).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock parseTree19
      ParseTree parseTree19 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("^tsyhT,y").when(parseTree19).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_RecordComponentContext0
      JavaParser.RecordComponentContext javaParser_RecordComponentContext0 = mock(JavaParser.RecordComponentContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0, parseTree19, parseTree18, parseTree17, parseTree16, parseTree15, parseTree14, parseTree13, parseTree12, parseTree11, parseTree10, parseTree9, parseTree8, parseTree7, parseTree6, parseTree5, parseTree4, parseTree3, parseTree2, parseTree1).when(javaParser_RecordComponentContext0).getChild(anyInt());
      doReturn(402).when(javaParser_RecordComponentContext0).getChildCount();
      
      //Call method: visitRecordComponent
      String string0 = javaParserBaseVisitor0.visitRecordComponent(javaParser_RecordComponentContext0);
      
      //Test Result Assert
      assertEquals("zgmp+r*e\"i8=Mb4V,j", string0);
  }

  @Test(timeout = 4000)
  public void test_visitRecordComponentList_081()  throws Throwable  {
      //caseID:110792c8da2217ef928535e463fc5bff
      //CoveredLines: [14, 849]
      //Input_0_JavaParser.RecordComponentListContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_RecordComponentListContext0
      JavaParser.RecordComponentListContext javaParser_RecordComponentListContext0 = mock(JavaParser.RecordComponentListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitRecordComponentList
      Object object0 = javaParserBaseVisitor0.visitRecordComponentList(javaParser_RecordComponentListContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitRecordDeclaration_082()  throws Throwable  {
      //caseID:b7fde6ba540205e0c343a2e136189f1e
      //CoveredLines: [14, 827]
      //Input_0_JavaParser.RecordDeclarationContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_RecordDeclarationContext0
      JavaParser.RecordDeclarationContext javaParser_RecordDeclarationContext0 = mock(JavaParser.RecordDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_RecordDeclarationContext0).getChildCount();
      
      //Call method: visitRecordDeclaration
      Object object0 = javaParserBaseVisitor0.visitRecordDeclaration(javaParser_RecordDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitRecordHeader_083()  throws Throwable  {
      //caseID:f6f6f94d3555d50f83dce5f93c1cb2c0
      //CoveredLines: [14, 277, 838, 1157]
      //Input_0_JavaParser.RecordHeaderContext: {getChildCount=1541, getChild=javaParser_TypeTypeOrVoidContext0, 1541 parserRuleContext0, 1541}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_TypeTypeOrVoidContext0
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock parserRuleContext0
      ParserRuleContext parserRuleContext0 = mock(ParserRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      JavaParser.PrimaryContext javaParser_PrimaryContext0 = new JavaParser.PrimaryContext(javaParser_TypeTypeOrVoidContext0, 1541);
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext1 = new JavaParser.TypeTypeOrVoidContext(parserRuleContext0, 1541);
      //mock javaParser_RecordHeaderContext0
      JavaParser.RecordHeaderContext javaParser_RecordHeaderContext0 = mock(JavaParser.RecordHeaderContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(javaParser_PrimaryContext0, javaParser_TypeTypeOrVoidContext1).when(javaParser_RecordHeaderContext0).getChild(anyInt());
      doReturn(1541).when(javaParser_RecordHeaderContext0).getChildCount();
      
      //Call method: visitRecordHeader
      Integer integer0 = javaParserBaseVisitor0.visitRecordHeader(javaParser_RecordHeaderContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitRequiresModifier_084()  throws Throwable  {
      //caseID:1d53a2225f97a3b094e0c6041d58b9f9
      //CoveredLines: [14, 816]
      //Input_0_JavaParser.RequiresModifierContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_RequiresModifierContext0
      JavaParser.RequiresModifierContext javaParser_RequiresModifierContext0 = mock(JavaParser.RequiresModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitRequiresModifier
      Object object0 = javaParserBaseVisitor0.visitRequiresModifier(javaParser_RequiresModifierContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitResource_085()  throws Throwable  {
      //caseID:ccb6fccb277e660c408cf8d7e8558ab2
      //CoveredLines: [14, 1003]
      //Input_0_JavaParser.ResourceContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_ResourceContext0
      JavaParser.ResourceContext javaParser_ResourceContext0 = mock(JavaParser.ResourceContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_ResourceContext0).getChildCount();
      
      //Call method: visitResource
      Object object0 = javaParserBaseVisitor0.visitResource(javaParser_ResourceContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitResourceSpecification_086()  throws Throwable  {
      //caseID:886d84f7e7b535991640f8692d3d9704
      //CoveredLines: [14, 981]
      //Input_0_JavaParser.ResourceSpecificationContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_ResourceSpecificationContext0
      JavaParser.ResourceSpecificationContext javaParser_ResourceSpecificationContext0 = mock(JavaParser.ResourceSpecificationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_ResourceSpecificationContext0).getChildCount();
      
      //Call method: visitResourceSpecification
      String string0 = javaParserBaseVisitor0.visitResourceSpecification(javaParser_ResourceSpecificationContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitResources_087()  throws Throwable  {
      //caseID:61e03a254d09f36c8adf62bf4919aa03
      //CoveredLines: [14, 992]
      //Input_0_JavaParser.ResourcesContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_ResourcesContext0
      JavaParser.ResourcesContext javaParser_ResourcesContext0 = mock(JavaParser.ResourcesContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitResources
      Integer integer0 = javaParserBaseVisitor0.visitResources(javaParser_ResourcesContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitStatement_088()  throws Throwable  {
      //caseID:43610528041e6d4e87315360e2f26cf5
      //CoveredLines: [14, 937]
      //Input_0_JavaParser.StatementContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_StatementContext0
      JavaParser.StatementContext javaParser_StatementContext0 = mock(JavaParser.StatementContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_StatementContext0).getChildCount();
      
      //Call method: visitStatement
      Object object0 = javaParserBaseVisitor0.visitStatement(javaParser_StatementContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitSuperSuffix_089()  throws Throwable  {
      //caseID:e6c9480d9fd823f0d7f9a93d9e8db2ba
      //CoveredLines: [14, 1366]
      //Input_0_JavaParser.SuperSuffixContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_SuperSuffixContext0
      JavaParser.SuperSuffixContext javaParser_SuperSuffixContext0 = mock(JavaParser.SuperSuffixContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_SuperSuffixContext0).getChildCount();
      
      //Call method: visitSuperSuffix
      Object object0 = javaParserBaseVisitor0.visitSuperSuffix(javaParser_SuperSuffixContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitSwitchBlockStatementGroup_090()  throws Throwable  {
      //caseID:4141c40e4c7bcf1d8bf8e3b77635ab9f
      //CoveredLines: [14, 1014]
      //Input_0_JavaParser.SwitchBlockStatementGroupContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Integer> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Integer>();
      //mock javaParser_SwitchBlockStatementGroupContext0
      JavaParser.SwitchBlockStatementGroupContext javaParser_SwitchBlockStatementGroupContext0 = mock(JavaParser.SwitchBlockStatementGroupContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_SwitchBlockStatementGroupContext0).getChildCount();
      
      //Call method: visitSwitchBlockStatementGroup
      Integer integer0 = javaParserBaseVisitor0.visitSwitchBlockStatementGroup(javaParser_SwitchBlockStatementGroupContext0);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_visitSwitchExpression_091()  throws Throwable  {
      //caseID:d085737f12018ff8c0c2ed29fe08e2b7
      //CoveredLines: [14, 1168]
      //Input_0_JavaParser.SwitchExpressionContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_SwitchExpressionContext0
      JavaParser.SwitchExpressionContext javaParser_SwitchExpressionContext0 = mock(JavaParser.SwitchExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitSwitchExpression
      Object object0 = javaParserBaseVisitor0.visitSwitchExpression(javaParser_SwitchExpressionContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitSwitchLabeledRule_092()  throws Throwable  {
      //caseID:d37b52ce31eebbf1bd2f9bde6ba2c74c
      //CoveredLines: [14, 1179]
      //Input_0_JavaParser.SwitchLabeledRuleContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_SwitchLabeledRuleContext0
      JavaParser.SwitchLabeledRuleContext javaParser_SwitchLabeledRuleContext0 = mock(JavaParser.SwitchLabeledRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitSwitchLabeledRule
      Object object0 = javaParserBaseVisitor0.visitSwitchLabeledRule(javaParser_SwitchLabeledRuleContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitSwitchRuleOutcome_093()  throws Throwable  {
      //caseID:ef2905bcc8dc6b451bfedaaf30013727
      //CoveredLines: [14, 1201]
      //Input_0_JavaParser.SwitchRuleOutcomeContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_SwitchRuleOutcomeContext0
      JavaParser.SwitchRuleOutcomeContext javaParser_SwitchRuleOutcomeContext0 = mock(JavaParser.SwitchRuleOutcomeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_SwitchRuleOutcomeContext0).getChildCount();
      
      //Call method: visitSwitchRuleOutcome
      String string0 = javaParserBaseVisitor0.visitSwitchRuleOutcome(javaParser_SwitchRuleOutcomeContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitTypeArgument_094()  throws Throwable  {
      //caseID:c8609c0b16de2ceb1a35a206ce29a7b6
      //CoveredLines: [14, 486]
      //Input_0_JavaParser.TypeArgumentContext: {getChildCount=(-1)}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_TypeArgumentContext0
      JavaParser.TypeArgumentContext javaParser_TypeArgumentContext0 = mock(JavaParser.TypeArgumentContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((-1)).when(javaParser_TypeArgumentContext0).getChildCount();
      
      //Call method: visitTypeArgument
      String string0 = javaParserBaseVisitor0.visitTypeArgument(javaParser_TypeArgumentContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitTypeArguments_095()  throws Throwable  {
      //caseID:aba34cecf47416373ed12cbcfb69eaa1
      //CoveredLines: [14, 1355]
      //Input_0_JavaParser.TypeArgumentsContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_TypeArgumentsContext0
      JavaParser.TypeArgumentsContext javaParser_TypeArgumentsContext0 = mock(JavaParser.TypeArgumentsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_TypeArgumentsContext0).getChildCount();
      
      //Call method: visitTypeArguments
      String string0 = javaParserBaseVisitor0.visitTypeArguments(javaParser_TypeArgumentsContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitTypeDeclaration_096()  throws Throwable  {
      //caseID:7c2c454c0b5460be1e646cd21105ebd5
      //CoveredLines: [14, 68]
      //Input_0_JavaParser.TypeDeclarationContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_TypeDeclarationContext0
      JavaParser.TypeDeclarationContext javaParser_TypeDeclarationContext0 = mock(JavaParser.TypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitTypeDeclaration
      Object object0 = javaParserBaseVisitor0.visitTypeDeclaration(javaParser_TypeDeclarationContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitTypeList_097()  throws Throwable  {
      //caseID:b242f2e615b4705cc44a9c26f9985e58
      //CoveredLines: [14, 1322]
      //Input_0_JavaParser.TypeListContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_TypeListContext0
      JavaParser.TypeListContext javaParser_TypeListContext0 = mock(JavaParser.TypeListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitTypeList
      String string0 = javaParserBaseVisitor0.visitTypeList(javaParser_TypeListContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitTypeParameters_098()  throws Throwable  {
      //caseID:008379c9e3345fbca994f4210041b606
      //CoveredLines: [14, 123]
      //Input_0_JavaParser.TypeParametersContext: {}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock javaParser_TypeParametersContext0
      JavaParser.TypeParametersContext javaParser_TypeParametersContext0 = mock(JavaParser.TypeParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitTypeParameters
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitTypeParameters(javaParser_TypeParametersContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_visitVariableDeclarator_099()  throws Throwable  {
      //caseID:053890248e0b301f1b2df08170949644
      //CoveredLines: [14, 431]
      //Input_0_JavaParser.VariableDeclaratorContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<String> javaParserBaseVisitor0 = new JavaParserBaseVisitor<String>();
      //mock javaParser_VariableDeclaratorContext0
      JavaParser.VariableDeclaratorContext javaParser_VariableDeclaratorContext0 = mock(JavaParser.VariableDeclaratorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_VariableDeclaratorContext0).getChildCount();
      
      //Call method: visitVariableDeclarator
      String string0 = javaParserBaseVisitor0.visitVariableDeclarator(javaParser_VariableDeclaratorContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_visitVariableDeclarators_100()  throws Throwable  {
      //caseID:d8cf46abf1585302d53f81483e73ca11
      //CoveredLines: [14, 420]
      //Input_0_JavaParser.VariableDeclaratorsContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_VariableDeclaratorsContext0
      JavaParser.VariableDeclaratorsContext javaParser_VariableDeclaratorsContext0 = mock(JavaParser.VariableDeclaratorsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_VariableDeclaratorsContext0).getChildCount();
      
      //Call method: visitVariableDeclarators
      Object object0 = javaParserBaseVisitor0.visitVariableDeclarators(javaParser_VariableDeclaratorsContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitVariableInitializer_101()  throws Throwable  {
      //caseID:233c32af233973fa9a7e335f4c78f5ca
      //CoveredLines: [14, 453]
      //Input_0_JavaParser.VariableInitializerContext: {getChildCount=0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<Object> javaParserBaseVisitor0 = new JavaParserBaseVisitor<Object>();
      //mock javaParser_VariableInitializerContext0
      JavaParser.VariableInitializerContext javaParser_VariableInitializerContext0 = mock(JavaParser.VariableInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(0).when(javaParser_VariableInitializerContext0).getChildCount();
      
      //Call method: visitVariableInitializer
      Object object0 = javaParserBaseVisitor0.visitVariableInitializer(javaParser_VariableInitializerContext0);
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_visitVariableModifier_102()  throws Throwable  {
      //caseID:39461f61db8b07bb05ccfcca1b4c140b
      //CoveredLines: [14, 101]
      //Input_0_JavaParser.VariableModifierContext: {getChildCount=1, getChild=parseTree0}
      //Assert: assertNull(method_result);
      
      JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext> javaParserBaseVisitor0 = new JavaParserBaseVisitor<JavaParser.TypeTypeOrVoidContext>();
      //mock parseTree0
      ParseTree parseTree0 = mock(ParseTree.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((JavaParser.TypeTypeOrVoidContext) null).when(parseTree0).accept(any(org.antlr.v4.runtime.tree.ParseTreeVisitor.class));
      //mock javaParser_VariableModifierContext0
      JavaParser.VariableModifierContext javaParser_VariableModifierContext0 = mock(JavaParser.VariableModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(parseTree0).when(javaParser_VariableModifierContext0).getChild(anyInt());
      doReturn(1).when(javaParser_VariableModifierContext0).getChildCount();
      
      //Call method: visitVariableModifier
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = javaParserBaseVisitor0.visitVariableModifier(javaParser_VariableModifierContext0);
      
      //Test Result Assert
      assertNull(javaParser_TypeTypeOrVoidContext0);
  }
}
