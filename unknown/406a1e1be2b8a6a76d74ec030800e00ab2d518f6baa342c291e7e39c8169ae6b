package com.alipay.codegencore.model.request;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * 批量化处理的codegpt请求
 * <AUTHOR>
 */
public class CodegptBatchRequestBean {
    @JSONField(name = "api_version")
    private String apiVersion;

    private boolean stream;

    @JSONField(name = "out_seq_length")
    private int outSeqLength;

    @JSONField(name = "beam_width")
    private Integer beamWidth;

    private List<CodegptRequestBean> prompts;

    @JSONField(name = "max_length")
    private Integer maxLength;

    public String getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    public boolean isStream() {
        return stream;
    }

    public void setStream(boolean stream) {
        this.stream = stream;
    }

    public int getOutSeqLength() {
        return outSeqLength;
    }

    public void setOutSeqLength(int outSeqLength) {
        this.outSeqLength = outSeqLength;
    }

    public Integer getBeamWidth() {
        return beamWidth;
    }

    public void setBeamWidth(Integer beamWidth) {
        this.beamWidth = beamWidth;
    }

    public List<CodegptRequestBean> getPrompts() {
        return prompts;
    }

    public void setPrompts(List<CodegptRequestBean> prompts) {
        this.prompts = prompts;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }
}
