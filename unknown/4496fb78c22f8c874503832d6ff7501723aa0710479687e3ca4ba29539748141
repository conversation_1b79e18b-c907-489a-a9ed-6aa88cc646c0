package com.alipay.codegencore.service.common.limiter.qpx;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.RateLimitTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * QPX限流
 * 限制一个时间窗口内，允许多少个请求通过。
 */
public class QpxLimiterFactory {

    private static final Logger tBaseQpxRateLimiterLogger = LoggerFactory.getLogger(TBaseQpxRateLimiter.class);

    private final Map<String, TBaseQpxRateLimiter> rateLimiterMap;

    /**
     * 模型默认的私有限流池
     */
    private final Map<String, TBaseQpxRateLimiter> modelDefaultPrivateRateLimiterMap = new HashMap<>();

    private static final QpxLimiterFactory INSTANCE = new QpxLimiterFactory();

    private final CodeGPTDrmConfig drmConfig;

    private final RefreshableCommonTbaseCacheManager cacheManager;

    /**
     * 私有构造函数
     * 初始化所有的令牌桶
     */
    private QpxLimiterFactory() {
        cacheManager = SpringUtil.getBean("defaultCacheManager");
        drmConfig = SpringUtil.getBean(CodeGPTDrmConfig.class);
        if (cacheManager == null || drmConfig == null) {
            throw new RuntimeException("cacheManager or drmConfig is null");
        }
        this.rateLimiterMap = new HashMap<>();
        for (String limiterKey : drmConfig.getQpxRateLimiterConfigJson().keySet()) {
            rateLimiterMap.put(limiterKey, new TBaseQpxRateLimiter(limiterKey, cacheManager, drmConfig));
        }
    }

    /**
     * 获取单例(不要在spring成员变量中使用，否则构造方法中的Spring bean可能未被初始化)
     *
     * @return LimiterFactory对象
     */
    public static QpxLimiterFactory getInstance() {
        return INSTANCE;
    }

    /**
     * 尝试获取令牌，如果当前已经达到了最大值，则返回false，否则返回true。
     *
     * @param rateLimitTypeEnum 限流类型枚举
     * @param tokenUser         tokenUser (token类型限流必传)
     * @param model             算法模型名称
     * @return 是否成功获取令牌
     */
    public boolean tryAcquire(RateLimitTypeEnum rateLimitTypeEnum, String tokenUser, String model) {
        // 该模型无需限流
        if (drmConfig.getNoNeedRateLimitModelList().stream().anyMatch(model::equalsIgnoreCase)) {
            return true;
        }
        if (rateLimitTypeEnum == RateLimitTypeEnum.TOKEN_USER) {
            if (StringUtils.isBlank(tokenUser)) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
            }
            return tryAcquireToken(tokenUser);
        } else if (rateLimitTypeEnum == RateLimitTypeEnum.ALGO_MODEL) {
            if (StringUtils.isBlank(model)) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
            }
            return tryAcquireModel(model);
        } else {
            return false;
        }
    }

    private boolean tryAcquireModel(String model) {
        JSONObject qpxConfig = drmConfig.getQpxRateLimiterConfigJson();
        String privateLimiterKey = RateLimitTypeEnum.ALGO_MODEL.name() + "_" + model;
        String defaultLimiterKey = RateLimitTypeEnum.ALGO_MODEL.name() + "_" + AppConstants.DEFAULT;
        boolean hasPrivatePool = qpxConfig.containsKey(privateLimiterKey);
        Map<String, TBaseQpxRateLimiter> rateLimiterMap = hasPrivatePool ? this.rateLimiterMap : this.modelDefaultPrivateRateLimiterMap;
        JSONObject modelConfig = hasPrivatePool ? qpxConfig.getJSONObject(privateLimiterKey) : qpxConfig.getJSONObject(defaultLimiterKey);

        Long windowTimeMills = modelConfig.getLong("windowTimeMills");
        Long windowTotalQuota = modelConfig.getLong("windowTotalQuota");
        // 默认私有池或者专门设置的私有池
        TBaseQpxRateLimiter rateLimiter;
        if (rateLimiterMap.containsKey(privateLimiterKey)) {
            rateLimiter = rateLimiterMap.get(privateLimiterKey);
            updateRateLimiterParam(rateLimiter, modelConfig);
        } else {
            rateLimiter = new TBaseQpxRateLimiter(privateLimiterKey, cacheManager, drmConfig, windowTimeMills, windowTotalQuota);
            rateLimiterMap.put(privateLimiterKey, rateLimiter);
        }
        return rateLimiter.tryAcquire();
    }

    private boolean tryAcquireToken(String tokenUser) {
        JSONObject qpxConfig = drmConfig.getQpxRateLimiterConfigJson();
        String privateLimiterKey = RateLimitTypeEnum.TOKEN_USER.name() + "_" + tokenUser;
        String publicLimiterKey = RateLimitTypeEnum.TOKEN_USER.name() + "_" + AppConstants.DEFAULT;
        TBaseQpxRateLimiter privateRateLimiter = rateLimiterMap.get(privateLimiterKey);
        TBaseQpxRateLimiter publicRateLimiter = rateLimiterMap.get(publicLimiterKey);
        updateRateLimiterParam(publicRateLimiter, qpxConfig.getJSONObject(publicLimiterKey));
        boolean hasPrivatePool = qpxConfig.containsKey(privateLimiterKey);
        // 有私有池
        if (hasPrivatePool) {
            if (privateRateLimiter == null) {
                privateRateLimiter = new TBaseQpxRateLimiter(privateLimiterKey, cacheManager, drmConfig);
                rateLimiterMap.put(privateLimiterKey, privateRateLimiter);
            }
            // 更新私有池的参数(如果需要)
            updateRateLimiterParam(privateRateLimiter, qpxConfig.getJSONObject(privateLimiterKey));
            // 私有池有容量直接走私有池
            if (privateRateLimiter.tryAcquire()) {
                return true;
            }
            // 私有池没有容量,允许使用公共共享池,直接走公共池的结果
            if (drmConfig.getQpxRateLimiterAllowUseDefaultTokenUserList().contains(tokenUser)) {
                return publicRateLimiter.tryAcquire();
            }
            // 私有池没有容量,不允许使用公共池,直接阻断
            return false;
        }
        // 没有私有池直接走公共池
        return publicRateLimiter.tryAcquire();
    }

    private void updateRateLimiterParam(TBaseQpxRateLimiter rateLimiter, JSONObject modelConfig) {
        Long windowTimeMills = modelConfig.getLong("windowTimeMills");
        Long windowTotalQuota = modelConfig.getLong("windowTotalQuota");
        if (!Objects.equals(rateLimiter.getWindowTimeMills(), windowTimeMills) ||
                !Objects.equals(rateLimiter.getWindowTotalQuota(), windowTotalQuota)) {
            rateLimiter.setWindowTimeMills(windowTimeMills);
            rateLimiter.setWindowTotalQuota(windowTotalQuota);
        }
    }

    /**
     * 尝试获取令牌，如果当前已经达到了最大值，则返回false，否则返回true。
     * 这个限流方式是所有rateLimitTypeEnum的限流窗口和数量都是一致的，但是每个rateLimitValue都是独立限流的
     * @param rateLimitTypeEnum
     * @param userId
     * @return
     */
    public boolean tryAcquire(RateLimitTypeEnum rateLimitTypeEnum, String userId) {
        String key = rateLimitTypeEnum.name() + "_" + userId;
        JSONObject qpxConfig = drmConfig.getQpxRateLimiterConfigJson().getJSONObject(rateLimitTypeEnum.name());
        Long windowTimeMills = qpxConfig.getLong("windowTimeMills");
        Long windowTotalQuota = qpxConfig.getLong("windowTotalQuota");
        TBaseQpxRateLimiter tBaseQpxRateLimiter;
        Serializable obj = cacheManager.get(AppConstants.USER_LIMIT_KEY + "_" + key);
        if (obj != null) {
            tBaseQpxRateLimiter = (TBaseQpxRateLimiter) obj;
        } else {
            tBaseQpxRateLimiter = new TBaseQpxRateLimiter(key);
            cacheManager.set(AppConstants.USER_LIMIT_KEY + "_" + key, tBaseQpxRateLimiter, "nx", "ex", 60 * 60);
        }
        tBaseQpxRateLimiter.setWindowTimeMills(windowTimeMills);
        tBaseQpxRateLimiter.setWindowTotalQuota(windowTotalQuota);
        tBaseQpxRateLimiter.setDrmConfig(drmConfig);
        tBaseQpxRateLimiter.setDefaultCacheManager(cacheManager);
        tBaseQpxRateLimiter.setLogger(tBaseQpxRateLimiterLogger);
        return tBaseQpxRateLimiter.tryAcquire();
    }

}
