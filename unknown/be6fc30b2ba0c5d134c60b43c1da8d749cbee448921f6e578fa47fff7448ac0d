package com.alipay.codegencore.utils.code;

import com.alipay.codegencore.model.openai.Plan;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/26 14:52
 */
public class ActionGenCodePrompt {

    private static final Logger logger = LoggerFactory.getLogger(ActionGenCodePrompt.class);

    /**
     * 拼接回车符
     */
    private static final char ENTER = '\n';

    /**
     * 文件描述
     */
    private static final String PROMPT_FILE_FORMAT = "文件%d:%s";

    /**
     * 修改计划描述
     */
    private static final String PROMPT_PLAN_FORMAT = "文件%d修改计划:%s";

    /**
     * 相似代码片段描述模板
     */
    private static final String PROMPT_SIMILARITY_CODE_FORMAT = "文件%d相似代码片段:%s";

    /**
     * 构造 plan 生成的 prompt
     * @param systemPrompt
     * @param requirementDesc
     * @param codes
     * @return
     */
    public static String buildPlanGenPrompt(String systemPrompt, String requirementDesc, List<CodeSearchClient.Item> codes) {

        List<String> promptList = Lists.newArrayList();
        promptList.add(systemPrompt);
        promptList.add(requirementDesc);
        for (int i = 0; i < codes.size(); i++) {
            CodeSearchClient.Item code = codes.get(i);
            int no = i + 1;
            promptList.add(String.format(PROMPT_FILE_FORMAT, no,
                    CodeSearchClient.buildNewPath(code.getFilePath(), code.getId())));
            promptList.add(String.format(PROMPT_SIMILARITY_CODE_FORMAT, no, code.getMethodContent()));
        }
        logger.info("plan gen build prompt len:{}", promptList.size());

        return Joiner.on(ENTER).skipNulls().join(promptList);
    }

    /**
     * 构造 plan 生成的 prompt
     * @param systemPrompt
     * @param requirementDesc
     * @param codes
     * @return
     */
    public static String buildPlanFileGenPrompt(String systemPrompt, String requirementDesc, List<BloopSearchClient.CodeResult> codes) {

        List<String> promptList = Lists.newArrayList();
        promptList.add(systemPrompt);
        promptList.add(requirementDesc);
        for (int i = 0; i < codes.size(); i++) {
            BloopSearchClient.CodeResult code = codes.get(i);
            int no = i + 1;
            promptList.add(String.format(PROMPT_FILE_FORMAT, no,
                    code.getPath()));
            promptList.add(String.format(PROMPT_SIMILARITY_CODE_FORMAT, no, code.getSnippet()));
        }
        logger.info("plan gen build prompt len:{}", promptList.size());

        return Joiner.on(ENTER).skipNulls().join(promptList);
    }

    /**
     * 构造 code 生成的 prompt
     * @param systemPrompt
     * @param requirementDesc
     * @param plans
     * @return
     */
    public static String buildCodeGenPrompt(String systemPrompt, String requirementDesc, List<Plan> plans) {

        List<String> promptList = Lists.newArrayList();
        promptList.add(systemPrompt);
        promptList.add(requirementDesc);
        for (int i = 0; i < plans.size(); i++) {
            Plan plan = plans.get(i);
            int no = i + 1;
            promptList.add(String.format(PROMPT_FILE_FORMAT, no,
                    CodeSearchClient.buildNewPathCode(plan.getFilePath(), plan.getId())));
            promptList.add(String.format(PROMPT_PLAN_FORMAT, no, plan.getStep()));
            promptList.add(String.format(PROMPT_SIMILARITY_CODE_FORMAT, i, plan.getSimilarityCode()));
        }
        logger.info("code gen build prompt len:{}", promptList.size());

        return Joiner.on(ENTER).skipNulls().join(promptList);
    }

 /*   public static String buildCodeFileGenPrompt(String promptTemplate, String requirementDesc, PlanFile plan) {

        JSON.
        PromptCommonUtils.buildPrompt(promptTemplate, pl)
        return null;
    }*/

}
