package com.alipay.codegencore.model.model;

import com.alipay.codegencore.model.domain.AlgoBackendDO;

/**
 * 模型管理response类
 */
public class AlgoBackendModel extends AlgoBackendDO {

    /**
     * owner用户工号
     */
    private String ownerUserEmpId;

    /**
     * owner用户花名
     */
    private String ownerUserNickName;

    /**
     * owner用户支付宝账号
     */
    private String alipayAccount;

    /**
     * 底座模型 所在助手id
     */
    private String     sceneId;
    /**
     * 模型预发生产的健康信息
     */
    private Integer healthPre;
    private String lastFailedReasonPre;
    private Integer healthProd;
    private String lastFailedReasonProd;

    public String getLastFailedReasonPre() {
        return lastFailedReasonPre;
    }

    public void setLastFailedReasonPre(String lastFailedReasonPre) {
        this.lastFailedReasonPre = lastFailedReasonPre;
    }


    public String getLastFailedReasonProd() {
        return lastFailedReasonProd;
    }

    public void setLastFailedReasonProd(String lastFailedReasonProd) {
        this.lastFailedReasonProd = lastFailedReasonProd;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public String getOwnerUserEmpId() {
        return ownerUserEmpId;
    }

    public void setOwnerUserEmpId(String ownerUserEmpId) {
        this.ownerUserEmpId = ownerUserEmpId;
    }

    public String getOwnerUserNickName() {
        return ownerUserNickName;
    }

    public void setOwnerUserNickName(String ownerUserNickName) {
        this.ownerUserNickName = ownerUserNickName;
    }

    public String getAlipayAccount() {
        return alipayAccount;
    }

    public void setAlipayAccount(String alipayAccount) {
        this.alipayAccount = alipayAccount;
    }

    public Integer getHealthPre() {
        return healthPre;
    }

    public void setHealthPre(Integer healthPre) {
        this.healthPre = healthPre;
    }

    public Integer getHealthProd() {
        return healthProd;
    }

    public void setHealthProd(Integer healthProd) {
        this.healthProd = healthProd;
    }
}
