# test-url activice(only dev)
run_mode=TEST
dbmode=dev

# zdal config
app_data_source_name=codegencore_ds
app_oss_data_source_name=codegencore_oss_ds
zdal_version=ERA10060602
# zcache config
zdal_console_url=http://zdataconsole-pool.stable.alipay.net:8080
zdal_tail_name=codegencoreTBaseCache
tbase_time_out=5000


# ???? - java???
alg_generator_line=https://codegencore-pre.alipay.com/api/test/innertest
# ???? - python???
alg_generator_python=https://codegencore-pre.alipay.com/api/test/pytest
# ???? - js/ts???
alg_generator_js=https://codegencore-pre.alipay.com/api/test/jstest

openai_service_url=https://codegencore-pre.alipay.com/api/chat/chatgpt/completion
tool_search_url=https://zarkag.antgroup.com/deveffinlp/codegpt_sgchain_service/codegpt_tool_manager
# agent sdk endpoint
agentsecsdk_endpoint = https://agentsecgateway.test.alipay.net