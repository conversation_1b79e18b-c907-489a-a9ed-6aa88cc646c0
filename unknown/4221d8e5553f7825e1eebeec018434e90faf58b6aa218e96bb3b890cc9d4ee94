package com.alipay.codegencore.utils;

import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.utils
 * @CreateTime : 2023-04-21
 */
public class VisableEnvUtilTest extends AbstractTestBase {

    /**
     * 测试
     */
    @Test
    public void test_isPrePub() {
        boolean test = VisableEnvUtil.isPrePub();
        Assertions.assertFalse(test);
    }
}
