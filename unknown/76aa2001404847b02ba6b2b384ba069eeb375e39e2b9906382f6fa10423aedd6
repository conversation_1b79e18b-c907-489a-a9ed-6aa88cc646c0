package com.alipay.codegencore.service.middle.zsearch.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.function.Function;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ZsearchClientEnum;
import com.alipay.codegencore.model.enums.ZsearchIndexEnum;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.zsearch.ZSearchResult;
import com.alipay.codegencore.service.middle.zsearch.ZsearchCommonService;
import com.alipay.smartsearch.SmartSearchClient;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import com.alipay.zsearch.ZSearchRestClient;
import com.alipay.zsearch.action.Action;
import com.alipay.zsearch.client.RestResult;
import com.alipay.zsearch.core.Bulk;
import com.alipay.zsearch.core.BulkResult;
import com.alipay.zsearch.core.DeleteByQuery;
import com.alipay.zsearch.core.Search;
import com.alipay.zsearch.core.SearchResult;
import com.alipay.zsearch.core.Update;
import com.alipay.zsearch.core.query.QueryBuilder;
import com.alipay.zsearch.core.search.SearchSourceBuilder;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * zsearch服务
 *
 * <AUTHOR>
 * @version : ZsearchServiceImpl.java, v 0.1 2020年11月23日 9:52 下午 yunchen Exp $
 */
@Service
public class ZsearchCommonServiceImpl implements ZsearchCommonService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ZsearchCommonServiceImpl.class);

    @AppConfig("zsearch_endpoint")
    private String zsearchEndpointIndependent;
    @AppConfig("zsearch_endpoint")
    private String zsearchEndpointShared;

    private ZSearchRestClient zSearchRestClientIndependent;

    private ZSearchRestClient zSearchRestClientShared;
    @Resource
    private ConfigService     configService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @PostConstruct
    public void init() {

        String zsearchUserName = configService.getConfigByKey(AppConstants.CONFIG_KEY_ZSEARCH_USER_NAME, true);
        String zsearchPassword = configService.getConfigByKey(AppConstants.CONFIGKEY_ZSEARCH_PASSWORD, true);
        int zsearchTimeout = codeGPTDrmConfig.getZsearchTimeout();
        Assert.isTrue(StringUtils.isNotBlank(zsearchUserName) && StringUtils.isNotBlank(zsearchPassword), "zsearch账密与超时配置不能为空");
        SmartSearchClient smartSearchClient = new SmartSearchClient(zsearchEndpointShared, zsearchUserName,
                zsearchPassword
                , zsearchTimeout, 80);
        zSearchRestClientShared = smartSearchClient.getClient();

        String zsearchUserNameIndependent = configService.getConfigByKey(AppConstants.CONFIG_KEY_ZSEARCH_USER_NAME_INDEPENDENT, true);
        String zsearchPasswordIndependent = configService.getConfigByKey(AppConstants.CONFIGKEY_ZSEARCH_PASSWORD_INDEPENDENT, true);
        int zsearchTimeoutIndependent = codeGPTDrmConfig.getZsearchTimeout();
        Assert.isTrue(StringUtils.isNotBlank(zsearchUserNameIndependent) && StringUtils.isNotBlank(zsearchPasswordIndependent),
                "zsearch账密与超时配置不能为空");
        SmartSearchClient smartSearchClientIndependent = new SmartSearchClient(zsearchEndpointIndependent, zsearchUserNameIndependent,
                zsearchPasswordIndependent
                , zsearchTimeoutIndependent, 80);
        zSearchRestClientIndependent = smartSearchClientIndependent.getClient();

    }

    @Override
    public ZSearchRestClient getZSearchRestClient(ZsearchClientEnum endpoint) {
        if (endpoint == ZsearchClientEnum.INDEPENDENT) {
            return zSearchRestClientIndependent;
        }
        else {
            return zSearchRestClientShared;
        }
    }

    /**
     * 分批次存储列表
     *
     * @param dataList             数据列表
     * @param indexInfoEnum        zsearch索引
     * @param idGenerationFunction id方法
     * @param batchSize            每批存储的数量
     */
    @Override
    public <E> void saveDataListByBatch(List<E> dataList, ZsearchIndexEnum indexInfoEnum, Function<E,String> idGenerationFunction,
                                        int batchSize, ZsearchClientEnum clientEnum) throws IOException {
        if (dataList == null || dataList.isEmpty()) {
            LOGGER.info("datalist is blank, skip zsearch insert!");
            return;
        }
        List<List<E>> subLists = Lists.partition(dataList, batchSize);
        for (List<E> subList : subLists) {
            saveDataList(subList, indexInfoEnum, idGenerationFunction, clientEnum);
        }
    }

    @Override
    public <E> void saveDataList(List<E> list, ZsearchIndexEnum indexInfoEnum, Function<E,String> idGenerationFunction,
                                 ZsearchClientEnum clientEnum) throws IOException {
        if (list == null || list.isEmpty()) {
            LOGGER.info("datalist is blank, skip zsearch insert!");
            return;
        }

        Bulk.Builder builder = new Bulk.Builder().index(indexInfoEnum.getIndexName()).type(indexInfoEnum.getTypeName());
        list.forEach(e -> {
            String id = idGenerationFunction.apply(e);
            Update build = new Update.Builder().id(id).doc(e).docAsUpsert(true).build();
            builder.add(build);
        });

        getZSearchRestClient(clientEnum).execute(builder.build());
    }

    @Override
    public <E> void saveDataMap(Map<String,E> map, ZsearchIndexEnum indexInfoEnum, ZsearchClientEnum clientEnum) throws IOException {
        Bulk.Builder builder = new Bulk.Builder().index(indexInfoEnum.getIndexName()).type(indexInfoEnum.getTypeName());
        int count = 0;
        // 每次向zSearch写入的数据条数
        Integer itemNumEveryTime = null;
        for (Map.Entry<String,E> entry : map.entrySet()) {
            String id = entry.getKey();
            E data = entry.getValue();
            /**
             * 5*1024*1024=10485760Bytes
             * String中一个英文字母占1Bytes,UTG8中一个中文占3Bytes
             * 这里zSearch的http接口限制大小为100MB,将请求体转json之后近似估算大小,确保每次请求体不会超过100MB
             * 按照目前的限制,每个http请求大约可以向zSearch写入1W+条数据
             */
            itemNumEveryTime = itemNumEveryTime == null ? 5242880 / JSON.toJSONString(data).length() : itemNumEveryTime;
            Update build = new Update.Builder().id(id).doc(data).docAsUpsert(true).build();
            builder.add(build);
            if (++count > itemNumEveryTime) {
                executeZSearchTask(builder.build(), clientEnum);
                builder = new Bulk.Builder().index(indexInfoEnum.getIndexName()).type(indexInfoEnum.getTypeName());
                count = 0;
            }
        }
        // 将剩余不满itemNumEveryTime条的数据上传
        if (count > 0) {
            executeZSearchTask(builder.build(), clientEnum);
        }
    }

    /**
     * 触发zSearch任务，任务最大尝试次数为失败重试，重试次数见drm参数
     *
     * @param build
     */
    public <T> T executeZSearchTask(Action<T> build, ZsearchClientEnum clientEnum) throws IOException {
        return executeZSearchTask(build, 0, codeGPTDrmConfig.getZsearchTaskRetryTimes(), clientEnum);
    }

    /**
     * 触发zSearch任务，失败后重试次数为times
     *
     * @param build        任务构造器
     * @param curRetryTime 当前重试次数
     * @param retryTimes   重试次数
     */
    private <T> T executeZSearchTask(Action<T> build, int curRetryTime, int retryTimes, ZsearchClientEnum clientEnum) throws IOException {
        // 每2次重试之间间隔1-2秒钟时间
        Random random = new Random();
        try {
            T t = getZSearchRestClient(clientEnum).execute(build);
            if (t instanceof RestResult) {
                RestResult restResult = (RestResult) t;
                if (restResult instanceof BulkResult) {
                    BulkResult bulkResult = (BulkResult) restResult;
                    List<BulkResult.BulkResultItem> bulkResultItems = bulkResult.getFailedItems();
                    if (CollectionUtils.isNotEmpty(bulkResultItems)) {
                        LOGGER.warn("executeZSearchTask异常,失败数量:{},失败数据:{}", bulkResultItems.size(), JSON.toJSONString(bulkResultItems));
                        throw new RuntimeException("executeZSearchTask异常,尝试重试");
                    }
                }
                if (!restResult.isSucceeded()) {
                    if (curRetryTime >= retryTimes) {
                        LOGGER.warn("execute zSearch Task fail,restResult:{},build:{}", restResult.getJsonString(), build);
                        throw new RuntimeException(String.format("execute zSearch Task fail by %s times", curRetryTime + 1));
                    }
                    Thread.sleep(1000 + random.nextInt(1000));
                    return executeZSearchTask(build, curRetryTime + 1, retryTimes, clientEnum);
                }
            }
            return t;
        } catch (Exception e) {
            LOGGER.warn("execute zSearch Task fail by {} times", curRetryTime + 1, e);
            if (curRetryTime >= retryTimes) {
                throw new RuntimeException(String.format("execute zSearch Task fail by %s times", curRetryTime + 1), e);
            }
            try {
                Thread.sleep(1000 + random.nextInt(1000));
            } catch (InterruptedException ex) {
                throw new RuntimeException("Thread.sleep fail");
            }
            return executeZSearchTask(build, curRetryTime + 1, retryTimes, clientEnum);
        }
    }

    @Override
    public <E> void saveData(String id, E data, ZsearchIndexEnum indexInfoEnum, ZsearchClientEnum clientEnum) throws IOException {
        Update build = new Update.Builder().index(indexInfoEnum.getIndexName()).type(indexInfoEnum.getTypeName()).id(id).doc(data)
                .docAsUpsert(true).build();
        executeZSearchTask(build, clientEnum);
    }

    @Override
    public <E> void saveData(E data, ZsearchIndexEnum indexInfoEnum, Function<E,String> idGenerationFunction, ZsearchClientEnum clientEnum)
            throws IOException {
        String id = idGenerationFunction.apply(data);
        Update build = new Update.Builder().index(indexInfoEnum.getIndexName()).type(indexInfoEnum.getTypeName()).id(id).doc(data)
                .docAsUpsert(true).build();
        getZSearchRestClient(clientEnum).execute(build);
    }

    /**
     * 从zsearch索引中查询代码数据
     *
     * @param zsearchIndexEnum 索引信息
     * @param partQueryBuilder 搜索条件
     * @param resultClass      结果类型
     * @param pageSize         每页数据量
     * @param <E>
     * @return
     */
    @Override
    public <E> List<E> queryData(ZsearchIndexEnum zsearchIndexEnum,
                                 SearchSourceBuilder partQueryBuilder,
                                 int pageSize,
                                 Class<E> resultClass, ZsearchClientEnum clientEnum) {

        if (pageSize > AppConstants.DEFAULT_ZSEARCH_PAGE_SIZE) {
            LOGGER.error("单页数据量:{}过大,请重新指定单页查询数量", pageSize);
            return null;
        }
        List<E> result = null;
        try {
            // partQueryBuilder的size默认是-1,只能从ZSearch查询10条数据
            partQueryBuilder.size(pageSize);
            if (partQueryBuilder.sorts() == null || partQueryBuilder.sorts().isEmpty()) {
                partQueryBuilder.sort("_id");
            }
            Search search = new Search.Builder()
                    .addIndex(zsearchIndexEnum.getIndexName())
                    .addType(zsearchIndexEnum.getTypeName())
                    .source(partQueryBuilder)
                    .build();

            SearchResult searchResult = getZSearchRestClient(clientEnum).execute(search);
            if (!searchResult.isSucceeded()) {
                LOGGER.error("查询zsearch数据异常.{}", searchResult.getErrorMessage());
                return result;
            }

            result = new ArrayList<>(searchResult.getTotal().intValue());
            List<E> partResult = searchResult.getSourceAsObjectList(resultClass);
            result.addAll(partResult);

            while (partResult.size() >= pageSize) {
                List<SearchResult.Hit<E,Void>> list = searchResult.getHits(resultClass);
                if (list.size() == 0) {
                    break;
                }

                List<String> sort = list.get(list.size() - 1).sort;
                if (sort == null) {
                    break;
                }

                partQueryBuilder.searchAfter(sort.toArray());
                search = new Search.Builder()
                        .addIndex(zsearchIndexEnum.getIndexName())
                        .addType(zsearchIndexEnum.getTypeName())
                        .source(partQueryBuilder)
                        .build();

                searchResult = getZSearchRestClient(clientEnum).execute(search);
                if (!searchResult.isSucceeded()) {
                    LOGGER.error("查询zsearch数据异常.{}", searchResult.getErrorMessage());
                    return null;
                }
                partResult = searchResult.getSourceAsObjectList(resultClass);
                result.addAll(partResult);
            }
        } catch (Throwable throwable) {
            LOGGER.error("查询zsearch数据异常.", throwable);
        }
        return result;
    }

    /**
     * 从zsearch索引中查询代码数据
     *
     * @param zsearchIndexEnum 索引信息
     * @param partQueryBuilder 搜索条件
     * @param resultClass      结果类型
     * @param limit            每页数据量
     * @param <E>
     * @return
     */
    @Override
    public <E> List<E> queryDataWithLimit(ZsearchIndexEnum zsearchIndexEnum, SearchSourceBuilder partQueryBuilder, int limit,
                                          Class<E> resultClass, ZsearchClientEnum clientEnum) {
        if (limit > AppConstants.DEFAULT_ZSEARCH_PAGE_SIZE) {
            LOGGER.error("数据量:{}过大,请重新指定查询数量", limit);
            return null;
        }
        partQueryBuilder.size(limit);
        List<E> result = null;
        try {
            Search search = new Search.Builder().addIndex(zsearchIndexEnum.getIndexName()).addType(zsearchIndexEnum.getTypeName()).source(
                    partQueryBuilder).build();
            SearchResult searchResult = getZSearchRestClient(clientEnum).execute(search);
            if (!searchResult.isSucceeded()) {
                LOGGER.error("查询zsearch数据异常.{}", searchResult.getErrorMessage());
                return result;
            }
            result = new ArrayList<>(searchResult.getTotal().intValue());
            List<E> partResult = searchResult.getSourceAsObjectList(resultClass);
            result.addAll(partResult);
        } catch (Throwable throwable) {
            LOGGER.error("查询zsearch数据异常.", throwable);
        }
        return result;
    }

    /**
     * 从zsearch索引中查询代码数据
     * <p>
     * 带有数据总数
     *
     * @param zsearchIndexEnum 索引信息
     * @param partQueryBuilder 搜索条件
     * @param resultClass      结果类型
     * @param limit            每页数据量
     * @param <E>
     * @return
     */
    @Override
    public <E> ZSearchResult<E> queryDataWithPage(ZsearchIndexEnum zsearchIndexEnum, SearchSourceBuilder partQueryBuilder, int limit,
                                                  Class<E> resultClass, ZsearchClientEnum clientEnum) {
        if (limit > AppConstants.DEFAULT_ZSEARCH_PAGE_SIZE) {
            LOGGER.error("数据量:{}过大,请重新指定查询数量", limit);
            return null;
        }
        partQueryBuilder.size(limit);
        ZSearchResult<E> result = new ZSearchResult<>();
        try {
            Search search = new Search.Builder().addIndex(zsearchIndexEnum.getIndexName()).addType(zsearchIndexEnum.getTypeName()).source(
                    partQueryBuilder).build();
            SearchResult searchResult = getZSearchRestClient(clientEnum).execute(search);
            if (!searchResult.isSucceeded()) {
                LOGGER.error("查询zsearch数据异常.{}", searchResult.getErrorMessage());
                return result;
            }

            List<E> partResult = searchResult.getSourceAsObjectList(resultClass);
            result.setData(partResult);
            result.setItem(searchResult.getTotal());
            result.setAggregations(searchResult.getAggregations());

        } catch (Throwable throwable) {
            LOGGER.error("查询zsearch数据异常.", throwable);
        }
        return result;
    }

    @Override
    public void deleteData(ZsearchIndexEnum zsearchIndexEnum, QueryBuilder queryBuilder, ZsearchClientEnum clientEnum) throws IOException {
        DeleteByQuery deleteByQuery = new DeleteByQuery
                .Builder(queryBuilder)
                .addIndex(zsearchIndexEnum.getIndexName())
                .addType(zsearchIndexEnum.getTypeName())
                .build();
        // 重试三次进行删除,删除失败会抛异常,所以这里不需要处理返回值
        executeZSearchTask(deleteByQuery, clientEnum);
    }
}
