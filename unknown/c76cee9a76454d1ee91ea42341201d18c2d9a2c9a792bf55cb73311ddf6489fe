package com.alipay.codegencore.model.request;

import com.alipay.codegencore.model.enums.ReviewPlatformEnum;

import java.util.List;
import java.util.Map;

/**
 * 投票接口的前端请求对象
 */
public class CodeGPTVoteRequestBean {
    /**
     * msg的uid
     */
    private String uid;
    /**
     * 投票数据:1表示点赞，0表示反对
     */
    private Integer vote;
    /**
     * 文本描述
     */
    private String text;
    /**
     * 勾选的标签
     */
    private List<String> tags;

    /**
     * 用户对审核结果的反馈
     * key:审核平台
     * val:1=认可,0=不认可
     */
    private Map<ReviewPlatformEnum, Integer> checkFeedback;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getVote() {
        return vote;
    }

    public void setVote(Integer vote) {
        this.vote = vote;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Map<ReviewPlatformEnum, Integer> getCheckFeedback() {
        return checkFeedback;
    }

    public void setCheckFeedback(Map<ReviewPlatformEnum, Integer> checkFeedback) {
        this.checkFeedback = checkFeedback;
    }
}
