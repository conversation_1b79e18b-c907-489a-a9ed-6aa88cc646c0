package com.alipay.codegencore.model.enums;

/**
 * 算法策略类型
 *
 * <AUTHOR>
 * 创建时间 2022-03-03
 */
public enum VisableEnvEnum {
    /**
     * 所有环境可见
     */
    ALL(1),

    /**
     * 仅预发可见
     */
    PREPUB(2);

    private int code;

    VisableEnvEnum(int code) {
        this.code = code;
    }

    VisableEnvEnum getByCode(int code) {
        for(VisableEnvEnum e: VisableEnvEnum.values()) {
            if(e.code == code) {
                return e;
            }
        }
        return null;
    }
    public int getCode() {
        return code;
    }
}
