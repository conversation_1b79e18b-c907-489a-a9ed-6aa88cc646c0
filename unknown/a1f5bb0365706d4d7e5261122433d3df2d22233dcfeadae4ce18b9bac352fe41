/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.middle.tbase;

import java.io.Serializable;
import java.util.List;

/**
 * tbase缓存服务
 *
 * <AUTHOR>
 * @version TbaseService.java, v 0.1 2023年06月02日 11:30 xiaobin
 */
public interface TbaseCacheService {

    /**
     * 设置全局cache
     * @param key
     * @param value
     * @param expire 有效期，单位为秒
     */
    void putCache(String key, Serializable value, int expire);

    /**
     * 删除缓存 key
     * @param key
     * @return
     */
    boolean delKey(String key);

    /**
     * 设置全局cache 不设置有效期
     * @param key
     * @param value
     */
    void putCache(String key, Serializable value);


    /**
     * 获取cache
     * @param key
     * @return
     */
    Serializable getCache(String key);

    /**
     * 获取锁
     *
     * @param lockName
     * @param expireSecond
     * @return
     */
    boolean getLock(String lockName, int expireSecond);

    /**
     * 释放锁
     *
     * @param lockName
     * @return
     */
    boolean releaseLock(String lockName);

    /**
     * 放入队尾
     * @param key
     * @param jobList
     * @return
     */
    Long rPushList(String key, Serializable[] jobList);

    /**
     * 从队列头获取指定个数
     * @param key
     * @param size
     * @param clazz
     * @return
     * @param <T>
     */
    <T extends Serializable> List<T> lPopList(String key, int size, Class<T> clazz);

    /**
     * 增量
     * @param key
     * @return
     */
    Long incrbyex(String key, int value, int defaultValue, int expire);

}