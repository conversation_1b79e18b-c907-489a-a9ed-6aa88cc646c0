/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore;

import com.alipay.codegencore.AbstractTestBase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;

import static org.smartunit.shaded.org.mockito.Mockito.CALLS_REAL_METHODS;
import static org.smartunit.shaded.org.mockito.Mockito.mock;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class AbstractTestBase_SSTest extends AbstractTestBase_SSTest_scaffolding {
// allCoveredLines:[11]

  @Test(timeout = 4000)
  public void test_invokeJunit5Test_0()  throws Throwable  {
      //caseID:22156c6e5eea6c9b9459fda350fc19de
      //CoveredLines: [11]
      
      //mock abstractTestBase0
      AbstractTestBase abstractTestBase0 = mock(AbstractTestBase.class, CALLS_REAL_METHODS);
      
      //Call method: invokeJunit5Test
      abstractTestBase0.invokeJunit5Test();
  }
}
