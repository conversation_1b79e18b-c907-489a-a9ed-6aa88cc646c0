package com.alipay.codegencore.service.tool.learning.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.EnvEnum;
import com.alipay.codegencore.service.tool.learning.FaasFunctionService;
import com.alipay.codegencore.utils.http.HttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 调用faas function
 * <AUTHOR>
 */
@Service
public class FaasFunctionServiceImpl implements FaasFunctionService {

    private static final Logger LOGGER = LoggerFactory.getLogger( FaasFunctionServiceImpl.class );
    /**
     * faas平台url
     */
    private static final String FAAS_URL= "https://function.alipay.com/webapi/function/exe";

    /**
     * 默认超时时间
     */
    private static final long DEFAULT_TIMEOUT = 45000L;

    /**
     * 调用faas function
     *
     * @param functionName
     * @param param
     * @return
     */
    @Override
    public Object fassFunction(String functionName, EnvEnum env, Object param) {
        LOGGER.info("fassFunction toolName:{}, env:{}, param:{}", functionName, env, param);

        try {
            JSONObject faasRequest = new JSONObject();
            faasRequest.put("functionName", functionName);
            faasRequest.put("env", env.name());
            faasRequest.put("params", param);

            String responseStr = HttpClient.post(FAAS_URL).content(faasRequest.toJSONString()).syncExecuteWithExceptionThrow(DEFAULT_TIMEOUT);
            LOGGER.info("fassFunction toolName:{} ,response:{}", functionName, responseStr);
            JSONObject response = JSON.parseObject(responseStr);
            if(!response.getBoolean("success")){
                return response;
            }

            return response.get("data");
        }catch (Exception e){
            LOGGER.error("fassFunction toolName:{} ,error:{}", functionName, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("msg", e.getMessage());
            return response;
        }
    }
}
