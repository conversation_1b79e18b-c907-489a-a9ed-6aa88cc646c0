package com.alipay.codegencore.model.openai;

import javax.validation.constraints.NotBlank;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 17:21
 */
public class RepoInfo {

    /**
     * 仓库url，单独加上是因为有的仓库是http协议，不是https
     */
    private String repoURL;

    @NotBlank(message = "repoPath不能为空")
    private String repoPath;

    @NotBlank(message = "branch不能为空")
    private String branch;

    public String getRepoURL() {
        return repoURL;
    }

    public void setRepoURL(String repoURL) {
        this.repoURL = repoURL;
    }

    public String getRepoPath() {
        return repoPath;
    }

    public void setRepoPath(String repoPath) {
        this.repoPath = repoPath;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", RepoInfo.class.getSimpleName() + "[", "]")
                .add("repoPath='" + repoPath + "'")
                .add("branch='" + branch + "'")
                .toString();
    }
}
