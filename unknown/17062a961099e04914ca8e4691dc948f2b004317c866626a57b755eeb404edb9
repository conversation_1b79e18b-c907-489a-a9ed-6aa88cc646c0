package com.alipay.codegencore.model.model;

/**
 * 检查代码对象模型
 *
 * <AUTHOR>
 * 创建时间 2022-08-12
 */
public class CheckCodeModel {
    /**
     * 需要是否包含当前class
     */
    private String className;
    /**
     * 判断className下是否包含methodName/field。
     * 由于补全可能没有补全出方法参数，补全结果中没有(符号，故此属性可能是methodName，也可能是field
     */
    private String methodOrFieldName;
    /**
     * 引用对象名
     * 如果调用静态方法，则{@link CheckCodeModel#className} 和 {@link CheckCodeModel#referenceName}相等
     */
    private String referenceName;

    public String getReferenceName() {
        return referenceName;
    }

    public void setReferenceName(String referenceName) {
        this.referenceName = referenceName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getMethodOrFieldName() {
        return methodOrFieldName;
    }

    public void setMethodOrFieldName(String methodOrFieldName) {
        this.methodOrFieldName = methodOrFieldName;
    }
}
