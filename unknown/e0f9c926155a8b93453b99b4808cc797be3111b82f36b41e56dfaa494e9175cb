package com.alipay.codegencore.web.codegpt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 文件处理控制器
 */
@Slf4j
@RestController
@CodeTalkWebApi
@RequestMapping("/webapi/documentHandle")
public class DocumentHandleController {

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private UserAclService userAclService;
    @Resource
    private DocumentHandleService documentHandleService;

    /**
     * 重新解析文件
     * @param documentUid 文件ID
     * @return
     */
    @GetMapping(value = "/reParseFile")
    public BaseResponse<Object> reParseFile(@RequestParam String documentUid){
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        documentHandleService.parseFile(documentUid, documentChatConfig, userAuthDO.getEmpId());
        return BaseResponse.buildSuccess();
    }

}
