package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.dal.example.SceneDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface SceneDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    long countByExample(SceneDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    int deleteByExample(SceneDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    @Delete({
        "delete from cg_scene",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    @Insert({
        "insert into cg_scene (gmt_create, gmt_modified, ",
        "name, description, ",
        "system_prompt, deleted, ",
        "query_template_list_json, mode, ",
        "model, plugin_list, ",
        "user_id, visable_user, ",
        "usage_count, audit_status, ",
        "enable, scene_tag, ",
        "visable_env, multi_round_support, ",
        "function_call_config, biz_id, ",
        "scene_type, icon_url, ",
        "icon_background_color, usage_user_count, ",
        "usage_message_count, owner_user_id, ",
        "recommend_scene, scene_sort, ",
        "use_instructions, document_uid_list, ",
        "yuque_token_list, plugin_command, ",
        "plugin_enable, scene_tips)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, ",
        "#{systemPrompt,jdbcType=VARCHAR}, #{deleted,jdbcType=INTEGER}, ",
        "#{queryTemplateListJson,jdbcType=VARCHAR}, #{mode,jdbcType=INTEGER}, ",
        "#{model,jdbcType=VARCHAR}, #{pluginList,jdbcType=VARCHAR}, ",
        "#{userId,jdbcType=BIGINT}, #{visableUser,jdbcType=INTEGER}, ",
        "#{usageCount,jdbcType=BIGINT}, #{auditStatus,jdbcType=INTEGER}, ",
        "#{enable,jdbcType=INTEGER}, #{sceneTag,jdbcType=VARCHAR}, ",
        "#{visableEnv,jdbcType=INTEGER}, #{multiRoundSupport,jdbcType=TINYINT}, ",
        "#{functionCallConfig,jdbcType=VARCHAR}, #{bizId,jdbcType=VARCHAR}, ",
        "#{sceneType,jdbcType=TINYINT}, #{iconUrl,jdbcType=VARCHAR}, ",
        "#{iconBackgroundColor,jdbcType=VARCHAR}, #{usageUserCount,jdbcType=BIGINT}, ",
        "#{usageMessageCount,jdbcType=BIGINT}, #{ownerUserId,jdbcType=BIGINT}, ",
        "#{recommendScene,jdbcType=TINYINT}, #{sceneSort,jdbcType=INTEGER}, ",
        "#{useInstructions,jdbcType=VARCHAR}, #{documentUidList,jdbcType=VARCHAR}, ",
        "#{yuqueTokenList,jdbcType=VARCHAR}, #{pluginCommand,jdbcType=VARCHAR}, ",
        "#{pluginEnable,jdbcType=TINYINT}, #{sceneTips,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(SceneDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    int insertSelective(SceneDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    List<SceneDO> selectByExample(SceneDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, name, description, system_prompt, deleted, query_template_list_json, ",
        "mode, model, plugin_list, user_id, visable_user, usage_count, audit_status, ",
        "enable, scene_tag, visable_env, multi_round_support, function_call_config, biz_id, ",
        "scene_type, icon_url, icon_background_color, usage_user_count, usage_message_count, ",
        "owner_user_id, recommend_scene, scene_sort, use_instructions, document_uid_list, ",
        "yuque_token_list, plugin_command, plugin_enable, scene_tips",
        "from cg_scene",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("com.alipay.codegencore.dal.mapper.SceneDOMapper.BaseResultMap")
    SceneDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    int updateByExampleSelective(@Param("record") SceneDO record, @Param("example") SceneDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    int updateByExample(@Param("record") SceneDO record, @Param("example") SceneDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    int updateByPrimaryKeySelective(SceneDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    @Update({
        "update cg_scene",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "name = #{name,jdbcType=VARCHAR},",
          "description = #{description,jdbcType=VARCHAR},",
          "system_prompt = #{systemPrompt,jdbcType=VARCHAR},",
          "deleted = #{deleted,jdbcType=INTEGER},",
          "query_template_list_json = #{queryTemplateListJson,jdbcType=VARCHAR},",
          "mode = #{mode,jdbcType=INTEGER},",
          "model = #{model,jdbcType=VARCHAR},",
          "plugin_list = #{pluginList,jdbcType=VARCHAR},",
          "user_id = #{userId,jdbcType=BIGINT},",
          "visable_user = #{visableUser,jdbcType=INTEGER},",
          "usage_count = #{usageCount,jdbcType=BIGINT},",
          "audit_status = #{auditStatus,jdbcType=INTEGER},",
          "enable = #{enable,jdbcType=INTEGER},",
          "scene_tag = #{sceneTag,jdbcType=VARCHAR},",
          "visable_env = #{visableEnv,jdbcType=INTEGER},",
          "multi_round_support = #{multiRoundSupport,jdbcType=TINYINT},",
          "function_call_config = #{functionCallConfig,jdbcType=VARCHAR},",
          "biz_id = #{bizId,jdbcType=VARCHAR},",
          "scene_type = #{sceneType,jdbcType=TINYINT},",
          "icon_url = #{iconUrl,jdbcType=VARCHAR},",
          "icon_background_color = #{iconBackgroundColor,jdbcType=VARCHAR},",
          "usage_user_count = #{usageUserCount,jdbcType=BIGINT},",
          "usage_message_count = #{usageMessageCount,jdbcType=BIGINT},",
          "owner_user_id = #{ownerUserId,jdbcType=BIGINT},",
          "recommend_scene = #{recommendScene,jdbcType=TINYINT},",
          "scene_sort = #{sceneSort,jdbcType=INTEGER},",
          "use_instructions = #{useInstructions,jdbcType=VARCHAR},",
          "document_uid_list = #{documentUidList,jdbcType=VARCHAR},",
          "yuque_token_list = #{yuqueTokenList,jdbcType=VARCHAR},",
          "plugin_command = #{pluginCommand,jdbcType=VARCHAR},",
          "plugin_enable = #{pluginEnable,jdbcType=TINYINT},",
          "scene_tips = #{sceneTips,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(SceneDO record);
}