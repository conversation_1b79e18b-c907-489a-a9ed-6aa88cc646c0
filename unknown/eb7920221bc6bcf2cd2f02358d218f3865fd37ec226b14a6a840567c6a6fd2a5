/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.http;

import com.alipay.codegencore.utils.http.GetBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.PrivateAccess;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.smartunit.shaded.org.mockito.Mockito.mock;
import static org.smartunit.shaded.org.mockito.Mockito.withSettings;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class GetBuilder_SSTest extends GetBuilder_SSTest_scaffolding {
// allCoveredLines:[31, 32, 43, 44, 46, 47, 59, 60, 61, 62, 63, 64, 65, 67, 68, 73, 74, 75]

  @Test(timeout = 4000)
  public void test_addParameter_0()  throws Throwable  {
      //caseID:df832d87a71346cddf55e118cfecdad7
      //CoveredLines: [31, 32, 43, 46, 47]
      //Input_0_String: 1
      //Input_1_String: 1
      //Assert: assertSame(getBuilder0, method_result);
      
      //mock httpClient0
      java.net.http.HttpClient httpClient0 = mock(java.net.http.HttpClient.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      GetBuilder getBuilder0 = new GetBuilder("", httpClient0);
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      PrivateAccess.setVariable((Class<?>) GetBuilder.class, getBuilder0, "param", (Object) map0);
      
      //Call method: addParameter
      GetBuilder getBuilder1 = getBuilder0.addParameter("1", "1");
      
      //Test Result Assert
      assertSame(getBuilder0, getBuilder1);
  }

  @Test(timeout = 4000)
  public void test_addParameter_1()  throws Throwable  {
      //caseID:d9a7700c2d70f01f727170cba86679f7
      //CoveredLines: [31, 32, 43, 44, 46, 47]
      //Input_0_String: null
      //Input_1_String: null
      //Assert: assertSame(method_result, getBuilder0);
      
      //mock httpClient0
      java.net.http.HttpClient httpClient0 = mock(java.net.http.HttpClient.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      GetBuilder getBuilder0 = new GetBuilder((String) null, httpClient0);
      
      //Call method: addParameter
      GetBuilder getBuilder1 = getBuilder0.addParameter((String) null, (String) null);
      
      //Test Result Assert
      assertSame(getBuilder1, getBuilder0);
  }

  @Test(timeout = 4000)
  public void test_syncExecute_2()  throws Throwable  {
      //caseID:b3a056917c5b837ba7fa6f6f7fa452d6
      //CoveredLines: [31, 32, 59, 67, 68, 73, 74, 75]
      //Input_0_long: 0
      //Assert: assertNull(method_result);
      
      //mock httpClient0
      java.net.http.HttpClient httpClient0 = mock(java.net.http.HttpClient.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      GetBuilder getBuilder0 = new GetBuilder("", httpClient0);
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      PrivateAccess.setVariable((Class<?>) GetBuilder.class, getBuilder0, "param", (Object) map0);
      
      //Call method: syncExecute
      String string0 = getBuilder0.syncExecute(0L);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_syncExecute_3()  throws Throwable  {
      //caseID:42e5fd04de54fe4aae2eca0d4a988a17
      //CoveredLines: [31, 32, 59, 67, 68, 73, 74, 75]
      //Input_0_long: 64
      //Assert: assertNull(method_result);
      
      //mock httpClient0
      java.net.http.HttpClient httpClient0 = mock(java.net.http.HttpClient.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      GetBuilder getBuilder0 = new GetBuilder("", httpClient0);
      
      //Call method: syncExecute
      String string0 = getBuilder0.syncExecute(64L);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_syncExecute_4()  throws Throwable  {
      //caseID:b0f37d19366caacedbb06a2a35849a01
      //CoveredLines: [31, 32, 59, 60, 61, 62, 63, 64, 65, 67, 68, 73, 74, 75]
      //Input_0_long: 0
      //Assert: assertNull(method_result);
      
      //mock httpClient0
      java.net.http.HttpClient httpClient0 = mock(java.net.http.HttpClient.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      GetBuilder getBuilder0 = new GetBuilder("name", httpClient0);
      ConcurrentHashMap<String, String> concurrentHashMap0 = new ConcurrentHashMap<String, String>();
      
      PrivateAccess.setVariable((Class<?>) GetBuilder.class, getBuilder0, "param", (Object) concurrentHashMap0);
      
      concurrentHashMap0.put("name", "name");
      
      //Call method: syncExecute
      String string0 = getBuilder0.syncExecute(0L);
      
      //Test Result Assert
      assertNull(string0);
  }
}
