package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ContentCheckSceneCodeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.enums.UserStatusEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.service.common.ContentCheckService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.AsyncLogUtils;
import com.alipay.holoxlib.common.shared.enums.CheckActionTypeEnum;
import com.alipay.holoxlib.common.shared.enums.SceneTypeEnum;
import com.alipay.holoxlib.common.shared.model.check.ContentProperty;
import com.alipay.holoxlib.common.shared.model.check.HoloxCheckEvent;
import com.alipay.holoxlib.common.shared.model.check.HoloxCheckResult;
import com.alipay.infosec.content.service.facade.HoloxContentCheckService;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 内容检查的service的实现类
 */
@Service
public class ContentCheckServiceImpl implements ContentCheckService {
    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");


    @AppConfig("spring.application.name")
    private String appName;

    @Resource
    private HoloxContentCheckService holoxContentCheckService;
    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private AsyncLogUtils asyncLogUtils;
    @Resource
    private UserAclService userAclService;

    @Override
    public ReviewResultModel checkContent(String text, ChatRoleEnum chatRoleEnum, String messageUid, int batch, Integer answerBatch, ChatRequestExtData extData, String questionUid) {
        Map<String, ContentProperty> contentPropertyMap = new HashMap<>(1);
        ContentProperty contentProperty = new ContentProperty();
        contentProperty.setText(text);
        contentPropertyMap.put("content", contentProperty);
        return checkContent(contentPropertyMap, chatRoleEnum, messageUid, batch, answerBatch, extData, questionUid);
    }

    private ReviewResultModel checkContent(Map<String, ContentProperty> contentData, ChatRoleEnum chatRoleEnum, String messageUid, Integer batch, Integer answerBatch, ChatRequestExtData extData, String questionUid) {
        verifyParam(contentData, chatRoleEnum, messageUid, batch, answerBatch, extData, questionUid);
        ReviewResultModel reviewResultModel = new ReviewResultModel();
        String batchStr = batch + "_" + answerBatch;
        try {
            if (AppConstants.OFF.equals(codeGPTDrmConfig.getInfosecSwitch())) {
                OTHERS_LOGGER.info("内容安全平台审核开关已经关闭,不进行内容安全审核");
                reviewResultModel.setRet(true);
                reviewResultModel.setCode("NO_REVIEW");
                return reviewResultModel;
            }
            HoloxCheckEvent event = new HoloxCheckEvent();
            event.setAppCode(appName);
            String sceneCode = getSceneCode(extData,chatRoleEnum);
            event.setSceneCode(sceneCode);
            event.setSceneType(SceneTypeEnum.INFOSMART.name());
            // 生成的标题只审核答案,唯一id用的是sessionUid和infoSec交互
            if (extData.isContentIsTitle()) {
                messageUid = questionUid;
            }
            // 业务方请求的唯一标识,每条消息的唯一标识
            String appSceneDataId = messageUid + "_" + batchStr;
            event.setAppSceneDataId(appSceneDataId);
            event.setContentData(contentData);
            // 内容发送者的id, 不存在填空串
            String userId;
            if (codeGPTDrmConfig.isIntranetApplication()) {
                userId = extData.getEmpId();
            } else {
                List<UserAuthDO> userAuthDOList = userAclService.getUserAuthDOByPhoneNumber(extData.getPhoneNumber());
                if (CollectionUtils.isEmpty(userAuthDOList) ||
                        userAuthDOList.get(0).getStatus() != UserStatusEnum.ACTIVE ||
                        StringUtils.isBlank(userAuthDOList.get(0).getAlipayAccount())) {
                    throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
                }
                userId = userAuthDOList.get(0).getAlipayAccount();
            }
            event.setUserId(userId);
            // 内容接受者的id, 不存在填空串
            String receiveId = StringUtils.isNotBlank(extData.getBizId()) ? extData.getBizId() : questionUid;
            event.setRecieverId(receiveId);
            event.setPublishDate(new Date());
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("questionUid", questionUid);
            eventData.put("codeGPTUser", extData.getCodeGPTUser());
            // codefuse的场景才有sessionUid
            eventData.put("sessionUid",extData.getSessionUid());
            // techplay的场景才有bizId
            eventData.put("bizId", extData.getBizId());
            if (extData.getAlgoBackendDO() != null) {
                eventData.put("model", extData.getAlgoBackendDO().getModel());
            }
            event.setEventData(eventData);
            long startTime = System.currentTimeMillis();
            HoloxCheckResult checkResult = holoxContentCheckService.check(event);
            asyncLogUtils.logThirdPartyCallTime(AppConstants.SAFETY_LOG_PREFIX, ReviewPlatformEnum.INFOSEC.name(), startTime, true, chatRoleEnum.getName(), sceneCode);
            if (!checkResult.isSucess()) {
                // 业务数据格式或者内容有问题，看一下错误日志，不明白的咨询infosec值班。
                OTHERS_LOGGER.warn("内容安全检查失败,msg:{}", checkResult.getResultMessage());
                reviewResultModel.setRet(false);
                reviewResultModel.setCode("INFOSEC_FAILED");
                return reviewResultModel;
            }
            // 获取内容识别结果
            CheckActionTypeEnum actionCode = checkResult.getResultCode();
            String reviewResultCode = Objects.requireNonNullElse(actionCode, CheckActionTypeEnum.PASSED).name();
            reviewResultModel.setCode(reviewResultCode);
            // eventId是用来排查问题的
            OTHERS_LOGGER.info("内容安全检查的同步结果,eventId:{},actionCode:{},sceneCode:{},questionUid:{},messageUid:{},batchStr:{}", checkResult.getId(), actionCode, sceneCode, questionUid,messageUid, batchStr);
            if (CheckActionTypeEnum.PASSED.name().equals(reviewResultCode)) {
                reviewResultModel.setRet(true);
            } else if (CheckActionTypeEnum.CC.name().equals(reviewResultCode)) {
                long waitAsyStartTime = System.currentTimeMillis();
                updateAsynchronousRet(reviewResultModel, messageUid, batch, answerBatch, chatRoleEnum, waitAsyStartTime);
                asyncLogUtils.logThirdPartyCallTime(AppConstants.SAFETY_LOG_PREFIX, ReviewPlatformEnum.INFOSEC.name(), waitAsyStartTime, false, chatRoleEnum.getName(), sceneCode);
                OTHERS_LOGGER.info("内容安全检查阻塞式等待的异步结果,eventId:{},sceneCode:{},messageUid:{},batchStr:{},eventData:{},reviewResultModel:{},contentData:{}",
                        checkResult.getId(), sceneCode, messageUid, batchStr, JSON.toJSONString(extData), JSON.toJSONString(reviewResultModel), JSON.toJSONString(contentData));
            } else {
                OTHERS_LOGGER.info("内容安全检查的同步结果,审核不通过,eventId:{},actionCode:{},sceneCode:{},messageUid:{},batchStr:{},eventData:{},reviewResultModel:{},contentData:{}",
                        checkResult.getId(), actionCode, sceneCode, messageUid, batchStr, JSON.toJSONString(extData), JSON.toJSONString(reviewResultModel), JSON.toJSONString(contentData));
                reviewResultModel.setRet(false);
            }
            return reviewResultModel;
        } catch (Exception e) {
            // 如果因为各种异常没有拿到结果，默认放过
            CHAT_LOGGER.warn("内容安全检查失败,默认放过,messageUid:{},batchStr:{},contentData:{}", messageUid, batchStr, JSON.toJSONString(contentData));
            OTHERS_LOGGER.error("内容安全检查失败", e);
            reviewResultModel.setRet(true);
            reviewResultModel.setCode("INFOSEC_ERROR");
            return reviewResultModel;
        }
    }

    private String getSceneCode(ChatRequestExtData extData, ChatRoleEnum chatRoleEnum) {
        if (codeGPTDrmConfig.isIntranetApplication()) {
            String codeGPTUser = extData.getCodeGPTUser();
            if (extData.isContentIsTitle()) {
                return ContentCheckSceneCodeEnum.CTO_CODEFUSE_AITOPIC.getSceneCode();
            }
            String sceneCode = getIntranetSceneCodeByUser(codeGPTUser, chatRoleEnum);
            if (StringUtils.isBlank(sceneCode)) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "Drm config error");
            }
            return sceneCode;
        }
        // 外网
        if (extData.isContentIsTitle()) {
            return ContentCheckSceneCodeEnum.CTO_CODEFUSE_AITOPIC_PUBLIC.getSceneCode();
        }
        if (chatRoleEnum == ChatRoleEnum.USER) {
            return ContentCheckSceneCodeEnum.CTO_CODEFUSE_QUESTION_PUBLIC.getSceneCode();
        }
        return ContentCheckSceneCodeEnum.CTO_CODEFUSE_ANSWER_PUBLIC.getSceneCode();
    }

    private String getIntranetSceneCodeByUser(String codeGPTUser, ChatRoleEnum chatRoleEnum) {
        JSONArray jsonArray = JSON.parseArray(codeGPTDrmConfig.getInfoSecNeedCheckUser());
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if (!jsonObject.getString("codeGPTUser").equalsIgnoreCase(codeGPTUser)) {
                continue;
            }
            if (chatRoleEnum == ChatRoleEnum.USER) {
                return jsonObject.getString("questionSceneCode");
            } else {
                return jsonObject.getString("answerSceneCode");
            }
        }
        throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec codeGPTUser:"+codeGPTUser+" is not support");
    }

    private void verifyParam(Map<String, ContentProperty> contentData, ChatRoleEnum chatRoleEnum, String messageUid, Integer batch, Integer answerBatch, ChatRequestExtData extData, String questionUid) {
        if (MapUtils.isEmpty(contentData) || !contentData.containsKey("content")) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec contentData is not blank");
        }
        if (chatRoleEnum == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec chatRoleEnum is not blank");
        }
        if (ChatRoleEnum.USER != chatRoleEnum && ChatRoleEnum.ASSISTANT != chatRoleEnum) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec chatRoleEnum is illegal");
        }
        if (StringUtils.isBlank(messageUid)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec messageUid is not blank");
        }
        if (StringUtils.isBlank(questionUid)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec questionUid is not blank");
        }
        if (batch == null || batch < 1) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec batch is not blank");
        }
        if (answerBatch == null || answerBatch < 1) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec answerBatch is not blank");
        }
        if (extData == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec extData is not blank");
        }
        if (StringUtils.isBlank(extData.getCodeGPTUser())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec codeGPTUser is not blank");
        }
        List<String> infoSecUserList = getNeedInfoSecCheckUserList();
        if (codeGPTDrmConfig.isIntranetApplication()) {
            if (!infoSecUserList.contains(extData.getCodeGPTUser())) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "request infoSec user is not supported");
            }
            if (StringUtils.isBlank(extData.getEmpId())) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec empId is not blank");
            }
        } else {
            if (StringUtils.isBlank(extData.getPhoneNumber())) {
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request infoSec phoneNumber is not blank");
            }
        }
    }

    private List<String> getNeedInfoSecCheckUserList() {
        JSONArray jsonArray = JSON.parseArray(codeGPTDrmConfig.getInfoSecNeedCheckUser());
        List<String> codeGPTUserList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            codeGPTUserList.add(jsonArray.getJSONObject(i).getString("codeGPTUser"));
        }
        return codeGPTUserList;
    }

    private void updateAsynchronousRet(ReviewResultModel reviewResultModel, String messageUid, Integer batch, Integer answerBatch, ChatRoleEnum chatRoleEnum,long waitAsyStartTime) throws InterruptedException {
        int timeout = 200;
        if (chatRoleEnum == ChatRoleEnum.USER) {
            timeout = codeGPTDrmConfig.getAsyCheckMaxWaitTimeQuestion();
        } else if (chatRoleEnum == ChatRoleEnum.ASSISTANT) {
            timeout = codeGPTDrmConfig.getAsyCheckMaxWaitTimeAnswer();
        }
        int loopWaitTime = codeGPTDrmConfig.getLoopWaitTime();
        while (true) {
            long asyCostTime = System.currentTimeMillis() - waitAsyStartTime;
            if (asyCostTime > timeout) {
                // 超过一定时间没有拿到数据,直接按成功处理
                reviewResultModel.setRet(true);
                reviewResultModel.setCode("ASY_TIMEOUT");
                CHAT_LOGGER.warn("内容安全检查异步超时,默认放过,chatRole:{},messageUid:{},batch:{},asyCostTime:{}", chatRoleEnum.getName(), messageUid, batch, asyCostTime);
                return;
            }
            // 要看前面批次的数据是不是通过的,都通过才可以让当前批通过。
            for (int i = batch - 1; i >= 1; i--) {
                for (int j = answerBatch - 1; j >= 1; j--) {
                    String asynchronousRetI = (String) refreshableCommonTbaseCacheManager.get(AppConstants.CODEGENCORE_INFOSEC_REVIEW_KEY_ + messageUid + "_" + i + "_" + j);
                    if (StringUtils.isBlank(asynchronousRetI)) {
                        continue;
                    }
                    // 前面的批次有审核不通过的,当前批次直接不通过
                    ReviewResultModel resultModel = JSON.parseObject(asynchronousRetI, ReviewResultModel.class);
                    if (!resultModel.isRet()) {
                        reviewResultModel.setRet(resultModel.isRet());
                        reviewResultModel.setCode(resultModel.getCode());
                        return;
                    }
                }
            }
            String asynchronousRet = (String) refreshableCommonTbaseCacheManager.get(AppConstants.CODEGENCORE_INFOSEC_REVIEW_KEY_ + messageUid + "_" + batch + "_" + answerBatch);
            if (StringUtils.isNotBlank(asynchronousRet)) {
                ReviewResultModel resultModel = JSON.parseObject(asynchronousRet, ReviewResultModel.class);
                reviewResultModel.setRet(resultModel.isRet());
                reviewResultModel.setCode(resultModel.getCode());
                return;
            }
            Thread.sleep(loopWaitTime);
        }
    }


}
