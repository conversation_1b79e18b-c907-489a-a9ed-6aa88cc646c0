package com.alipay.codegencore.service.common;

import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.model.ReviewResultModel;

/**
 * 解语花合规审查平台
 * <a href="https://rcsmart.alipay.com/">解语花官网</a>
 * <a href="https://yuque.antfin-inc.com/xg9l39/zbmynv/tvwas3ugut9vf3pz?singleDoc#">接口文档</a>
 */
public interface RcsmartCheckService {

    /**
     * 解语花安全审查
     * @param messageUid 消息uid
     * @param batch 批次
     * @param text 文本
     * @param chatRoleEnum user/assistant
     * @return 审核结果
     */
    ReviewResultModel rcsmartCheck(String messageUid,int batch,String text, ChatRoleEnum chatRoleEnum);

}
