package com.alipay.codegencore.service.common;

import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.openai.ChatRequestExtData;

/**
 * 内容检查的service
 */
public interface ContentCheckService {

    /**
     * 消息分批审查
     * @param text 文本
     * @param messageUid 消息的唯一id
     * @param batch 送检的批次:1,2,3,4
     * @param extData 业务参数(回调会带上)
     * @param questionUid 问题的uid
     * @return ReviewResultModel
     */
    ReviewResultModel checkContent(String text, ChatRoleEnum chatRoleEnum, String messageUid, int batch, Integer answerBatch, ChatRequestExtData extData, String questionUid);

}
