package com.alipay.codegencore.model.enums;

/**
 * 用户侧基座模型
 *
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.enums
 * @CreateTime : 2023-12-04
 */
public enum BaseModelHandlerEnum {

    CodeFuse("CodeGPTModelHandler", "CodeFuse或开源模型"),
    AntGLM("MayaStreamModelHandler", "AntGLM");

    private String value;
    private String description;

    BaseModelHandlerEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static BaseModelHandlerEnum getByName(String name) {
        for (BaseModelHandlerEnum baseModelHandlerEnum : values()) {
            if (baseModelHandlerEnum.name().equalsIgnoreCase(name)) {
                return baseModelHandlerEnum;
            }
        }
        return null;
    }
}
