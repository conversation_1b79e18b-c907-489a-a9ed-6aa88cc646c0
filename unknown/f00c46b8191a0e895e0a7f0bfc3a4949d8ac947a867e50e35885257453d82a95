package com.alipay.codegencore.service.middle.msgbroker;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.service.impl.LinkeOpenapiService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.common.event.UniformEvent;
import com.alipay.rcqualitydataprod.common.service.facade.pcui.PcUICaseCommonFacade;
import com.alipay.rcqualitydataprod.common.service.facade.request.UiBatchExecuteByYuYanIdReq;
import com.alipay.rcqualitydataprod.common.service.facade.result.BaseResult;
import com.alipay.sofa.rpc.api.annotation.RpcConsumer;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zcache.serialize.BytesObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.concurrent.ExecutorService;

/**
 * linke发布完成自动触发agent巡检
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.03.04
 */
@Service("codegencoreInspectionListener")
public class CodegencoreInspectionListener implements CodegencoreEventHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CodegencoreInspectionListener.class);

    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_O_LINKE|EC_RELEASE_STAGE_CHANGE";

    @RpcConsumer
    PcUICaseCommonFacade pcUICaseCommonFacade;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource
    private LinkeOpenapiService linkeOpenapiService;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Override
    public void handle(UniformEvent message) {
        // 判断自动巡检是否开启
        LOGGER.info("收到linke订阅消息的部署通知，目前自动巡检开启状态:{}",codeGPTDrmConfig.getInspectionSwitch());
        if(codeGPTDrmConfig.getInspectionSwitch().equalsIgnoreCase("open")){
            HashMap messageContent = (HashMap) message.getEventPayload();
            String externalId = (String) messageContent.get("externalId");
            // 检查缓存是否存在当前发布单，防止多次触发
            if(noneSerializationCacheManager.exists(externalId)){
                LOGGER.info("本次消息通知已被处理过，不再重复处理");
                return;
            }else {
                // 将当前发布单id放入缓存中，防止重复触发
                noneSerializationCacheManager.setex(externalId,60*60,new BytesObject("true".getBytes(StandardCharsets.UTF_8)));
            }
            if(!linkeOpenapiService.checkAppNameByExternalId(externalId)){
                // 非codegencore应用不做处理
                LOGGER.info("本次消息通知不触发自动巡检");
                return;
            }
            LOGGER.info("linke发布成功，触发自动巡检");
            appThreadPool.submit(
                    () ->{
                        UiBatchExecuteByYuYanIdReq request = new UiBatchExecuteByYuYanIdReq();
                        request.setExt(new HashMap<>());
                        request.setEnv("PROD");
                        request.setSprintId(AppConstants.INSPECTION_SPRINT_ID);
                        request.setSourceType(AppConstants.INSPECTION_SOURCE_TYPE);
                        request.setYuyanId(AppConstants.INSPECTION_YUYAN_ID);
                        request.setYuyanName(AppConstants.INSPECTION_YUYAN_NAME);
                        request.setBranch("");
                        request.setPageIdList(JSONObject.parseArray(codeGPTDrmConfig.getPageIdList(), Long.class));
                        BaseResult baseResult = pcUICaseCommonFacade.batchExecuteByYuYanId(request);
                        if(baseResult.isSuccess()){
                            LOGGER.info("巡检任务触发成功，resultData{}", JSONObject.toJSONString(baseResult.getData()));
                        }else {
                            LOGGER.info("巡检任务触发失败，errorMsg{}", JSONObject.toJSONString(baseResult.getErrorMsg()));
                        }
                    });
        }


    }
    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }
}
