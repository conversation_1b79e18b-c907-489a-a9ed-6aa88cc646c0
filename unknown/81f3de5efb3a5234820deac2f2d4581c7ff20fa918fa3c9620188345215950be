package com.alipay.codegencore.model.openai;

/**
 * 仓库问答通用流式消息格式
 */
public class AnswerChatMessage extends ChatMessage {

    /**
     * 问答消息默认版本
     */
    private String version = "V3";

    /**
     * 构造函数
     */
    public AnswerChatMessage() {
        super();
    }

    /**
     * 构造函数
     */
    public AnswerChatMessage(String role, String content) {
        super(role, content);
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
