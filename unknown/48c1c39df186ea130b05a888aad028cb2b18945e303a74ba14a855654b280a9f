package com.alipay.codegencore.model.enums;

/**
 * 系统架构
 *
 * <AUTHOR>
 * 创建时间 2022-10-19
 */
public enum OsArchEnum {
    /**
     * 可适配任意架构
     */
    ANY(0),
    /**
     * 86_64架构,mac系统用
     */
    X86_64(1),
    /**
     * arm64架构，mac系统用
     */
    ARM64(2),
    /**
     * 异常数据
     */
    UNKNOWN(99);

    OsArchEnum(int archType) {
        this.archType = archType;
    }

    /**
     * 系统架构类型值
     * 如果是 {@code 0}，则代表适配任何架构
     */
    private int archType;

    public int getArchType() {
        return archType;
    }

    public void setArchType(int archType) {
        this.archType = archType;
    }

    /**
     * 根据type获取枚举
     *
     * @return
     */
    public static OsArchEnum getOsArchEnumByType(int osArchType) {
        for (OsArchEnum osArchEnum : OsArchEnum.values()) {
            if (osArchEnum.getArchType() == osArchType) {
                return osArchEnum;
            }
        }
        return UNKNOWN;
    }
}
