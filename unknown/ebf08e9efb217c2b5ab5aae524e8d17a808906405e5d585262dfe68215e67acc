package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.yuque.YuQueBookResponseModel;
import com.alipay.codegencore.model.model.yuque.YuQueDocModel;
import com.alipay.codegencore.model.model.yuque.YuQueDocInfoResponseModel;
import com.alipay.codegencore.model.model.yuque.YuQueGroupResponseModel;
import com.alipay.codegencore.model.model.yuque.YuQueTocModel;
import com.alipay.codegencore.model.model.yuque.YuQueTocResponseModel;
import com.alipay.codegencore.service.utils.YuQueDocUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * https://yuque.antfin-inc.com/iikq97/lozx5s/xqcnt02r7si55b2e 通过语雀token获取团队知识库内容
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.01.12
 */
@Service
public class YuQueDocUtilService {
    private static final Logger LOGGER = LoggerFactory.getLogger(YuQueDocUtilService.class);
    private static final String YUQUE_GROUP_LOGIN_API = "/api/v2/user";
    private static final String YUQUE_BOOK_API = "/api/v2/groups/";
    private static final String YUQUE_DOCS_API = "/api/v2/repos/";
    private static final String YUQUE_OPENAPI_HOST = "https://yuque-api.antfin-inc.com";
    private static final String YUQUE_DOC_HOST = "https://yuque.antfin-inc.com/";


    /**
     * 将语雀知识库导出为一个txt文件
     * @param token   token 团队token
     * @param bookId  知识库id
     * @param docList 需要获取的文档id
     * <AUTHOR>
     * @since 2024.01.12
     */
    public List<YuQueDocModel> yuQueDocExport(String token, Long bookId, List<Long> docList, List<String> docSlugList) {
        // 检查token是否有效
        if (!checkTokenAvailable(token)) {
            // token不可用
            return null;
        }
        // 获取知识库所有文档
        List<YuQueDocModel> docs = null;
        if(CollectionUtils.isNotEmpty(docSlugList)){
            docs = getAllDocsByBookSlug(token, bookId, docSlugList);
        }else {
            docs = getAllDocsByBookId(token, bookId, docList);
        }
        // 避免npe
        if(CollectionUtils.isEmpty(docs)){
            docs = new ArrayList<>();
        }
        // 处理文档原始内容
        for (YuQueDocModel doc : docs) {
            doc.setContent(YuQueDocUtils.convertMarkdownToPlainText(doc.getContent()));
        }

        return docs;

    }

    /**
     * 检查token是否有效
     * @param token token
     * @return java.lang.Boolean
     * <AUTHOR>
     * @since 2024.01.12
     */
    public Boolean checkTokenAvailable(String token) {
        // 检查token有效性
        YuQueGroupResponseModel groupLogin = getGroupLogin(token);
        if (StringUtils.isBlank(groupLogin.getLogin())) {
            throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE, "Token不正确，请重新输入");
        }
        List<YuQueBookResponseModel> groupBooks = getGroupBooks(token);
        // 检查repo读取权限
        if (CollectionUtils.isEmpty(groupBooks)) {
            throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE, "缺少读取团队的知识库的权限");
        }
        // 检查doc读取权限
        List<String> allDocId = getAllDocSlug(token, groupBooks.get(0).getId());
        if (CollectionUtils.isEmpty(allDocId)) {
            throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE, "缺少读取团队的文档的权限");
        }
        return true;
    }

    /**
     * 获取团队知识库列表
     * @param token token
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @since 2024.01.12
     */
    public List<YuQueBookResponseModel> getGroupBooks(String token) {
        List<YuQueBookResponseModel> books = new ArrayList<>();
        YuQueGroupResponseModel groupLogin = getGroupLogin(token);
        if (StringUtils.isBlank(groupLogin.getLogin())) {
            return books;
        }
        JSONObject langChainResponse = null;
        try {
            String response = HttpClient.get(YUQUE_OPENAPI_HOST + YUQUE_BOOK_API + groupLogin.getLogin() + "/repos")
                    .header("accept", "application/json")
                    .header("X-Auth-Token", token)
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
        } catch (Exception e) {
            LOGGER.info("get groupBooks failed for {}", e.getMessage());
        }
        if (langChainResponse != null && langChainResponse.containsKey("data")) {
            JSONArray data = langChainResponse.getJSONArray("data");
            for (int i = 0; i < data.size(); i++) {
                if (data.getJSONObject(i).getString("type").equalsIgnoreCase("Book")) {
                    books.add(data.getObject(i, YuQueBookResponseModel.class));
                }
            }
        }
        if (langChainResponse != null && !langChainResponse.containsKey("data")) {
            LOGGER.info("get groupBooks response fail for {}", langChainResponse.toJSONString());
            throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE, "语雀token缺少读取团队的知识库的权限");
        }
        return books;
    }

    /**
     * 获取团队login
     * @param token token
     * @return java.lang.String
     * <AUTHOR>
     * @since 2024.01.12
     */
    public YuQueGroupResponseModel getGroupLogin(String token) {
        JSONObject langChainResponse = null;
        try {
            String response = HttpClient.get(YUQUE_OPENAPI_HOST + YUQUE_GROUP_LOGIN_API)
                    .header("accept", "application/json")
                    .header("X-Auth-Token", token)
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
        } catch (Exception e) {
            LOGGER.info("get groupLogin failed for {}", e.getMessage());
        }
        if (langChainResponse != null && langChainResponse.containsKey("data")) {
            YuQueGroupResponseModel group = langChainResponse.getObject("data", YuQueGroupResponseModel.class);
            return group;
        }
        if (langChainResponse != null && !langChainResponse.containsKey("data")) {
            LOGGER.info("get group login fail for {}", langChainResponse.toJSONString());
            throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE, "Token已失效，请重新绑定");
        }
        return new YuQueGroupResponseModel();

    }

    /**
     * 获取知识库文件目录
     * @param bookId    bookId
     * @param token     token
     * @param docIdList 已绑定的docId
     * <AUTHOR>
     * @since 2024.01.17
     */
    public List<YuQueTocModel> getYuQueBookToc(Long bookId, String token, List<Long> docIdList, List<String> docSlugList) {
        if(CollectionUtils.isEmpty(docIdList)){
            docIdList = new ArrayList<>();
        }
        if(CollectionUtils.isEmpty(docSlugList)){
            docSlugList = new ArrayList<>();
        }
        JSONObject langChainResponse = null;
        List<YuQueTocResponseModel> data = null;
        List<YuQueTocModel> toc = new ArrayList<>();
        try {
            String response = HttpClient.get(YUQUE_OPENAPI_HOST + YUQUE_DOCS_API + bookId + "/toc")
                    .header("accept", "application/json")
                    .header("X-Auth-Token", token)
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
        } catch (Exception e) {
            LOGGER.info("get groupLogin failed for {}", e.getMessage());
        }
        if (langChainResponse != null && langChainResponse.containsKey("data")) {
            data = langChainResponse.getJSONArray("data").toJavaList(YuQueTocResponseModel.class);
        }
        if (data != null) {
            generateTocTree(toc, data, docIdList, docSlugList);
        }
        if (langChainResponse != null && !langChainResponse.containsKey("data")) {
            throw new BizException(ResponseEnum.YUQUE_TOKEN_UNAVAILABLE, "语雀token缺少读取团队的文档的权限");
        }
        return toc;

    }

    /**
     * 目录response数据转换
     * @param yuQueTocResponseModel yuQueTocResponseModel
     * @return com.alipay.codegencore.model.model.yuque.YuQueTocModel
     * <AUTHOR>
     * @since 2024.01.17
     */
    private YuQueTocModel conv2TocModel(YuQueTocResponseModel yuQueTocResponseModel) {
        YuQueTocModel yuQueTocModel = new YuQueTocModel();
        yuQueTocModel.setKey(yuQueTocResponseModel.getSlug());
        yuQueTocModel.setUuid(yuQueTocResponseModel.getUuid());
        yuQueTocModel.setTitle(yuQueTocResponseModel.getTitle());
        yuQueTocModel.setDocId(yuQueTocResponseModel.getDocId());
        if (!yuQueTocResponseModel.getType().equalsIgnoreCase("DOC")) {
            yuQueTocModel.setDisableCheckbox(true);
        } else {
            yuQueTocModel.setDisableCheckbox(false);
        }
        return yuQueTocModel;
    }


    /**
     * 获取知识库中所有文档
     * @param token  token
     * @param bookId 知识库id
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @since 2024.01.12
     */
    private List<YuQueDocModel> getAllDocsByBookId(String token, Long bookId, List<Long> docList) {
        List<YuQueDocModel> docs = new ArrayList<>();
        // 获取团队的LOGIN
        String groupLogin = getGroupLogin(token).getLogin();
        // 获取团队知识库列表
        List<YuQueBookResponseModel> groupBooks = getGroupBooks(token);
        String bookSlug = getBookSlug(groupBooks, bookId);
        if (bookSlug == null) {
            LOGGER.info("the Book id {} is not exist in this group", bookId);
            return docs;
        }
        String baseDocUrl = YUQUE_DOC_HOST + groupLogin + "/" + bookSlug;
        // 遍历该知识库所有文档并返回
        for (Long docId : docList) {
            JSONObject langChainResponse = null;
            try {
                String response = HttpClient.get(YUQUE_OPENAPI_HOST + YUQUE_DOCS_API + bookId + "/docs/" + docId)
                        .header("accept", "application/json")
                        .header("X-Auth-Token", token)
                        .syncExecuteWithExceptionThrow(10000);
                langChainResponse = JSONObject.parseObject(response);
            } catch (Exception e) {
                LOGGER.info("get docs failed for {}", e.getMessage());
            }
            if (langChainResponse != null && langChainResponse.containsKey("data")) {
                YuQueDocInfoResponseModel docInfoResponseModel = langChainResponse.getObject("data", YuQueDocInfoResponseModel.class);
                YuQueDocModel yuQueDocModel = new YuQueDocModel();
                yuQueDocModel.setContent(docInfoResponseModel.getBody());
                yuQueDocModel.setTitle(docInfoResponseModel.getTitle());
                yuQueDocModel.setDocUrl(baseDocUrl + "/" + docInfoResponseModel.getSlug());
                yuQueDocModel.setId(docInfoResponseModel.getId());
                if (checkDocTypeSupportive(yuQueDocModel.getContent())) {
                    docs.add(yuQueDocModel);
                }
            }
            if (langChainResponse != null) {
                LOGGER.debug("get docsInfo response for {}", langChainResponse.toJSONString());
            }
        }

        return docs;

    }
    /**
     * 获取知识库中所有文档
     * @param token  token
     * @param bookId 知识库id
     * @return List<YuQueDocModel>
     * <AUTHOR>
     * @since 2024.01.12
     */
    private List<YuQueDocModel> getAllDocsByBookSlug(String token, Long bookId, List<String> docList) {
        List<YuQueDocModel> docs = new ArrayList<>();
        // 获取团队的LOGIN
        String groupLogin = getGroupLogin(token).getLogin();
        // 获取团队知识库列表
        List<YuQueBookResponseModel> groupBooks = getGroupBooks(token);
        String bookSlug = getBookSlug(groupBooks, bookId);
        if (bookSlug == null) {
            LOGGER.info("the Book id {} is not exist in this group", bookId);
            return docs;
        }
        String baseDocUrl = YUQUE_DOC_HOST + groupLogin + "/" + bookSlug;
        // 遍历该知识库所有文档并返回
        for (String slug : docList) {
            JSONObject langChainResponse = null;
            try {
                String response = HttpClient.get(YUQUE_OPENAPI_HOST + YUQUE_DOCS_API + bookId + "/docs/" + slug)
                        .header("accept", "application/json")
                        .header("X-Auth-Token", token)
                        .syncExecuteWithExceptionThrow(10000);
                langChainResponse = JSONObject.parseObject(response);
            } catch (Exception e) {
                LOGGER.info("get docs failed for {}", e.getMessage());
            }
            if (langChainResponse != null && langChainResponse.containsKey("data")) {
                try{
                    YuQueDocInfoResponseModel docInfoResponseModel = langChainResponse.getObject("data", YuQueDocInfoResponseModel.class);
                    YuQueDocModel yuQueDocModel = new YuQueDocModel();
                    yuQueDocModel.setContent(docInfoResponseModel.getBody());
                    yuQueDocModel.setTitle(docInfoResponseModel.getTitle());
                    yuQueDocModel.setDocUrl(baseDocUrl + "/" + docInfoResponseModel.getSlug());
                    yuQueDocModel.setId(docInfoResponseModel.getId());
                    if (checkDocTypeSupportive(yuQueDocModel.getContent())) {
                        docs.add(yuQueDocModel);
                    }
                }catch (Exception e){
                    LOGGER.info("get docsInfo fail for the slug{} and the response is {}",slug,langChainResponse.toJSONString());
                    throw new BizException(ResponseEnum.DOCUMENT_PARSE_FAILED,"文档获取详情时解析返回失败");
                }

            }
            if (langChainResponse != null) {
                LOGGER.debug("get docsInfo response for {}", langChainResponse.toJSONString());
            }
        }

        return docs;

    }

    /**
     * 获取知识库文档的id
     * @param token  token
     * @param bookId bookId
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @since 2024.01.12
     */
    private List<String> getAllDocSlug(String token, Long bookId) {
        List<String> docsSlug = new ArrayList<>();
        JSONObject langChainResponse = null;
        try {
            String response = HttpClient.get(YUQUE_OPENAPI_HOST + YUQUE_DOCS_API + bookId + "/docs")
                    .header("accept", "application/json")
                    .header("X-Auth-Token", token)
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSONObject.parseObject(response);
        } catch (Exception e) {
            LOGGER.info("get docs failed for {}", e.getMessage());
        }
        if (langChainResponse != null && langChainResponse.containsKey("data")) {
            JSONArray data = langChainResponse.getJSONArray("data");
            // 测试token读取doc权限，不指定id
            for (int i = 0; i < data.size(); i++) {
                if (data.getJSONObject(i).getString("type").equalsIgnoreCase("Doc")) {
                    docsSlug.add(data.getJSONObject(i).getString("slug"));
                }
            }
        }
        // 返回缺少data字段，缺少权限或者其他异常
        if (langChainResponse != null && !langChainResponse.containsKey("data")) {
            LOGGER.info("get docsId response fail for {}", langChainResponse.toJSONString());
            return null;
        }
        return docsSlug;
    }

    /**
     * 判断知识库是否存在并返回bookSlug
     * @param groupBooks groupBooks
     * @param bookId     bookId
     * @return java.lang.String
     * <AUTHOR>
     * @since 2024.01.17
     */
    private String getBookSlug(List<YuQueBookResponseModel> groupBooks, Long bookId) {
        for (YuQueBookResponseModel groupBook : groupBooks) {
            if (groupBook.getId().equals(bookId)) {
                return groupBook.getSlug();
            }
        }
        return null;
    }

    /**
     * 生成语雀文档目录树
     * @param toc       toc
     * @param data      data
     * @param docIdList docIdList
     * <AUTHOR>
     * @since 2024.01.22
     */
    private void generateTocTree(List<YuQueTocModel> toc, List<YuQueTocResponseModel> data, List<Long> docIdList, List<String> docSlugList) {
        Map<String, YuQueTocModel> tocMap = new HashMap<>();
        for (YuQueTocResponseModel yuQueTocResponseModel : data) {
            YuQueTocModel yuQueTocModel = conv2TocModel(yuQueTocResponseModel);
            if(CollectionUtils.isEmpty(docSlugList)){
                if (docIdList.contains(yuQueTocModel.getDocId())) {
                    yuQueTocModel.setBinding(true);
                }
            }else {
                if (docSlugList.contains(yuQueTocModel.getKey())) {
                    yuQueTocModel.setBinding(true);
                }
            }
            tocMap.put(yuQueTocResponseModel.getUuid(), yuQueTocModel);
            if (yuQueTocResponseModel.getDepth().equals(1)) {
                toc.add(yuQueTocModel);
            } else {
                if (tocMap.get(yuQueTocResponseModel.getParentUuid()).getChildren() == null) {
                    tocMap.get(yuQueTocResponseModel.getParentUuid()).setChildren(new ArrayList<>());
                }
                tocMap.get(yuQueTocResponseModel.getParentUuid()).getChildren().add(yuQueTocModel);
            }
        }
    }

    /**
     * 检查文档类型是否支持解析
     * @param body body
     * @return java.lang.Boolean
     * <AUTHOR>
     * @since 2024.01.22
     */
    private static Boolean checkDocTypeSupportive(String body) {
        if (StringUtils.isBlank(body)) {
            return false;
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(body);
            if (jsonObject != null && jsonObject.containsKey("format")) {
                return false;
            }
        } catch (JSONException e) {
            return true;
        }
        return false;
    }


}
