package com.alipay.codegencore.model.response.answer;

import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * 索引构建响应
 */
public class IndexBuildResponse {

    private Long taskId;

    private String state;

    public IndexBuildResponse() {
    }

    /**
     * 构造函数
     * @param taskId
     * @param state
     */
    public IndexBuildResponse(Long taskId, String state) {
        this.taskId = taskId;
        this.state = state;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
