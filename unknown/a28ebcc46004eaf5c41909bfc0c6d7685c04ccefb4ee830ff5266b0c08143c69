package com.alipay.codegencore.model;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.response.tsingyan.TsingyanConfigDataModel;

import java.util.Arrays;
import java.util.List;

/**
 * 服务常量集合
 *
 * <AUTHOR>
 * 创建时间 2021-11-09
 */
public class AppConstants {


    /**
     * 青燕插件默认配置(drm默认值)
     */
    public static final String DEFAULT_TSINGYAN_PLUGIN_CONFIG_JSON = JSONObject.toJSONString(new TsingyanConfigDataModel());
    /**
     * 青燕插件配置对象(可通过drm修改)
     */
    public static volatile TsingyanConfigDataModel TSINGYAN_PLUGIN_CONFIG_MODEL = new TsingyanConfigDataModel();

    /**
     * 默认的近端配置数据(默认定时任务是24小时.24*60=1440)
     */
    public static final String DEFAULT_CODEGEN_CLIENT_CONFIG_DATA = "{\"threadIntervalTime\":1440}";
    /**
     * 默认分隔符
     */
    public static final String SPLIT_SYMBOL = "-";
    /**
     * new方法名 - 'new'
     */
    public static final String METHOD_NAME_NEW = "new";
    /**
     * 前缀树节点通配符
     */
    public static final String WILDCARD_SYMBOL = "*";

    /**
     * 无效的token内容
     * antlr4解析时，部分未开发完代码会自动填充miss语句，利用此正则来过滤无效字符
     * eg:代码： for (int k   解析时被转为： {for(<missing ')'><missing ';'>intk
     */
    public static final String INVALID_TOKEN_STR = "<([^<>]*)>*";
    /**
     * 数组正则，用来去掉代码中的中括号
     */
    public static final String ARR_TOKEN_STR = "\\[([^<>]*)\\]";

    /*********************  key start  *********************/


    /*********************  key end  *********************/

    /*********************  系统配置 start  *********************/

    /**
     * zdal的password_encypt配置key
     */
    public static final String ZDAL_CONFIG_KEY_PASSWORD_ENCRYPT = "zdal_password_encrypt";
    /**
     * zdal的obproxy配置key
     */
    public static final String ZDAL_CONFIG_KEY_OBPROXY = "zdal_obproxy_host";
    /**
     * zdal的password_encypt配置value
     */
    public static final String ZDAL_CONFIG_VALUE_PASSWORD_ENCRYPT = "true";
    /**
     * 枚举转换器的包名
     */
    public static final String MYBATIS_ENUM_TYPEHANDLER_PATH = "com.alipay.codegencore.dal.typehandler";

    /**
     * 日志分隔符
     * 防止源代码里面有",",无法拆分源代码数据
     */
    public static final String LOG_SEPARATOR = "=CODEGEN=";
    /**
     * java关键字
     */
    public static String JAVA_KEY_WORLD = "abstract,assert,boolean,break,byte,case,catch,char,class,continue,default,do,double,else,enum,extends,final,finally,float,for,if,implements,import,int,interface,instanceof,long,native,new,package,private,protected,public,return,short,static,strictfp,super,switch,synchronized,this,throw,throws,transient,try,void,volatile,while,goto,const";


    /**
     * 日志内容-补全结果
     * {结果},{耗时},{请求参数},{补全结果}
     */
    public static final String LOG_CODE_COMPLETIONS_INFO = "{}" + LOG_SEPARATOR + "{}" + LOG_SEPARATOR + "{}" + LOG_SEPARATOR + "{}";


    /**
     * 方法执行结果-成功
     */
    public static final String LOG_METHOD_RESULT_Y = "Y";
    /**
     * 方法执行结果-失败
     */
    public static final String LOG_METHOD_RESULT_N = "N";

    /**
     * 代码规则模版超时时间
     * 默认3天(服务默认每隔20分钟会有刷新）
     */
    public static final int CACHE_CODE_TEMPLATE_DATA_TIME_OUT = 3 * 24 * 60 * 60;

    /**
     * 代码补全展示名默认长度
     */
    public static volatile int CODE_COMPLETION_DISPLAY_LENGTH = 60;
    /**
     * 未知标签
     * 如果用户/本地目录为空，则将值设置为未知标签
     */
    public static final String UNKNOWN_LABEL = "UNKNOWN";

    /*********************  系统配置 end  *********************/

    /**
     * codegpt_user头部
     */
    public static final String HTTP_HEADER_CODEGPT_USER_KEY="codegpt_user";

    /**
     * codegpt_token秘钥
     */
    public static final String HTTP_HEADER_CODEGPT_TOKEN_KEY="codegpt_token";

    /**
     * 是否是debug
     */
    public static final String HTTP_HEADER_DEBUG="debug";


    /**
     * codegpt使用的token对应的用户
     */
    public static final String CODEGPT_TOKEN_USER="codegpt";

    /**
     * TechPlay使用的token对应的用户
     */
    public static final String TECHPLAY_TOKEN_USER="TechPlay";

    /**
     * workFun使用的token对应的用户
     */
    public static final String WORK_CHAT_TOKEN_USER="work_chat";

    /**
     * 钉钉机器人使用的token对应的user
     */
    public static final String ANT_CHAT_TOKEN_USER="antchat";

    /**
     * tbase中更多推荐的数据key的前缀
     */
    public static final String CODEGENCORE_MESSAGE_MORE_REFERENCE="CODEGENCORE_MESSAGE_MORE_REFERENCE";

    /**
     * rockcode的app id
     */
    public static final String ROCKCODE_APPID="CODEGENCORE";


    /**
     * rockcode的api key
     */
    public static final String CONFIG_KEY_ROCKCODE_API_KEY="rockCodeApiKey";
    /**
     * openai的api key
     */
    public static final String CONFIG_KEY_OPENAI_API_KEY="openaiApiKey";
    /**
     * 数据表选择插件的system prompt
     */
    public static final String CONFIG_KEY_DATA_TABLE_SELECT_SYSTEM_PROMPT="dataTableSelectSystemPrompt";
    /**
     * 数据表选择插件的prompt模版
     */
    public static final String CONFIG_KEY_DATA_TABLE_SELECT_PROMPT_TEMPLATE="dataTableSelectPromptTemplate";

    /**
     * 配置表key-zsearch用户名
     */
    public static final String CONFIG_KEY_ZSEARCH_USER_NAME = "zsearchUserName";
    public static final String CONFIG_KEY_ZSEARCH_USER_NAME_INDEPENDENT = "zsearchUserNameIndependent";
    /**
     * 配置表key-zsearch密码
     */
    public static final String CONFIGKEY_ZSEARCH_PASSWORD = "zsearchPassword";
    public static final String CONFIGKEY_ZSEARCH_PASSWORD_INDEPENDENT = "zsearchPasswordIndependent";
    /**
     * 配置表key-bloop问答服务地址
     */
    public static final String CONFIG_KEY_BLOOP_HOST = "PYTHON_BLOOP_HOST";

    /**
     * 配置表key-bloop问答服务预发地址
     */
    public static final String CONFIG_KEY_BLOOP_PRE_HOST = "PYTHON_BLOOP_PRE_HOST";

    /**
     * 配置表key-bloop问答服务uri
     */
    public static final String CONFIG_KEY_BLOOP_ANSWER_URI = "/api/bloop/answer";

    /**
     * 配置表key-bloop 搜索服务uri
     */
    public static final String CONFIG_KEY_BLOOP_SEARCH_URI = "/api/bloop/search";

    /**
     * 配置表key-bloop antcode copilot推荐问题配置
     */
    public static final String CONFIG_KEY_ANTCODE_COPILOT_RECOMMEND_QUESTION_CONFIG = "antcodeCopilotRecommendQuestionConfig";

    /**
     * 配置表key-bloop antcode copilot id
     */
    public static final long ANTCODE_COPILOT_SCENE_ID=11500002;

    /**
     * es默认查询数量
     */
    public static final int DEFAULT_ZSEARCH_PAGE_SIZE = 500;

    /**
     * 开关打开
     */
    public static final String ON = "ON";
    /**
     * 开关关闭
     */
    public static final String OFF = "OFF";

    /**
     * infosec系统的检测结果存储tbase的key的前缀
     */
    public static final String CODEGENCORE_INFOSEC_REVIEW_KEY_ = "CODEGENCORE_INFOSEC_REVIEW_KEY_";

    /**
     * antDsr数据审查的eventCode
     */
    public static final String ANTDSR_EVENT_CODE = "codegencore_antdsr_check";

    /**
     * antdsr审查结果标签
     */
    public static final String CODEGENCORE_ACCESS_LABEL = "codegencore_access_label";
    /**
     * 通过
     */
    public static final String PASSED = "PASSED";
    /**
     * 不通过
     */
    public static final String REJECTED = "REJECTED";

    /**
     * 会话默认名称
     */
    public static final String SESSION_DEFAULT_TITLE = "新会话";

    /**
     * 迭代默认名称前缀
     */
    public static final String ITERATION_NAME_DEFAULT = "日常开发-";

    /**
     * completion流式接口的结束标记
     */
    public static final String COMPLETION_STREAM_API_END_MARK="[DONE]";

    /**
     * 用户需要意图识别的的tbase的key
     */
    public static final String USER_NEED_INTENTION_RECOGNITION="CODEGENCORE_USER_NEED_INTENTION_RECOGNITION";

    /*
     * 用户需要审查的前缀
     */
    public static final String USER_NEED_REVIEW="CODEGENCORE_USER_NEED_REVIEW_";

    /**
     * 模型名称
     */
    public static final String CHATGPT = "chatgpt";

    /**
     * 模型名称
     */
    public static final String GPT4 = "gpt4";

    /**
     * 流式默认的结束原因
     */
    public static final String DEFAULT_STREAM_FINISH_REASON = "stop";

    /**
     * 因方法调用导致的流式结束
     */
    public static final String FUNCTION_CALL_STREAM_FINISH_REASON = "function_call";


    /**
     * 用户提问的前缀
     */
    public static final String HUMAN = "<human>";
    /**
     * AI回复的前缀
     */
    public static final String BOT = "<bot>";

    /**
     * SYSTEM
     */
    public static final String SYSTEM = "<system>";

    /**
     * FUNCTION
     */
    public static final String FUNCTION = "<function>";

    /**
     * codegpt默认的function call的开始标记
     */
    public static final String DEFAULT_CODEGPT_FUNCTION_CALL_START_TOKEN = "#function";

    /**
     * 安全耗时埋点的日志前缀
     */
    public static final String SAFETY_LOG_PREFIX = "安全耗时埋点20230731";
    /**
     * 会话加锁的前缀
     */
    public static final String CODEGENCORE_TBASE_KEY_SESSION_UID = "CODEGENCORE_TBASE_KEY_SESSION_UID:";

    /**
     * 会话级别配置key
     */
    public static final String IMPL_CONFIG = "implConfig";

    /**
     * 会话级别配置key
     */
    public static final String MAX_TOKEN = "maxToken";

    /**
     * 会话级别配置key
     */
    public static final String MAX_ROUND = "maxRound";

    /**
     * tbase中流式数据的前缀,算法的python服务也有用这个前缀写数据，如无必要不能修改。慎重。
     */
    public static final String STREAM_DATA_PREFIX="stream_answer_";

    /**
     * 同上，不过是插件的流式数据前缀，同样不要修改
     */
    public static final String PLUGIN_STREAM_DATA_PREFIX="plugin_stream_answer_";
    /**
     * 用来控制input流的逻辑， 当cancel时， 通过该值做通信
     */
    public static final String STREAM_INPUT_PREFIX="stream_input_";

    /**
     * 用户信息缓存前缀
     */
    public static final String CACHE_PREFIX = "user_auth:";
    /**
     * 插入用户的lock prefix
     */
    public static final String INSERT_USER_PREFIX = "insert_user_prefix:";

    /**
     * 模型环境监测缓存前缀
     */
    public static final String MODEL_ENV_CHECK_PREFIX = "model_env_check_prefix:";

    /**
     * 模型健康度监测缓存前缀
     */
    public static final String MODEL_HEALTH_DEGREE_CHECK_PREFIX = "model_health_degree_check_prefix:";

    /**
     * 用户取消时， 对应的finishedReason
     */
    public static final String USER_CANCELED = "userCanceled";

    /**
     * 用户取消时， 通知AGIC流关闭的标签
     */
    public static final String INPUT_CLOSE_TAG = "close";

    /**
     * 插件阶段--前置接口调用
     */
    public static final String PRE_REQUEST_STAGE = "preRequest";
    /**
     * 插件阶段--大模型调用
     */
    public static final String LLM_STAGE = "llm";
    /**
     * 插件阶段--回答
     */
    public static final String ANSWER_STAGE = "answer";
    /**
     * 插件阶段--后置接口调用
     */
    public static final String POST_REQUEST_STAGE = "postRequest";
    /**
     * 插件阶段--总结
     */
    public static final String SUMMARY = "summary";

    /**
     * 插件阶段--决策
     */
    public static final String FUNCTION_DECIDE_STAGE = "functionDecide";
    /**
     * DEFAULT
     */
    public static final String DEFAULT = "DEFAULT";
    /**
     * openAPI 同一个用户最多创建的模型数量
     */
    public static final int MODEL_UPPER_LIMIT = 10;

    /**
     * OSS的CHAT_DOCUMENT目录
     */
    public static final String CHAT_DOCUMENT = "CHAT_DOCUMENT/";
    /**
     * OSS的 OSS的CHAT_DOCUMENT 目录
     */
    public static final String CHAT_DOCUMENT_SUMMARY = "CHAT_DOCUMENT/CHAT_DOCUMENT_SUMMARY/";

    /**
     * OSS的 DOCUMENT_SOURCE_FILE 目录
     */
    public static final String DOCUMENT_SOURCE_FILE = "DOCUMENT/DOCUMENT_SOURCE_FILE/";
    /**
     * OSS的 DOCUMENT_CONTENT 目录
     */
    public static final String DOCUMENT_CONTENT = "DOCUMENT/DOCUMENT_CONTENT/";
    /**
     * OSS的 DOCUMENT_SEGMENT 目录
     */
    public static final String DOCUMENT_SEGMENT = "DOCUMENT/DOCUMENT_SEGMENT/";

    /**
     * OSS的 内网用户反馈目录
     */
    public static final String USER_FEEDBACK_INSIDE = "USER_FEEDBACK/INSIDE/";
    /**
     * OSS的 外网用户反馈目录
     */
    public static final String USER_FEEDBACK_EXTERNAL = "USER_FEEDBACK_OUT/EXTERNAL/";
    /**
     * OSS的 OSS的FORM_UPLOAD_FILE 目录
     */
    public static final String FORM_UPLOAD_FILE = "FORM_UPLOAD_FILE/";

    /**
     * 用户上传的ICON图片
     */
    public static final String USER_ICON_IMAGE = "USER_ICON_IMAGE/";

    /**
     * 删除会话关联的文件的锁的前缀
     */
    public static final String SESSION_DELETE_FILE_LOCK = "SESSION_DELETE_FILE_LOCK_";
    /**
     * OSS的AccessKey
     */
    public static final String OSS_ACCESS_KEY = "ossAccessKey";
    /**
     * OSS的SecretKey
     */
    public static final String OSS_SECRET_KEY = "ossSecretKey";
    /**
     * session绑定的文件信息的tbase的前缀
     */
    public static final String SESSION_FILE_TBASE_PREFIX = "SESSION_FILE_TBASE_PREFIX_";

    /**
     * 请求枢纽ChatGpt key
     */
    public static final String API_KEY = "HubChatGptApiKey";
    /**
     * CONTENT消息内容
     */
    public static final String CONTENT = "content";
    /**
     * openai的embedding接口
     */
    public static final String OPENAI_EMBEDDING_URL = "https://codegencore.alipay.com/api/chat/commonPower/v1/embeddings";
    /**
    * antGML webSocket请求地址
    * */
    public static final String ANTGML_WEBSOCKET_URL = "ws://antassistant-antvip.normal.global.alipay.com:9987";
    /**
     * antGML http请求地址
     */
    public static final String ANTGML_HTTP_URL = "http://antassistant-cross.global.alipay.com/ant_llm_api/chat";

    /**
     * 最短工号长度
     */
    public static final int EMP_ID_MIN_LENGTH = 6;

    /**
     * 用户限流对象的key的前缀
     */
    public static final String USER_LIMIT_KEY = "USER_LIMIT_KEY";

    /**
     * 钉钉SECRET
     */
    public static final String DING_SECRET = "dingSecret";

    /**
     * 钉钉ACCESS_TOKEN
     */
    public static final String DING_ACCESS_TOKEN = "dingAccessToken";

    /**
     * 钉钉TEST_SECRET
     */
    public static final String DING_TEST_SECRET = "dingTestSecret";

    /**
     * 钉钉TEST_ACCESS_TOKEN
     */
    public static final String DING_TEST_ACCESS_TOKEN = "dingTestAccessToken";

    /**
     * 未知的空异常
     */
    public static final String UNKNOWN_NULL_CONTENT = "出现异常，未生成任何答案，请重新发送提问内容";
    /**
     * 上传文件最大3MB
     */
    public static final long UPLOAD_MAX_SIZE = 3 * 1024 * 1024;
    /**
     * 反馈内容最大长度
     */
    public static final int FEEDBACK_MAX_LENGTH = 999;

    /**
     * dima工作项拼接内容
     */
    public static final String CONTENT_FORMAT= "<p data-lake-id=\"uf26c34c1\" id=\"uf26c34c1\"><span data-lake-id=\"u6eed20db\" id=\"u6eed20db\">%s</span>%s</p>";

    /**
     * dima显示内容拼接
     */
    public static final String DIMA_IMAGE = "<card type=\"inline\" name=\"image\" value=\"data:%s\"></card>";
    /**
     * dima工作项拼接内容
     */
    public static final String IMAGE_URL_FORMAT= "{\"src\":\"\",\"taskId\":\"u4f2a6cf2-a99d-445b-90a2-602a101f6bc\",\"clientId\":\"u1ea96771-3a27-4\","
            + "\"originalType\":\"binary\",\"linkTarget\":\"_blank\",\"name\":\"1.svg\",\"from\":\"ui\",\"ratio\":1,"
            + "\"status\":\"pending\",\"style\":\"none\",\"showTitle\":false,\"title\":\"\",\"rotation\":0,\"crop\":[0,0,1,1],"
            + "\"id\":\"ue669d55a\",\"margin\":{\"top\":true,\"bottom\":true}}\n";
    /**
     * dima创建工作项指定人empid 当前为贤过
     */
    public static final String CREATE_WORKITEM_ASSIGNEE_EMPID = "246663";

    /**
     * dima的AccessKey
     */
    public static final String DIMA_ACCESS_KEY = "dimaAccessKey";
    /**
     * dima的SecretKey
     */
    public static final String DIMA_SECRET_KEY = "dimaSecretKey";
    /**
     * codefuse再dima的工作空间id
     */
    public static final String WORKSPACE_ID = "W23001000147";
    /**
     *  requestTimeOut openapi接口设置最大值
     */
    public static final int REQUEST_TIME_OUT = 300000;
    /**
     *  firstStreamDataWaitTime openapi接口设置最大值
     */
    public static final int FIRST_STREAM_DATA_WAIT_TIME = 40000;
    /**
     * 内网应用名
     */
    public static final String INTRANET_APPLICATION_NAME = "codegencore";
    /**
     * 外网应用名
     */
    public static final String EXTRANET_APPLICATION_NAME = "tsingyancodegen";

    public static final int HEALTH_CHECK_CACHE_EXPIRED = 3600 * 24 * 3;


    public final static int THREE_DAYS = 3600 * 24 * 3;

    /**
     * 流式数据协议版本
     */
    public static final String STREAM_PROTOCOL_VERSION = "2.0";

    /**
     * 创建会话锁的前缀
     */
    public static final String CODEGENCORE_NEW_SESSION_LOCK = "CODEGENCORE_NEW_SESSION_LOCK_";

    /**
     * 确保浏览器是下载行为的 content_type
     */
    public static final String CONTENT_TYPE_DOWNLOAD = "application/octet-stream";
    /**
     * linke触发巡检SPRINT_ID
     */
    public static final String INSPECTION_SPRINT_ID = "Agent-普通账号巡检";
    /**
     * linke触发巡检SourceType
     */
    public static final String INSPECTION_SOURCE_TYPE = "linkeverify";
    /**
     * linke触发巡检YuyanId
     */
    public static final String INSPECTION_YUYAN_ID = "180020010001258565";
    /**
     * linke触发巡检YuyanName
     */
    public static final String INSPECTION_YUYAN_NAME = "CodeFuse-main";

    /**
     * antcode域名
     */
    public static final String ANTCODE_GIT_DOMAIN = "code.alipay.com";

    /**
     * antcode的默认仓库搜索分页大小
     */
    public static final Integer DEFAULT_ANTCODE_REPO_SEARCH_PAGE_SIZE = 50;

    /**
     * session上下文中存储的仓库路径，用于仓库问答
     */
    public static final String SESSION_CONTEXT_REPO_PATH_KEY = "repoPath";

    /**
     * session上下文中存储的分支
     */
    public static final String SESSION_CONTEXT_BRANCH_KEY = "branch";

    /**
     * 会话开始前填写的信息对应的key
     */
    public static final String SESSION_CONTEXT_FORM_DATA_KEY = "formData";
    /**
     * 调整为队列的handler
     */
    public static final List<String> QUEUE_HANDLER = Arrays.asList("MayaStreamModelHandler",
            "ChatGptModelHubHandler");
    ;

    /**
     * 仓库文件校验定时任务的锁
     */
    public static final String REPO_FILE_CHECK_LOCK = "REPO_FILE_CHECK_LOCK";

    /**
     * http请求的traceId
     */
    public static final String HTTP_TRACE_ID_HEADER = "SOFA-TraceId";
}
