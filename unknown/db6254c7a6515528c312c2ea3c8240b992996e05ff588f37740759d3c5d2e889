package com.alipay.codegencore.model.domain;

import com.alipay.codegencore.model.enums.BizSceneEnum;

/**
 * 代码模版对象
 *
 * <AUTHOR>
 * 创建时间 2022-02-22
 */
public class CodeTemplateDO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 模板名
     * 根据模版名称可以从oss获取到模版内容
     */
    private String name;
    /**
     * 模版规则
     * 目前参考 {@link com.alipay.codegencore.model.model.CodeTemplateRuleModel} 配置，后续可扩展
     */
    private String rule;
    /**
     * 模版场景类型
     */
    private BizSceneEnum bizSceneEnum;
    /**
     * 模版所用于的场景描述
     */
    private String sceneDesc;

    public BizSceneEnum getBizSceneEnum() {
        return bizSceneEnum;
    }

    public void setBizSceneEnum(BizSceneEnum bizSceneEnum) {
        this.bizSceneEnum = bizSceneEnum;
    }

    public String getSceneDesc() {
        return sceneDesc;
    }

    public void setSceneDesc(String sceneDesc) {
        this.sceneDesc = sceneDesc;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }
}
