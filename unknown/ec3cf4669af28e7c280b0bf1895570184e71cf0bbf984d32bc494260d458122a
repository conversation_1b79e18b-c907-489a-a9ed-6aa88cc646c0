package com.alipay.codegencore.service.codegpt.user;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.request.TokenRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.fc.process.common.service.facade.callback.CallbackInfo;
import com.alipay.fc.process.common.service.facade.callback.ProcessCallback;
import com.alipay.fc.process.common.service.facade.callback.ProcessInfo;
import com.alipay.fc.process.common.service.facade.enums.ErrorCodeEnum;
import com.alipay.fc.process.common.service.facade.model.ProcessBasicMap;
import com.alipay.fc.process.common.service.facade.result.ResultWithData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

import static com.alipay.codegencore.model.contant.WebApiContents.CONTEXT_USER;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.11.27
 */
public class TokenAuditService implements ProcessCallback {
    private static final Logger LOGGER = LoggerFactory.getLogger(TokenAuditService.class);
    @Resource(name = "tokenService")
    private TokenService tokenService;
    @Resource
    private UserAuthDOMapper userAuthDOMapper;
    @Override
    public ResultWithData<CallbackInfo> onCall(ProcessInfo info) {
        ResultWithData<CallbackInfo> result = new ResultWithData<CallbackInfo>();
        try {
            String operate = info.getOperate();
            if(operate.equals("reject")){
                CallbackInfo callbackInfo = new CallbackInfo();
                result.setResultObj(callbackInfo);
                ProcessBasicMap context = callbackInfo.getBusinessContext();
                context.put("result", "创建token被拒绝");
                return result;
            }
            // 记录审批人信息
            String operatorName = info.getOperateTraces().get(0).getOperatorName();
            UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
            userAuthDOExample.createCriteria().andUserNameEqualTo(operatorName);
            List<UserAuthDO> userAuthDOS = userAuthDOMapper.selectByExample(userAuthDOExample);
            if(!userAuthDOS.isEmpty()){
                ContextUtil.set(CONTEXT_USER, userAuthDOS.get(0));
            }
            //1、可以从ProcessInfo中取得各种有用的参数
            //业务上下文，这个map也就是由BPInstanceFacade.start方法传入的
            ProcessBasicMap businessContext = info.getBusinessContext();
            HashMap<String,Object> userInfo = (HashMap<String, Object>) businessContext.get("empId");
            String empId = (String) userInfo.get("staffNo");
            BigDecimal balance = new BigDecimal(100);
            String appName = (String) businessContext.get("appName");
            String description = (String) businessContext.get("description");
            String uriPatternList = (String) businessContext.get("uriPatternList");
            String user = (String) businessContext.get("user");
            LOGGER.info("token流程业务上下文：{}", JSONObject.toJSONString(info));
            TokenRequestBean tokenRequestBean = new TokenRequestBean();
            tokenRequestBean.setEmpId(empId);
            tokenRequestBean.setBalance(balance);
            tokenRequestBean.setDescription(description);
            tokenRequestBean.setUriPatternList(uriPatternList);
            tokenRequestBean.setUser(user);
            tokenRequestBean.setEnableStatus((byte)1);
            tokenRequestBean.setAppName(appName);
            BaseResponse data = tokenService.insert(tokenRequestBean);
            if(data.getErrorCode() != 0){
                result.setSuccess(false);
                result.setError(ErrorCodeEnum.UNKNOW, "创建token失败"+data.getErrorMsg());
                return result;
            }
            TokenDO token = (TokenDO)data.getData();
            CallbackInfo callbackInfo = new CallbackInfo();
            result.setResultObj(callbackInfo);
            result.setSuccess(true);
            ProcessBasicMap context = callbackInfo.getBusinessContext();
            context.put("result", "codegpt_token:"+token.getToken()+"\n"+"codegpt_user:"+token.getUser());
        } catch (Exception e) {
            LOGGER.error("token流程回调失败", e);
            result.setSuccess(false);
            result.setError(ErrorCodeEnum.UNKNOW, "执行错误信息");
        }
        //流程平台以 result 为 success 和 false 来判断回调是否成功
        return result;
    }
}
