package com.alipay.codegencore.utils.code;

import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;


class CodeInsightClientTest {

    @BeforeEach
    void beforeInit() {
    }


    @Test
    void searchTaskStatus() {

        Long recordId = 1316620224L;
        String result = CodeInsightClient.searchTaskStatus(recordId);
        Assert.assertNotNull(result);

    }

    @Test
    void createBuildTask() {
    }
}