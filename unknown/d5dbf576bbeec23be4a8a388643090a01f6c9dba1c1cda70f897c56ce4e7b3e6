package com.alipay.codegencore.service.middle.msgbroker;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.date.DateUtil;
import com.alipay.codegencore.dal.example.DocumentDOExample;
import com.alipay.codegencore.dal.mapper.DocumentDOMapper;
import com.alipay.codegencore.model.domain.DocumentDO;
import com.alipay.codegencore.model.enums.DocumentSourceEnum;
import com.alipay.codegencore.model.enums.DocumentStatusEnum;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.codegencore.service.middle.zsearch.ZsearchCommonService;
import com.alipay.common.event.UniformEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.middle.msgbroker
 * @CreateTime : 2024-01-19
 */
@Service("yuQueDocumentRenewListener")
public class YuQueDocumentRenewListener implements CodegencoreEventHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(YuQueDocumentRenewListener.class);

    /**
     * topic|eventCode的Key 一般用于注册
     */
    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_F_SC|EC_codegencore_yuque_document_renew";

    @Resource
    private DocumentHandleService documentHandleService;
    @Resource
    private DocumentDOMapper      documentDOMapper;
    @Resource
    private ZsearchCommonService  zsearchCommonService;
    @Resource
    private CodeGPTDrmConfig      codeGPTDrmConfig;

    @Override
    public void handle(UniformEvent message) {
        LOGGER.info("开始更新语雀文档时间:{}", DateUtil.formatChineseDate(new Date(), false, true));
        //查询所有ready状态的document
        DocumentDOExample documentDOExample = new DocumentDOExample();
        documentDOExample.createCriteria().andDocumentStatusEqualTo(DocumentStatusEnum.READY.name()).andSourceEqualTo(
                DocumentSourceEnum.YUQUE_BOOK.name());
        List<DocumentDO> documentDOS = documentDOMapper.selectByExample(documentDOExample);
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        documentDOS.parallelStream().forEach(documentDO -> {
            try {
                documentHandleService.updateYuQueDocument(documentDO, documentChatConfig);
                LOGGER.info("更新语雀文档成功,documentUid:{}", documentDO.getUid());
            } catch (Exception e) {
                LOGGER.error("更新语雀文档失败,documentDO:{} message:{}", JSONObject.toJSONString(documentDO), e);
                DocumentDOExample documentDOExampleDb = new DocumentDOExample();
                documentDOExampleDb.createCriteria().andUidEqualTo(documentDO.getUid());
                DocumentDO documentDODb = new DocumentDO();
                documentDO.setDocumentStatus(DocumentStatusEnum.PARSE_FAILED.name());
                documentDOMapper.updateByExampleSelective(documentDODb, documentDOExample);
            }
        });
    }

    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }
}
