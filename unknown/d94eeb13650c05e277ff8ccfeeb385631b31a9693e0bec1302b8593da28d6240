package com.alipay.codegencore.service.ideaevo.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjsonfordrm.JSON;
import com.alipay.codegencore.dal.mapper.AnswerIndexBuildTaskMapper;
import com.alipay.codegencore.model.domain.AnswerIndexBuildTaskDO;
import com.alipay.codegencore.model.enums.PlanStepType;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.answer.TaskState;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.openai.*;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.ideaevo.*;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.drm.SvatDrmConfig;
import com.alipay.codegencore.utils.SvatLogUtils;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.code.BloopSearchClient;
import com.alipay.codegencore.utils.code.CodefuseSearchClient;
import com.alipay.codegencore.utils.code.JavaATS;
import com.alipay.codegencore.utils.thread.ThreadPoolUtils;
import com.github.javaparser.ast.expr.SimpleName;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 19:37
 */
@Slf4j
@Service
public class ActionGenCodeServiceImpl implements ActionGenCodeService {

    /**
     * 接口变更
     */
    private static final String HTTP_MODIFY = "HTTP_MODIFY";


    @Autowired
    private ModelRequestHandler modelRequestHandler;

    @Autowired
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Autowired
    private SvatDrmConfig svatDrmConfig;

    @Autowired
    private AnswerIndexBuildTaskMapper answerIndexBuildTaskMapper;

    @Autowired
    private CalculateTokenService calculateTokenService;

    @Autowired
    private ActionGenCodeSearchInfoService actionGenCodeSearchInfoService;


    @Override
    public List<PlanFile> genPlanFile(GenPlanRequest request, String modelEnv, String searchType, String planType, String modelName, String planPromptName) {

        final String sessionId = request.getSessionId();
        final GenRequirementInfo genRequirementInfo = request.getRequirementInfo();
        final String query = getRequirementDesc(genRequirementInfo);

        RepoInfo repoInfo = request.getRepoInfo();

        if (HTTP_MODIFY.equals(genRequirementInfo.getChangeAsset())) {
            //接口变更
            log.info("session:{} is http modify:{}", sessionId, genRequirementInfo.getChangeAsset());
            List<PlanFile> planFileList = modelRequestHandler.splitSendModelPlanFileGenForHttpModify(sessionId, query, genRequirementInfo, repoInfo, modelEnv, modelName);
            if (CollectionUtils.isNotEmpty(planFileList)) {
                return planFileList;
            }
        }

        //搜索
        VatSearchType vatSearchType = VatSearchType.getByName(getSearchType(searchType));
        if (VatSearchType.V2_ANSWER == vatSearchType) {
            String branch = indexCheck(repoInfo);
            repoInfo.setBranch(branch);
        }
        List<BloopSearchClient.CodeResult> codeList = onlySearch(repoInfo, query, vatSearchType);

        //搜索结果后处理
        VatPlanType vatPlanType = VatPlanType.getByName(getPlanType(planType));
        List<BloopSearchClient.CodeResult> handleCodeList;
        if (VatSearchType.V3_BATE == vatSearchType
                && VatPlanType.PART_GEN == vatPlanType) {
            handleCodeList = codeList;
        } else {
            handleCodeList = searchPostHandle(repoInfo, codeList, vatSearchType);
        }

        //异步保存搜索结果
        ThreadPoolUtils.execute(ThreadPoolUtils.indexBuildPool,
                () -> actionGenCodeSearchInfoService.save(sessionId, query, JSON.toJSONString(handleCodeList)));

        return generationPlan(query, handleCodeList, modelEnv, vatPlanType, modelName, planPromptName);
    }

    @Override
    public CodeInfoFile genCodeFile(GenCodeFileRequest genCodeFileRequest, String modelEnv, String modelName, String codePromptName) {

        final String query = getRequirementDesc(genCodeFileRequest.getRequirementInfo());

        Stopwatch stopwatch = Stopwatch.createStarted();
        CodeInfoFile codeInfoFile = null;
        try {
            //先尝试拉取文件内容
            RepoInfo repoInfo = genCodeFileRequest.getRepoInfo();
            PlanFile plan = genCodeFileRequest.getPlan();
            String fileContent = AntCodeClient.getFileContent(repoInfo.getRepoPath(), repoInfo.getBranch(), plan.getFilePath());

            if (StringUtils.isBlank(fileContent)) {
                //表示可能是新增文件，增加一次查询，排除查询问题
                fileContent = AntCodeClient.getFileContent(repoInfo.getRepoPath(), repoInfo.getBranch(), plan.getFilePath());
                if (StringUtils.isBlank(fileContent)) {
                    plan.setType(PlanStepType.A.name());
                }
            }

            //非新增，如果代码内容为空，要报错
            if (!PlanStepType.A.eq(plan.getType())
                    && StringUtils.isBlank(fileContent)) {
                throw new BizException(ResponseEnum.SVAT_GEN_CODE_NOT_FILE_CONTENT);
            }

            codeInfoFile = modelRequestHandler.sendModelCodeFileGen(query, plan, fileContent, modelEnv, modelName, codePromptName);
        } finally {
            stopwatch.stop();
            SvatLogUtils.log(SvatLogUtils.BizType.CODE_GEN, codeInfoFile != null, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
        return codeInfoFile;
    }

    @Override
    public void genCodeFileStream(GenCodeFileRequest genCodeFileRequest, String modelEnv, String modelName) {
        final String query = getRequirementDesc(genCodeFileRequest.getRequirementInfo());

        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            //先尝试拉取文件内容
            RepoInfo repoInfo = genCodeFileRequest.getRepoInfo();
            PlanFile plan = genCodeFileRequest.getPlan();
            String fileContent = AntCodeClient.getFileContent(repoInfo.getRepoPath(), repoInfo.getBranch(), plan.getFilePath());

            if (StringUtils.isBlank(fileContent)) {
                //表示可能是新增文件，增加一次查询，排除查询问题
                fileContent = AntCodeClient.getFileContent(repoInfo.getRepoPath(), repoInfo.getBranch(), plan.getFilePath());
                if (StringUtils.isBlank(fileContent)) {
                    plan.setType(PlanStepType.A.name());
                }
            }

            //非新增，如果代码内容为空，要报错
            if (!PlanStepType.A.eq(plan.getType())
                    && StringUtils.isBlank(fileContent)) {
                throw new BizException(ResponseEnum.SVAT_GEN_CODE_NOT_FILE_CONTENT);
            }

            modelRequestHandler.sendModelCodeFileGenStream(query, plan, fileContent, modelEnv, modelName);
        } finally {
            stopwatch.stop();
            SvatLogUtils.log(SvatLogUtils.BizType.CODE_GEN, true, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }
    }

    @Override
    public List<BloopSearchClient.CodeResult> search(GenPlanRequest searchRequest) {
        final String query = getRequirementDesc(searchRequest.getRequirementInfo());

        RepoInfo repoInfo = searchRequest.getRepoInfo();
        String branch = indexCheck(repoInfo);
        repoInfo.setBranch(branch);
        List<BloopSearchClient.CodeResult> codeResultList = onlySearch(repoInfo,
                query, VatSearchType.V2_ANSWER);
        if (CollectionUtils.isEmpty(codeResultList)) {
            throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL);
        }
        return codeResultList;
    }

    @Override
    public JSONObject evaluationFile(GenPlanRequest request, String modelEnv, String searchType,
                                     String planType, String codeType, String planModelName,
                                     String codeModelName, int codeModelCount, String planPromptName) {
        JSONObject resultData = new JSONObject();

        try {

            final String sessionId = request.getSessionId();
            final String query = getRequirementDesc(request.getRequirementInfo());

            RepoInfo repoInfo = request.getRepoInfo();

            JSONObject searchRequest = new JSONObject();
            searchRequest.put("repoPath", repoInfo.getRepoPath());
            searchRequest.put("branch", repoInfo.getBranch());
            searchRequest.put("query", query);
            resultData.put("searchReq", searchRequest);

            //搜索
            VatSearchType vatSearchType = VatSearchType.getByName(searchType);
            if (VatSearchType.V2_ANSWER == vatSearchType) {
                String branch = indexCheck(repoInfo);
                repoInfo.setBranch(branch);
            }
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<BloopSearchClient.CodeResult> codeList = onlySearch(repoInfo, query, vatSearchType);
            stopwatch.stop();
            resultData.put("searchRes", codeList);
            resultData.put("searchCount", codeList.size());
            resultData.put("searchCost", stopwatch.elapsed(TimeUnit.MILLISECONDS));

            //搜索结果后处理
            VatPlanType vatPlanType = VatPlanType.getByName(planType);
            List<BloopSearchClient.CodeResult> handleCodeList;
            if (VatSearchType.V3_BATE == vatSearchType
                    && VatPlanType.PART_GEN == vatPlanType) {
                handleCodeList = codeList;
            } else {
                handleCodeList = searchPostHandle(repoInfo, codeList, vatSearchType);
            }
            resultData.put("searchPostHandleRes", handleCodeList);
            resultData.put("searchPostHandleCount", handleCodeList.size());
            if (CollectionUtils.isEmpty(handleCodeList)) {
                return resultData;
            }

            JSONObject planRequest = new JSONObject();
            planRequest.put("query", query);
            planRequest.put("codeInfoList", handleCodeList);
            resultData.put("planReq", planRequest);

            if (StringUtils.isBlank(planModelName)) {
                return resultData;
            }
            //plan 生成
            stopwatch.reset();
            stopwatch.start();
            List<PlanFile> planFileList = generationPlan(query, handleCodeList, modelEnv, vatPlanType, planModelName, planPromptName);
            stopwatch.stop();
            resultData.put("planRes", planFileList);
            resultData.put("planCount", planFileList.size());
            resultData.put("planCost", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            if (CollectionUtils.isEmpty(planFileList)) {
                return resultData;
            }

            if (StringUtils.isBlank(codeModelName)) {
                return resultData;
            }
            //发起 code 生成
            stopwatch.reset();
            stopwatch.start();
            List<JSONObject> codeGenList = Lists.newArrayList();

            Semaphore semaphore = new Semaphore(codeModelCount);

            List<Future<JSONObject>> futureList = planFileList
                    .stream()
                    .map(planFile -> ThreadPoolUtils.submit(ThreadPoolUtils.indexBuildPool,
                            () -> {
                                try {
                                    GenCodeFileRequest genCodeFileRequest = new GenCodeFileRequest();
                                    genCodeFileRequest.setRepoInfo(repoInfo);
                                    genCodeFileRequest.setPlan(planFile);
                                    genCodeFileRequest.setSessionId(sessionId);
                                    GenRequirementInfo genRequirementInfo = new GenRequirementInfo();
                                    genRequirementInfo.setGenerateChangeLogic(query);
                                    genRequirementInfo.setUserChangeLogic(query);
                                    genCodeFileRequest.setRequirementInfo(genRequirementInfo);
                                    JSONObject codeData = new JSONObject();
                                    codeData.put("codeReq", genCodeFileRequest);
                                    log.info("session:{} file code gen:{}", sessionId, planFile.getFilePath());
                                    semaphore.acquire();
                                    Stopwatch stopwatchItem = Stopwatch.createStarted();
                                    CodeInfoFile codeInfoFile = genCodeFile(genCodeFileRequest, modelEnv, codeModelName, codeType);
                                    stopwatchItem.stop();
                                    log.info("session:{} code gen:{}", sessionId, codeInfoFile);
                                    codeData.put("codeRes", codeInfoFile);
                                    codeData.put("codeOnceCost", stopwatchItem.elapsed(TimeUnit.MILLISECONDS));
                                    return codeData;
                                } catch (Exception e) {
                                    log.warn("session:{} file:{} gen code failed", sessionId, planFile.getFilePath(), e);
                                } finally {
                                    semaphore.release();
                                }
                                return null;
                            }))
                    .collect(Collectors.toList());

            futureList.forEach(future -> {
                try {
                    JSONObject jsonObject = future.get();
                    if (jsonObject != null) {
                        codeGenList.add(jsonObject);
                    }
                } catch (Exception e) {
                    log.error("get gen code result failed.", e);
                }
            });

            stopwatch.stop();
            resultData.put("codeCost", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            resultData.put("codeGen", codeGenList);
            resultData.put("codeCount", codeGenList.size());
            resultData.put("codeAvgCost", codeGenList.stream().mapToLong(item -> item.getLongValue("codeOnceCost")).average().orElse(0));

        } catch (Exception e) {
            log.error("found exception. req:{}", request, e);
        }

        return resultData;
    }


    /**
     * 返回需求描述
     * @param genRequirementInfo
     * @return
     */
    private String getRequirementDesc(GenRequirementInfo genRequirementInfo) {

        if (codeGPTDrmConfig.isActionGenCodeQuerySwitch()) {
            return genRequirementInfo.getUserChangeLogic();
        }

        return genRequirementInfo.getGenerateChangeLogic();
    }

    /**
     * 索引创建校验
     * @param repoInfo
     */
    private String indexCheck(RepoInfo repoInfo) {

        String branch = AntCodeClient.defaultIfBlank(repoInfo.getBranch());

        Pair<String, String> groupAndProject = AntCodeClient.getRepoInfoByRepoPath(repoInfo.getRepoPath());
        if (groupAndProject == null) {
            throw new BizException(ResponseEnum.SVAT_REPO_INFO_ILL);
        }
        AnswerIndexBuildTaskDO taskDO = answerIndexBuildTaskMapper.getByRepoInfo(groupAndProject.getLeft(),
                groupAndProject.getRight(), branch);

        if ((taskDO == null
                || !TaskState.indexUsable(taskDO.getState()))
                && !AntCodeClient.isMaster(branch)) {
            branch = AntCodeClient.DEFAULT_BRANCH;
            // 用 master 重试下
            taskDO = answerIndexBuildTaskMapper.getByRepoInfo(groupAndProject.getLeft(),
                    groupAndProject.getRight(), branch);
        }

        if (taskDO == null) {
            throw new BizException(ResponseEnum.SVAT_REPO_NOT_INDEX);
        }

        if (!TaskState.indexUsable(taskDO.getState())) {
            throw new BizException(ResponseEnum.SVAT_INDEX_BUILDING);
        }

        return branch;
    }

    /**
     * 仅搜索
     * @param repoInfo
     * @param query
     * @param searchType
     * @return
     */
    public List<BloopSearchClient.CodeResult> onlySearch(RepoInfo repoInfo,
                                                         String query, VatSearchType searchType) {

        //进行相似代码搜索
        log.info("begin search:{} repo:{}", query, repoInfo);
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<BloopSearchClient.CodeResult> codeResultList = Lists.newArrayList();
        try {
            if (VatSearchType.V2_ANSWER == searchType) {
                codeResultList = BloopSearchClient.searchFile(repoInfo.getRepoPath(),
                        repoInfo.getBranch(), query);

            } else if (VatSearchType.V3_BATE == searchType) {
                String repoUrl = repoInfo.getRepoURL();
                if (StringUtils.isBlank(repoUrl)) {
                    repoUrl = "https://code.alipay.com/" + repoInfo.getRepoPath();
                }
                codeResultList = BloopSearchClient.searchFileV2(repoUrl,
                        repoInfo.getBranch(), query, svatDrmConfig.getLexcialIndexConfig(),
                        svatDrmConfig.getLexcialRecallSize(), svatDrmConfig.getEmbeddingIndexConfig(),
                        svatDrmConfig.getEmbeddingRecallSize(), svatDrmConfig.getRerankName());
            } else if (VatSearchType.V4_VAT_SEARCH == searchType) {
                String repoUrl = repoInfo.getRepoURL();
                if (StringUtils.isBlank(repoUrl)) {
                    repoUrl = "https://code.alipay.com/" + repoInfo.getRepoPath() + ".git";
                }
                codeResultList = CodefuseSearchClient.vatSearch(repoUrl, repoInfo.getBranch(), query);
            } else {
                codeResultList = List.of();
            }
        } finally {
            stopwatch.stop();
            final long cost = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            SvatLogUtils.log(SvatLogUtils.BizType.SEARCH, CollectionUtils.isNotEmpty(codeResultList), cost);
            log.info("search result:{} cost:{}", codeResultList, cost);
        }

        return codeResultList;
    }

    /**
     * 搜索后处理
     * @param repoInfo
     * @param codeResultList
     * @param searchType
     * @return
     */
    public List<BloopSearchClient.CodeResult> searchPostHandle(RepoInfo repoInfo, List<BloopSearchClient.CodeResult> codeResultList,
                                                               VatSearchType searchType) {
        log.info("begin search post handle");
        if (VatSearchType.V2_ANSWER == searchType) {

            //查找代码片段
            return codeResultList.parallelStream()
                    .peek(codeResult -> {
                        String content = AntCodeClient.getFileContent(repoInfo.getRepoPath(), repoInfo.getBranch(),
                                codeResult.getPath());
                        if (StringUtils.isNotBlank(content)) {
                            String[] lines = AntCodeClient.splitCodeLine(content);
                            if (ArrayUtils.isNotEmpty(lines)) {
                                int length = lines.length;
                                int start = codeResult.getStartLine() > length ? length - 1 : codeResult.getStartLine() - 1;
                                int end = codeResult.getEndLine() > length ? length - 1 : codeResult.getEndLine() - 1;
                                if (start > end) {
                                    end = start;
                                }
                                StringBuilder snippet = new StringBuilder();
                                for (int i = start; i <= end; i++) {
                                    snippet.append(lines[i]).append("\n");
                                }
                                snippet.deleteCharAt(snippet.length() - 1);
                                codeResult.setSnippet(snippet.toString());
                                codeResult.setContent(snippet.toString());
                            }
                        }
                    }).collect(Collectors.toList());

        } else if (VatSearchType.V3_BATE == searchType) {
            String[] suffixWhites = StringUtils.split(svatDrmConfig.getSearchResultFileSuffixWhite(), ",");
            //按照分值排序
            List<BloopSearchClient.CodeResult> sortCodeResultList = codeResultList.stream()
                    .filter(item -> {
                        for (String suffix : suffixWhites) {
                            if (item.getPath().endsWith(suffix)) {
                                return true;
                            }
                        }
                        return false;
                    })
                    .sorted(Comparator.comparing(BloopSearchClient.CodeResult::getRerankScore))
                    .collect(Collectors.toList());

            //根据 path 聚合
            Map<String, List<BloopSearchClient.CodeResult>> pathCodes = sortCodeResultList.stream()
                    .collect(Collectors.groupingBy(BloopSearchClient.CodeResult::getPath));

            //补充方法内容
            Map<String, List<SimpleName>> pathMethods = JavaATS.getFileMethods(repoInfo.getRepoPath(), repoInfo.getBranch(), pathCodes.keySet());

            long planInputMaxTokens = svatDrmConfig.getPlanInputMaxTokens();
            List<BloopSearchClient.CodeResult> codeResults = Lists.newArrayList();
            //这块最多抛弃 20 条
            for (int i = 0; i < 20; i++) {

                codeResults = JavaATS.convertMethodChunk(pathCodes, pathMethods);

                //计算 token
                String allContent = codeResults.stream().map(item -> String.join("\n", item.getPath(), item.getContent()))
                        .collect(Collectors.joining("\n"));
                long tokenSize = calculateTokenService.getTokenQty(allContent);
                log.info("content token size:{} config size:{}", tokenSize, planInputMaxTokens);
                if (tokenSize <= planInputMaxTokens) {
                    break;
                }
                //抛弃掉低分值内容,排序后删除第一个
                BloopSearchClient.CodeResult remove = sortCodeResultList.remove(0);
                log.info("remove low score:{} path:{}", remove.getRerankScore(), remove.getPath());
                pathCodes = sortCodeResultList.stream()
                        .collect(Collectors.groupingBy(BloopSearchClient.CodeResult::getPath));
            }
            log.info("search post handle:{}", codeResults);
            return codeResults;
        }

        return codeResultList;
    }

    /**
     * 计划生成
     * @param query
     * @param codeList
     * @param modelEnv
     * @param planType
     * @return
     */
    public List<PlanFile> generationPlan(String query, List<BloopSearchClient.CodeResult> codeList,
                                         String modelEnv, VatPlanType planType, String modelName,
                                         String planPromptName) {

        log.info("plan gen:{}", planType);
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<PlanFile> planFileList = Lists.newArrayList();
        try {
            if (VatPlanType.ALL_GEN == planType) {
                planFileList = modelRequestHandler.sendModelPlanFileGenNew(query, codeList, modelEnv, modelName, planPromptName);
            } else {
                List<PlanFile> planList = modelRequestHandler.splitSendModelPlanFileGen(query, codeList, modelEnv, modelName);

                //按照文件聚合
                Map<String, List<PlanFile>> planMap = planList.stream().collect(Collectors.groupingBy(PlanFile::getFilePath));
                for (String path : planMap.keySet()) {
                    List<PlanFile> plans = planMap.get(path);
                    PlanFile planFile = new PlanFile();
                    planFile.setFilePath(path);
                    planFile.setType(PlanStepType.M.name());
                    List<String> steps = Lists.newArrayList();
                    plans.forEach(plan -> {
                        if (CollectionUtils.isNotEmpty(plan.getStep())) {
                            steps.addAll(plan.getStep());
                        }
                    });
                    planFile.setStep(steps);
                    planFileList.add(planFile);
                }
            }
        } finally {
            stopwatch.stop();
            final long cost = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            SvatLogUtils.log(SvatLogUtils.BizType.PLAN_GEN, CollectionUtils.isNotEmpty(planFileList), cost);
            log.info("plan gen result:{} cost:{}", planFileList, cost);
        }

        if (CollectionUtils.isEmpty(planFileList)) {
            throw new BizException(ResponseEnum.SVAT_QUERY_FUZZINESS);
        }

        return planFileList;
    }

    /**
     * 搜索类型
     * @param searchType
     * @return
     */
    private String getSearchType(String searchType) {
        if (StringUtils.isBlank(searchType)
                || !VatSearchType.valid(searchType)) {
            //为空或者指定的非法，则以 drm 中指定值为准
            return svatDrmConfig.getSearchType();
        }
        return searchType;
    }

    /**
     * 计划生成类型
     * @param planType
     * @return
     */
    private String getPlanType(String planType) {
        if (StringUtils.isBlank(planType)
                || !VatPlanType.valid(planType)) {
            //为空或者指定的非法，则以 drm 中指定值为准
            return svatDrmConfig.getPlanType();
        }
        return planType;
    }

}
