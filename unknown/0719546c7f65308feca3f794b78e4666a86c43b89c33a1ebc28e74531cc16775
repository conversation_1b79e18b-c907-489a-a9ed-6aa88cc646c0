package com.alipay.codegencore.model.response.linke;

import java.util.List;

import com.alipay.codegencore.model.enums.IterationTypeEnum;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.response.linke
 * @CreateTime : 2024-04-15
 */
public class WorkItemDefaultVO {
    /**
     * 默认的工作项信息
     */
    private List<WorkItemVO>  defaultWorkItems;
    /**
     * 默认的迭代类型
     */
    private IterationTypeEnum defaultIterationTypeValue;
    /**
     * 默认的应用名
     */
    private String            defaultAppName;

    public List<WorkItemVO> getDefaultWorkItems() {
        return defaultWorkItems;
    }

    public void setDefaultWorkItems(List<WorkItemVO> defaultWorkItems) {
        this.defaultWorkItems = defaultWorkItems;
    }

    public IterationTypeEnum getDefaultIterationTypeValue() {
        return defaultIterationTypeValue;
    }

    public void setDefaultIterationTypeValue(IterationTypeEnum defaultIterationTypeValue) {
        this.defaultIterationTypeValue = defaultIterationTypeValue;
    }

    public String getDefaultAppName() {
        return defaultAppName;
    }

    public void setDefaultAppName(String defaultAppName) {
        this.defaultAppName = defaultAppName;
    }
}
