package com.alipay.codegencore.web.codegpt;

import com.alibaba.fastjson.JSONArray;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserStatusEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.request.UpdateAllowAccessTypeVO;
import com.alipay.codegencore.model.request.UpdateUserVO;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.codegpt.DevInsightServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.web.codegpt
 * @CreateTime : 2023-04-23
 */
@Slf4j
@RestController
@CodeTalkWebApi
@RequestMapping("/webapi/userAuth")
public class CodeFuseUserAuthController {


    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;

    @Resource
    private DevInsightServiceImpl devInsightService;

    @Resource
    private UserAclService userAclService;

    /**
     * 查看所有用户
     *
     * @param pageNo       分页
     * @param pageSize     分页
     * @param filterField  过滤条件 名字和工号字段模糊搜索
     * @param startTime    根据创建时间过滤 开始时间
     * @param endTime      结束时间
     * @param filterAdmin  过滤是否是admin
     * @param filterStatus 筛选 是否是正常用户
     * @return
     */
    @GetMapping(path = "/userList")
    public PageResponse<List<UserAuthDO>> userList(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                   @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                   @RequestParam(value = "filterField", required = false) String filterField,
                                                   @RequestParam(value = "startTime", required = false) String startTime,
                                                   @RequestParam(value = "endTime", required = false) String endTime,
                                                   @RequestParam(value = "filterAdmin", required = false) String filterAdmin,
                                                   @RequestParam(value = "filterStatus", required = false) String filterStatus) {
        isAdmin();
        return codeFuseUserAuthService.selectUserAuth(pageNo, pageSize, filterField, startTime, endTime, filterAdmin, filterStatus);
    }


    /**
     * 工号或者花名 支持模糊搜索员工 调用研发洞察
     *
     * @param query
     * @return
     */
    @GetMapping(path = "/queryUser")
    public BaseResponse<List<Map>> queryUser(@RequestParam String query, @RequestParam(defaultValue = "false") Boolean containDismission) {
        JSONArray data = devInsightService.queryUser(query);
        List<Map> result = data.toJavaList(Map.class);
        if (!containDismission){
            result = result.stream().filter(map -> !(boolean)map.get("isDisabled")).collect(Collectors.toList());
        }
        return BaseResponse.build(result);
    }

    /**
     * 添加用户
     * 支持批量添加 失败的empId 返回出去
     *
     * @return
     */
    @PostMapping(path = "/userIncrease")
    public BaseResponse<List<String>> increaseUser(@RequestBody List<String> empId) {
        isAdmin();
        List<String> empIds = codeFuseUserAuthService.insertUserAuth(empId);
        return BaseResponse.build(empIds);
    }

    /**
     * 批量更新用户状态
     *
     * @return
     */
    @PostMapping(path = "/updateUserStatus")
    public BaseResponse<Boolean> updateUserStatus(@RequestBody UpdateUserVO updateUserVO) {
        isAdmin();
        codeFuseUserAuthService.updateUserStatus(updateUserVO.getUserIdList(), updateUserVO.getNewStatus() == UserStatusEnum.ACTIVE);
        return BaseResponse.buildSuccess();
    }

    /**
     * 更新权限范围
     * @return 更新成功
     */
    @PostMapping("/updateAllowAccessType")
    public BaseResponse<Boolean> updateAllowAccessType(@RequestBody UpdateAllowAccessTypeVO updateAllowAccessTypeVO) {
        isAdmin();
        if (updateAllowAccessTypeVO == null || updateAllowAccessTypeVO.getUserId() == null || updateAllowAccessTypeVO.getAllowAccessType() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        codeFuseUserAuthService.updateAllowAccessType(updateAllowAccessTypeVO);
        return BaseResponse.buildSuccess();
    }

    /**
     * 判断是否是管理员
     */
    private void isAdmin() {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
    }

}
