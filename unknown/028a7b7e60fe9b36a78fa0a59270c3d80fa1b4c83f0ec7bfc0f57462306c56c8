package com.alipay.codegencore.model.model;

import java.util.List;

/**
 * <AUTHOR>
 * @version : UpdateSessionContextRequestModel, v 0.1 2023年04月19日 16:58 yunchen Exp $
 */
public class UpdateSessionContextRequestModel {
    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 需要更新的内容
     */
    private List<SingleSessionContextRequest> updateItems;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<SingleSessionContextRequest> getUpdateItems() {
        return updateItems;
    }

    public void setUpdateItems(List<SingleSessionContextRequest> updateItems) {
        this.updateItems = updateItems;
    }


    public static class SingleSessionContextRequest{
        /**
         * the key of the context
         */
        private String key;
        /**
         * the value of the context
         */
        private Object value;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public Object getValue() {
            return value;
        }

        public void setValue(Object value) {
            this.value = value;
        }
    }
}


