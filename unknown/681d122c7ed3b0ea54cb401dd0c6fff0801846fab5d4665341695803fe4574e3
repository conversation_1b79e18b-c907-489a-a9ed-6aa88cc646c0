package com.alipay.codegencore.model.model;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.model
 * @CreateTime : 2024-02-22
 */
public class ModelCurrentAvailableInfo {
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 模型描述
     */
    private String modelDescription;

    /**
     * 模型可见环境
     */
    private Integer visableEnv;
    /**
     * 模型健康检查详细信息
     */
    private ModelHealthCheck health;
    /**
     * 模型部署服务器信息
     */
    private ModelAvailableServer deployInfo;

    public Integer getVisableEnv() {
        return visableEnv;
    }

    public void setVisableEnv(Integer visableEnv) {
        this.visableEnv = visableEnv;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelDescription() {
        return modelDescription;
    }

    public void setModelDescription(String modelDescription) {
        this.modelDescription = modelDescription;
    }

    public ModelHealthCheck getHealth() {
        return health;
    }

    public void setHealth(ModelHealthCheck health) {
        this.health = health;
    }

    public ModelAvailableServer getDeployInfo() {
        return deployInfo;
    }

    public void setDeployInfo(ModelAvailableServer deployInfo) {
        this.deployInfo = deployInfo;
    }
}
