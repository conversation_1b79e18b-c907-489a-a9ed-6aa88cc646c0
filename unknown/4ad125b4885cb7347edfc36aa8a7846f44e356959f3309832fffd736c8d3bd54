package com.alipay.codegencore.model.model.analysis;

import java.util.List;

/**
 * 代码模型-方法
 *
 * <AUTHOR>
 * 创建时间 2022-08-09
 */
public class MethodBodyModel {
    /**
     * 方法名
     */
    private String name;
    /**
     * 返回值名
     */
    private String returnTypeName;
    /**
     * 方法参数
     */
    private List<MethodParamModel> paramList;
    /**
     * 方法体内变量列表（仅在没写完方法时 + TEMP扫描时做统计)
     */
    private List<VariableModel> variableList;
    /**
     * 方法体内容（仅在没写完方法时 + TEMP扫描时做统计)
     */
    private String content;



    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<VariableModel> getVariableList() {
        return variableList;
    }

    public void setVariableList(List<VariableModel> variableList) {
        this.variableList = variableList;
    }

    public List<MethodParamModel> getParamList() {
        return paramList;
    }

    public void setParamList(List<MethodParamModel> paramList) {
        this.paramList = paramList;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getReturnTypeName() {
        return returnTypeName;
    }

    public void setReturnTypeName(String returnTypeName) {
        this.returnTypeName = returnTypeName;
    }

}
