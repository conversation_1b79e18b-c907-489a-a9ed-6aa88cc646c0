package com.alipay.codegencore.dal.mapper;

import java.util.List;

import com.alipay.codegencore.dal.example.UserSceneRecordsDOExample;
import com.alipay.codegencore.model.domain.UserSceneRecordsDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface UserSceneRecordsDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    long countByExample(UserSceneRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    int deleteByExample(UserSceneRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    @Delete({
            "delete from cg_user_scene_records",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    @Insert({
            "insert into cg_user_scene_records (gmt_create, gmt_modified, ",
            "user_id, scene_id, control_type, ",
            "deleted)",
            "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
            "#{userId,jdbcType=BIGINT}, #{sceneId,jdbcType=BIGINT}, #{controlType,jdbcType=INTEGER}, ",
            "#{deleted,jdbcType=TINYINT})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(UserSceneRecordsDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    int insertSelective(UserSceneRecordsDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    List<UserSceneRecordsDO> selectByExample(UserSceneRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    @Select({
            "select",
            "id, gmt_create, gmt_modified, user_id, scene_id, control_type, deleted",
            "from cg_user_scene_records",
            "where id = #{id,jdbcType=BIGINT}"
    })
    UserSceneRecordsDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    int updateByExampleSelective(@Param("record") UserSceneRecordsDO record, @Param("example") UserSceneRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    int updateByExample(@Param("record") UserSceneRecordsDO record, @Param("example") UserSceneRecordsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    int updateByPrimaryKeySelective(UserSceneRecordsDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_user_scene_records
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    @Update({
            "update cg_user_scene_records",
            "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
            "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
            "user_id = #{userId,jdbcType=BIGINT},",
            "scene_id = #{sceneId,jdbcType=BIGINT},",
            "control_type = #{controlType,jdbcType=INTEGER},",
            "deleted = #{deleted,jdbcType=TINYINT}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(UserSceneRecordsDO record);
}