package com.alipay.codegencore.model.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageInfo;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:37
 */
public class TaskObject {

    @JSONField(name = "id")
    private String id;

    @JSONField(name = "created_at")
    private Long createdAt;

    @JSONField(name = "chunk_created_at")
    private Long chunkCreatedAt;

    @JSONField(name = "session_id")
    private String sessionId;

    @JSONField(name = "agent_id")
    private String agentId;

    @JSONField(name = "status", serializeUsing = EnumSerializer.class)
    private RemoteAgentStatus status;

    @JSONField(name = "metadata")
    private Map<String, Object> metadata;

    @JSONField(name = "in_progress_detail")
    private InProgressDetail inProgressDetail;

    @JSONField(name = "message_delta")
    private MessageDelta messageDelta;

    @JSONField(name = "last_error")
    private String lastError;

    @JSONField(name = "required_action")
    private RequiredAction requiredAction;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getChunkCreatedAt() {
        return chunkCreatedAt;
    }

    public void setChunkCreatedAt(Long chunkCreatedAt) {
        this.chunkCreatedAt = chunkCreatedAt;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public RemoteAgentStatus getStatus() {
        return status;
    }

    public void setStatus(RemoteAgentStatus status) {
        this.status = status;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public InProgressDetail getInProgressDetail() {
        return inProgressDetail;
    }

    public void setInProgressDetail(InProgressDetail inProgressDetail) {
        this.inProgressDetail = inProgressDetail;
    }

    public MessageDelta getMessageDelta() {
        return messageDelta;
    }

    public void setMessageDelta(MessageDelta messageDelta) {
        this.messageDelta = messageDelta;
    }

    public String getLastError() {
        return lastError;
    }

    public void setLastError(String lastError) {
        this.lastError = lastError;
    }

    public RequiredAction getRequiredAction() {
        return requiredAction;
    }

    public void setRequiredAction(RequiredAction requiredAction) {
        this.requiredAction = requiredAction;
    }

    /**
     * 消息转换
     * @param response
     * @param sessionId
     * @param sceneId
     * @return
     */
    public static TaskObject of(NewPluginStreamPartResponse response, String sessionId, Long sceneId) {
        TaskObject taskObject = new TaskObject();
        taskObject.setId(response.getId());
        long createAt = System.currentTimeMillis();
        taskObject.setCreatedAt(createAt);
        taskObject.setChunkCreatedAt(createAt);
        taskObject.setSessionId(sessionId);
        if (sceneId != null) {
            taskObject.setAgentId("" + sceneId);
        }
        taskObject.setStatus(RemoteAgentStatus.IN_PROGRESS);
        InProgressDetail inProgressDetail = new InProgressDetail();
        StageInfo stageInfo = response.getStageInfo();
        if (stageInfo != null) {
            inProgressDetail.setInput(JSON.toJSONString(stageInfo.getInput()));
            inProgressDetail.setOutput(JSON.toJSONString(stageInfo.getOutput()));
        }
        if (StageTypeEnum.ANSWER.getName().equals(response.getType())) {
            inProgressDetail.setType("llm");
        } else {
            inProgressDetail.setType("tool");
        }

        final String finishReason = response.getFinishReason();
        inProgressDetail.setFinish(StringUtils.isNotBlank(finishReason));
        taskObject.setInProgressDetail(inProgressDetail);
        MessageDelta messageDelta = new MessageDelta();
        MessageContent messageContent = new MessageContent();
        messageContent.setIndex(0);
        messageContent.setValue(response.getContent());
        messageContent.setType(MessageContent.MessageType.TEXT);
        messageDelta.setContent(messageContent);
        taskObject.setMessageDelta(messageDelta);

        if ("SUCCESS".equals(finishReason)) {
            if (StageTypeEnum.ANSWER.getName().equals(response.getType())) {
                taskObject.setStatus(RemoteAgentStatus.COMPLETED);
            }
        } else if ("PAUSE".equals(finishReason)) {
            taskObject.setStatus(RemoteAgentStatus.REQUIRES_ACTION);
            RequiredAction requiredAction = new RequiredAction();
            requiredAction.setType(RequiredAction.RequiredActionType.ADD_MESSAGE);
            requiredAction.setReason(taskObject.getInProgressDetail().getInput());
            taskObject.setRequiredAction(requiredAction);
            taskObject.setInProgressDetail(null);
            taskObject.setMessageDelta(null);
        } else {
            if (StringUtils.isNotBlank(finishReason)) {
                taskObject.setStatus(RemoteAgentStatus.FAILED);
                taskObject.setLastError(finishReason);
            }
        }
        return taskObject;
    }

}
