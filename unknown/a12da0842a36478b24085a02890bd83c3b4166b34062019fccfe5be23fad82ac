package com.alipay.codegencore.utils.codescan;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ClassTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.analysis.*;
import com.alipay.codegencore.utils.thread.ContextUtil;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ErrorNode;
import org.antlr.v4.runtime.tree.ParseTree;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 静态分析观察者
 * 根据
 *
 * <AUTHOR>
 * 创建时间 2022-08-01
 */
public class SimpleJavaParserVisitor extends JavaParserBaseVisitor<AbstractCodeAnalysisResult> {

    /**
     * 线程变量key-扫描类型
     */
    private static final String CURRENT_SCAN_TYPE_KEY = "SCAN_TYPE";
    /**
     * 线程变量key-结果builder对象
     */
    private static final String CURRENT_SCAN_BUILD_KEY = "SCAN_BUILD";
    /**
     * 线程变量key-是否分析参数开关
     */
    private static final String IS_COLLECT_PARAM_KEY = "COLLECT_PARAM";
    /**
     * 线程变量key-是否收集方法体内变量
     */
    private static final String IS_COLLECT_METHOD_VAR_KEY = "IS_COLLECT_METHOD_VAR_KEY";

    /**
     * 扫描入口
     * 1.根据不同的类型，初始化不同的结果对象。将类型信息和结果对象写入当前请求线程上下文
     * 2.执行{@link JavaParserBaseVisitor#visit(ParseTree)}，将结果对象传递观察者各个节点
     * 3.返回结果对象，并清理线程上下文
     *
     * @param tree
     * @param scanTypeEnum
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visit(ParseTree tree, ScanTypeEnum scanTypeEnum) {

        try {
            ContextUtil.set(CURRENT_SCAN_TYPE_KEY, scanTypeEnum);
            CodeAnalysisBuilder builder = new CodeAnalysisBuilder();
            ContextUtil.set(CURRENT_SCAN_BUILD_KEY, builder);
            super.visit(tree);
            return builder.buildResult(scanTypeEnum);
        } catch (Exception e) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, e);
        } finally {
            ContextUtil.remove();
        }
    }


    /**
     * 解析packageName
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitPackageDeclaration(JavaParser.PackageDeclarationContext ctx) {
        List<ParseTree> children = ctx.children;
        if (children == null || children.size() != 3) {
            return null;
        }
        String packageName = children.get(1).getText();

        CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);
        builder.packageName(packageName);
        ContextUtil.set(CURRENT_SCAN_BUILD_KEY, builder);
        return super.visitPackageDeclaration(ctx);
    }

    /**
     * 解析注解
     * //todo 注解暂时不深度解析，需要想清除element属于field还是method
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitAnnotationTypeDeclaration(JavaParser.AnnotationTypeDeclarationContext ctx) {

        if (extractClass(ctx.children, ClassTypeEnum.ANNOTATION)) {
            return null;
        }

        return super.visitAnnotationTypeDeclaration(ctx);
    }

    /**
     * 解析接口
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitInterfaceDeclaration(JavaParser.InterfaceDeclarationContext ctx) {

        if (extractClass(ctx.children, ClassTypeEnum.INTERFACE)) {
            return null;
        }

        return super.visitInterfaceDeclaration(ctx);
    }

    /**
     * 解析接口定义
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitInterfaceCommonBodyDeclaration(JavaParser.InterfaceCommonBodyDeclarationContext ctx) {
//        return super.visitInterfaceCommonBodyDeclaration(ctx);

        try {
            ParserRuleContext parent = ctx.getParent().getParent().getParent().getParent();
            if (parent instanceof JavaParser.ClassCreatorRestContext) {
                ContextUtil.set(IS_COLLECT_PARAM_KEY, false);
                ContextUtil.set(IS_COLLECT_METHOD_VAR_KEY, false);
                return super.visitInterfaceCommonBodyDeclaration(ctx);
            }
            String className = getTargetClassNameByParentNode(parent.getParent());
            //如果没找到class信息，不进行解析
            if (className == null) {
                return super.visitInterfaceCommonBodyDeclaration(ctx);
            }

            List<ParseTree> children = ctx.children;
            if (children == null || children.size() == 0) {
                return super.visitInterfaceCommonBodyDeclaration(ctx);
            }
            MethodBodyModel methodBodyModel = new MethodBodyModel();
            for (ParseTree parseTree : children) {
                if (parseTree instanceof JavaParser.TypeTypeOrVoidContext) {
                    methodBodyModel.setReturnTypeName(parseTree.getText());
                    continue;
                }
                if (parseTree instanceof JavaParser.IdentifierContext) {
                    methodBodyModel.setName(parseTree.getText());
                    continue;
                }
                if (parseTree instanceof JavaParser.FormalParametersContext) {
                    ContextUtil.set(IS_COLLECT_PARAM_KEY, true);
                }
                if (parseTree instanceof JavaParser.MethodBodyContext) {
                    String startText = ((JavaParser.MethodBodyContext) parseTree).start.getText();
                    String stopText = ((JavaParser.MethodBodyContext) parseTree).stop.getText();
                    //如果start和stop都是{，说明没有内容。直接跳过
                    if ("{".equals(startText) && "{".equals(stopText)) {
                        continue;
                    }
                    if (!("{".equals(startText) && "}".equals(stopText))) {
                        ContextUtil.set(IS_COLLECT_METHOD_VAR_KEY, true);
                        String content = parseTree.getText();
                        int eofIndex = content.lastIndexOf("<EOF>");
                        if (eofIndex >= 0) {
                            content = content.substring(0, eofIndex);
                        }
                        methodBodyModel.setContent(content);
                    }

                }
            }
            if (methodBodyModel.getName() != null && methodBodyModel.getReturnTypeName() != null) {
                CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);
                builder.method(methodBodyModel, className);
            }
            return super.visitInterfaceCommonBodyDeclaration(ctx);
        } finally {
            ContextUtil.set(IS_COLLECT_PARAM_KEY, false);
            ContextUtil.set(IS_COLLECT_METHOD_VAR_KEY, false);
        }

    }

    /**
     * 解析class/实现类/父类相关信息
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitClassDeclaration(JavaParser.ClassDeclarationContext ctx) {
        if (extractClass(ctx.children, ClassTypeEnum.CLASS)) {
            return null;
        }
        return super.visitClassDeclaration(ctx);
    }

    /**
     * 解析枚举类型
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitEnumDeclaration(JavaParser.EnumDeclarationContext ctx) {
        if (extractClass(ctx.children, ClassTypeEnum.ENUM)) {
            return null;
        }

        return super.visitEnumDeclaration(ctx);
    }

    /**
     * 解析枚举常量
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitEnumConstant(JavaParser.EnumConstantContext ctx) {
        List<ParseTree> children = ctx.children;
        if (children == null || children.size() == 0) {
            return super.visitEnumConstant(ctx);
        }

        ParserRuleContext parent = ctx.getParent().getParent();
        String className = getTargetClassNameByParentNode(parent);
        //如果没找到class信息，不进行解析
        if (className == null) {
            return super.visitEnumConstant(ctx);
        }

        FieldBodyModel fieldBodyModel = new FieldBodyModel();
        fieldBodyModel.setType(className);
        for (ParseTree parseTree : children) {
            if (parseTree instanceof JavaParser.IdentifierContext) {
                fieldBodyModel.setName(parseTree.getText());
            }
        }
        if (fieldBodyModel.getName() != null && fieldBodyModel.getType() != null) {
            CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);
            builder.field(fieldBodyModel, className);
        }

        return super.visitEnumConstant(ctx);
    }


    /**
     * 解析类常量
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitConstDeclaration(JavaParser.ConstDeclarationContext ctx) {
        List<ParseTree> children = ctx.children;
        if (children == null || children.size() == 0) {
            return super.visitConstDeclaration(ctx);
        }
        ParserRuleContext parent = ctx.getParent().getParent().getParent().getParent();
        String className = getTargetClassNameByParentNode(parent);
        //如果没找到class信息，不进行解析
        if (className == null) {
            return super.visitConstDeclaration(ctx);
        }

        FieldBodyModel fieldBodyModel = new FieldBodyModel();
        for (ParseTree parseTree : children) {
            if (parseTree instanceof JavaParser.TypeTypeContext) {
                fieldBodyModel.setType(parseTree.getText());
                continue;
            }
            if (parseTree instanceof JavaParser.VariableDeclaratorsContext) {
                String variableFromVariableDeclarators = getVariableFromVariableDeclarators((JavaParser.VariableDeclaratorsContext) parseTree);
                fieldBodyModel.setName(variableFromVariableDeclarators);
                continue;
            }
            if (parseTree instanceof JavaParser.ConstantDeclaratorContext) {
                List<ParseTree> constantChildList = ((JavaParser.ConstantDeclaratorContext) parseTree).children;
                if (constantChildList == null || constantChildList.size() == 0) {
                    continue;
                }
                for (ParseTree constantChild : constantChildList) {
                    if (constantChild instanceof JavaParser.IdentifierContext) {
                        fieldBodyModel.setName(constantChild.getText());
                    }
                }
            }
        }
        if (fieldBodyModel.getName() != null && fieldBodyModel.getType() != null) {
            CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);
            builder.field(fieldBodyModel, className);
        }

        return super.visitConstDeclaration(ctx);
    }

    /**
     * 解析类字段
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitFieldDeclaration(JavaParser.FieldDeclarationContext ctx) {
        List<ParseTree> children = ctx.children;
        if (children == null || children.size() == 0) {
            return super.visitFieldDeclaration(ctx);
        }

        ParserRuleContext parent = ctx.getParent().getParent().getParent().getParent();
        String className = getTargetClassNameByParentNode(parent);
        //如果没找到class信息，不进行解析
        if (className == null) {
            return super.visitFieldDeclaration(ctx);
        }

        FieldBodyModel fieldBodyModel = new FieldBodyModel();
        for (ParseTree parseTree : children) {
            if (parseTree instanceof JavaParser.TypeTypeContext) {
                fieldBodyModel.setType(parseTree.getText().replaceAll(AppConstants.INVALID_TOKEN_STR,""));
                continue;
            }
            if (parseTree instanceof JavaParser.VariableDeclaratorsContext) {
                String variableFromVariableDeclarators = getVariableFromVariableDeclarators((JavaParser.VariableDeclaratorsContext) parseTree);
                fieldBodyModel.setName(variableFromVariableDeclarators);
            }
        }
        if (fieldBodyModel.getName() != null && fieldBodyModel.getType() != null) {
            CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);
            builder.field(fieldBodyModel, className);
        }

        return super.visitFieldDeclaration(ctx);
    }

    /**
     * 解析方法体内所有变量
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitLocalVariableDeclaration(JavaParser.LocalVariableDeclarationContext ctx) {
        Boolean isCollectVar = ContextUtil.getOrDefault(IS_COLLECT_METHOD_VAR_KEY, Boolean.class, false);
        ScanTypeEnum scanTypeEnum = ContextUtil.get(CURRENT_SCAN_TYPE_KEY, ScanTypeEnum.class);
        if (!isCollectVar || scanTypeEnum == ScanTypeEnum.SCAN_FILE) {
            return super.visitLocalVariableDeclaration(ctx);
        }

        List<ParseTree> children = ctx.children;
        if (children == null || children.size() == 0) {
            return super.visitLocalVariableDeclaration(ctx);
        }
        VariableModel variableModel = new VariableModel();
        for (ParseTree parseTree : children) {
            if (parseTree instanceof JavaParser.TypeTypeContext) {
                variableModel.setType(parseTree.getText().replaceAll(AppConstants.INVALID_TOKEN_STR,""));
            }
            if (parseTree instanceof JavaParser.VariableDeclaratorsContext) {
                String variableFromVariableDeclarators = getVariableFromVariableDeclarators((JavaParser.VariableDeclaratorsContext) parseTree);
                variableModel.setName(variableFromVariableDeclarators);
            }
        }
        if (variableModel.getName() != null && variableModel.getType() != null) {
            CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);
//            List<MethodBodyModel> methodList = builder.methodList;
            MethodBodyModel methodBodyModel = builder.getLastMethod();
            if (methodBodyModel == null) {
                return super.visitLocalVariableDeclaration(ctx);
            }

            List<VariableModel> variableList = methodBodyModel.getVariableList();
            if (variableList == null) {
                variableList = new LinkedList<>();
            }
            variableList.add(variableModel);
            methodBodyModel.setVariableList(variableList);
        }

        return super.visitLocalVariableDeclaration(ctx);
    }


    /**
     * 分析method信息
     * 如果parent是{@link com.alipay.codegencore.service.utils.codescan.JavaParser.ClassCreatorRestContext},说明是匿名函数，直接跳过
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitMethodDeclaration(JavaParser.MethodDeclarationContext ctx) {
        try {
            ParserRuleContext parent = ctx.getParent().getParent().getParent().getParent();
            if (parent instanceof JavaParser.ClassCreatorRestContext) {
                ContextUtil.set(IS_COLLECT_PARAM_KEY, false);
                ContextUtil.set(IS_COLLECT_METHOD_VAR_KEY, false);
                return super.visitMethodDeclaration(ctx);
            }
            if (parent instanceof JavaParser.ClassBodyContext) {
                parent = parent.getParent();
            }
            String className = getTargetClassNameByParentNode(parent);
            //如果没找到class信息，不进行解析
            if (className == null) {
                return super.visitMethodDeclaration(ctx);
            }

            List<ParseTree> children = ctx.children;
            if (children == null || children.size() == 0) {
                return super.visitMethodDeclaration(ctx);
            }
            MethodBodyModel methodBodyModel = new MethodBodyModel();
            for (ParseTree parseTree : children) {
                if (parseTree instanceof JavaParser.TypeTypeOrVoidContext) {
                    methodBodyModel.setReturnTypeName(parseTree.getText().replaceAll(AppConstants.INVALID_TOKEN_STR,""));
                    continue;
                }
                if (parseTree instanceof JavaParser.IdentifierContext) {
                    methodBodyModel.setName(parseTree.getText());
                    continue;
                }
                if (parseTree instanceof JavaParser.FormalParametersContext) {
                    ContextUtil.set(IS_COLLECT_PARAM_KEY, true);
                }
                if (parseTree instanceof JavaParser.MethodBodyContext) {
                    String startText = ((JavaParser.MethodBodyContext) parseTree).start.getText();
                    String stopText = ((JavaParser.MethodBodyContext) parseTree).stop.getText();
                    //如果start和stop都是{，说明没有内容。直接跳过
                    if ("{".equals(startText) && "{".equals(stopText)) {
                        continue;
                    }
                    if (!("{".equals(startText) && "}".equals(stopText))) {
                        ContextUtil.set(IS_COLLECT_METHOD_VAR_KEY, true);
                        String content = parseTree.getText();
                        int eofIndex = content.lastIndexOf("<EOF>");
                        if (eofIndex >= 0) {
                            content = content.substring(0, eofIndex);
                        }
                        methodBodyModel.setContent(content);
                    }

                }
            }
            if (methodBodyModel.getName() != null && methodBodyModel.getReturnTypeName() != null) {
                CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);
                builder.method(methodBodyModel, className);
            }
            return super.visitMethodDeclaration(ctx);
        } finally {
            ContextUtil.set(IS_COLLECT_PARAM_KEY, false);
            ContextUtil.set(IS_COLLECT_METHOD_VAR_KEY, false);
        }
    }

    /**
     * 分析构造方法
     * //todo 暂时先不处理参数解析
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitConstructorDeclaration(JavaParser.ConstructorDeclarationContext ctx) {
        ContextUtil.set(IS_COLLECT_PARAM_KEY, false);
        return super.visitConstructorDeclaration(ctx);
    }

    /**
     * 收集方法参数
     *
     * @param ctx the parse tree
     * @return
     */
    @Override
    public AbstractCodeAnalysisResult visitFormalParameter(JavaParser.FormalParameterContext ctx) {
        Boolean isCollectParam = ContextUtil.getOrDefault(IS_COLLECT_PARAM_KEY, Boolean.class, false);

        CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);
        MethodBodyModel methodBodyModel = builder.getLastMethod();

        List<ParseTree> children = ctx.children;
        if (!isCollectParam || children == null || children.size() == 0 || methodBodyModel == null) {
            return super.visitFormalParameter(ctx);
        }

        MethodParamModel methodParamModel = new MethodParamModel();
        for (ParseTree parseTree : children) {
            if (parseTree instanceof JavaParser.TypeTypeContext) {
                methodParamModel.setType(parseTree.getText());
            }
            if (parseTree instanceof JavaParser.VariableDeclaratorIdContext) {
                methodParamModel.setName(parseTree.getText());
            }
        }
        if (methodParamModel.getName() != null && methodParamModel.getType() != null) {
            List<MethodParamModel> paramList = methodBodyModel.getParamList();
            if (paramList == null) {
                paramList = new LinkedList<>();
            }
            paramList.add(methodParamModel);
            methodBodyModel.setParamList(paramList);
        }
        return super.visitFormalParameter(ctx);
    }

    /**
     * 从父节点中找到目标className
     *
     * @param parent
     * @return
     */
    private String getTargetClassNameByParentNode(ParserRuleContext parent) {
        String className = null;
        if (parent instanceof JavaParser.ClassDeclarationContext) {
            //进入到方法分析了，那class信息肯定有，找到1位置(Identifier)，获取class名
            className = parent.getChild(1).getText();
        } else if (parent instanceof JavaParser.EnumDeclarationContext) {
            className = parent.getChild(1).getText();
        } else if (parent instanceof JavaParser.InterfaceDeclarationContext) {
            className = parent.getChild(1).getText();
        } else if (parent instanceof JavaParser.EnumDeclarationContext) {
            className = parent.getChild(1).getText();
        }
        return className;
    }

    /**
     * 抽取class主体信息
     *
     * @param children
     * @param classTypeEnum
     * @return
     */

    private boolean extractClass(List<ParseTree> children, ClassTypeEnum classTypeEnum) {
        CodeAnalysisBuilder builder = ContextUtil.get(CURRENT_SCAN_BUILD_KEY, CodeAnalysisBuilder.class);

        if (children == null || children.size() == 0) {
            return true;
        }
        String className = null;
        for (ParseTree child : children) {
            if (child instanceof JavaParser.IdentifierContext) {
                builder.className(child.getText(), classTypeEnum);
                className = child.getText();
                continue;
            }
            //注意：此处如果是interface类型，extends时候，会是TypeListContext类型，和预期不符，但不影响使用
            if (child instanceof JavaParser.TypeTypeContext) {
                builder.extendClassName(child.getText(), className);
                continue;
            }
            if (child instanceof JavaParser.TypeListContext) {
                builder.implementClassNameArr(getImplementArr((JavaParser.TypeListContext) child), className);
            }
        }
        return false;
    }


    @Override
    public AbstractCodeAnalysisResult visitErrorNode(ErrorNode node) {
        return super.visitErrorNode(node);
    }


    /**
     * 从{@link com.alipay.codegencore.service.utils.codescan.JavaParser.VariableDeclaratorsContext}中分析赋值的变量名
     *
     * @param parseTree
     * @return
     */
    private String getVariableFromVariableDeclarators(JavaParser.VariableDeclaratorsContext parseTree) {
        List<ParseTree> variableDeclaratorContextList = parseTree.children;
        if (variableDeclaratorContextList == null || variableDeclaratorContextList.size() == 0) {
            return null;
        }
        for (ParseTree variable : variableDeclaratorContextList) {
            if (variable instanceof JavaParser.VariableDeclaratorContext) {
                return getVariableTarget((JavaParser.VariableDeclaratorContext) variable);
            }
        }
        return null;
    }

    /**
     * 从{@link com.alipay.codegencore.service.utils.codescan.JavaParser.VariableDeclaratorContext}中分析赋值的变量名
     *
     * @param variableDeclaratorContext
     * @return
     */
    private String getVariableTarget(JavaParser.VariableDeclaratorContext variableDeclaratorContext) {
        List<ParseTree> children = variableDeclaratorContext.children;
        if (children == null || children.size() == 0) {
            return null;
        }
        for (ParseTree parseTree : children) {
            if (parseTree instanceof JavaParser.VariableDeclaratorIdContext) {
                return parseTree.getText();
            }
        }
        return null;
    }


    /**
     * 从{@link com.alipay.codegencore.service.utils.codescan.JavaParser.TypeListContext} 中获取实现接口列表
     *
     * @param ctx
     * @return
     */
    private String[] getImplementArr(JavaParser.TypeListContext ctx) {
        List<ParseTree> children = ctx.children;
        if (children == null || children.size() == 0) {
            return null;
        }
        List<String> implementsNameList = new LinkedList<>();
        for (ParseTree parseTree : children) {
            if (parseTree instanceof JavaParser.TypeTypeContext) {
                implementsNameList.add(parseTree.getText());
            }
        }
        if (implementsNameList.size() == 0) {
            return null;
        }
        return implementsNameList.toArray(new String[implementsNameList.size()]);
    }


    /**
     * 代码扫描结果集构建工厂
     */
    private static class CodeAnalysisBuilder {
        private String packageName;
//        private String className;
//
//        private String extendClassName;
//
//        private String[] implementClassNameArr;
//
//        private List<FieldBodyModel> fieldList;
//        private List<MethodBodyModel> methodList;


        private AbstractCodeAnalysisResult currentClass;
        private List<AbstractCodeAnalysisResult> innerClassList;

        public MethodBodyModel getLastMethod() {
            AbstractCodeAnalysisResult lastClass = currentClass;
            if (innerClassList != null) {
                lastClass = innerClassList.get(innerClassList.size() - 1);
            }
            List<MethodBodyModel> methodList = lastClass.getMethodList();
            if (methodList == null || methodList.size() == 0) {
                return null;
            }

            return methodList.get(methodList.size() - 1);
        }

        /**
         * 设置packageName
         *
         * @param packageName
         * @return
         */
        public CodeAnalysisBuilder packageName(String packageName) {
            this.packageName = packageName;
            return this;
        }

        /**
         * 设置className
         *
         * @param className
         * @return
         */
        public CodeAnalysisBuilder className(String className, ClassTypeEnum classTypeEnum) {
            AbstractCodeAnalysisResult classConstruct = new AbstractCodeAnalysisResult();
            classConstruct.setClassName(className);
            classConstruct.setPackageName(packageName);
            classConstruct.setClassType(classTypeEnum);
            if (currentClass == null) {
                currentClass = classConstruct;
                return this;
            }
            if (innerClassList == null) {
                innerClassList = new LinkedList<>();
            }
            innerClassList.add(classConstruct);
            currentClass.setInnerClassList(innerClassList);
            return this;
        }

        /**
         * 设置extendClassName
         *
         * @param extendClassName
         * @return
         */
        public CodeAnalysisBuilder extendClassName(String extendClassName, String className) {
            AbstractCodeAnalysisResult constructByName = getClassConstructByName(currentClass, className);
            constructByName.setExtendClassName(extendClassName);
            return this;
        }

        /**
         * 设置implementClass数组
         *
         * @param implementClassNameArr
         * @return
         */
        public CodeAnalysisBuilder implementClassNameArr(String[] implementClassNameArr, String className) {
            AbstractCodeAnalysisResult constructByName = getClassConstructByName(currentClass, className);
            constructByName.setImplementClassNameArr(implementClassNameArr);
            return this;
        }

        /**
         * 添加类字段信息
         *
         * @param fieldBodyModel
         * @param className      哪个class下的字段
         * @return
         */
        public CodeAnalysisBuilder field(FieldBodyModel fieldBodyModel, String className) {
            AbstractCodeAnalysisResult constructByName = getClassConstructByName(currentClass, className);
            List<FieldBodyModel> fieldList = constructByName.getFieldList();
            if (fieldList == null) {
                fieldList = new LinkedList<>();
            }
            fieldList.add(fieldBodyModel);
            constructByName.setFieldList(fieldList);
            return this;
        }

        /**
         * 添加方法信息
         *
         * @param methodBodyModel
         * @param className       哪个class下的方法
         * @return
         */
        public CodeAnalysisBuilder method(MethodBodyModel methodBodyModel, String className) {
            AbstractCodeAnalysisResult constructByName = getClassConstructByName(currentClass, className);
            List<MethodBodyModel> methodList = constructByName.getMethodList();
            if (methodList == null) {
                methodList = new LinkedList<>();
            }
            methodList.add(methodBodyModel);
            constructByName.setMethodList(methodList);
            return this;
        }

        /**
         * 根据className，找到class对象
         *
         * @return
         */
        private AbstractCodeAnalysisResult getClassConstructByName(AbstractCodeAnalysisResult currentClassConstruct, String className) {
            if (className.equals(currentClassConstruct.getClassName())) {
                return currentClassConstruct;
            }
            List<AbstractCodeAnalysisResult> childrenClassList = currentClassConstruct.getInnerClassList();
            if (childrenClassList != null && childrenClassList.size() > 0) {
                for (AbstractCodeAnalysisResult childClass : childrenClassList) {
                    //防止有多层嵌套，用递归去找子类
                    AbstractCodeAnalysisResult abstractCodeAnalysisResult = getClassConstructByName(childClass, className);
                    if (abstractCodeAnalysisResult != null) {
                        return abstractCodeAnalysisResult;
                    }
                }
            }
            return null;
        }


        /**
         * 根据不同的扫描类型，构建结果
         * 目前所有字段/方法，全部收拢在最外层class
         *
         * @param scanTypeEnum
         * @return
         */
        public AbstractCodeAnalysisResult buildResult(ScanTypeEnum scanTypeEnum) {
            AbstractCodeAnalysisResult result = null;
            if (currentClass == null) {
                return result;
            }
            switch (scanTypeEnum) {
                case TEMP_REQUEST:
                    result = buildTempConstructResult();
                    break;
                case SCAN_FILE:
                    result = buildCodeConstructResult();
                    break;
                case ALL:
                    result = buildAllConstructResult();
                    break;
                default:
            }
            return result;
        }

        /**
         * 构建混合结果
         *
         * @return
         */
        private CodeAndTempStructResult buildAllConstructResult() {
            CodeAndTempStructResult codeAndTempStructResult = new CodeAndTempStructResult();
            TempCodeAnalysisResultContext tempConstructResult = (TempCodeAnalysisResultContext) buildTempConstructResult();
            CodeStructResult codeAnalysisResult = (CodeStructResult) buildCodeConstructResult();
            codeAndTempStructResult.setCodeStructResult(codeAnalysisResult);
            codeAndTempStructResult.setTempCodeAnalysisResultContext(tempConstructResult);
            return codeAndTempStructResult;
        }

        /**
         * 构建{@link CodeStructResult}结果
         *
         * @return
         */
        private AbstractCodeAnalysisResult buildCodeConstructResult() {
            CodeStructResult result = new CodeStructResult();
            result.setPackageName(packageName);
            result.setClassName(currentClass.getClassName());
            result.setClassType(currentClass.getClassType());
            result.setExtendClassName(currentClass.getExtendClassName());
            result.setImplementClassNameArr(currentClass.getImplementClassNameArr());
            Set<String> fieldOrMethodSet = collectAllMethodAndFieldName(currentClass, new HashSet<>());
            if (fieldOrMethodSet.size() > 0) {
                result.setFieldOrMethodSet(fieldOrMethodSet);
            }
            return result;
        }

        /**
         * 构建{@link TempCodeAnalysisResultContext}结果
         *
         * @return
         */
        private AbstractCodeAnalysisResult buildTempConstructResult() {
            TempCodeAnalysisResultContext result = new TempCodeAnalysisResultContext();
            result.setPackageName(packageName);
            result.setClassName(currentClass.getClassName());
            result.setFieldReferenceMap(fieldToMap(currentClass, new HashMap<>()));
            result.setClassType(currentClass.getClassType());
            result.setExtendClassName(currentClass.getExtendClassName());
            result.setImplementClassNameArr(currentClass.getImplementClassNameArr());
            MethodBodyModel lastMethod = getLastMethod(currentClass);
            if (lastMethod == null) {
                return result;
            }
            result.setWritingMethodBodyModel(lastMethod);
            result.setLocalVariableMap(localVariableToMap(lastMethod));
            return result;
        }

        /**
         * 收集所有Method/FieldName信息
         *
         * @param currentClass
         * @param fieldOrMethodSet
         */
        private Set<String> collectAllMethodAndFieldName(AbstractCodeAnalysisResult currentClass, Set<String> fieldOrMethodSet) {
            List<AbstractCodeAnalysisResult> innerClassList = currentClass.getInnerClassList();
            if (innerClassList != null && innerClassList.size() > 0) {
                for (AbstractCodeAnalysisResult innerClass : innerClassList) {
                    fieldOrMethodSet = collectAllMethodAndFieldName(innerClass, fieldOrMethodSet);
                }
            }
            List<MethodBodyModel> methodList = currentClass.getMethodList();
            if (methodList != null && methodList.size() > 0) {
                for (MethodBodyModel methodBodyModel : methodList) {
                    fieldOrMethodSet.add(methodBodyModel.getName());
                }
            }
            List<FieldBodyModel> fieldList = currentClass.getFieldList();
            if (fieldList != null && fieldList.size() > 0) {
                for (FieldBodyModel fieldBodyModel : fieldList) {
                    fieldOrMethodSet.add(fieldBodyModel.getName());
                }
            }
            return fieldOrMethodSet;
        }


        /**
         * 获取最后一个方法
         * {@link TempCodeAnalysisResultContext }模型使用
         *
         * @param codeAnalysisResult
         * @return
         */
        private MethodBodyModel getLastMethod(AbstractCodeAnalysisResult codeAnalysisResult) {
            List<AbstractCodeAnalysisResult> innerClassList = codeAnalysisResult.getInnerClassList();
            if (innerClassList != null && innerClassList.size() > 0) {
                return getLastMethod(innerClassList.get(innerClassList.size() - 1));
            }
            List<MethodBodyModel> methodList = codeAnalysisResult.getMethodList();
            if (methodList == null || methodList.size() == 0) {
                return null;
            }
            return methodList.get(methodList.size() - 1);
        }

        /**
         * 将字段列表转换为map映射(包含内部类)
         * key：字段名
         * value：字段类型
         *
         * @param currentClass
         * @return
         */
        private Map<String, String> fieldToMap(AbstractCodeAnalysisResult currentClass, Map<String, String> result) {
            List<FieldBodyModel> fieldList = currentClass.getFieldList();
            if (fieldList == null || fieldList.size() == 0) {
                return result;
            }
            Map<String, String> collectDataMap = fieldList.stream().collect(Collectors.toMap(FieldBodyModel::getName, FieldBodyModel::getType));
            if (collectDataMap != null && collectDataMap.size() > 0) {
                result.putAll(collectDataMap);
            }
            List<AbstractCodeAnalysisResult> innerClassList = currentClass.getInnerClassList();
            if (innerClassList != null && innerClassList.size() > 0) {
                for (AbstractCodeAnalysisResult innerClass : innerClassList) {
                    fieldToMap(innerClass, result);
                }
            }
            return result;
        }

        /**
         * 将方法的variable列表转为map映射
         * key：字段名
         * value：字段类型
         *
         * @param methodBodyModel
         * @return
         */
        private Map<String, String> localVariableToMap(MethodBodyModel methodBodyModel) {

            List<VariableModel> variableList = methodBodyModel.getVariableList();
            List<MethodParamModel> paramList = methodBodyModel.getParamList();
            Map<String, String> result = new HashMap<>();
            if (variableList != null && variableList.size() > 0) {
                for (VariableModel variableModel : variableList) {
                    result.put(variableModel.getName(), variableModel.getType());
                }
            }
            if (paramList != null && paramList.size() > 0) {
                for (MethodParamModel paramModel : paramList) {
                    result.put(paramModel.getName(), paramModel.getType());
                }
            }
            return result.size() == 0 ? null : result;
        }
    }
}
