/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.List;

/**
 * 复合卡片配置信息
 * <AUTHOR>
 * @version $Id: ComplexCard.java, v 0.1 2022-11-14 10:37 LiYuYao Exp $$
 */
public class ComplexCard extends BeanStringSwitcherImpl {

    /**
     * 卡片名称
     */
    private String title;

    /**
     * 内容信息
     */
    private List<CardItem> items;

    /**
     * 结果信息
     */
    private List<ResultItem> results;

    /**
     * 按钮信息
     */
    private List<LinkItem> buttons;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<CardItem> getItems() {
        return items;
    }

    public void setItems(List<CardItem> items) {
        this.items = items;
    }

    public List<ResultItem> getResults() {
        return results;
    }

    public void setResults(List<ResultItem> results) {
        this.results = results;
    }

    public List<LinkItem> getButtons() {
        return buttons;
    }

    public void setButtons(List<LinkItem> buttons) {
        this.buttons = buttons;
    }
}