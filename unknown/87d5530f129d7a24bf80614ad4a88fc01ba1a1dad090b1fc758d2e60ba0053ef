package com.alipay.codegencore.service.impl;

import com.alipay.archdatacenter.facade.model.dto.app.backendapp.BackendAppDTO;
import com.alipay.archdatacenter.facade.model.request.app.backendapp.BackendAppSearchRequest;
import com.alipay.archdatacenter.facade.model.result.common.AdcResult;
import com.alipay.archdatacenter.facade.service.app.backendapp.BackendAppQueryFacade;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.sofa.rpc.api.annotation.RpcConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.03.11
 */
@Service
public class BackendAppQueryService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BackendAppQueryService.class);
    @RpcConsumer
    BackendAppQueryFacade backendAppQueryFacade;

    /**
     * 根据应用名模糊搜索应用名列表
     *
     * @param appName 应用名
     * @return BaseResponse<List < String>> 应用名列表
     */
    public BaseResponse<List<String>> searchAppName(String appName) {
        // 创建后台应用搜索请求对象
        BackendAppSearchRequest backendAppSearchRequest = new BackendAppSearchRequest();
        // 设置模糊应用名
        backendAppSearchRequest.setFuzzyAppName(appName);
        // 调用后台应用查询接口进行搜索
        AdcResult<List<String>> listAdcResult = backendAppQueryFacade.searchAppNames(backendAppSearchRequest);
        if (listAdcResult.isSuccess()) {
            // 打印日志，记录搜索成功的结果
            LOGGER.info("查询应用名成功，结果为：{}", listAdcResult.getResult());
            // 构建基础响应对象，将搜索结果封装到响应对象中并返回
            return BaseResponse.build(listAdcResult.getResult());
        } else {
            // 打印日志，记录搜索失败的原因
            LOGGER.info("查询应用名失败，原因：{}", listAdcResult.getErrorMessage());
            // 构建基础响应对象，将错误码和错误信息封装到响应对象中并返回
            return BaseResponse.build(ResponseEnum.ERROR_THROW, listAdcResult.getErrorMessage());
        }
    }


    /**
     * 根据应用名查询详情
     * @param appName
     * @return
     */
    public BaseResponse<BackendAppDTO> getAppDetail(String appName) {
        AdcResult<BackendAppDTO> backendAppDTOAdcResult = backendAppQueryFacade.getAppDetail(appName);
        if (backendAppDTOAdcResult.isSuccess()){
            LOGGER.info("查询应用详情成功，结果为: {}", backendAppDTOAdcResult.getResult());
            return BaseResponse.build(backendAppDTOAdcResult.getResult());
        }else{
            LOGGER.info("查询应用信息失败，原因：{}", backendAppDTOAdcResult.getErrorMessage());
            return BaseResponse.build(ResponseEnum.ERROR_THROW, backendAppDTOAdcResult.getErrorMessage());
        }
    }
}
