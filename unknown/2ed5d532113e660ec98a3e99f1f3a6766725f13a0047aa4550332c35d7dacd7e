package com.alipay.codegencore.model.domain;

import java.util.Date;

public class UserPluginRecordsDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_plugin_records.id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_plugin_records.gmt_create
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_plugin_records.gmt_modified
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_plugin_records.user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_plugin_records.plugin_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Long pluginId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_plugin_records.control_type
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Integer controlType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_plugin_records.deleted
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    private Byte deleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_plugin_records.id
     *
     * @return the value of cg_user_plugin_records.id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_plugin_records.id
     *
     * @param id the value for cg_user_plugin_records.id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_plugin_records.gmt_create
     *
     * @return the value of cg_user_plugin_records.gmt_create
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_plugin_records.gmt_create
     *
     * @param gmtCreate the value for cg_user_plugin_records.gmt_create
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_plugin_records.gmt_modified
     *
     * @return the value of cg_user_plugin_records.gmt_modified
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_plugin_records.gmt_modified
     *
     * @param gmtModified the value for cg_user_plugin_records.gmt_modified
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_plugin_records.user_id
     *
     * @return the value of cg_user_plugin_records.user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_plugin_records.user_id
     *
     * @param userId the value for cg_user_plugin_records.user_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_plugin_records.plugin_id
     *
     * @return the value of cg_user_plugin_records.plugin_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Long getPluginId() {
        return pluginId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_plugin_records.plugin_id
     *
     * @param pluginId the value for cg_user_plugin_records.plugin_id
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setPluginId(Long pluginId) {
        this.pluginId = pluginId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_plugin_records.control_type
     *
     * @return the value of cg_user_plugin_records.control_type
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Integer getControlType() {
        return controlType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_plugin_records.control_type
     *
     * @param controlType the value for cg_user_plugin_records.control_type
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setControlType(Integer controlType) {
        this.controlType = controlType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_plugin_records.deleted
     *
     * @return the value of cg_user_plugin_records.deleted
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Byte getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_plugin_records.deleted
     *
     * @param deleted the value for cg_user_plugin_records.deleted
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }
}