package com.alipay.codegencore.model.enums;

/**
 * 请求antDsr的bizCode和bizName
 */
public enum AntDsrBizCodeEnum {

    /**
     * 来自techplay的调用
     */
    TECH_PLAY("techplay","techplay"),
    /**
     * 来自codefuse的调用
     */
    CODE_FUSE("codefuse","codefuse"),
    /**
     * 来自其他api的调用
     */
    CODEGENCORE_OTHER("codegencore_other","codegencore_other"),
    ;

    private final String bizCode;
    private final String bizName;

    AntDsrBizCodeEnum(String bizCode, String bizName) {
        this.bizCode = bizCode;
        this.bizName = bizName;
    }

    public String getBizCode() {
        return bizCode;
    }

    public String getBizName() {
        return bizName;
    }
}
