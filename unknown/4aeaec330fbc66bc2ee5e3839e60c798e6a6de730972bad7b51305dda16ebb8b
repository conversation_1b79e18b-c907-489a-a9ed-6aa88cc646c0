package com.alipay.codegencore.utils;

import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class ListUtilsTest {

    @Test
    public void test(){
        List<String> list = Lists.newArrayList("1","2","3","4");
        List<String> retListNull = ListUtils.getPageList(list,2,5);
        Assert.assertEquals(0, retListNull.size());
        List<String> retList = ListUtils.getPageList(list,2,1);
        Assert.assertEquals(2, retList.size());
    }

}
