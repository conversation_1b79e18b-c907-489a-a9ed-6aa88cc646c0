/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.codescan;

import com.alipay.codegencore.utils.codescan.JavaParser;
import com.alipay.codegencore.utils.codescan.JavaParserBaseListener;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ErrorNode;
import org.antlr.v4.runtime.tree.TerminalNode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import static org.smartunit.shaded.org.mockito.Mockito.mock;
import static org.smartunit.shaded.org.mockito.Mockito.withSettings;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class JavaParserBaseListener_SSTest extends JavaParserBaseListener_SSTest_scaffolding {
// allCoveredLines:[12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72, 78, 84, 90, 96, 102, 108, 114, 120, 126, 132, 138, 144, 150, 156, 162, 168, 174, 180, 186, 192, 198, 204, 210, 216, 222, 228, 234, 240, 246, 252, 258, 264, 270, 276, 282, 288, 294, 300, 306, 312, 318, 324, 330, 336, 342, 348, 354, 360, 366, 372, 378, 384, 390, 396, 402, 408, 414, 420, 426, 432, 438, 444, 450, 456, 462, 468, 474, 480, 486, 492, 498, 504, 510, 516, 522, 528, 534, 540, 546, 552, 558, 564, 570, 576, 582, 588, 594, 600, 606, 612, 618, 624, 630, 636, 642, 648, 654, 660, 666, 672, 678, 684, 690, 696, 702, 708, 714, 720, 726, 732, 738, 744, 750, 756, 762, 768, 774, 780, 786, 792, 798, 804, 810, 816, 822, 828, 834, 840, 846, 852, 858, 864, 870, 876, 882, 888, 894, 900, 906, 912, 918, 924, 930, 936, 942, 948, 954, 960, 966, 972, 978, 984, 990, 996, 1002, 1008, 1014, 1020, 1026, 1032, 1038, 1044, 1050, 1056, 1062, 1068, 1074, 1080, 1086, 1092, 1098, 1104, 1110, 1116, 1122, 1128, 1134, 1140, 1146, 1152, 1158, 1164, 1170, 1176, 1182, 1188, 1194, 1200, 1206, 1212, 1218, 1224, 1230, 1236, 1242, 1248, 1254, 1260, 1266, 1272, 1278, 1284, 1290, 1296, 1302, 1308, 1314, 1320, 1326, 1332, 1338, 1344, 1350, 1356, 1362, 1368, 1374, 1380, 1386, 1392, 1398, 1404, 1410, 1416, 1422, 1428, 1434, 1440, 1446, 1452, 1458, 1464, 1470, 1476, 1482, 1488, 1494, 1500, 1507, 1513, 1519, 1525]

  @Test(timeout = 4000)
  public void test_enterAltAnnotationQualifiedName_000()  throws Throwable  {
      //caseID:5eba032ffdd31bff03b9124614cbceb2
      //CoveredLines: [12, 666]
      //Input_0_JavaParser.AltAnnotationQualifiedNameContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AltAnnotationQualifiedNameContext0
      JavaParser.AltAnnotationQualifiedNameContext javaParser_AltAnnotationQualifiedNameContext0 = mock(JavaParser.AltAnnotationQualifiedNameContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAltAnnotationQualifiedName
      javaParserBaseListener0.enterAltAnnotationQualifiedName(javaParser_AltAnnotationQualifiedNameContext0);
  }

  @Test(timeout = 4000)
  public void test_enterAnnotation_001()  throws Throwable  {
      //caseID:1191b326217e75086dccba9d7c697297
      //CoveredLines: [12, 678]
      //Input_0_JavaParser.AnnotationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationContext0
      JavaParser.AnnotationContext javaParser_AnnotationContext0 = mock(JavaParser.AnnotationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAnnotation
      javaParserBaseListener0.enterAnnotation(javaParser_AnnotationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterAnnotationConstantRest_002()  throws Throwable  {
      //caseID:8d0dc46aee71ea3a3176692b1b884b7c
      //CoveredLines: [12, 810]
      //Input_0_JavaParser.AnnotationConstantRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationConstantRestContext0
      JavaParser.AnnotationConstantRestContext javaParser_AnnotationConstantRestContext0 = mock(JavaParser.AnnotationConstantRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAnnotationConstantRest
      javaParserBaseListener0.enterAnnotationConstantRest(javaParser_AnnotationConstantRestContext0);
  }

  @Test(timeout = 4000)
  public void test_enterAnnotationMethodOrConstantRest_003()  throws Throwable  {
      //caseID:3f954a3aa0d5b9bc65d7b6f8825ae92d
      //CoveredLines: [12, 786]
      //Input_0_JavaParser.AnnotationMethodOrConstantRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationMethodOrConstantRestContext0
      JavaParser.AnnotationMethodOrConstantRestContext javaParser_AnnotationMethodOrConstantRestContext0 = mock(JavaParser.AnnotationMethodOrConstantRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAnnotationMethodOrConstantRest
      javaParserBaseListener0.enterAnnotationMethodOrConstantRest(javaParser_AnnotationMethodOrConstantRestContext0);
  }

  @Test(timeout = 4000)
  public void test_enterAnnotationMethodRest_004()  throws Throwable  {
      //caseID:9bd0b7a7d350262ac7d4302ecdf87def
      //CoveredLines: [12, 798]
      //Input_0_JavaParser.AnnotationMethodRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationMethodRestContext0
      JavaParser.AnnotationMethodRestContext javaParser_AnnotationMethodRestContext0 = mock(JavaParser.AnnotationMethodRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAnnotationMethodRest
      javaParserBaseListener0.enterAnnotationMethodRest(javaParser_AnnotationMethodRestContext0);
  }

  @Test(timeout = 4000)
  public void test_enterAnnotationTypeBody_005()  throws Throwable  {
      //caseID:a8451a0c3e25379d7211b531a3ae3f25
      //CoveredLines: [12, 750]
      //Input_0_JavaParser.AnnotationTypeBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationTypeBodyContext0
      JavaParser.AnnotationTypeBodyContext javaParser_AnnotationTypeBodyContext0 = mock(JavaParser.AnnotationTypeBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAnnotationTypeBody
      javaParserBaseListener0.enterAnnotationTypeBody(javaParser_AnnotationTypeBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_enterAnnotationTypeDeclaration_006()  throws Throwable  {
      //caseID:7acd309e278465f39c1e6a0e4436e738
      //CoveredLines: [12, 738]
      //Input_0_JavaParser.AnnotationTypeDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationTypeDeclarationContext0
      JavaParser.AnnotationTypeDeclarationContext javaParser_AnnotationTypeDeclarationContext0 = mock(JavaParser.AnnotationTypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAnnotationTypeDeclaration
      javaParserBaseListener0.enterAnnotationTypeDeclaration(javaParser_AnnotationTypeDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterAnnotationTypeElementDeclaration_007()  throws Throwable  {
      //caseID:ed5fd8a9732396e60666db4f25d14c54
      //CoveredLines: [12, 762]
      //Input_0_JavaParser.AnnotationTypeElementDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationTypeElementDeclarationContext0
      JavaParser.AnnotationTypeElementDeclarationContext javaParser_AnnotationTypeElementDeclarationContext0 = mock(JavaParser.AnnotationTypeElementDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAnnotationTypeElementDeclaration
      javaParserBaseListener0.enterAnnotationTypeElementDeclaration(javaParser_AnnotationTypeElementDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterAnnotationTypeElementRest_008()  throws Throwable  {
      //caseID:f906f2763ed1f0ee2df45d117d5b415e
      //CoveredLines: [12, 774]
      //Input_0_JavaParser.AnnotationTypeElementRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationTypeElementRestContext0
      JavaParser.AnnotationTypeElementRestContext javaParser_AnnotationTypeElementRestContext0 = mock(JavaParser.AnnotationTypeElementRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterAnnotationTypeElementRest
      javaParserBaseListener0.enterAnnotationTypeElementRest(javaParser_AnnotationTypeElementRestContext0);
  }

  @Test(timeout = 4000)
  public void test_enterArguments_009()  throws Throwable  {
      //caseID:68376c0e806346892f3acf87849f22b2
      //CoveredLines: [12, 1494]
      //Input_0_JavaParser.ArgumentsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ArgumentsContext0
      JavaParser.ArgumentsContext javaParser_ArgumentsContext0 = mock(JavaParser.ArgumentsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterArguments
      javaParserBaseListener0.enterArguments(javaParser_ArgumentsContext0);
  }

  @Test(timeout = 4000)
  public void test_enterArrayCreatorRest_010()  throws Throwable  {
      //caseID:1c79a006194f0d2bbd02b2dc6a943f5c
      //CoveredLines: [12, 1350]
      //Input_0_JavaParser.ArrayCreatorRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ArrayCreatorRestContext0
      JavaParser.ArrayCreatorRestContext javaParser_ArrayCreatorRestContext0 = mock(JavaParser.ArrayCreatorRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterArrayCreatorRest
      javaParserBaseListener0.enterArrayCreatorRest(javaParser_ArrayCreatorRestContext0);
  }

  @Test(timeout = 4000)
  public void test_enterArrayInitializer_011()  throws Throwable  {
      //caseID:8d84afb63e45fba5a6fd136ed230573a
      //CoveredLines: [12, 486]
      //Input_0_JavaParser.ArrayInitializerContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ArrayInitializerContext0
      JavaParser.ArrayInitializerContext javaParser_ArrayInitializerContext0 = mock(JavaParser.ArrayInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterArrayInitializer
      javaParserBaseListener0.enterArrayInitializer(javaParser_ArrayInitializerContext0);
  }

  @Test(timeout = 4000)
  public void test_enterBlock_012()  throws Throwable  {
      //caseID:6cba50f7316d75bb87988fbb033aedb4
      //CoveredLines: [12, 942]
      //Input_0_JavaParser.BlockContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_BlockContext0
      JavaParser.BlockContext javaParser_BlockContext0 = mock(JavaParser.BlockContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterBlock
      javaParserBaseListener0.enterBlock(javaParser_BlockContext0);
  }

  @Test(timeout = 4000)
  public void test_enterBlockStatement_013()  throws Throwable  {
      //caseID:e3c6043e69f84cc9c98beba2f7f02b99
      //CoveredLines: [12, 954]
      //Input_0_JavaParser.BlockStatementContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_BlockStatementContext0
      JavaParser.BlockStatementContext javaParser_BlockStatementContext0 = mock(JavaParser.BlockStatementContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterBlockStatement
      javaParserBaseListener0.enterBlockStatement(javaParser_BlockStatementContext0);
  }

  @Test(timeout = 4000)
  public void test_enterCatchClause_014()  throws Throwable  {
      //caseID:1790abaef55e20bb9e94fdb4e9b35051
      //CoveredLines: [12, 1014]
      //Input_0_JavaParser.CatchClauseContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CatchClauseContext0
      JavaParser.CatchClauseContext javaParser_CatchClauseContext0 = mock(JavaParser.CatchClauseContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterCatchClause
      javaParserBaseListener0.enterCatchClause(javaParser_CatchClauseContext0);
  }

  @Test(timeout = 4000)
  public void test_enterCatchType_015()  throws Throwable  {
      //caseID:40474a8294ed3d837a040b14fa80e191
      //CoveredLines: [12, 1026]
      //Input_0_JavaParser.CatchTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CatchTypeContext0
      JavaParser.CatchTypeContext javaParser_CatchTypeContext0 = mock(JavaParser.CatchTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterCatchType
      javaParserBaseListener0.enterCatchType(javaParser_CatchTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_enterClassBody_016()  throws Throwable  {
      //caseID:2a7bc74695c63a6bd1ef29134108d676
      //CoveredLines: [12, 210]
      //Input_0_JavaParser.ClassBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassBodyContext0
      JavaParser.ClassBodyContext javaParser_ClassBodyContext0 = mock(JavaParser.ClassBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterClassBody
      javaParserBaseListener0.enterClassBody(javaParser_ClassBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_enterClassBodyDeclaration_017()  throws Throwable  {
      //caseID:87984633039c694b5b896cbef54a46d9
      //CoveredLines: [12, 234]
      //Input_0_JavaParser.ClassBodyDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassBodyDeclarationContext0
      JavaParser.ClassBodyDeclarationContext javaParser_ClassBodyDeclarationContext0 = mock(JavaParser.ClassBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterClassBodyDeclaration
      javaParserBaseListener0.enterClassBodyDeclaration(javaParser_ClassBodyDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterClassCreatorRest_018()  throws Throwable  {
      //caseID:4a527ff3dcd2a4d25a6ee8ef7242e31f
      //CoveredLines: [12, 1362]
      //Input_0_JavaParser.ClassCreatorRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassCreatorRestContext0
      JavaParser.ClassCreatorRestContext javaParser_ClassCreatorRestContext0 = mock(JavaParser.ClassCreatorRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterClassCreatorRest
      javaParserBaseListener0.enterClassCreatorRest(javaParser_ClassCreatorRestContext0);
  }

  @Test(timeout = 4000)
  public void test_enterClassDeclaration_019()  throws Throwable  {
      //caseID:f5180472df03816ff8abe9dda6fb2873
      //CoveredLines: [12, 102]
      //Input_0_JavaParser.ClassDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassDeclarationContext0
      JavaParser.ClassDeclarationContext javaParser_ClassDeclarationContext0 = mock(JavaParser.ClassDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterClassDeclaration
      javaParserBaseListener0.enterClassDeclaration(javaParser_ClassDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterClassOrInterfaceModifier_020()  throws Throwable  {
      //caseID:e625f004f33de828cd9bb54815a93e1e
      //CoveredLines: [12, 78]
      //Input_0_JavaParser.ClassOrInterfaceModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassOrInterfaceModifierContext0
      JavaParser.ClassOrInterfaceModifierContext javaParser_ClassOrInterfaceModifierContext0 = mock(JavaParser.ClassOrInterfaceModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterClassOrInterfaceModifier
      javaParserBaseListener0.enterClassOrInterfaceModifier(javaParser_ClassOrInterfaceModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_enterClassOrInterfaceType_021()  throws Throwable  {
      //caseID:0bd38310f906158337f585fec4bcf3bf
      //CoveredLines: [12, 498]
      //Input_0_JavaParser.ClassOrInterfaceTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassOrInterfaceTypeContext0
      JavaParser.ClassOrInterfaceTypeContext javaParser_ClassOrInterfaceTypeContext0 = mock(JavaParser.ClassOrInterfaceTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterClassOrInterfaceType
      javaParserBaseListener0.enterClassOrInterfaceType(javaParser_ClassOrInterfaceTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_enterClassType_022()  throws Throwable  {
      //caseID:1a2dd947faa9c7a280e5d0e5b5a43f3d
      //CoveredLines: [12, 1302]
      //Input_0_JavaParser.ClassTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassTypeContext0
      JavaParser.ClassTypeContext javaParser_ClassTypeContext0 = mock(JavaParser.ClassTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterClassType
      javaParserBaseListener0.enterClassType(javaParser_ClassTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_enterCompilationUnit_023()  throws Throwable  {
      //caseID:dfb0c67ab4b7f938166a39366f200785
      //CoveredLines: [12, 18]
      //Input_0_JavaParser.CompilationUnitContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CompilationUnitContext0
      JavaParser.CompilationUnitContext javaParser_CompilationUnitContext0 = mock(JavaParser.CompilationUnitContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterCompilationUnit
      javaParserBaseListener0.enterCompilationUnit(javaParser_CompilationUnitContext0);
  }

  @Test(timeout = 4000)
  public void test_enterConstDeclaration_024()  throws Throwable  {
      //caseID:b1473ede5a3569dff43b3087937b8b35
      //CoveredLines: [12, 366]
      //Input_0_JavaParser.ConstDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ConstDeclarationContext0
      JavaParser.ConstDeclarationContext javaParser_ConstDeclarationContext0 = mock(JavaParser.ConstDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterConstDeclaration
      javaParserBaseListener0.enterConstDeclaration(javaParser_ConstDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterConstantDeclarator_025()  throws Throwable  {
      //caseID:eb2155f2da11c7180c4f22e4d9385db4
      //CoveredLines: [12, 378]
      //Input_0_JavaParser.ConstantDeclaratorContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ConstantDeclaratorContext0
      JavaParser.ConstantDeclaratorContext javaParser_ConstantDeclaratorContext0 = mock(JavaParser.ConstantDeclaratorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterConstantDeclarator
      javaParserBaseListener0.enterConstantDeclarator(javaParser_ConstantDeclaratorContext0);
  }

  @Test(timeout = 4000)
  public void test_enterConstructorDeclaration_026()  throws Throwable  {
      //caseID:607f5f31a72b6c166438afa2171dd2e8
      //CoveredLines: [12, 318]
      //Input_0_JavaParser.ConstructorDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ConstructorDeclarationContext0
      JavaParser.ConstructorDeclarationContext javaParser_ConstructorDeclarationContext0 = mock(JavaParser.ConstructorDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterConstructorDeclaration
      javaParserBaseListener0.enterConstructorDeclaration(javaParser_ConstructorDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterCreatedName_027()  throws Throwable  {
      //caseID:1c948fdd8b8f6657fa534d7093fdd7dd
      //CoveredLines: [12, 1326]
      //Input_0_JavaParser.CreatedNameContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CreatedNameContext0
      JavaParser.CreatedNameContext javaParser_CreatedNameContext0 = mock(JavaParser.CreatedNameContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterCreatedName
      javaParserBaseListener0.enterCreatedName(javaParser_CreatedNameContext0);
  }

  @Test(timeout = 4000)
  public void test_enterCreator_028()  throws Throwable  {
      //caseID:1e673149434ca0bec9b0321b02c561f4
      //CoveredLines: [12, 1314]
      //Input_0_JavaParser.CreatorContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CreatorContext0
      JavaParser.CreatorContext javaParser_CreatorContext0 = mock(JavaParser.CreatorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterCreator
      javaParserBaseListener0.enterCreator(javaParser_CreatorContext0);
  }

  @Test(timeout = 4000)
  public void test_enterDefaultValue_029()  throws Throwable  {
      //caseID:4286994de9613c42e4c4b2f0ef23c077
      //CoveredLines: [12, 822]
      //Input_0_JavaParser.DefaultValueContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_DefaultValueContext0
      JavaParser.DefaultValueContext javaParser_DefaultValueContext0 = mock(JavaParser.DefaultValueContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterDefaultValue
      javaParserBaseListener0.enterDefaultValue(javaParser_DefaultValueContext0);
  }

  @Test(timeout = 4000)
  public void test_enterElementValue_030()  throws Throwable  {
      //caseID:fb9fe21b818da6128fb7ee8b30f9a5ff
      //CoveredLines: [12, 714]
      //Input_0_JavaParser.ElementValueContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ElementValueContext0
      JavaParser.ElementValueContext javaParser_ElementValueContext0 = mock(JavaParser.ElementValueContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterElementValue
      javaParserBaseListener0.enterElementValue(javaParser_ElementValueContext0);
  }

  @Test(timeout = 4000)
  public void test_enterElementValueArrayInitializer_031()  throws Throwable  {
      //caseID:dc627ef3bbfa53b7bd3f0840e253b671
      //CoveredLines: [12, 726]
      //Input_0_JavaParser.ElementValueArrayInitializerContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ElementValueArrayInitializerContext0
      JavaParser.ElementValueArrayInitializerContext javaParser_ElementValueArrayInitializerContext0 = mock(JavaParser.ElementValueArrayInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterElementValueArrayInitializer
      javaParserBaseListener0.enterElementValueArrayInitializer(javaParser_ElementValueArrayInitializerContext0);
  }

  @Test(timeout = 4000)
  public void test_enterElementValuePair_032()  throws Throwable  {
      //caseID:f925e83f5172568015332049f04b5f7d
      //CoveredLines: [12, 702]
      //Input_0_JavaParser.ElementValuePairContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ElementValuePairContext0
      JavaParser.ElementValuePairContext javaParser_ElementValuePairContext0 = mock(JavaParser.ElementValuePairContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterElementValuePair
      javaParserBaseListener0.enterElementValuePair(javaParser_ElementValuePairContext0);
  }

  @Test(timeout = 4000)
  public void test_enterElementValuePairs_033()  throws Throwable  {
      //caseID:317b5620214b15a6bbfdb48ff0b5b7d4
      //CoveredLines: [12, 690]
      //Input_0_JavaParser.ElementValuePairsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ElementValuePairsContext0
      JavaParser.ElementValuePairsContext javaParser_ElementValuePairsContext0 = mock(JavaParser.ElementValuePairsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterElementValuePairs
      javaParserBaseListener0.enterElementValuePairs(javaParser_ElementValuePairsContext0);
  }

  @Test(timeout = 4000)
  public void test_enterEnhancedForControl_034()  throws Throwable  {
      //caseID:4bec94bcc3e21687ec4554454dc5af62
      //CoveredLines: [12, 1134]
      //Input_0_JavaParser.EnhancedForControlContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnhancedForControlContext0
      JavaParser.EnhancedForControlContext javaParser_EnhancedForControlContext0 = mock(JavaParser.EnhancedForControlContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterEnhancedForControl
      javaParserBaseListener0.enterEnhancedForControl(javaParser_EnhancedForControlContext0);
  }

  @Test(timeout = 4000)
  public void test_enterEnumBodyDeclarations_035()  throws Throwable  {
      //caseID:********************************
      //CoveredLines: [12, 186]
      //Input_0_JavaParser.EnumBodyDeclarationsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnumBodyDeclarationsContext0
      JavaParser.EnumBodyDeclarationsContext javaParser_EnumBodyDeclarationsContext0 = mock(JavaParser.EnumBodyDeclarationsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterEnumBodyDeclarations
      javaParserBaseListener0.enterEnumBodyDeclarations(javaParser_EnumBodyDeclarationsContext0);
  }

  @Test(timeout = 4000)
  public void test_enterEnumConstant_036()  throws Throwable  {
      //caseID:68ab7edb5e097b8ec10f576f968b8ff1
      //CoveredLines: [12, 174]
      //Input_0_JavaParser.EnumConstantContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnumConstantContext0
      JavaParser.EnumConstantContext javaParser_EnumConstantContext0 = mock(JavaParser.EnumConstantContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterEnumConstant
      javaParserBaseListener0.enterEnumConstant(javaParser_EnumConstantContext0);
  }

  @Test(timeout = 4000)
  public void test_enterEnumConstants_037()  throws Throwable  {
      //caseID:6992b7b0f90c4a0896fca27798429d67
      //CoveredLines: [12, 162]
      //Input_0_JavaParser.EnumConstantsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnumConstantsContext0
      JavaParser.EnumConstantsContext javaParser_EnumConstantsContext0 = mock(JavaParser.EnumConstantsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterEnumConstants
      javaParserBaseListener0.enterEnumConstants(javaParser_EnumConstantsContext0);
  }

  @Test(timeout = 4000)
  public void test_enterEnumDeclaration_038()  throws Throwable  {
      //caseID:3c66c4725556fd5660ddcb11cf01d4e3
      //CoveredLines: [12, 150]
      //Input_0_JavaParser.EnumDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnumDeclarationContext0
      JavaParser.EnumDeclarationContext javaParser_EnumDeclarationContext0 = mock(JavaParser.EnumDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterEnumDeclaration
      javaParserBaseListener0.enterEnumDeclaration(javaParser_EnumDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterEveryRule_039()  throws Throwable  {
      //caseID:5b816b341b9b3be8d406a41d4f215bad
      //CoveredLines: [12, 1507]
      //Input_0_ParserRuleContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock parserRuleContext0
      ParserRuleContext parserRuleContext0 = mock(ParserRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterEveryRule
      javaParserBaseListener0.enterEveryRule(parserRuleContext0);
  }

  @Test(timeout = 4000)
  public void test_enterExplicitGenericInvocation_040()  throws Throwable  {
      //caseID:a5686fb0fe92444814bc4a5a42e9b0c8
      //CoveredLines: [12, 1374]
      //Input_0_JavaParser.ExplicitGenericInvocationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ExplicitGenericInvocationContext0
      JavaParser.ExplicitGenericInvocationContext javaParser_ExplicitGenericInvocationContext0 = mock(JavaParser.ExplicitGenericInvocationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterExplicitGenericInvocation
      javaParserBaseListener0.enterExplicitGenericInvocation(javaParser_ExplicitGenericInvocationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterExplicitGenericInvocationSuffix_041()  throws Throwable  {
      //caseID:6eebc2ebe459ecb63d0f8cd63bc732b1
      //CoveredLines: [12, 1482]
      //Input_0_JavaParser.ExplicitGenericInvocationSuffixContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ExplicitGenericInvocationSuffixContext0
      JavaParser.ExplicitGenericInvocationSuffixContext javaParser_ExplicitGenericInvocationSuffixContext0 = mock(JavaParser.ExplicitGenericInvocationSuffixContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterExplicitGenericInvocationSuffix
      javaParserBaseListener0.enterExplicitGenericInvocationSuffix(javaParser_ExplicitGenericInvocationSuffixContext0);
  }

  @Test(timeout = 4000)
  public void test_enterExpression_042()  throws Throwable  {
      //caseID:a766a31f3eafd06236437816bfa3b17d
      //CoveredLines: [12, 1182]
      //Input_0_JavaParser.ExpressionContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ExpressionContext0
      JavaParser.ExpressionContext javaParser_ExpressionContext0 = mock(JavaParser.ExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterExpression
      javaParserBaseListener0.enterExpression(javaParser_ExpressionContext0);
  }

  @Test(timeout = 4000)
  public void test_enterExpressionList_043()  throws Throwable  {
      //caseID:4dd546435d4a96bb368fa01aa9fd5c9d
      //CoveredLines: [12, 1158]
      //Input_0_JavaParser.ExpressionListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ExpressionListContext0
      JavaParser.ExpressionListContext javaParser_ExpressionListContext0 = mock(JavaParser.ExpressionListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterExpressionList
      javaParserBaseListener0.enterExpressionList(javaParser_ExpressionListContext0);
  }

  @Test(timeout = 4000)
  public void test_enterFieldDeclaration_044()  throws Throwable  {
      //caseID:fb0f42618d9e36cd95383208fe4d1cbc
      //CoveredLines: [12, 330]
      //Input_0_JavaParser.FieldDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FieldDeclarationContext0
      JavaParser.FieldDeclarationContext javaParser_FieldDeclarationContext0 = mock(JavaParser.FieldDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterFieldDeclaration
      javaParserBaseListener0.enterFieldDeclaration(javaParser_FieldDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterFinallyBlock_045()  throws Throwable  {
      //caseID:837fdabdc829a34b67bb5beac6ccd80d
      //CoveredLines: [12, 1038]
      //Input_0_JavaParser.FinallyBlockContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FinallyBlockContext0
      JavaParser.FinallyBlockContext javaParser_FinallyBlockContext0 = mock(JavaParser.FinallyBlockContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterFinallyBlock
      javaParserBaseListener0.enterFinallyBlock(javaParser_FinallyBlockContext0);
  }

  @Test(timeout = 4000)
  public void test_enterFloatLiteral_046()  throws Throwable  {
      //caseID:5194230ac34e4b18306399032303ac7e
      //CoveredLines: [12, 654]
      //Input_0_JavaParser.FloatLiteralContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FloatLiteralContext0
      JavaParser.FloatLiteralContext javaParser_FloatLiteralContext0 = mock(JavaParser.FloatLiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterFloatLiteral
      javaParserBaseListener0.enterFloatLiteral(javaParser_FloatLiteralContext0);
  }

  @Test(timeout = 4000)
  public void test_enterForControl_047()  throws Throwable  {
      //caseID:47ad8af4fa76dc4775393438e5e6fd80
      //CoveredLines: [12, 1110]
      //Input_0_JavaParser.ForControlContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ForControlContext0
      JavaParser.ForControlContext javaParser_ForControlContext0 = mock(JavaParser.ForControlContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterForControl
      javaParserBaseListener0.enterForControl(javaParser_ForControlContext0);
  }

  @Test(timeout = 4000)
  public void test_enterForInit_048()  throws Throwable  {
      //caseID:2d6392170b5032504d1bf6d732e8ceaa
      //CoveredLines: [12, 1122]
      //Input_0_JavaParser.ForInitContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ForInitContext0
      JavaParser.ForInitContext javaParser_ForInitContext0 = mock(JavaParser.ForInitContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterForInit
      javaParserBaseListener0.enterForInit(javaParser_ForInitContext0);
  }

  @Test(timeout = 4000)
  public void test_enterFormalParameter_049()  throws Throwable  {
      //caseID:f21d66787f563276f29b20e1a9a3304f
      //CoveredLines: [12, 570]
      //Input_0_JavaParser.FormalParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FormalParameterContext0
      JavaParser.FormalParameterContext javaParser_FormalParameterContext0 = mock(JavaParser.FormalParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterFormalParameter
      javaParserBaseListener0.enterFormalParameter(javaParser_FormalParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_enterFormalParameterList_050()  throws Throwable  {
      //caseID:84d6b61099984be26756a666d887ef04
      //CoveredLines: [12, 558]
      //Input_0_JavaParser.FormalParameterListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FormalParameterListContext0
      JavaParser.FormalParameterListContext javaParser_FormalParameterListContext0 = mock(JavaParser.FormalParameterListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterFormalParameterList
      javaParserBaseListener0.enterFormalParameterList(javaParser_FormalParameterListContext0);
  }

  @Test(timeout = 4000)
  public void test_enterFormalParameters_051()  throws Throwable  {
      //caseID:39ec78f22db453ada0cce4d7c1898c10
      //CoveredLines: [12, 534]
      //Input_0_JavaParser.FormalParametersContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FormalParametersContext0
      JavaParser.FormalParametersContext javaParser_FormalParametersContext0 = mock(JavaParser.FormalParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterFormalParameters
      javaParserBaseListener0.enterFormalParameters(javaParser_FormalParametersContext0);
  }

  @Test(timeout = 4000)
  public void test_enterGenericConstructorDeclaration_052()  throws Throwable  {
      //caseID:9b489e549b9bafb363a0ed142db00001
      //CoveredLines: [12, 306]
      //Input_0_JavaParser.GenericConstructorDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_GenericConstructorDeclarationContext0
      JavaParser.GenericConstructorDeclarationContext javaParser_GenericConstructorDeclarationContext0 = mock(JavaParser.GenericConstructorDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterGenericConstructorDeclaration
      javaParserBaseListener0.enterGenericConstructorDeclaration(javaParser_GenericConstructorDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterGenericInterfaceMethodDeclaration_053()  throws Throwable  {
      //caseID:130a9aae527cf9b4adfb03e284a95ab4
      //CoveredLines: [12, 414]
      //Input_0_JavaParser.GenericInterfaceMethodDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_GenericInterfaceMethodDeclarationContext0
      JavaParser.GenericInterfaceMethodDeclarationContext javaParser_GenericInterfaceMethodDeclarationContext0 = mock(JavaParser.GenericInterfaceMethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterGenericInterfaceMethodDeclaration
      javaParserBaseListener0.enterGenericInterfaceMethodDeclaration(javaParser_GenericInterfaceMethodDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterGenericMethodDeclaration_054()  throws Throwable  {
      //caseID:273596262b99029fbcd5856d7be0ac38
      //CoveredLines: [12, 294]
      //Input_0_JavaParser.GenericMethodDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_GenericMethodDeclarationContext0
      JavaParser.GenericMethodDeclarationContext javaParser_GenericMethodDeclarationContext0 = mock(JavaParser.GenericMethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterGenericMethodDeclaration
      javaParserBaseListener0.enterGenericMethodDeclaration(javaParser_GenericMethodDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterGuardedPattern_055()  throws Throwable  {
      //caseID:06d34126473d327d049e3a4b932522fe
      //CoveredLines: [12, 1278]
      //Input_0_JavaParser.GuardedPatternContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_GuardedPatternContext0
      JavaParser.GuardedPatternContext javaParser_GuardedPatternContext0 = mock(JavaParser.GuardedPatternContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterGuardedPattern
      javaParserBaseListener0.enterGuardedPattern(javaParser_GuardedPatternContext0);
  }

  @Test(timeout = 4000)
  public void test_enterIdentifier_056()  throws Throwable  {
      //caseID:647acacf34a85a2e18bd60d3919f755c
      //CoveredLines: [12, 978]
      //Input_0_JavaParser.IdentifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_IdentifierContext0
      JavaParser.IdentifierContext javaParser_IdentifierContext0 = mock(JavaParser.IdentifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterIdentifier
      javaParserBaseListener0.enterIdentifier(javaParser_IdentifierContext0);
  }

  @Test(timeout = 4000)
  public void test_enterImportDeclaration_057()  throws Throwable  {
      //caseID:5c5e08c0719a637c8bfa2208647bbbcc
      //CoveredLines: [12, 42]
      //Input_0_JavaParser.ImportDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ImportDeclarationContext0
      JavaParser.ImportDeclarationContext javaParser_ImportDeclarationContext0 = mock(JavaParser.ImportDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterImportDeclaration
      javaParserBaseListener0.enterImportDeclaration(javaParser_ImportDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterInnerCreator_058()  throws Throwable  {
      //caseID:f73903fbe398312c313fce9599a36be0
      //CoveredLines: [12, 1338]
      //Input_0_JavaParser.InnerCreatorContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InnerCreatorContext0
      JavaParser.InnerCreatorContext javaParser_InnerCreatorContext0 = mock(JavaParser.InnerCreatorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterInnerCreator
      javaParserBaseListener0.enterInnerCreator(javaParser_InnerCreatorContext0);
  }

  @Test(timeout = 4000)
  public void test_enterIntegerLiteral_059()  throws Throwable  {
      //caseID:c777af345b840b02e5cc4dbf5affa0d8
      //CoveredLines: [12, 642]
      //Input_0_JavaParser.IntegerLiteralContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_IntegerLiteralContext0
      JavaParser.IntegerLiteralContext javaParser_IntegerLiteralContext0 = mock(JavaParser.IntegerLiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterIntegerLiteral
      javaParserBaseListener0.enterIntegerLiteral(javaParser_IntegerLiteralContext0);
  }

  @Test(timeout = 4000)
  public void test_enterInterfaceBody_060()  throws Throwable  {
      //caseID:b1159388cdbb320a422ebef8d38d898f
      //CoveredLines: [12, 222]
      //Input_0_JavaParser.InterfaceBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceBodyContext0
      JavaParser.InterfaceBodyContext javaParser_InterfaceBodyContext0 = mock(JavaParser.InterfaceBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterInterfaceBody
      javaParserBaseListener0.enterInterfaceBody(javaParser_InterfaceBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_enterInterfaceBodyDeclaration_061()  throws Throwable  {
      //caseID:fd006a8b40e681d7bf7562fe1074eba5
      //CoveredLines: [12, 342]
      //Input_0_JavaParser.InterfaceBodyDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceBodyDeclarationContext0
      JavaParser.InterfaceBodyDeclarationContext javaParser_InterfaceBodyDeclarationContext0 = mock(JavaParser.InterfaceBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterInterfaceBodyDeclaration
      javaParserBaseListener0.enterInterfaceBodyDeclaration(javaParser_InterfaceBodyDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterInterfaceCommonBodyDeclaration_062()  throws Throwable  {
      //caseID:b816128442487df64bfbf459f4105eda
      //CoveredLines: [12, 426]
      //Input_0_JavaParser.InterfaceCommonBodyDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceCommonBodyDeclarationContext0
      JavaParser.InterfaceCommonBodyDeclarationContext javaParser_InterfaceCommonBodyDeclarationContext0 = mock(JavaParser.InterfaceCommonBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterInterfaceCommonBodyDeclaration
      javaParserBaseListener0.enterInterfaceCommonBodyDeclaration(javaParser_InterfaceCommonBodyDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterInterfaceDeclaration_063()  throws Throwable  {
      //caseID:97aff68bb0b63efbd07d99ab9cbe04ea
      //CoveredLines: [12, 198]
      //Input_0_JavaParser.InterfaceDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceDeclarationContext0
      JavaParser.InterfaceDeclarationContext javaParser_InterfaceDeclarationContext0 = mock(JavaParser.InterfaceDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterInterfaceDeclaration
      javaParserBaseListener0.enterInterfaceDeclaration(javaParser_InterfaceDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterInterfaceMemberDeclaration_064()  throws Throwable  {
      //caseID:6318f7c242cdcce59f867377d65e507f
      //CoveredLines: [12, 354]
      //Input_0_JavaParser.InterfaceMemberDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceMemberDeclarationContext0
      JavaParser.InterfaceMemberDeclarationContext javaParser_InterfaceMemberDeclarationContext0 = mock(JavaParser.InterfaceMemberDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterInterfaceMemberDeclaration
      javaParserBaseListener0.enterInterfaceMemberDeclaration(javaParser_InterfaceMemberDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterInterfaceMethodDeclaration_065()  throws Throwable  {
      //caseID:d4886d494f71c04e47b2253d177da39d
      //CoveredLines: [12, 390]
      //Input_0_JavaParser.InterfaceMethodDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceMethodDeclarationContext0
      JavaParser.InterfaceMethodDeclarationContext javaParser_InterfaceMethodDeclarationContext0 = mock(JavaParser.InterfaceMethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterInterfaceMethodDeclaration
      javaParserBaseListener0.enterInterfaceMethodDeclaration(javaParser_InterfaceMethodDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterInterfaceMethodModifier_066()  throws Throwable  {
      //caseID:5707c6a8c2c8134ced3a266dd72df573
      //CoveredLines: [12, 402]
      //Input_0_JavaParser.InterfaceMethodModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceMethodModifierContext0
      JavaParser.InterfaceMethodModifierContext javaParser_InterfaceMethodModifierContext0 = mock(JavaParser.InterfaceMethodModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterInterfaceMethodModifier
      javaParserBaseListener0.enterInterfaceMethodModifier(javaParser_InterfaceMethodModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLambdaBody_067()  throws Throwable  {
      //caseID:aaeed9aa53c2e1c7352e4fe44e608008
      //CoveredLines: [12, 1230]
      //Input_0_JavaParser.LambdaBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaBodyContext0
      JavaParser.LambdaBodyContext javaParser_LambdaBodyContext0 = mock(JavaParser.LambdaBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLambdaBody
      javaParserBaseListener0.enterLambdaBody(javaParser_LambdaBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLambdaExpression_068()  throws Throwable  {
      //caseID:7c9df9b1494ae1e56a982dbace8d0be1
      //CoveredLines: [12, 1206]
      //Input_0_JavaParser.LambdaExpressionContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaExpressionContext0
      JavaParser.LambdaExpressionContext javaParser_LambdaExpressionContext0 = mock(JavaParser.LambdaExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLambdaExpression
      javaParserBaseListener0.enterLambdaExpression(javaParser_LambdaExpressionContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLambdaLVTIList_069()  throws Throwable  {
      //caseID:a035eead799163ebf4ec511392f91891
      //CoveredLines: [12, 594]
      //Input_0_JavaParser.LambdaLVTIListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaLVTIListContext0
      JavaParser.LambdaLVTIListContext javaParser_LambdaLVTIListContext0 = mock(JavaParser.LambdaLVTIListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLambdaLVTIList
      javaParserBaseListener0.enterLambdaLVTIList(javaParser_LambdaLVTIListContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLambdaLVTIParameter_070()  throws Throwable  {
      //caseID:2e9b47a903acadbf06c3f05c85a0cd85
      //CoveredLines: [12, 606]
      //Input_0_JavaParser.LambdaLVTIParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaLVTIParameterContext0
      JavaParser.LambdaLVTIParameterContext javaParser_LambdaLVTIParameterContext0 = mock(JavaParser.LambdaLVTIParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLambdaLVTIParameter
      javaParserBaseListener0.enterLambdaLVTIParameter(javaParser_LambdaLVTIParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLambdaParameters_071()  throws Throwable  {
      //caseID:405366fab944490b09a43740c54417d1
      //CoveredLines: [12, 1218]
      //Input_0_JavaParser.LambdaParametersContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaParametersContext0
      JavaParser.LambdaParametersContext javaParser_LambdaParametersContext0 = mock(JavaParser.LambdaParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLambdaParameters
      javaParserBaseListener0.enterLambdaParameters(javaParser_LambdaParametersContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLastFormalParameter_072()  throws Throwable  {
      //caseID:6ff4a9338ad24cfeb29c1f9ba9040e70
      //CoveredLines: [12, 582]
      //Input_0_JavaParser.LastFormalParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LastFormalParameterContext0
      JavaParser.LastFormalParameterContext javaParser_LastFormalParameterContext0 = mock(JavaParser.LastFormalParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLastFormalParameter
      javaParserBaseListener0.enterLastFormalParameter(javaParser_LastFormalParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLiteral_073()  throws Throwable  {
      //caseID:9be1a069914b7450063302d492d2d5dc
      //CoveredLines: [12, 630]
      //Input_0_JavaParser.LiteralContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LiteralContext0
      JavaParser.LiteralContext javaParser_LiteralContext0 = mock(JavaParser.LiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLiteral
      javaParserBaseListener0.enterLiteral(javaParser_LiteralContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLocalTypeDeclaration_074()  throws Throwable  {
      //caseID:03e60ff268330fceb839327e8cf8abe9
      //CoveredLines: [12, 990]
      //Input_0_JavaParser.LocalTypeDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LocalTypeDeclarationContext0
      JavaParser.LocalTypeDeclarationContext javaParser_LocalTypeDeclarationContext0 = mock(JavaParser.LocalTypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLocalTypeDeclaration
      javaParserBaseListener0.enterLocalTypeDeclaration(javaParser_LocalTypeDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterLocalVariableDeclaration_075()  throws Throwable  {
      //caseID:c09f3f79cfa5ff8842dd8db55b21b59e
      //CoveredLines: [12, 966]
      //Input_0_JavaParser.LocalVariableDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LocalVariableDeclarationContext0
      JavaParser.LocalVariableDeclarationContext javaParser_LocalVariableDeclarationContext0 = mock(JavaParser.LocalVariableDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterLocalVariableDeclaration
      javaParserBaseListener0.enterLocalVariableDeclaration(javaParser_LocalVariableDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterMemberDeclaration_076()  throws Throwable  {
      //caseID:d49250f132fc8af44395273ae847409f
      //CoveredLines: [12, 246]
      //Input_0_JavaParser.MemberDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_MemberDeclarationContext0
      JavaParser.MemberDeclarationContext javaParser_MemberDeclarationContext0 = mock(JavaParser.MemberDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterMemberDeclaration
      javaParserBaseListener0.enterMemberDeclaration(javaParser_MemberDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterMethodBody_077()  throws Throwable  {
      //caseID:2dcb4296fa724b1bc39ecca961c94922
      //CoveredLines: [12, 270]
      //Input_0_JavaParser.MethodBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_MethodBodyContext0
      JavaParser.MethodBodyContext javaParser_MethodBodyContext0 = mock(JavaParser.MethodBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterMethodBody
      javaParserBaseListener0.enterMethodBody(javaParser_MethodBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_enterMethodCall_078()  throws Throwable  {
      //caseID:91c387608c5cc27fc7ece8622593095e
      //CoveredLines: [12, 1170]
      //Input_0_JavaParser.MethodCallContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_MethodCallContext0
      JavaParser.MethodCallContext javaParser_MethodCallContext0 = mock(JavaParser.MethodCallContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterMethodCall
      javaParserBaseListener0.enterMethodCall(javaParser_MethodCallContext0);
  }

  @Test(timeout = 4000)
  public void test_enterMethodDeclaration_079()  throws Throwable  {
      //caseID:5667ad5f59dcf1c4e6d3cff52b00738b
      //CoveredLines: [12, 258]
      //Input_0_JavaParser.MethodDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_MethodDeclarationContext0
      JavaParser.MethodDeclarationContext javaParser_MethodDeclarationContext0 = mock(JavaParser.MethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterMethodDeclaration
      javaParserBaseListener0.enterMethodDeclaration(javaParser_MethodDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterModifier_080()  throws Throwable  {
      //caseID:c63c5dfb75098221d5b5a38433e27af4
      //CoveredLines: [12, 66]
      //Input_0_JavaParser.ModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ModifierContext0
      JavaParser.ModifierContext javaParser_ModifierContext0 = mock(JavaParser.ModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterModifier
      javaParserBaseListener0.enterModifier(javaParser_ModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_enterModuleBody_081()  throws Throwable  {
      //caseID:bd00a579bbf440618d590ef58643518b
      //CoveredLines: [12, 846]
      //Input_0_JavaParser.ModuleBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ModuleBodyContext0
      JavaParser.ModuleBodyContext javaParser_ModuleBodyContext0 = mock(JavaParser.ModuleBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterModuleBody
      javaParserBaseListener0.enterModuleBody(javaParser_ModuleBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_enterModuleDeclaration_082()  throws Throwable  {
      //caseID:b61309e2d799477a663db9746d391a69
      //CoveredLines: [12, 834]
      //Input_0_JavaParser.ModuleDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ModuleDeclarationContext0
      JavaParser.ModuleDeclarationContext javaParser_ModuleDeclarationContext0 = mock(JavaParser.ModuleDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterModuleDeclaration
      javaParserBaseListener0.enterModuleDeclaration(javaParser_ModuleDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterModuleDirective_083()  throws Throwable  {
      //caseID:364a3acb543950b5f263e25acf680da2
      //CoveredLines: [12, 858]
      //Input_0_JavaParser.ModuleDirectiveContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ModuleDirectiveContext0
      JavaParser.ModuleDirectiveContext javaParser_ModuleDirectiveContext0 = mock(JavaParser.ModuleDirectiveContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterModuleDirective
      javaParserBaseListener0.enterModuleDirective(javaParser_ModuleDirectiveContext0);
  }

  @Test(timeout = 4000)
  public void test_enterNonWildcardTypeArguments_084()  throws Throwable  {
      //caseID:c492188929423f8617de90826fa6cb69
      //CoveredLines: [12, 1410]
      //Input_0_JavaParser.NonWildcardTypeArgumentsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_NonWildcardTypeArgumentsContext0
      JavaParser.NonWildcardTypeArgumentsContext javaParser_NonWildcardTypeArgumentsContext0 = mock(JavaParser.NonWildcardTypeArgumentsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterNonWildcardTypeArguments
      javaParserBaseListener0.enterNonWildcardTypeArguments(javaParser_NonWildcardTypeArgumentsContext0);
  }

  @Test(timeout = 4000)
  public void test_enterNonWildcardTypeArgumentsOrDiamond_085()  throws Throwable  {
      //caseID:ca151bc87b589e3ca79c54b416e13cde
      //CoveredLines: [12, 1398]
      //Input_0_JavaParser.NonWildcardTypeArgumentsOrDiamondContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_NonWildcardTypeArgumentsOrDiamondContext0
      JavaParser.NonWildcardTypeArgumentsOrDiamondContext javaParser_NonWildcardTypeArgumentsOrDiamondContext0 = mock(JavaParser.NonWildcardTypeArgumentsOrDiamondContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterNonWildcardTypeArgumentsOrDiamond
      javaParserBaseListener0.enterNonWildcardTypeArgumentsOrDiamond(javaParser_NonWildcardTypeArgumentsOrDiamondContext0);
  }

  @Test(timeout = 4000)
  public void test_enterPackageDeclaration_086()  throws Throwable  {
      //caseID:25b3c7856daf231f91cc16da44f124d0
      //CoveredLines: [12, 30]
      //Input_0_JavaParser.PackageDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_PackageDeclarationContext0
      JavaParser.PackageDeclarationContext javaParser_PackageDeclarationContext0 = mock(JavaParser.PackageDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterPackageDeclaration
      javaParserBaseListener0.enterPackageDeclaration(javaParser_PackageDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterParExpression_087()  throws Throwable  {
      //caseID:7065fd5c2c1715b8d10a9932f59484ed
      //CoveredLines: [12, 1146]
      //Input_0_JavaParser.ParExpressionContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ParExpressionContext0
      JavaParser.ParExpressionContext javaParser_ParExpressionContext0 = mock(JavaParser.ParExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterParExpression
      javaParserBaseListener0.enterParExpression(javaParser_ParExpressionContext0);
  }

  @Test(timeout = 4000)
  public void test_enterPattern_088()  throws Throwable  {
      //caseID:b197c9e8f2cb6bc5cab8b76d6dbab40d
      //CoveredLines: [12, 1194]
      //Input_0_JavaParser.PatternContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_PatternContext0
      JavaParser.PatternContext javaParser_PatternContext0 = mock(JavaParser.PatternContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterPattern
      javaParserBaseListener0.enterPattern(javaParser_PatternContext0);
  }

  @Test(timeout = 4000)
  public void test_enterPrimary_089()  throws Throwable  {
      //caseID:d8770f496bee4dfec25138d27a24f12a
      //CoveredLines: [12, 1242]
      //Input_0_JavaParser.PrimaryContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_PrimaryContext0
      JavaParser.PrimaryContext javaParser_PrimaryContext0 = mock(JavaParser.PrimaryContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterPrimary
      javaParserBaseListener0.enterPrimary(javaParser_PrimaryContext0);
  }

  @Test(timeout = 4000)
  public void test_enterPrimitiveType_090()  throws Throwable  {
      //caseID:6aee6f7eb72a1b4c4c710b34476276d3
      //CoveredLines: [12, 1446]
      //Input_0_JavaParser.PrimitiveTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_PrimitiveTypeContext0
      JavaParser.PrimitiveTypeContext javaParser_PrimitiveTypeContext0 = mock(JavaParser.PrimitiveTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterPrimitiveType
      javaParserBaseListener0.enterPrimitiveType(javaParser_PrimitiveTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_enterQualifiedName_091()  throws Throwable  {
      //caseID:56a8819ff43e462d1a6b03fc9a0f17ce
      //CoveredLines: [12, 618]
      //Input_0_JavaParser.QualifiedNameContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_QualifiedNameContext0
      JavaParser.QualifiedNameContext javaParser_QualifiedNameContext0 = mock(JavaParser.QualifiedNameContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterQualifiedName
      javaParserBaseListener0.enterQualifiedName(javaParser_QualifiedNameContext0);
  }

  @Test(timeout = 4000)
  public void test_enterQualifiedNameList_092()  throws Throwable  {
      //caseID:af090563c65070e9596bcb921b32ff88
      //CoveredLines: [12, 522]
      //Input_0_JavaParser.QualifiedNameListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_QualifiedNameListContext0
      JavaParser.QualifiedNameListContext javaParser_QualifiedNameListContext0 = mock(JavaParser.QualifiedNameListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterQualifiedNameList
      javaParserBaseListener0.enterQualifiedNameList(javaParser_QualifiedNameListContext0);
  }

  @Test(timeout = 4000)
  public void test_enterReceiverParameter_093()  throws Throwable  {
      //caseID:928bf3869cbdc21a0765efbe785d1099
      //CoveredLines: [12, 546]
      //Input_0_JavaParser.ReceiverParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ReceiverParameterContext0
      JavaParser.ReceiverParameterContext javaParser_ReceiverParameterContext0 = mock(JavaParser.ReceiverParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterReceiverParameter
      javaParserBaseListener0.enterReceiverParameter(javaParser_ReceiverParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_enterRecordBody_094()  throws Throwable  {
      //caseID:38bb8af44eaa3c48be67d0adafc7dbf8
      //CoveredLines: [12, 930]
      //Input_0_JavaParser.RecordBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordBodyContext0
      JavaParser.RecordBodyContext javaParser_RecordBodyContext0 = mock(JavaParser.RecordBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterRecordBody
      javaParserBaseListener0.enterRecordBody(javaParser_RecordBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_enterRecordComponent_095()  throws Throwable  {
      //caseID:1ebfd358da1caf5463b6d89348a9092e
      //CoveredLines: [12, 918]
      //Input_0_JavaParser.RecordComponentContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordComponentContext0
      JavaParser.RecordComponentContext javaParser_RecordComponentContext0 = mock(JavaParser.RecordComponentContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterRecordComponent
      javaParserBaseListener0.enterRecordComponent(javaParser_RecordComponentContext0);
  }

  @Test(timeout = 4000)
  public void test_enterRecordComponentList_096()  throws Throwable  {
      //caseID:2be89944972279469cda5b1c547b282a
      //CoveredLines: [12, 906]
      //Input_0_JavaParser.RecordComponentListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordComponentListContext0
      JavaParser.RecordComponentListContext javaParser_RecordComponentListContext0 = mock(JavaParser.RecordComponentListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterRecordComponentList
      javaParserBaseListener0.enterRecordComponentList(javaParser_RecordComponentListContext0);
  }

  @Test(timeout = 4000)
  public void test_enterRecordDeclaration_097()  throws Throwable  {
      //caseID:ce1e1c069e53f6c97127476caf936128
      //CoveredLines: [12, 882]
      //Input_0_JavaParser.RecordDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordDeclarationContext0
      JavaParser.RecordDeclarationContext javaParser_RecordDeclarationContext0 = mock(JavaParser.RecordDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterRecordDeclaration
      javaParserBaseListener0.enterRecordDeclaration(javaParser_RecordDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterRecordHeader_098()  throws Throwable  {
      //caseID:d5d1882b3010689fecee5ec813c67044
      //CoveredLines: [12, 894]
      //Input_0_JavaParser.RecordHeaderContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordHeaderContext0
      JavaParser.RecordHeaderContext javaParser_RecordHeaderContext0 = mock(JavaParser.RecordHeaderContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterRecordHeader
      javaParserBaseListener0.enterRecordHeader(javaParser_RecordHeaderContext0);
  }

  @Test(timeout = 4000)
  public void test_enterRequiresModifier_099()  throws Throwable  {
      //caseID:2e67fc97a8d89629b9eace85f0177247
      //CoveredLines: [12, 870]
      //Input_0_JavaParser.RequiresModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RequiresModifierContext0
      JavaParser.RequiresModifierContext javaParser_RequiresModifierContext0 = mock(JavaParser.RequiresModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterRequiresModifier
      javaParserBaseListener0.enterRequiresModifier(javaParser_RequiresModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_enterResource_100()  throws Throwable  {
      //caseID:b8def38f91ae8807417edde0315e6c8f
      //CoveredLines: [12, 1074]
      //Input_0_JavaParser.ResourceContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ResourceContext0
      JavaParser.ResourceContext javaParser_ResourceContext0 = mock(JavaParser.ResourceContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterResource
      javaParserBaseListener0.enterResource(javaParser_ResourceContext0);
  }

  @Test(timeout = 4000)
  public void test_enterResourceSpecification_101()  throws Throwable  {
      //caseID:a8d3270fa28c8f2afe81ba7277328e3c
      //CoveredLines: [12, 1050]
      //Input_0_JavaParser.ResourceSpecificationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ResourceSpecificationContext0
      JavaParser.ResourceSpecificationContext javaParser_ResourceSpecificationContext0 = mock(JavaParser.ResourceSpecificationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterResourceSpecification
      javaParserBaseListener0.enterResourceSpecification(javaParser_ResourceSpecificationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterResources_102()  throws Throwable  {
      //caseID:23ee08d47a860ebbf31b37e3ec0ea687
      //CoveredLines: [12, 1062]
      //Input_0_JavaParser.ResourcesContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ResourcesContext0
      JavaParser.ResourcesContext javaParser_ResourcesContext0 = mock(JavaParser.ResourcesContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterResources
      javaParserBaseListener0.enterResources(javaParser_ResourcesContext0);
  }

  @Test(timeout = 4000)
  public void test_enterStatement_103()  throws Throwable  {
      //caseID:ce9a7a8392ec41f244062ef397358bc3
      //CoveredLines: [12, 1002]
      //Input_0_JavaParser.StatementContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_StatementContext0
      JavaParser.StatementContext javaParser_StatementContext0 = mock(JavaParser.StatementContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterStatement
      javaParserBaseListener0.enterStatement(javaParser_StatementContext0);
  }

  @Test(timeout = 4000)
  public void test_enterSuperSuffix_104()  throws Throwable  {
      //caseID:3f4070bb1a0ea9264f2ee85c95b51643
      //CoveredLines: [12, 1470]
      //Input_0_JavaParser.SuperSuffixContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SuperSuffixContext0
      JavaParser.SuperSuffixContext javaParser_SuperSuffixContext0 = mock(JavaParser.SuperSuffixContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterSuperSuffix
      javaParserBaseListener0.enterSuperSuffix(javaParser_SuperSuffixContext0);
  }

  @Test(timeout = 4000)
  public void test_enterSwitchBlockStatementGroup_105()  throws Throwable  {
      //caseID:ef4c35af57144fba95ea2484e4ef6ed4
      //CoveredLines: [12, 1086]
      //Input_0_JavaParser.SwitchBlockStatementGroupContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchBlockStatementGroupContext0
      JavaParser.SwitchBlockStatementGroupContext javaParser_SwitchBlockStatementGroupContext0 = mock(JavaParser.SwitchBlockStatementGroupContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterSwitchBlockStatementGroup
      javaParserBaseListener0.enterSwitchBlockStatementGroup(javaParser_SwitchBlockStatementGroupContext0);
  }

  @Test(timeout = 4000)
  public void test_enterSwitchExpression_106()  throws Throwable  {
      //caseID:d0f571c1efd7bdb0bd98cd416012932c
      //CoveredLines: [12, 1254]
      //Input_0_JavaParser.SwitchExpressionContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchExpressionContext0
      JavaParser.SwitchExpressionContext javaParser_SwitchExpressionContext0 = mock(JavaParser.SwitchExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterSwitchExpression
      javaParserBaseListener0.enterSwitchExpression(javaParser_SwitchExpressionContext0);
  }

  @Test(timeout = 4000)
  public void test_enterSwitchLabel_107()  throws Throwable  {
      //caseID:cbf1df801d7210f67f8bc7f90373acc6
      //CoveredLines: [12, 1098]
      //Input_0_JavaParser.SwitchLabelContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchLabelContext0
      JavaParser.SwitchLabelContext javaParser_SwitchLabelContext0 = mock(JavaParser.SwitchLabelContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterSwitchLabel
      javaParserBaseListener0.enterSwitchLabel(javaParser_SwitchLabelContext0);
  }

  @Test(timeout = 4000)
  public void test_enterSwitchLabeledRule_108()  throws Throwable  {
      //caseID:f707b555321344065377df15efcc1029
      //CoveredLines: [12, 1266]
      //Input_0_JavaParser.SwitchLabeledRuleContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchLabeledRuleContext0
      JavaParser.SwitchLabeledRuleContext javaParser_SwitchLabeledRuleContext0 = mock(JavaParser.SwitchLabeledRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterSwitchLabeledRule
      javaParserBaseListener0.enterSwitchLabeledRule(javaParser_SwitchLabeledRuleContext0);
  }

  @Test(timeout = 4000)
  public void test_enterSwitchRuleOutcome_109()  throws Throwable  {
      //caseID:c48de9c237678c91ff63aeccd6d3aa65
      //CoveredLines: [12, 1290]
      //Input_0_JavaParser.SwitchRuleOutcomeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchRuleOutcomeContext0
      JavaParser.SwitchRuleOutcomeContext javaParser_SwitchRuleOutcomeContext0 = mock(JavaParser.SwitchRuleOutcomeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterSwitchRuleOutcome
      javaParserBaseListener0.enterSwitchRuleOutcome(javaParser_SwitchRuleOutcomeContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeArgument_110()  throws Throwable  {
      //caseID:633c249a52935f8aa13410848deef49a
      //CoveredLines: [12, 510]
      //Input_0_JavaParser.TypeArgumentContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeArgumentContext0
      JavaParser.TypeArgumentContext javaParser_TypeArgumentContext0 = mock(JavaParser.TypeArgumentContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeArgument
      javaParserBaseListener0.enterTypeArgument(javaParser_TypeArgumentContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeArguments_111()  throws Throwable  {
      //caseID:6ffee8a87daadff81371c0a05ef0fcc8
      //CoveredLines: [12, 1458]
      //Input_0_JavaParser.TypeArgumentsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeArgumentsContext0
      JavaParser.TypeArgumentsContext javaParser_TypeArgumentsContext0 = mock(JavaParser.TypeArgumentsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeArguments
      javaParserBaseListener0.enterTypeArguments(javaParser_TypeArgumentsContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeArgumentsOrDiamond_112()  throws Throwable  {
      //caseID:bbcbc0ff8d4cb3a6cb15b56756f87e0c
      //CoveredLines: [12, 1386]
      //Input_0_JavaParser.TypeArgumentsOrDiamondContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeArgumentsOrDiamondContext0
      JavaParser.TypeArgumentsOrDiamondContext javaParser_TypeArgumentsOrDiamondContext0 = mock(JavaParser.TypeArgumentsOrDiamondContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeArgumentsOrDiamond
      javaParserBaseListener0.enterTypeArgumentsOrDiamond(javaParser_TypeArgumentsOrDiamondContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeBound_113()  throws Throwable  {
      //caseID:e34eb6d78bdb062126ff051607aae0bf
      //CoveredLines: [12, 138]
      //Input_0_JavaParser.TypeBoundContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeBoundContext0
      JavaParser.TypeBoundContext javaParser_TypeBoundContext0 = mock(JavaParser.TypeBoundContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeBound
      javaParserBaseListener0.enterTypeBound(javaParser_TypeBoundContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeDeclaration_114()  throws Throwable  {
      //caseID:b284631392a285de3bf0fbfbab0ada4b
      //CoveredLines: [12, 54]
      //Input_0_JavaParser.TypeDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeDeclarationContext0
      JavaParser.TypeDeclarationContext javaParser_TypeDeclarationContext0 = mock(JavaParser.TypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeDeclaration
      javaParserBaseListener0.enterTypeDeclaration(javaParser_TypeDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeList_115()  throws Throwable  {
      //caseID:ff30d4acab5db4f174bdaca5ce5cc6a7
      //CoveredLines: [12, 1422]
      //Input_0_JavaParser.TypeListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeListContext0
      JavaParser.TypeListContext javaParser_TypeListContext0 = mock(JavaParser.TypeListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeList
      javaParserBaseListener0.enterTypeList(javaParser_TypeListContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeParameter_116()  throws Throwable  {
      //caseID:21ad03431ad41ffb223b5aa6fae2f52c
      //CoveredLines: [12, 126]
      //Input_0_JavaParser.TypeParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeParameterContext0
      JavaParser.TypeParameterContext javaParser_TypeParameterContext0 = mock(JavaParser.TypeParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeParameter
      javaParserBaseListener0.enterTypeParameter(javaParser_TypeParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeParameters_117()  throws Throwable  {
      //caseID:c1fa7ccd0033cf8a4021a7388d5ece62
      //CoveredLines: [12, 114]
      //Input_0_JavaParser.TypeParametersContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeParametersContext0
      JavaParser.TypeParametersContext javaParser_TypeParametersContext0 = mock(JavaParser.TypeParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeParameters
      javaParserBaseListener0.enterTypeParameters(javaParser_TypeParametersContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeType_118()  throws Throwable  {
      //caseID:50fcb43456b84226fead5b0e8bce4a51
      //CoveredLines: [12, 1434]
      //Input_0_JavaParser.TypeTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeTypeContext0
      JavaParser.TypeTypeContext javaParser_TypeTypeContext0 = mock(JavaParser.TypeTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeType
      javaParserBaseListener0.enterTypeType(javaParser_TypeTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_enterTypeTypeOrVoid_119()  throws Throwable  {
      //caseID:377be8f133bb53e941736f0e27babc7a
      //CoveredLines: [12, 282]
      //Input_0_JavaParser.TypeTypeOrVoidContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeTypeOrVoidContext0
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterTypeTypeOrVoid
      javaParserBaseListener0.enterTypeTypeOrVoid(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_enterVariableDeclarator_120()  throws Throwable  {
      //caseID:ddd4ceb9f48f363e4fab004b5de63f0a
      //CoveredLines: [12, 450]
      //Input_0_JavaParser.VariableDeclaratorContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableDeclaratorContext0
      JavaParser.VariableDeclaratorContext javaParser_VariableDeclaratorContext0 = mock(JavaParser.VariableDeclaratorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterVariableDeclarator
      javaParserBaseListener0.enterVariableDeclarator(javaParser_VariableDeclaratorContext0);
  }

  @Test(timeout = 4000)
  public void test_enterVariableDeclaratorId_121()  throws Throwable  {
      //caseID:2db5cb8d492301388ea2fbdf1a6a34c7
      //CoveredLines: [12, 462]
      //Input_0_JavaParser.VariableDeclaratorIdContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableDeclaratorIdContext0
      JavaParser.VariableDeclaratorIdContext javaParser_VariableDeclaratorIdContext0 = mock(JavaParser.VariableDeclaratorIdContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterVariableDeclaratorId
      javaParserBaseListener0.enterVariableDeclaratorId(javaParser_VariableDeclaratorIdContext0);
  }

  @Test(timeout = 4000)
  public void test_enterVariableDeclarators_122()  throws Throwable  {
      //caseID:f296cc46c95aacf2d0f98922f1623018
      //CoveredLines: [12, 438]
      //Input_0_JavaParser.VariableDeclaratorsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableDeclaratorsContext0
      JavaParser.VariableDeclaratorsContext javaParser_VariableDeclaratorsContext0 = mock(JavaParser.VariableDeclaratorsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterVariableDeclarators
      javaParserBaseListener0.enterVariableDeclarators(javaParser_VariableDeclaratorsContext0);
  }

  @Test(timeout = 4000)
  public void test_enterVariableInitializer_123()  throws Throwable  {
      //caseID:06b3b3e9e4d772b9001a08c8301d969e
      //CoveredLines: [12, 474]
      //Input_0_JavaParser.VariableInitializerContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableInitializerContext0
      JavaParser.VariableInitializerContext javaParser_VariableInitializerContext0 = mock(JavaParser.VariableInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterVariableInitializer
      javaParserBaseListener0.enterVariableInitializer(javaParser_VariableInitializerContext0);
  }

  @Test(timeout = 4000)
  public void test_enterVariableModifier_124()  throws Throwable  {
      //caseID:ac764842ffda2f5227342158c26a23ef
      //CoveredLines: [12, 90]
      //Input_0_JavaParser.VariableModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableModifierContext0
      JavaParser.VariableModifierContext javaParser_VariableModifierContext0 = mock(JavaParser.VariableModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: enterVariableModifier
      javaParserBaseListener0.enterVariableModifier(javaParser_VariableModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAltAnnotationQualifiedName_125()  throws Throwable  {
      //caseID:9c05249f44cd08d8a852c30260463b59
      //CoveredLines: [12, 672]
      //Input_0_JavaParser.AltAnnotationQualifiedNameContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AltAnnotationQualifiedNameContext0
      JavaParser.AltAnnotationQualifiedNameContext javaParser_AltAnnotationQualifiedNameContext0 = mock(JavaParser.AltAnnotationQualifiedNameContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAltAnnotationQualifiedName
      javaParserBaseListener0.exitAltAnnotationQualifiedName(javaParser_AltAnnotationQualifiedNameContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAnnotation_126()  throws Throwable  {
      //caseID:a56f265887b249b9a4084a2ae839f045
      //CoveredLines: [12, 684]
      //Input_0_JavaParser.AnnotationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationContext0
      JavaParser.AnnotationContext javaParser_AnnotationContext0 = mock(JavaParser.AnnotationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAnnotation
      javaParserBaseListener0.exitAnnotation(javaParser_AnnotationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAnnotationConstantRest_127()  throws Throwable  {
      //caseID:1bf1b076db793c0d180b81be1356789f
      //CoveredLines: [12, 816]
      //Input_0_JavaParser.AnnotationConstantRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationConstantRestContext0
      JavaParser.AnnotationConstantRestContext javaParser_AnnotationConstantRestContext0 = mock(JavaParser.AnnotationConstantRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAnnotationConstantRest
      javaParserBaseListener0.exitAnnotationConstantRest(javaParser_AnnotationConstantRestContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAnnotationMethodOrConstantRest_128()  throws Throwable  {
      //caseID:bde5c5b085496c1076ad0001af2afa11
      //CoveredLines: [12, 792]
      //Input_0_JavaParser.AnnotationMethodOrConstantRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationMethodOrConstantRestContext0
      JavaParser.AnnotationMethodOrConstantRestContext javaParser_AnnotationMethodOrConstantRestContext0 = mock(JavaParser.AnnotationMethodOrConstantRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAnnotationMethodOrConstantRest
      javaParserBaseListener0.exitAnnotationMethodOrConstantRest(javaParser_AnnotationMethodOrConstantRestContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAnnotationMethodRest_129()  throws Throwable  {
      //caseID:d8847fe9dec49304550a83a4ed50336d
      //CoveredLines: [12, 804]
      //Input_0_JavaParser.AnnotationMethodRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationMethodRestContext0
      JavaParser.AnnotationMethodRestContext javaParser_AnnotationMethodRestContext0 = mock(JavaParser.AnnotationMethodRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAnnotationMethodRest
      javaParserBaseListener0.exitAnnotationMethodRest(javaParser_AnnotationMethodRestContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAnnotationTypeBody_130()  throws Throwable  {
      //caseID:427ce1879696f125f2cd177914fc6803
      //CoveredLines: [12, 756]
      //Input_0_JavaParser.AnnotationTypeBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationTypeBodyContext0
      JavaParser.AnnotationTypeBodyContext javaParser_AnnotationTypeBodyContext0 = mock(JavaParser.AnnotationTypeBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAnnotationTypeBody
      javaParserBaseListener0.exitAnnotationTypeBody(javaParser_AnnotationTypeBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAnnotationTypeDeclaration_131()  throws Throwable  {
      //caseID:163adc050b019614165426bbd0443a44
      //CoveredLines: [12, 744]
      //Input_0_JavaParser.AnnotationTypeDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationTypeDeclarationContext0
      JavaParser.AnnotationTypeDeclarationContext javaParser_AnnotationTypeDeclarationContext0 = mock(JavaParser.AnnotationTypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAnnotationTypeDeclaration
      javaParserBaseListener0.exitAnnotationTypeDeclaration(javaParser_AnnotationTypeDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAnnotationTypeElementDeclaration_132()  throws Throwable  {
      //caseID:33840431e1443bc50b6df6e7c4f4797a
      //CoveredLines: [12, 768]
      //Input_0_JavaParser.AnnotationTypeElementDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationTypeElementDeclarationContext0
      JavaParser.AnnotationTypeElementDeclarationContext javaParser_AnnotationTypeElementDeclarationContext0 = mock(JavaParser.AnnotationTypeElementDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAnnotationTypeElementDeclaration
      javaParserBaseListener0.exitAnnotationTypeElementDeclaration(javaParser_AnnotationTypeElementDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitAnnotationTypeElementRest_133()  throws Throwable  {
      //caseID:70dc71afee88b8c90c74180a4ae35dea
      //CoveredLines: [12, 780]
      //Input_0_JavaParser.AnnotationTypeElementRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_AnnotationTypeElementRestContext0
      JavaParser.AnnotationTypeElementRestContext javaParser_AnnotationTypeElementRestContext0 = mock(JavaParser.AnnotationTypeElementRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitAnnotationTypeElementRest
      javaParserBaseListener0.exitAnnotationTypeElementRest(javaParser_AnnotationTypeElementRestContext0);
  }

  @Test(timeout = 4000)
  public void test_exitArguments_134()  throws Throwable  {
      //caseID:26415c885d21998cb314c18a2ea3931d
      //CoveredLines: [12, 1500]
      //Input_0_JavaParser.ArgumentsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ArgumentsContext0
      JavaParser.ArgumentsContext javaParser_ArgumentsContext0 = mock(JavaParser.ArgumentsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitArguments
      javaParserBaseListener0.exitArguments(javaParser_ArgumentsContext0);
  }

  @Test(timeout = 4000)
  public void test_exitArrayCreatorRest_135()  throws Throwable  {
      //caseID:7d1aff867fb14a6b7078c526e6c2fe8a
      //CoveredLines: [12, 1356]
      //Input_0_JavaParser.ArrayCreatorRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ArrayCreatorRestContext0
      JavaParser.ArrayCreatorRestContext javaParser_ArrayCreatorRestContext0 = mock(JavaParser.ArrayCreatorRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitArrayCreatorRest
      javaParserBaseListener0.exitArrayCreatorRest(javaParser_ArrayCreatorRestContext0);
  }

  @Test(timeout = 4000)
  public void test_exitArrayInitializer_136()  throws Throwable  {
      //caseID:c0415ccf5512ec3978001915b932804d
      //CoveredLines: [12, 492]
      //Input_0_JavaParser.ArrayInitializerContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ArrayInitializerContext0
      JavaParser.ArrayInitializerContext javaParser_ArrayInitializerContext0 = mock(JavaParser.ArrayInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitArrayInitializer
      javaParserBaseListener0.exitArrayInitializer(javaParser_ArrayInitializerContext0);
  }

  @Test(timeout = 4000)
  public void test_exitBlock_137()  throws Throwable  {
      //caseID:513f91cda01d9f9eca6fe5b93cecd13b
      //CoveredLines: [12, 948]
      //Input_0_JavaParser.BlockContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_BlockContext0
      JavaParser.BlockContext javaParser_BlockContext0 = mock(JavaParser.BlockContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitBlock
      javaParserBaseListener0.exitBlock(javaParser_BlockContext0);
  }

  @Test(timeout = 4000)
  public void test_exitBlockStatement_138()  throws Throwable  {
      //caseID:8495b341b4271511464761040c9de25f
      //CoveredLines: [12, 960]
      //Input_0_JavaParser.BlockStatementContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_BlockStatementContext0
      JavaParser.BlockStatementContext javaParser_BlockStatementContext0 = mock(JavaParser.BlockStatementContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitBlockStatement
      javaParserBaseListener0.exitBlockStatement(javaParser_BlockStatementContext0);
  }

  @Test(timeout = 4000)
  public void test_exitCatchClause_139()  throws Throwable  {
      //caseID:5347aaedc4586c6cec22a011ac6aeaa4
      //CoveredLines: [12, 1020]
      //Input_0_JavaParser.CatchClauseContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CatchClauseContext0
      JavaParser.CatchClauseContext javaParser_CatchClauseContext0 = mock(JavaParser.CatchClauseContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitCatchClause
      javaParserBaseListener0.exitCatchClause(javaParser_CatchClauseContext0);
  }

  @Test(timeout = 4000)
  public void test_exitCatchType_140()  throws Throwable  {
      //caseID:911b17a06e0568a785f62ec1f47aeee6
      //CoveredLines: [12, 1032]
      //Input_0_JavaParser.CatchTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CatchTypeContext0
      JavaParser.CatchTypeContext javaParser_CatchTypeContext0 = mock(JavaParser.CatchTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitCatchType
      javaParserBaseListener0.exitCatchType(javaParser_CatchTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_exitClassBody_141()  throws Throwable  {
      //caseID:d551d9668e1c6bfa0bc76bbae05720aa
      //CoveredLines: [12, 216]
      //Input_0_JavaParser.ClassBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassBodyContext0
      JavaParser.ClassBodyContext javaParser_ClassBodyContext0 = mock(JavaParser.ClassBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitClassBody
      javaParserBaseListener0.exitClassBody(javaParser_ClassBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_exitClassBodyDeclaration_142()  throws Throwable  {
      //caseID:6cdf51f7c3f5e630ab834b9e6e475f39
      //CoveredLines: [12, 240]
      //Input_0_JavaParser.ClassBodyDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassBodyDeclarationContext0
      JavaParser.ClassBodyDeclarationContext javaParser_ClassBodyDeclarationContext0 = mock(JavaParser.ClassBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitClassBodyDeclaration
      javaParserBaseListener0.exitClassBodyDeclaration(javaParser_ClassBodyDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitClassCreatorRest_143()  throws Throwable  {
      //caseID:5fddd44d24f8bfbeff0e0e68726a1e56
      //CoveredLines: [12, 1368]
      //Input_0_JavaParser.ClassCreatorRestContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassCreatorRestContext0
      JavaParser.ClassCreatorRestContext javaParser_ClassCreatorRestContext0 = mock(JavaParser.ClassCreatorRestContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitClassCreatorRest
      javaParserBaseListener0.exitClassCreatorRest(javaParser_ClassCreatorRestContext0);
  }

  @Test(timeout = 4000)
  public void test_exitClassDeclaration_144()  throws Throwable  {
      //caseID:87ff26dfd26a3e8ef146ec85fad67fa3
      //CoveredLines: [12, 108]
      //Input_0_JavaParser.ClassDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassDeclarationContext0
      JavaParser.ClassDeclarationContext javaParser_ClassDeclarationContext0 = mock(JavaParser.ClassDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitClassDeclaration
      javaParserBaseListener0.exitClassDeclaration(javaParser_ClassDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitClassOrInterfaceModifier_145()  throws Throwable  {
      //caseID:b9415cf1bda65ce7dd2da40f3792ff14
      //CoveredLines: [12, 84]
      //Input_0_JavaParser.ClassOrInterfaceModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassOrInterfaceModifierContext0
      JavaParser.ClassOrInterfaceModifierContext javaParser_ClassOrInterfaceModifierContext0 = mock(JavaParser.ClassOrInterfaceModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitClassOrInterfaceModifier
      javaParserBaseListener0.exitClassOrInterfaceModifier(javaParser_ClassOrInterfaceModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_exitClassOrInterfaceType_146()  throws Throwable  {
      //caseID:68430145a8aebfba1e9a689a3417225f
      //CoveredLines: [12, 504]
      //Input_0_JavaParser.ClassOrInterfaceTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassOrInterfaceTypeContext0
      JavaParser.ClassOrInterfaceTypeContext javaParser_ClassOrInterfaceTypeContext0 = mock(JavaParser.ClassOrInterfaceTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitClassOrInterfaceType
      javaParserBaseListener0.exitClassOrInterfaceType(javaParser_ClassOrInterfaceTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_exitClassType_147()  throws Throwable  {
      //caseID:00efa32a434e1b09f7e4e260d16bec86
      //CoveredLines: [12, 1308]
      //Input_0_JavaParser.ClassTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ClassTypeContext0
      JavaParser.ClassTypeContext javaParser_ClassTypeContext0 = mock(JavaParser.ClassTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitClassType
      javaParserBaseListener0.exitClassType(javaParser_ClassTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_exitCompilationUnit_148()  throws Throwable  {
      //caseID:d344435adb81bba9521437e35e6c47f7
      //CoveredLines: [12, 24]
      //Input_0_JavaParser.CompilationUnitContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CompilationUnitContext0
      JavaParser.CompilationUnitContext javaParser_CompilationUnitContext0 = mock(JavaParser.CompilationUnitContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitCompilationUnit
      javaParserBaseListener0.exitCompilationUnit(javaParser_CompilationUnitContext0);
  }

  @Test(timeout = 4000)
  public void test_exitConstDeclaration_149()  throws Throwable  {
      //caseID:1ede00affe0be55d02259f30b2baa9f9
      //CoveredLines: [12, 372]
      //Input_0_JavaParser.ConstDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ConstDeclarationContext0
      JavaParser.ConstDeclarationContext javaParser_ConstDeclarationContext0 = mock(JavaParser.ConstDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitConstDeclaration
      javaParserBaseListener0.exitConstDeclaration(javaParser_ConstDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitConstantDeclarator_150()  throws Throwable  {
      //caseID:718348f233cfb6578b2f26123daa6b47
      //CoveredLines: [12, 384]
      //Input_0_JavaParser.ConstantDeclaratorContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ConstantDeclaratorContext0
      JavaParser.ConstantDeclaratorContext javaParser_ConstantDeclaratorContext0 = mock(JavaParser.ConstantDeclaratorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitConstantDeclarator
      javaParserBaseListener0.exitConstantDeclarator(javaParser_ConstantDeclaratorContext0);
  }

  @Test(timeout = 4000)
  public void test_exitConstructorDeclaration_151()  throws Throwable  {
      //caseID:99b15b6448d8d10509fb25bde279429c
      //CoveredLines: [12, 324]
      //Input_0_JavaParser.ConstructorDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ConstructorDeclarationContext0
      JavaParser.ConstructorDeclarationContext javaParser_ConstructorDeclarationContext0 = mock(JavaParser.ConstructorDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitConstructorDeclaration
      javaParserBaseListener0.exitConstructorDeclaration(javaParser_ConstructorDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitCreatedName_152()  throws Throwable  {
      //caseID:f492f648f38fcf0bcf98b34d153d9319
      //CoveredLines: [12, 1332]
      //Input_0_JavaParser.CreatedNameContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CreatedNameContext0
      JavaParser.CreatedNameContext javaParser_CreatedNameContext0 = mock(JavaParser.CreatedNameContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitCreatedName
      javaParserBaseListener0.exitCreatedName(javaParser_CreatedNameContext0);
  }

  @Test(timeout = 4000)
  public void test_exitCreator_153()  throws Throwable  {
      //caseID:********************************
      //CoveredLines: [12, 1320]
      //Input_0_JavaParser.CreatorContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_CreatorContext0
      JavaParser.CreatorContext javaParser_CreatorContext0 = mock(JavaParser.CreatorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitCreator
      javaParserBaseListener0.exitCreator(javaParser_CreatorContext0);
  }

  @Test(timeout = 4000)
  public void test_exitDefaultValue_154()  throws Throwable  {
      //caseID:cd6e847bbf466715acfcb902b10f5d47
      //CoveredLines: [12, 828]
      //Input_0_JavaParser.DefaultValueContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_DefaultValueContext0
      JavaParser.DefaultValueContext javaParser_DefaultValueContext0 = mock(JavaParser.DefaultValueContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitDefaultValue
      javaParserBaseListener0.exitDefaultValue(javaParser_DefaultValueContext0);
  }

  @Test(timeout = 4000)
  public void test_exitElementValue_155()  throws Throwable  {
      //caseID:4dac5fc307786fe1d29deff5818c1e93
      //CoveredLines: [12, 720]
      //Input_0_JavaParser.ElementValueContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ElementValueContext0
      JavaParser.ElementValueContext javaParser_ElementValueContext0 = mock(JavaParser.ElementValueContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitElementValue
      javaParserBaseListener0.exitElementValue(javaParser_ElementValueContext0);
  }

  @Test(timeout = 4000)
  public void test_exitElementValueArrayInitializer_156()  throws Throwable  {
      //caseID:eaa4cf6754679dad4cd736794bee2bcf
      //CoveredLines: [12, 732]
      //Input_0_JavaParser.ElementValueArrayInitializerContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ElementValueArrayInitializerContext0
      JavaParser.ElementValueArrayInitializerContext javaParser_ElementValueArrayInitializerContext0 = mock(JavaParser.ElementValueArrayInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitElementValueArrayInitializer
      javaParserBaseListener0.exitElementValueArrayInitializer(javaParser_ElementValueArrayInitializerContext0);
  }

  @Test(timeout = 4000)
  public void test_exitElementValuePair_157()  throws Throwable  {
      //caseID:64d6da2c8c0462eb0abf070ed5fe3054
      //CoveredLines: [12, 708]
      //Input_0_JavaParser.ElementValuePairContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ElementValuePairContext0
      JavaParser.ElementValuePairContext javaParser_ElementValuePairContext0 = mock(JavaParser.ElementValuePairContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitElementValuePair
      javaParserBaseListener0.exitElementValuePair(javaParser_ElementValuePairContext0);
  }

  @Test(timeout = 4000)
  public void test_exitElementValuePairs_158()  throws Throwable  {
      //caseID:2f1267860fb7cefde3e9c052754d5cf4
      //CoveredLines: [12, 696]
      //Input_0_JavaParser.ElementValuePairsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ElementValuePairsContext0
      JavaParser.ElementValuePairsContext javaParser_ElementValuePairsContext0 = mock(JavaParser.ElementValuePairsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitElementValuePairs
      javaParserBaseListener0.exitElementValuePairs(javaParser_ElementValuePairsContext0);
  }

  @Test(timeout = 4000)
  public void test_exitEnhancedForControl_159()  throws Throwable  {
      //caseID:91a61ab37184452355367d543112e0d0
      //CoveredLines: [12, 1140]
      //Input_0_JavaParser.EnhancedForControlContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnhancedForControlContext0
      JavaParser.EnhancedForControlContext javaParser_EnhancedForControlContext0 = mock(JavaParser.EnhancedForControlContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitEnhancedForControl
      javaParserBaseListener0.exitEnhancedForControl(javaParser_EnhancedForControlContext0);
  }

  @Test(timeout = 4000)
  public void test_exitEnumBodyDeclarations_160()  throws Throwable  {
      //caseID:edd3a3a2440c9cc630cbcf93ae3f3c41
      //CoveredLines: [12, 192]
      //Input_0_JavaParser.EnumBodyDeclarationsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnumBodyDeclarationsContext0
      JavaParser.EnumBodyDeclarationsContext javaParser_EnumBodyDeclarationsContext0 = mock(JavaParser.EnumBodyDeclarationsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitEnumBodyDeclarations
      javaParserBaseListener0.exitEnumBodyDeclarations(javaParser_EnumBodyDeclarationsContext0);
  }

  @Test(timeout = 4000)
  public void test_exitEnumConstant_161()  throws Throwable  {
      //caseID:0752ea12ef5977b8d5c0f2f8b1a7a49e
      //CoveredLines: [12, 180]
      //Input_0_JavaParser.EnumConstantContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnumConstantContext0
      JavaParser.EnumConstantContext javaParser_EnumConstantContext0 = mock(JavaParser.EnumConstantContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitEnumConstant
      javaParserBaseListener0.exitEnumConstant(javaParser_EnumConstantContext0);
  }

  @Test(timeout = 4000)
  public void test_exitEnumConstants_162()  throws Throwable  {
      //caseID:4f97eeb63e17a1bfc36ab5fc8a612950
      //CoveredLines: [12, 168]
      //Input_0_JavaParser.EnumConstantsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnumConstantsContext0
      JavaParser.EnumConstantsContext javaParser_EnumConstantsContext0 = mock(JavaParser.EnumConstantsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitEnumConstants
      javaParserBaseListener0.exitEnumConstants(javaParser_EnumConstantsContext0);
  }

  @Test(timeout = 4000)
  public void test_exitEnumDeclaration_163()  throws Throwable  {
      //caseID:d609643d24885215005e7123a5983268
      //CoveredLines: [12, 156]
      //Input_0_JavaParser.EnumDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_EnumDeclarationContext0
      JavaParser.EnumDeclarationContext javaParser_EnumDeclarationContext0 = mock(JavaParser.EnumDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitEnumDeclaration
      javaParserBaseListener0.exitEnumDeclaration(javaParser_EnumDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitEveryRule_164()  throws Throwable  {
      //caseID:347c74af9cee002f16b0f948dac49ae1
      //CoveredLines: [12, 1513]
      //Input_0_ParserRuleContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock parserRuleContext0
      ParserRuleContext parserRuleContext0 = mock(ParserRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitEveryRule
      javaParserBaseListener0.exitEveryRule(parserRuleContext0);
  }

  @Test(timeout = 4000)
  public void test_exitExplicitGenericInvocation_165()  throws Throwable  {
      //caseID:c9c2360a191d073afab3f942d6cfb251
      //CoveredLines: [12, 1380]
      //Input_0_JavaParser.ExplicitGenericInvocationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ExplicitGenericInvocationContext0
      JavaParser.ExplicitGenericInvocationContext javaParser_ExplicitGenericInvocationContext0 = mock(JavaParser.ExplicitGenericInvocationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitExplicitGenericInvocation
      javaParserBaseListener0.exitExplicitGenericInvocation(javaParser_ExplicitGenericInvocationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitExplicitGenericInvocationSuffix_166()  throws Throwable  {
      //caseID:805dde38b350d26d9a3962eb59dbaa36
      //CoveredLines: [12, 1488]
      //Input_0_JavaParser.ExplicitGenericInvocationSuffixContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ExplicitGenericInvocationSuffixContext0
      JavaParser.ExplicitGenericInvocationSuffixContext javaParser_ExplicitGenericInvocationSuffixContext0 = mock(JavaParser.ExplicitGenericInvocationSuffixContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitExplicitGenericInvocationSuffix
      javaParserBaseListener0.exitExplicitGenericInvocationSuffix(javaParser_ExplicitGenericInvocationSuffixContext0);
  }

  @Test(timeout = 4000)
  public void test_exitExpression_167()  throws Throwable  {
      //caseID:6f67cf1d11bd223176e316d13a0a67db
      //CoveredLines: [12, 1188]
      //Input_0_JavaParser.ExpressionContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ExpressionContext0
      JavaParser.ExpressionContext javaParser_ExpressionContext0 = mock(JavaParser.ExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitExpression
      javaParserBaseListener0.exitExpression(javaParser_ExpressionContext0);
  }

  @Test(timeout = 4000)
  public void test_exitExpressionList_168()  throws Throwable  {
      //caseID:df3eb8c2bf122383ac59770ae35a215b
      //CoveredLines: [12, 1164]
      //Input_0_JavaParser.ExpressionListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ExpressionListContext0
      JavaParser.ExpressionListContext javaParser_ExpressionListContext0 = mock(JavaParser.ExpressionListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitExpressionList
      javaParserBaseListener0.exitExpressionList(javaParser_ExpressionListContext0);
  }

  @Test(timeout = 4000)
  public void test_exitFieldDeclaration_169()  throws Throwable  {
      //caseID:5f7d14d4c56036dd00f82e22fcc34e80
      //CoveredLines: [12, 336]
      //Input_0_JavaParser.FieldDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FieldDeclarationContext0
      JavaParser.FieldDeclarationContext javaParser_FieldDeclarationContext0 = mock(JavaParser.FieldDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitFieldDeclaration
      javaParserBaseListener0.exitFieldDeclaration(javaParser_FieldDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitFinallyBlock_170()  throws Throwable  {
      //caseID:********************************
      //CoveredLines: [12, 1044]
      //Input_0_JavaParser.FinallyBlockContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FinallyBlockContext0
      JavaParser.FinallyBlockContext javaParser_FinallyBlockContext0 = mock(JavaParser.FinallyBlockContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitFinallyBlock
      javaParserBaseListener0.exitFinallyBlock(javaParser_FinallyBlockContext0);
  }

  @Test(timeout = 4000)
  public void test_exitFloatLiteral_171()  throws Throwable  {
      //caseID:c391f065c32dbbd4dc771ea58893e9e1
      //CoveredLines: [12, 660]
      //Input_0_JavaParser.FloatLiteralContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FloatLiteralContext0
      JavaParser.FloatLiteralContext javaParser_FloatLiteralContext0 = mock(JavaParser.FloatLiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitFloatLiteral
      javaParserBaseListener0.exitFloatLiteral(javaParser_FloatLiteralContext0);
  }

  @Test(timeout = 4000)
  public void test_exitForControl_172()  throws Throwable  {
      //caseID:573a827c4eeb85fc2b710d60658d4c18
      //CoveredLines: [12, 1116]
      //Input_0_JavaParser.ForControlContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ForControlContext0
      JavaParser.ForControlContext javaParser_ForControlContext0 = mock(JavaParser.ForControlContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitForControl
      javaParserBaseListener0.exitForControl(javaParser_ForControlContext0);
  }

  @Test(timeout = 4000)
  public void test_exitForInit_173()  throws Throwable  {
      //caseID:2813665c4cc0f924374751944a2fc263
      //CoveredLines: [12, 1128]
      //Input_0_JavaParser.ForInitContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ForInitContext0
      JavaParser.ForInitContext javaParser_ForInitContext0 = mock(JavaParser.ForInitContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitForInit
      javaParserBaseListener0.exitForInit(javaParser_ForInitContext0);
  }

  @Test(timeout = 4000)
  public void test_exitFormalParameter_174()  throws Throwable  {
      //caseID:3bf0aa1de23ea78a113f6676715365ce
      //CoveredLines: [12, 576]
      //Input_0_JavaParser.FormalParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FormalParameterContext0
      JavaParser.FormalParameterContext javaParser_FormalParameterContext0 = mock(JavaParser.FormalParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitFormalParameter
      javaParserBaseListener0.exitFormalParameter(javaParser_FormalParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_exitFormalParameterList_175()  throws Throwable  {
      //caseID:d8e25426ee2bfa376541d2c1f40696d3
      //CoveredLines: [12, 564]
      //Input_0_JavaParser.FormalParameterListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FormalParameterListContext0
      JavaParser.FormalParameterListContext javaParser_FormalParameterListContext0 = mock(JavaParser.FormalParameterListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitFormalParameterList
      javaParserBaseListener0.exitFormalParameterList(javaParser_FormalParameterListContext0);
  }

  @Test(timeout = 4000)
  public void test_exitFormalParameters_176()  throws Throwable  {
      //caseID:2b336b396a1dd72bb8c3979e419fb86a
      //CoveredLines: [12, 540]
      //Input_0_JavaParser.FormalParametersContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_FormalParametersContext0
      JavaParser.FormalParametersContext javaParser_FormalParametersContext0 = mock(JavaParser.FormalParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitFormalParameters
      javaParserBaseListener0.exitFormalParameters(javaParser_FormalParametersContext0);
  }

  @Test(timeout = 4000)
  public void test_exitGenericConstructorDeclaration_177()  throws Throwable  {
      //caseID:6ea1a6bb843cdbf728bacb7b0c49dcce
      //CoveredLines: [12, 312]
      //Input_0_JavaParser.GenericConstructorDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_GenericConstructorDeclarationContext0
      JavaParser.GenericConstructorDeclarationContext javaParser_GenericConstructorDeclarationContext0 = mock(JavaParser.GenericConstructorDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitGenericConstructorDeclaration
      javaParserBaseListener0.exitGenericConstructorDeclaration(javaParser_GenericConstructorDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitGenericInterfaceMethodDeclaration_178()  throws Throwable  {
      //caseID:efa099eb1b64200786ff76e173c36e39
      //CoveredLines: [12, 420]
      //Input_0_JavaParser.GenericInterfaceMethodDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_GenericInterfaceMethodDeclarationContext0
      JavaParser.GenericInterfaceMethodDeclarationContext javaParser_GenericInterfaceMethodDeclarationContext0 = mock(JavaParser.GenericInterfaceMethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitGenericInterfaceMethodDeclaration
      javaParserBaseListener0.exitGenericInterfaceMethodDeclaration(javaParser_GenericInterfaceMethodDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitGenericMethodDeclaration_179()  throws Throwable  {
      //caseID:1d7f9f1a4c7f3d574e9452a2de656800
      //CoveredLines: [12, 300]
      //Input_0_JavaParser.GenericMethodDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_GenericMethodDeclarationContext0
      JavaParser.GenericMethodDeclarationContext javaParser_GenericMethodDeclarationContext0 = mock(JavaParser.GenericMethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitGenericMethodDeclaration
      javaParserBaseListener0.exitGenericMethodDeclaration(javaParser_GenericMethodDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitGuardedPattern_180()  throws Throwable  {
      //caseID:f503f4f60648f40daf42f596c22e4c2f
      //CoveredLines: [12, 1284]
      //Input_0_JavaParser.GuardedPatternContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_GuardedPatternContext0
      JavaParser.GuardedPatternContext javaParser_GuardedPatternContext0 = mock(JavaParser.GuardedPatternContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitGuardedPattern
      javaParserBaseListener0.exitGuardedPattern(javaParser_GuardedPatternContext0);
  }

  @Test(timeout = 4000)
  public void test_exitIdentifier_181()  throws Throwable  {
      //caseID:10854456706daf0ade6c0d00bfb548f0
      //CoveredLines: [12, 984]
      //Input_0_JavaParser.IdentifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_IdentifierContext0
      JavaParser.IdentifierContext javaParser_IdentifierContext0 = mock(JavaParser.IdentifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitIdentifier
      javaParserBaseListener0.exitIdentifier(javaParser_IdentifierContext0);
  }

  @Test(timeout = 4000)
  public void test_exitImportDeclaration_182()  throws Throwable  {
      //caseID:22e8e82f8537626fa962761baee96df9
      //CoveredLines: [12, 48]
      //Input_0_JavaParser.ImportDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ImportDeclarationContext0
      JavaParser.ImportDeclarationContext javaParser_ImportDeclarationContext0 = mock(JavaParser.ImportDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitImportDeclaration
      javaParserBaseListener0.exitImportDeclaration(javaParser_ImportDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitInnerCreator_183()  throws Throwable  {
      //caseID:8342c014be11e963aa1dc6d31e48685d
      //CoveredLines: [12, 1344]
      //Input_0_JavaParser.InnerCreatorContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InnerCreatorContext0
      JavaParser.InnerCreatorContext javaParser_InnerCreatorContext0 = mock(JavaParser.InnerCreatorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitInnerCreator
      javaParserBaseListener0.exitInnerCreator(javaParser_InnerCreatorContext0);
  }

  @Test(timeout = 4000)
  public void test_exitIntegerLiteral_184()  throws Throwable  {
      //caseID:f2094c4756cd7f1b148947fce2a994b2
      //CoveredLines: [12, 648]
      //Input_0_JavaParser.IntegerLiteralContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_IntegerLiteralContext0
      JavaParser.IntegerLiteralContext javaParser_IntegerLiteralContext0 = mock(JavaParser.IntegerLiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitIntegerLiteral
      javaParserBaseListener0.exitIntegerLiteral(javaParser_IntegerLiteralContext0);
  }

  @Test(timeout = 4000)
  public void test_exitInterfaceBody_185()  throws Throwable  {
      //caseID:da0426e3b9a7610b55b4bdbc34d4656a
      //CoveredLines: [12, 228]
      //Input_0_JavaParser.InterfaceBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceBodyContext0
      JavaParser.InterfaceBodyContext javaParser_InterfaceBodyContext0 = mock(JavaParser.InterfaceBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitInterfaceBody
      javaParserBaseListener0.exitInterfaceBody(javaParser_InterfaceBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_exitInterfaceBodyDeclaration_186()  throws Throwable  {
      //caseID:ff958cbf7cbaea91dd31d6ed8dfef6db
      //CoveredLines: [12, 348]
      //Input_0_JavaParser.InterfaceBodyDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceBodyDeclarationContext0
      JavaParser.InterfaceBodyDeclarationContext javaParser_InterfaceBodyDeclarationContext0 = mock(JavaParser.InterfaceBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitInterfaceBodyDeclaration
      javaParserBaseListener0.exitInterfaceBodyDeclaration(javaParser_InterfaceBodyDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitInterfaceCommonBodyDeclaration_187()  throws Throwable  {
      //caseID:3679c511f6437c18425daa50b0325e81
      //CoveredLines: [12, 432]
      //Input_0_JavaParser.InterfaceCommonBodyDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceCommonBodyDeclarationContext0
      JavaParser.InterfaceCommonBodyDeclarationContext javaParser_InterfaceCommonBodyDeclarationContext0 = mock(JavaParser.InterfaceCommonBodyDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitInterfaceCommonBodyDeclaration
      javaParserBaseListener0.exitInterfaceCommonBodyDeclaration(javaParser_InterfaceCommonBodyDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitInterfaceDeclaration_188()  throws Throwable  {
      //caseID:413f34f2e0069623e36b1c9ed9f8c163
      //CoveredLines: [12, 204]
      //Input_0_JavaParser.InterfaceDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceDeclarationContext0
      JavaParser.InterfaceDeclarationContext javaParser_InterfaceDeclarationContext0 = mock(JavaParser.InterfaceDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitInterfaceDeclaration
      javaParserBaseListener0.exitInterfaceDeclaration(javaParser_InterfaceDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitInterfaceMemberDeclaration_189()  throws Throwable  {
      //caseID:b81021b9902a5f57d552a2d6456761da
      //CoveredLines: [12, 360]
      //Input_0_JavaParser.InterfaceMemberDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceMemberDeclarationContext0
      JavaParser.InterfaceMemberDeclarationContext javaParser_InterfaceMemberDeclarationContext0 = mock(JavaParser.InterfaceMemberDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitInterfaceMemberDeclaration
      javaParserBaseListener0.exitInterfaceMemberDeclaration(javaParser_InterfaceMemberDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitInterfaceMethodDeclaration_190()  throws Throwable  {
      //caseID:61fc2f0de600127169ab04c90693ec88
      //CoveredLines: [12, 396]
      //Input_0_JavaParser.InterfaceMethodDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceMethodDeclarationContext0
      JavaParser.InterfaceMethodDeclarationContext javaParser_InterfaceMethodDeclarationContext0 = mock(JavaParser.InterfaceMethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitInterfaceMethodDeclaration
      javaParserBaseListener0.exitInterfaceMethodDeclaration(javaParser_InterfaceMethodDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitInterfaceMethodModifier_191()  throws Throwable  {
      //caseID:8e16e5d9c75df66ff435b124db5a40d8
      //CoveredLines: [12, 408]
      //Input_0_JavaParser.InterfaceMethodModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_InterfaceMethodModifierContext0
      JavaParser.InterfaceMethodModifierContext javaParser_InterfaceMethodModifierContext0 = mock(JavaParser.InterfaceMethodModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitInterfaceMethodModifier
      javaParserBaseListener0.exitInterfaceMethodModifier(javaParser_InterfaceMethodModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLambdaBody_192()  throws Throwable  {
      //caseID:d91013e8c49ad7153cf5bf44fd285c06
      //CoveredLines: [12, 1236]
      //Input_0_JavaParser.LambdaBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaBodyContext0
      JavaParser.LambdaBodyContext javaParser_LambdaBodyContext0 = mock(JavaParser.LambdaBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLambdaBody
      javaParserBaseListener0.exitLambdaBody(javaParser_LambdaBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLambdaExpression_193()  throws Throwable  {
      //caseID:da612b616b6c92465789138b4c27c96a
      //CoveredLines: [12, 1212]
      //Input_0_JavaParser.LambdaExpressionContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaExpressionContext0
      JavaParser.LambdaExpressionContext javaParser_LambdaExpressionContext0 = mock(JavaParser.LambdaExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLambdaExpression
      javaParserBaseListener0.exitLambdaExpression(javaParser_LambdaExpressionContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLambdaLVTIList_194()  throws Throwable  {
      //caseID:7765c3b00acb38a5231006fb4fbba291
      //CoveredLines: [12, 600]
      //Input_0_JavaParser.LambdaLVTIListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaLVTIListContext0
      JavaParser.LambdaLVTIListContext javaParser_LambdaLVTIListContext0 = mock(JavaParser.LambdaLVTIListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLambdaLVTIList
      javaParserBaseListener0.exitLambdaLVTIList(javaParser_LambdaLVTIListContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLambdaLVTIParameter_195()  throws Throwable  {
      //caseID:ac23dadf3de0e063839808c945aa195e
      //CoveredLines: [12, 612]
      //Input_0_JavaParser.LambdaLVTIParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaLVTIParameterContext0
      JavaParser.LambdaLVTIParameterContext javaParser_LambdaLVTIParameterContext0 = mock(JavaParser.LambdaLVTIParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLambdaLVTIParameter
      javaParserBaseListener0.exitLambdaLVTIParameter(javaParser_LambdaLVTIParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLambdaParameters_196()  throws Throwable  {
      //caseID:a11a850f96eb729312e07d4ea4ef6893
      //CoveredLines: [12, 1224]
      //Input_0_JavaParser.LambdaParametersContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LambdaParametersContext0
      JavaParser.LambdaParametersContext javaParser_LambdaParametersContext0 = mock(JavaParser.LambdaParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLambdaParameters
      javaParserBaseListener0.exitLambdaParameters(javaParser_LambdaParametersContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLastFormalParameter_197()  throws Throwable  {
      //caseID:f3b63d12cbb217cd565c4f730431ed21
      //CoveredLines: [12, 588]
      //Input_0_JavaParser.LastFormalParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LastFormalParameterContext0
      JavaParser.LastFormalParameterContext javaParser_LastFormalParameterContext0 = mock(JavaParser.LastFormalParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLastFormalParameter
      javaParserBaseListener0.exitLastFormalParameter(javaParser_LastFormalParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLiteral_198()  throws Throwable  {
      //caseID:65d87ed6da79aaa97872012643dcb034
      //CoveredLines: [12, 636]
      //Input_0_JavaParser.LiteralContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LiteralContext0
      JavaParser.LiteralContext javaParser_LiteralContext0 = mock(JavaParser.LiteralContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLiteral
      javaParserBaseListener0.exitLiteral(javaParser_LiteralContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLocalTypeDeclaration_199()  throws Throwable  {
      //caseID:42a5e9a29c4d2425317f380e72f57255
      //CoveredLines: [12, 996]
      //Input_0_JavaParser.LocalTypeDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LocalTypeDeclarationContext0
      JavaParser.LocalTypeDeclarationContext javaParser_LocalTypeDeclarationContext0 = mock(JavaParser.LocalTypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLocalTypeDeclaration
      javaParserBaseListener0.exitLocalTypeDeclaration(javaParser_LocalTypeDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitLocalVariableDeclaration_200()  throws Throwable  {
      //caseID:a83b068dcbbe3ce067495c8fd7ecd2ff
      //CoveredLines: [12, 972]
      //Input_0_JavaParser.LocalVariableDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_LocalVariableDeclarationContext0
      JavaParser.LocalVariableDeclarationContext javaParser_LocalVariableDeclarationContext0 = mock(JavaParser.LocalVariableDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitLocalVariableDeclaration
      javaParserBaseListener0.exitLocalVariableDeclaration(javaParser_LocalVariableDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitMemberDeclaration_201()  throws Throwable  {
      //caseID:bf251970acb8dc20f76b9a7e217cba8e
      //CoveredLines: [12, 252]
      //Input_0_JavaParser.MemberDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_MemberDeclarationContext0
      JavaParser.MemberDeclarationContext javaParser_MemberDeclarationContext0 = mock(JavaParser.MemberDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitMemberDeclaration
      javaParserBaseListener0.exitMemberDeclaration(javaParser_MemberDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitMethodBody_202()  throws Throwable  {
      //caseID:b458c30b69f7f535f9c4ab662a2798d3
      //CoveredLines: [12, 276]
      //Input_0_JavaParser.MethodBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_MethodBodyContext0
      JavaParser.MethodBodyContext javaParser_MethodBodyContext0 = mock(JavaParser.MethodBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitMethodBody
      javaParserBaseListener0.exitMethodBody(javaParser_MethodBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_exitMethodCall_203()  throws Throwable  {
      //caseID:4d1056dd7d0c1d251c056dcacac4596e
      //CoveredLines: [12, 1176]
      //Input_0_JavaParser.MethodCallContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_MethodCallContext0
      JavaParser.MethodCallContext javaParser_MethodCallContext0 = mock(JavaParser.MethodCallContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitMethodCall
      javaParserBaseListener0.exitMethodCall(javaParser_MethodCallContext0);
  }

  @Test(timeout = 4000)
  public void test_exitMethodDeclaration_204()  throws Throwable  {
      //caseID:c9fde16f95980a9453ddf2e2e01617a1
      //CoveredLines: [12, 264]
      //Input_0_JavaParser.MethodDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_MethodDeclarationContext0
      JavaParser.MethodDeclarationContext javaParser_MethodDeclarationContext0 = mock(JavaParser.MethodDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitMethodDeclaration
      javaParserBaseListener0.exitMethodDeclaration(javaParser_MethodDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitModifier_205()  throws Throwable  {
      //caseID:a4e6608ba23fdd59db637c0d3dc0e0d7
      //CoveredLines: [12, 72]
      //Input_0_JavaParser.ModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ModifierContext0
      JavaParser.ModifierContext javaParser_ModifierContext0 = mock(JavaParser.ModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitModifier
      javaParserBaseListener0.exitModifier(javaParser_ModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_exitModuleBody_206()  throws Throwable  {
      //caseID:9ec30328a87e91b29ae41038856d02a5
      //CoveredLines: [12, 852]
      //Input_0_JavaParser.ModuleBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ModuleBodyContext0
      JavaParser.ModuleBodyContext javaParser_ModuleBodyContext0 = mock(JavaParser.ModuleBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitModuleBody
      javaParserBaseListener0.exitModuleBody(javaParser_ModuleBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_exitModuleDeclaration_207()  throws Throwable  {
      //caseID:27bb08fc737e4f9644b6ebc86f523e8f
      //CoveredLines: [12, 840]
      //Input_0_JavaParser.ModuleDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ModuleDeclarationContext0
      JavaParser.ModuleDeclarationContext javaParser_ModuleDeclarationContext0 = mock(JavaParser.ModuleDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitModuleDeclaration
      javaParserBaseListener0.exitModuleDeclaration(javaParser_ModuleDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitModuleDirective_208()  throws Throwable  {
      //caseID:8c84e67e3d652dcfcdda38b90b6dc7c2
      //CoveredLines: [12, 864]
      //Input_0_JavaParser.ModuleDirectiveContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ModuleDirectiveContext0
      JavaParser.ModuleDirectiveContext javaParser_ModuleDirectiveContext0 = mock(JavaParser.ModuleDirectiveContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitModuleDirective
      javaParserBaseListener0.exitModuleDirective(javaParser_ModuleDirectiveContext0);
  }

  @Test(timeout = 4000)
  public void test_exitNonWildcardTypeArguments_209()  throws Throwable  {
      //caseID:f966806f03e598723877db02b6cd1599
      //CoveredLines: [12, 1416]
      //Input_0_JavaParser.NonWildcardTypeArgumentsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_NonWildcardTypeArgumentsContext0
      JavaParser.NonWildcardTypeArgumentsContext javaParser_NonWildcardTypeArgumentsContext0 = mock(JavaParser.NonWildcardTypeArgumentsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitNonWildcardTypeArguments
      javaParserBaseListener0.exitNonWildcardTypeArguments(javaParser_NonWildcardTypeArgumentsContext0);
  }

  @Test(timeout = 4000)
  public void test_exitNonWildcardTypeArgumentsOrDiamond_210()  throws Throwable  {
      //caseID:99a52a5ec969b66633f6616f57f5ee1d
      //CoveredLines: [12, 1404]
      //Input_0_JavaParser.NonWildcardTypeArgumentsOrDiamondContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_NonWildcardTypeArgumentsOrDiamondContext0
      JavaParser.NonWildcardTypeArgumentsOrDiamondContext javaParser_NonWildcardTypeArgumentsOrDiamondContext0 = mock(JavaParser.NonWildcardTypeArgumentsOrDiamondContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitNonWildcardTypeArgumentsOrDiamond
      javaParserBaseListener0.exitNonWildcardTypeArgumentsOrDiamond(javaParser_NonWildcardTypeArgumentsOrDiamondContext0);
  }

  @Test(timeout = 4000)
  public void test_exitPackageDeclaration_211()  throws Throwable  {
      //caseID:4aad8b16453c609c4164e6bc247f01fa
      //CoveredLines: [12, 36]
      //Input_0_JavaParser.PackageDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_PackageDeclarationContext0
      JavaParser.PackageDeclarationContext javaParser_PackageDeclarationContext0 = mock(JavaParser.PackageDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitPackageDeclaration
      javaParserBaseListener0.exitPackageDeclaration(javaParser_PackageDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitParExpression_212()  throws Throwable  {
      //caseID:9bc1bc33fa5583f6de317b9926707ccb
      //CoveredLines: [12, 1152]
      //Input_0_JavaParser.ParExpressionContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ParExpressionContext0
      JavaParser.ParExpressionContext javaParser_ParExpressionContext0 = mock(JavaParser.ParExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitParExpression
      javaParserBaseListener0.exitParExpression(javaParser_ParExpressionContext0);
  }

  @Test(timeout = 4000)
  public void test_exitPattern_213()  throws Throwable  {
      //caseID:7aa8ac043f1cafd1a9514790f28d04cc
      //CoveredLines: [12, 1200]
      //Input_0_JavaParser.PatternContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_PatternContext0
      JavaParser.PatternContext javaParser_PatternContext0 = mock(JavaParser.PatternContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitPattern
      javaParserBaseListener0.exitPattern(javaParser_PatternContext0);
  }

  @Test(timeout = 4000)
  public void test_exitPrimary_214()  throws Throwable  {
      //caseID:6493c00667528ab0b287085261952a31
      //CoveredLines: [12, 1248]
      //Input_0_JavaParser.PrimaryContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_PrimaryContext0
      JavaParser.PrimaryContext javaParser_PrimaryContext0 = mock(JavaParser.PrimaryContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitPrimary
      javaParserBaseListener0.exitPrimary(javaParser_PrimaryContext0);
  }

  @Test(timeout = 4000)
  public void test_exitPrimitiveType_215()  throws Throwable  {
      //caseID:633db35aa22660b2e2bab6355ae541be
      //CoveredLines: [12, 1452]
      //Input_0_JavaParser.PrimitiveTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_PrimitiveTypeContext0
      JavaParser.PrimitiveTypeContext javaParser_PrimitiveTypeContext0 = mock(JavaParser.PrimitiveTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitPrimitiveType
      javaParserBaseListener0.exitPrimitiveType(javaParser_PrimitiveTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_exitQualifiedName_216()  throws Throwable  {
      //caseID:84e506a7cfcb226989b7c88299f51bde
      //CoveredLines: [12, 624]
      //Input_0_JavaParser.QualifiedNameContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_QualifiedNameContext0
      JavaParser.QualifiedNameContext javaParser_QualifiedNameContext0 = mock(JavaParser.QualifiedNameContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitQualifiedName
      javaParserBaseListener0.exitQualifiedName(javaParser_QualifiedNameContext0);
  }

  @Test(timeout = 4000)
  public void test_exitQualifiedNameList_217()  throws Throwable  {
      //caseID:fb37ed6f0d60c10209f6e3e7e5f21cf3
      //CoveredLines: [12, 528]
      //Input_0_JavaParser.QualifiedNameListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_QualifiedNameListContext0
      JavaParser.QualifiedNameListContext javaParser_QualifiedNameListContext0 = mock(JavaParser.QualifiedNameListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitQualifiedNameList
      javaParserBaseListener0.exitQualifiedNameList(javaParser_QualifiedNameListContext0);
  }

  @Test(timeout = 4000)
  public void test_exitReceiverParameter_218()  throws Throwable  {
      //caseID:40f96b9431cfd796f34977f138869cd2
      //CoveredLines: [12, 552]
      //Input_0_JavaParser.ReceiverParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ReceiverParameterContext0
      JavaParser.ReceiverParameterContext javaParser_ReceiverParameterContext0 = mock(JavaParser.ReceiverParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitReceiverParameter
      javaParserBaseListener0.exitReceiverParameter(javaParser_ReceiverParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_exitRecordBody_219()  throws Throwable  {
      //caseID:f2fa02a83d167c8a3630259934655aa9
      //CoveredLines: [12, 936]
      //Input_0_JavaParser.RecordBodyContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordBodyContext0
      JavaParser.RecordBodyContext javaParser_RecordBodyContext0 = mock(JavaParser.RecordBodyContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitRecordBody
      javaParserBaseListener0.exitRecordBody(javaParser_RecordBodyContext0);
  }

  @Test(timeout = 4000)
  public void test_exitRecordComponent_220()  throws Throwable  {
      //caseID:754943149396cfa2786ed1f67500009d
      //CoveredLines: [12, 924]
      //Input_0_JavaParser.RecordComponentContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordComponentContext0
      JavaParser.RecordComponentContext javaParser_RecordComponentContext0 = mock(JavaParser.RecordComponentContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitRecordComponent
      javaParserBaseListener0.exitRecordComponent(javaParser_RecordComponentContext0);
  }

  @Test(timeout = 4000)
  public void test_exitRecordComponentList_221()  throws Throwable  {
      //caseID:9998d22825ee0907c50e2cb583dc3654
      //CoveredLines: [12, 912]
      //Input_0_JavaParser.RecordComponentListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordComponentListContext0
      JavaParser.RecordComponentListContext javaParser_RecordComponentListContext0 = mock(JavaParser.RecordComponentListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitRecordComponentList
      javaParserBaseListener0.exitRecordComponentList(javaParser_RecordComponentListContext0);
  }

  @Test(timeout = 4000)
  public void test_exitRecordDeclaration_222()  throws Throwable  {
      //caseID:16d5f70ada4dd2b471f8b8dcec5f8d87
      //CoveredLines: [12, 888]
      //Input_0_JavaParser.RecordDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordDeclarationContext0
      JavaParser.RecordDeclarationContext javaParser_RecordDeclarationContext0 = mock(JavaParser.RecordDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitRecordDeclaration
      javaParserBaseListener0.exitRecordDeclaration(javaParser_RecordDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitRecordHeader_223()  throws Throwable  {
      //caseID:9862dd4ce269e8d0ffb3585fa93c27bc
      //CoveredLines: [12, 900]
      //Input_0_JavaParser.RecordHeaderContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RecordHeaderContext0
      JavaParser.RecordHeaderContext javaParser_RecordHeaderContext0 = mock(JavaParser.RecordHeaderContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitRecordHeader
      javaParserBaseListener0.exitRecordHeader(javaParser_RecordHeaderContext0);
  }

  @Test(timeout = 4000)
  public void test_exitRequiresModifier_224()  throws Throwable  {
      //caseID:e3510e921ad9eb5557509915175bb501
      //CoveredLines: [12, 876]
      //Input_0_JavaParser.RequiresModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_RequiresModifierContext0
      JavaParser.RequiresModifierContext javaParser_RequiresModifierContext0 = mock(JavaParser.RequiresModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitRequiresModifier
      javaParserBaseListener0.exitRequiresModifier(javaParser_RequiresModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_exitResource_225()  throws Throwable  {
      //caseID:84180c6c03a10e248aa0b34c1523cc68
      //CoveredLines: [12, 1080]
      //Input_0_JavaParser.ResourceContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ResourceContext0
      JavaParser.ResourceContext javaParser_ResourceContext0 = mock(JavaParser.ResourceContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitResource
      javaParserBaseListener0.exitResource(javaParser_ResourceContext0);
  }

  @Test(timeout = 4000)
  public void test_exitResourceSpecification_226()  throws Throwable  {
      //caseID:fbc279673825559991cddf13b0b24671
      //CoveredLines: [12, 1056]
      //Input_0_JavaParser.ResourceSpecificationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ResourceSpecificationContext0
      JavaParser.ResourceSpecificationContext javaParser_ResourceSpecificationContext0 = mock(JavaParser.ResourceSpecificationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitResourceSpecification
      javaParserBaseListener0.exitResourceSpecification(javaParser_ResourceSpecificationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitResources_227()  throws Throwable  {
      //caseID:5ada6c2817b7b522841aaf4cfac18869
      //CoveredLines: [12, 1068]
      //Input_0_JavaParser.ResourcesContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_ResourcesContext0
      JavaParser.ResourcesContext javaParser_ResourcesContext0 = mock(JavaParser.ResourcesContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitResources
      javaParserBaseListener0.exitResources(javaParser_ResourcesContext0);
  }

  @Test(timeout = 4000)
  public void test_exitStatement_228()  throws Throwable  {
      //caseID:a20485678ee9610500e1bea4986b8ecf
      //CoveredLines: [12, 1008]
      //Input_0_JavaParser.StatementContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_StatementContext0
      JavaParser.StatementContext javaParser_StatementContext0 = mock(JavaParser.StatementContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitStatement
      javaParserBaseListener0.exitStatement(javaParser_StatementContext0);
  }

  @Test(timeout = 4000)
  public void test_exitSuperSuffix_229()  throws Throwable  {
      //caseID:763cfa56ae4ee5a1d6722a047c0ca5b3
      //CoveredLines: [12, 1476]
      //Input_0_JavaParser.SuperSuffixContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SuperSuffixContext0
      JavaParser.SuperSuffixContext javaParser_SuperSuffixContext0 = mock(JavaParser.SuperSuffixContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitSuperSuffix
      javaParserBaseListener0.exitSuperSuffix(javaParser_SuperSuffixContext0);
  }

  @Test(timeout = 4000)
  public void test_exitSwitchBlockStatementGroup_230()  throws Throwable  {
      //caseID:af0a6675e6ce4b3f614c3f8ec8643e06
      //CoveredLines: [12, 1092]
      //Input_0_JavaParser.SwitchBlockStatementGroupContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchBlockStatementGroupContext0
      JavaParser.SwitchBlockStatementGroupContext javaParser_SwitchBlockStatementGroupContext0 = mock(JavaParser.SwitchBlockStatementGroupContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitSwitchBlockStatementGroup
      javaParserBaseListener0.exitSwitchBlockStatementGroup(javaParser_SwitchBlockStatementGroupContext0);
  }

  @Test(timeout = 4000)
  public void test_exitSwitchExpression_231()  throws Throwable  {
      //caseID:c80388b8c9d14b3788863e1e440d80be
      //CoveredLines: [12, 1260]
      //Input_0_JavaParser.SwitchExpressionContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchExpressionContext0
      JavaParser.SwitchExpressionContext javaParser_SwitchExpressionContext0 = mock(JavaParser.SwitchExpressionContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitSwitchExpression
      javaParserBaseListener0.exitSwitchExpression(javaParser_SwitchExpressionContext0);
  }

  @Test(timeout = 4000)
  public void test_exitSwitchLabel_232()  throws Throwable  {
      //caseID:b1d3fc03f736b541a42212f2a021fdd9
      //CoveredLines: [12, 1104]
      //Input_0_JavaParser.SwitchLabelContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchLabelContext0
      JavaParser.SwitchLabelContext javaParser_SwitchLabelContext0 = mock(JavaParser.SwitchLabelContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitSwitchLabel
      javaParserBaseListener0.exitSwitchLabel(javaParser_SwitchLabelContext0);
  }

  @Test(timeout = 4000)
  public void test_exitSwitchLabeledRule_233()  throws Throwable  {
      //caseID:51d20b45ffe1f7fbeb55b18454a29a55
      //CoveredLines: [12, 1272]
      //Input_0_JavaParser.SwitchLabeledRuleContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchLabeledRuleContext0
      JavaParser.SwitchLabeledRuleContext javaParser_SwitchLabeledRuleContext0 = mock(JavaParser.SwitchLabeledRuleContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitSwitchLabeledRule
      javaParserBaseListener0.exitSwitchLabeledRule(javaParser_SwitchLabeledRuleContext0);
  }

  @Test(timeout = 4000)
  public void test_exitSwitchRuleOutcome_234()  throws Throwable  {
      //caseID:********************************
      //CoveredLines: [12, 1296]
      //Input_0_JavaParser.SwitchRuleOutcomeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_SwitchRuleOutcomeContext0
      JavaParser.SwitchRuleOutcomeContext javaParser_SwitchRuleOutcomeContext0 = mock(JavaParser.SwitchRuleOutcomeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitSwitchRuleOutcome
      javaParserBaseListener0.exitSwitchRuleOutcome(javaParser_SwitchRuleOutcomeContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeArgument_235()  throws Throwable  {
      //caseID:6e08c45bb78dd3af6499ffaafca7c525
      //CoveredLines: [12, 516]
      //Input_0_JavaParser.TypeArgumentContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeArgumentContext0
      JavaParser.TypeArgumentContext javaParser_TypeArgumentContext0 = mock(JavaParser.TypeArgumentContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeArgument
      javaParserBaseListener0.exitTypeArgument(javaParser_TypeArgumentContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeArguments_236()  throws Throwable  {
      //caseID:8f9d8a74d1127f354d2f792def5d82a0
      //CoveredLines: [12, 1464]
      //Input_0_JavaParser.TypeArgumentsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeArgumentsContext0
      JavaParser.TypeArgumentsContext javaParser_TypeArgumentsContext0 = mock(JavaParser.TypeArgumentsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeArguments
      javaParserBaseListener0.exitTypeArguments(javaParser_TypeArgumentsContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeArgumentsOrDiamond_237()  throws Throwable  {
      //caseID:918d271c7aea03c77ca670e6e472ce3b
      //CoveredLines: [12, 1392]
      //Input_0_JavaParser.TypeArgumentsOrDiamondContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeArgumentsOrDiamondContext0
      JavaParser.TypeArgumentsOrDiamondContext javaParser_TypeArgumentsOrDiamondContext0 = mock(JavaParser.TypeArgumentsOrDiamondContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeArgumentsOrDiamond
      javaParserBaseListener0.exitTypeArgumentsOrDiamond(javaParser_TypeArgumentsOrDiamondContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeBound_238()  throws Throwable  {
      //caseID:c313c2f10d2cc89f58d3aa2750770923
      //CoveredLines: [12, 144]
      //Input_0_JavaParser.TypeBoundContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeBoundContext0
      JavaParser.TypeBoundContext javaParser_TypeBoundContext0 = mock(JavaParser.TypeBoundContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeBound
      javaParserBaseListener0.exitTypeBound(javaParser_TypeBoundContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeDeclaration_239()  throws Throwable  {
      //caseID:6ac9b9225da14265f66c4cdf2895e2fc
      //CoveredLines: [12, 60]
      //Input_0_JavaParser.TypeDeclarationContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeDeclarationContext0
      JavaParser.TypeDeclarationContext javaParser_TypeDeclarationContext0 = mock(JavaParser.TypeDeclarationContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeDeclaration
      javaParserBaseListener0.exitTypeDeclaration(javaParser_TypeDeclarationContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeList_240()  throws Throwable  {
      //caseID:********************************
      //CoveredLines: [12, 1428]
      //Input_0_JavaParser.TypeListContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeListContext0
      JavaParser.TypeListContext javaParser_TypeListContext0 = mock(JavaParser.TypeListContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeList
      javaParserBaseListener0.exitTypeList(javaParser_TypeListContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeParameter_241()  throws Throwable  {
      //caseID:03e1fe234dd1d56bd9cd4fafb1873689
      //CoveredLines: [12, 132]
      //Input_0_JavaParser.TypeParameterContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeParameterContext0
      JavaParser.TypeParameterContext javaParser_TypeParameterContext0 = mock(JavaParser.TypeParameterContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeParameter
      javaParserBaseListener0.exitTypeParameter(javaParser_TypeParameterContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeParameters_242()  throws Throwable  {
      //caseID:********************************
      //CoveredLines: [12, 120]
      //Input_0_JavaParser.TypeParametersContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeParametersContext0
      JavaParser.TypeParametersContext javaParser_TypeParametersContext0 = mock(JavaParser.TypeParametersContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeParameters
      javaParserBaseListener0.exitTypeParameters(javaParser_TypeParametersContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeType_243()  throws Throwable  {
      //caseID:192b266332f65bfa259ee99805697f68
      //CoveredLines: [12, 1440]
      //Input_0_JavaParser.TypeTypeContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeTypeContext0
      JavaParser.TypeTypeContext javaParser_TypeTypeContext0 = mock(JavaParser.TypeTypeContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeType
      javaParserBaseListener0.exitTypeType(javaParser_TypeTypeContext0);
  }

  @Test(timeout = 4000)
  public void test_exitTypeTypeOrVoid_244()  throws Throwable  {
      //caseID:b4c803d8a5917b610d6c3e6108d29f5f
      //CoveredLines: [12, 288]
      //Input_0_JavaParser.TypeTypeOrVoidContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_TypeTypeOrVoidContext0
      JavaParser.TypeTypeOrVoidContext javaParser_TypeTypeOrVoidContext0 = mock(JavaParser.TypeTypeOrVoidContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitTypeTypeOrVoid
      javaParserBaseListener0.exitTypeTypeOrVoid(javaParser_TypeTypeOrVoidContext0);
  }

  @Test(timeout = 4000)
  public void test_exitVariableDeclarator_245()  throws Throwable  {
      //caseID:03bec17aeaab5fd741ecfd2db838f317
      //CoveredLines: [12, 456]
      //Input_0_JavaParser.VariableDeclaratorContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableDeclaratorContext0
      JavaParser.VariableDeclaratorContext javaParser_VariableDeclaratorContext0 = mock(JavaParser.VariableDeclaratorContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitVariableDeclarator
      javaParserBaseListener0.exitVariableDeclarator(javaParser_VariableDeclaratorContext0);
  }

  @Test(timeout = 4000)
  public void test_exitVariableDeclaratorId_246()  throws Throwable  {
      //caseID:ecc3dc1806c912caa462833c00faba9d
      //CoveredLines: [12, 468]
      //Input_0_JavaParser.VariableDeclaratorIdContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableDeclaratorIdContext0
      JavaParser.VariableDeclaratorIdContext javaParser_VariableDeclaratorIdContext0 = mock(JavaParser.VariableDeclaratorIdContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitVariableDeclaratorId
      javaParserBaseListener0.exitVariableDeclaratorId(javaParser_VariableDeclaratorIdContext0);
  }

  @Test(timeout = 4000)
  public void test_exitVariableDeclarators_247()  throws Throwable  {
      //caseID:dcb9aea96627d3aa7025719e5253482d
      //CoveredLines: [12, 444]
      //Input_0_JavaParser.VariableDeclaratorsContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableDeclaratorsContext0
      JavaParser.VariableDeclaratorsContext javaParser_VariableDeclaratorsContext0 = mock(JavaParser.VariableDeclaratorsContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitVariableDeclarators
      javaParserBaseListener0.exitVariableDeclarators(javaParser_VariableDeclaratorsContext0);
  }

  @Test(timeout = 4000)
  public void test_exitVariableInitializer_248()  throws Throwable  {
      //caseID:15d43ed50fabf6a582cfa1acd02d8a77
      //CoveredLines: [12, 480]
      //Input_0_JavaParser.VariableInitializerContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableInitializerContext0
      JavaParser.VariableInitializerContext javaParser_VariableInitializerContext0 = mock(JavaParser.VariableInitializerContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitVariableInitializer
      javaParserBaseListener0.exitVariableInitializer(javaParser_VariableInitializerContext0);
  }

  @Test(timeout = 4000)
  public void test_exitVariableModifier_249()  throws Throwable  {
      //caseID:d6afe7b74323fa35703e2f4403b5dfb6
      //CoveredLines: [12, 96]
      //Input_0_JavaParser.VariableModifierContext: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock javaParser_VariableModifierContext0
      JavaParser.VariableModifierContext javaParser_VariableModifierContext0 = mock(JavaParser.VariableModifierContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: exitVariableModifier
      javaParserBaseListener0.exitVariableModifier(javaParser_VariableModifierContext0);
  }

  @Test(timeout = 4000)
  public void test_visitErrorNode_250()  throws Throwable  {
      //caseID:118d8572d70fcb9c61431c5fba35f464
      //CoveredLines: [12, 1525]
      //Input_0_ErrorNode: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock errorNode0
      ErrorNode errorNode0 = mock(ErrorNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitErrorNode
      javaParserBaseListener0.visitErrorNode(errorNode0);
  }

  @Test(timeout = 4000)
  public void test_visitTerminal_251()  throws Throwable  {
      //caseID:8d2a7341a3f950bdecad82a2467496d3
      //CoveredLines: [12, 1519]
      //Input_0_TerminalNode: {}
      
      JavaParserBaseListener javaParserBaseListener0 = new JavaParserBaseListener();
      //mock terminalNode0
      TerminalNode terminalNode0 = mock(TerminalNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: visitTerminal
      javaParserBaseListener0.visitTerminal(terminalNode0);
  }
}
