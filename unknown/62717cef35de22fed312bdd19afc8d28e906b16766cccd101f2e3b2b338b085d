package com.alipay.codegencore.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AlgoBackendDOExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public AlgoBackendDOExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andEnableIsNull() {
            addCriterion("enable is null");
            return (Criteria) this;
        }

        public Criteria andEnableIsNotNull() {
            addCriterion("enable is not null");
            return (Criteria) this;
        }

        public Criteria andEnableEqualTo(Boolean value) {
            addCriterion("enable =", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotEqualTo(Boolean value) {
            addCriterion("enable <>", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThan(Boolean value) {
            addCriterion("enable >", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable >=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThan(Boolean value) {
            addCriterion("enable <", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("enable <=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableIn(List<Boolean> values) {
            addCriterion("enable in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotIn(List<Boolean> values) {
            addCriterion("enable not in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("enable between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable not between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andJumpIsNull() {
            addCriterion("jump is null");
            return (Criteria) this;
        }

        public Criteria andJumpIsNotNull() {
            addCriterion("jump is not null");
            return (Criteria) this;
        }

        public Criteria andJumpEqualTo(String value) {
            addCriterion("jump =", value, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpNotEqualTo(String value) {
            addCriterion("jump <>", value, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpGreaterThan(String value) {
            addCriterion("jump >", value, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpGreaterThanOrEqualTo(String value) {
            addCriterion("jump >=", value, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpLessThan(String value) {
            addCriterion("jump <", value, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpLessThanOrEqualTo(String value) {
            addCriterion("jump <=", value, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpLike(String value) {
            addCriterion("jump like", value, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpNotLike(String value) {
            addCriterion("jump not like", value, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpIn(List<String> values) {
            addCriterion("jump in", values, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpNotIn(List<String> values) {
            addCriterion("jump not in", values, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpBetween(String value1, String value2) {
            addCriterion("jump between", value1, value2, "jump");
            return (Criteria) this;
        }

        public Criteria andJumpNotBetween(String value1, String value2) {
            addCriterion("jump not between", value1, value2, "jump");
            return (Criteria) this;
        }

        public Criteria andVisableUserIsNull() {
            addCriterion("visable_user is null");
            return (Criteria) this;
        }

        public Criteria andVisableUserIsNotNull() {
            addCriterion("visable_user is not null");
            return (Criteria) this;
        }

        public Criteria andVisableUserEqualTo(Integer value) {
            addCriterion("visable_user =", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotEqualTo(Integer value) {
            addCriterion("visable_user <>", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserGreaterThan(Integer value) {
            addCriterion("visable_user >", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserGreaterThanOrEqualTo(Integer value) {
            addCriterion("visable_user >=", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserLessThan(Integer value) {
            addCriterion("visable_user <", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserLessThanOrEqualTo(Integer value) {
            addCriterion("visable_user <=", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserIn(List<Integer> values) {
            addCriterion("visable_user in", values, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotIn(List<Integer> values) {
            addCriterion("visable_user not in", values, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserBetween(Integer value1, Integer value2) {
            addCriterion("visable_user between", value1, value2, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotBetween(Integer value1, Integer value2) {
            addCriterion("visable_user not between", value1, value2, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableEnvIsNull() {
            addCriterion("visable_env is null");
            return (Criteria) this;
        }

        public Criteria andVisableEnvIsNotNull() {
            addCriterion("visable_env is not null");
            return (Criteria) this;
        }

        public Criteria andVisableEnvEqualTo(Integer value) {
            addCriterion("visable_env =", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvNotEqualTo(Integer value) {
            addCriterion("visable_env <>", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvGreaterThan(Integer value) {
            addCriterion("visable_env >", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvGreaterThanOrEqualTo(Integer value) {
            addCriterion("visable_env >=", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvLessThan(Integer value) {
            addCriterion("visable_env <", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvLessThanOrEqualTo(Integer value) {
            addCriterion("visable_env <=", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvIn(List<Integer> values) {
            addCriterion("visable_env in", values, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvNotIn(List<Integer> values) {
            addCriterion("visable_env not in", values, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvBetween(Integer value1, Integer value2) {
            addCriterion("visable_env between", value1, value2, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvNotBetween(Integer value1, Integer value2) {
            addCriterion("visable_env not between", value1, value2, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andMaxTokenIsNull() {
            addCriterion("max_token is null");
            return (Criteria) this;
        }

        public Criteria andMaxTokenIsNotNull() {
            addCriterion("max_token is not null");
            return (Criteria) this;
        }

        public Criteria andMaxTokenEqualTo(Integer value) {
            addCriterion("max_token =", value, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenNotEqualTo(Integer value) {
            addCriterion("max_token <>", value, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenGreaterThan(Integer value) {
            addCriterion("max_token >", value, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_token >=", value, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenLessThan(Integer value) {
            addCriterion("max_token <", value, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenLessThanOrEqualTo(Integer value) {
            addCriterion("max_token <=", value, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenIn(List<Integer> values) {
            addCriterion("max_token in", values, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenNotIn(List<Integer> values) {
            addCriterion("max_token not in", values, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenBetween(Integer value1, Integer value2) {
            addCriterion("max_token between", value1, value2, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxTokenNotBetween(Integer value1, Integer value2) {
            addCriterion("max_token not between", value1, value2, "maxToken");
            return (Criteria) this;
        }

        public Criteria andMaxRoundIsNull() {
            addCriterion("max_round is null");
            return (Criteria) this;
        }

        public Criteria andMaxRoundIsNotNull() {
            addCriterion("max_round is not null");
            return (Criteria) this;
        }

        public Criteria andMaxRoundEqualTo(Integer value) {
            addCriterion("max_round =", value, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundNotEqualTo(Integer value) {
            addCriterion("max_round <>", value, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundGreaterThan(Integer value) {
            addCriterion("max_round >", value, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_round >=", value, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundLessThan(Integer value) {
            addCriterion("max_round <", value, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundLessThanOrEqualTo(Integer value) {
            addCriterion("max_round <=", value, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundIn(List<Integer> values) {
            addCriterion("max_round in", values, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundNotIn(List<Integer> values) {
            addCriterion("max_round not in", values, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundBetween(Integer value1, Integer value2) {
            addCriterion("max_round between", value1, value2, "maxRound");
            return (Criteria) this;
        }

        public Criteria andMaxRoundNotBetween(Integer value1, Integer value2) {
            addCriterion("max_round not between", value1, value2, "maxRound");
            return (Criteria) this;
        }

        public Criteria andImplIsNull() {
            addCriterion("impl is null");
            return (Criteria) this;
        }

        public Criteria andImplIsNotNull() {
            addCriterion("impl is not null");
            return (Criteria) this;
        }

        public Criteria andImplEqualTo(String value) {
            addCriterion("impl =", value, "impl");
            return (Criteria) this;
        }

        public Criteria andImplNotEqualTo(String value) {
            addCriterion("impl <>", value, "impl");
            return (Criteria) this;
        }

        public Criteria andImplGreaterThan(String value) {
            addCriterion("impl >", value, "impl");
            return (Criteria) this;
        }

        public Criteria andImplGreaterThanOrEqualTo(String value) {
            addCriterion("impl >=", value, "impl");
            return (Criteria) this;
        }

        public Criteria andImplLessThan(String value) {
            addCriterion("impl <", value, "impl");
            return (Criteria) this;
        }

        public Criteria andImplLessThanOrEqualTo(String value) {
            addCriterion("impl <=", value, "impl");
            return (Criteria) this;
        }

        public Criteria andImplLike(String value) {
            addCriterion("impl like", value, "impl");
            return (Criteria) this;
        }

        public Criteria andImplNotLike(String value) {
            addCriterion("impl not like", value, "impl");
            return (Criteria) this;
        }

        public Criteria andImplIn(List<String> values) {
            addCriterion("impl in", values, "impl");
            return (Criteria) this;
        }

        public Criteria andImplNotIn(List<String> values) {
            addCriterion("impl not in", values, "impl");
            return (Criteria) this;
        }

        public Criteria andImplBetween(String value1, String value2) {
            addCriterion("impl between", value1, value2, "impl");
            return (Criteria) this;
        }

        public Criteria andImplNotBetween(String value1, String value2) {
            addCriterion("impl not between", value1, value2, "impl");
            return (Criteria) this;
        }

        public Criteria andImplConfigIsNull() {
            addCriterion("impl_config is null");
            return (Criteria) this;
        }

        public Criteria andImplConfigIsNotNull() {
            addCriterion("impl_config is not null");
            return (Criteria) this;
        }

        public Criteria andImplConfigEqualTo(String value) {
            addCriterion("impl_config =", value, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigNotEqualTo(String value) {
            addCriterion("impl_config <>", value, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigGreaterThan(String value) {
            addCriterion("impl_config >", value, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigGreaterThanOrEqualTo(String value) {
            addCriterion("impl_config >=", value, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigLessThan(String value) {
            addCriterion("impl_config <", value, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigLessThanOrEqualTo(String value) {
            addCriterion("impl_config <=", value, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigLike(String value) {
            addCriterion("impl_config like", value, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigNotLike(String value) {
            addCriterion("impl_config not like", value, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigIn(List<String> values) {
            addCriterion("impl_config in", values, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigNotIn(List<String> values) {
            addCriterion("impl_config not in", values, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigBetween(String value1, String value2) {
            addCriterion("impl_config between", value1, value2, "implConfig");
            return (Criteria) this;
        }

        public Criteria andImplConfigNotBetween(String value1, String value2) {
            addCriterion("impl_config not between", value1, value2, "implConfig");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckIsNull() {
            addCriterion("need_health_check is null");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckIsNotNull() {
            addCriterion("need_health_check is not null");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckEqualTo(Boolean value) {
            addCriterion("need_health_check =", value, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckNotEqualTo(Boolean value) {
            addCriterion("need_health_check <>", value, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckGreaterThan(Boolean value) {
            addCriterion("need_health_check >", value, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckGreaterThanOrEqualTo(Boolean value) {
            addCriterion("need_health_check >=", value, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckLessThan(Boolean value) {
            addCriterion("need_health_check <", value, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckLessThanOrEqualTo(Boolean value) {
            addCriterion("need_health_check <=", value, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckIn(List<Boolean> values) {
            addCriterion("need_health_check in", values, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckNotIn(List<Boolean> values) {
            addCriterion("need_health_check not in", values, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckBetween(Boolean value1, Boolean value2) {
            addCriterion("need_health_check between", value1, value2, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andNeedHealthCheckNotBetween(Boolean value1, Boolean value2) {
            addCriterion("need_health_check not between", value1, value2, "needHealthCheck");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionIsNull() {
            addCriterion("model_description is null");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionIsNotNull() {
            addCriterion("model_description is not null");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionEqualTo(String value) {
            addCriterion("model_description =", value, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionNotEqualTo(String value) {
            addCriterion("model_description <>", value, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionGreaterThan(String value) {
            addCriterion("model_description >", value, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("model_description >=", value, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionLessThan(String value) {
            addCriterion("model_description <", value, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionLessThanOrEqualTo(String value) {
            addCriterion("model_description <=", value, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionLike(String value) {
            addCriterion("model_description like", value, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionNotLike(String value) {
            addCriterion("model_description not like", value, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionIn(List<String> values) {
            addCriterion("model_description in", values, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionNotIn(List<String> values) {
            addCriterion("model_description not in", values, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionBetween(String value1, String value2) {
            addCriterion("model_description between", value1, value2, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andModelDescriptionNotBetween(String value1, String value2) {
            addCriterion("model_description not between", value1, value2, "modelDescription");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheIsNull() {
            addCriterion("enable_gpt_cache is null");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheIsNotNull() {
            addCriterion("enable_gpt_cache is not null");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheEqualTo(Boolean value) {
            addCriterion("enable_gpt_cache =", value, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheNotEqualTo(Boolean value) {
            addCriterion("enable_gpt_cache <>", value, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheGreaterThan(Boolean value) {
            addCriterion("enable_gpt_cache >", value, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_gpt_cache >=", value, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheLessThan(Boolean value) {
            addCriterion("enable_gpt_cache <", value, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_gpt_cache <=", value, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheIn(List<Boolean> values) {
            addCriterion("enable_gpt_cache in", values, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheNotIn(List<Boolean> values) {
            addCriterion("enable_gpt_cache not in", values, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_gpt_cache between", value1, value2, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andEnableGptCacheNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_gpt_cache not between", value1, value2, "enableGptCache");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNull() {
            addCriterion("owner_user_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNotNull() {
            addCriterion("owner_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdEqualTo(Integer value) {
            addCriterion("owner_user_id =", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotEqualTo(Integer value) {
            addCriterion("owner_user_id <>", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThan(Integer value) {
            addCriterion("owner_user_id >", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("owner_user_id >=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThan(Integer value) {
            addCriterion("owner_user_id <", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("owner_user_id <=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIn(List<Integer> values) {
            addCriterion("owner_user_id in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotIn(List<Integer> values) {
            addCriterion("owner_user_id not in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdBetween(Integer value1, Integer value2) {
            addCriterion("owner_user_id between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("owner_user_id not between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andModelBaseIsNull() {
            addCriterion("model_base is null");
            return (Criteria) this;
        }

        public Criteria andModelBaseIsNotNull() {
            addCriterion("model_base is not null");
            return (Criteria) this;
        }

        public Criteria andModelBaseEqualTo(Boolean value) {
            addCriterion("model_base =", value, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseNotEqualTo(Boolean value) {
            addCriterion("model_base <>", value, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseGreaterThan(Boolean value) {
            addCriterion("model_base >", value, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseGreaterThanOrEqualTo(Boolean value) {
            addCriterion("model_base >=", value, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseLessThan(Boolean value) {
            addCriterion("model_base <", value, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseLessThanOrEqualTo(Boolean value) {
            addCriterion("model_base <=", value, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseIn(List<Boolean> values) {
            addCriterion("model_base in", values, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseNotIn(List<Boolean> values) {
            addCriterion("model_base not in", values, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseBetween(Boolean value1, Boolean value2) {
            addCriterion("model_base between", value1, value2, "modelBase");
            return (Criteria) this;
        }

        public Criteria andModelBaseNotBetween(Boolean value1, Boolean value2) {
            addCriterion("model_base not between", value1, value2, "modelBase");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountIsNull() {
            addCriterion("usage_user_count is null");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountIsNotNull() {
            addCriterion("usage_user_count is not null");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountEqualTo(Integer value) {
            addCriterion("usage_user_count =", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountNotEqualTo(Integer value) {
            addCriterion("usage_user_count <>", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountGreaterThan(Integer value) {
            addCriterion("usage_user_count >", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("usage_user_count >=", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountLessThan(Integer value) {
            addCriterion("usage_user_count <", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountLessThanOrEqualTo(Integer value) {
            addCriterion("usage_user_count <=", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountIn(List<Integer> values) {
            addCriterion("usage_user_count in", values, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountNotIn(List<Integer> values) {
            addCriterion("usage_user_count not in", values, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountBetween(Integer value1, Integer value2) {
            addCriterion("usage_user_count between", value1, value2, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountNotBetween(Integer value1, Integer value2) {
            addCriterion("usage_user_count not between", value1, value2, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountIsNull() {
            addCriterion("usage_session_count is null");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountIsNotNull() {
            addCriterion("usage_session_count is not null");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountEqualTo(Integer value) {
            addCriterion("usage_session_count =", value, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountNotEqualTo(Integer value) {
            addCriterion("usage_session_count <>", value, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountGreaterThan(Integer value) {
            addCriterion("usage_session_count >", value, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("usage_session_count >=", value, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountLessThan(Integer value) {
            addCriterion("usage_session_count <", value, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountLessThanOrEqualTo(Integer value) {
            addCriterion("usage_session_count <=", value, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountIn(List<Integer> values) {
            addCriterion("usage_session_count in", values, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountNotIn(List<Integer> values) {
            addCriterion("usage_session_count not in", values, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountBetween(Integer value1, Integer value2) {
            addCriterion("usage_session_count between", value1, value2, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageSessionCountNotBetween(Integer value1, Integer value2) {
            addCriterion("usage_session_count not between", value1, value2, "usageSessionCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountIsNull() {
            addCriterion("usage_message_count is null");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountIsNotNull() {
            addCriterion("usage_message_count is not null");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountEqualTo(Integer value) {
            addCriterion("usage_message_count =", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountNotEqualTo(Integer value) {
            addCriterion("usage_message_count <>", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountGreaterThan(Integer value) {
            addCriterion("usage_message_count >", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("usage_message_count >=", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountLessThan(Integer value) {
            addCriterion("usage_message_count <", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountLessThanOrEqualTo(Integer value) {
            addCriterion("usage_message_count <=", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountIn(List<Integer> values) {
            addCriterion("usage_message_count in", values, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountNotIn(List<Integer> values) {
            addCriterion("usage_message_count not in", values, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountBetween(Integer value1, Integer value2) {
            addCriterion("usage_message_count between", value1, value2, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountNotBetween(Integer value1, Integer value2) {
            addCriterion("usage_message_count not between", value1, value2, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Integer value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Integer value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Integer value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Integer value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Integer> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Integer> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Integer value1, Integer value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andModelTypeIsNull() {
            addCriterion("model_type is null");
            return (Criteria) this;
        }

        public Criteria andModelTypeIsNotNull() {
            addCriterion("model_type is not null");
            return (Criteria) this;
        }

        public Criteria andModelTypeEqualTo(Integer value) {
            addCriterion("model_type =", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeNotEqualTo(Integer value) {
            addCriterion("model_type <>", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeGreaterThan(Integer value) {
            addCriterion("model_type >", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("model_type >=", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeLessThan(Integer value) {
            addCriterion("model_type <", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeLessThanOrEqualTo(Integer value) {
            addCriterion("model_type <=", value, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeIn(List<Integer> values) {
            addCriterion("model_type in", values, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeNotIn(List<Integer> values) {
            addCriterion("model_type not in", values, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeBetween(Integer value1, Integer value2) {
            addCriterion("model_type between", value1, value2, "modelType");
            return (Criteria) this;
        }

        public Criteria andModelTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("model_type not between", value1, value2, "modelType");
            return (Criteria) this;
        }

        public Criteria andSupportPluginIsNull() {
            addCriterion("support_plugin is null");
            return (Criteria) this;
        }

        public Criteria andSupportPluginIsNotNull() {
            addCriterion("support_plugin is not null");
            return (Criteria) this;
        }

        public Criteria andSupportPluginEqualTo(Boolean value) {
            addCriterion("support_plugin =", value, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginNotEqualTo(Boolean value) {
            addCriterion("support_plugin <>", value, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginGreaterThan(Boolean value) {
            addCriterion("support_plugin >", value, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginGreaterThanOrEqualTo(Boolean value) {
            addCriterion("support_plugin >=", value, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginLessThan(Boolean value) {
            addCriterion("support_plugin <", value, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginLessThanOrEqualTo(Boolean value) {
            addCriterion("support_plugin <=", value, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginIn(List<Boolean> values) {
            addCriterion("support_plugin in", values, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginNotIn(List<Boolean> values) {
            addCriterion("support_plugin not in", values, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginBetween(Boolean value1, Boolean value2) {
            addCriterion("support_plugin between", value1, value2, "supportPlugin");
            return (Criteria) this;
        }

        public Criteria andSupportPluginNotBetween(Boolean value1, Boolean value2) {
            addCriterion("support_plugin not between", value1, value2, "supportPlugin");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_algo_backend
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 11 15:53:53 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_algo_backend
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}