package com.alipay.codegencore.model.util;

import com.alipay.codegencore.model.remote.RemoteAgentStatus;
import com.alipay.codegencore.model.remote.TaskObject;

import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/18 19:29
 */
public class RemoteAgentStreamDataBuildUtils {

    /**
     * 发送第一个包
     * @param sessionId
     * @param sceneId
     */
    public static TaskObject buildQueuedData(String sessionId, Long sceneId, String taskId) {
        return buildBaseData(sessionId, sceneId, taskId, (taskObject) -> taskObject.setStatus(RemoteAgentStatus.QUEUED));
    }

    /**
     * 发送取消包
     * @param sessionId
     * @param sceneId
     */
    public static TaskObject buildCancelData(String sessionId, Long sceneId, String taskId) {
        return buildBaseData(sessionId, sceneId, taskId, (taskObject) -> taskObject.setStatus(RemoteAgentStatus.CANCELLED));
    }

    /**
     * 发送失败包
     * @param sessionId
     * @param sceneId
     */
    public static TaskObject buildCompletedData(String sessionId, Long sceneId, String taskId) {
        return buildBaseData(sessionId, sceneId, taskId, (taskObject) -> taskObject.setStatus(RemoteAgentStatus.COMPLETED));
    }

    /**
     * 发送失败包
     * @param sessionId
     * @param sceneId
     */
    public static TaskObject buildFailedData(String sessionId, Long sceneId, String taskId, String errorMsg) {
        return buildBaseData(sessionId, sceneId, taskId, taskObject -> {
            taskObject.setStatus(RemoteAgentStatus.FAILED);
            taskObject.setLastError(errorMsg);
        });
    }

    /**
     * 发送基础包
     * @param sessionId
     * @param sceneId
     * @param taskId
     * @param otherHandler
     */
    private static TaskObject buildBaseData(String sessionId, Long sceneId, String taskId, Consumer<TaskObject> otherHandler) {
        TaskObject taskObject = new TaskObject();
        taskObject.setId(taskId);
        long createAt = System.currentTimeMillis();
        taskObject.setCreatedAt(createAt);
        taskObject.setChunkCreatedAt(createAt);
        if (sceneId != null) {
            taskObject.setAgentId("" + sceneId);
        }
        taskObject.setSessionId(sessionId);
        otherHandler.accept(taskObject);
        return taskObject;
    }

}
