package com.alipay.codegencore.service.middle.msgbroker;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.impl.MayaServiceImpl;
import com.alipay.codegencore.service.impl.model.AlgoModelHealthUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.common.event.UniformEvent;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 模型保活定时任务
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023.11.22
 */
@Slf4j
@Service("codegencoreModelKeepLiving")
public class CodegencoreModelKeepLiving implements CodegencoreEventHandler {
    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private MayaService mayaService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    /**
     * topic|eventCode的Key 一般用于注册
     */
    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_F_SC|EC_codegencore_model_keepliving_inspection";

    private static final String DEFAULT_QUESTION="hello";

    private static final Logger LOGGER = LoggerFactory.getLogger(CodegencoreModelKeepLiving.class);

    @Override
    public void handle(UniformEvent message) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to keep live model");
        }

        appThreadPool.submit(()->{
            String blackModelStr = codeGPTDrmConfig.getModelKeepLivingBlackList();
            List<String> blackList = null;
            if(blackModelStr != null && !blackModelStr.isEmpty()){
                try {
                    blackList = JSONObject.parseObject(blackModelStr,List.class);
                } catch (Exception ex ) {
                    LOGGER.warn("transfer modelKeepLivingBlackList failed, {}", blackModelStr, ex);
                }
            }

            final List<String> blackListF = blackList==null?new ArrayList<>():blackList;
            List<AlgoBackendDO> algoBackendDOList = MayaServiceImpl.jumpLogic(algoBackendService.getAllAlgoBackend())
                    .stream()
                    .filter(AlgoBackendDO::getEnable)
                    .filter(MayaServiceImpl::isMayaImpl)
                    .filter(a->!blackListF.contains(a.getModel()))
                    .collect(Collectors.toList());
            List<String> checkModelStr = algoBackendDOList.stream().map(AlgoBackendDO::getModel).collect(Collectors.toList());
            List<String> noNeedUpdateModels = new ArrayList<>();
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("total model health check, {}, {}", checkModelStr.size(), JSONObject.toJSONString(checkModelStr));
            }

            for (AlgoBackendDO algoBackendDO: algoBackendDOList) {
                if(LOGGER.isInfoEnabled()) {
                    LOGGER.info("start to keep live model, {}", algoBackendDO.getModel());
                }

                String modelName = algoBackendDO.getModel();
                JSONObject healthDegreeCache = algoModelHealthUtilService.getHealthDegreeCacheByModel(modelName);
                boolean isProdNeedCheck = isNeedCheckByCache(healthDegreeCache, modelName, "prod");
                boolean isPreNeedCheck  = isNeedCheckByCache(healthDegreeCache, modelName, "pre");

                if(!isProdNeedCheck && !isPreNeedCheck) {
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("no need health check, {}", modelName);
                    }
                    noNeedUpdateModels.add(modelName);
                    continue;
                }

                JSONObject prodCheckResult = null;
                JSONObject preCheckResult = null;
                if(isProdNeedCheck) {
                    prodCheckResult = executeKeepLiving(algoBackendDO, "prod");
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("check result : {}, {}, {}", modelName, "prod", JSONObject.toJSONString(prodCheckResult));
                    }
                }
                if(isPreNeedCheck) {
                    preCheckResult = executeKeepLiving(algoBackendDO, "pre");
                    if(LOGGER.isInfoEnabled()) {
                        LOGGER.info("check result : {}, {}, {}", modelName, "pre", JSONObject.toJSONString(preCheckResult));
                    }
                }
                if(LOGGER.isInfoEnabled()) {
                    LOGGER.info("end to keep live mode, {}", modelName);
                }
            }
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("follow models is not need updated, {}, {}", noNeedUpdateModels.size(), JSONObject.toJSONString(noNeedUpdateModels));
            }
        });

    }

    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }
    /**
     * 保活任务执行方法
     * <AUTHOR>
     * @since 2023.11.27
     * @param modelEnv modelEnv
     * @param algoBackendDO algoBackendDO
     */
    private JSONObject executeKeepLiving(AlgoBackendDO algoBackendDO,String modelEnv){
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to health check logic, {}, {}", algoBackendDO.getModel(), modelEnv);
        }
        JSONObject checkDetail = new JSONObject();
        String modelName =  algoBackendDO.getModel();
        if(isNeedCheckDeploy(algoBackendDO)) {
            if(!mayaService.hasAvailableServers(modelName, modelEnv)) {
                LOGGER.warn("maya server not available, {}", modelName);
                checkDetail.put("result", false);
                checkDetail.put("lastFailedReason", ResponseEnum.MODEL_NO_AVAILABLE_SERVER_ERROR.name());
                algoModelHealthUtilService.costHealth(modelEnv, modelName, ResponseEnum.MODEL_NO_AVAILABLE_SERVER_ERROR.name());
                return checkDetail;
            }
        }

        LOGGER.info("start to keep live model in {} Environment",modelEnv);
        algoBackendDO.setEnableGptCache(false);
        ChatMessage answer = sendTestMessage(algoBackendDO,modelEnv);
        boolean isSuccess = false;
        ResponseEnum errorEnum = null;
        try {
            if (answer != null && answer.getContent()!=null) {
                if(LOGGER.isInfoEnabled()) {
                    LOGGER.info("health check success, {}, {}", algoBackendDO.getModel(), modelEnv);
                    isSuccess = true;
                }
            } else {
                LOGGER.warn("{},{}环境模型保活任务调用ai模型成功", algoBackendDO.getModel(),modelEnv);
                errorEnum = ResponseEnum.AI_CALL_ERROR;
            }
        } catch (Throwable e) {
            LOGGER.info("{},{}环境模型保活任务调用ai模型异常:{}", algoBackendDO.getModel(), modelEnv, e);
            errorEnum = ResponseEnum.AI_CALL_ERROR;
        }

        if(isSuccess) {
            checkDetail.put("result", true);
            return checkDetail;
        } else {
            checkDetail.put("result", false);
            checkDetail.put("lastFailedReason", errorEnum==null?ResponseEnum.ERROR_THROW.name():errorEnum.name());
            return checkDetail;
        }
    }

    /**
     * 调用模型
     * <AUTHOR>
     * @since 2023.11.22
     * @param algoBackendDO algoBackendDO
     * @return com.alipay.codegencore.model.openai.ChatMessage
     */
    private ChatMessage sendTestMessage(AlgoBackendDO algoBackendDO,String modEnv){
        List<ChatMessage> chatMessages = List.of(new ChatMessage(ChatRoleEnum.USER.getName(), DEFAULT_QUESTION));
        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        chatCompletionRequest.setMessages(chatMessages);
        chatCompletionRequest.setMaxTokens(10);
        String requestId = ShortUid.getUid();
        GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId,
                AppConstants.CODEGPT_TOKEN_USER, false, algoBackendDO, chatCompletionRequest);
        params.setUniqueAnswerId(requestId);
        params.setModelEnv(modEnv);

        ChatMessage chatMessage = null;
        try {
            chatMessage = AlgoModelExecutor.getInstance().executorChat(algoBackendDO, params);
        } catch (Exception ex) {
            LOGGER.warn("send mmessage expection, {}, {}", algoBackendDO.getModel(), modEnv, ex);
        }
        return chatMessage;
    }


    private boolean isNeedCheckByCache(JSONObject healthDegreeCache, String modelName, String modelEnv) {
        if(null == healthDegreeCache) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("health degree is empty, so need health degree check, {}, {}", modelName, modelEnv);
            }
            return true;
        }

        String checkTime = null;
        if(null != healthDegreeCache.get(modelEnv)
            && null != healthDegreeCache.getJSONObject(modelEnv).get("checkTime")) {
            checkTime = healthDegreeCache.getJSONObject(modelEnv).getString("checkTime");
        }

        if(StringUtils.isBlank(checkTime)) {
            LOGGER.warn("invalid check time so need health degree check, {}, {}", modelName, modelEnv);
            return true;
        }

        Date current = new Date(System.currentTimeMillis());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date checkTimeDate = null;
        try {
            checkTimeDate = dateFormat.parse(checkTime);
        } catch (ParseException e) {
            LOGGER.warn("parse time error, {}, {}, {}", modelName, modelEnv, checkTime, e);
        }
        if(null == checkTimeDate) {
            LOGGER.warn("invalid time format, so need health degree check, {}, {}, {}", modelName, modelEnv, checkTime);
            return true;
        }
        long interval  = (current.getTime() - checkTimeDate.getTime()) / 1000;
        int keepModelMinIntervals = algoModelHealthUtilService.getDrmValueOfHealthDegree("keepModelMinIntervals");
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("interval is {}, {}, {}, {},  {}, {}", interval, current.getTime() / 1000, checkTimeDate.getTime() / 1000, keepModelMinIntervals, modelName, modelEnv);
        }
        if(keepModelMinIntervals < interval) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("interval is more than keepModelMinIntervals, so need health degree check, {}, {}, {}", modelName, modelEnv, checkTime);
            }
            return true;
        } else {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("interval is less than keepModelMinIntervals, so not need health degree check, {}, {}, {}", modelName, modelEnv, checkTime);
            }
            return false;
        }
    }

    private Boolean isNeedCheckDeploy(AlgoBackendDO algoBackendDO) {
        if(MayaServiceImpl.isMayaImpl(algoBackendDO)) {
            return false;
        }

        String ignoreEnvCheckModels = codeGPTDrmConfig.getIgnoreEnvCheckModels();
        JSONArray ignoreEnvCheckModelsJsonArray = null;
        try {
            ignoreEnvCheckModelsJsonArray = JSONArray.parseArray(ignoreEnvCheckModels);
        } catch(Exception ex) {
            LOGGER.warn("json transfer failed, {}", ignoreEnvCheckModels, ex);
        }
        if(null == ignoreEnvCheckModelsJsonArray
            || ignoreEnvCheckModelsJsonArray.isEmpty())  {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("black list is empty, so need check deploy, {}", algoBackendDO.getModel());
            }
            return true;
        }
        for(Object o : ignoreEnvCheckModelsJsonArray) {
            String ignoreModel = (String)o;
            if(ignoreModel.equalsIgnoreCase(algoBackendDO.getModel())) {
                if(LOGGER.isInfoEnabled()) {
                    LOGGER.info("impl is matched ignore env check model, {}", algoBackendDO.getModel());
                }
                return false;
            }
        }
        return true;
    }
}
