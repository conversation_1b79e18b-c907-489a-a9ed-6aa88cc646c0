package com.alipay.codegencore.model.model.links;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.domain.GptConversationDO;
import com.alipay.codegencore.model.domain.GptMessageDO;
import com.alipay.codegencore.model.domain.GptMessageFeedbackDO;
import com.alipay.codegencore.model.model.links.Enum.GptConversationChannelEnum;
import com.alipay.codegencore.model.model.links.Enum.GptConversationStatusEnum;
import com.alipay.codegencore.model.model.links.Enum.GptMessageTypeEnum;
import com.alipay.codegencore.model.model.links.VO.GptConversationVO;
import com.alipay.codegencore.model.model.links.VO.GptMessageVO;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.06.28
 */
public class LinksCovertUtil {
    /**
     * model转换成DO
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptConversationModel gptConversationModel
     * @param gptConversationDO gptConversationDO
     */
    public static void GptConversationModelCoverToDO(GptConversationModel gptConversationModel, GptConversationDO gptConversationDO){
        gptConversationDO.setMongoId(gptConversationModel.getId());
        gptConversationDO.setChannel(gptConversationModel.getChannel().name());
        gptConversationDO.setConversationId(gptConversationModel.getConversationId());
        gptConversationDO.setId(gptConversationModel.getDbId());
        gptConversationDO.setDeleted(gptConversationModel.getDeleted());
        gptConversationDO.setBizId(gptConversationModel.getBizId());
        gptConversationDO.setExtInfo(JSON.toJSONString(gptConversationModel.getExtInfo()));
        gptConversationDO.setRoomId(gptConversationModel.getRoomId());
        gptConversationDO.setStatus(gptConversationModel.getStatus().name());
        gptConversationDO.setTitle(gptConversationModel.getTitle());
        gptConversationDO.setUserId(gptConversationModel.getUserId());
    }
    /**
     * model转换成VO
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param gptConversationModel gptConversationModel
     * @param gptConversationVO gptConversationVO
     */
    public static void GptConversationModelCoverToVO(GptConversationModel gptConversationModel, GptConversationVO gptConversationVO){
        gptConversationVO.setId(gptConversationModel.getId());
        gptConversationVO.setConversationId(gptConversationModel.getConversationId());
        gptConversationVO.setDeleted(gptConversationModel.getDeleted());
        gptConversationVO.setBizId(gptConversationModel.getBizId());
        gptConversationVO.setRoomId(gptConversationModel.getRoomId());
        gptConversationVO.setStatus(gptConversationModel.getStatus());
        gptConversationVO.setTitle(gptConversationModel.getTitle());
        gptConversationVO.setUserId(gptConversationModel.getUserId());
        gptConversationVO.setChannel(gptConversationModel.getChannel());
        gptConversationVO.setGmtLastMessage(gptConversationModel.getGmtLastMessage());
    }

    /**
     * DO转换成model
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptConversationDO gptConversationDO
     * @param gptConversationModel gptConversationModel
     *
     */
    public static void GptConversationDOCoverToModel(GptConversationDO gptConversationDO, GptConversationModel gptConversationModel) {
        gptConversationModel.setId(gptConversationDO.getMongoId());
        gptConversationModel.setChannel(GptConversationChannelEnum.valueOf(gptConversationDO.getChannel()));
        gptConversationModel.setConversationId(gptConversationDO.getConversationId());
        gptConversationModel.setDbId(gptConversationDO.getId());
        gptConversationModel.setDeleted(gptConversationDO.getDeleted());
        gptConversationModel.setBizId(gptConversationDO.getBizId());
        gptConversationModel.setExtInfo(JSON.parseObject(gptConversationDO.getExtInfo(), GptConversationExtInfo.class));
        gptConversationModel.setRoomId(gptConversationDO.getRoomId());
        gptConversationModel.setStatus(GptConversationStatusEnum.valueOf(gptConversationDO.getStatus()));
        gptConversationModel.setTitle(gptConversationDO.getTitle());
        gptConversationModel.setUserId(gptConversationDO.getUserId());
    }
    /**
     * model转换成DO
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptMessageModel gptMessageModel
     * @param gptMessageDO gptMessageDO
     */
    public static void GptMessageModelCoverToDO(GptMessageModel gptMessageModel, GptMessageDO gptMessageDO){
        gptMessageDO.setMongoId(gptMessageModel.getId());
        gptMessageDO.setConversationId(gptMessageModel.getConversationId());
        gptMessageDO.setContent(JSON.toJSONString(gptMessageModel.getContent()));
        gptMessageDO.setDeleted(gptMessageModel.getDeleted());
        gptMessageDO.setExtInfo(JSON.toJSONString(gptMessageModel.getExtInfo()));
        gptMessageDO.setId(gptMessageModel.getDbId());
        gptMessageDO.setType(gptMessageModel.getType().name());
        gptMessageDO.setUserId(gptMessageModel.getUserId());
        gptMessageDO.setReplayMessageId(gptMessageModel.getReplayMessageId());
    }
    /**
     * model转换成VO
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param gptMessageModel gptMessageModel
     * @param gptMessageVO gptMessageVO
     */
    public static void GptMessageModelCoverToVO(GptMessageModel gptMessageModel, GptMessageVO gptMessageVO){
        gptMessageVO.setId(gptMessageModel.getId());
        gptMessageVO.setConversationId(gptMessageModel.getConversationId());
        gptMessageVO.setContent(gptMessageModel.getContent());
        gptMessageVO.setDeleted(gptMessageModel.getDeleted());
        gptMessageVO.setType(gptMessageModel.getType());
        gptMessageVO.setUserId(gptMessageModel.getUserId());
        gptMessageVO.setReplayMessageId(gptMessageModel.getReplayMessageId());
        gptMessageVO.setGptModel(gptMessageModel.getGptModel());

    }
    /**
    * DO转换成model
    * <AUTHOR>
    * @since 2024.06.28
    * @param gptMessageDO gptMessageDO
    * @param gptMessageModel gptMessageModel
    */
    public static void GptMessageDOCoverToModel(GptMessageDO gptMessageDO, GptMessageModel gptMessageModel) {
        gptMessageModel.setId(gptMessageDO.getMongoId());
        gptMessageModel.setConversationId(gptMessageDO.getConversationId());
        gptMessageModel.setContent(JSON.parseObject(gptMessageDO.getContent(), GptMessageContent.class));
        gptMessageModel.setDeleted(gptMessageDO.getDeleted());
        gptMessageModel.setExtInfo(JSON.parseObject(gptMessageDO.getExtInfo(), GptMessageExtInfo.class));
        gptMessageModel.setDbId(gptMessageDO.getId());
        gptMessageModel.setType(GptMessageTypeEnum.valueOf(gptMessageDO.getType()));
        gptMessageModel.setUserId(gptMessageDO.getUserId());
        gptMessageModel.setReplayMessageId(gptMessageDO.getReplayMessageId());
        gptMessageModel.setGmtModified(gptMessageDO.getGmtModified());
    }
    /**
     * model转换成DO
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptMessageFeedbackModel gptMessageFeedbackModel
     * @param gptMessageFeedbackDO gptMessageFeedbackDO
     */
    public static void GptMessageFeedBackModelCoverToDO(GptMessageFeedbackModel gptMessageFeedbackModel, GptMessageFeedbackDO gptMessageFeedbackDO) {
        gptMessageFeedbackDO.setMongoId(gptMessageFeedbackModel.getId());
        gptMessageFeedbackDO.setMessageId(gptMessageFeedbackModel.getMessageId());
        gptMessageFeedbackDO.setContent(JSON.toJSONString(gptMessageFeedbackModel.getContent()));
        gptMessageFeedbackDO.setDeleted(gptMessageFeedbackModel.getDeleted());
        gptMessageFeedbackDO.setId(gptMessageFeedbackModel.getDbId());
        gptMessageFeedbackDO.setAgreed(gptMessageFeedbackModel.getAgreed());
        gptMessageFeedbackDO.setUserId(gptMessageFeedbackModel.getUserId());
    }
    /**
     * DO转换成model
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptMessageFeedbackDO gptMessageFeedbackDO
     * @param gptMessageFeedbackModel gptMessageFeedbackModel
     */
    public static void GptMessageFeedBackDOCoverToModel(GptMessageFeedbackDO gptMessageFeedbackDO, GptMessageFeedbackModel gptMessageFeedbackModel) {
        gptMessageFeedbackModel.setId(gptMessageFeedbackDO.getMongoId());
        gptMessageFeedbackModel.setMessageId(gptMessageFeedbackDO.getMessageId());
        gptMessageFeedbackModel.setContent(JSON.parseObject(gptMessageFeedbackDO.getContent(), GptMessageFeedbackContent.class));
        gptMessageFeedbackModel.setDeleted(gptMessageFeedbackDO.getDeleted());
        gptMessageFeedbackModel.setDbId(gptMessageFeedbackDO.getId());
        gptMessageFeedbackModel.setAgreed(gptMessageFeedbackDO.getAgreed());
        gptMessageFeedbackModel.setUserId(gptMessageFeedbackDO.getUserId());
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.04
     * @param pluginInfo pluginInfo
     * @return com.alipay.codegencore.model.model.links.LinksPluginInfo
     */
    public static LinksPluginInfo convertPluginInfo(PluginInfo pluginInfo) {
        LinksPluginInfo linksPluginInfo = new LinksPluginInfo();
        linksPluginInfo.setDescription(pluginInfo.getDescription());
        linksPluginInfo.setId(String.valueOf(pluginInfo.getId()));
        linksPluginInfo.setName(pluginInfo.getName());
        linksPluginInfo.setType(pluginInfo.getType());
        return linksPluginInfo;
    }

}
