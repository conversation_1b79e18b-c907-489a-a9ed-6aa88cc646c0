/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version IndustryGeneratorJumpUrlInfo.java, v 0.1 2023年06月20日 上午11:07 wb-yyz273513
 */
public class IndustryGeneratorJumpUrlInfo extends BeanStringSwitcherImpl {

    /**
     * 工具id
     */
    private String configId;

    /**
     * 工具名
     */
    private String name;

    /**
     * 租户Id
     */
    private String roomId;

    /**
     * 类型
     */
    private String type;

    /**
     * 前端页面地址
     */
    private String url;

    /**
     * 对应行业工具类型
     */
    private String industryToolType;

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIndustryToolType() {
        return industryToolType;
    }

    public void setIndustryToolType(String industryToolType) {
        this.industryToolType = industryToolType;
    }
}
