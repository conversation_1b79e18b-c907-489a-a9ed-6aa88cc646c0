package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.service.common.DataCheckService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.AsyncLogUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.keymap.KeymapUtils;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 数据安全审查service的实现类
 */
@Service
public class DataCheckServiceImpl implements DataCheckService {

    @AppConfig("keymap_biz_code")
    private String bizCode;

    @AppConfig("keymap_scene_id")
    private String sceneId;

    @AppConfig("keymap_interface")
    private String keymapInterface;

    private static final Logger LOGGER = LoggerFactory.getLogger(DataCheckService.class);

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private AsyncLogUtils asyncLogUtils;

    @Override
    public ReviewResultModel checkData(String messageUid, Integer batch, String text) {
        ReviewResultModel reviewResultModel = new ReviewResultModel();
        if (AppConstants.OFF.equals(codeGPTDrmConfig.getKeymapSwitch())) {
            OTHERS_LOGGER.info("数据安全平台审核开关已经关闭,不进行数据安全审核");
            reviewResultModel.setRet(true);
            reviewResultModel.setCode("NO_REVIEW");
            return reviewResultModel;
        }
        JSONObject param = new JSONObject();
        try {
            Long ts = System.currentTimeMillis();
            String token = KeymapUtils.genKeymapToken(bizCode,sceneId,ts);
            if (StringUtils.isBlank(token)) {
                reviewResultModel.setRet(false);
                reviewResultModel.setCode("NULL_TOKEN");
                return reviewResultModel;
            }
            String bizId = messageUid + "_"  + batch;
            param.put("token",token);
            JSONObject content = new JSONObject();
            param.put("content",content);
            JSONArray params = new JSONArray();
            JSONObject data = new JSONObject();
            data.put("data",text);
            params.add(data);
            content.put("params",params);
            content.put("bizId",bizId);
            content.put("sceneId",sceneId);
            content.put("bizCode",bizCode);
            content.put("ts",ts);
            long startTime = System.currentTimeMillis();
            String response = HttpClient.post(codeGPTDrmConfig.getKeymapUrl()+keymapInterface)
                    .content(param.toJSONString())
                    .syncExecuteWithExceptionThrow(codeGPTDrmConfig.getKeymapTimeOut());
            asyncLogUtils.logThirdPartyCallTime(AppConstants.SAFETY_LOG_PREFIX, ReviewPlatformEnum.KEYMAP.name(), startTime, true,ChatRoleEnum.USER.getName(),null);
            JSONObject ret = JSONObject.parseObject(response);
            JSONObject result = ret.getJSONObject("result");
            // 错误码类型详见:https://yuque.antfin-inc.com/ioqia8/kg7h1z/yeaxwd7xpuf8uw4l?singleDoc#ABbzS
            if (!"000_100_000".equals(ret.getString("errorCode")) ||
                    !ret.getBoolean("code") ||
                    !"FINISH".equals(result.getString("status"))) {
                LOGGER.warn("检测内容异常,response:{}",response);
                reviewResultModel.setRet(false);
                reviewResultModel.setCode(ret.getString("errorCode"));
                return reviewResultModel;
            }
            JSONArray scanDetail = result.getJSONArray("scanDetail");
            String level = scanDetail.getJSONObject(0).getString("level");
            if (StringUtils.isNotBlank(level)) {
                LOGGER.warn("检测内容涉及到敏感数据,response:{}",response);
                reviewResultModel.setRet(false);
                reviewResultModel.setCode("SENSITIVE");
                return reviewResultModel;
            }
            reviewResultModel.setRet(true);
            reviewResultModel.setCode("PASSED");
            return reviewResultModel;
        } catch (Exception e) {
            LOGGER.error("请求keymap发生异常,参数:"+param.toJSONString(), e);
            reviewResultModel.setRet(false);
            reviewResultModel.setCode("KEYMAP_ERROR");
            return reviewResultModel;
        }
    }

    @Override
    public ReviewResultModel antDsrCheckData(String messageUid, Integer batch, String text, ChatRequestExtData chatRequestExtData) {
        verifyAntDsrParam(messageUid,batch,chatRequestExtData);
        ReviewResultModel resultModel = new ReviewResultModel();

        OTHERS_LOGGER.info("数据安全平台antDsr审核开关已经关闭,不进行数据安全审核");
        resultModel.setRet(true);
        resultModel.setCode("NO_REVIEW");
        return resultModel;
    }

    private void verifyAntDsrParam(String messageUid, Integer batch, ChatRequestExtData chatRequestExtData) {
        if (StringUtils.isBlank(messageUid)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request antDsr messageUid is not blank");
        }
        if (batch==null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request antDsr batch is not blank");
        }
        if (chatRequestExtData==null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request antDsr chatRequestExtData is not blank");
        }
        if (StringUtils.isBlank(chatRequestExtData.getEmpId())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request antDsr empId is not blank");
        }
        if (StringUtils.isBlank(chatRequestExtData.getCodeGPTUser())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"request antDsr codeGPTUser is not blank");
        }
    }
}
