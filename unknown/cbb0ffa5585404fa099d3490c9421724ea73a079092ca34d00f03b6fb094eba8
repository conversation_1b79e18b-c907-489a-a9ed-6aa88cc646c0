package com.alipay.codegencore.service.answer;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.copilot.CodeReference;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 仓库问答服务
 */
public interface RepoChatService {
    /**
     * 流式对话
     * @param answerRequest 包含仓库信息用户提问
     */
    void streamChat(GptAlgModelServiceRequest answerRequest,
                    JSONObject repoChatConfig,
                    List<CodeReference> codeReferences,
                    HttpServletResponse httpServletResponse);

    /**
     * 仓库问答前置校验
     * @param repoGroup
     * @param repoName
     * @param branch
     * @param toBuild
     * @return
     */
    ResponseEnum preCheck(String workNo, String repoGroup, String repoName, String branch, boolean toBuild,JSONObject repoChatConfig);


    /**
     * 获取pybloop服务地址
     * @return
     */
    String getPybloopHost();
}
