/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.thread;

import com.alipay.codegencore.utils.thread.ContextUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;

import java.util.Map;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class ContextUtil_SSTest extends ContextUtil_SSTest_scaffolding {
// allCoveredLines:[12, 33, 45, 55, 56, 59, 67, 68]

  @Test(timeout = 4000)
  public void test_get_0()  throws Throwable  {
      //caseID:884be12d2cc94d7c626d14a62392c276
      //CoveredLines: [12, 33]
      //Input_0_String: 1
      //Assert: assertNull(method_result);
      
      ContextUtil contextUtil0 = new ContextUtil();
      
      //Call method: get
      Object object0 = ContextUtil.get("1");
      
      //Test Result Assert
      assertNull(object0);
  }

  @Test(timeout = 4000)
  public void test_getAll_1()  throws Throwable  {
      //caseID:1bdea39d4e6d5490bec3678b8b4af870
      //CoveredLines: [12, 59]
      //Assert: assertTrue(method_result.isEmpty());
      
      ContextUtil contextUtil0 = new ContextUtil();
      
      //Call method: getAll
      Map<String, Object> map0 = ContextUtil.getAll();
      
      //Test Result Assert
      assertTrue(map0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test_getOrDefault_2()  throws Throwable  {
      //caseID:3021554c2949e95de63a50ba1a6fa523
      //CoveredLines: [12, 45]
      //Input_0_String: com.alipay.codegencore.utils.thread.ContextUtil
      //Input_1_Class<Integer>: Integer.class
      //Input_2_Integer: null
      //Assert: assertNull(method_result);
      
      ContextUtil contextUtil0 = new ContextUtil();
      Class<Integer> class0 = Integer.class;
      
      //Call method: getOrDefault
      Integer integer0 = ContextUtil.getOrDefault("com.alipay.codegencore.utils.thread.ContextUtil", class0, (Integer) null);
      
      //Test Result Assert
      assertNull(integer0);
  }

  @Test(timeout = 4000)
  public void test_remove_3()  throws Throwable  {
      //caseID:1b20f5a0c6385f39e1ddb9c0ad5c7d6d
      //CoveredLines: [67, 68]
      
      
      //Call method: remove
      ContextUtil.remove();
  }

  @Test(timeout = 4000)
  public void test_set_4()  throws Throwable  {
      //caseID:8f63dba0b7de062b2a1e9d6d7e6b6185
      //CoveredLines: [55, 56]
      //Input_0_String: 1.0
      //Input_1_Object: 1.0
      
      
      //Call method: set
      ContextUtil.set("1.0", "1.0");
  }
}
