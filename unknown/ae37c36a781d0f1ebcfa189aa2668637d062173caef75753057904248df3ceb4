package com.alipay.codegencore.web.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.copilot.CodeReference;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.request.answer.AnswerRequest;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.answer.AnswerIndexService;
import com.alipay.codegencore.service.answer.RepoChatService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.StreamDataListener;
import com.alipay.common.tracer.util.TracerContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Flow;

/**
 * 仓库问答
 */
@RestController
@RequestMapping("/api/answer")
@Slf4j
public class AnswerChatController {

    private static final String HEADER_TRACE_ID = "X-ANSWER-TRACE-ID";
    private static final String ANTCODE_HOST = "https://code.alipay.com";

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    @Autowired
    private ConfigService configService;

    @Autowired
    private AnswerIndexService answerIndexService;

    @Resource
    private RepoChatService repoChatService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;


    /**
     * 仓库搜索
     * @param request
     */
    @PostMapping(path = "/repo/search")
    public ChatCompletionRequest repoSearch(@RequestBody JSONObject request) {
        String requestId = ShortUid.getUid();
        log.info("begin repo search:{}, {}",requestId, JSON.toJSONString(request));

        JSONObject repoInfo = request.getJSONObject("repoInfo");
        String query = request.getString("query");
        String source = request.getString("source");
        JSONArray messages = request.getJSONArray("messages");
        String user = request.getString("user");

        String repoGroup = repoInfo.getString("repoGroup");
        String repoName = repoInfo.getString("repoName");
        String branch = repoInfo.getString("branch");
        ResponseEnum responseEnum = repoChatService.preCheck(user, repoGroup, repoName, branch, true,new JSONObject());

        //JSONArray转List, 注意处理messages格式非法的情况
        List<ChatMessage> chatMessageList = messages.toJavaList(ChatMessage.class);
        // 过滤掉其中的system message
        chatMessageList.removeIf(chatMessage -> ChatRoleEnum.SYSTEM.getName().equals(chatMessage.getRole()));

        if (ResponseEnum.SUCCESS.equals(responseEnum)) {
            log.info("begin repo search:{}/{};{}", repoGroup, repoName, branch);
            // 请求pybloopsearch接口
            JSONObject requestData = new JSONObject();
            JSONObject repoInfoJson = new JSONObject();
            repoInfoJson.put("repo_ref", String.format("%s/%s/%s", AppConstants.ANTCODE_GIT_DOMAIN, repoGroup, repoName));
            repoInfoJson.put("branch", branch);
            requestData.put("repo_info", repoInfoJson);
            requestData.put("source", source);
            requestData.put("query", query);
            requestData.put("request_id", requestId);
            requestData.put("messages", chatMessageList);
            requestData.put("user", user);

            String host = repoChatService.getPybloopHost();
            String url = host+AppConstants.CONFIG_KEY_BLOOP_SEARCH_URI;
            log.info("repo pybloop url:{}", url);
            try {
                String response = HttpClient.post(url)
                        .header(HEADER_TRACE_ID, TracerContextUtil.getTraceId())
                        .content(JSON.toJSONString(requestData)).syncExecute(40000);
                JSONObject responseJson = JSON.parseObject(response);

                JSONArray messagesJson = responseJson.getJSONArray("messages");
                chatMessageList = messagesJson.toJavaList(ChatMessage.class);
            } catch (Exception e) {
                log.error("repo search exception", e);
                // 出错时返回提示信息
                ChatMessage chatMessage = new ChatMessage(ChatRoleEnum.USER.getName(), String.format("搜索失败：%s", e.getMessage()));
                chatMessageList = List.of(chatMessage);
            }

        } else {
            String errorMsgPrompt = String.format("用中文复述以下信息：%s", responseEnum.getErrorMsg());
            ChatMessage chatMessage = new ChatMessage(ChatRoleEnum.USER.getName(), errorMsgPrompt);
            chatMessageList = List.of(chatMessage);
        }

        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        chatCompletionRequest.setMessages(chatMessageList);
        return chatCompletionRequest;
    }

    /**
     * 仓库问答
     * @param response
     * @param answerRequest
     */
    @PostMapping(path = "/repo/general/chat", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void repoGeneralChat(HttpServletResponse response, @RequestBody AnswerRequest answerRequest) {
        answer(response, answerRequest, true);
    }

    /**
     * 仓库问答
     * @param response
     * @param answerRequest
     */
    @PostMapping(path = "/repo/chat", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void repoChat(HttpServletResponse response, @RequestBody AnswerRequest answerRequest) {
        answer(response, answerRequest, false);
    }

    /**
     * 问答
     * @param response
     * @param answerRequest
     * @param toBuild
     */
    private void answer(HttpServletResponse response, @RequestBody AnswerRequest answerRequest, boolean toBuild) {
        // 用于监控来自插件端仓库问答请求量
        CHAT_LOGGER.warn("repo chat from Plugin,userId:{},repoUrl:{},repoBranch:{},messages:{}",answerRequest.getUserId(), answerRequest.getRepoUrl(),answerRequest.getBranch(), JSON.toJSONString(answerRequest.getMessage()));
        response.setContentType(MediaType.TEXT_EVENT_STREAM_VALUE);
        response.setCharacterEncoding("UTF-8");

        //校验仓库
        Pair<String, String> projectInfo = AntCodeClient.getGroupAndProjectPathByRepoUrl(answerRequest.getRepoUrl());
        if (projectInfo == null) {
            writeErrorResponse(response, ResponseEnum.ANSWER_REPO_ARG_ERR);
            return;
        }
        if(codeGPTDrmConfig.isEnableRepoUrlWhiteList()){
            try{
                // 校验仓库是否支持
                Triple<String, String, String> triple = AntCodeClient.getHostAndGroupAndProjectPathByRepoUrl(answerRequest.getRepoUrl());
                List<String> urlWhiteList = JSONArray.parseArray(codeGPTDrmConfig.getRepoUrlWhiteList()).toJavaList(String.class);
                if (!urlWhiteList.contains(triple.getLeft())) {
                    CHAT_LOGGER.warn("repoUrl unsupported, repoUrl:{}", answerRequest.getRepoUrl());
                    writeErrorResponse(response, ResponseEnum.ANSWER_REPO_NOT_ALLOW);
                    return;
                }
            }catch (Exception e){
                log.error("verify repoUrlWhiteList error", e);
            }
        }

        ResponseEnum checkRes = repoChatService.preCheck(answerRequest.getUserId(), projectInfo.getLeft(), projectInfo.getRight(), answerRequest.getBranch(), toBuild,answerRequest.getClientConfig());
        if (ResponseEnum.SUCCESS.equals(checkRes)) {
            log.info("begin repo answer:{};{}", answerRequest.getRepoUrl(), answerRequest.getBranch());
            repoAnswer(response, answerRequest);
        } else {
            if(ResponseEnum.ANSWER_REPO_INDEX_BUILDING.equals(checkRes)||ResponseEnum.ANSWER_REPO_NOT_INDEX.equals(checkRes)){
                CHAT_LOGGER.warn("repo chat block due to repo not index,repoUrl:{},repoBranch:{},messages:{}", answerRequest.getRepoUrl(),answerRequest.getBranch(), JSON.toJSONString(answerRequest.getMessage()));
            }else {
                CHAT_LOGGER.warn("repo chat error,repoUrl:{},repoBranch:{},messages:{}", answerRequest.getRepoUrl(),answerRequest.getBranch(), JSON.toJSONString(answerRequest.getMessage()));
            }
            log.info("begin chat completion:{}", checkRes);
            writeErrorResponse(response, checkRes);
        }
    }

    /**
     * 发起问答
     * @param response
     * @param request
     */
    private void repoAnswer(HttpServletResponse response, AnswerRequest request) {
        String host = repoChatService.getPybloopHost();
        String url = host+AppConstants.CONFIG_KEY_BLOOP_ANSWER_URI;
        log.info("repo pybloop url:{}", url);

        String requestId = ShortUid.getUid();
        log.info("begin repo answer:{}, {}",requestId, JSON.toJSONString(request));
        List<CodeReference> codeReferences = request.getReferences();

        Map<String, Object> requestData = new HashMap<>(5);
        //这块host先写死，理论上只会用内部antCode
        Triple<String, String, String> repoInfo = AntCodeClient.getHostAndGroupAndProjectPathByRepoUrl(request.getRepoUrl());
        if (repoInfo == null) {
            writeErrorResponse(response, ResponseEnum.ANSWER_REPO_ARG_ERR);
        }
        rewriteReferenceUrl(codeReferences,repoInfo.getMiddle() + "/" + repoInfo.getRight(),request.getBranch());
        requestData.put("repo_ref", repoInfo.getLeft() + "/" + repoInfo.getMiddle() + "/" + repoInfo.getRight());
        requestData.put("messages", request.getMessage());
        requestData.put("branch", AntCodeClient.defaultIfBlank(request.getBranch()));
        requestData.put("user", request.getUserId());
        requestData.put("search_only", false);
        requestData.put("request_id", requestId);
        requestData.put("client_config", request.getClientConfig());
        requestData.put("unique_answer_id", requestId);
        requestData.put("references", codeReferences);
        requestData.put("plugin_context", request.getContext());

        try {
            HttpClient.post(url)
                    .header(HEADER_TRACE_ID, TracerContextUtil.getTraceId())
                    .content(JSON.toJSONString(requestData))
                    .streamExecute(600000, new StreamDataListener() {

                        @Override
                        public void onConnect(Flow.Subscription subscription) {
                            log.info("repo answer on connect");
                        }

                        @Override
                        public void eachData(String data, Flow.Subscription subscription) {
                            log.debug("repo answer each data");
                            writeResponse(response, data);
                        }

                        @Override
                        public void onError(Throwable throwable) {
                            log.warn("repo answer on error, requestId: {}", requestId, throwable);
                            writeResponse(response, "服务繁忙，稍后重试");
                        }

                        @Override
                        public void onComplete() {
                            log.info("repo answer on complete, requestId: {}", requestId);
                        }

                    }, (statusCode, errorResponse) -> {
                        log.error("answer error {}, {}; {}", requestId, statusCode, errorResponse);
                        writeErrorResponse(response, errorResponse);
                    });
        } catch (URISyntaxException e) {
            log.error("stream found exception, requestId: {}", requestId, e);
            writeErrorResponse(response, "服务繁忙，稍后重试");
        }
    }

    /**
     * 发送流式包到前端
     * @param response
     * @param responseEnum
     */
    private void writeErrorResponse(HttpServletResponse response, ResponseEnum responseEnum) {
        response.setStatus(HttpStatus.SC_SERVICE_UNAVAILABLE);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        writeResponse(response, JSON.toJSONString(BaseResponse.build(responseEnum)));
    }

    /**
     * 发送流式包到前端
     * @param response
     * @param errorMessage
     */
    private void writeErrorResponse(HttpServletResponse response, String errorMessage) {
        response.setStatus(HttpStatus.SC_SERVICE_UNAVAILABLE);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        writeResponse(response, JSON.toJSONString(BaseResponse.build(ResponseEnum.ANSWER_REPO_FAIL, errorMessage)));
    }

    /**
     * 发送流式包到前端
     * @param response
     * @param data
     */
    private void writeResponse(HttpServletResponse response, String data) {
        try {
            response.getWriter().write(data);
            response.getWriter().write("\n");
            response.getWriter().flush();
        } catch (IOException e) {
            log.error("write exception info fail", e);
        }
    }

    /**
     * 重写插件端传回的reference的url
     * @param codeReferences
     * @param repoPath
     * @param branch
     */
    private void rewriteReferenceUrl(List<CodeReference> codeReferences, String repoPath, String branch){
        if (CollectionUtils.isEmpty(codeReferences)) {
            return;
        }
        for(CodeReference codeReference : codeReferences){
            String url = String.format("%s/%s/blob/%s/%s",ANTCODE_HOST , repoPath, branch, codeReference.getUrl());
            codeReference.setUrl(url);
        }

    }
}
