package com.alipay.codegencore.model.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 最终审核结果的model
 * <AUTHOR>
 */
public class CheckResultModel {

    /**
     * 全部审核通过
     */
    private boolean allCheckRet;

    /**
     * 用户提问 / ai回答
     */
    private ChatRoleEnum chatRoleEnum;

    /**
     * 审核结果,key:审核平台,val:审核结果
     */
    private Map<ReviewPlatformEnum, ReviewResultModel> resultModelMap;

    /**
     * 审核不通过的结果码
     */
    @JSONField(serialize = false)
    private ResponseEnum responseEnum;

    public CheckResultModel() {
    }

    /**
     * 确保 allCheckRet 取值和下面两个参数取值的数据一致性
     * @param keymapCheckResult 数据审核
     * @param infoSecCheckResult 内容审核
     */
    public CheckResultModel(ChatRoleEnum chatRoleEnum,
                            boolean allCheckRet,
                            ReviewResultModel keymapCheckResult,
                            ReviewResultModel infoSecCheckResult,
                            ReviewResultModel antDsrCheckResult,
                            ReviewResultModel intentionCheckResult,
                            ReviewResultModel rcSmartCheckResult) {
        this.chatRoleEnum = chatRoleEnum;
        this.allCheckRet = allCheckRet;
        resultModelMap = new LinkedHashMap<>();
        resultModelMap.put(ReviewPlatformEnum.INFOSEC,infoSecCheckResult);
        resultModelMap.put(ReviewPlatformEnum.ANTDSR,antDsrCheckResult);
        resultModelMap.put(ReviewPlatformEnum.KEYMAP,keymapCheckResult);
        resultModelMap.put(ReviewPlatformEnum.INTENTION,intentionCheckResult);
        resultModelMap.put(ReviewPlatformEnum.RCSMART,rcSmartCheckResult);
        boolean keymapCheckRet = keymapCheckResult == null || keymapCheckResult.isRet();
        boolean infoSecCheckRet = infoSecCheckResult == null || infoSecCheckResult.isRet();
        boolean antDsrCheckRet = antDsrCheckResult == null || antDsrCheckResult.isRet();
        // 数据和内容都没有审核通过,则说明是提问内容没有审核通过
        if ((!keymapCheckRet || !antDsrCheckRet) && !infoSecCheckRet) {
            responseEnum = ResponseEnum.QUESTION_DATA_CONTENT_FAILED;
        } else if (!keymapCheckRet || !antDsrCheckRet) {
            // 只有数据审核不过,说明是提问内容没有审核过
            responseEnum = ResponseEnum.QUESTION_DATA_CHECK_FAILED;
        } else if (!infoSecCheckRet) {
            // 只有内容审核不过,可能是提问,也可能是ai的回答审核不过
            responseEnum = chatRoleEnum == ChatRoleEnum.USER ? ResponseEnum.QUESTION_CONTENT_FAILED : ResponseEnum.ANSWER_CONTENT_FAILED;
        }
    }

    public void setAllCheckRet(boolean allCheckRet) {
        this.allCheckRet = allCheckRet;
    }
    
    public boolean isAllCheckRet() {
        return allCheckRet;
    }

    public ResponseEnum getResponseEnum() {
        return responseEnum;
    }

    public void setResponseEnum(ResponseEnum responseEnum) {
        this.responseEnum = responseEnum;
    }

    public ChatRoleEnum getChatRoleEnum() {
        return chatRoleEnum;
    }

    public void setChatRoleEnum(ChatRoleEnum chatRoleEnum) {
        this.chatRoleEnum = chatRoleEnum;
    }

    public Map<ReviewPlatformEnum, ReviewResultModel> getResultModelMap() {
        return resultModelMap;
    }

    public void setResultModelMap(Map<ReviewPlatformEnum, ReviewResultModel> resultModelMap) {
        this.resultModelMap = resultModelMap;
    }

    /**
     * 设置审核结果
     * @param reviewPlatformEnum 审核平台
     * @param resultModel 审核结果
     */
    public void putResultModelMap(ReviewPlatformEnum reviewPlatformEnum, ReviewResultModel resultModel) {
        if (resultModelMap == null) {
            resultModelMap = new LinkedHashMap<>();
        }
        resultModelMap.put(reviewPlatformEnum,resultModel);
    }
}
