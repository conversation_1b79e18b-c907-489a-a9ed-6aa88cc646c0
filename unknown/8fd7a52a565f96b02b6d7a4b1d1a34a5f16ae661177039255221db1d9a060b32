package com.alipay.codegencore.service.codegpt;

import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.UserPluginRecordsDO;
import com.alipay.codegencore.model.enums.ControlTypeEnum;
import com.alipay.codegencore.model.openai.UserPluginRecordsVO;
import com.alipay.codegencore.model.response.PageResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.08.09
 */
public interface UserPluginRecordsService {
    /**
     * 获取工具的权限信息
     *
     * @param pluginId 工具id
     * @return
     */
    PageResponse<List<UserPluginRecordsVO>> getPluginControlInfo(Long pluginId, String query, ControlTypeEnum controlTypeEnum, int pageNo, int pageSize);

    /**
     * 把工具的可见/可编辑用户的历史数据全部删除，然后用新的工号数据覆盖
     * @param pluginDO
     * @param controlTypeEnum
     * @param empIdList
     */
    void overridePluginUserControl(PluginDO pluginDO, ControlTypeEnum controlTypeEnum, List<String> empIdList);

    /**
     * 获取当前用户一个工具的权限信息
     *
     * @param userId      用户id
     * @param pluginId 	 工具id
     * @return
     */
    UserPluginRecordsDO getUserPermissionInfo(Long userId, Long pluginId);
    /**
     * 批量导入助手的权限信息
     *
     * @param pluginDO pluginDO 插件
     * @param controlTypeEnum 权限类型
     * @param empIds  empId
     * @return
     */
    List<String> batchInsertUserScene(PluginDO pluginDO, List<String> empIds, ControlTypeEnum controlTypeEnum);

}
