/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.links;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.FileAnnotationTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.links.CopilotAnswer;
import com.alipay.codegencore.model.model.links.GptMessageContent;
import com.alipay.codegencore.model.model.links.GptMessageModel;
import com.alipay.codegencore.model.model.links.MessageForm;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.LinksApiService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.CommonTools;
import com.alipay.codegencore.web.codegpt.ChatMessageController;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version CopilotConvesationSseMessageApi.java, v 0.1 2023年11月30日 下午1:51 wb-tzg858080
 */
@Controller
@CodeTalkWebApi
@RequestMapping("/cors")
public class CopilotConversationSseMessageApi{
    private static final Logger LOGGER = LoggerFactory.getLogger(CopilotConversationSseMessageApi.class);
    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");
    @Resource
    private UserAclService userAclService;
    @Resource
    private LinksApiService linksApiService;
    @Resource
    private ChatMessageController chatMessageController;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;


    /**
     * sse 进行消息的返回
     *
     * @param httpServletResponse 返回reponse
     * @param conversationId                  会话id
     */
    @PostMapping(path = "/copilot/conversation/{conversationId}/message",
            produces = {"application/json;charset=UTF-8", "text/event-stream;charset=UTF-8"})
    public void message(@PathVariable String conversationId, @RequestBody MessageForm form, HttpServletResponse httpServletResponse)
            throws IOException {
        if(linksApiService.isRepoChat(form.getCommand())){
            CHAT_LOGGER.warn("repo chat from copilot,query:{}",form.getQuery());
        }
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        // 检查当前会话是否属于当前用户
        if(!linksApiService.checkUserConversationAuth(String.valueOf(currentUser.getId().longValue()),conversationId)){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        httpServletResponse.setHeader("Content-Type", "text/event-stream;charset=UTF-8");
        if(form.getExtraInfo()!=null && form.getExtraInfo().containsKey("repoChatConfig") ){
            String prUrl = form.getExtraInfo().getJSONObject("repoChatConfig").getString("pr_url");
            if(StringUtils.isNotBlank(prUrl)){
                form.setCommand("Pr");
            }

        }
        linksApiService.createMessage(httpServletResponse, conversationId, currentUser.getId(),form.getCommand(), form.getQuery(), form.getExtraInfo());
    }


    /**
     * sse 进行消息的返回
     *
     * @param httpServletResponse 返回reponse
     * @param conversationId      会话id
     */
    @PostMapping(path = "/copilot/conversation/{conversationId}/regenerate",
            produces = {"application/json;charset=UTF-8", "text/event-stream;charset=UTF-8"})
    public void regenerateMessage(HttpServletResponse httpServletResponse, @PathVariable String conversationId, @RequestBody(required = false) JSONObject extraInfo) throws IOException {
        if(extraInfo!=null&&extraInfo.containsKey("repoChatConfig")){
            CHAT_LOGGER.warn("repo chat from copilot,regenerate");
        }
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        // 检查当前会话是否属于当前用户
        if(!linksApiService.checkUserConversationAuth(String.valueOf(currentUser.getId().longValue()),conversationId)){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        httpServletResponse.setHeader("Content-Type", "text/event-stream;charset=UTF-8");
        linksApiService.regenerate(httpServletResponse,conversationId,currentUser.getId(), extraInfo);
    }

    /**
     * sse 继续回答
     *
     * @param httpServletResponse 返回reponse
     * @param messageId      消息id
     */
    @PostMapping(path = "/copilot/conversation/{conversationId}/message/{messageId}/continueConversation",
            produces = {"application/json;charset=UTF-8", "text/event-stream;charset=UTF-8"})
    public void continueConversation(HttpServletResponse httpServletResponse,
                                     @PathVariable String messageId,
                                     @RequestBody(required = false) Map<String,Object> params) throws IOException {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        httpServletResponse.setHeader("Content-Type", "text/event-stream;charset=UTF-8");
        LOGGER.info( "continueConversation-messageId = {} ，params={}", messageId, params);
        GptMessageModel gptMessageModel = linksApiService.getMessageById(messageId);
        if (gptMessageModel == null || gptMessageModel.getContent() == null) {
            CommonTools.writeResponse(null,200,httpServletResponse, ResponseEnum.MESSAGE_IS_NOT_EXIST,null);
            return;
        }
        CopilotAnswer copilotAnswer = gptMessageModel.getContent().getCopilotAnswer();
        copilotAnswer.setParams(params);
        copilotAnswer.setIsForm(false);
        JSONObject jsonObject = new JSONObject();
        if(params!=null){
            jsonObject.putAll(params);
        }
        chatMessageController.continueConversation(httpServletResponse,gptMessageModel.getCopilotMessageId(),jsonObject);
        ChatMessageDO chatMessageDO = chatMessageService.getChatMessageByUid(gptMessageModel.getCopilotMessageId());
        copilotAnswer.setResult(chatMessageDO.getContent());
        if(StringUtils.isBlank(chatMessageDO.getErrorMsg())){
            String content = chatMessageDO.getContent();
            String runtimeInfoStr = chatMessageDO.getRuntimeInfo();
            JSONObject runtimeInfo = runtimeInfoStr==null?new JSONObject():JSONObject.parseObject(runtimeInfoStr);
            JSONObject docsInfo = runtimeInfo.getJSONObject("docsInfo");
            LOGGER.info("docInfo{}",JSONObject.toJSONString(docsInfo));
            content = chatMessageService.rewriteFileAnnotation(content, docsInfo, FileAnnotationTypeEnum.valueOf(codeGPTDrmConfig.getFileAnnotationType()));
            LOGGER.info("after rewrite file annotation content{}",content);
            copilotAnswer.setResult(content);
            copilotAnswer.setId(chatMessageDO.getUid());
            if(StringUtils.isNotBlank(chatMessageDO.getPluginLog())){
                linksApiService.parseData(chatMessageDO.getPluginLog(),copilotAnswer);
            }
        }else {
            copilotAnswer.setResult(chatMessageDO.getErrorMsg());
            copilotAnswer.setId(chatMessageDO.getUid());
        }
        linksApiService.updateMessage(messageId, GptMessageContent.getCopilotAnswer(copilotAnswer,null));
    }




}
