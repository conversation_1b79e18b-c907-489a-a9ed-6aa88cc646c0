package com.alipay.codegencore.service.codegpt;

import com.alipay.codegencore.model.enums.ChatGPTModelEnum;
import com.alipay.codegencore.model.enums.ChatRoleEnum;

import java.math.BigDecimal;

/**
 * 计费的service
 */
public interface CostService {

    /**
     * 验证对应的账户是否有余额
     *
     * @param codeGPTUser 分配的账户名称
     * @return true=有余额
     */
    boolean hasBalance(String codeGPTUser);

    /**
     * 获取对应的账户的余额
     *
     * @param codeGPTUser 分配的账户名称
     * @return true=有余额
     */
    BigDecimal getBalance(String codeGPTUser);

    /**
     * 从指定账户扣费,问题和回答同时扣费
     *
     * @param codeGPTUser 分配的账户名称
     * @param modelEnum 模型
     * @param promptTokens 问题使用的token数量
     * @param completionTokens 答案使用的token数量
     * @return 总共扣的费用
     */
    BigDecimal cost(String codeGPTUser, ChatGPTModelEnum modelEnum, long promptTokens, long completionTokens);

    /**
     * 从指定的账户扣费
     * @param codeGPTUser
     * @param allCost
     * @return
     */
    int cost(String codeGPTUser, BigDecimal allCost);

    /**
     * 计算问题和回答的费用
     *
     * @param modelEnum 模型
     * @param promptTokens 问题使用的token数量
     * @param completionTokens 答案使用的token数量
     * @return 总共扣的费用
     */
    BigDecimal calculateNeedCostFee(ChatGPTModelEnum modelEnum, long promptTokens, long completionTokens);
    /**
     * 给账户充值
     *
     * @param codeGPTUser 账户
     * @param money       钱,单位:美元
     * @return true=充值成功
     */
    boolean recharge(String codeGPTUser, Integer money);

    /**
     * 从账户扣钱
     *
     * @param codeGPTUser 账户
     * @param money       钱,单位:美元
     * @return true=扣费成功
     */
    boolean decBalance(String codeGPTUser, Integer money);

    /**
     * 计算指定模型多少token数的费用是多少
     *
     * @param model    模型
     * @param tokenNum token数量
     * @return 费用, 单位为美元
     */
    BigDecimal getFee(ChatGPTModelEnum model, long tokenNum, ChatRoleEnum chatRoleEnum);

}
