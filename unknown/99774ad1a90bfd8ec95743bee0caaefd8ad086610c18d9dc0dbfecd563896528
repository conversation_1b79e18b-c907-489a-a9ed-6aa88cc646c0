package com.alipay.codegencore.web.filter;

import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.io.output.TeeOutputStream;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * 支持response流多次读取，写入response流的同时会写入byteArrayOutputStream
 * <AUTHOR>
 * @version : MultiReadHttpServletResponse.java, v 0.1 2023年05月22日 15:02 baoping Exp $
 */
public class MultiReadHttpServletResponse extends HttpServletResponseWrapper {
    private PrintWriter writer;
    private ServletOutputStream outputStream;
    private TeeOutputStream teeOutputStream;
    private ByteArrayOutputStream byteArrayOutputStream;

    /**
     * 包装HttpServletResponse，将输出流写到teeOutputStream和byteArrayOutputStream里来支持response流多次读取
     * @param response
     * @throws IOException
     */
    public MultiReadHttpServletResponse(HttpServletResponse response) throws IOException {
        super(response);
        byteArrayOutputStream = new ByteArrayOutputStream();
        teeOutputStream = new TeeOutputStream(response.getOutputStream(), byteArrayOutputStream);
        writer = new PrintWriter(new OutputStreamWriter(teeOutputStream, StandardCharsets.UTF_8));
        outputStream = new WrappedServletOutputStream(teeOutputStream);
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        return writer;
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        return outputStream;
    }

    /**
     * 获取response输出的字节流
     * @return
     */
    public byte[] getBytes() {
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 重新包装输出流的response，所有写入写出都在teeOutputStream里进行
     */
    private class WrappedServletOutputStream extends ServletOutputStream {

        private final TeeOutputStream teeOutputStream;

        /**
         * 初始化WrappedServletOutputStream
         * @param teeOutputStream
         */
        public WrappedServletOutputStream(TeeOutputStream teeOutputStream) {
            this.teeOutputStream = teeOutputStream;
        }

        @Override
        public void write(int b) throws IOException {
            teeOutputStream.write(b);
        }

        @Override
        public void flush() throws IOException {
            teeOutputStream.flush();
        }

        @Override
        public void close() throws IOException {
            teeOutputStream.close();
        }

        @Override
        public boolean isReady() {
            return false;
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {
        }
    }
}
