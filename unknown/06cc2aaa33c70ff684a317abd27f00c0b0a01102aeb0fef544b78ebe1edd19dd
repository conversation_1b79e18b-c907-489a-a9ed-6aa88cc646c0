package com.alipay.codegencore.model.model.tool.learning;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.response.PluginStreamPartResponse;

/**
 * 插件流式数据缓存
 * <AUTHOR>
 */
public class PluginStreamBuffer {
    private PluginInfo pluginInfo;
    private StringBuilder preRequestContent;
    private StringBuilder llmStageContent;
    private StringBuilder postRequestContent;
    private StringBuilder summaryContent;
    private StringBuilder llmResult;
    private StringBuilder summaryResult;
    private String errorStage;

    public PluginStreamBuffer() {
        pluginInfo = new PluginInfo();
        preRequestContent = new StringBuilder();
        llmResult = new StringBuilder();
        llmStageContent = new StringBuilder();
        postRequestContent = new StringBuilder();
        summaryContent = new StringBuilder();
        summaryResult = new StringBuilder();
        errorStage = null;
    }

    /**
     * 构造函数
     * @param pluginInfo
     */
    public PluginStreamBuffer(PluginInfo pluginInfo) {
        this.pluginInfo = pluginInfo;
        preRequestContent = new StringBuilder();
        llmResult = new StringBuilder();
        llmStageContent = new StringBuilder();
        postRequestContent = new StringBuilder();
        summaryContent = new StringBuilder();
        summaryResult = new StringBuilder();
        errorStage = null;
    }

    /**
     * 缓存插件流式数据
     * @param pluginStreamPartResponse 插件流式数据
     */
    public void buffer(PluginStreamPartResponse pluginStreamPartResponse){
        if(AppConstants.PRE_REQUEST_STAGE.equalsIgnoreCase(pluginStreamPartResponse.getStage())){
            preRequestContent.append(pluginStreamPartResponse.getContent());
        }else if (AppConstants.LLM_STAGE.equalsIgnoreCase(pluginStreamPartResponse.getStage())){
            if(pluginStreamPartResponse.getContent() != null){
                llmStageContent.append(pluginStreamPartResponse.getContent());

                if(pluginStreamPartResponse.isKeyContent()){
                    llmResult.append(pluginStreamPartResponse.getContent());
                }
            }
        } else if(AppConstants.POST_REQUEST_STAGE.equalsIgnoreCase(pluginStreamPartResponse.getStage())){
            postRequestContent.append(pluginStreamPartResponse.getContent());
        }else if(AppConstants.SUMMARY.equalsIgnoreCase(pluginStreamPartResponse.getStage())){
            summaryContent.append(pluginStreamPartResponse.getContent());
            if(pluginStreamPartResponse.isKeyContent()){
                summaryResult.append(pluginStreamPartResponse.getContent());
            }
        }
    }

    public PluginInfo getPluginInfo() {
        return pluginInfo;
    }

    public void setPluginInfo(PluginInfo pluginInfo) {
        this.pluginInfo = pluginInfo;
    }

    public StringBuilder getPreRequestContent() {
        return preRequestContent;
    }

    public void setPreRequestContent(StringBuilder preRequestContent) {
        this.preRequestContent = preRequestContent;
    }

    public StringBuilder getLlmResult() {
        return llmResult;
    }

    public void setLlmResult(StringBuilder llmResult) {
        this.llmResult = llmResult;
    }

    public StringBuilder getLlmStageContent() {
        return llmStageContent;
    }

    public void setLlmStageContent(StringBuilder llmStageContent) {
        this.llmStageContent = llmStageContent;
    }

    public StringBuilder getPostRequestContent() {
        return postRequestContent;
    }

    public void setPostRequestContent(StringBuilder postRequestContent) {
        this.postRequestContent = postRequestContent;
    }

    public StringBuilder getSummaryContent() {
        return summaryContent;
    }

    public void setSummaryContent(StringBuilder summaryContent) {
        this.summaryContent = summaryContent;
    }

    public String getErrorStage() {
        return errorStage;
    }

    public void setErrorStage(String errorStage) {
        this.errorStage = errorStage;
    }

    public StringBuilder getSummaryResult() {
        return summaryResult;
    }

    public void setSummaryResult(StringBuilder summaryResult) {
        this.summaryResult = summaryResult;
    }
}
