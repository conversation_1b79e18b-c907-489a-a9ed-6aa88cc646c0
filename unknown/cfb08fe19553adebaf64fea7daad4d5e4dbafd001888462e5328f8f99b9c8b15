package com.alipay.codegencore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.common.logging.Logger;
import com.alibaba.common.logging.LoggerFactory;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.UserSceneRecordsDOExample;
import com.alipay.codegencore.dal.example.UserSceneRecordsDOExample.Criteria;
import com.alipay.codegencore.dal.mapper.UserSceneRecordsDOMapper;
import com.alipay.codegencore.dal.mapper.UserSceneRecordsManualMapper;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.domain.UserSceneRecordsDO;
import com.alipay.codegencore.model.enums.ControlTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.openai.UserSceneRecordsVO;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.UserSceneRecordsService;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.UserAclService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.impl
 * @CreateTime : 2023-08-15
 */
@Service
public class UserSceneRecordsServiceImpl implements UserSceneRecordsService {

    private final Logger LOGGER = LoggerFactory.getLogger(UserSceneRecordsServiceImpl.class);

    @Resource
    private UserSceneRecordsDOMapper     userSceneRecordsDOMapper;

    @Resource
    private UserSceneRecordsManualMapper userSceneRecordsManualMapper;

    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;

    @Resource
    private UserAclService userAclService;
    @Resource
    private SceneService sceneService;

    /**
     * 获取助手的权限信息
     *
     * @param sceneId 助手id
     * @return
     */
    @Override
    public PageResponse<List<UserSceneRecordsVO>> getSceneControlInfo(Long sceneId, String query, ControlTypeEnum controlTypeEnum, int pageNo, int pageSize) {
        Integer controlType = controlTypeEnum == null ? null : controlTypeEnum.getCode();
        List<UserSceneRecordsVO> userSceneRecordsVOS = userSceneRecordsManualMapper.selectUser(sceneId, query, controlType,(pageNo - 1) * pageSize, pageSize);
        Long aLong = userSceneRecordsManualMapper.selectUserCount(sceneId, query, controlType);
        return PageResponse.build(ResponseEnum.SUCCESS, userSceneRecordsVOS, aLong);
    }

    /**
     * 批量导入助手的权限信息
     *
     * @param sceneId 助手id
     * @param empIds  empId
     * @return
     */
    @Override
    public List<String> batchInsertUserScene(SceneDO sceneDO, List<String> empIds, ControlTypeEnum controlTypeEnum) {
        if (CollectionUtil.isEmpty(empIds)) {
            return new ArrayList<>();
        }
        List<String> errorUserIds = codeFuseUserAuthService.insertUserAuth(empIds);
        empIds.removeAll(errorUserIds);
        List<UserAuthDO> userByEmpId = codeFuseUserAuthService.getUserByEmpIds(empIds);

        userByEmpId.forEach(userAuthDO -> {
            //助手创建人不需要添加权限
            if (userAuthDO.getId().longValue() == sceneDO.getUserId().longValue()){
                return;
            }
            UserSceneRecordsDOExample userSceneRecordsDOExample = new UserSceneRecordsDOExample();
            userSceneRecordsDOExample.createCriteria()
                    .andUserIdEqualTo(userAuthDO.getId()).
                    andSceneIdEqualTo(sceneDO.getId());
            List<UserSceneRecordsDO> userSceneRecordsDOS = userSceneRecordsDOMapper.selectByExample(userSceneRecordsDOExample);
            UserSceneRecordsDO userSceneRecordsDO = new UserSceneRecordsDO();
            if (CollectionUtil.isNotEmpty(userSceneRecordsDOS)) {
                LOGGER.warn("用户已经存在于该场景中，userName：" + userAuthDO.getUserName());
                UserSceneRecordsDO sceneRecordsDO = userSceneRecordsDOS.get(0);
                boolean needUpdate = false;
                // 已删除的话加回来,并且权限加成当前所设置权限
                if (sceneRecordsDO.getDeleted() == 1) {
                    userSceneRecordsDO.setDeleted((byte) 0);
                    userSceneRecordsDO.setControlType(controlTypeEnum.getCode());
                    needUpdate = true;
                }
                // 可编辑权限 > 可见权限
                if (sceneRecordsDO.getControlType() == ControlTypeEnum.SEE.getCode() &&
                        controlTypeEnum == ControlTypeEnum.UPDATE) {
                    userSceneRecordsDO.setControlType(ControlTypeEnum.UPDATE.getCode());
                    needUpdate = true;
                }
                if (needUpdate) {
                    userSceneRecordsDOMapper.updateByExampleSelective(userSceneRecordsDO, userSceneRecordsDOExample);
                }
                return;
            }

            userSceneRecordsDO.setSceneId(sceneDO.getId());
            userSceneRecordsDO.setUserId(userAuthDO.getId());
            userSceneRecordsDO.setControlType(controlTypeEnum.getCode());
            userSceneRecordsDOMapper.insertSelective(userSceneRecordsDO);
        });
        UserAuthDO currentUser = userAclService.getCurrentUser();
        LOGGER.info(String.format("操作人：%s 批量插入，场景ID：%d，用户ID：%s，插入失败id：%s", currentUser.getUserName(), sceneDO.getId(),
                JSONObject.toJSONString(empIds), JSONObject.toJSONString(errorUserIds)));
        return errorUserIds;
    }

    /**
     * 修改助手的一个用户的信息
     *
     * @param sceneId     助手id
     * @param userId      用户id
     * @param controlType 权限类型
     * @return
     */
    @Override
    public Boolean updateSceneControl(Long sceneId, Long userId, ControlTypeEnum controlType) {
        UserSceneRecordsDOExample userSceneRecordsDOExample = new UserSceneRecordsDOExample();
        userSceneRecordsDOExample.createCriteria()
                .andSceneIdEqualTo(sceneId)
                .andUserIdEqualTo(userId)
                .andDeletedEqualTo((byte) 0);
        UserSceneRecordsDO userSceneRecordsDO = new UserSceneRecordsDO();
        userSceneRecordsDO.setControlType(controlType.getCode());
        Boolean update = userSceneRecordsDOMapper.updateByExampleSelective(userSceneRecordsDO, userSceneRecordsDOExample) > 0;
        UserAuthDO currentUser = userAclService.getCurrentUser();
        LOGGER.info(String.format("操作人：%s 更新场景控制信息，场景ID：%d，用户ID：%d，控制类型：%s", currentUser.getUserName(), sceneId, userId, controlType));
        return update;
    }

    /**
     * 去除助手的一个用户权限
     *
     * @param sceneId 助手id
     * @param userId  用户id
     * @return
     */
    @Override
    public Boolean deleteSceneUserControl(Long sceneId, Long userId) {
        UserSceneRecordsDOExample userSceneRecordsDOExample = new UserSceneRecordsDOExample();
        Criteria criteria = userSceneRecordsDOExample.createCriteria();
        criteria.andSceneIdEqualTo(sceneId);
        if (userId != null) {
            criteria.andUserIdEqualTo(userId);
        }

        UserSceneRecordsDO userSceneRecordsDO = new UserSceneRecordsDO();
        userSceneRecordsDO.setDeleted((byte) 1);
        Boolean delete = userSceneRecordsDOMapper.updateByExampleSelective(userSceneRecordsDO, userSceneRecordsDOExample) > 0;
        UserAuthDO currentUser = userAclService.getCurrentUser();
        LOGGER.info(String.format("操作人：%s 删除场景控制信息，场景ID：%d，用户ID：%d", currentUser.getUserName(), sceneId, userId));
        return delete;
    }

    /**
     * 获取user的拥有权限的助手信息
     *
     * @param userId      用户id
     * @param controlType 权限类型
     * @return
     */
    @Override
    public List<UserSceneRecordsDO> getControlInfoByUser(Long userId, ControlTypeEnum controlType) {
        UserSceneRecordsDOExample userSceneRecordsDOExample = new UserSceneRecordsDOExample();
        userSceneRecordsDOExample.createCriteria()
                .andUserIdEqualTo(userId)
                .andControlTypeEqualTo(controlType.getCode())
                .andDeletedEqualTo((byte) 0);
        return userSceneRecordsDOMapper.selectByExample(userSceneRecordsDOExample);
    }

    /**
     * 获取user可以查看的所有权限助手
     *
     * @param userId 用户id
     * @return
     */
    @Override
    public List<UserSceneRecordsDO> getAllControlInfoByUser(Long userId) {
        UserSceneRecordsDOExample userSceneRecordsDOExample = new UserSceneRecordsDOExample();
        userSceneRecordsDOExample.createCriteria()
                .andUserIdEqualTo(userId)
                .andDeletedEqualTo((byte) 0);
        return userSceneRecordsDOMapper.selectByExample(userSceneRecordsDOExample);
    }

    /**
     * 获取当前用户一个助手的权限信息
     *
     * @param userId  用户id
     * @param sceneId 助手id
     * @return
     */
    @Override
    public UserSceneRecordsDO getUserPermissionInfo(Long userId, Long sceneId) {
        UserSceneRecordsDOExample userSceneRecordsDOExample = new UserSceneRecordsDOExample();
        userSceneRecordsDOExample.createCriteria()
                .andUserIdEqualTo(userId)
                .andSceneIdEqualTo(sceneId)
                .andDeletedEqualTo((byte) 0);
        List<UserSceneRecordsDO> userSceneRecordsDOS = userSceneRecordsDOMapper.selectByExample(userSceneRecordsDOExample);
        return CollectionUtil.isEmpty(userSceneRecordsDOS) ? null : userSceneRecordsDOS.get(0);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void overrideSceneUserControl(Long sceneId, ControlTypeEnum controlTypeEnum, List<String> empIdList) {
        SceneDO sceneDO = sceneService.getSceneById(sceneId);
        if (sceneDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "助手不存在");
        }
        UserSceneRecordsDOExample userSceneRecordsDOExample = new UserSceneRecordsDOExample();
        Criteria criteria = userSceneRecordsDOExample.createCriteria();
        criteria.andSceneIdEqualTo(sceneId);
        criteria.andControlTypeEqualTo(controlTypeEnum.getCode());
        UserSceneRecordsDO userSceneRecordsDO = new UserSceneRecordsDO();
        userSceneRecordsDO.setDeleted((byte) 1);
        userSceneRecordsDOMapper.updateByExampleSelective(userSceneRecordsDO, userSceneRecordsDOExample);
        if (CollectionUtil.isEmpty(empIdList)) {
            return;
        }
        batchInsertUserScene(sceneDO, empIdList, controlTypeEnum);
    }
}
