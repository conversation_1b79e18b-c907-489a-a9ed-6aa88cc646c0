/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2019 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alipay.codegencore.model.model.links.Enum.CommonResultEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 承载分页查询结果
 *
 * <AUTHOR>
 * @version $Id: PageResult.java, v 0.1 2019-04-22 17:00 zhi.huangcz Exp $$
 */
public class PageResult<T> extends ToString {
    /** 用于前端识别：标示这个是可分页数据对象 */
    private boolean             pageable     = true;
    /** 符合查询条件的数据总量*/
    private int                 total;
    /** 符合查询条件的数据分页后总页数*/
    private int                 totalPages;
    /** 当前第几页*/
    private int                 page;
    /** 每页最大记录数*/
    private int                 pageSize;
    /** 结果列表 */
    public  List<T>             list         = new ArrayList<>();
    public  boolean             success      = false;
    /**
     * 业务处理结果码
     */
    private ResultCode          resultCode;
    /**
     * 结果描述
     */
    public  String              msg;
    /**
     * 错误码
     */
    public  String              errorCode;
    /**
     * 错误信息
     */
    public  String              errorMessage;
    /**
     * 额外辅助信息
     */
    private Map<String, Object> extraInfoMap = new HashMap<>();
    /**
     * 耗时
     */
    private Long processingTime;

    public boolean isPageable() {
        return pageable;
    }

    public void setPageable(boolean pageable) {
        this.pageable = pageable;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public ResultCode getResultCode() {
        return resultCode;
    }

    public void setResultCode(ResultCode resultCode) {
        this.resultCode = resultCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Map<String, Object> getExtraInfoMap() {
        return extraInfoMap;
    }

    public void setExtraInfoMap(Map<String, Object> extraInfoMap) {
        this.extraInfoMap = extraInfoMap;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public PageResult() {}

    /**
     * PageResult
     * @param resultCode
     */
    public PageResult(ResultCode resultCode) {
        this.success = resultCode.isSuccessCode();
        if (this.success) {
            this.msg = resultCode.getMessage();
        } else {
            this.errorCode = resultCode.getCode();
            this.errorMessage = resultCode.getMessage();
        }
        this.resultCode = resultCode;
    }

    /**
     * PageResult
     * @param list
     * @param total
     * @param page
     * @param pageSize
     */
    public PageResult(List<T> list, int total, int page, int pageSize) {
        this.success = true;
        this.msg = CommonResultEnum.SUCCESS.getMessage();
        this.list = list;
        this.total = total;
        this.page = page;
        this.pageSize = pageSize;
        this.totalPages = total / pageSize;
        if (total % pageSize > 0) {
            this.totalPages++;
        }
    }

    /**
     * 空的分页列表
     *
     * @return
     */
    public static <A> PageResult<A> empty() {
        return new PageResult(new ArrayList<>(), 0, 1, 20);
    }
}