package com.alipay.codegencore.service.impl;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.collection.CollectionUtil;
import com.alipay.codegencore.dal.example.UserFeedbackDOExample;
import com.alipay.codegencore.dal.mapper.UserFeedbackDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.domain.UserFeedbackDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserFeedBackStatusEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.openai.UserFeedBackVO;
import com.alipay.codegencore.service.codegpt.UserFeedbackService;
import com.alipay.codegencore.service.common.DimaOpenApiService;
import com.alipay.codegencore.service.common.OssService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import org.mortbay.util.ajax.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户反馈
 *
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.impl
 * @CreateTime : 2023-10-31
 */
@Service
public class UserFeedbackServiceImpl implements UserFeedbackService {

    private static final Logger               LOGGER = LoggerFactory.getLogger(UserFeedbackServiceImpl.class);
    @Resource
    private              UserFeedbackDOMapper userFeedbackDOMapper;
    @Resource
    private              OssService           ossService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private CodeGPTDrmConfig drmConfig;

    @Resource
    private DimaOpenApiService dimaOpenApiService;

    /**
     * 查询所有用户反馈
     */
    @Override
    public List<UserFeedbackDO> queryAllUserFeedback() {
        UserFeedbackDOExample userFeedbackDOExample = new UserFeedbackDOExample();
        return userFeedbackDOMapper.selectByExample(userFeedbackDOExample);
    }
    /**
     * 新增用户反馈
     */
    @Override
    public Boolean addUserFeedback(MultipartFile[] multipartFile, String content) {
        UserAuthDO currentUser = userAclService.getCurrentUser();

        List<JSONObject> pictures = new ArrayList<>();
        List<String> fileOssUrls = new ArrayList<>();
        if (multipartFile != null) {
            for (MultipartFile file : multipartFile) {
                // 图片大小限制3mb
                if (file.getSize() > AppConstants.UPLOAD_MAX_SIZE) {
                    throw new BizException(ResponseEnum.FILE_TOO_BIG);
                }
                String contentType = file.getContentType();
                if (contentType != null) {
                    contentType = contentType.toUpperCase();
                    String suffix = contentType.split("/")[1];
                    // 格式限制 为jgp  png   jpeg
                    if (contentType.contains("JPG") || contentType.contains("PNG") || contentType.contains("JPEG")) {
                        JSONObject picture = new JSONObject();
                        String fileOssUrl;
                        String fileUid = ShortUid.getUid();
                        fileUid = fileUid.replaceAll("\\.","").replaceAll("_","");
                        fileUid = fileUid + "." +suffix;
                        String filePath;
                        if (drmConfig.isIntranetApplication()){
                            filePath = AppConstants.USER_FEEDBACK_INSIDE + fileUid;
                        }else {
                            filePath = AppConstants.USER_FEEDBACK_EXTERNAL + fileUid;
                        }
                        try {
                            fileOssUrl = ossService.putObject(filePath, file.getInputStream(),
                                    null, null);
                        } catch (Exception e) {
                            LOGGER.error("uploadFile failed userId"+currentUser.getId(), e);
                            throw new BizException(ResponseEnum.UPLOAD_FILE_FAILED);
                        }
                        LOGGER.info("用户反馈图片写入oss成功,fileUid:{},fileOssUrl:{}", fileUid, fileOssUrl);
                        picture.put("fileName", fileUid);
                        picture.put("fileOssUrl", fileOssUrl);
                        picture.put("fileOriginalName", file.getOriginalFilename());
                        fileOssUrls.add(fileOssUrl);
                        pictures.add(picture);
                    }
                    else {
                        throw new BizException(ResponseEnum.FILE_TYPE_NOT_SUPPORTED);
                    }
                }
                else {
                    throw new BizException(ResponseEnum.FILE_TYPE_NOT_SUPPORTED);
                }
            }

        }
        UserFeedbackDO record = new UserFeedbackDO();
        record.setUserId(currentUser.getId());
        if (CollectionUtil.isNotEmpty(pictures)) {
            record.setPictures(JSON.toString(pictures));
        }
        record.setContent(content);
        record.setStatus(UserFeedBackStatusEnum.NOT_HANDLED.getValue().byteValue());
        int i = userFeedbackDOMapper.insertSelective(record);
        //新增用户反馈后直接创建工作项
        this.createWorkItem(content, fileOssUrls,null);
        return i == 1;
    }

    /**
     * 新增用户反馈
     *
     * @param userFeedBackVO fileUrl 前端同学文件上传oss后的url
     *                       content 反馈内容
     * @return
     */
    @Override
    public Boolean feedBackViaUrl(UserFeedBackVO userFeedBackVO) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        UserFeedbackDO record = new UserFeedbackDO();
        record.setUserId(currentUser.getId());

        if (userFeedBackVO.getFileUrl() == null) {
            userFeedBackVO.setFileUrl(new ArrayList<>());
        }
        record.setPictures(JSON.toString(userFeedBackVO.getFileUrl()));
        record.setContent(userFeedBackVO.getContent());
        record.setStatus(UserFeedBackStatusEnum.NOT_HANDLED.getValue().byteValue());
        record.setSceneId(userFeedBackVO.getSceneId());
        int i = userFeedbackDOMapper.insertSelective(record);

        this.createWorkItem(userFeedBackVO.getContent(), userFeedBackVO.getFileUrl(),userFeedBackVO.getSceneId());

        return i == 1;
    }

    /**
     * 创建工作项
     *
     * @param content 内容
     * @param urls    文件地址
     */
    private void createWorkItem(String content, List<String> urls,Long sceneId) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        String title = "用户反馈-CodeFuse-外网-" + currentUser.getId();
        if (drmConfig.isIntranetApplication()) {
            title = "用户反馈-CodeFuse-内网-" + currentUser.getEmpId();
            content = "<br>用户empId:" + currentUser.getEmpId() + "<br>用户反馈内容:" + content;
            if (sceneId != null){
                content = "<br>助手id:" + sceneId +content;
            }
        }
        else {
            content = "用户ID:" + currentUser.getId() + "<br>用户反馈内容:" + content;
        }
        StringBuilder fileUrls = new StringBuilder();
        for (String url : urls) {
            JSONObject urlFormat = JSONObject.parseObject(AppConstants.IMAGE_URL_FORMAT);
            urlFormat.put("name", '1');
            urlFormat.put("src", url);
            fileUrls.append(String.format(AppConstants.DIMA_IMAGE, URLEncoder.encode(urlFormat.toJSONString(), StandardCharsets.UTF_8)));
        }
        dimaOpenApiService.createWorkItem(AppConstants.CREATE_WORKITEM_ASSIGNEE_EMPID,
                String.format(AppConstants.CONTENT_FORMAT, content, fileUrls), title);
    }
}
