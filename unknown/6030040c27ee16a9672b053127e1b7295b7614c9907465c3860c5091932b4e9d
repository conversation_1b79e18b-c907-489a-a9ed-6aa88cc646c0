/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2022 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 *
 * <AUTHOR>
 * @version $Id: ResultItem.java, v 0.1 2022-11-15 11:05 LiYuYao Exp $$
 */
public class ResultItem extends ToString {
    /**
     * 结果名称
     */
    private String name;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 状态
     */
    private String status;

    /**
     * 链接信息
     */
    private LinkItem link;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LinkItem getLink() {
        return link;
    }

    public void setLink(LinkItem link) {
        this.link = link;
    }
}