package com.alipay.codegencore.service.middle.zsearch;

import com.alipay.zsearch.core.search.aggregation.MetricAggregation;

import java.util.List;

/**
 * 对zSearch数据添加总数字段
 */
public class ZSearchResult<T> {
    /**
     * 消息总条数
     */
    private Long item;
    /**
     * 数据
     */
    private List<T> data;
    /**
     * 数据
     */
    private MetricAggregation aggregations;

    public Long getItem() {
        return item;
    }

    public void setItem(Long item) {
        this.item = item;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public MetricAggregation getAggregations() {
        return aggregations;
    }

    public void setAggregations(MetricAggregation aggregations) {
        this.aggregations = aggregations;
    }
}
