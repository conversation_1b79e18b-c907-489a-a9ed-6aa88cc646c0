package com.alipay.codegencore.service.tool.learning;

import com.alipay.codegencore.model.tool.learning.WorkflowConfigCheckResult;

import java.util.Map;

/**
 * 插件配置
 * <AUTHOR>
 */
public interface PluginConfigService {


    /**
     * 校验工作量配置文件
     *
     * @param yamlText yaml文本
     * @return 校验结果
     */
    WorkflowConfigCheckResult checkWorkFlowYaml(String yamlText);

    /**
     * 解析工作流配置文件
     * @param yamlText yaml文本
     * @return 解析结果
     */
    Map<String, Object> parseWorkFlowYaml(String yamlText);

    /**
     * 将aci信息添加到工作流配置文件中
     *
     * @param yamlText
     * @param aciInfo
     * @return
     */
    String addAciInfoToWorkflowYaml(String yamlText, String aciInfo);

    /**
     * 获取插件的type，pipeline 和 api
     * @param infoConfig
     * @return
     */
    String getPluginTypeByInfo(Object infoConfig);
}
