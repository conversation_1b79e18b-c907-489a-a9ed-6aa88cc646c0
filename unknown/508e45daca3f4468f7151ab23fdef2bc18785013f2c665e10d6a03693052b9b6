/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.Map;

/**
 * <AUTHOR>
 * @version PlatformToolInfo.java, v 0.1 2023年11月23日 下午7:15 wb-tzg858080
 */
public class PlatformToolInfo extends ToString {

    /**
     * 工具名称
     */
    private String  toolName ;

    /**
     * 工具参数
     */
    private Map<String,String> toolParams ;

    /**
     * 日志id
     */
    private String logId ;

    /**
     * 日志地址
     */
    private String logUrl ;

    public String getToolName() {
        return toolName;
    }

    public void setToolName(String toolName) {
        this.toolName = toolName;
    }

    public Map<String, String> getToolParams() {
        return toolParams;
    }

    public void setToolParams(Map<String, String> toolParams) {
        this.toolParams = toolParams;
    }

    public String getLogId() {
        return logId;
    }

    public void setLogId(String logId) {
        this.logId = logId;
    }

    public String getLogUrl() {
        return logUrl;
    }

    public void setLogUrl(String logUrl) {
        this.logUrl = logUrl;
    }
}
