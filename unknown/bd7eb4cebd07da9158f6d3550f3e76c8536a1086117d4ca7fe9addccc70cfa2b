/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.service.utils;

import com.alipay.codegencore.service.utils.SingletonServerManager;
import com.alipay.codegencore.utils.search.SnowFlake;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;

import static org.junit.Assert.assertNotNull;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class SingletonServerManager_SSTest extends SingletonServerManager_SSTest_scaffolding {
// allCoveredLines:[16, 25, 34]

  @Test(timeout = 4000)
  public void test_getIdGenerator_0()  throws Throwable  {
      //caseID:d5b2b7af12ad38ecff22a66328327499
      //CoveredLines: [25]
      //Assert: assertNotNull(method_result);
      
      
      //Call method: getIdGenerator
      SnowFlake snowFlake0 = SingletonServerManager.getIdGenerator();
      
      //Test Result Assert
      assertNotNull(snowFlake0);
  }


}
