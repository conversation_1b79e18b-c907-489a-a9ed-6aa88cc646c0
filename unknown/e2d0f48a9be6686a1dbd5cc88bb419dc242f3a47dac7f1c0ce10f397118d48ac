package com.alipay.codegencore.model.enums;

/**
 * 算法策略类型
 *
 * <AUTHOR>
 * 创建时间 2022-03-03
 */
public enum VisableUserEnum {

    /**
     * 无用户可见
     */
    NONE(0),

    /**
     * 所有用户可见
     */
    ALL(1),

    /**
     * 仅管理员可见
     */
    ADMIN(2);

    private int code;

    VisableUserEnum(int code) {
        this.code = code;
    }

    VisableUserEnum getByCode(int code) {
        for(VisableUserEnum e: VisableUserEnum.values()) {
            if(e.code == code) {
                return e;
            }
        }

        return null;// not found
    }

    public int getCode() {
        return code;
    }
}
