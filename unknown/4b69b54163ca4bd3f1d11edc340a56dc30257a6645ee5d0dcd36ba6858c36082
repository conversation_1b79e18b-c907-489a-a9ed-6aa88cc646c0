package com.alipay.codegencore.web.filter;

import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.utils.thread.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.alipay.codegencore.model.contant.WebApiContents.CONTEXT_USER;
import static com.alipay.codegencore.model.contant.WebApiContents.TOKEN_USER;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/12 11:17
 */
@Slf4j
@WebFilter(urlPatterns = {"/v1/agent/*", "/v1/sessions/*"})
public class RemoteAgentTokenFilter implements Filter {

    /**
     * 鉴权用户
     */
    private static final String HEADER_AGENT_USER = "Agent-User";
    /**
     * 鉴权token
     */
    private static final String HEADER_AGENT_TOKEN = "Agent-Token";
    /**
     * 分布式链路追踪 ID
     */
    private static final String HEADER_SOFA_TRACED_ID = "SOFA-TraceId";

    @Autowired
    private UserAclService userAclService;

    @Autowired
    private CodeFuseUserAuthService codeFuseUserAuthService;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        final String token = httpServletRequest.getHeader(HEADER_AGENT_TOKEN);
        final String user = httpServletRequest.getHeader(HEADER_AGENT_USER);
        final String traceId = httpServletRequest.getHeader(HEADER_SOFA_TRACED_ID);
        log.info("agent request user:{} token:{} traceId:{}", user, token, traceId);
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        httpServletResponse.setHeader(HEADER_SOFA_TRACED_ID, traceId);

        try {
            String requestURI = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
            boolean authorized = userAclService.isAuthorizedByToken(user, token, requestURI);
            if (!authorized) {
                httpServletResponse.setStatus(HttpStatus.UNAUTHORIZED.value());
                httpServletResponse.getWriter().write(HttpStatus.UNAUTHORIZED.toString());
                return;
            }

            TokenDO tokenDO = userAclService.queryTokenOwner(user);
            Long userId = NumberUtils.toLong(tokenDO.getOwnerUserId());
            UserAuthDO userAuthDO = codeFuseUserAuthService.selectByPrimaryKey(userId);
            ContextUtil.set(CONTEXT_USER, userAuthDO);
            ContextUtil.set(TOKEN_USER, user);

            chain.doFilter(request, response);
        } finally {
            ContextUtil.remove();
        }
    }

}