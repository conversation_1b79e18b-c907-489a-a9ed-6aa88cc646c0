package com.alipay.codegencore.service.common.segment;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.SegmentationStrategyTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @version : SegmentationStrategy.java, v 0.1 2024年01月11日 11:57 baoping Exp $
 */
public interface SegmentationStrategy {

    /**
     * @param text
     * @param documentChatConfig
     * @return
     */
    List<String> segment(String text, JSONObject documentChatConfig);
    /**
     * 获取分词策略类型
     *
     * <AUTHOR>
     * @since 2024.09.05
     * @return com.alipay.codegencore.model.enums.SegmentationStrategyTypeEnum
     */
    SegmentationStrategyTypeEnum getType();

}
