/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version $Id: ScriptLink.java, v 0.1 2023-05-30 16:55 wb-tzg858080 Exp $$
 */
public class ScriptLink extends ToString {
    /**
     * 序列号
     */
    private String sequence;
    /**
     * title
     */
    private String title;

    /**
     * url
     */
    private String url;

    public String getSequence() {
        return sequence;
    }

    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
