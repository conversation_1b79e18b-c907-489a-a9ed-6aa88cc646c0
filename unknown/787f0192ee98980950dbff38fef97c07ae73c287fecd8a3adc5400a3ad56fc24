package com.alipay.codegencore.service.impl;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.service.codegpt.CustomizeChatService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.impl.model.AlgoModelUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.StreamDataListener;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Flow;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.07.11
 */
@Service
@Slf4j
public class CustomizeChatServiceImpl implements CustomizeChatService {
    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");
    @Resource
    private AlgoModelUtilService algoModelUtilService;

    @Resource
    private CheckService checkService;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;
    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Override
    public void streamChat(GptAlgModelServiceRequest params) {
        Consumer<StreamResponseModel> resultHandler = params.getResultHandler();
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = params.getChatStreamPartResponseHandler();

        String requestId = params.getRequestId();
        String uniqueAnswerId = params.getUniqueAnswerId();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        // 对用户输入的问题进行审核,输入内容一批全部审
        List<ChatMessage> messages = chatCompletionRequest.getMessages();

        CHAT_LOGGER.info("codegpt completion requestId:{},user:{} request:{}", requestId, params.getUserName(), JSON.toJSONString(chatCompletionRequest));

        // 检查提问是否符合安全审查规范
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(requestId, messages, null, false);
        if (!requestCheckResultModel.isAllCheckRet()) {
            algoModelUtilService.processWhenQuestionFailCheck(requestCheckResultModel, chatStreamPartResponseConsumer, resultHandler);
        }
        String url = params.getCustomizeUrl();
        String streamInputId = String.format("%s%s", AppConstants.STREAM_INPUT_PREFIX, params.getUniqueAnswerId());
        appThreadPool.execute(() -> customizeChat(messages, chatCompletionRequest, requestId, uniqueAnswerId, streamInputId ,url));
        algoModelUtilService.getChatDataFromTBase(params, messages, requestCheckResultModel, false, new ChatStreamBuffer(),codeGPTDrmConfig.isModelEnableQueue());
    }

    /**
     * 处理仓库问答对话流程
     */
    private void customizeChat(List<ChatMessage> messages,
                               ChatCompletionRequest chatCompletionRequest,
                               String requestId,
                               String uniqueAnswerId,
                               String streamInputId,
                               String url) {

        Map<String, Object> requestData = new HashMap<>(5);
        requestData.put("messages", messages);
        requestData.put("stream", true);
        if(chatCompletionRequest.getChatRequestExtData()!=null){
            requestData.put("chatRequestExtData",chatCompletionRequest.getChatRequestExtData());
        }
        try {
            HttpClient.post(url)
                    .content(JSON.toJSONString(requestData))
                    .streamExecute(600000, new StreamDataListener() {

                        @Override
                        public void onConnect(Flow.Subscription subscription) {
                            log.info("customize answer on connect");
                        }

                        @Override
                        public void eachData(String data, Flow.Subscription subscription) {
                            // 处理取消逻辑

                            if (algoModelUtilService.needCloseInputStream(streamInputId)) {
                                log.info("close input stream");
                                subscription.cancel();
                           }
                            log.debug("customize answer each data: {}", data);

                            if (StringUtils.isBlank(data)) {
                                return;
                            }
                            Pair<String, ResponseEnum> customizeStreamContent = algoModelUtilService.getCustomizeStreamContent(data);
                            log.debug("customize response:{}, {}", customizeStreamContent.getKey(), customizeStreamContent.getValue());
                            String finishReason = customizeStreamContent.getValue() == null ? null : customizeStreamContent.getValue().name();
                            if(StringUtils.isNotBlank(customizeStreamContent.getKey())){
                                if(codeGPTDrmConfig.isModelEnableQueue()){
                                    algoModelUtilService.memoryQueueStreamData(new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), customizeStreamContent.getKey()),finishReason,uniqueAnswerId);
                                }else {
                                    ChatUtils.handleEveryStreamData(new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), customizeStreamContent.getKey()), finishReason, noneSerializationCacheManager, uniqueAnswerId);
                                }
                            }

                        }

                        @Override
                        public void onError(Throwable throwable) {
                            Pattern pattern = Pattern.compile("^Stream.*cancelled$", Pattern.DOTALL);
                            Matcher matcher = pattern.matcher(throwable.getMessage());
                            if (matcher.find()) {
                                log.info("手动结束流");
                                if(codeGPTDrmConfig.isModelEnableQueue()){
                                    algoModelUtilService.memoryQueueStreamData(null,ResponseEnum.SUCCESS.name(),uniqueAnswerId);
                                }else {
                                    ChatUtils.handleEveryStreamData(null, ResponseEnum.SUCCESS.name(), noneSerializationCacheManager, uniqueAnswerId);
                                }
                                return;
                            }
                            log.error("chagpt stream request failed, id: {}, ", requestId,  throwable);
                            if(codeGPTDrmConfig.isModelEnableQueue()){
                                algoModelUtilService.memoryQueueStreamError(ResponseEnum.ANSWER_CUSTOMIZE_FAIL.getErrorMsg(),uniqueAnswerId,ResponseEnum.STREAM_DELTA_PROCESS_FAILED);
                            }else {
                                algoModelUtilService.handleEveryStreamError(ResponseEnum.ANSWER_CUSTOMIZE_FAIL.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.STREAM_DELTA_PROCESS_FAILED);
                            }

                        }

                        @Override
                        public void onComplete() {
                            log.info("customize answer on complete");
                            if(codeGPTDrmConfig.isModelEnableQueue()){
                                algoModelUtilService.memoryQueueStreamData(null,ResponseEnum.SUCCESS.name(),uniqueAnswerId);
                            }else {
                                ChatUtils.handleEveryStreamData(null, ResponseEnum.SUCCESS.name(), noneSerializationCacheManager, uniqueAnswerId);
                            }

                        }

                    }, (statusCode, errorResponse) -> {
                        log.error("customize chat Stream error,statusCode:{},errorResponse:{}", statusCode, errorResponse);
                        ChatMessage endMessage = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), "自定义问答流程出错，请重试或者联系管理员排查");
                        if(codeGPTDrmConfig.isModelEnableQueue()){
                            algoModelUtilService.memoryQueueStreamData(endMessage,ResponseEnum.ANSWER_CUSTOMIZE_FAIL.name(),uniqueAnswerId);
                        }else {
                            ChatUtils.handleEveryStreamData(endMessage, ResponseEnum.ANSWER_CUSTOMIZE_FAIL.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId);
                        }

                    });
        } catch (Exception e) {
            log.error("自定义问答接口报错", e);
            if(codeGPTDrmConfig.isModelEnableQueue()){
                algoModelUtilService.memoryQueueStreamError(ResponseEnum.ANSWER_CUSTOMIZE_FAIL.getErrorMsg(),uniqueAnswerId,ResponseEnum.AI_CALL_ERROR);
            }else {
                algoModelUtilService.handleEveryStreamError(ResponseEnum.ANSWER_CUSTOMIZE_FAIL.getErrorMsg(), noneSerializationCacheManager, uniqueAnswerId, ResponseEnum.AI_CALL_ERROR);
            }
        }
    }
}
