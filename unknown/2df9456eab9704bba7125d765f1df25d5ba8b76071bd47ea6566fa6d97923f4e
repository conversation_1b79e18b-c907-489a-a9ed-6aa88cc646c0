package com.alipay.codegencore.model.enums;

/**
 * 内容审查平台的场景码
 */
public enum ContentCheckSceneCodeEnum {

    CTO_TECHPLAY_QUESTION("cto_techplay_question","cto_内网工具techplay_蚂蚁员工提问"),
    CTO_TECHPLAY_ANSWER("cto_techplay_answer","cto_内网工具techplay_ai回答"),

    CTO_CODEFUSE_QUESTION("cto_codefuse_question","cto_CodeFuse产品_提问"),
    CTO_CODEFUSE_ANSWER("cto_codefuse_answer","cto_CodeFuse产品_ai回答"),
    CTO_CODEFUSE_AITOPIC("cto_codefuse_aitopic","cto_codefuse_ai生成话题"),


    CTO_CODEFUSE_QUESTION_PUBLIC("cto_codefuse_question_public","cto_codefuse_提问_公网"),
    CTO_CODEFUSE_ANSWER_PUBLIC("cto_codefuse_answer_public","cto_codefuse_ai回答_公网"),
    CTO_CODEFUSE_AITOPIC_PUBLIC("cto_codefuse_aitopic_public","cto_codefuse_ai生成话题_公网"),
    ;

    private String sceneCode;
    private String sceneDesc;


    ContentCheckSceneCodeEnum(String sceneCode, String sceneDesc) {
        this.sceneCode = sceneCode;
        this.sceneDesc = sceneDesc;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public String getSceneDesc() {
        return sceneDesc;
    }

    /**
     * 根据场景码获取枚举数据
     * @param sceneCode 场景码
     * @return 枚举
     */
    public static ContentCheckSceneCodeEnum getBySceneCode(String sceneCode) {
        for (ContentCheckSceneCodeEnum contentCheckSceneCode : values()) {
            if (contentCheckSceneCode.sceneCode.equals(sceneCode)) {
                return contentCheckSceneCode;
            }
        }
        return null;
    }
}
