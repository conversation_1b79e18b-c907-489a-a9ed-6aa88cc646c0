ABSTRACT=1
ASSERT=2
BOOLEAN=3
BREAK=4
BYTE=5
CASE=6
CATCH=7
CHAR=8
CLASS=9
CONST=10
CONTINUE=11
DEFAULT=12
DO=13
DOUBLE=14
ELSE=15
ENUM=16
EXTENDS=17
FINAL=18
FINALLY=19
FLOAT=20
FOR=21
IF=22
GOTO=23
IMPLEMENTS=24
IMPORT=25
INSTANCEOF=26
INT=27
INTERFACE=28
LONG=29
NATIVE=30
NEW=31
PACKAGE=32
PRIVATE=33
PROTECTED=34
PUBLIC=35
RETURN=36
SHORT=37
STATIC=38
STRICTFP=39
SUPER=40
SWITCH=41
SYNCHRONIZED=42
THIS=43
THROW=44
THROWS=45
TRANSIENT=46
TRY=47
VOID=48
VOLATILE=49
WHILE=50
MODULE=51
OPEN=52
REQUIRES=53
EXPORTS=54
OPENS=55
TO=56
USES=57
PROVIDES=58
WITH=59
TRANSITIVE=60
VAR=61
YIELD=62
RECORD=63
SEALED=64
PERMITS=65
NON_SEALED=66
DECIMAL_LITERAL=67
HEX_LITERAL=68
OCT_LITERAL=69
BINARY_LITERAL=70
FLOAT_LITERAL=71
HEX_FLOAT_LITERAL=72
BOOL_LITERAL=73
CHAR_LITERAL=74
STRING_LITERAL=75
TEXT_BLOCK=76
NULL_LITERAL=77
LPAREN=78
RPAREN=79
LBRACE=80
RBRACE=81
LBRACK=82
RBRACK=83
SEMI=84
COMMA=85
DOT=86
ASSIGN=87
GT=88
LT=89
BANG=90
TILDE=91
QUESTION=92
COLON=93
EQUAL=94
LE=95
GE=96
NOTEQUAL=97
AND=98
OR=99
INC=100
DEC=101
ADD=102
SUB=103
MUL=104
DIV=105
BITAND=106
BITOR=107
CARET=108
MOD=109
ADD_ASSIGN=110
SUB_ASSIGN=111
MUL_ASSIGN=112
DIV_ASSIGN=113
AND_ASSIGN=114
OR_ASSIGN=115
XOR_ASSIGN=116
MOD_ASSIGN=117
LSHIFT_ASSIGN=118
RSHIFT_ASSIGN=119
URSHIFT_ASSIGN=120
ARROW=121
COLONCOLON=122
AT=123
ELLIPSIS=124
WS=125
COMMENT=126
LINE_COMMENT=127
IDENTIFIER=128
'abstract'=1
'assert'=2
'boolean'=3
'break'=4
'byte'=5
'case'=6
'catch'=7
'char'=8
'class'=9
'const'=10
'continue'=11
'default'=12
'do'=13
'double'=14
'else'=15
'enum'=16
'extends'=17
'final'=18
'finally'=19
'float'=20
'for'=21
'if'=22
'goto'=23
'implements'=24
'import'=25
'instanceof'=26
'int'=27
'interface'=28
'long'=29
'native'=30
'new'=31
'package'=32
'private'=33
'protected'=34
'public'=35
'return'=36
'short'=37
'static'=38
'strictfp'=39
'super'=40
'switch'=41
'synchronized'=42
'this'=43
'throw'=44
'throws'=45
'transient'=46
'try'=47
'void'=48
'volatile'=49
'while'=50
'module'=51
'open'=52
'requires'=53
'exports'=54
'opens'=55
'to'=56
'uses'=57
'provides'=58
'with'=59
'transitive'=60
'var'=61
'yield'=62
'record'=63
'sealed'=64
'permits'=65
'non-sealed'=66
'null'=77
'('=78
')'=79
'{'=80
'}'=81
'['=82
']'=83
';'=84
','=85
'.'=86
'='=87
'>'=88
'<'=89
'!'=90
'~'=91
'?'=92
':'=93
'=='=94
'<='=95
'>='=96
'!='=97
'&&'=98
'||'=99
'++'=100
'--'=101
'+'=102
'-'=103
'*'=104
'/'=105
'&'=106
'|'=107
'^'=108
'%'=109
'+='=110
'-='=111
'*='=112
'/='=113
'&='=114
'|='=115
'^='=116
'%='=117
'<<='=118
'>>='=119
'>>>='=120
'->'=121
'::'=122
'@'=123
'...'=124
