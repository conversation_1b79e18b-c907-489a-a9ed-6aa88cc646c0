package com.alipay.codegencore.model.model;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.model
 * @CreateTime : 2023-12-08
 */
public class ModelHealthCheck {

    /**
     * 生产环境
     */
    private HealthCheckRet prod;
    /**
     * 预发环境
     */
    private HealthCheckRet pre;

    public HealthCheckRet getProd() {
        return prod;
    }

    public void setProd(HealthCheckRet prod) {
        this.prod = prod;
    }

    public HealthCheckRet getPre() {
        return pre;
    }

    public void setPre(HealthCheckRet pre) {
        this.pre = pre;
    }

    public static class HealthCheckRet {
        /**
         * 健康度概览
         */
        private HealthCheckSummary summary;
        /**
         * 健康度检查时间
         */
        private String             checkTime;
        /**
         * 健康度检查历史
         */
        private HealthCheckHistory checkHistory;

        public HealthCheckSummary getSummary() {
            return summary;
        }

        public void setSummary(HealthCheckSummary summary) {
            this.summary = summary;
        }

        public String getCheckTime() {
            return checkTime;
        }

        public void setCheckTime(String checkTime) {
            this.checkTime = checkTime;
        }

        public HealthCheckHistory getCheckHistory() {
            return checkHistory;
        }

        public void setCheckHistory(HealthCheckHistory checkHistory) {
            this.checkHistory = checkHistory;
        }

        public static class HealthCheckSummary {
            /**
             * 健康度
             */
            private int    healthDegree;
            /**
             * 最后一次失败原因
             */
            private String lastFailedReason;

            public int getHealthDegree() {
                return healthDegree;
            }

            public void setHealthDegree(int healthDegree) {
                this.healthDegree = healthDegree;
            }

            public String getLastFailedReason() {
                return lastFailedReason;
            }

            public void setLastFailedReason(String lastFailedReason) {
                this.lastFailedReason = lastFailedReason;
            }
        }

        public static class HealthCheckHistory {
            /**
             * 上次可用时间
             */
            private String lastAvailable;
            /**
             * 上次不可用时间
             */
            private String lastUnavailable;

            public String getLastAvailable() {
                return lastAvailable;
            }

            public void setLastAvailable(String lastAvailable) {
                this.lastAvailable = lastAvailable;
            }

            public String getLastUnavailable() {
                return lastUnavailable;
            }

            public void setLastUnavailable(String lastUnavailable) {
                this.lastUnavailable = lastUnavailable;
            }
        }
    }
}
