package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.GptMessageFeedbackDOExample;
import com.alipay.codegencore.model.domain.GptMessageFeedbackDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface GptMessageFeedbackDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    long countByExample(GptMessageFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    int deleteByExample(GptMessageFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    @Delete({
        "delete from links_gpt_message_feedback",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    @Insert({
        "insert into links_gpt_message_feedback (gmt_create, gmt_modified, ",
        "mongo_id, deleted, ",
        "user_id, message_id, ",
        "agreed, content)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{mongoId,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, ",
        "#{userId,jdbcType=VARCHAR}, #{messageId,jdbcType=VARCHAR}, ",
        "#{agreed,jdbcType=TINYINT}, #{content,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(GptMessageFeedbackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    int insertSelective(GptMessageFeedbackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    List<GptMessageFeedbackDO> selectByExample(GptMessageFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, mongo_id, deleted, user_id, message_id, agreed, ",
        "content",
        "from links_gpt_message_feedback",
        "where id = #{id,jdbcType=BIGINT}"
    })
    GptMessageFeedbackDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    int updateByExampleSelective(@Param("record") GptMessageFeedbackDO record, @Param("example") GptMessageFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    int updateByExample(@Param("record") GptMessageFeedbackDO record, @Param("example") GptMessageFeedbackDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    int updateByPrimaryKeySelective(GptMessageFeedbackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_message_feedback
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    @Update({
        "update links_gpt_message_feedback",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "mongo_id = #{mongoId,jdbcType=VARCHAR},",
          "deleted = #{deleted,jdbcType=TINYINT},",
          "user_id = #{userId,jdbcType=VARCHAR},",
          "message_id = #{messageId,jdbcType=VARCHAR},",
          "agreed = #{agreed,jdbcType=TINYINT},",
          "content = #{content,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(GptMessageFeedbackDO record);
}