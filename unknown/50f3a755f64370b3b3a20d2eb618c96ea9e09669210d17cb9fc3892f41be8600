package com.alipay.codegencore.service.common;

import java.util.List;

import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.model.AlgoBackendModel;
import com.alipay.codegencore.model.model.ModelAvailableSimply;
import com.alipay.codegencore.model.model.ModelCurrentAvailableInfo;
import com.alipay.codegencore.model.response.PageResponse;


//AlgoBackendServiceImpl
public interface AlgoBackendService {


    /**
     * 通过名字查找模型（内部处理 jump 逻辑）
     * @param model 模型名
     * @return 模型信息
     */
    AlgoBackendDO getAlgoBackendByName(String model);

    /**
     * 获取当前模型(不跳转)
     * @param model
     * @return
     */
    AlgoBackendDO getAlgoBackendByNameNoJump(String model);

    /**
     * 通过Id查找模型
     * @param id
     * @return
     */
    AlgoBackendModel selectByPrimaryKey(Long id);

    /**
     * 获取默认的模型
     * @return 默认模型信息
     */
    AlgoBackendDO getDefaultAlgoBackend();
    /**
     * 获取所有模型
     * @return 所有的模型
     */
    List<AlgoBackendDO> getAllAlgoBackend();

    /**
     * 获取所有模型-分页
     * @param pageNo
     * @param pageSize
     * @param modelName  根据模模型名称模糊查询
     * @return
     */
    PageResponse<List<AlgoBackendModel>> getAllPageAlgoBackend(int pageNo, int pageSize, String modelName);


    /**
     * 新增模型
     * @param algoBackendDO
     * @return
     */
     Boolean addModel(AlgoBackendModel algoBackendModel, UserAuthDO userAuthDO);

    /**
     * 修改模型信息
     *
     * @param algoBackendModel
     * @return
     */
    Boolean updateModel(AlgoBackendModel algoBackendModel, UserAuthDO userAuthDO);

    /**
     * 获取模型jump配置的下拉列表
     *
     * @return
     */
    List<String> getModelJumpConfigList();

    /**
     * 删除指定模型信息
     *
     * @param id 模型id
     * @return
     */
    boolean deleteModel(long id, UserAuthDO userAuthDO);

    /**
     * 通过管理者ID查找模型
     * @param ownerUserId 管理者Id
     * @return 模型信息列表
     */
    List<AlgoBackendDO> getUserModelList(Long ownerUserId);
    /************** 用户模型管理 *******************************************************************/

    /**
     * 获取用户模型
     *
     * @param pageNo    页数
     * @param pageSize  每页大小
     * @param modelName 根据模模型名称模糊查询
     * @return
     */
    PageResponse<List<AlgoBackendModel>> getUserAlgoBackend(int pageNo, int pageSize, String modelName);

    /**
     * 通过Id查找模型
     *
     * @param id 模型id
     * @return
     */
    AlgoBackendModel getUserModelInfo(Long id);

    /**
     * 用户新增模型
     *
     * @return
     */
    Boolean addUserModel(AlgoBackendModel algoBackendDO);

    /**
     * 用户修改模型信息
     *
     * @param algoBackendDO 模型信息
     * @return
     */
    Boolean updateUserModel(AlgoBackendModel algoBackendDO);

    /**
     * 用户删除指定模型信息
     *
     * @param id 模型id
     * @return
     */
    boolean deleteUserModel(long id);

    /**
     * 获取基座模型
     *
     * @param pageNo    页数
     * @param pageSize  每页大小
     * @param modelName 根据模模型名称模糊查询
     * @return
     */
    PageResponse<List<AlgoBackendModel>> getModelBase(int pageNo, int pageSize, String modelName);


    /**
     * 增加模型使用消息数
     * @param model
     */
    void addUsageMessageCount(String model);

    /**
     * 增加使用用户数量
     *
     * @param model 模型名称
     */
    void addUsageUserCount(String model);

    /**
     * 增加使用会话数量
     *
     * @param model 模型名称
     */
    void addUsageSessionCount(String model);

    /**
     * 获取全员可见范围的启用模型
     *
     * @return 模型信息列表
     */
    List<AlgoBackendDO> getAllSeeModel();

    /**
     * 获取模型可用性信息
     *
     * @param pageNo    页数
     * @param pageSize  每页大小
     * @param modelName 模型名称
     * @return
     */
    PageResponse<List<ModelCurrentAvailableInfo>> getModelAvailableInfo(int pageNo, int pageSize, String modelName);

    /**
     * 获取单个模型的可用性信息
     * @param modelName
     * @return
     */
    ModelCurrentAvailableInfo getSingleModelAvailableInfo(String modelName);
    /**
     * 获取模型可用性信息
     *
     * @param modelNames 模型名称
     * @return
     */
    List<ModelAvailableSimply> checkModelsAvailable(List<String> modelNames);
}
