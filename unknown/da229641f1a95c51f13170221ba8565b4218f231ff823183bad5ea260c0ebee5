/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alipay.codegencore.model.model.links.Enum.GptConversationChannelEnum;
import com.alipay.codegencore.model.model.links.Enum.GptConversationStatusEnum;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $Id: GptConversationDO.java, v 0.1 2023-05-29 13:23 tanzhigang Exp $$
 */
public class GptConversationModel extends BaseModel {

    /**
     * 渠道
     */
    private GptConversationChannelEnum channel;

    /**
     * 渠道业务id
     */
    private String bizId;

    /**
     * 租户id
     */
    private String roomId;

    /**
     * 创建人
     */
    private String userId;

    /**
     * 状态
     */
    private GptConversationStatusEnum status;

    /**
     * 最后一条消息时间
     */
    private Date gmtLastMessage;

    /**
     * 会话名称
     */
    private String title;

    /**
     * 人工会话id
     */
    private String conversationId;

    /**
     * 扩展信息
     */
    private GptConversationExtInfo extInfo;

    public GptConversationChannelEnum getChannel() {
        return channel;
    }

    public void setChannel(GptConversationChannelEnum channel) {
        this.channel = channel;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public GptConversationStatusEnum getStatus() {
        return status;
    }

    public void setStatus(GptConversationStatusEnum status) {
        this.status = status;
    }

    public Date getGmtLastMessage() {
        return gmtLastMessage;
    }

    public void setGmtLastMessage(Date gmtLastMessage) {
        this.gmtLastMessage = gmtLastMessage;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public GptConversationExtInfo getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(GptConversationExtInfo extInfo) {
        this.extInfo = extInfo;
    }
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01 
     * @return java.lang.String
     */
    public String getCopilotSessionId(){
        return extInfo!= null ?extInfo.getCopilotSessionId():null ;
    }

}