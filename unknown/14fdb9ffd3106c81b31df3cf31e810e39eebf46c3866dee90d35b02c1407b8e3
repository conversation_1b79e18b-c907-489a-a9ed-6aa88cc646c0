/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.middle.dingding;

import java.util.List;

import com.alipay.codegencore.model.domain.DingDingMessageDO;

/**
 * <AUTHOR>
 * @version RobotSingleMessageService.java, v 0.1 2023年10月27日 下午2:50 yhw01352860
 */
public interface RobotSingleMessageService {

    /**
     * 钉钉机器人单聊消息
     * @param userIds
     * @param dingDingMessageDO
     */
    void sendMessage(List<String> userIds, DingDingMessageDO dingDingMessageDO);
}
