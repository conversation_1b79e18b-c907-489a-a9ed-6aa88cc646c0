<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alipay</groupId>
		<artifactId>codegencore-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<artifactId>codegencore-dal</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<dependencies>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>zcache-alipay-sofa-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>codegencore-utils</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>codegencore-model</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>dds-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.zdal</groupId>
			<artifactId>zdal-orm-annotation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>mq-alipay-sofa-boot-starter</artifactId>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>1.4.0</version>
				<configuration>
					<verbose>true</verbose>
					<overwrite>true</overwrite>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>mysql</groupId>
						<artifactId>mysql-connector-java</artifactId>
						<version>5.1.40</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>

</project>
