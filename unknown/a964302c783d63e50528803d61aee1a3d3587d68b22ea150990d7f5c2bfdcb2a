package com.alipay.codegencore.model.model.codegpt;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;

import java.util.List;
import java.util.function.Consumer;

/**
 * processFinish传递的参数
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023.12.29
 */
public class FinishParam {
    /**
     * tbase中读取的数据
     */
    private JSONObject streamJson;
    /**
     * 用户id
     */
    private String user;
    /**
     * tbase获取数据的key
     */
    private String key;
    /**
     * 请求uid
     */
    private String requestId;
    /**
     * 生成的回复uid
     */
    private String answerUid;
    /**
     * 流式数据下标
     */
    private int streamDataIndex;
    /**
     * 是否开启流式
     */
    private boolean streamChat;
    /**
     * 请求所有安全检查结果
     */
    private CheckResultModel requestCheckResultModel;
    /**
     * 流式传输的缓存数据
     */
    private ChatStreamBuffer streamBuffer;
    /**
     * functionCall参数
     */
    private ChatFunctionCall functionCall;
    /**
     * chat请求的拓展信息
     */
    private ChatRequestExtData chatRequestExtData;
    /**
     * 历史聊天记录
     */
    private List<ChatMessage> copyMessages;
    /**
     * 是否开启GPTCache
     */
    private boolean needGPTCache;
    /**
     * 当前调用的模型
     */
    private AlgoBackendDO algoBackendDO;
    /**
     * 用于前端交互的处理器
     */
    Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer;
    /**
     * 用于落库的处理器
     */
    Consumer<StreamResponseModel> resultHandler;
    /**
     * 用于tbase的数据使用完成之后的处理器，一般是删除TBase的数据
     */
    private Consumer<String> needDelTBaseKey;
    /**
     * 当前的模型环境
     */
    private String modelEnv;
    /**
     * 流式传输结束处理
     */
    private boolean stopStream;
    /**
     * runtimeInfo
     */
    private JSONObject runTimeInfo;

    public JSONObject getRunTimeInfo() {
        return runTimeInfo;
    }

    public void setRunTimeInfo(JSONObject runTimeInfo) {
        this.runTimeInfo = runTimeInfo;
    }

    public JSONObject getStreamJson() {
        return streamJson;
    }

    public void setStreamJson(JSONObject streamJson) {
        this.streamJson = streamJson;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getAnswerUid() {
        return answerUid;
    }

    public void setAnswerUid(String answerUid) {
        this.answerUid = answerUid;
    }

    public int getStreamDataIndex() {
        return streamDataIndex;
    }

    public void setStreamDataIndex(int streamDataIndex) {
        this.streamDataIndex = streamDataIndex;
    }

    public boolean isStreamChat() {
        return streamChat;
    }

    public void setStreamChat(boolean streamChat) {
        this.streamChat = streamChat;
    }

    public CheckResultModel getRequestCheckResultModel() {
        return requestCheckResultModel;
    }

    public void setRequestCheckResultModel(CheckResultModel requestCheckResultModel) {
        this.requestCheckResultModel = requestCheckResultModel;
    }

    public ChatStreamBuffer getStreamBuffer() {
        return streamBuffer;
    }

    public void setStreamBuffer(ChatStreamBuffer streamBuffer) {
        this.streamBuffer = streamBuffer;
    }

    public ChatFunctionCall getFunctionCall() {
        return functionCall;
    }

    public void setFunctionCall(ChatFunctionCall functionCall) {
        this.functionCall = functionCall;
    }

    public ChatRequestExtData getChatRequestExtData() {
        return chatRequestExtData;
    }

    public void setChatRequestExtData(ChatRequestExtData chatRequestExtData) {
        this.chatRequestExtData = chatRequestExtData;
    }

    public List<ChatMessage> getCopyMessages() {
        return copyMessages;
    }

    public void setCopyMessages(List<ChatMessage> copyMessages) {
        this.copyMessages = copyMessages;
    }

    public boolean isNeedGPTCache() {
        return needGPTCache;
    }

    public void setNeedGPTCache(boolean needGPTCache) {
        this.needGPTCache = needGPTCache;
    }

    public AlgoBackendDO getAlgoBackendDO() {
        return algoBackendDO;
    }

    public void setAlgoBackendDO(AlgoBackendDO algoBackendDO) {
        this.algoBackendDO = algoBackendDO;
    }

    public Consumer<ChatStreamPartResponse> getChatStreamPartResponseConsumer() {
        return chatStreamPartResponseConsumer;
    }

    public void setChatStreamPartResponseConsumer(Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer) {
        this.chatStreamPartResponseConsumer = chatStreamPartResponseConsumer;
    }

    public Consumer<StreamResponseModel> getResultHandler() {
        return resultHandler;
    }

    public void setResultHandler(Consumer<StreamResponseModel> resultHandler) {
        this.resultHandler = resultHandler;
    }

    public Consumer<String> getNeedDelTBaseKey() {
        return needDelTBaseKey;
    }

    public void setNeedDelTBaseKey(Consumer<String> needDelTBaseKey) {
        this.needDelTBaseKey = needDelTBaseKey;
    }

    public String getModelEnv() {
        return modelEnv;
    }

    public void setModelEnv(String modelEnv) {
        this.modelEnv = modelEnv;
    }

    public boolean isStopStream() {
        return stopStream;
    }

    public void setStopStream(boolean stopStream) {
        this.stopStream = stopStream;
    }
}
