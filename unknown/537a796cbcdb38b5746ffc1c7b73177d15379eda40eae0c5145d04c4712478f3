/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * 对象与字符串转换
 *
 * <AUTHOR>
 * @version $Id: BeanStringSwitcher.java, v 0.1 2020-05-08 16:45 zhi.huangcz Exp $$
 */
public interface BeanStringSwitcher {
    /**
     * 转为JSON字符串
     *
     * @return
     */
    String toJson();

    /**
     * 字符串转为对象
     *
     * @param json
     * @return
     */
    <T> T toBean(String json);
}