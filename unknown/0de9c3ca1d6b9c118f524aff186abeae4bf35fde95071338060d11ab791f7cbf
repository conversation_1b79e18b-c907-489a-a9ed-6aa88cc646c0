package com.alipay.codegencore.model.response.tsingyan;

import com.alipay.codegencore.model.model.CodeModel;

import java.io.Serializable;
import java.util.List;

/**
 * 代码补全结果
 *
 * <AUTHOR>
 * 创建时间 2022-10-12
 */
public class CompletionResultModel implements Serializable {
    /**
     * 防止字段变化导致反序列化失败
     */
    private static final long serialVersionUID = 1L;
    /**
     * 会话id
     */
    private Long sessionId;
    /**
     * 代码模型列表
     */
    private List<CodeModel> codeModelList;
    /**
     * 算法耗时
     */
    private Long processingTime = 0L;

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public List<CodeModel> getCodeModelList() {
        return codeModelList;
    }

    public void setCodeModelList(List<CodeModel> codeModelList) {
        this.codeModelList = codeModelList;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }
}
