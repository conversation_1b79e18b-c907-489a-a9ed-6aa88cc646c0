package com.alipay.codegencore.model.response.linke;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.response.linke
 * @CreateTime : 2024-04-10
 */
public class WorkItemVO {
    /**
    需求的URL
    */
    private String issueUrl;
    /**
     * 需求的ID
     */
    private String issueId;
    /**
     * 分配给谁
     */
    private String assignTo;
    /**
     * 创建者
     */
    private String author;
    /**
     * 需求类型
     */
    private String issueType;
    /**
     * 需求所在站点
     */
    private String issueSite;
    /**
     * 需求状态
     */
    private String issueStatus;
    /**
     * 需求标题
     */
    private String issueTitle;
    /**
     * 优先级
     */
    private String priority;
    /**
     * 创建时间
     */
    private String createAt;
    /**
     * 需求提供者
     */
    private String issueProvider;
    /**
     * 状态
     */
    private String status;

    public String getIssueUrl() {
        return issueUrl;
    }

    public void setIssueUrl(String issueUrl) {
        this.issueUrl = issueUrl;
    }

    public String getIssueId() {
        return issueId;
    }

    public void setIssueId(String issueId) {
        this.issueId = issueId;
    }

    public String getAssignTo() {
        return assignTo;
    }

    public void setAssignTo(String assignTo) {
        this.assignTo = assignTo;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getIssueType() {
        return issueType;
    }

    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    public String getIssueSite() {
        return issueSite;
    }

    public void setIssueSite(String issueSite) {
        this.issueSite = issueSite;
    }

    public String getIssueStatus() {
        return issueStatus;
    }

    public void setIssueStatus(String issueStatus) {
        this.issueStatus = issueStatus;
    }

    public String getIssueTitle() {
        return issueTitle;
    }

    public void setIssueTitle(String issueTitle) {
        this.issueTitle = issueTitle;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getCreateAt() {
        return createAt;
    }

    public void setCreateAt(String createAt) {
        this.createAt = createAt;
    }

    public String getIssueProvider() {
        return issueProvider;
    }

    public void setIssueProvider(String issueProvider) {
        this.issueProvider = issueProvider;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
