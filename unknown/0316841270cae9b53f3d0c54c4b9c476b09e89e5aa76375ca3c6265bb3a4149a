package com.alipay.codegencore.model.response;

import com.alipay.codegencore.model.domain.UserAuthDO;

/**
 * 查询用户时的返回值
 *
 * <AUTHOR>
 * 创建时间 2023-04-03
 */
public class RegisterUserResponse {
    /**
     * 用户认证信息
     */
    private UserAuthDO userAuthDO;


    /**
     * 排队等待验证的用户总量
     */
    private long verifyCount;
    /**
     * 是否自动审核
     */
    private boolean approvalFlag;

    public boolean isApprovalFlag() {
        return approvalFlag;
    }

    public void setApprovalFlag(boolean approvalFlag) {
        this.approvalFlag = approvalFlag;
    }

    public UserAuthDO getUserAuthDO() {
        return userAuthDO;
    }

    public void setUserAuthDO(UserAuthDO userAuthDO) {
        this.userAuthDO = userAuthDO;
    }

    public long getVerifyCount() {
        return verifyCount;
    }

    public void setVerifyCount(long verifyCount) {
        this.verifyCount = verifyCount;
    }
}
