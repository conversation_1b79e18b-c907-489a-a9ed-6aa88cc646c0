package com.alipay.codegencore.utils.dingding;

import java.util.HashMap;
import java.util.Map;
import com.alibaba.fastjson.JSON;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * 钉钉util
 */
public class DingDingUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger( DingDingUtil.class );

    private static final String DINGDING_ADDRESS = "https://oapi.dingtalk.com/robot/send";

    private static  String SECRET = null;
    private static  String TEST_SECRET = null;
    private static  String ACCESS_TOKEN = null;
    private static  String TEST_ACCESS_TOKEN = null;

    public static void init(String secret, String accessToken, String testSecret, String testAccessToken) {
        SECRET = secret;
        ACCESS_TOKEN = accessToken;
        TEST_SECRET = testSecret;
        TEST_ACCESS_TOKEN = testAccessToken;
    }


    /**
     * 发送钉钉消息
     *
     * @param message 消息
     */
    public static void sendMessage(String message) {
        sendMessage(message, false);
    }

    /**
     * 发送钉钉消息
     *
     * @param message 消息
     * @param test    是否发到测试群
     */
    public static String sendMessage(String message, boolean test) {
        try {
            Long timestamp = System.currentTimeMillis();
            String sign = getSign(timestamp, test);
            String accessToken = test ? TEST_ACCESS_TOKEN : ACCESS_TOKEN;
            Map<String,Object> msgBody = new HashMap<>();
            msgBody.put("msgtype","text");
            msgBody.put("text", new HashMap(){{
                put("content", message);
            }});
            String url = DINGDING_ADDRESS + "?access_token=" + accessToken + "&sign=" + sign + "&timestamp=" + timestamp;
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            // 设置请求体
            StringEntity entity = new StringEntity(JSON.toJSONString(msgBody), "UTF-8");
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-Type", "application/json;charset=UTF8");
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity httpEntity = httpResponse.getEntity();
            String responseStr = EntityUtils.toString(httpEntity, "UTF-8");
            LOGGER.info("sendMessage response：{}",responseStr);
            return responseStr;
        } catch (Exception e) {
            LOGGER.error("DingDingServerError: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private static String getSign(Long timestamp, boolean test) {

        String sign = null;

        String secret = test ? TEST_SECRET : SECRET;
        try {
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            sign = new String(Base64.encodeBase64(signData));
        } catch (Exception e) {
            LOGGER.error("get sign error", e);
            return null;
        }

        return sign;
    }
}