/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version $Id: BaseForm.java, v 0.1 2020-11-20 14:37 zhi.huangcz Exp $$
 */
public class BaseForm extends ToString {
    /** 业务模型ID */
    private String id;
    /** 操作人工号 */
    private String operatorEmpId;
    /** 操作类型 */
    private String actionType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOperatorEmpId() {
        return operatorEmpId;
    }

    public void setOperatorEmpId(String operatorEmpId) {
        this.operatorEmpId = operatorEmpId;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }
}