/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.web.linkide;

import com.alipay.codegencore.model.model.CompletionsCodeModel;
import com.alipay.codegencore.model.request.CompletionsRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.CodeRecommandService;
import com.alipay.codegencore.service.utils.AsyncLogUtils;
import com.alipay.codegencore.web.linkide.RecommandController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.PrivateAccess;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.smartunit.shaded.org.mockito.Mockito.*;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class RecommandController_SSTest extends RecommandController_SSTest_scaffolding {
// allCoveredLines:[29, 46, 48, 49, 51, 52, 55, 56, 57, 58, 59, 60]

  @Test(timeout = 4000)
  public void test_completions_0()  throws Throwable  {
      //caseID:b740e51005977046eea8f604238ccc6d
      //CoveredLines: [29, 46, 48, 57, 58, 59, 60]
      //Input_0_CompletionsRequestBean: null
      //Assert: assertNull(method_result.getErrorMsg());
      
      RecommandController recommandController0 = new RecommandController();
      //mock asyncLogUtils0
      AsyncLogUtils asyncLogUtils0 = mock(AsyncLogUtils.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      PrivateAccess.setVariable((Class<?>) RecommandController.class, recommandController0, "asyncLogUtils", (Object) asyncLogUtils0);
      
      //Call method: completions
      BaseResponse<CompletionsCodeModel> baseResponse0 = recommandController0.completions((CompletionsRequestBean) null);
      
      //Test Result Assert
      assertNull(baseResponse0.getErrorMsg());
  }

  @Test(timeout = 4000)
  public void test_completions_1()  throws Throwable  {
      //caseID:ffb99ca2193b8cd0a0edaca0abb6bd01
      //CoveredLines: [29, 46, 48, 49, 51, 55, 56]
      //Input_0_CompletionsRequestBean: {}
      //Assert: assertEquals(0, method_result.getErrorCode());
      
      RecommandController recommandController0 = new RecommandController();
      //mock asyncLogUtils0
      AsyncLogUtils asyncLogUtils0 = mock(AsyncLogUtils.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      PrivateAccess.setVariable((Class<?>) RecommandController.class, recommandController0, "asyncLogUtils", (Object) asyncLogUtils0);
      //mock completionsRequestBean0
      CompletionsRequestBean completionsRequestBean0 = mock(CompletionsRequestBean.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: completions
      BaseResponse<CompletionsCodeModel> baseResponse0 = recommandController0.completions(completionsRequestBean0);
      
      //Test Result Assert
      assertEquals(0, baseResponse0.getErrorCode());
  }

  @Test(timeout = 4000)
  public void test_completions_2()  throws Throwable  {
      //caseID:fe9a4351465fc1fcbc48feebdf228a7e
      //CoveredLines: [29, 46, 48, 49, 51, 52, 55, 56]
      //Input_0_CompletionsRequestBean: {getUserLabel=\"1.0\", getPrompt=\"1.0\", getUserDir=\"1.0\"}
      //Assert: assertNull(method_result.getErrorMsg());
      
      RecommandController recommandController0 = new RecommandController();
      //mock codeRecommandService0
      CodeRecommandService codeRecommandService0 = mock(CodeRecommandService.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock asyncLogUtils0
      AsyncLogUtils asyncLogUtils0 = mock(AsyncLogUtils.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      PrivateAccess.setVariable((Class<?>) RecommandController.class, recommandController0, "asyncLogUtils", (Object) asyncLogUtils0);
      
      PrivateAccess.setVariable((Class<?>) RecommandController.class, recommandController0, "codeRecommandService", (Object) codeRecommandService0);
      //mock completionsRequestBean0
      CompletionsRequestBean completionsRequestBean0 = mock(CompletionsRequestBean.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1.0").when(completionsRequestBean0).getUserDir();
      doReturn("1.0").when(completionsRequestBean0).getUserLabel();
      doReturn("1.0").when(completionsRequestBean0).getPrompt();
      
      //Call method: completions
      BaseResponse<CompletionsCodeModel> baseResponse0 = recommandController0.completions(completionsRequestBean0);
      
      //Test Result Assert
      assertNull(baseResponse0.getErrorMsg());
  }
}
