package com.alipay.codegencore.web.openapi;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.ideaevo.IdeaEvoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * codefuse助手openapi
 */
@RestController
@RequestMapping("/api/ideaEvo")
@Slf4j
public class IdeaEvoController {

    @Resource
    private IdeaEvoService ideaEvoService;

    @Resource
    private UserAclService userAclService;

    /**
     * ai代码计划生成
     *
     * @param codeGPTUser
     * @param codeGPTToken
     * @param featureData
     * @return
     */
    @PostMapping(path = "/plan")
    public BaseResponse<JSONObject> plan(HttpServletRequest httpServletRequest,
                                         @RequestHeader(value = "modelEnv", defaultValue = "auto") String modelEnv,
                                         @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                         @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                         @RequestBody JSONObject featureData) {
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        JSONObject plan = ideaEvoService.aiCodePlan(featureData, modelEnv);
        return BaseResponse.build(plan);
    }

    /**
     * 代码生成
     * @param httpServletRequest
     * @param codeGPTUser
     * @param codeGPTToken
     * @param featureData
     * @return
     */
    @PostMapping(path = "/codegen")
    public BaseResponse<JSONArray> codegen(HttpServletRequest httpServletRequest,
                                           @RequestHeader(value = "modelEnv", defaultValue = "auto") String modelEnv,
                                           @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                           @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                           @RequestBody JSONObject featureData) {
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        JSONArray plan = ideaEvoService.aiCodeGen(featureData, modelEnv);
        return BaseResponse.build(plan);
    }
}
