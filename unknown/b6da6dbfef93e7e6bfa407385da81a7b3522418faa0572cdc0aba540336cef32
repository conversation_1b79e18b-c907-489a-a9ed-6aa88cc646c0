package com.alipay.codegencore.utils.codescan;

import org.antlr.v4.runtime.atn.PredictionContextCache;

/**
 * 自定义缓存，继承自{@link org.antlr.v4.runtime.atn.PredictionContextCache}
 * 原因：PredictionContextCache内部的cache数据会不断递增，需要有清空操作
 * <AUTHOR>
 * 创建时间 2022-06-21
 */
public class CodegenContextCache extends PredictionContextCache {
    /**
     * 清空缓存数据
     */
    public void clean(){
        cache.clear();
    }
}
