package com.alipay.codegencore.model.model;

/**
 * 埋点信息配置
 *
 * <AUTHOR>
 * 创建时间 2023-02-03
 */
public class EventTrackConfigModel {
    /**
     * 启动多久后开启埋点上传定时任务 从内存中（单位：分钟）
     */
    private long startSubmitReportEventTrackTime = 2L;

    /**
     * 埋点上传定时任务间隔时间，从内存中（单位：分钟）
     */
    private long submitReportEventTrackTime = 5L;

    /**
     * 启动多久后开启上传 日志文件 中的埋点（单位：分钟）
     */
    private long startSubmitReportEventTrackFromFileTime = 1L;

    /**
     * 上传 日志文件 中的埋点定时任务间隔时间（单位：分钟）
     */
    private long submitReportEventTrackFromFileTime = 5L;


    /**
     * 启动多久后开启埋点上传定时任务 从内存中（单位：分钟）
     */
    private long startSubmitReportFileTokenEventTrackTime = 30L;

    /**
     * 埋点上传定时任务间隔时间，从内存中（单位：分钟）
     */
    private long submitReportFileTokenEventTrackTime = 2 * 60L;

    /**
     * 异常上报间隔时间（ms） 默认为一天内不上传重复异常
     */
    private long exceptionReportIntervalTimeMillis = 24 * 60 * 60 * 60 * 1000L;

    /**
     * 日志文件最大占用磁盘大小（byte） 默认为10M
     */
    private long logFileMaxSize = 10 * 1024 * 1024L;

    /**
     * 日志文件最大个数
     */
    private int logFileMaxNum = 4;

    /**
     * 埋点环形缓冲区大小
     */
    private int eventTrackRingBufferSize = 16 * 1024;

    public long getStartSubmitReportEventTrackTime() {
        return startSubmitReportEventTrackTime;
    }

    public void setStartSubmitReportEventTrackTime(long startSubmitReportEventTrackTime) {
        this.startSubmitReportEventTrackTime = startSubmitReportEventTrackTime;
    }

    public long getSubmitReportEventTrackTime() {
        return submitReportEventTrackTime;
    }

    public void setSubmitReportEventTrackTime(long submitReportEventTrackTime) {
        this.submitReportEventTrackTime = submitReportEventTrackTime;
    }

    public long getStartSubmitReportEventTrackFromFileTime() {
        return startSubmitReportEventTrackFromFileTime;
    }

    public void setStartSubmitReportEventTrackFromFileTime(long startSubmitReportEventTrackFromFileTime) {
        this.startSubmitReportEventTrackFromFileTime = startSubmitReportEventTrackFromFileTime;
    }

    public long getSubmitReportEventTrackFromFileTime() {
        return submitReportEventTrackFromFileTime;
    }

    public void setSubmitReportEventTrackFromFileTime(long submitReportEventTrackFromFileTime) {
        this.submitReportEventTrackFromFileTime = submitReportEventTrackFromFileTime;
    }

    public long getStartSubmitReportFileTokenEventTrackTime() {
        return startSubmitReportFileTokenEventTrackTime;
    }

    public void setStartSubmitReportFileTokenEventTrackTime(long startSubmitReportFileTokenEventTrackTime) {
        this.startSubmitReportFileTokenEventTrackTime = startSubmitReportFileTokenEventTrackTime;
    }

    public long getSubmitReportFileTokenEventTrackTime() {
        return submitReportFileTokenEventTrackTime;
    }

    public void setSubmitReportFileTokenEventTrackTime(long submitReportFileTokenEventTrackTime) {
        this.submitReportFileTokenEventTrackTime = submitReportFileTokenEventTrackTime;
    }

    public long getExceptionReportIntervalTimeMillis() {
        return exceptionReportIntervalTimeMillis;
    }

    public void setExceptionReportIntervalTimeMillis(long exceptionReportIntervalTimeMillis) {
        this.exceptionReportIntervalTimeMillis = exceptionReportIntervalTimeMillis;
    }

    public long getLogFileMaxSize() {
        return logFileMaxSize;
    }

    public void setLogFileMaxSize(long logFileMaxSize) {
        this.logFileMaxSize = logFileMaxSize;
    }

    public int getLogFileMaxNum() {
        return logFileMaxNum;
    }

    public void setLogFileMaxNum(int logFileMaxNum) {
        this.logFileMaxNum = logFileMaxNum;
    }

    public int getEventTrackRingBufferSize() {
        return eventTrackRingBufferSize;
    }

    public void setEventTrackRingBufferSize(int eventTrackRingBufferSize) {
        this.eventTrackRingBufferSize = eventTrackRingBufferSize;
    }
}
