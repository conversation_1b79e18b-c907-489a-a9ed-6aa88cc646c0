package com.alipay.codegencore.service.middle.msgbroker;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.DingDingMessageDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.impl.MayaServiceImpl;
import com.alipay.codegencore.service.impl.model.AlgoModelHealthUtilService;
import com.alipay.codegencore.service.middle.dingding.RobotSingleMessageService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.common.event.UniformEvent;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Service("modelHealthDegreeNotifyListener")
public class ModelHealthDegreeNotifyListener implements CodegencoreEventHandler {
    /**
     * topic|eventCode的Key 一般用于注册
     */
    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_F_SC|EC_codegencore_model_health_degree_notify";

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private UserAuthDOMapper userAuthDOMapper;

    @Resource
    private RobotSingleMessageService robotSingleMessageService;

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelHealthDegreeNotifyListener.class);

    @Override
    public void handle(UniformEvent message) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to notify model health degree");
        }
        appThreadPool.submit(()-> {
            String blackModelStr = codeGPTDrmConfig.getModelKeepLivingBlackList();
            List<String> blackList = null;
            if (blackModelStr != null && !blackModelStr.isEmpty()) {
                try {
                    blackList = JSONObject.parseObject(blackModelStr, List.class);
                } catch (Exception ex) {
                    LOGGER.warn("transfer modelKeepLivingBlackList failed, {}", blackModelStr, ex);
                }
            }

            final List<String> blackListF = blackList == null ? new ArrayList<>() : blackList;
            List<AlgoBackendDO> algoBackendDOList = MayaServiceImpl.jumpLogic(algoBackendService.getAllAlgoBackend())
                    .stream()
                    .filter(AlgoBackendDO::getEnable)
                    .filter(MayaServiceImpl::isMayaImpl)
                    .filter(a -> !blackListF.contains(a.getModel()))
                    .collect(Collectors.toList());
            List<String> checkModelStr = algoBackendDOList.stream().map(AlgoBackendDO::getModel).collect(Collectors.toList());
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("total model health degree notify, {}, {}", checkModelStr.size(), JSONObject.toJSONString(checkModelStr));
            }
            for (AlgoBackendDO algoBackendDO: algoBackendDOList) {
                if(LOGGER.isInfoEnabled()) {
                    LOGGER.info("start to notify model health degree, {}", algoBackendDO.getModel());
                }
                notifyModelHealthDegree(algoBackendDO);
                if(LOGGER.isInfoEnabled()) {
                    LOGGER.info("finish to notify model health degree, {}", algoBackendDO.getModel());
                }
            }
        });
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("end to notify model health degree");
        }
    }

    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }

    private void notifyModelHealthDegree(AlgoBackendDO algoBackendDO) {
        if(null == algoBackendDO.getOwnerUserId()
            || 0 == algoBackendDO.getOwnerUserId()) {
            LOGGER.warn("find model owner is empty, {}", algoBackendDO.getModel());
            return;
        }

        UserAuthDO userAuthDO = userAuthDOMapper.selectByPrimaryKey((long) algoBackendDO.getOwnerUserId());
        if(null == userAuthDO
            || StringUtils.isBlank(userAuthDO.getEmpId())) {
            LOGGER.warn("can't find user info or empid is empty : {}", algoBackendDO.getOwnerUserId());
            return;
        }

        Integer isProdAvailable = algoModelHealthUtilService.isHealth("prod", algoBackendDO.getModel());
        Integer isPreAvailable = algoModelHealthUtilService.isHealth("pre", algoBackendDO.getModel());


        if(Objects.equals(isProdAvailable, AlgoModelHealthUtilService.HealthDegreeEnum.USABLE.getCode())
                || Objects.equals(isPreAvailable, AlgoModelHealthUtilService.HealthDegreeEnum.USABLE.getCode()) ) {
            LOGGER.info("prod or pre, or both available, so don't need send Dingding, {}", algoBackendDO.getModel());
            return;
        }


        sendDingding(algoBackendDO.getModel(), userAuthDO.getEmpId());
    }

    private void sendDingding(String model, String empid) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("model health degree notify : {}, {}", model, empid);
        }

        String defaultMembers = codeGPTDrmConfig.getNotifyMembersDefault();
        final List<String> empids = new ArrayList<>();

        if(StringUtils.isNotBlank(defaultMembers)) {
            try {
                JSONArray ja = JSONObject.parseObject(defaultMembers, JSONArray.class);
                ja.forEach(m->empids.add((String)m));
            } catch(Exception exception) {
                LOGGER.warn("failed to transfer drm's notifyMembersDefault, {}", defaultMembers);
            }
        }
        if(!empids.contains(empid)) {
            empids.add(empid);
        }
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("send info : {}, {}", model, empid);
        }

        try {
            robotSingleMessageService.sendMessage(empids, constructMessage(model));
        } catch(Exception ex) {
           LOGGER.warn("send ding exception, {}, {}", model, empid, ex);
        }
    }

    private DingDingMessageDO constructMessage(String model) {

        String content = "您在codefuse上配置的模型" +
                model +
                "在预发和线上环境都不可以用了，请及时处理!";

        DingDingMessageDO dingDingMessageDO = new DingDingMessageDO("模型服务不可用"
                ,content, "处理方法 ", "https://yuque.antfin.com/iikq97/lozx5s/ek3g6egc3cvba87d?singleDoc# 《codefuse模型不可用处理》");
        return dingDingMessageDO;
    }

}


