package com.alipay.codegencore.web.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.enums.RecordLogEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.CollectLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 打印http接口的日志信息
 * <AUTHOR>
 * @version : WebServerLogFilter.java, v 0.1 2023年05月22日 10:43 baoping Exp $
 */
@WebFilter(urlPatterns = {"/api/assistant/**", "/webapi/**", "/api/chat/**"})
@Slf4j
@Configuration
@Order(2)
public class WebServerLogFilter implements Filter {
    private static final Logger GOC_LOGGER = LoggerFactory.getLogger("GOCLOG");
    private static final Logger OTHERS_INFO = LoggerFactory.getLogger("OTHERSINFO");

    @Autowired
    private SceneService sceneService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        String requestURI = new UrlPathHelper().getLookupPathForRequest((HttpServletRequest)request);
        List<String> ignoreUrlList = Arrays.asList("/webapi/user/heartbeat");
        if ((requestURI.startsWith("/api/assistant") || requestURI.startsWith("/webapi") || requestURI.startsWith("/api/chat") || requestURI.startsWith("/yuque")) && !ignoreUrlList.contains(requestURI)) {
            MultiReadHttpServletResponse responseWrapper = new MultiReadHttpServletResponse((HttpServletResponse) response);
            MultiReadHttpServletRequest requestWrapper = new MultiReadHttpServletRequest((HttpServletRequest) request);
            request.setAttribute("startTime", System.currentTimeMillis());
            chain.doFilter(requestWrapper, responseWrapper);
            printFinishLog(requestWrapper, responseWrapper);
        } else {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }

    /**
     * 打印http接口的监控日志
     * @param request
     * @param wrapper
     */
    private void printFinishLog(MultiReadHttpServletRequest request, MultiReadHttpServletResponse wrapper) {
        String url = new UrlPathHelper().getLookupPathForRequest(request);
        String data = new String(wrapper.getBytes(), StandardCharsets.UTF_8);


        // 日志默认值 请求url、执行时间、生成速度、模型、场景、场景模式、结束原因
        long executeTime = System.currentTimeMillis() - (long)request.getAttribute("startTime");
        int speed = -1;
        String model = null;
        long sceneId = -1;
        int mode = -1;
        String finishReason = null;

        try {
            if (isJsonData(data)) {
                JSONObject json = JSON.parseObject(data);
                finishReason = getFinishReason(json);
                if (url.startsWith("/api/chat/")) {
                    model = url.split("/")[3];
                    if (json.containsKey("data") && json.get("data") != null) {
                        speed = (int) (json.getString("data").length() / (executeTime / 1000 + 1));
                    }
                }
            } else {
                // 新流式结构
                if (isNewStreamData(data)) {
                    // 通过反序列化的方式得到数据
                    List<NewPluginStreamPartResponse> chList = transData2NewStream(data);
                    finishReason = chList.get(chList.size()-1).getFinishReason();

                    // 获取模型信息
                    SceneDO sceneDO = sceneService.getSceneByMessageUid(chList.get(0).getId());
                    model = sceneDO.getModel();
                    sceneId = sceneDO.getId();
                    mode = sceneDO.getMode();

                    // 不记录暂停状态
                    if (finishReason.equals(ResponseEnum.PAUSE.name())) {
                        return;
                    }

                    // 计算文字生成速度
                    if (isNormalStream(chList)) {
                        speed = (int) (getNewStreamContent(chList).length() / (executeTime / 1000 + 1));
                    }
                } else {
                    // 旧流式结构
                    List<ChatStreamPartResponse> chList = transData2OldStream(data);
                    finishReason = chList.get(chList.size()-1).getChoices().get(0).getFinishReason();

                    // 获取模型信息
                    if (url.startsWith("/api/chat/")) {
                        model = url.split("/")[3];
                    } else {
                        // 这个字段目前没有用了，如果不把模型信息放在url里，没有方法获取到请求的模型信息
                        model = chList.get(0).getModel();
                    }

                    // 计算文字生成速度
                    speed = (int) (getOldStreamContent(chList).length() / (executeTime / 1000 + 1));
                }
            }
            if(url.startsWith("/api/chat")&&url.endsWith("/completion")){
                printOpenApiDataLog(request,data);
            }
            // 打印日志
            GOC_LOGGER.info(",{},{},{},{},{},{},{}",  url, finishReason, executeTime, speed, model, sceneId, mode);
        } catch (Exception e) {
            GOC_LOGGER.info(",{},{},{},{},{},{},{}",  url, finishReason, executeTime, speed, model, sceneId, mode);
            return;
        }
    }

    /**
     * 将data数据反序列化为List<ChatStreamPartResponse>
     * @param data
     * @return
     */
    private List<NewPluginStreamPartResponse> transData2NewStream(String data) {
        String[] jsons = data.split( "\n\n");

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("[");
        for (int i = 0; i < jsons.length; i++) {
            stringBuilder.append(StringUtils.substringAfter(jsons[i], "data:"));
            if (i != jsons.length-1) {
                stringBuilder.append(",");
            }
        }
        stringBuilder.append("]");

        return JSON.parseArray(stringBuilder.toString(), NewPluginStreamPartResponse.class);
    }

    /**
     * 将data数据反序列化为List<ChatStreamPartResponse>
     * @param data
     * @return
     */
    private List<ChatStreamPartResponse> transData2OldStream(String data) {
        String[] jsons = data.split( "\n\n");

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("[");
        for (int i = 0; i < jsons.length; i++) {
            stringBuilder.append(StringUtils.substringAfter(jsons[i], "data:"));
            if (i != jsons.length-1) {
                stringBuilder.append(",");
            }
        }
        stringBuilder.append("]");

        return JSON.parseArray(stringBuilder.toString(), ChatStreamPartResponse.class);
    }

    /**
     * 通过response反序列化的List<NewPluginStreamPartResponse>获取content
     * @param chList
     * @return
     */
    private String getNewStreamContent(List<NewPluginStreamPartResponse> chList) {
        StringBuilder stringBuilder = new StringBuilder();
        for (NewPluginStreamPartResponse ch : chList) {
            if (StringUtils.equals(ch.getType(), "answer") && ch.getContent() != null) {
                stringBuilder.append(ch.getContent());
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 通过response反序列化的List<ChatStreamPartResponse>获取content
     * @param chList
     * @return
     */
    private String getOldStreamContent(List<ChatStreamPartResponse> chList) {
        StringBuilder stringBuilder = new StringBuilder();
        for (ChatStreamPartResponse ch : chList) {
            for (ChatStreamPartResponse.Choice choice : ch.getChoices()) {
                if (choice.getDelta().getContent() != null) {
                    stringBuilder.append(choice.getDelta().getContent());
                }
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 通过流式信息判断是普通流式还是插件流式
     * @param chList
     * @return
     */
    private boolean isNormalStream(List<NewPluginStreamPartResponse> chList) {
        return StringUtils.equals(chList.get(0).getType(), "answer");
    }

    /**
     * 判断流式信息是否是新流式
     * @param data
     * @return
     */
    private boolean isNewStreamData(String data) {
        String[] jsons = data.split( "\n\n");
        JSONObject jsonObject = JSONObject.parseObject(StringUtils.substringAfter(jsons[0], "data:"));
        return !jsonObject.containsKey("choices");
    }

    /**
     * 判断data是否是json格式
     * @param data
     * @return
     */
    private boolean isJsonData(String data) {
        try {
            JSONObject.parseObject(data);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 通过返回的response body获取finishReason
     * @param json
     * @return
     */
    private String getFinishReason(JSONObject json) {
        String finishReason = json.getString("errorType");
        if (finishReason == null) {
            int errorCode = (int) json.getOrDefault("errorCode", 999);
            finishReason = ResponseEnum.getByCode(errorCode).name();
        }
        return finishReason;
    }
    /**
     * 记录日志：openapi调用的输入和输出
     *
     * <AUTHOR>
     * @since 2024.02.20
     * @param request request
     * @param data data
     */
    private void printOpenApiDataLog(MultiReadHttpServletRequest request,String data){
        if(codeGPTDrmConfig.getOpenApiLogSwitch().equalsIgnoreCase("close")){
            // 日志未开启
            return;
        }
        OTHERS_INFO.info("openAPI call write to log the data: {}", data.replaceAll("\\r|\\n", ""));
        try{
            String url = new UrlPathHelper().getLookupPathForRequest(request);
            String[] parts = url.split("/");
            String body = new String(request.getBody(), StandardCharsets.UTF_8);
            Map<String,Object> recordInfo = new HashMap<>();
            ChatCompletionRequest chatCompletionRequest = JSONObject.parseObject(body, ChatCompletionRequest.class);
            recordInfo.put("stream", chatCompletionRequest.getStream());
            recordInfo.put("model",parts[3]);
            recordInfo.put("user", request.getHeader("codegpt_user"));
            if(CollectionUtils.isNotEmpty(chatCompletionRequest.getMessages())){
                recordInfo.put("messages", JSONObject.toJSONString(chatCompletionRequest.getMessages()));
            }
            if(StringUtils.isNotBlank(chatCompletionRequest.getPrompt())){
                recordInfo.put("prompt",chatCompletionRequest.getPrompt().replaceAll("\\r|\\n", ""));
            }
            if((Boolean) recordInfo.get("stream")){
                recordInfo.put("result",parseStreamData(data));
            }else{
                // 非流式
                JSONObject jsonObject = JSONObject.parseObject(data);
                if(StringUtils.isNotBlank(jsonObject.getString("data"))){
                    recordInfo.put("result",jsonObject.getString("data").replaceAll("\\r|\\n", ""));
                }else {
                    recordInfo.put("result","model execute fail for ErrorMsg:"+jsonObject.getString("errorMsg").replaceAll("\\r|\\n", ""));
                }
            }
            CollectLogUtils.printCollectLogVersion2(RecordLogEnum.API_DATA, recordInfo);
        }catch (Throwable e){
            OTHERS_INFO.error("openAPI call write to log the data error", e);
        }


    }

    /**
     * 解析流式调用的数据
     *
     * <AUTHOR>
     * @since 2024.02.20
     * @param data data
     * @return java.lang.String
     */
    private String parseStreamData(String data){
        data = data.replaceAll("\\r|\\n", "");
        String[] jsonStrings = data.split("data: ");
        StringBuilder result = new StringBuilder();
        for (String jsonString : jsonStrings) {
            if(StringUtils.isNotBlank(jsonString.trim())){
                ChatStreamPartResponse chatStreamPartResponse = JSONObject.parseObject(jsonString.trim(), ChatStreamPartResponse.class);
                if(StringUtils.isBlank(chatStreamPartResponse.getChoices().get(0).getFinishReason())){
                    result.append(chatStreamPartResponse.getChoices().get(0).getDelta().getContent());
                }else {
                    boolean streamFinishResult = chatStreamPartResponse.getChoices().get(0).getFinishReason().equalsIgnoreCase("stop") || chatStreamPartResponse.getChoices().get(0).getFinishReason().equalsIgnoreCase("SUCCESS") || chatStreamPartResponse.getChoices().get(0).getFinishReason().equalsIgnoreCase("PAUSE");
                    if(streamFinishResult){
                        result.append(chatStreamPartResponse.getChoices().get(0).getDelta().getContent());
                    }else {
                        result.append(" --model execute fail for ErrorMsg:");
                        result.append(chatStreamPartResponse.getChoices().get(0).getDelta().getContent());
                    }
                }

            }
        }
        return result.toString().replaceAll("\\r|\\n", "");
    }
}
