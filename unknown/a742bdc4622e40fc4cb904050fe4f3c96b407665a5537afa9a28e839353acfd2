<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.UserPluginRecordsDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.UserPluginRecordsDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="plugin_id" jdbcType="BIGINT" property="pluginId" />
    <result column="control_type" jdbcType="INTEGER" property="controlType" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    id, gmt_create, gmt_modified, user_id, plugin_id, control_type, deleted
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.UserPluginRecordsDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_user_plugin_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.UserPluginRecordsDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    delete from cg_user_plugin_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.UserPluginRecordsDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_user_plugin_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="pluginId != null">
        plugin_id,
      </if>
      <if test="controlType != null">
        control_type,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="pluginId != null">
        #{pluginId,jdbcType=BIGINT},
      </if>
      <if test="controlType != null">
        #{controlType,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.UserPluginRecordsDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    select count(*) from cg_user_plugin_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    update cg_user_plugin_records
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.pluginId != null">
        plugin_id = #{record.pluginId,jdbcType=BIGINT},
      </if>
      <if test="record.controlType != null">
        control_type = #{record.controlType,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    update cg_user_plugin_records
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      user_id = #{record.userId,jdbcType=BIGINT},
      plugin_id = #{record.pluginId,jdbcType=BIGINT},
      control_type = #{record.controlType,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.UserPluginRecordsDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Aug 09 11:17:31 CST 2024.
    -->
    update cg_user_plugin_records
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="pluginId != null">
        plugin_id = #{pluginId,jdbcType=BIGINT},
      </if>
      <if test="controlType != null">
        control_type = #{controlType,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>