package com.alipay.codegencore.utils.adapter;


/**
 * Created by z<PERSON><PERSON><PERSON> on 2017/10/31.
 */
public class HttpAdapterResponse {

    private Integer statusCode;

    private String retContent;

    public HttpAdapterResponse(){
        statusCode = null;
        retContent = null;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public String getRetContent() {
        return retContent;
    }

    public void setRetContent(String retContent) {
        this.retContent = retContent;
    }
}
