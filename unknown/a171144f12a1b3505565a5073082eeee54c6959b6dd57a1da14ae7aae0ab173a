package com.alipay.codegencore.model.openai;

import java.util.function.Function;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 方法调用对象
 */
public class ChatFunction {

    private String name;
    private String description;

    private JSONObject parameters;

    @JsonIgnore
    @JSONField(serialize = false)
    private Function<Object, Object> executor;

    @JsonIgnore
    @JSONField(serialize = false)
    private JSONObject functionParamConfigDisplay;

    public ChatFunction() {
    }

    public ChatFunction(String name) {
        this.name = name;
    }

    public JSONObject getFunctionParamConfigDisplay() {
        return functionParamConfigDisplay;
    }

    public void setFunctionParamConfigDisplay(JSONObject functionParamConfigDisplay) {
        this.functionParamConfigDisplay = functionParamConfigDisplay;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public JSONObject getParameters() {
        return parameters;
    }

    public void setParameters(JSONObject parameters) {
        this.parameters = parameters;
    }

    public Function<Object, Object> getExecutor() {
        return executor;
    }

    public void setExecutor(Function<Object, Object> executor) {
        this.executor = executor;
    }
}