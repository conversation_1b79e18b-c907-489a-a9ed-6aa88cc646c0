package com.alipay.codegencore.service.utils;

import java.util.LinkedList;


/**
 * <AUTHOR>
 * 一个FIFO的队列,指定队列大小,队列满了之后默认弹出队头数据
 */
public class FixedSizeQueue<T> extends LinkedList<T>{

    private final int maxSize;

    /**
     * 创建指定大小的队列
     * @param maxSize 队列大小
     */
    public FixedSizeQueue(int maxSize) {
        super();
        this.maxSize = maxSize;
    }

    /**
     * 向队列里面写入数据
     * @param data 数据
     */
    @Override
    public void addLast(T data) {
        if (size() == maxSize) {
            super.removeFirst();
        }
        super.addLast(data);
    }

    /**
     * 队列里数据按照str返回
     * @return 结果
     */
    public String getQueueStr(){
        StringBuilder sb = new StringBuilder();
        for (T t : this) {
            sb.append(t);
        }
        return sb.toString();
    }

}
