package com.alipay.codegencore.model.model;

import com.alipay.codegencore.model.enums.AlgFlowEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 近端jar配置信息
 *
 * <AUTHOR>
 * 创建时间 2022-06-06
 */
public class ClientConfigModel {
    /**
     * 定时线程间隔时间（单位：分钟)
     */
    private int threadIntervalTime = 60;
    /**
     * 需要下载的jar列表
     */
    private List<FileModel> downloadFileList = new ArrayList<>();
    /**
     * 文件自动收集开关（默认关，打开后请求量会大增)
     */
    private boolean collectFileSwitch = false;

    /**
     * 缓存超时时间
     * {@code 0} 表示无限制，单位毫秒
     */
    private int cacheTimeOut = 0;
    /**
     * 缓存容量
     * {@code 0}表示无大小限制
     */
    private int cacheCapacity = 20000;

    /**
     * 默认分流策略，优先在线
     * 防止枚举后续有变化，用字符串替代。取值依赖于{@link AlgFlowEnum}
     */
    private String defaultFlow = "ONLINE";
    /**
     * 流量变更阈值
     */
    private int flowThreshold = 10;
    /**
     * 流量变更开关
     * 如果为flase，则不会根据网络情况自动变更流量
     */
    private boolean flowChangeAble = false;

    /**
     * 取前x数据。默认1
     */
    private int offLineTopK = 1;
    /**
     * 补全长度。默认100
     */
    private int offLineMexlen = 100;
    /**
     * 离线推理服务重启的阈值
     */
    private int offLineRebootThreshold = 20;


    public int getOffLineRebootThreshold() {
        return offLineRebootThreshold;
    }

    public void setOffLineRebootThreshold(int offLineRebootThreshold) {
        this.offLineRebootThreshold = offLineRebootThreshold;
    }

    public int getOffLineTopK() {
        return offLineTopK;
    }

    public void setOffLineTopK(int offLineTopK) {
        this.offLineTopK = offLineTopK;
    }

    public int getOffLineMexlen() {
        return offLineMexlen;
    }

    public void setOffLineMexlen(int offLineMexlen) {
        this.offLineMexlen = offLineMexlen;
    }

    public boolean isFlowChangeAble() {
        return flowChangeAble;
    }

    public void setFlowChangeAble(boolean flowChangeAble) {
        this.flowChangeAble = flowChangeAble;
    }

    public int getFlowThreshold() {
        return flowThreshold;
    }

    public void setFlowThreshold(int flowThreshold) {
        this.flowThreshold = flowThreshold;
    }

    public String getDefaultFlow() {
        return defaultFlow;
    }

    public void setDefaultFlow(String defaultFlow) {
        this.defaultFlow = defaultFlow;
    }

    public int getCacheTimeOut() {
        return cacheTimeOut;
    }

    public void setCacheTimeOut(int cacheTimeOut) {
        this.cacheTimeOut = cacheTimeOut;
    }

    public int getCacheCapacity() {
        return cacheCapacity;
    }

    public void setCacheCapacity(int cacheCapacity) {
        this.cacheCapacity = cacheCapacity;
    }


    public boolean isCollectFileSwitch() {
        return collectFileSwitch;
    }

    public void setCollectFileSwitch(boolean collectFileSwitch) {
        this.collectFileSwitch = collectFileSwitch;
    }

    public int getThreadIntervalTime() {
        return threadIntervalTime;
    }

    public void setThreadIntervalTime(int threadIntervalTime) {
        this.threadIntervalTime = threadIntervalTime;
    }

    public List<FileModel> getDownloadFileList() {
        return downloadFileList;
    }

    public void setDownloadFileList(List<FileModel> downloadFileList) {
        this.downloadFileList = downloadFileList;
    }
}
