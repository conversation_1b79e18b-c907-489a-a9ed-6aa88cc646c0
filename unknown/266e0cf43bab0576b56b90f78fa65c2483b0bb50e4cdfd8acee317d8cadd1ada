/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.search;

import com.alipay.codegencore.utils.search.SnowFlake;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.PrivateAccess;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import static org.junit.Assert.assertEquals;
import static org.smartunit.runtime.SmartAssertions.verifyException;
import static org.smartunit.shaded.org.mockito.Mockito.*;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class SnowFlake_SSTest extends SnowFlake_SSTest_scaffolding {
// allCoveredLines:[43, 44, 46, 47, 50, 53, 54, 55, 57, 58, 59, 60, 63, 66, 68, 69, 70, 71, 74, 77, 84, 85, 86, 89, 91, 93, 98, 101, 103, 110, 111, 114, 122, 133, 134, 135, 136, 137, 138, 139, 142, 143, 146, 147, 148]

  @Test(timeout = 4000)
  public void test_createNodeId_0()  throws Throwable  {
      //caseID:23127496f3bc429ff2c84097408df674
      //CoveredLines: [43, 43, 43, 44, 44, 44, 46, 47, 50, 53, 54, 55, 133, 134, 135, 136, 137, 138, 139, 142, 143, 146, 147, 148]
      //Input_0_StringBuilder: charSequence0
      //Assert: assertEquals("t002A002A002A", stringBuilder0.toString());
      //Assert: assertEquals(1L, method_result);
      
      SnowFlake snowFlake0 = new SnowFlake(0L, 0L);
      //mock charSequence0
      CharSequence charSequence0 = mock(CharSequence.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn('t').when(charSequence0).charAt(anyInt());
      doReturn(4770, 1, 1).when(charSequence0).length();
      StringBuilder stringBuilder0 = new StringBuilder(charSequence0);
      
      //Call method: createNodeId
      long long0 = (long)PrivateAccess.callMethod((Class<SnowFlake>) SnowFlake.class, snowFlake0, "createNodeId", (Object) stringBuilder0, (Class<?>) StringBuilder.class);
      
      //Test Result Assert
      assertEquals("t002A002A002A", stringBuilder0.toString());
      
      //Test Result Assert
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test_getNextMill_1()  throws Throwable  {
      //caseID:6b47fa36014c3ae1f5e851793440c0c5
      //CoveredLines: [43, 43, 43, 44, 44, 44, 57, 58, 59, 60, 63, 66, 110, 111, 114, 122, 133, 134, 135, 136, 137, 138, 139, 142, 143, 146, 147, 148]
      //Assert: assertEquals(1392409281320L, method_result);
      
      SnowFlake snowFlake0 = new SnowFlake();
      
      //Call method: getNextMill
      long long0 = (long)PrivateAccess.callMethod((Class<SnowFlake>) SnowFlake.class, snowFlake0, "getNextMill");
      
      //Test Result Assert
      assertEquals(1392409281320L, long0);
  }

  @Test(timeout = 4000)
  public void test_nextId_2()  throws Throwable  {
      //caseID:bcdf95c7b0c4225a988d9b2c12193bde
      //CoveredLines: [43, 43, 43, 44, 44, 44, 57, 58, 59, 60, 63, 66, 84, 85, 86, 122, 133, 134, 135, 136, 137, 138, 139, 142, 143, 146, 147, 148]
      
      SnowFlake snowFlake0 = new SnowFlake();
      
      PrivateAccess.setVariable((Class<?>) SnowFlake.class, snowFlake0, "lastStmp", (Object) 1480166465631L);
      
      //Call method: nextId
      // Undeclared exception!
      try { 
        snowFlake0.nextId();
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.utils.search.SnowFlake", e);
         assertEquals("org.smartunit.runtime.mock.java.lang.MockRuntimeException", e.getClass().getName());
         assertEquals("Clock moved backwards.  Refusing to generate id", e.getMessage());
      }
  }

  @Test(timeout = 4000)
  public void test_nextId_3()  throws Throwable  {
      //caseID:60764ac79fcd62216abcf667fa5cdeac
      //CoveredLines: [43, 43, 43, 44, 44, 44, 57, 58, 59, 60, 63, 66, 84, 85, 89, 98, 101, 103, 122, 133, 134, 135, 136, 137, 138, 139, 142, 143, 146, 147, 148]
      //Assert: assertEquals((-368080309184311296L), method_result);
      
      SnowFlake snowFlake0 = new SnowFlake();
      
      //Call method: nextId
      long long0 = snowFlake0.nextId();
      
      //Test Result Assert
      assertEquals((-368080309184311296L), long0);
  }

  @Test(timeout = 4000)
  public void test_nextId_4()  throws Throwable  {
      //caseID:4e98a1107fc377e2d055926c4b1a98f9
      //CoveredLines: [43, 43, 43, 44, 44, 44, 68, 69, 70, 71, 74, 77, 84, 85, 89, 91, 93, 101, 103, 122, 133, 134, 135, 136, 137, 138, 139, 142, 143, 146, 147, 148]
      //Assert: assertEquals((-368080309184237567L), method_result);
      
      SnowFlake snowFlake0 = new SnowFlake("ymp*raQF");
      
      PrivateAccess.setVariable((Class<?>) SnowFlake.class, snowFlake0, "lastStmp", (Object) 1392409281320L);
      
      //Call method: nextId
      long long0 = snowFlake0.nextId();
      
      //Test Result Assert
      assertEquals((-368080309184237567L), long0);
  }

  @Test(timeout = 4000)
  public void test_nextId_5()  throws Throwable  {
      //caseID:6cabdb9caa5174f6f6fdef48bc36efb4
      //CoveredLines: [43, 43, 43, 44, 44, 44, 57, 58, 59, 60, 63, 66, 84, 85, 89, 91, 93, 101, 103, 122, 133, 134, 135, 136, 137, 138, 139, 142, 143, 146, 147, 148]
      //Assert: assertEquals((-368080309184311295L), method_result);
      
      SnowFlake snowFlake0 = new SnowFlake();
      
      PrivateAccess.setVariable((Class<?>) SnowFlake.class, snowFlake0, "lastStmp", (Object) 1392409281320L);
      
      //Call method: nextId
      long long0 = snowFlake0.nextId();
      
      //Test Result Assert
      assertEquals((-368080309184311295L), long0);
  }
}
