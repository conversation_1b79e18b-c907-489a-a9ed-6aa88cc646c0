package com.alipay.codegencore.model.domain;

public class FileDataDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.id
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.file_name
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private String fileName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.version
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private String version;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.group_name
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private String groupName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.file_sha
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private String fileSha;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.os_arch
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private Integer osArch;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.os_type
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private Integer osType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.is_used
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private Integer isUsed;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_file_data.download_url
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    private String downloadUrl;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.id
     *
     * @return the value of cg_file_data.id
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.id
     *
     * @param id the value for cg_file_data.id
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.file_name
     *
     * @return the value of cg_file_data.file_name
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.file_name
     *
     * @param fileName the value for cg_file_data.file_name
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.version
     *
     * @return the value of cg_file_data.version
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.version
     *
     * @param version the value for cg_file_data.version
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.group_name
     *
     * @return the value of cg_file_data.group_name
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public String getGroupName() {
        return groupName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.group_name
     *
     * @param groupName the value for cg_file_data.group_name
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.file_sha
     *
     * @return the value of cg_file_data.file_sha
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public String getFileSha() {
        return fileSha;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.file_sha
     *
     * @param fileSha the value for cg_file_data.file_sha
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setFileSha(String fileSha) {
        this.fileSha = fileSha;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.os_arch
     *
     * @return the value of cg_file_data.os_arch
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public Integer getOsArch() {
        return osArch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.os_arch
     *
     * @param osArch the value for cg_file_data.os_arch
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setOsArch(Integer osArch) {
        this.osArch = osArch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.os_type
     *
     * @return the value of cg_file_data.os_type
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public Integer getOsType() {
        return osType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.os_type
     *
     * @param osType the value for cg_file_data.os_type
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setOsType(Integer osType) {
        this.osType = osType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.is_used
     *
     * @return the value of cg_file_data.is_used
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public Integer getIsUsed() {
        return isUsed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.is_used
     *
     * @param isUsed the value for cg_file_data.is_used
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setIsUsed(Integer isUsed) {
        this.isUsed = isUsed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_file_data.download_url
     *
     * @return the value of cg_file_data.download_url
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public String getDownloadUrl() {
        return downloadUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_file_data.download_url
     *
     * @param downloadUrl the value for cg_file_data.download_url
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }
}