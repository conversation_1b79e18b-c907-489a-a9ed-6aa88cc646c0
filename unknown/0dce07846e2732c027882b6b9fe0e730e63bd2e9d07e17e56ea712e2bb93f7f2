package com.alipay.codegencore.web.common;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.OssService;
import com.alipay.codegencore.service.common.UserAclService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 操作oss的控制器
 */
@RestController
@CodeTalkWebApi
@RequestMapping("/admin/oss")
@Slf4j
public class OssController {

    @Resource
    private OssService ossService;
    @Resource
    private UserAclService userAclService;

    /**
     * 上传文件方法 PUT /putObject
     *
     * @param param 包含文件路径和过期天数等参数的JSONObject对象
     * @return 返回上传成功后的URL或失败原因
     */
    @PostMapping("/putObject")
    public BaseResponse<String> putObject(@RequestBody JSONObject param) {
        // 判断当前用户是否有管理员权限
        if (!userAclService.isAdmin()) {
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }
        // 获取传入的文件路径和过期天数等参数
        String filePath = param.getString("filePath");
        String contentType = param.getString("contentType");
        Integer expireDays = param.getInteger("expireDays");
        // 调用OSS服务的putObject方法上传文件并返回上传成功后的URL
        String url = ossService.putObject(filePath, IOUtils.toInputStream(param.toJSONString(), "UTF-8"), expireDays, contentType);
        return BaseResponse.build(url);
    }

    /**
     * 获取指定文件内容方法 GET /getString
     *
     * @param filePath 文件在OSS上的唯一标识符
     * @return 返回获取到的文件内容字符串或失败原因
     */
    @GetMapping("/getString")
    public BaseResponse<String> getString(@RequestParam String filePath) {
        // 判断当前用户是否有管理员权限
        if (!userAclService.isAdmin()) {
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }
        // 调用OSS服务的getString方法获取指定文件内容并返回
        return BaseResponse.build(ossService.getString(filePath));
    }

    /**
     * 删除指定文件方法 DELETE /deleteFile
     *
     * @param filePath 文件在OSS上的唯一标识符
     * @return 返回删除成功或失败原因
     */
    @DeleteMapping("/deleteFile")
    public BaseResponse deleteFile(@RequestParam String filePath) {
        // 判断当前用户是否有管理员权限
        if (!userAclService.isAdmin()) {
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }
        // 调用OSS服务的deleteFile方法删除指定文件并返回
        ossService.deleteFile(filePath);
        return BaseResponse.buildSuccess();
    }

}

