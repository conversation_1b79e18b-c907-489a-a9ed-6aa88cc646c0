package com.alipay.codegencore.model.model;

import java.math.BigDecimal;
import java.util.List;

/**
 * EmbeddingResponseModel类表示嵌入响应模型。
 */
public class EmbeddingResponseModel {
    /**
     * 文件的uid
     */
    private String fileUid;
    /**
     * 当前段落/当前部分的uid
     */
    private String partUid;
    /**
     * 当前段落的原始str
     */
    private String originalStr;
    /**
     * 当前段落summary之后的str
     */
    private String summaryStr;
    /**
     * 原始段落的Embedding结果
     */
    private List<BigDecimal> originalEmbeddingList;
    /**
     * 当前段落summary之后的str的Embedding结果
     */
    private List<BigDecimal> summaryEmbeddingList;

    /**
     * 原始段落相似度
     */
    private BigDecimal originalSimilarity;
    /**
     * 文档URL
     */
    private String dcoUrl;
    /**
     * 文档标题
     */
    private String docTitle;

    public String getDcoUrl() {
        return dcoUrl;
    }

    public void setDcoUrl(String dcoUrl) {
        this.dcoUrl = dcoUrl;
    }

    public String getDocTitle() {
        return docTitle;
    }

    public void setDocTitle(String docTitle) {
        this.docTitle = docTitle;
    }

    public String getFileUid() {
        return fileUid;
    }

    public void setFileUid(String fileUid) {
        this.fileUid = fileUid;
    }

    public String getPartUid() {
        return partUid;
    }

    public void setPartUid(String partUid) {
        this.partUid = partUid;
    }

    public String getOriginalStr() {
        return originalStr;
    }

    public void setOriginalStr(String originalStr) {
        this.originalStr = originalStr;
    }

    public String getSummaryStr() {
        return summaryStr;
    }

    public void setSummaryStr(String summaryStr) {
        this.summaryStr = summaryStr;
    }

    public List<BigDecimal> getSummaryEmbeddingList() {
        return summaryEmbeddingList;
    }

    public void setSummaryEmbeddingList(List<BigDecimal> summaryEmbeddingList) {
        this.summaryEmbeddingList = summaryEmbeddingList;
    }

    public List<BigDecimal> getOriginalEmbeddingList() {
        return originalEmbeddingList;
    }

    public void setOriginalEmbeddingList(List<BigDecimal> originalEmbeddingList) {
        this.originalEmbeddingList = originalEmbeddingList;
    }

    public BigDecimal getOriginalSimilarity() {
        return originalSimilarity;
    }

    public void setOriginalSimilarity(BigDecimal originalSimilarity) {
        this.originalSimilarity = originalSimilarity;
    }
}
