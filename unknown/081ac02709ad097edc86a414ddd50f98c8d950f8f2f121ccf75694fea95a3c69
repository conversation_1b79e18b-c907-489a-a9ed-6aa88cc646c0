package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.PluginDOExample;
import com.alipay.codegencore.model.domain.PluginDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface PluginDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    long countByExample(PluginDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int deleteByExample(PluginDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    @Delete({
        "delete from cg_plugin",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    @Insert({
        "insert into cg_plugin (gmt_create, gmt_modified, ",
        "name, description, ",
        "workflow_config, enable, ",
        "deleted, user_id, ",
        "usage_count, owner_user_id, ",
        "icon_url, icon_background_color, ",
        "use_instructions, type, ",
        "aci_info, visable_user)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, ",
        "#{workflowConfig,jdbcType=VARCHAR}, #{enable,jdbcType=INTEGER}, ",
        "#{deleted,jdbcType=INTEGER}, #{userId,jdbcType=BIGINT}, ",
        "#{usageCount,jdbcType=BIGINT}, #{ownerUserId,jdbcType=BIGINT}, ",
        "#{iconUrl,jdbcType=VARCHAR}, #{iconBackgroundColor,jdbcType=VARCHAR}, ",
        "#{useInstructions,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, ",
        "#{aciInfo,jdbcType=VARCHAR}, #{visableUser,jdbcType=INTEGER})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(PluginDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int insertSelective(PluginDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    List<PluginDO> selectByExample(PluginDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, name, description, workflow_config, enable, deleted, ",
        "user_id, usage_count, owner_user_id, icon_url, icon_background_color, use_instructions, ",
        "type, aci_info, visable_user",
        "from cg_plugin",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("com.alipay.codegencore.dal.mapper.PluginDOMapper.BaseResultMap")
    PluginDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int updateByExampleSelective(@Param("record") PluginDO record, @Param("example") PluginDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int updateByExample(@Param("record") PluginDO record, @Param("example") PluginDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    int updateByPrimaryKeySelective(PluginDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    @Update({
        "update cg_plugin",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "name = #{name,jdbcType=VARCHAR},",
          "description = #{description,jdbcType=VARCHAR},",
          "workflow_config = #{workflowConfig,jdbcType=VARCHAR},",
          "enable = #{enable,jdbcType=INTEGER},",
          "deleted = #{deleted,jdbcType=INTEGER},",
          "user_id = #{userId,jdbcType=BIGINT},",
          "usage_count = #{usageCount,jdbcType=BIGINT},",
          "owner_user_id = #{ownerUserId,jdbcType=BIGINT},",
          "icon_url = #{iconUrl,jdbcType=VARCHAR},",
          "icon_background_color = #{iconBackgroundColor,jdbcType=VARCHAR},",
          "use_instructions = #{useInstructions,jdbcType=VARCHAR},",
          "type = #{type,jdbcType=VARCHAR},",
          "aci_info = #{aciInfo,jdbcType=VARCHAR},",
          "visable_user = #{visableUser,jdbcType=INTEGER}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(PluginDO record);
}