package com.alipay.codegencore.utils;

import com.alipay.codegencore.utils.codefuse.SignUtil;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.utils
 * @CreateTime : 2023-11-06
 */
public class SignUtilTest extends AbstractTestBase{
    /**
     * 测试
     */
    @Test
    public void test_sign() {
        String accessKey    = "8888888888888888";
        String accessSecret = "8888888888888888";
        long timestamp = System.currentTimeMillis();
        String sign = null;
        try {
             sign = SignUtil.dimaProjectSign(accessKey, accessSecret, timestamp);
            System.out.println(sign);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(sign);
        Assertions.assertNotNull(sign);
    }
}
