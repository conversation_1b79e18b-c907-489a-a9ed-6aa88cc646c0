package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.ChatSessionDOExample;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ChatSessionDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    long countByExample(ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    int deleteByExample(ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    @Delete({
        "delete from cg_chat_session",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    @Insert({
        "insert into cg_chat_session (gmt_create, gmt_modified, ",
        "uid, title, deleted, ",
        "user_id, model, prompt, ",
        "model_config, scene_id, ",
        "scene_test, oss_address_list, ",
        "ext_info, source_platform, ",
        "document_uid_list)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{uid,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, ",
        "#{userId,jdbcType=BIGINT}, #{model,jdbcType=VARCHAR}, #{prompt,jdbcType=VARCHAR}, ",
        "#{modelConfig,jdbcType=VARCHAR}, #{sceneId,jdbcType=BIGINT}, ",
        "#{sceneTest,jdbcType=TINYINT}, #{ossAddressList,jdbcType=VARCHAR}, ",
        "#{extInfo,jdbcType=VARCHAR}, #{sourcePlatform,jdbcType=VARCHAR}, ",
        "#{documentUidList,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(ChatSessionDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    int insertSelective(ChatSessionDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    List<ChatSessionDO> selectByExample(ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, uid, title, deleted, user_id, model, prompt, model_config, ",
        "scene_id, scene_test, oss_address_list, ext_info, source_platform, document_uid_list",
        "from cg_chat_session",
        "where id = #{id,jdbcType=BIGINT}"
    })
    ChatSessionDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    int updateByExampleSelective(@Param("record") ChatSessionDO record, @Param("example") ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    int updateByExample(@Param("record") ChatSessionDO record, @Param("example") ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    int updateByPrimaryKeySelective(ChatSessionDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    @Update({
        "update cg_chat_session",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "uid = #{uid,jdbcType=VARCHAR},",
          "title = #{title,jdbcType=VARCHAR},",
          "deleted = #{deleted,jdbcType=TINYINT},",
          "user_id = #{userId,jdbcType=BIGINT},",
          "model = #{model,jdbcType=VARCHAR},",
          "prompt = #{prompt,jdbcType=VARCHAR},",
          "model_config = #{modelConfig,jdbcType=VARCHAR},",
          "scene_id = #{sceneId,jdbcType=BIGINT},",
          "scene_test = #{sceneTest,jdbcType=TINYINT},",
          "oss_address_list = #{ossAddressList,jdbcType=VARCHAR},",
          "ext_info = #{extInfo,jdbcType=VARCHAR},",
          "source_platform = #{sourcePlatform,jdbcType=VARCHAR},",
          "document_uid_list = #{documentUidList,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(ChatSessionDO record);
}