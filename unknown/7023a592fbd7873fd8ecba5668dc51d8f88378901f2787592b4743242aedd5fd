/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.impl.model;

import com.alibaba.fastjson.JSON;
import com.alipay.antq.common.utils.StringUtils;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RateLimitTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.request.CodegptBatchRequestBean;
import com.alipay.codegencore.model.request.CodegptRequestBean;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.common.limiter.qpx.QpxLimiterFactory;
import com.alipay.codegencore.service.gptcache.bean.GptCacheResponse;
import com.alipay.codegencore.utils.dingding.DingDingUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.alipay.codegencore.model.enums.ResponseEnum.MAYA_RESPONSE_IS_EMPTY;

/**
 * <AUTHOR>
 * @version CodeGPTLanguageModelServiceImpl.java, v 0.1 2023年03月20日 15:58 xiaobin
 */
@Service("codeGptLanguageModelService")
public class CodeGptLanguageModelServiceImpl implements LanguageModelService {

    private static final Logger LOGGER = LoggerFactory.getLogger( CodeGptLanguageModelServiceImpl.class );

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    @Resource
    private CheckService checkService;

    @Resource
    private MayaService mayaService;

    @Resource
    private CalculateTokenService calculateTokenService;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource
    private AlgoModelUtilService algoModelUtilService;

    private volatile Date backendThreadRefreshTime;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;

    /**
     * 模型map,key是模型名称,用于加锁使用。
     */
    private final Map<String, AlgoBackendDO> algoBackendDOMap = new ConcurrentHashMap<>();
    /**
     * 待发送给maya的请求，key是模型名称
     */
    private final Map<String, List<GptAlgModelServiceRequest>> batchRequestWaitMap = new ConcurrentHashMap<>();
    /**
     * 批量发送的时间窗口起始点，key是模型名称
     */
    private final Map<String, Long> batchWindowStartTimeMap = new ConcurrentHashMap<>();

    private final ExecutorService batchWindowsThreadPool = new ThreadPoolExecutor(1,1,0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1), new ThreadPoolExecutor.AbortPolicy());

    public boolean isServiceOk() {

        int allowInterval = 1000 * 30;
        if(backendThreadRefreshTime == null){
            LOGGER.error("CodeGPTModelHandler backendThreadRefreshTime is empty" );
            return false;
        }

        if((new Date().getTime() - backendThreadRefreshTime.getTime()) > allowInterval){
            LOGGER.error("CodeGPTModelHandler backendThreadRefreshTime not refresh");
            return false;
        }

        return true;
    }

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        batchWindowsThreadPool.execute(()->{
            String domain = null;
            try {
                domain = InetAddress.getLocalHost().getHostName();
            } catch (Throwable e) {
                LOGGER.error("CodeGPTModelHandler发送任务线程异常,获取当前机器域名失败", e);
            }
            while (true) {
                backendThreadRefreshTime = new Date();

                try {
                    if (mapValIsEmpty(batchRequestWaitMap)) {
                        try {
                            Thread.sleep(50);
                        } catch (InterruptedException e) {
                            LOGGER.info("thread sleep error", e);
                        }
                        continue;
                    }
                    for (Map.Entry<String, List<GptAlgModelServiceRequest>> entry : batchRequestWaitMap.entrySet()) {
                        List<GptAlgModelServiceRequest> algModelServiceRequestList = entry.getValue();
                        if (CollectionUtils.isEmpty(algModelServiceRequestList)) {
                            continue;
                        }

                        // 如果当前模型积攒的请求,没有超过最长时间,并且也没有超长,就先不发。
                        AlgoBackendDO defaultAlgoConfig = algModelServiceRequestList.get(0).getAlgoBackendDO();
                        if (!waitTimeOut(defaultAlgoConfig) && !batchSizeTooLong(algModelServiceRequestList)) {
                            continue;
                        }

                        try {
                            List<List<GptAlgModelServiceRequest>> splitRequestLists;
                            synchronized (algoBackendDOMap.get(entry.getKey())) {
                                List<String> requestIdList = algModelServiceRequestList.stream().map(GptAlgModelServiceRequest::getRequestId).collect(Collectors.toList());
                                long thisModelBatchWindowStartTime = batchWindowStartTimeMap.get(defaultAlgoConfig.getModel()) == null ? 0 : batchWindowStartTimeMap.get(defaultAlgoConfig.getModel());
                                CHAT_LOGGER.info("reach batch wait list limit or time window limit, batch size:{}, time window:{}, requestIdList: {}",
                                        algModelServiceRequestList.size(), System.currentTimeMillis() - thisModelBatchWindowStartTime, JSON.toJSONString(requestIdList));
                                splitRequestLists = splitListByBatchSize(algModelServiceRequestList);
                                algModelServiceRequestList.clear();
                            }
                            for (List<GptAlgModelServiceRequest> requestList : splitRequestLists) {
                                List<String> batchRequestIdList = requestList.stream().map(GptAlgModelServiceRequest::getRequestId).collect(Collectors.toList());
                                //异步请求，不阻塞主线程处理速度
                                appThreadPool.execute(() -> {
                                    try {
                                        getAiResult(defaultAlgoConfig, requestList);
                                    } catch (BizException e) {
                                        algoModelUtilService.pushErrorMsgToTBase(requestList, e.getMessage(), e.getErrorType());
                                        CHAT_LOGGER.error("request failed, request id list: {}, error type: {}, error message: {}", JSON.toJSONString(batchRequestIdList), e.getErrorType(), e.getMessage(), e);
                                    } catch (Throwable e) {
                                        algoModelUtilService.pushErrorMsgToTBase(requestList, e.getMessage(), null);
                                        CHAT_LOGGER.error("request failed, request id list: {}, error message: {}", JSON.toJSONString(batchRequestIdList), e.getMessage(), e);
                                    }
                                });
                            }
                        } catch (Throwable e) {
                            LOGGER.error(String.format("CodeGPTModelHandler发送任务线程异常,丢弃任务数量:%s,entry:%s", algModelServiceRequestList.size(), JSON.toJSONString(entry)), e);
                            DingDingUtil.sendMessage(String.format("CodeGPTModelHandler发送任务线程异常,丢弃该批次任务,请尽快排查修复问题,domain:%s", domain));
                            algModelServiceRequestList.clear();
                        }
                    }
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        LOGGER.info("thread sleep error", e);
                    }
                } catch (Throwable e) {
                    LOGGER.error("CodeGPTModelHandler发送任务线程异常", e);
                    DingDingUtil.sendMessage("CodeGPTModelHandler发送任务线程异常,请尽快重启服务并排查问题,domain:" + domain);
                    break;
                }
            }
        });
    }

    private List<List<GptAlgModelServiceRequest>> splitListByBatchSize(List<GptAlgModelServiceRequest> algModelServiceRequestList) {
        if (CollectionUtils.isEmpty(algModelServiceRequestList)) {
            return new ArrayList<>();
        }
        AlgoBackendDO algoBackendDO = algModelServiceRequestList.get(0).getAlgoBackendDO();
        List<List<GptAlgModelServiceRequest>> result = new ArrayList<>();
        List<GptAlgModelServiceRequest> list = new ArrayList<>();
        for (GptAlgModelServiceRequest gptAlgModelServiceRequest : algModelServiceRequestList) {
            if (list.size() >= AlgoBackendUtil.exactBatchSizeConfig(algoBackendDO)) {
                result.add(list);
                list = new ArrayList<>();
            }
            list.add(gptAlgModelServiceRequest);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            result.add(list);
        }
        return result;
    }

    private boolean batchSizeTooLong(List<GptAlgModelServiceRequest> algModelServiceRequestList) {
        AlgoBackendDO algoBackendDO = algModelServiceRequestList.get(0).getAlgoBackendDO();
        return algModelServiceRequestList.size() >= AlgoBackendUtil.exactBatchSizeConfig(algoBackendDO);
    }

    private boolean waitTimeOut(AlgoBackendDO defaultAlgoConfig) {
        long thisModelBatchWindowStartTime = batchWindowStartTimeMap.get(defaultAlgoConfig.getModel()) == null ? 0 : batchWindowStartTimeMap.get(defaultAlgoConfig.getModel());
        return System.currentTimeMillis() - thisModelBatchWindowStartTime >= AlgoBackendUtil.exactBatchTimeWindowConfig(defaultAlgoConfig);
    }

    private boolean mapValIsEmpty(Map<String, List<GptAlgModelServiceRequest>> batchRequestWaitMap) {
        if (MapUtils.isEmpty(batchRequestWaitMap)) {
            return true;
        }
        for (List<GptAlgModelServiceRequest> val : batchRequestWaitMap.values()) {
            if (CollectionUtils.isNotEmpty(val)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void streamChatForServlet(GptAlgModelServiceRequest params) {
        String user = params.getUserName();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        String requestId = params.getRequestId();
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = params.getChatStreamPartResponseHandler();
        Consumer<StreamResponseModel> resultHandler = params.getResultHandler();

        CHAT_LOGGER.info("codegpt completion requestId:{},user:{} request:{}", requestId, user, JSON.toJSONString(chatCompletionRequest));

        if (!chatCompletionRequest.getStream()) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "stream must be true");
        }
        // 对用户输入的问题进行审核,输入内容一批全部审
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        List<ChatMessage> copyMessages = JSON.parseArray(JSON.toJSONString(messages),ChatMessage.class);
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        if (chatRequestExtData != null) {
            chatRequestExtData.setAlgoBackendDO(algoBackendDO);
        }

        CheckResultModel requestCheckResultModel;
        if(messages != null){
            requestCheckResultModel = checkService.getQuestionCheckResult(requestId, copyMessages, chatRequestExtData,false);
        }else {
            requestCheckResultModel = checkService.getQuestionCheckResult(requestId, chatCompletionRequest.getPrompt(), chatRequestExtData, false);
        }

        if (!requestCheckResultModel.isAllCheckRet()) {
            algoModelUtilService.processWhenQuestionFailCheck(requestCheckResultModel, chatStreamPartResponseConsumer, resultHandler);
            return;
        }

        // 记录请求数据到list/gpt catch
        //请求算法服务，算法服务向tbase队列中写入数据。如果命中gptcache，则自己往tbase队列中写从gptcache获取的缓存
        writeRequestToList(algoBackendDO,AlgoModelUtilService.copyParamWithoutServletResponse(params));
        algoModelUtilService.getChatDataFromTBase(params, copyMessages, requestCheckResultModel, algoBackendDO.getEnableGptCache(),new ChatStreamBuffer(),false);
    }

    @Override
    public ChatMessage chat(GptAlgModelServiceRequest params) {
        String user = params.getUserName();
        ChatCompletionRequest chatRequestBean = params.getChatCompletionRequest();
        String requestId = params.getRequestId();
        // 对用户输入的问题进行审核
        List<ChatMessage> messages = chatRequestBean.getMessages();
        List<ChatMessage> copyMessages = JSON.parseArray(JSON.toJSONString(messages),ChatMessage.class);
        // 用户的输入直接一批全审核
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        ChatRequestExtData chatRequestExtData = chatRequestBean.getChatRequestExtData();
        if (chatRequestExtData != null) {
            chatRequestExtData.setAlgoBackendDO(algoBackendDO);
        }

        CheckResultModel requestCheckResultModel = null;
        if(messages != null){
            requestCheckResultModel = checkService.getQuestionCheckResult(requestId, messages, chatRequestExtData,false);
        }else {
            requestCheckResultModel = checkService.getQuestionCheckResult(requestId, chatRequestBean.getPrompt(), chatRequestExtData,false);

        }
        if (!requestCheckResultModel.isAllCheckRet()) {
            throw new BizException(ResponseEnum.CHECK_FAILED);
        }
        CHAT_LOGGER.info("codegpt completion requestId:{},user:{} request:{}", requestId, user, JSON.toJSONString(chatRequestBean));

        // 记录请求数据到list/gpt catch
        writeRequestToList(algoBackendDO,AlgoModelUtilService.copyParamWithoutServletResponse(params));

        ChatStreamBuffer streamBuffer = new ChatStreamBuffer();
        algoModelUtilService.getChatDataFromTBase(params, copyMessages, requestCheckResultModel, algoBackendDO.getEnableGptCache(), streamBuffer,false);
        String result = streamBuffer.getContent().toString();
        CHAT_LOGGER.info("codegpt completion requestId:{},user:{} result:{}", requestId, user, result);
        return new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), result);
    }

    /**
     * 获取AI推理结果
     *
     * @param gptAlgModelServiceRequestList
     * @return
     */
    private String getAiResult(AlgoBackendDO defaultAlgoConfig, List<GptAlgModelServiceRequest> gptAlgModelServiceRequestList) {
        if(gptAlgModelServiceRequestList.isEmpty()){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "request list is empty");
        }

        if (defaultAlgoConfig == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, gptAlgModelServiceRequestList.get(0).getChatCompletionRequest().getModel() + " model not found");
        }

        List<CodegptRequestBean> requestList = new ArrayList<>();
        List<CodegptRequestBean> requestPromptList = new ArrayList<>();
        for (GptAlgModelServiceRequest requestContext : gptAlgModelServiceRequestList) {
            ChatCompletionRequest chatCompletionRequest = requestContext.getChatCompletionRequest();
            if(StringUtils.isNotBlank(chatCompletionRequest.getPrompt())){
                requestPromptList.add(algoModelUtilService.beforeModelRequest(chatCompletionRequest, requestContext.getAlgoBackendDO(), requestContext.getUniqueAnswerId()));
            }else {
                CodegptRequestBean request = algoModelUtilService.beforeModelRequest(chatCompletionRequest, requestContext.getAlgoBackendDO(), requestContext.getUniqueAnswerId());
                requestList.add(request);
            }

        }
        CodegptBatchRequestBean codegptBatchRequestBean=new CodegptBatchRequestBean();
        codegptBatchRequestBean.setApiVersion("v2");
        codegptBatchRequestBean.setBeamWidth(AlgoBackendUtil.exactBeamWidthConfig(defaultAlgoConfig));
        codegptBatchRequestBean.setOutSeqLength(AlgoBackendUtil.exactOutSeqLengthConfig(defaultAlgoConfig));
        codegptBatchRequestBean.setStream(true);
        codegptBatchRequestBean.setPrompts(requestList);
        String result = null;
        // 如果待发送的请求里面包含Prompt和Messages两种类型，将请求拆分成2次发送
        if(CollectionUtils.isNotEmpty(requestPromptList) && CollectionUtils.isNotEmpty(requestList)){
            CHAT_LOGGER.info("Prompt and Messages both exist in the request, split into two requests");
        }
        for (int i = 0; i < 2; i++) {
            if(CollectionUtils.isNotEmpty(codegptBatchRequestBean.getPrompts())){
                CHAT_LOGGER.info("codegpt completion final batch request:{}", JSON.toJSONString(codegptBatchRequestBean));
                String response = mayaService.getInferenceResult(JSON.toJSONString(codegptBatchRequestBean),
                        AlgoBackendUtil.exactSceneNameConfig(defaultAlgoConfig),
                        AlgoBackendUtil.exactChainNameConfig(defaultAlgoConfig),
                        //TODO 这里会有bug， 当同一个模型请求有的指向预发，有的指向线上时
                        AlgoBackendUtil.exactRequestTimeOutConfig(defaultAlgoConfig),"data","res",false, defaultAlgoConfig, gptAlgModelServiceRequestList.get(0).getModelEnv());
                CHAT_LOGGER.info("codegpt completion batch request result:{}", response);
                if (response == null) {
                    CHAT_LOGGER.info("codegpt completion batch request get no result");
                    throw new BizException(MAYA_RESPONSE_IS_EMPTY, "neox completion get no result");
                }
                result = algoModelUtilService.afterModelResponse(response, defaultAlgoConfig);
            }
            if(CollectionUtils.isNotEmpty(requestPromptList)){
                CHAT_LOGGER.info("start to send Prompt request");
                codegptBatchRequestBean.setPrompts(requestPromptList);
            }else {
                break;
            }
        }
        return result;
    }

    private void writeRequestToList(AlgoBackendDO algoBackendDO, GptAlgModelServiceRequest params) {
        if (algoBackendDO.getEnableGptCache()){
            GptCacheResponse gptCacheResponse = algoModelUtilService.getGPTCache(algoBackendDO, params);
            if (gptCacheResponse != null && StringUtils.isNotBlank(gptCacheResponse.getAnswer())) {
                // 命中缓存，去子线程中往TBase中写
                appThreadPool.execute(() -> algoModelUtilService.pushToTBase(gptCacheResponse, params));
                return;
            }
        }
        algoBackendDOMap.putIfAbsent(algoBackendDO.getModel(), algoBackendDO);
        // 缓存没有命中，去加入maya请求队列中
        synchronized (algoBackendDOMap.get(algoBackendDO.getModel())) {
            //第一个请求加入队列的时候，时间窗口才会启动
            if (CollectionUtils.isEmpty(batchRequestWaitMap.get(algoBackendDO.getModel()))) {
                batchWindowStartTimeMap.put(algoBackendDO.getModel(), System.currentTimeMillis());
            }
            //添加进批处理队列，直接等待结果即可
            if (batchRequestWaitMap.containsKey(algoBackendDO.getModel())) {
                batchRequestWaitMap.get(algoBackendDO.getModel()).add(params);
            } else {
                batchRequestWaitMap.put(algoBackendDO.getModel(), Lists.newArrayList(params));
            }
        }
    }

}