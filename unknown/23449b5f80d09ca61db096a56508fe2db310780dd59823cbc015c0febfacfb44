package com.alipay.codegencore.model.openai;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 17:28
 */
public class GenCodeFileRequest {

    @NotBlank(message = "sessionId不能为空")
    private String sessionId;

    private String appName;

    @NotNull(message = "仓库信息不能为Null")
    @Valid
    private RepoInfo repoInfo;

    @NotNull(message = "需求信息不能为Null")
    @Valid
    private GenRequirementInfo requirementInfo;

    @NotNull(message = "Plan不能为Null")
    @Valid
    private PlanFile plan;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public RepoInfo getRepoInfo() {
        return repoInfo;
    }

    public void setRepoInfo(RepoInfo repoInfo) {
        this.repoInfo = repoInfo;
    }

    public GenRequirementInfo getRequirementInfo() {
        return requirementInfo;
    }

    public void setRequirementInfo(GenRequirementInfo requirementInfo) {
        this.requirementInfo = requirementInfo;
    }

    public PlanFile getPlan() {
        return plan;
    }

    public void setPlan(PlanFile plan) {
        this.plan = plan;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GenCodeFileRequest.class.getSimpleName() + "[", "]")
                .add("sessionId='" + sessionId + "'")
                .add("appName='" + appName + "'")
                .add("repoInfo=" + repoInfo)
                .add("requirementInfo=" + requirementInfo)
                .add("plans=" + plan)
                .toString();
    }
}
