package com.alipay.codegencore.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SceneDOExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public SceneDOExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andSystemPromptIsNull() {
            addCriterion("system_prompt is null");
            return (Criteria) this;
        }

        public Criteria andSystemPromptIsNotNull() {
            addCriterion("system_prompt is not null");
            return (Criteria) this;
        }

        public Criteria andSystemPromptEqualTo(String value) {
            addCriterion("system_prompt =", value, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptNotEqualTo(String value) {
            addCriterion("system_prompt <>", value, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptGreaterThan(String value) {
            addCriterion("system_prompt >", value, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptGreaterThanOrEqualTo(String value) {
            addCriterion("system_prompt >=", value, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptLessThan(String value) {
            addCriterion("system_prompt <", value, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptLessThanOrEqualTo(String value) {
            addCriterion("system_prompt <=", value, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptLike(String value) {
            addCriterion("system_prompt like", value, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptNotLike(String value) {
            addCriterion("system_prompt not like", value, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptIn(List<String> values) {
            addCriterion("system_prompt in", values, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptNotIn(List<String> values) {
            addCriterion("system_prompt not in", values, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptBetween(String value1, String value2) {
            addCriterion("system_prompt between", value1, value2, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andSystemPromptNotBetween(String value1, String value2) {
            addCriterion("system_prompt not between", value1, value2, "systemPrompt");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Integer value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Integer value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Integer value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Integer value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Integer> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Integer> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Integer value1, Integer value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonIsNull() {
            addCriterion("query_template_list_json is null");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonIsNotNull() {
            addCriterion("query_template_list_json is not null");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonEqualTo(String value) {
            addCriterion("query_template_list_json =", value, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonNotEqualTo(String value) {
            addCriterion("query_template_list_json <>", value, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonGreaterThan(String value) {
            addCriterion("query_template_list_json >", value, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonGreaterThanOrEqualTo(String value) {
            addCriterion("query_template_list_json >=", value, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonLessThan(String value) {
            addCriterion("query_template_list_json <", value, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonLessThanOrEqualTo(String value) {
            addCriterion("query_template_list_json <=", value, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonLike(String value) {
            addCriterion("query_template_list_json like", value, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonNotLike(String value) {
            addCriterion("query_template_list_json not like", value, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonIn(List<String> values) {
            addCriterion("query_template_list_json in", values, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonNotIn(List<String> values) {
            addCriterion("query_template_list_json not in", values, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonBetween(String value1, String value2) {
            addCriterion("query_template_list_json between", value1, value2, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andQueryTemplateListJsonNotBetween(String value1, String value2) {
            addCriterion("query_template_list_json not between", value1, value2, "queryTemplateListJson");
            return (Criteria) this;
        }

        public Criteria andModeIsNull() {
            addCriterion("mode is null");
            return (Criteria) this;
        }

        public Criteria andModeIsNotNull() {
            addCriterion("mode is not null");
            return (Criteria) this;
        }

        public Criteria andModeEqualTo(Integer value) {
            addCriterion("mode =", value, "mode");
            return (Criteria) this;
        }

        public Criteria andModeNotEqualTo(Integer value) {
            addCriterion("mode <>", value, "mode");
            return (Criteria) this;
        }

        public Criteria andModeGreaterThan(Integer value) {
            addCriterion("mode >", value, "mode");
            return (Criteria) this;
        }

        public Criteria andModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("mode >=", value, "mode");
            return (Criteria) this;
        }

        public Criteria andModeLessThan(Integer value) {
            addCriterion("mode <", value, "mode");
            return (Criteria) this;
        }

        public Criteria andModeLessThanOrEqualTo(Integer value) {
            addCriterion("mode <=", value, "mode");
            return (Criteria) this;
        }

        public Criteria andModeIn(List<Integer> values) {
            addCriterion("mode in", values, "mode");
            return (Criteria) this;
        }

        public Criteria andModeNotIn(List<Integer> values) {
            addCriterion("mode not in", values, "mode");
            return (Criteria) this;
        }

        public Criteria andModeBetween(Integer value1, Integer value2) {
            addCriterion("mode between", value1, value2, "mode");
            return (Criteria) this;
        }

        public Criteria andModeNotBetween(Integer value1, Integer value2) {
            addCriterion("mode not between", value1, value2, "mode");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andPluginListIsNull() {
            addCriterion("plugin_list is null");
            return (Criteria) this;
        }

        public Criteria andPluginListIsNotNull() {
            addCriterion("plugin_list is not null");
            return (Criteria) this;
        }

        public Criteria andPluginListEqualTo(String value) {
            addCriterion("plugin_list =", value, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListNotEqualTo(String value) {
            addCriterion("plugin_list <>", value, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListGreaterThan(String value) {
            addCriterion("plugin_list >", value, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListGreaterThanOrEqualTo(String value) {
            addCriterion("plugin_list >=", value, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListLessThan(String value) {
            addCriterion("plugin_list <", value, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListLessThanOrEqualTo(String value) {
            addCriterion("plugin_list <=", value, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListLike(String value) {
            addCriterion("plugin_list like", value, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListNotLike(String value) {
            addCriterion("plugin_list not like", value, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListIn(List<String> values) {
            addCriterion("plugin_list in", values, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListNotIn(List<String> values) {
            addCriterion("plugin_list not in", values, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListBetween(String value1, String value2) {
            addCriterion("plugin_list between", value1, value2, "pluginList");
            return (Criteria) this;
        }

        public Criteria andPluginListNotBetween(String value1, String value2) {
            addCriterion("plugin_list not between", value1, value2, "pluginList");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andVisableUserIsNull() {
            addCriterion("visable_user is null");
            return (Criteria) this;
        }

        public Criteria andVisableUserIsNotNull() {
            addCriterion("visable_user is not null");
            return (Criteria) this;
        }

        public Criteria andVisableUserEqualTo(Integer value) {
            addCriterion("visable_user =", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotEqualTo(Integer value) {
            addCriterion("visable_user <>", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserGreaterThan(Integer value) {
            addCriterion("visable_user >", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserGreaterThanOrEqualTo(Integer value) {
            addCriterion("visable_user >=", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserLessThan(Integer value) {
            addCriterion("visable_user <", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserLessThanOrEqualTo(Integer value) {
            addCriterion("visable_user <=", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserIn(List<Integer> values) {
            addCriterion("visable_user in", values, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotIn(List<Integer> values) {
            addCriterion("visable_user not in", values, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserBetween(Integer value1, Integer value2) {
            addCriterion("visable_user between", value1, value2, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotBetween(Integer value1, Integer value2) {
            addCriterion("visable_user not between", value1, value2, "visableUser");
            return (Criteria) this;
        }

        public Criteria andUsageCountIsNull() {
            addCriterion("usage_count is null");
            return (Criteria) this;
        }

        public Criteria andUsageCountIsNotNull() {
            addCriterion("usage_count is not null");
            return (Criteria) this;
        }

        public Criteria andUsageCountEqualTo(Long value) {
            addCriterion("usage_count =", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountNotEqualTo(Long value) {
            addCriterion("usage_count <>", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountGreaterThan(Long value) {
            addCriterion("usage_count >", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountGreaterThanOrEqualTo(Long value) {
            addCriterion("usage_count >=", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountLessThan(Long value) {
            addCriterion("usage_count <", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountLessThanOrEqualTo(Long value) {
            addCriterion("usage_count <=", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountIn(List<Long> values) {
            addCriterion("usage_count in", values, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountNotIn(List<Long> values) {
            addCriterion("usage_count not in", values, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountBetween(Long value1, Long value2) {
            addCriterion("usage_count between", value1, value2, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountNotBetween(Long value1, Long value2) {
            addCriterion("usage_count not between", value1, value2, "usageCount");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNull() {
            addCriterion("audit_status is null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNotNull() {
            addCriterion("audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusEqualTo(Integer value) {
            addCriterion("audit_status =", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotEqualTo(Integer value) {
            addCriterion("audit_status <>", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThan(Integer value) {
            addCriterion("audit_status >", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_status >=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThan(Integer value) {
            addCriterion("audit_status <", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("audit_status <=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIn(List<Integer> values) {
            addCriterion("audit_status in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotIn(List<Integer> values) {
            addCriterion("audit_status not in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("audit_status between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_status not between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andEnableIsNull() {
            addCriterion("enable is null");
            return (Criteria) this;
        }

        public Criteria andEnableIsNotNull() {
            addCriterion("enable is not null");
            return (Criteria) this;
        }

        public Criteria andEnableEqualTo(Integer value) {
            addCriterion("enable =", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotEqualTo(Integer value) {
            addCriterion("enable <>", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThan(Integer value) {
            addCriterion("enable >", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThanOrEqualTo(Integer value) {
            addCriterion("enable >=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThan(Integer value) {
            addCriterion("enable <", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThanOrEqualTo(Integer value) {
            addCriterion("enable <=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableIn(List<Integer> values) {
            addCriterion("enable in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotIn(List<Integer> values) {
            addCriterion("enable not in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableBetween(Integer value1, Integer value2) {
            addCriterion("enable between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotBetween(Integer value1, Integer value2) {
            addCriterion("enable not between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andSceneTagIsNull() {
            addCriterion("scene_tag is null");
            return (Criteria) this;
        }

        public Criteria andSceneTagIsNotNull() {
            addCriterion("scene_tag is not null");
            return (Criteria) this;
        }

        public Criteria andSceneTagEqualTo(String value) {
            addCriterion("scene_tag =", value, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagNotEqualTo(String value) {
            addCriterion("scene_tag <>", value, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagGreaterThan(String value) {
            addCriterion("scene_tag >", value, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagGreaterThanOrEqualTo(String value) {
            addCriterion("scene_tag >=", value, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagLessThan(String value) {
            addCriterion("scene_tag <", value, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagLessThanOrEqualTo(String value) {
            addCriterion("scene_tag <=", value, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagLike(String value) {
            addCriterion("scene_tag like", value, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagNotLike(String value) {
            addCriterion("scene_tag not like", value, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagIn(List<String> values) {
            addCriterion("scene_tag in", values, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagNotIn(List<String> values) {
            addCriterion("scene_tag not in", values, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagBetween(String value1, String value2) {
            addCriterion("scene_tag between", value1, value2, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andSceneTagNotBetween(String value1, String value2) {
            addCriterion("scene_tag not between", value1, value2, "sceneTag");
            return (Criteria) this;
        }

        public Criteria andVisableEnvIsNull() {
            addCriterion("visable_env is null");
            return (Criteria) this;
        }

        public Criteria andVisableEnvIsNotNull() {
            addCriterion("visable_env is not null");
            return (Criteria) this;
        }

        public Criteria andVisableEnvEqualTo(Integer value) {
            addCriterion("visable_env =", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvNotEqualTo(Integer value) {
            addCriterion("visable_env <>", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvGreaterThan(Integer value) {
            addCriterion("visable_env >", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvGreaterThanOrEqualTo(Integer value) {
            addCriterion("visable_env >=", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvLessThan(Integer value) {
            addCriterion("visable_env <", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvLessThanOrEqualTo(Integer value) {
            addCriterion("visable_env <=", value, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvIn(List<Integer> values) {
            addCriterion("visable_env in", values, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvNotIn(List<Integer> values) {
            addCriterion("visable_env not in", values, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvBetween(Integer value1, Integer value2) {
            addCriterion("visable_env between", value1, value2, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andVisableEnvNotBetween(Integer value1, Integer value2) {
            addCriterion("visable_env not between", value1, value2, "visableEnv");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportIsNull() {
            addCriterion("multi_round_support is null");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportIsNotNull() {
            addCriterion("multi_round_support is not null");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportEqualTo(Byte value) {
            addCriterion("multi_round_support =", value, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportNotEqualTo(Byte value) {
            addCriterion("multi_round_support <>", value, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportGreaterThan(Byte value) {
            addCriterion("multi_round_support >", value, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportGreaterThanOrEqualTo(Byte value) {
            addCriterion("multi_round_support >=", value, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportLessThan(Byte value) {
            addCriterion("multi_round_support <", value, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportLessThanOrEqualTo(Byte value) {
            addCriterion("multi_round_support <=", value, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportIn(List<Byte> values) {
            addCriterion("multi_round_support in", values, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportNotIn(List<Byte> values) {
            addCriterion("multi_round_support not in", values, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportBetween(Byte value1, Byte value2) {
            addCriterion("multi_round_support between", value1, value2, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andMultiRoundSupportNotBetween(Byte value1, Byte value2) {
            addCriterion("multi_round_support not between", value1, value2, "multiRoundSupport");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigIsNull() {
            addCriterion("function_call_config is null");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigIsNotNull() {
            addCriterion("function_call_config is not null");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigEqualTo(String value) {
            addCriterion("function_call_config =", value, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigNotEqualTo(String value) {
            addCriterion("function_call_config <>", value, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigGreaterThan(String value) {
            addCriterion("function_call_config >", value, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigGreaterThanOrEqualTo(String value) {
            addCriterion("function_call_config >=", value, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigLessThan(String value) {
            addCriterion("function_call_config <", value, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigLessThanOrEqualTo(String value) {
            addCriterion("function_call_config <=", value, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigLike(String value) {
            addCriterion("function_call_config like", value, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigNotLike(String value) {
            addCriterion("function_call_config not like", value, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigIn(List<String> values) {
            addCriterion("function_call_config in", values, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigNotIn(List<String> values) {
            addCriterion("function_call_config not in", values, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigBetween(String value1, String value2) {
            addCriterion("function_call_config between", value1, value2, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andFunctionCallConfigNotBetween(String value1, String value2) {
            addCriterion("function_call_config not between", value1, value2, "functionCallConfig");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNull() {
            addCriterion("biz_id is null");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNotNull() {
            addCriterion("biz_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizIdEqualTo(String value) {
            addCriterion("biz_id =", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotEqualTo(String value) {
            addCriterion("biz_id <>", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThan(String value) {
            addCriterion("biz_id >", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThanOrEqualTo(String value) {
            addCriterion("biz_id >=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThan(String value) {
            addCriterion("biz_id <", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThanOrEqualTo(String value) {
            addCriterion("biz_id <=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLike(String value) {
            addCriterion("biz_id like", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotLike(String value) {
            addCriterion("biz_id not like", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdIn(List<String> values) {
            addCriterion("biz_id in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotIn(List<String> values) {
            addCriterion("biz_id not in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdBetween(String value1, String value2) {
            addCriterion("biz_id between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotBetween(String value1, String value2) {
            addCriterion("biz_id not between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andSceneTypeIsNull() {
            addCriterion("scene_type is null");
            return (Criteria) this;
        }

        public Criteria andSceneTypeIsNotNull() {
            addCriterion("scene_type is not null");
            return (Criteria) this;
        }

        public Criteria andSceneTypeEqualTo(Byte value) {
            addCriterion("scene_type =", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeNotEqualTo(Byte value) {
            addCriterion("scene_type <>", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeGreaterThan(Byte value) {
            addCriterion("scene_type >", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("scene_type >=", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeLessThan(Byte value) {
            addCriterion("scene_type <", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeLessThanOrEqualTo(Byte value) {
            addCriterion("scene_type <=", value, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeIn(List<Byte> values) {
            addCriterion("scene_type in", values, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeNotIn(List<Byte> values) {
            addCriterion("scene_type not in", values, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeBetween(Byte value1, Byte value2) {
            addCriterion("scene_type between", value1, value2, "sceneType");
            return (Criteria) this;
        }

        public Criteria andSceneTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("scene_type not between", value1, value2, "sceneType");
            return (Criteria) this;
        }

        public Criteria andIconUrlIsNull() {
            addCriterion("icon_url is null");
            return (Criteria) this;
        }

        public Criteria andIconUrlIsNotNull() {
            addCriterion("icon_url is not null");
            return (Criteria) this;
        }

        public Criteria andIconUrlEqualTo(String value) {
            addCriterion("icon_url =", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotEqualTo(String value) {
            addCriterion("icon_url <>", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlGreaterThan(String value) {
            addCriterion("icon_url >", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlGreaterThanOrEqualTo(String value) {
            addCriterion("icon_url >=", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLessThan(String value) {
            addCriterion("icon_url <", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLessThanOrEqualTo(String value) {
            addCriterion("icon_url <=", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLike(String value) {
            addCriterion("icon_url like", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotLike(String value) {
            addCriterion("icon_url not like", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlIn(List<String> values) {
            addCriterion("icon_url in", values, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotIn(List<String> values) {
            addCriterion("icon_url not in", values, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlBetween(String value1, String value2) {
            addCriterion("icon_url between", value1, value2, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotBetween(String value1, String value2) {
            addCriterion("icon_url not between", value1, value2, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorIsNull() {
            addCriterion("icon_background_color is null");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorIsNotNull() {
            addCriterion("icon_background_color is not null");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorEqualTo(String value) {
            addCriterion("icon_background_color =", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorNotEqualTo(String value) {
            addCriterion("icon_background_color <>", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorGreaterThan(String value) {
            addCriterion("icon_background_color >", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorGreaterThanOrEqualTo(String value) {
            addCriterion("icon_background_color >=", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorLessThan(String value) {
            addCriterion("icon_background_color <", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorLessThanOrEqualTo(String value) {
            addCriterion("icon_background_color <=", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorLike(String value) {
            addCriterion("icon_background_color like", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorNotLike(String value) {
            addCriterion("icon_background_color not like", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorIn(List<String> values) {
            addCriterion("icon_background_color in", values, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorNotIn(List<String> values) {
            addCriterion("icon_background_color not in", values, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorBetween(String value1, String value2) {
            addCriterion("icon_background_color between", value1, value2, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorNotBetween(String value1, String value2) {
            addCriterion("icon_background_color not between", value1, value2, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountIsNull() {
            addCriterion("usage_user_count is null");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountIsNotNull() {
            addCriterion("usage_user_count is not null");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountEqualTo(Long value) {
            addCriterion("usage_user_count =", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountNotEqualTo(Long value) {
            addCriterion("usage_user_count <>", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountGreaterThan(Long value) {
            addCriterion("usage_user_count >", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountGreaterThanOrEqualTo(Long value) {
            addCriterion("usage_user_count >=", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountLessThan(Long value) {
            addCriterion("usage_user_count <", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountLessThanOrEqualTo(Long value) {
            addCriterion("usage_user_count <=", value, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountIn(List<Long> values) {
            addCriterion("usage_user_count in", values, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountNotIn(List<Long> values) {
            addCriterion("usage_user_count not in", values, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountBetween(Long value1, Long value2) {
            addCriterion("usage_user_count between", value1, value2, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageUserCountNotBetween(Long value1, Long value2) {
            addCriterion("usage_user_count not between", value1, value2, "usageUserCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountIsNull() {
            addCriterion("usage_message_count is null");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountIsNotNull() {
            addCriterion("usage_message_count is not null");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountEqualTo(Long value) {
            addCriterion("usage_message_count =", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountNotEqualTo(Long value) {
            addCriterion("usage_message_count <>", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountGreaterThan(Long value) {
            addCriterion("usage_message_count >", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountGreaterThanOrEqualTo(Long value) {
            addCriterion("usage_message_count >=", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountLessThan(Long value) {
            addCriterion("usage_message_count <", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountLessThanOrEqualTo(Long value) {
            addCriterion("usage_message_count <=", value, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountIn(List<Long> values) {
            addCriterion("usage_message_count in", values, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountNotIn(List<Long> values) {
            addCriterion("usage_message_count not in", values, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountBetween(Long value1, Long value2) {
            addCriterion("usage_message_count between", value1, value2, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andUsageMessageCountNotBetween(Long value1, Long value2) {
            addCriterion("usage_message_count not between", value1, value2, "usageMessageCount");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNull() {
            addCriterion("owner_user_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNotNull() {
            addCriterion("owner_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdEqualTo(Long value) {
            addCriterion("owner_user_id =", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotEqualTo(Long value) {
            addCriterion("owner_user_id <>", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThan(Long value) {
            addCriterion("owner_user_id >", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("owner_user_id >=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThan(Long value) {
            addCriterion("owner_user_id <", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThanOrEqualTo(Long value) {
            addCriterion("owner_user_id <=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIn(List<Long> values) {
            addCriterion("owner_user_id in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotIn(List<Long> values) {
            addCriterion("owner_user_id not in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdBetween(Long value1, Long value2) {
            addCriterion("owner_user_id between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotBetween(Long value1, Long value2) {
            addCriterion("owner_user_id not between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneIsNull() {
            addCriterion("recommend_scene is null");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneIsNotNull() {
            addCriterion("recommend_scene is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneEqualTo(Boolean value) {
            addCriterion("recommend_scene =", value, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneNotEqualTo(Boolean value) {
            addCriterion("recommend_scene <>", value, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneGreaterThan(Boolean value) {
            addCriterion("recommend_scene >", value, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneGreaterThanOrEqualTo(Boolean value) {
            addCriterion("recommend_scene >=", value, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneLessThan(Boolean value) {
            addCriterion("recommend_scene <", value, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneLessThanOrEqualTo(Boolean value) {
            addCriterion("recommend_scene <=", value, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneIn(List<Boolean> values) {
            addCriterion("recommend_scene in", values, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneNotIn(List<Boolean> values) {
            addCriterion("recommend_scene not in", values, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneBetween(Boolean value1, Boolean value2) {
            addCriterion("recommend_scene between", value1, value2, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andRecommendSceneNotBetween(Boolean value1, Boolean value2) {
            addCriterion("recommend_scene not between", value1, value2, "recommendScene");
            return (Criteria) this;
        }

        public Criteria andSceneSortIsNull() {
            addCriterion("scene_sort is null");
            return (Criteria) this;
        }

        public Criteria andSceneSortIsNotNull() {
            addCriterion("scene_sort is not null");
            return (Criteria) this;
        }

        public Criteria andSceneSortEqualTo(Integer value) {
            addCriterion("scene_sort =", value, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortNotEqualTo(Integer value) {
            addCriterion("scene_sort <>", value, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortGreaterThan(Integer value) {
            addCriterion("scene_sort >", value, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("scene_sort >=", value, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortLessThan(Integer value) {
            addCriterion("scene_sort <", value, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortLessThanOrEqualTo(Integer value) {
            addCriterion("scene_sort <=", value, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortIn(List<Integer> values) {
            addCriterion("scene_sort in", values, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortNotIn(List<Integer> values) {
            addCriterion("scene_sort not in", values, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortBetween(Integer value1, Integer value2) {
            addCriterion("scene_sort between", value1, value2, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andSceneSortNotBetween(Integer value1, Integer value2) {
            addCriterion("scene_sort not between", value1, value2, "sceneSort");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsIsNull() {
            addCriterion("use_instructions is null");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsIsNotNull() {
            addCriterion("use_instructions is not null");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsEqualTo(String value) {
            addCriterion("use_instructions =", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsNotEqualTo(String value) {
            addCriterion("use_instructions <>", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsGreaterThan(String value) {
            addCriterion("use_instructions >", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsGreaterThanOrEqualTo(String value) {
            addCriterion("use_instructions >=", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsLessThan(String value) {
            addCriterion("use_instructions <", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsLessThanOrEqualTo(String value) {
            addCriterion("use_instructions <=", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsLike(String value) {
            addCriterion("use_instructions like", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsNotLike(String value) {
            addCriterion("use_instructions not like", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsIn(List<String> values) {
            addCriterion("use_instructions in", values, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsNotIn(List<String> values) {
            addCriterion("use_instructions not in", values, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsBetween(String value1, String value2) {
            addCriterion("use_instructions between", value1, value2, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsNotBetween(String value1, String value2) {
            addCriterion("use_instructions not between", value1, value2, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListIsNull() {
            addCriterion("document_uid_list is null");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListIsNotNull() {
            addCriterion("document_uid_list is not null");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListEqualTo(String value) {
            addCriterion("document_uid_list =", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListNotEqualTo(String value) {
            addCriterion("document_uid_list <>", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListGreaterThan(String value) {
            addCriterion("document_uid_list >", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListGreaterThanOrEqualTo(String value) {
            addCriterion("document_uid_list >=", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListLessThan(String value) {
            addCriterion("document_uid_list <", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListLessThanOrEqualTo(String value) {
            addCriterion("document_uid_list <=", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListLike(String value) {
            addCriterion("document_uid_list like", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListNotLike(String value) {
            addCriterion("document_uid_list not like", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListIn(List<String> values) {
            addCriterion("document_uid_list in", values, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListNotIn(List<String> values) {
            addCriterion("document_uid_list not in", values, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListBetween(String value1, String value2) {
            addCriterion("document_uid_list between", value1, value2, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListNotBetween(String value1, String value2) {
            addCriterion("document_uid_list not between", value1, value2, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListIsNull() {
            addCriterion("yuque_token_list is null");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListIsNotNull() {
            addCriterion("yuque_token_list is not null");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListEqualTo(String value) {
            addCriterion("yuque_token_list =", value, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListNotEqualTo(String value) {
            addCriterion("yuque_token_list <>", value, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListGreaterThan(String value) {
            addCriterion("yuque_token_list >", value, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListGreaterThanOrEqualTo(String value) {
            addCriterion("yuque_token_list >=", value, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListLessThan(String value) {
            addCriterion("yuque_token_list <", value, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListLessThanOrEqualTo(String value) {
            addCriterion("yuque_token_list <=", value, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListLike(String value) {
            addCriterion("yuque_token_list like", value, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListNotLike(String value) {
            addCriterion("yuque_token_list not like", value, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListIn(List<String> values) {
            addCriterion("yuque_token_list in", values, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListNotIn(List<String> values) {
            addCriterion("yuque_token_list not in", values, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListBetween(String value1, String value2) {
            addCriterion("yuque_token_list between", value1, value2, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andYuqueTokenListNotBetween(String value1, String value2) {
            addCriterion("yuque_token_list not between", value1, value2, "yuqueTokenList");
            return (Criteria) this;
        }

        public Criteria andPluginCommandIsNull() {
            addCriterion("plugin_command is null");
            return (Criteria) this;
        }

        public Criteria andPluginCommandIsNotNull() {
            addCriterion("plugin_command is not null");
            return (Criteria) this;
        }

        public Criteria andPluginCommandEqualTo(String value) {
            addCriterion("plugin_command =", value, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandNotEqualTo(String value) {
            addCriterion("plugin_command <>", value, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandGreaterThan(String value) {
            addCriterion("plugin_command >", value, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandGreaterThanOrEqualTo(String value) {
            addCriterion("plugin_command >=", value, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandLessThan(String value) {
            addCriterion("plugin_command <", value, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandLessThanOrEqualTo(String value) {
            addCriterion("plugin_command <=", value, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandLike(String value) {
            addCriterion("plugin_command like", value, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandNotLike(String value) {
            addCriterion("plugin_command not like", value, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandIn(List<String> values) {
            addCriterion("plugin_command in", values, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandNotIn(List<String> values) {
            addCriterion("plugin_command not in", values, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandBetween(String value1, String value2) {
            addCriterion("plugin_command between", value1, value2, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginCommandNotBetween(String value1, String value2) {
            addCriterion("plugin_command not between", value1, value2, "pluginCommand");
            return (Criteria) this;
        }

        public Criteria andPluginEnableIsNull() {
            addCriterion("plugin_enable is null");
            return (Criteria) this;
        }

        public Criteria andPluginEnableIsNotNull() {
            addCriterion("plugin_enable is not null");
            return (Criteria) this;
        }

        public Criteria andPluginEnableEqualTo(Boolean value) {
            addCriterion("plugin_enable =", value, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableNotEqualTo(Boolean value) {
            addCriterion("plugin_enable <>", value, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableGreaterThan(Boolean value) {
            addCriterion("plugin_enable >", value, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("plugin_enable >=", value, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableLessThan(Boolean value) {
            addCriterion("plugin_enable <", value, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("plugin_enable <=", value, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableIn(List<Boolean> values) {
            addCriterion("plugin_enable in", values, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableNotIn(List<Boolean> values) {
            addCriterion("plugin_enable not in", values, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("plugin_enable between", value1, value2, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andPluginEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("plugin_enable not between", value1, value2, "pluginEnable");
            return (Criteria) this;
        }

        public Criteria andSceneTipsIsNull() {
            addCriterion("scene_tips is null");
            return (Criteria) this;
        }

        public Criteria andSceneTipsIsNotNull() {
            addCriterion("scene_tips is not null");
            return (Criteria) this;
        }

        public Criteria andSceneTipsEqualTo(String value) {
            addCriterion("scene_tips =", value, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsNotEqualTo(String value) {
            addCriterion("scene_tips <>", value, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsGreaterThan(String value) {
            addCriterion("scene_tips >", value, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsGreaterThanOrEqualTo(String value) {
            addCriterion("scene_tips >=", value, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsLessThan(String value) {
            addCriterion("scene_tips <", value, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsLessThanOrEqualTo(String value) {
            addCriterion("scene_tips <=", value, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsLike(String value) {
            addCriterion("scene_tips like", value, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsNotLike(String value) {
            addCriterion("scene_tips not like", value, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsIn(List<String> values) {
            addCriterion("scene_tips in", values, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsNotIn(List<String> values) {
            addCriterion("scene_tips not in", values, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsBetween(String value1, String value2) {
            addCriterion("scene_tips between", value1, value2, "sceneTips");
            return (Criteria) this;
        }

        public Criteria andSceneTipsNotBetween(String value1, String value2) {
            addCriterion("scene_tips not between", value1, value2, "sceneTips");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_scene
     *
     * @mbg.generated do_not_delete_during_merge Fri Nov 22 16:25:51 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_scene
     *
     * @mbg.generated Fri Nov 22 16:25:51 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}