package com.alipay.codegencore.model.openai;


import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 17:24
 */
public class Plan {

    private Long id;

    private String type;

    private String filePath;

    private String step;

    private String similarityCode;

    /**
     * 无参构造器
     */
    public Plan() {
    }

    /**
     * 全参构造器
     * @param id
     * @param type
     * @param filePath
     * @param step
     * @param similarityCode
     */
    public Plan(Long id, String type, String filePath, String step, String similarityCode) {
        this.id = id;
        this.type = type;
        this.filePath = filePath;
        this.step = step;
        this.similarityCode = similarityCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getSimilarityCode() {
        return similarityCode;
    }

    public void setSimilarityCode(String similarityCode) {
        this.similarityCode = similarityCode;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", Plan.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("type='" + type + "'")
                .add("filePath='" + filePath + "'")
                .add("step='" + step + "'")
                .add("similarityCode='" + similarityCode + "'")
                .toString();
    }
}
