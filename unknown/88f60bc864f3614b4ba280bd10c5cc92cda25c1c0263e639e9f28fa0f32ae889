/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.List;

/**
 * <AUTHOR>
 * @version GptTask.java, v 0.1 2023年11月27日 上午11:45 wb-tzg858080
 */
public class GptTask extends ToString {
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务表单数据
     */
    private List<FormItem> formItems;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 跳转链接
     */
    private String taskUrl ;

    /**
     * 任务结果
     */
    private String result;

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public List<FormItem> getFormItems() {
        return formItems;
    }

    public void setFormItems(List<FormItem> formItems) {
        this.formItems = formItems;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskUrl() {
        return taskUrl;
    }

    public void setTaskUrl(String taskUrl) {
        this.taskUrl = taskUrl;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
