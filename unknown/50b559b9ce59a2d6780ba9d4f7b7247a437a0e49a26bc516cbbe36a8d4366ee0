package com.alipay.codegencore.utils;

import com.alipay.codegencore.model.model.RepoInfoModel;
import org.junit.Assert;
import org.junit.Test;

/**
 * @Description 仓库url工具类测试
 * @Date 2021/9/23 8:00 下午
 * <AUTHOR> yunchen
 * @Version 1.0
 */
public class RepoUrlUtilTest {

    private final String codeRepoUrl1="*******************:business_release/appinspect.git";
    private final String codeRepoUrl2="http://code.alipay.com/business_release/appinspect.git";
    private final String codeRepoUrl3="*******************:business_release/appinspect/tree/master";
    private final String codeRepoUrl4="http://code.alipay.com/business_release/appinspect/tree/master";
    private final String codeRepoUrl5="http://code.alipay.com/business_release/appinspect/tree/dev";
    private final String codeRepoUrl6="https://code.alipay.com/business_release/appinspect/tree/dev";
    private final String codeRepoUrl7="https://code.alipay.com/business_release/appinspect/tree/dev-xxx";


    private final String[] codeRepoUrlList=new String[]{codeRepoUrl1,codeRepoUrl2,codeRepoUrl3,codeRepoUrl4,codeRepoUrl5,codeRepoUrl6,codeRepoUrl7};

    @Test
    public void parseRepoUrl() {
        for (String repoUrl: codeRepoUrlList){
            RepoInfoModel repoInfoModel = RepoUrlUtil.parseRepoUrl(repoUrl);
            Assert.assertNotNull(repoInfoModel);
            Assert.assertEquals("code.alipay.com",repoInfoModel.getGitDomain());
            Assert.assertEquals("business_release",repoInfoModel.getRepoGroup());
            Assert.assertEquals("appinspect",repoInfoModel.getRepoName());
        }
    }

    @Test
    public void testParseSpecial() {
        String codeRepoUrl = "*******************:antchain/mychain-1.0-sdk-java.git";
        RepoInfoModel repoInfoModel = RepoUrlUtil.parseRepoUrl(codeRepoUrl);

        Assert.assertNotNull(repoInfoModel);
        Assert.assertEquals("code.alipay.com",repoInfoModel.getGitDomain());
        Assert.assertEquals("antchain",repoInfoModel.getRepoGroup());
        Assert.assertEquals("mychain-1.0-sdk-java",repoInfoModel.getRepoName());
    }

    @Test
    public void buildSSHStypeRepoUrl() {
        RepoInfoModel repoInfoModel=new RepoInfoModel();
        repoInfoModel.setGitDomain("code.alipay.com");
        repoInfoModel.setRepoGroup("business_release");
        repoInfoModel.setRepoName("appinspect");
        String codeRepoUrl = RepoUrlUtil.buildSSHStypeRepoUrl(repoInfoModel);
        Assert.assertEquals("*******************:business_release/appinspect.git",codeRepoUrl);
    }

    @Test
    public void buildHttpStypeRepoUrl() {
        RepoInfoModel repoInfoModel=new RepoInfoModel();
        repoInfoModel.setGitDomain("code.alipay.com");
        repoInfoModel.setRepoGroup("business_release");
        repoInfoModel.setRepoName("appinspect");
        String codeRepoUrl = RepoUrlUtil.buildHttpStypeRepoUrl(repoInfoModel);
        Assert.assertEquals("https://code.alipay.com/business_release/appinspect.git",codeRepoUrl);
    }

    @Test
    public void getHttpStypeRepoUrl() {
        String codeRepoUrl = RepoUrlUtil.getHttpStypeRepoUrl(codeRepoUrl1);
        Assert.assertEquals("https://code.alipay.com/business_release/appinspect.git",codeRepoUrl);
    }

    @Test
    public void getSSHStyleRepoUrl() {
        String codeRepoUrl = RepoUrlUtil.getSSHStyleRepoUrl(codeRepoUrl1);
        Assert.assertEquals("*******************:business_release/appinspect.git",codeRepoUrl);
    }

    @Test
    public void getSSHStypeRepoUrlWithGitDomain() {
        String codeRepoUrl = RepoUrlUtil.getSSHStypeRepoUrlWithGitDomain(codeRepoUrl1,"code.alipay.com");
        Assert.assertEquals("*******************:business_release/appinspect.git",codeRepoUrl);
    }

    @Test
    public void testNull(){
        Assert.assertNull(RepoUrlUtil.parseRepoUrl(null));
        Assert.assertNull(RepoUrlUtil.getSSHStyleRepoUrl(null));
        Assert.assertNull(RepoUrlUtil.getHttpStypeRepoUrl(null));
        Assert.assertNull(RepoUrlUtil.getSSHStypeRepoUrlWithGitDomain(null,null));
        Assert.assertNull(RepoUrlUtil.buildSSHStypeRepoUrl(null));
        Assert.assertNull(RepoUrlUtil.buildHttpStypeRepoUrl(null));
    }

    @Test
    public void testGetAntCodeURL(){
        String result = "https://code.alipay.com/common_release/codemeasure/blob/d372499deffee482b3b0f66445e40dd6932cd676/app/bootstrap/src/main/java/com/alipay/codemeasure/CodeFinderTimedServiceInitListener.java#L10-24";
        String antCodeURL = RepoUrlUtil.getAntCodeURL("common_release","codemeasure","d372499deffee482b3b0f66445e40dd6932cd676","app/bootstrap/src/main/java/com/alipay/codemeasure/CodeFinderTimedServiceInitListener.java",10,24);
        Assert.assertEquals(antCodeURL, result);
    }
}