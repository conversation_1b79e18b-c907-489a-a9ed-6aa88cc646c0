package com.alipay.codegencore.model.model.yuque;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.01.17
 */
public class YuQueTocModel {
    /**
     * @param 分组或文档名称
     */
    private String title;
    /**
     * @param 文档slug
     */
    private String key;
    /**
     * @param 子节点
     */
    private List<YuQueTocModel> children;
    /**
     * @param 分组专属字段，含义为不可勾选
     */
    private Boolean disableCheckbox;
    /**
     * @param 文档专属字段，是否绑定中
     */
    private Boolean binding;
    /**
     * @param uuid
     */
    private String uuid;
    /**
     * @param doc_id， 兼容旧逻辑
     */
    private Long docId;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public List<YuQueTocModel> getChildren() {
        return children;
    }

    public void setChildren(List<YuQueTocModel> children) {
        this.children = children;
    }

    public Boolean getDisableCheckbox() {
        return disableCheckbox;
    }

    public void setDisableCheckbox(Boolean disableCheckbox) {
        this.disableCheckbox = disableCheckbox;
    }

    public Boolean getBinding() {
        return binding;
    }

    public void setBinding(Boolean binding) {
        this.binding = binding;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }
}
