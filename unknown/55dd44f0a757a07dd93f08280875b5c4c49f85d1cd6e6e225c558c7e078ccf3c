package com.alipay.codegencore.service.tool.learning;

import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.model.codegpt.PluginServiceRequestContext;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;

import java.util.List;

/**
 * function call service
 * <AUTHOR>
 */
public interface FunctionCallService {
    /**
     * 根据query搜索工具，是对工具的粗筛
     * @param chatMessageList 消息列表
     * @param sceneDO 场景信息
     * @return 工具列表
     */
    List<String> searchTool(List<ChatMessage> chatMessageList, SceneDO sceneDO);

    /**
     * 执行chatFunctionCall
     * @param chatFunctionCall 方法调用
     */
    String executeFunction(PluginServiceRequestContext queryParams,
                           ChatFunctionCall chatFunctionCall,
                           PluginDO pluginDO,
                           Integer index,
                           boolean isContinue);

    /**
     * 流式对话
     * @param queryParams 用户提问
     * @param pluginDOList 数据库中的插件信息
     */
    void streamChat(PluginServiceRequestContext queryParams, List<PluginDO> pluginDOList);

    /**
     * 提交表单后继续流式对话
     * @param queryParams
     * @param pluginDOList
     */
    void continueStreamChat(PluginServiceRequestContext queryParams, List<PluginDO> pluginDOList);
}
