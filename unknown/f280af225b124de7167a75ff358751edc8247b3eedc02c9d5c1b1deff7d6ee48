package com.alipay.codegencore.model.request.tsingyan;


/**
 * 插件侧对话请求bean
 *
 * <AUTHOR>
 * 创建时间 2023-04-13
 */
public class ChatRequestBean extends AbstractRequestBean {
    /**
     * 模型name
     */
    private String model;

    /**
     * 用户问题
     */
    private String question;

    /**
     * 会话id 用于支持多轮会话
     */
    private String sessionId;

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
