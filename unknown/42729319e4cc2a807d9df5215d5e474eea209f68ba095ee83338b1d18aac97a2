/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.enums;

import com.alipay.codegencore.model.exception.BizException;

/**
 * <AUTHOR>
 * @version UserFeedBackStatusEnum.java, v 0.1 2023年10月31日 下午4:53 lqb01337046
 */
public enum UserFeedBackStatusEnum {
    //未受理
    NOT_HANDLED(1),
    //已受理
    HANDLED(2),

    ;

    private Integer value;

    UserFeedBackStatusEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举
     *
     * @param value
     * @return
     */
    public static UserFeedBackStatusEnum getEnumByValue(Integer value) {
        for (UserFeedBackStatusEnum item : UserFeedBackStatusEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "Invalid value: " + value);
    }
}
