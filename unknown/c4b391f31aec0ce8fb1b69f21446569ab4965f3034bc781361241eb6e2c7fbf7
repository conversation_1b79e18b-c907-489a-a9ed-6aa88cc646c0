/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.common;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @version TotoroService.java, v 0.1 2023年05月31日 上午11:16 changxing.cx
 */
public interface TotoroService {
    /**
     * 自然语言转化为totoro DSL命令
     *
     * @param nl 自然语言
     * @return dsl命令
     */
    String nl2TotoroDSL(String nl);

    /**
     * 将自然语言转化为任务
     *
     * @param data 自然语言字符串
     * @return 任务字符串
     */
    String nl2task(JSONObject data);
}
