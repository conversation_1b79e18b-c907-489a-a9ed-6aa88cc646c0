/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.domain;

/**
 * <AUTHOR>
 * @version DingDingMessageDO.java, v 0.1 2023年10月26日 下午3:10 yhw01352860
 * 更多的消息类型详见：https://open-aliding.alibaba-inc.com/build/document/orgapp/types-of-messages-sent-by-robots
 */
public class DingDingMessageDO {

    /**
     * 可用于普通文字消息
     * @param title
     * @param text
     */
    public DingDingMessageDO(String title, String text) {
        this.title = title;
        this.text = text;
    }

    /**
     * 可用于跳转链接的文字消息
     * @param title
     * @param text
     * @param singleTitle
     * @param singleURL
     */
    public DingDingMessageDO(String title, String text, String singleTitle, String singleURL) {
        this.title = title;
        this.text = text;
        this.singleTitle = singleTitle;
        this.singleURL = singleURL;
    }

    /**
     * 消息标题。这段内容将显示在消息列表里
     */
    private String title;
    /**
     *消息内容
     */
    private String text;
    /**
     *链接文字，如：查看详情
     */
    private String singleTitle;
    /**
     *跳转链接，结合singleTitle一起传
     */
    private String singleURL;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getSingleTitle() {
        return singleTitle;
    }

    public void setSingleTitle(String singleTitle) {
        this.singleTitle = singleTitle;
    }

    public String getSingleURL() {
        return singleURL;
    }

    public void setSingleURL(String singleURL) {
        this.singleURL = singleURL;
    }
}
