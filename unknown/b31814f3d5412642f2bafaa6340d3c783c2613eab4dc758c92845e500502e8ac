package com.alipay.codegencore.utils;

import com.alipay.codegencore.model.enums.RecordLogEnum;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.common.tracer.util.TracerContextUtil;
import com.google.common.base.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 收集日志工具类
 */
public class CollectLogUtils {

    private static final Logger log = LoggerFactory.getLogger(CollectLogUtils.class);
    // 日志时间格式
    public static final String ISO_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 标准时间格式
     * yyyy-MM-dd HH:mm:ss
     */
    public static DateTimeFormatter ISO_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern(ISO_DATE_FORMAT);

    /**
     * 业务埋点日志
     */
    public static final Logger metricLog  = LoggerFactory.getLogger("BIZ-METRIC");


    /**
     * 埋点信息
     *
     * @param action  埋点类型
     * @param content 埋点信息
     * @param <T>     数据类型
     */
    public static <T> void printCollectLog(RecordLogEnum action, Map<String, T> content) {
        StringBuilder sb = new StringBuilder();
        content.put("whichEnv", (T) getServiceEnv());
        content.put("traceId", TracerContextUtil.getTraceId() == null ? null : (T) TracerContextUtil.getTraceId());
        content.forEach((key, value) -> sb.append(key).append("||").append(value).append(";"));
        LocalDateTime now = LocalDateTime.now();
        String date = toISOStr(now);
        metricLog.info("{}@@{}@@{}@@{}@@{}@@{}@@{}@@{}", "codegencore", date, date, 0,
                "SUCCESS", "SUCCESS", action.name, sb);
    }

    /**
     * 埋点信息,适用于api调用传输数据的解析
     *
     * @param action  埋点类型
     * @param content 埋点信息
     * @param <T>     数据类型
     */
    public static <T> void printCollectLogVersion2(RecordLogEnum action, Map<String, T> content) {
        StringBuilder sb = new StringBuilder();
        content.put("whichEnv", (T) getServiceEnv());
        content.put("traceId", TracerContextUtil.getTraceId() == null ? null : (T) TracerContextUtil.getTraceId());
        content.forEach((key, value) -> sb.append(key).append("||").append(value).append("^+^"));
        LocalDateTime now = LocalDateTime.now();
        String date = toISOStr(now);
        metricLog.info("{}@@{}@@{}@@{}@@{}@@{}@@{}@@{}", "codegencore", date, date, 0,
                "SUCCESS", "SUCCESS", action.name, sb);
    }

    /**
     * 埋点信息，传入各应用自定义埋点类型
     *
     * @param action  埋点类型
     * @param content 埋点信息
     * @param <T>     数据类型
     */
    public static <T> void printCollectLog(String action, Map<String, T> content) {
        StringBuilder sb = new StringBuilder();
        content.put("whichEnv", (T) getServiceEnv());
        content.put("traceId", TracerContextUtil.getTraceId() == null ? null : (T) TracerContextUtil.getTraceId());
        content.forEach((key, value) -> sb.append(key).append("||").append(value).append(";"));
        LocalDateTime now = LocalDateTime.now();
        String date = toISOStr(now);
        metricLog.info("{}@@{}@@{}@@{}@@{}@@{}@@{}@@{}", "codegencore", date, date, 0,
                "SUCCESS", "SUCCESS", action, sb);
    }

    /**
     * 将LocalDateTime类型的日期时间转换为ISO格式的字符串。
     *
     * @param dateTime 需要转换的日期时间对象，不能为空。
     * @return 转换后的ISO格式字符串。
     */
    public static String toISOStr(LocalDateTime dateTime) {
        // 使用Preconditions工具类对传入参数进行非空校验。
        Preconditions.checkNotNull(dateTime);
        // 返回按照ISO格式转换后的字符串。
        return dateTime.format(ISO_DATE_TIME_FORMAT);
    }

    /**
     * 获取当前服务所在环境的字符串表示，可能的返回值为"prod"或"pre"。
     */
    public static String getServiceEnv() {
        // 默认环境为生产环境
        String whichEnv = "prod";
        // 如果当前环境为预发环境，则环境标识为"pre"
        if (VisableEnvUtil.isPrePub()) {
            whichEnv = "pre";
        }
        return whichEnv;
    }

}
