package com.alipay.codegencore.model.model.yuque;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 团队token信息
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.01.17
 */
public class YuQueTokenInfoModel {
    private String token;
    private String name;
    @JSONField(name = "team_id")
    private Long teamId;
    @JSONField(name = "operate_user_id")
    private Long operateUserId;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Long operateUserId) {
        this.operateUserId = operateUserId;
    }
}
