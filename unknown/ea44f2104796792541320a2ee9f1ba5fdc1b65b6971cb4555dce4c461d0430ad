package com.alipay.codegencore.model.model;

import java.io.Serializable;
import java.util.List;

/**
 * 模版参数类型
 * 由于需要存到tbase，无法使用map，所以需要定义实现了Serializable接口的对象
 * key:模版参数名
 * value: 模版参数对应的取值来源。例如查找符合某个类型规则的属性名：Field.{type}
 * eg."trService":"Field.PromoMatchFacade"  含义： 将当前class中PromoMatchFacade类型的属性名，赋值给trService参数
 *
 * <AUTHOR>
 * 创建时间 2022-03-24
 */
public class TemplateParamModel implements Serializable {
    /**
     * 防止字段变化导致反序列化失败
     */
    private static final long serialVersionUID = 1L;
    /**
     * 参数key
     */
    private String key;
    /**
     * 参数取值对象
     */
    private List<String> valueList;
    /**
     * 参数默认值
     */
    private String defaultValue;

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<String> getValueList() {
        return valueList;
    }

    public void setValueList(List<String> valueList) {
        this.valueList = valueList;
    }
}
