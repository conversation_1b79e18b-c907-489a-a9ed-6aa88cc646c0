package com.alipay.codegencore.service.middle.agentsecsdk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.agentsecgateway.common.service.sdk.base.AgentSecSdkResult;
import com.alipay.agentsecgateway.common.service.sdk.secsdk.AgentSecSdk;
import com.alipay.agentsecgateway.common.service.sdk.secsdk.vo.ActionResult;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.model.codegpt.PluginServiceRequestContext;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.model.AlgoModelUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.PluginConfigService;
import com.alipay.codegencore.service.tool.learning.impl.FunctionCallServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;

/**
 * agent sec sdk Service
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.04.23
 */
@Service
public class SecAgentHelper {
    private static final String WEB_SOURCE_PLATFORM = "codefuse";
    private static final String OPERATION_TYPE_USER = "user";
    private static final String OPERATION_TYPE_APP = "app";
    private static final String API_TYPE_HTTP = "HTTP";

    private static final String PLUGIN_CONFIG_NAME = "name";
    private static final String PLUGIN_CONFIG_API_URL = "url";
    private static final String PLUGIN_CONFIG_PARAMS = "params";
    private static final String PLUGIN_CONFIG_STAGES = "stages";
    private static final String PLUGIN_CONFIG_INFO = "info";
    private static final String PLUGIN_CONFIG_INFO_VERSION = "version";
    private static final String PLUGIN_CONFIG_VERSION1 = "v1";
    private static final String PLUGIN_CONFIG_STAGE_PREREQUEST = "preRequest";
    private static final String PLUGIN_CONFIG_STAGE_POSTREQUEST = "postRequest";
    private static final String PLUGIN_CONFIG_STAGE_APILIST = "api_list";
    private static final Logger LOGGER = LoggerFactory.getLogger(SecAgentHelper.class);
    @Resource
    private AgentSecSdk agentSecSdk;
    @Resource
    private UserAclService userAclService;
    @Resource
    private TokenService tokenService;
    @Resource
    private PluginConfigService pluginConfigService;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private AlgoModelUtilService algoModelUtilService;

    // agent 权限相关

    /**
     * 检查当前用户或者应用(appName)是否具有该agent权限
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param chatSessionDO chatSessionDO
     */
    public void createAgentSessionCheck(ChatSessionDO chatSessionDO){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return;
        }
        String operationType;
        String operationId;
        if(WEB_SOURCE_PLATFORM.equalsIgnoreCase(chatSessionDO.getSourcePlatform())){
            operationType = OPERATION_TYPE_USER;
            operationId = userAclService.getCurrentUser().getEmpId();
        }else {
            operationType = OPERATION_TYPE_APP;
            TokenDO token = tokenService.getTokenSystem(chatSessionDO.getSourcePlatform());
            if(token!=null && StringUtils.isNotBlank(token.getAppName())){
                operationId = token.getAppName();
            }else {
                operationId = userAclService.getCurrentUser().getEmpId();
            }
        }

        LOGGER.info("agentSecSdk.threadCreate params: sceneId {},operationType {},operationId {}",chatSessionDO.getSceneId(),operationType,operationId);
        AgentSecSdkResult<ActionResult> sdkResult;
        // 避免因为安全审核影响业务逻辑
        try {
            sdkResult = agentSecSdk.threadCreate(
                    chatSessionDO.getSceneId().toString(),        // Agent唯一标识
                    StringUtils.isNotBlank(chatSessionDO.getUid())? chatSessionDO.getUid() : "",            // 会话ID（可选，没有填空字符串）
                    operationType,    // 操作者类型，取值: user表示人, app表示应用
                    operationId
            );
        }catch (Throwable e){
            LOGGER.error("agentSecSdk.threadCreate error: ",e);
            return;
        }
        LOGGER.info("agentSecSdk.threadCreate result:" + JSONObject.toJSONString(sdkResult));
        if(sdkResult.isSuccess() && ActionResult.BLOCK.equals(sdkResult.getData().getAction())){
            throw new BizException(ResponseEnum.NO_AUTH,"创建会话失败,缺少当前agent的权限");
        }
    }


    // 插件 权限相关

    /**
     * 检查当前用户或者应用(appName)是否具有该插件权限
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param queryParams queryParams
     * @param pluginDO pluginDO
     */
    public void checkPluginAuth(PluginServiceRequestContext queryParams, PluginDO pluginDO, ChatFunctionCall chatFunctionCall){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return;
        }
        String sessionUid = "";
        String operationType;
        String operationId;
        String agentId;
        String functionName = "";
        String functionArguments = "";
        if(queryParams.getChatSessionDO()!=null){
            sessionUid = queryParams.getChatSessionDO().getUid();
            operationType = OPERATION_TYPE_USER;
            operationId = queryParams.getUserAuthDO().getEmpId();
            agentId = queryParams.getChatSessionDO().getSceneId().toString();
        }else {
            operationType = OPERATION_TYPE_APP;
            TokenDO tokenDO = tokenService.getTokenSystem(queryParams.getUserName());
            operationId = tokenDO.getAppName();
            if(StringUtils.isBlank(operationId)){
                operationId = queryParams.getUserName();
            }
            agentId = queryParams.getSceneDO().getId().toString();
        }

        AgentSecSdkResult<ActionResult> sdkResult;
        try {
            Map<String, Object> workFlowConfig = pluginConfigService.parseWorkFlowYaml(pluginDO.getWorkflowConfig());
            Map<String, Object> pluginBasicInfo = (Map<String, Object>)workFlowConfig.get(PLUGIN_CONFIG_INFO);
            // 目前只支持version1的插件
            if(!PLUGIN_CONFIG_VERSION1.equalsIgnoreCase(String.valueOf(pluginBasicInfo.get(PLUGIN_CONFIG_INFO_VERSION)))){
                LOGGER.info("toolInit cancel,only support version1 plugin");
                return;
            }
            List<Map<String, Object>> paramSchemaList = (List<Map<String, Object>>) workFlowConfig.get(PLUGIN_CONFIG_PARAMS);
            List<Map<String, Object>> stageConfigList = (List<Map<String, Object>>) workFlowConfig.get(PLUGIN_CONFIG_STAGES);
            List<String> apiUrlVersion1 = getPluginApiUrlVersion1(stageConfigList);
            if(chatFunctionCall!=null){
                functionName = StringUtils.isNotBlank(chatFunctionCall.getName())?chatFunctionCall.getName():"";
                functionArguments = StringUtils.isNotBlank(chatFunctionCall.getArguments())?chatFunctionCall.getArguments():"";
            }else {
                // 插件模式
                functionName = pluginDO.getName();
                Map<String, Object> jsonParam = new HashMap<>();
                addPreRequestParams(queryParams,jsonParam,paramSchemaList,queryParams.getPluginParams());
                functionArguments = JSON.toJSONString(jsonParam);
            }
            LOGGER.info("agentSecSdk.callTool param: {}", agentId + "," + sessionUid + "," + pluginDO.getId().toString() + "," + ",HTTP," + JSON.toJSONString(apiUrlVersion1)+","+JSON.toJSONString(paramSchemaList)+","+functionArguments);
            sdkResult = agentSecSdk.callTool(
                    agentId,     //  agentId
                    sessionUid,     //  会话Id
                    pluginDO.getId().toString(),     //  工具/插件 唯一标识
                    operationType,  // 操作者类型，取值: user表示人, app表示应用
                    operationId,     //  操作者ID, 取值: 工号/appname
                    functionName,     // 调用的function名称
                    functionArguments,     // 调用的function参数，用于审计 （可选，没有填空字符串）
                    API_TYPE_HTTP,     //  调用的接口类型：HTTP/TR/INTERNAL(内置), (没有填空字符串, 如果有多个, 用英文逗号隔开)
                    JSON.toJSONString(apiUrlVersion1),         //  调用的工具API地址，用于审计 （toolCallType为HTTP和RPC类型时需要）(没有填空字符串, 如果有多个, 用英文逗号隔开)
                    functionArguments      //  调用的工具API参数，用于审计 （可选，没有填空字符串）
            );
        }catch (Throwable e){
            LOGGER.error("agentSecSdk.callTool error: ",e);
            return;
        }
        LOGGER.info("agentSecSdk.callTool result:" + JSONObject.toJSONString(sdkResult));
        if(sdkResult.isSuccess() && ActionResult.BLOCK.equals(sdkResult.getData().getAction())){
            throw new BizException(ResponseEnum.NO_AUTH,"你没有当前插件\""+pluginDO.getName()+"\"的权限");
        }
    }

    // 对话 审核相关
    /**
     * AI agent SEC 对话内容审核
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param messageUid messageUid
     * @param questionUid questionUid
     * @param extData extData
     * @param chatMessageList chatMessageList
     * @param longContent longContent
     * @param chatRoleEnum chatRoleEnum
     * @return com.alipay.codegencore.model.model.ReviewResultModel
     */
    public ReviewResultModel checkChatContent(String messageUid,String questionUid, ChatRequestExtData extData, List<ChatMessage> chatMessageList, String longContent, ChatRoleEnum chatRoleEnum){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return null;
        }
        // 避免因为安全审核影响业务逻辑
        try {
            String sessionUid = "";
            String llmModel = "";
            String operatorId = "";
            if(extData!=null){
                sessionUid = extData.getSessionUid();
                if(extData.getAlgoBackendDO()!=null){
                    llmModel = extData.getAlgoBackendDO().getModel();
                }
                if(StringUtils.isNotBlank(extData.getEmpId())){
                    operatorId = extData.getEmpId();
                }
            }
            AgentSecSdkResult<ActionResult> sdkResult;
            if(chatRoleEnum == ChatRoleEnum.USER){
                LOGGER.info("start to check chatPrompt,questionuid = {}, sessionUid = {}, question = {}",questionUid, sessionUid, JSON.toJSONString(chatMessageList));
                sdkResult = agentSecSdk.chatPrompt(questionUid, sessionUid, JSON.toJSONString(chatMessageList), true,StringUtils.isNotBlank(operatorId)?OPERATION_TYPE_USER:"",operatorId,llmModel);
                LOGGER.info("agentSecSdk.chatPrompt result is :{}", JSONObject.toJSONString(sdkResult));

            }else {
                LOGGER.info("start to check chatResponse,questionuid = {}, messageUid={}, sessionUid = {}, AiResult = {}",questionUid, messageUid,sessionUid,longContent);
                sdkResult = agentSecSdk.chatResponse(questionUid, messageUid, sessionUid, longContent, true,StringUtils.isNotBlank(operatorId)?operatorId:OPERATION_TYPE_USER,operatorId,llmModel);
                LOGGER.info("agentSecSdk.chatResponse result is :{}", JSONObject.toJSONString(sdkResult));
            }
            if (sdkResult.isSuccess() && ActionResult.BLOCK.equals(sdkResult.getData().getAction())) {
                return new ReviewResultModel(false);
            }
            return new ReviewResultModel(true);

        }catch (Throwable e){
            LOGGER.error("request AI AGENT SEC fail ",e);
            return null;
        }
    }

    // agent 创建，修改，删除相关

    /**
     * agent创建，向 agent安全网关 同步agent信息
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param sceneDO sceneDO
     */
    public void agentInit(SceneDO sceneDO){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return;
        }
        Long userId = sceneDO.getUserId()!=null?sceneDO.getUserId():sceneDO.getOwnerUserId();
        LOGGER.info("addScene ,operator empId:{} sceneDO:{}", userId, JSON.toJSONString(sceneDO));
        UserAuthDO userAuthDO = userAclService.selectByUserId(userId);
        if(userAuthDO == null){
            LOGGER.info("this agent: {} userAuthDO is null", sceneDO.getId());
            return;
        }
        try{
            AgentSecSdkResult<Void> sdkResult = agentSecSdk.agentInit(
                    sceneDO.getId().toString(),     // Agent唯一标识
                    userAuthDO.getEmpId(),
                    sceneDO.getName(),
                    sceneDO.getDescription(),
                    StringUtils.isNotBlank(sceneDO.getSystemPrompt())?sceneDO.getSystemPrompt():"",
                    StringUtils.isNotBlank(sceneDO.getModel())?sceneDO.getModel():"",
                    getPlugListArray(sceneDO.getPluginList())
            );
            LOGGER.info("agentSecSdk.agentInit, id:{}, result:{}", sceneDO.getId(), sdkResult.toString());
        }catch (Throwable e){
            LOGGER.error("agentSecSdk.agentInit error: ",e);
        }
    }
    /**
     * agent修改，向 agent安全网关 同步agent信息
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param sceneDO sceneDO
     */
    public void agentModify(SceneDO sceneDO){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return;
        }
        String empId = "";
        if(sceneDO.getUserId()!=null){
            empId = userAclService.selectByUserId(sceneDO.getUserId()).getEmpId();
        }
        if(StringUtils.isBlank(empId)){
            empId = userAclService.getCurrentUser().getEmpId();
        }

        LOGGER.info("updateSceneById ,operator empId:{} sceneDO:{}", empId, JSON.toJSONString(sceneDO));
        // 向 agent安全网关 同步agent信息
        try {
            AgentSecSdkResult<Void> sdkResult = agentSecSdk.agentModify(
                    sceneDO.getId().toString(),     // Agent唯一标识
                    empId,
                    sceneDO.getName(),
                    sceneDO.getDescription(),
                    StringUtils.isNotBlank(sceneDO.getSystemPrompt())?sceneDO.getSystemPrompt():"",
                    StringUtils.isNotBlank(sceneDO.getModel())?sceneDO.getModel():"",
                    getPlugListArray(sceneDO.getPluginList())
            );
            LOGGER.info("agentSecSdk.agentModify, id:{}, result:{}", sceneDO.getId(), sdkResult.toString());
        }catch (Throwable e){
            LOGGER.error("agentSecSdk.agentModify error: ",e);
        }

    }
    /**
     * 同步删除agent信息
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param sceneId sceneId
     */
    public void agentDelete(Long sceneId){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return;
        }
        try {
            AgentSecSdkResult<Void> sdkResult = agentSecSdk.agentDelete(String.valueOf(sceneId));
            LOGGER.info("agentSecSdk.agentDelete id:{}, result:{}", sceneId, sdkResult.toString());
        }catch (Throwable e){
            LOGGER.error("agentSecSdk.agentDelete error: ",e);
        }

    }

    // 插件 创建 修改 删除相关

    /**
     * 插件创建时 ，同步信息
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param pluginDO pluginDO
     * @param userAuthDO userAuthDO
     */
    public void pluginInit(PluginDO pluginDO, UserAuthDO userAuthDO){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return;
        }
        try {
            Map<String, Object> pluginConfig = pluginConfigService.parseWorkFlowYaml(pluginDO.getWorkflowConfig());
            Map<String, Object> pluginBasicInfo = (Map<String, Object>)pluginConfig.get(PLUGIN_CONFIG_INFO);
            // 目前只支持version1的插件
            if(!PLUGIN_CONFIG_VERSION1.equalsIgnoreCase(String.valueOf(pluginBasicInfo.get(PLUGIN_CONFIG_INFO_VERSION)))){
                LOGGER.info("toolInit cancel,only support version1 plugin");
                return;
            }
            List<Map<String, Object>> paramSchemaList = (List<Map<String, Object>>) pluginConfig.get(PLUGIN_CONFIG_PARAMS);
            List<Map<String, Object>> stageConfigList = (List<Map<String, Object>>) pluginConfig.get(PLUGIN_CONFIG_STAGES);
            List<String> apiUrlVersion1 = getPluginApiUrlVersion1(stageConfigList);

            // 向 agent安全网关 同步插件信息
            LOGGER.info("agentSecSdk.toolInit, id:{}, operator empId:{}, pluginName{}, ", pluginDO.getId(), userAuthDO.getEmpId(),pluginDO.getName());
            AgentSecSdkResult<Void> sdkResult = agentSecSdk.toolInit(
                    pluginDO.getId().toString(),     // 工具唯一标识
                    pluginDO.getName(),     // 工具名称
                    userAuthDO.getEmpId(),
                    pluginDO.getDescription(),     // 工具描述（可选，没有填空字符串）
                    JSON.toJSONString(paramSchemaList),     // todo: 工具参数描述 （可选，没有填空字符串）
                    API_TYPE_HTTP,     // todo: 调用的接口类型：HTTP/TR/INTERNAL(内置), (没有填空字符串, 如果有多个, 用英文逗号隔开)
                    JSON.toJSONString(apiUrlVersion1),         // todo: 调用的工具API地址，用于审计 （toolCallType为HTTP和RPC类型时需要）(没有填空字符串, 如果有多个, 用英文逗号隔开)
                    pluginDO.getWorkflowConfig()          // 工具整体配置 （可选，没有填空字符串）
            );
            LOGGER.info("agentSecSdk.toolInit, id:{}, result:{}", pluginDO.getId(), sdkResult.toString());
        }catch (Throwable e){
            LOGGER.error("agentSecSdk.toolInit error: ",e);
        }

    }
    /**
     * 插件修改， 同步信息
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param pluginDO pluginDO
     * @param userAuthDO userAuthDO
     */
    public void pluginModify(PluginDO pluginDO, UserAuthDO userAuthDO){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return;
        }
        try{
            Map<String, Object> pluginConfig = pluginConfigService.parseWorkFlowYaml(pluginDO.getWorkflowConfig());
            Map<String, Object> pluginBasicInfo = (Map<String, Object>)pluginConfig.get(PLUGIN_CONFIG_INFO);
            // 目前只支持version1的插件
            if(!PLUGIN_CONFIG_VERSION1.equalsIgnoreCase(String.valueOf(pluginBasicInfo.get(PLUGIN_CONFIG_INFO_VERSION)))){
                LOGGER.info("toolInit cancel,only support version1 plugin");
                return;
            }
            List<Map<String, Object>> paramSchemaList = (List<Map<String, Object>>) pluginConfig.get(PLUGIN_CONFIG_PARAMS);
            List<Map<String, Object>> stageConfigList = (List<Map<String, Object>>) pluginConfig.get(PLUGIN_CONFIG_STAGES);
            List<String> apiUrlVersion1 = getPluginApiUrlVersion1(stageConfigList);
            // 向 agent安全网关 同步插件信息
            LOGGER.info("agentSecSdk.toolModify, id:{}, operator empId:{}, pluginName{}, ", pluginDO.getId(), userAuthDO.getEmpId(),pluginDO.getName());
            AgentSecSdkResult<Void> sdkResult = agentSecSdk.toolModify(
                    pluginDO.getId().toString(),     // 工具唯一标识
                    pluginDO.getName(),     // 工具名称
                    userAuthDO.getEmpId(),     //  工具owner (工号)
                    pluginDO.getDescription(),     // 工具描述（可选，没有填空字符串）
                    JSON.toJSONString(paramSchemaList),     //  工具参数描述 （可选，没有填空字符串）
                    API_TYPE_HTTP,     //  调用的接口类型：HTTP/TR/INTERNAL(内置), (没有填空字符串, 如果有多个, 用英文逗号隔开)
                    JSON.toJSONString(apiUrlVersion1),         // 调用的工具API地址，用于审计 （toolCallType为HTTP和RPC类型时需要）(没有填空字符串, 如果有多个, 用英文逗号隔开)
                    pluginDO.getWorkflowConfig()          // 工具整体配置 （可选，没有填空字符串）
            );
            LOGGER.info("agentSecSdk.toolModify, id:{}, result:{}", pluginDO.getId(), sdkResult.toString());
        }catch (Throwable e){
            LOGGER.error("agentSecSdk.toolModify error: ",e);
        }

    }

    /**
     * 插件删除， 同步信息
     *
     * <AUTHOR>
     * @since 2024.04.23
     * @param pluginId pluginId
     */
    public void pluginDelete(Long pluginId){
        if(!codeGPTDrmConfig.isOpenAgentSecSdk()){
            LOGGER.info("agentSecSdk is closed");
            return;
        }
        try{
            AgentSecSdkResult<Void> sdkResult = agentSecSdk.toolDelete(String.valueOf(pluginId));
            LOGGER.info("agentSecSdk.toolDelete, id:{}, result:{}", pluginId, sdkResult.toString());
        }catch (Throwable e){
            LOGGER.error("agentSecSdk.toolDelete error: ",e);
        }

    }

    /**
     * AgentSecSdk所需传递plugList
     *
     * <AUTHOR>
     * @since 2024.04.17
     * @param plugins plugins
     * @return java.util.List<java.lang.String>
     */
    private List<String> getPlugListArray(String plugins){
        if(StringUtils.isBlank(plugins)){
            return new ArrayList<>();
        }
        return JSONArray.parseArray(plugins, String.class);
    }


    /**
     * 获取version1的插件的apiUrls
     *
     * <AUTHOR>
     * @since 2024.04.17
     * @param stageConfigList stageConfigList
     * @return java.util.List<java.lang.String>
     */
    private List<String> getPluginApiUrlVersion1(List<Map<String,Object>> stageConfigList){
        List<String> apiUrl = new ArrayList<>();
        for (Map<String, Object> stringObjectMap : stageConfigList) {
            if(PLUGIN_CONFIG_STAGE_PREREQUEST.equalsIgnoreCase(String.valueOf(stringObjectMap.get(PLUGIN_CONFIG_NAME)))){
                // 这里强转参考PluginWorkflowServiceImpl.preRequest方法，此处强转均使用try catch捕获，不会影响原本的业务逻辑
                ArrayList<Map<String, Object>> apiList = (ArrayList<Map<String, Object>>) stringObjectMap.get(PLUGIN_CONFIG_STAGE_APILIST);
                Map<String, Object> api = apiList.get(0);
                apiUrl.add((String) api.get(PLUGIN_CONFIG_API_URL));
            }
            if(PLUGIN_CONFIG_STAGE_POSTREQUEST.equalsIgnoreCase(String.valueOf(stringObjectMap.get(PLUGIN_CONFIG_NAME)))){
                apiUrl.add(String.valueOf(stringObjectMap.get(PLUGIN_CONFIG_API_URL)));
            }
        }
        return apiUrl;
    }

    /**
     * 添加请求http的参数
     * @param paramsJson 请求http接口的参数，paramsJson是引用传递，可以在校验过程中put参数
     * @param paramSchemaList
     * @param params
     * @return
     */
    private void addPreRequestParams(PluginServiceRequestContext queryParams, Map<String, Object> paramsJson, List<Map<String, Object>> paramSchemaList, Map<String, Object> params) {
        for (Map<String, Object> paramMap : paramSchemaList) {
            String paramKey = (String) paramMap.get("name");
            Object defaultVal = paramMap.get("default");
            // 匹配 ${ 和 } 之间的内容,如果defaultVal是类似${sessionUid}格式的，给默认赋值
            if (defaultVal != null) {
                Matcher matcher = FunctionCallServiceImpl.DEFAULT_PARAM_PATTERN.matcher(defaultVal.toString());
                if (matcher.matches()) {
                    String key = matcher.group(2);
                    JSONObject sessionContext = algoModelUtilService.getSessionContext(queryParams);
                    if(sessionContext != null){
                        defaultVal = sessionContext.get(key);
                    }
                }
            }
            Object keyValue = params!=null && params.get(paramKey) != null ? params.get(paramKey) : defaultVal;
            paramsJson.put(paramKey, keyValue);
        }
    }

}
