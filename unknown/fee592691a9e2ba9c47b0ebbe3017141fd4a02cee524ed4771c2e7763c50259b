package com.alipay.codegencore.model.domain;

public class ConfigDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_config.id
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_config.name
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_config.value
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    private String value;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_config.id
     *
     * @return the value of cg_config.id
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_config.id
     *
     * @param id the value for cg_config.id
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_config.name
     *
     * @return the value of cg_config.name
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_config.name
     *
     * @param name the value for cg_config.name
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_config.value
     *
     * @return the value of cg_config.value
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    public String getValue() {
        return value;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_config.value
     *
     * @param value the value for cg_config.value
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    public void setValue(String value) {
        this.value = value;
    }
}