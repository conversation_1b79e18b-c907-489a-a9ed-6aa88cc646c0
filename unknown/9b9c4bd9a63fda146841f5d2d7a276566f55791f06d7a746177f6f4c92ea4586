package com.alipay.codegencore.model.enums;

/**
 * 系统名
 *
 * <AUTHOR>
 * 创建时间 2022-10-19
 */
public enum OsTypeEnum {
    /**
     * 可适配任意系统
     */
    ANY(0),
    /**
     * win环境
     */
    WIN(1),
    /**
     * mac环境
     */
    MAC(2),
    /**
     * linux环境
     */
    LINUX(3),
    /**
     * 异常数据
     */
    UNKNOWN(99);

    OsTypeEnum(int osType) {
        this.osType = osType;
    }

    /**
     * 系统名类型
     * 如果是 {@code 0}，则代表适配任何系统
     */
    private int osType;

    public int getOsType() {
        return osType;
    }

    public void setOsType(int osType) {
        this.osType = osType;
    }

    /**
     * 根据type获取枚举
     *
     * @return
     */
    public static OsTypeEnum getOsTypeEnumByType(int osType) {
        for (OsTypeEnum osTypeEnum : OsTypeEnum.values()) {
            if (osTypeEnum.getOsType() == osType) {
                return osTypeEnum;
            }
        }
        return UNKNOWN;
    }

}
