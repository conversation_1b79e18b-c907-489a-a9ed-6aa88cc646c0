package com.alipay.codegencore.model.contant;

/**
 * 算法实现配置的键名
 */
public class AlgoImplConfigKey {
    /**
     * 服务器地址
     */
    public static final String SERVER = "server";
    /**
     * 频率惩罚
     */
    public static final String FREQUENCY_PENALTY = "frequencyPenalty";
    /**
     * 存在惩罚
     */
    public static final String PRESENCE_PENALTY = "presencePenalty";

    /**
     * n值
     */
    public static final String N = "n";
    /**
     * 温度参数
     */
    public static final String TEMPERATURE = "temperature";
    /**
     * top p值
     */
    public static final String TOP_P = "topP";
    /**
     * top k值
     */
    public static final String TOP_K = "topK";
    /**
     * outSeqLength的值
     */
    public static final String OUT_SEQ_LENGTH = "outSeqLength";
    /**
     * maya 场景名
     */
    public static final String SCENE_NAME = "sceneName";
    /**
     * maya chainName
     */
    public static final String CHAIN_NAME = "chainName";
    /**
     * requestTimeOut的值
     */
    public static final String REQUEST_TIME_OUT = "requestTimeOut";
    /**
     * beamWidth的值
     */
    public static final String BEAM_WIDTH = "beamWidth";
    /**
     * 长度惩罚
     */
    public static final String LEN_PENALTY = "lenPenalty";
    /**
     * 获取批量化处理时的时间窗口大小，时间单位为毫秒
     */
    public static final String BATCH_TIME_WINDOW = "batchTimeWindow";
    /**
     * 获取批量化处理时的批处理大小，大于此值就发送
     */
    public static final String BATCH_SIZE = "batchSize";
    /**
     * 流式回答数据包轮询步长,单位毫秒
     */
    public static final String STREAM_DATA_POLLING_STEP = "streamDataPollingStep";
    /**
     * 第一个流式回答数据包等待超时时间,单位毫秒
     */
    public static final String FIRST_STREAM_DATA_WAIT_TIME = "firstStreamDataWaitTime";
    /**
     * 流式回答数据包等待超时时间,单位毫秒
     */
    public static final String COMMON_STREAM_DATA_WAIT_TIME = "commonStreamDataWaitTime";
    /**
     * 流式接口中句子的最小长度
     */
    public static final String SENTENCE_MIN_LENGTH = "sentenceMinLength";
    /**
     * 流式接口中句子长度的增长步长
     */
    public static final String SENTENCE_LENGTH_GROWTH_STEP = "sentenceLengthGrowthStep";
    /**
     * 流式接口中句子的最大长度
     */
    public static final String SENTENCE_MAX_LENGTH = "sentenceMaxLength";
    /**
     * 流式接口中缓冲器的最小长度
     */
    public static final String BUFFER_MIN_LENGTH = "bufferMinLength";
    /**
     * 流式接口中缓冲器的长度的增长步长
     */
    public static final String BUFFER_LENGTH_GROWTH_STEP = "bufferLengthGrowthStep";
    /**
     * 流式接口中缓冲器的最大长度
     */
    public static final String BUFFER_MAX_LENGTH = "bufferMaxLength";
    /**
     * 请求的模型
     */
    public static final String REQUEST_MODEL = "requestModel";
    /**
     * 请求惩罚
     */
    public static final String REPETITION_PENALTY = "repetitionPenalty";
    /**
     * 随机种子
     */
    public static final String RANDOM_SEED = "randomSeed";
    /**
     * 模型默认的system级别的prompt
     */
    public static final String SYSTEM_PROMPT = "systemPrompt";
    /**
     * AntGLM的连接时间
     */
    public static final String CONN_TIMEOUT = "connTimeout";
    /**
     * AntGLM的最大输出长度-按需设置
     */
    public static final String MAX_OUTPUT_LENGTH = "maxOutputLength";
    /**
     * AntGLM的模型版本
     */
    public static final String VERSION = "version";
    /**
     * AntGLM的模型ID
     */
    public static final String MODEL_ID = "modelId";

    /**
     * 请求maya的参数中map的key
     */
    public static final String REQUEST_MAYA_DATA_KEY = "requestMayaDataKey";

    /**
     * maya返回值的参数中map的key
     */
    public static final String RESPONSE_MAYA_DATA_KEY = "responseMayaDataKey";

    /**
     * 请求法务大模型的算法的参数
     */
    public static final String NUM_BEAMS = "numBeams";

    /**
     * maya 输出/输出格式版本 version
     */
    public static final String RESPONSE_MAYA_DATA_VERSION = "mayaDataVersion";

    /**
     * maya 模型部署的环境
     */
    public static final String MODEL_ENV = "modelEnv";

    /**
     * 停止词
     */
    public static final String STOP_WORDS = "stopWords";
    /**
     * 停止tokens
     */
    public static final String LAST_TOKENS = "lastTokens";

    /**
     * 扩展字段
     */
    public static final String EXT_INFO = "extInfo";
    /**
     * token
     */
    public static final String TOKEN = "token";

}