package com.alipay.codegencore.service.tool.learning.plugin.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.tool.learning.ToolLearningUtil;
import com.alipay.codegencore.service.tool.learning.plugin.CodefuseApiPlugin;
import com.alipay.codegencore.service.utils.ShortUid;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * codefuse插件-数据表选择插件
 * <AUTHOR>
 */
@Service
public class DataTableSelectPlugin implements CodefuseApiPlugin {

    @Resource
    private ConfigService configService;

    @Resource
    private AlgoBackendService algoBackendService;

    /**
     * 请求
     *
     * @param info
     */
    @Override
    public Object request(JSONObject info) {
        String query = info.getString("query");

        String dataTableSelectPromptTemplate = configService.getConfigByKey(AppConstants.CONFIG_KEY_DATA_TABLE_SELECT_PROMPT_TEMPLATE, false);

        String template = ToolLearningUtil.applyPromptTemplate(dataTableSelectPromptTemplate, query, null, null, null, null);
        String systemMessage = configService.getConfigByKey(AppConstants.CONFIG_KEY_DATA_TABLE_SELECT_SYSTEM_PROMPT, false);


        List<ChatMessage> chatMessages = Lists.newArrayList(new ChatMessage(ChatRoleEnum.SYSTEM.getName(), systemMessage)
                , new ChatMessage(ChatRoleEnum.USER.getName(), template));
        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        chatCompletionRequest.setMessages(chatMessages);
        chatCompletionRequest.setModel("gpt-4-0314");

        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(AppConstants.GPT4);
        String requestId = ShortUid.getUid();
        GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId,
                AppConstants.CODEGPT_TOKEN_USER, false, algoBackendDO, chatCompletionRequest);
        params.setUniqueAnswerId(requestId);
        ChatMessage answer = AlgoModelExecutor.getInstance().executorChat(algoBackendDO, params);
        if (answer != null && answer.getContent()!=null) {
            String result = answer.getContent();
            return Map.of("tableName", result);
        }
        return null;
    }
}
