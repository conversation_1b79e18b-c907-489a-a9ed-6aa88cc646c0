package com.alipay.codegencore.web.codegpt;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.RateLimitDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.common.limiter.RateLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 限流的controller
 */
@Slf4j
@CodeTalkWebApi
@RestController
@RequestMapping("/webapi/rateLimit")
public class RateLimitController {

    @Resource
    private RateLimitService rateLimitService;
    @Resource
    private UserAclService userAclService;

    /**
     * 获取所有限流配置
     *
     * @param pageNo   页数
     * @param pageSize 每页数量
     * @return
     */
    @GetMapping(path = "/getAllRateLimit")
    public PageResponse<List<RateLimitDO>> getAllRateLimit(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                           @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                           @RequestParam(required = false) String query,
                                                           @RequestParam(required = false) String type,
                                                           @RequestParam(required = false) String template,
                                                           @RequestParam(required = false) Boolean needLimit,
                                                           @RequestParam(required = false) String sorted) {
        assertAdmin();
        return rateLimitService.getAllRateLimit(pageNo, pageSize, query, type, template, needLimit, sorted);

    }

    /**
     * 根据id查询一条限流规则
     *
     * @param id 页数
     * @return
     */
    @GetMapping(path = "/getRateLimitById")
    public BaseResponse<RateLimitDO> getRateLimitById(@RequestParam Long id) {
        assertAdmin();
        log.info("getRateLimitById id :{}", id);
        return BaseResponse.build(rateLimitService.getRateLimitById(id));
    }

    /**
     * 插入一条限流规则
     * @param rateLimitDO
     * @return
     */
    @PostMapping(path="/insertRateLimit")
    public BaseResponse<Object> insertRateLimit(@RequestBody RateLimitDO rateLimitDO) {
        assertAdmin();
        log.info("insertRateLimit rateLimitDO :{}", JSON.toJSONString(rateLimitDO));
        if (rateLimitDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        rateLimitService.insertSelective(rateLimitDO);
        return BaseResponse.buildSuccess();
    }

    /**
     * 更新一条限流规则
     * @param rateLimitDO
     * @return
     */
    @PostMapping(path="/updateRateLimit")
    public BaseResponse<Object> updateRateLimit(@RequestBody RateLimitDO rateLimitDO) {
        assertAdmin();
        log.info("updateRateLimit rateLimitDO :{}", JSON.toJSONString(rateLimitDO));
        if (rateLimitDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        rateLimitService.updateByPrimaryKeySelective(rateLimitDO);
        return BaseResponse.buildSuccess();
    }

    /**
     * 删除一条限流规则
     * @param id
     * @return
     */
    @GetMapping(path="/deleteRateLimit")
    public BaseResponse<Object> deleteRateLimit(@RequestParam Long id) {
        assertAdmin();

        rateLimitService.deleteByPrimaryKey(id);
        return BaseResponse.buildSuccess();
    }

    private void assertAdmin() {
        if (!userAclService.isAdmin()){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
    }
}
