/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.contant;

/**
 * <AUTHOR>
 * @version WebApiContents.java, v 0.1 2023年03月29日 20:57 xiaobin
 */
public class WebApiContents {

    /**
     * 原始用户信息，即被映射前的用户
     */
    public static final String ORIGIN_USER = "ORIGIN_USER";

    /**
     * 用户信息（可以通过附身登录被代理）
     */
    public static final String CONTEXT_USER = "USER_INFO";

    /**
     * 在线用户tBase key
     */
    public static final String ONLINE_USER_LIST_TBASE_KEY = "online_user_list_key";

    /**
     * 在线用户心跳间隔时间(单位ms)
     */
    public static final Long ONLINE_USER_HEARTBEAT_INTERVAL_TIME = 60 * 1000L;

    /**
     * 判定用户下线的时间
     */
    public static final Long ONLINE_USER_OFFLINE_TIME = 3 * ONLINE_USER_HEARTBEAT_INTERVAL_TIME;

    /**
     * 判定用户下线的时间
     */
    public static final String TBASE_MOCK_USER_INFO_KEY = "tbase_mock_user_info_key";


    /**
     * token用户的owner工号
     */
//    public static final String TOKEN_OWNER_USER_ID = "TOKEN_OWNER_USER_ID";
    /**
     * token user
     */
    public static final String TOKEN_USER = "TOKEN_USER";

    /**
     * 获取 token 对象
     */
    public static final String TOKEN_DO = "TOKEN_DO";

}