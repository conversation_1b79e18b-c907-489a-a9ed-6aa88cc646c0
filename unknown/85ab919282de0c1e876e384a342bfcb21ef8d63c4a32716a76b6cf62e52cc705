package com.alipay.codegencore.web.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.contant.WebApiContents;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.remote.MessageObject;
import com.alipay.codegencore.model.util.RemoteAgentStreamDataBuildUtils;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.codegencore.web.remote.vo.TaskRequestObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/13 14:29
 */
@Slf4j
@RestController
@RequestMapping("/v1/sessions")
public class TaskController {

    /**
     * 补充参数信息
     */
    private static final String SUPPLEMENT_ARGUMENT = "supplement_argument";

    @Autowired
    private UserAclService userAclService;

    @Autowired
    private ChatMessageService chatMessageService;

    @Autowired
    private ChatSessionManageService chatSessionManageService;

    @Autowired
    private SceneService sceneService;

    @Resource
    private SecAgentHelper secAgentHelper;

    /**
     * 创建任务并执行
     *  流式接口
     *  创建并执行 Task
     * 使用场景
     * 基于一个会话发起 Agent 调用
     * @param sessionId
     * @param taskRequestObject
     */
    @PostMapping(value = "/{session_id}/tasks", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public void createTaskAndRun(HttpServletResponse httpServletResponse,
                                 @PathVariable("session_id") String sessionId,
                                 @RequestBody TaskRequestObject taskRequestObject) {

        String taskId = ShortUid.getUid();
        log.info("create task:{} session:{}", taskId, sessionId);

        Long sceneId = NumberUtils.toLong(taskRequestObject.getAgentId());
        SceneDO scene = sceneService.getSceneById(sceneId);
        if (scene == null) {
            ChatUtils.flushSseResponseNoData(httpServletResponse,
                    JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildFailedData(
                            sessionId, sceneId, taskId, ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE.getErrorMsg())));
            return;
        }

        try {
            ChatSessionDO chatSession = sessionUserCheck(sessionId);

            ChatSessionDO chatSessionDO = new ChatSessionDO();
            chatSessionDO.setUid(sessionId);
            chatSessionDO.setSceneId(sceneId);
            if (StringUtils.isNotBlank(scene.getModel())) {
                chatSessionDO.setModel(scene.getModel());
            }
            chatSessionDO.setSourcePlatform(chatSession.getSourcePlatform());
            // 集成agentSecSdk.threadCreate, 判断用户是否有agent使用权限, 根据agentSecSdk.threadCreate返回结果作相应处理
            secAgentHelper.createAgentSessionCheck(chatSessionDO);
            Boolean updateSession = chatSessionManageService.updateSession(chatSessionDO);
            log.info("update session:{} scene:{} result:{}", sessionId, sceneId, updateSession);

            createSessionTaskAndRun(chatSessionDO, taskRequestObject, taskId, httpServletResponse);
        } catch (BizException e) {
            ChatUtils.flushSseResponseNoData(httpServletResponse,
                    JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildFailedData(
                            sessionId, sceneId, taskId, e.getErrorType().getErrorMsg())));
        } catch (Throwable t) {
            log.error("session:{} found exception:", sessionId, t);
            ChatUtils.flushSseResponseNoData(httpServletResponse,
                    JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildFailedData(
                            sessionId, sceneId, taskId, ResponseEnum.ERROR_THROW.getErrorMsg())));
        }
    }

    /**
     * 创建会话任务并执行
     * 同时创建 Session 和 Task 并执行
     * 使用场景
     * 同时创建一个会话，并发起 Agent 调用
     * @param taskRequestObject
     */
    @PostMapping("/tasks")
    public void createSessionTaskAndRun(HttpServletResponse httpServletResponse,
                                        @RequestBody TaskRequestObject taskRequestObject) {

        Long userId = userAclService.getCurrentUser().getId();
        String sourcePlatform = ContextUtil.get(WebApiContents.TOKEN_USER, String.class);
        Long sceneId = NumberUtils.toLong(taskRequestObject.getAgentId());
        log.info("create session userId:{}, sourcePlatform:{}, agentId:{}", userId, sourcePlatform, sceneId);

        String taskId = ShortUid.getUid();
        String sessionId = null;
        try {
            ChatSessionDO chatSessionDO = chatSessionManageService.getNewSession(userId, null,
                    null, sceneId, false, sourcePlatform, null,false);
            if (chatSessionDO == null) {
                throw new BizException(ResponseEnum.SESSION_CREATE_FAIL);
            }
            sessionId = chatSessionDO.getUid();
            log.info("create session success :{} task:{}", sessionId, taskId);

            createSessionTaskAndRun(chatSessionDO, taskRequestObject, taskId, httpServletResponse);
        } catch (BizException e) {
            ChatUtils.flushSseResponseNoData(httpServletResponse,
                    JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildFailedData(
                            sessionId, sceneId, taskId, e.getErrorType().getErrorMsg())));
        } catch (Throwable t) {
            log.error("session:{} found exception:", sessionId, t);
            ChatUtils.flushSseResponseNoData(httpServletResponse,
                    JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildFailedData(
                            sessionId, sceneId, taskId, ResponseEnum.ERROR_THROW.getErrorMsg())));
        }
    }

    /**
     * 重启任务
     * 重新执行 Task
     * 使用场景：Agent 响应结果为暂停，需要用户追加消息后重启 Agent （例如确认执行结果、追加缺失的参数）
     * 使用场景
     * 已发起的 Agent 请求响应结果为暂停，需要用户执行某些动作后（例如确认执行结果、追加缺失的参数）重新执行 Agent
     * @param sessionId
     * @param taskId
     * @param taskRequestObject
     */
    @PostMapping("/{session_id}/tasks/{task_id}/restart")
    public void taskRestart(HttpServletResponse httpServletResponse,
                            @PathVariable("session_id") String sessionId,
                            @PathVariable("task_id") String taskId,
                            @RequestBody TaskRequestObject taskRequestObject) {

        Long sceneId = null;
        try {
            ChatSessionDO chatSessionDO = sessionUserCheck(sessionId);
            sceneId = chatSessionDO.getSceneId();
            //发送第一个包
            ChatUtils.flushSseResponseNoData(httpServletResponse,
                    JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildQueuedData(
                            sessionId, chatSessionDO.getSceneId(), taskId)));

            //任务被取消，不再执行
            if (BooleanUtils.toBoolean(taskRequestObject.getCancel())) {
                ChatUtils.flushSseResponseNoData(httpServletResponse,
                        JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildCancelData(
                                sessionId, chatSessionDO.getSceneId(), taskId)));
                return;
            }

            Map<String, Object> metadata = taskRequestObject.getMetadata();
            JSONObject data = null;
            if (MapUtils.isNotEmpty(metadata)) {
                Object supplementArgument = metadata.get(SUPPLEMENT_ARGUMENT);
                // 这块确定使用 json 字符串传补充的参数信息（JSONObject）
                if (supplementArgument != null) {
                    data = JSON.parseObject(metadata.get(SUPPLEMENT_ARGUMENT).toString());
                }
            }

            chatMessageService.continueConversation(httpServletResponse, taskId, data, true);
        } catch (BizException e) {
            ChatUtils.flushSseResponseNoData(httpServletResponse,
                    JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildFailedData(
                            sessionId, sceneId, taskId, e.getErrorType().getErrorMsg())));
        } catch (Throwable t) {
            log.error("session:{} found exception:", sessionId, t);
            ChatUtils.flushSseResponseNoData(httpServletResponse,
                    JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildFailedData(
                            sessionId, sceneId, taskId, ResponseEnum.ERROR_THROW.getErrorMsg())));
        }
    }

    /**
     * 会话检查、用户权限检查
     * @param sessionId
     */
    private ChatSessionDO sessionUserCheck(String sessionId) {
        if (!userAclService.isSessionBelongToUser(sessionId)) {
            log.warn("session:{} does not belong to current user", sessionId);
            throw new BizException(ResponseEnum.USER_SESSION_NOT_MATCH);
        }

        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionId);
        if(null == chatSessionDO || chatSessionDO.getDeleted() == 1) {
            log.warn("session:{} not existed", sessionId);
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        return chatSessionDO;
    }

    /**
     * 创建会话任务并执行
     * @param chatSessionDO
     * @param taskRequestObject
     * @param httpServletResponse
     */
    private void createSessionTaskAndRun(ChatSessionDO chatSessionDO, TaskRequestObject taskRequestObject,
                                         String taskId, HttpServletResponse httpServletResponse) {

        List<MessageObject> messageObjectList = taskRequestObject.getMessages();
        if (CollectionUtils.isEmpty(messageObjectList)) {
            log.warn("message is empty");
            return;
        }

        final String sessionId = chatSessionDO.getUid();
        //发送第一个包
        ChatUtils.flushSseResponseNoData(httpServletResponse,
                JSON.toJSONString(RemoteAgentStreamDataBuildUtils.buildQueuedData(
                        sessionId, chatSessionDO.getSceneId(), taskId)));

        String content = messageObjectList.get(0).getContent().getValue();

        chatMessageService.conversation(httpServletResponse, sessionId, content, false, true, taskId, false, null);
    }

}
