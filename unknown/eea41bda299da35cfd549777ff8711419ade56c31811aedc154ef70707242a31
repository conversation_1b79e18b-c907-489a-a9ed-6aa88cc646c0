package com.alipay.codegencore.service.middle.msgbroker.handler;

import com.alipay.common.event.UniformEvent;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.service.msgbroker.handler
 * @CreateTime : 2023-04-19
 */
public interface CodegencoreEventHandler {
    /**
     * 事件处理方法（实现者需要保证业务的幂等性）.
     * <p>各业务需catch各自异常</p>
     *
     * @param message 统一事件
     */
    void handle(UniformEvent message);

    /**
     * 获取topic|eventCode，一般用于注册
     *
     * @return topic|eventCode格式的id
     */
    String getTopicEventCodePair();
}
