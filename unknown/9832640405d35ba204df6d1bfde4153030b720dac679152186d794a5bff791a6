package com.alipay.codegencore.service.tool.learning.plugin;

import com.alipay.codegencore.model.model.UpdateSessionContextRequestModel;
import com.alipay.codegencore.model.response.linke.ApprovalUser;
import com.alipay.codegencore.model.response.linke.DefaultPRValueVO;
import com.alipay.codegencore.model.response.linke.WorkItemDefaultVO;
import com.alipay.codegencore.model.response.linke.WorkItemVO;

import java.util.List;

/**
 * 插件调用期间获取linke的信息服务
 *
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.tool.learning.plugin
 * @CreateTime : 2024-03-28
 */
public interface CodeFuseCallLinke {
    /**
     * 根据appid和关键字查询应用名称
     *
     * @param empId
     * @param query
     * @return
     */
    List<String> getAppName(String empId, String query, String sessionId);


    /**
     * 获取工作项列表
     *
     * @param empId
     * @param query
     * @return
     */
    List<WorkItemVO> getWorkItemList(String empId, String query, String sessionId);

    /**
     * 获取pr审批人列表
     *
     * @param empId
     * @return
     */
    List<ApprovalUser> getApprovalUser(String empId, String query, String sessionId);


    /**
     * 获取分支列表
     *
     * @param empId
     * @param query
     * @param sessionId
     * @return
     */
    List<String> getBranchList(String empId, String query, String sessionId);

    /**
     * 设置会话的上下文
     *
     * @param sessionId
     * @param key
     * @param value
     * @return
     */
    Long setSessionContext(String sessionId, String key, String value);

    /**
     * 批量更新会话上下文
     * @param updateSessionContextRequestModel
     * @return
     */
    void setSessionContextBatch(UpdateSessionContextRequestModel updateSessionContextRequestModel);

    /**
     * 根据empId获取默认的应用信息
     *
     * @param empId
     * @return
     */
    WorkItemDefaultVO getDefaultIteration(String empId, String sessionId);

    /**
     * 获取创建pr的默认信息
     *
     * @param empId
     * @param sessionId
     * @return
     */
    DefaultPRValueVO getDefaultPrValue(String empId, String sessionId);
}
