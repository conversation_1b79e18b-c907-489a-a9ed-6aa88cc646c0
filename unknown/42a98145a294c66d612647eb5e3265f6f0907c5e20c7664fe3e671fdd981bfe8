/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.search;

import com.alipay.codegencore.utils.search.TrieTree;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.smartunit.shaded.org.mockito.Mockito.*;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class TrieTree_SSTest extends TrieTree_SSTest_scaffolding {
// allCoveredLines:[21, 22, 23, 29, 30, 41, 44, 45, 46, 48, 51, 53, 65, 66, 67, 68, 71, 72, 73, 74, 76, 78, 80, 84, 88, 89, 98, 128, 129, 130, 131, 132, 143]

  @Test(timeout = 4000)
  public void test_addNode_0()  throws Throwable  {
      //caseID:4dd0cbf80fb063abb32e2d0b2268861f
      //CoveredLines: [21, 22, 23, 41, 44, 51, 51, 53, 98, 128, 129, 130, 131, 132]
      //Input_0_String: 1.0
      //Input_1_TrieTree.TrieNode: {getChildren=map0}
      //Input_2_String: 6oVYACK*UnI?T,n
      //Input_3_Map<String, String>: {}
      //Input_4_boolean: false
      //Assert: assertNull(method_result);
      
      TrieTree trieTree0 = new TrieTree();
      //mock map0
      Map<String, TrieTree.TrieNode> map0 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock trieTree_TrieNode0
      TrieTree.TrieNode trieTree_TrieNode0 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map0).when(trieTree_TrieNode0).getChildren();
      //mock map1
      Map<String, String> map1 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: addNode
      TrieTree.TrieNode trieTree_TrieNode1 = trieTree0.addNode("1.0", trieTree_TrieNode0, "6oVYACK*UnI?T,n", map1, false);
      
      //Test Result Assert
      assertNull(trieTree_TrieNode1);
  }

  @Test(timeout = 4000)
  public void test_addNode_1()  throws Throwable  {
      //caseID:0a266546da362465e2e4b33e5a5f1f36
      //CoveredLines: [21, 22, 23, 41, 44, 45, 46, 48, 48, 53, 98, 128, 129, 130, 131, 132, 143]
      //Input_0_String: 1.0
      //Input_1_TrieTree.TrieNode: {getChildren=hashMap0}
      //Input_2_String: 1
      //Input_3_Map<String, String>: hashMap1
      //Input_4_boolean: true
      //Assert: assertEquals("0", method_result.getValue());
      //Assert: assertEquals("1", method_result.getTemplateId());
      //Assert: assertFalse(hashMap0.isEmpty());
      
      TrieTree trieTree0 = new TrieTree();
      HashMap<String, TrieTree.TrieNode> hashMap0 = new HashMap<String, TrieTree.TrieNode>();
      //mock trieTree_TrieNode0
      TrieTree.TrieNode trieTree_TrieNode0 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(hashMap0).when(trieTree_TrieNode0).getChildren();
      HashMap<String, String> hashMap1 = new HashMap<String, String>();
      
      //Call method: addNode
      TrieTree.TrieNode trieTree_TrieNode1 = trieTree0.addNode("1.0", trieTree_TrieNode0, "1", hashMap1, true);
      
      //Test Result Assert
      assertEquals("0", trieTree_TrieNode1.getValue());
      
      //Test Result Assert
      assertEquals("1", trieTree_TrieNode1.getTemplateId());
      
      //Test Result Assert
      assertFalse(hashMap0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test_clean_2()  throws Throwable  {
      //caseID:********************************
      //CoveredLines: [21, 22, 23, 29, 30, 98, 128, 129, 130, 131, 132]
      
      TrieTree trieTree0 = new TrieTree();
      
      //Call method: clean
      trieTree0.clean();
  }

  @Test(timeout = 4000)
  public void test_getRoot_3()  throws Throwable  {
      //caseID:fda8f83dad05e2f4de473359007270fa
      //CoveredLines: [21, 22, 23, 84, 98, 128, 129, 130, 131, 132]
      //Assert: assertNull(method_result.getTemplateId());
      
      TrieTree trieTree0 = new TrieTree();
      
      //Call method: getRoot
      TrieTree.TrieNode trieTree_TrieNode0 = trieTree0.getRoot();
      
      //Test Result Assert
      assertNull(trieTree_TrieNode0.getTemplateId());
  }

  @Test(timeout = 4000)
  public void test_searchWithOneLevel_4()  throws Throwable  {
      //caseID:b90dfad06c0dd72427c6d013aa3dc133
      //CoveredLines: [21, 22, 23, 65, 67, 68, 98, 128, 129, 130, 131, 132]
      //Input_0_TrieTree.TrieNode: {getChildren=hashMap0 hashMap0}
      //Input_1_String: 
      //Assert: assertNotSame(method_result, trieTree_TrieNode1);
      
      TrieTree trieTree0 = new TrieTree();
      //mock trieTree_TrieNode0
      TrieTree.TrieNode trieTree_TrieNode0 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      HashMap<String, TrieTree.TrieNode> hashMap0 = new HashMap<String, TrieTree.TrieNode>();
      
      hashMap0.put("", trieTree_TrieNode0);
      //mock trieTree_TrieNode1
      TrieTree.TrieNode trieTree_TrieNode1 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(hashMap0, hashMap0).when(trieTree_TrieNode1).getChildren();
      
      //Call method: searchWithOneLevel
      TrieTree.TrieNode trieTree_TrieNode2 = trieTree0.searchWithOneLevel(trieTree_TrieNode1, "");
      
      //Test Result Assert
      assertNotSame(trieTree_TrieNode2, trieTree_TrieNode1);
  }

  @Test(timeout = 4000)
  public void test_searchWithOneLevel_5()  throws Throwable  {
      //caseID:79537a9ae86115563866b1806169ebf0
      //CoveredLines: [21, 22, 23, 65, 67, 71, 80, 98, 128, 129, 130, 131, 132]
      //Input_0_TrieTree.TrieNode: {getChildren=map0 map1, toString=\"e?hN'\"}
      //Input_1_String: 
      //Assert: assertNotNull(method_result);
      //Assert: assertNull(method_result.getTemplateId());
      
      TrieTree trieTree0 = new TrieTree();
      //mock trieTree_TrieNode0
      TrieTree.TrieNode trieTree_TrieNode0 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock map0
      Map<String, TrieTree.TrieNode> map0 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(trieTree_TrieNode0).when(map0).get(any());
      //mock map1
      Map<String, TrieTree.TrieNode> map1 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((TrieTree.TrieNode) null).when(map1).get(any());
      //mock trieTree_TrieNode1
      TrieTree.TrieNode trieTree_TrieNode1 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map0, map1).when(trieTree_TrieNode1).getChildren();
      doReturn("e?hN'").when(trieTree_TrieNode1).toString();
      
      //Call method: searchWithOneLevel
      TrieTree.TrieNode trieTree_TrieNode2 = trieTree0.searchWithOneLevel(trieTree_TrieNode1, "");
      
      //Test Result Assert
      assertNotNull(trieTree_TrieNode2);
      
      //Test Result Assert
      assertNull(trieTree_TrieNode2.getTemplateId());
  }

  @Test(timeout = 4000)
  public void test_searchWithOneLevel_6()  throws Throwable  {
      //caseID:053709414ccd02e69100de0bd8c7edf3
      //CoveredLines: [21, 22, 23, 65, 66, 67, 71, 72, 73, 74, 76, 78, 98, 128, 129, 130, 131, 132]
      //Input_0_TrieTree.TrieNode: {getChildren=map2 map4 map3}
      //Input_1_String: 41
      //Assert: assertNull(method_result);
      
      TrieTree trieTree0 = new TrieTree();
      //mock map0
      Map<String, TrieTree.TrieNode> map0 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((TrieTree.TrieNode) null).when(map0).get(any());
      //mock map1
      Map<String, TrieTree.TrieNode> map1 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((TrieTree.TrieNode) null).when(map1).get(any());
      //mock trieTree_TrieNode0
      TrieTree.TrieNode trieTree_TrieNode0 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map0, map1).when(trieTree_TrieNode0).getChildren();
      //mock map2
      Map<String, TrieTree.TrieNode> map2 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((TrieTree.TrieNode) null).when(map2).get(any());
      //mock map3
      Map<String, TrieTree.TrieNode> map3 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(trieTree_TrieNode0).when(map3).get(any());
      //mock map4
      Map<String, TrieTree.TrieNode> map4 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((TrieTree.TrieNode) null).when(map4).get(any());
      //mock trieTree_TrieNode1
      TrieTree.TrieNode trieTree_TrieNode1 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map2, map4, map3).when(trieTree_TrieNode1).getChildren();
      
      //Call method: searchWithOneLevel
      TrieTree.TrieNode trieTree_TrieNode2 = trieTree0.searchWithOneLevel(trieTree_TrieNode1, "41");
      
      //Test Result Assert
      assertNull(trieTree_TrieNode2);
  }

  @Test(timeout = 4000)
  public void test_searchWithOneLevel_7()  throws Throwable  {
      //caseID:5984c17274dfdb5306e7c0a6056707c1
      //CoveredLines: [21, 22, 23, 65, 66, 67, 71, 72, 73, 74, 78, 80, 98, 128, 129, 130, 131, 132]
      //Input_0_TrieTree.TrieNode: {getChildren=hashMap0 hashMap0 map0}
      //Input_1_String: 2
      //Assert: assertNotNull(method_result);
      //Assert: assertNotSame(method_result, trieTree_TrieNode1);
      
      TrieTree trieTree0 = new TrieTree();
      //mock trieTree_TrieNode0
      TrieTree.TrieNode trieTree_TrieNode0 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("43").when(trieTree_TrieNode0).toString();
      HashMap<String, TrieTree.TrieNode> hashMap0 = new HashMap<String, TrieTree.TrieNode>();
      //mock map0
      Map<String, TrieTree.TrieNode> map0 = (Map<String, TrieTree.TrieNode>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(trieTree_TrieNode0).when(map0).get(any());
      //mock trieTree_TrieNode1
      TrieTree.TrieNode trieTree_TrieNode1 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(hashMap0, hashMap0, map0).when(trieTree_TrieNode1).getChildren();
      
      //Call method: searchWithOneLevel
      TrieTree.TrieNode trieTree_TrieNode2 = trieTree0.searchWithOneLevel(trieTree_TrieNode1, "2");
      
      //Test Result Assert
      assertNotNull(trieTree_TrieNode2);
      
      //Test Result Assert
      assertNotSame(trieTree_TrieNode2, trieTree_TrieNode1);
  }

  @Test(timeout = 4000)
  public void test_setRoot_8()  throws Throwable  {
      //caseID:9f4288e9a6039a8c814241cccb8a6318
      //CoveredLines: [21, 22, 23, 88, 89, 98, 128, 129, 130, 131, 132]
      //Input_0_TrieTree.TrieNode: {}
      
      TrieTree trieTree0 = new TrieTree();
      //mock trieTree_TrieNode0
      TrieTree.TrieNode trieTree_TrieNode0 = mock(TrieTree.TrieNode.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      
      //Call method: setRoot
      trieTree0.setRoot(trieTree_TrieNode0);
  }
}
