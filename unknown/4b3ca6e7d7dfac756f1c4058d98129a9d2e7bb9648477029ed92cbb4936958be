package com.alipay.codegencore.utils.http;

import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * post方法构建
 *
 * <AUTHOR>
 * 创建时间 2022-10-17
 */
public class PostBuilder extends AbstractRequestBuilder {
    private static final Logger LOGGER = LoggerFactory.getLogger(PostBuilder.class);
    /**
     * http请求类型
     */
    private static final String CONTENT_TYPE_KEY = "Content-Type";
    /**
     * http请求类型-json格式
     */
    private static final String CONTENT_TYPE_JSON_VALUE = "application/json";

    /**
     * 头部
     */
    private final List<String> HEADER = Lists.newArrayList(CONTENT_TYPE_KEY, CONTENT_TYPE_JSON_VALUE);

    /**
     * json格式参数
     */
    private String jsonContent;

    /**
     * post请求构造函数
     *
     * @param url
     * @param httpClient
     */
    public PostBuilder(String url, java.net.http.HttpClient httpClient) {
        super(url, httpClient);
    }

    /**
     * 增加json参数
     *
     * @param jsonContent
     * @return
     */
    public PostBuilder content(String jsonContent) {
        this.jsonContent = jsonContent;
        return this;
    }

    /**
     * 增加头部
     *
     * @param key   不可为空
     * @param value 不可为空
     * @return
     */
    public PostBuilder header(String key, String value) {
        if (key == null || value == null) {
            throw new IllegalArgumentException("key and value can not be null!");
        }
        this.HEADER.add(key);
        this.HEADER.add(value);
        return this;
    }

    /**
     * 增加头部
     *
     * @param headers 请求头列表
     * @return
     */
    public PostBuilder headers(Map<String, String> headers) {
        if (headers == null || headers.isEmpty()) {
            throw new IllegalArgumentException("headers can not be null!");
        }
        headers.forEach((key, value) -> {
            this.HEADER.add(key);
            this.HEADER.add(value);
        });
        return this;
    }


    /**
     * 同步发起post请求
     *
     * @param timeout
     * @return
     */
    @Override
    public String syncExecute(long timeout) {
        try {
            return syncExecuteWithExceptionThrow(timeout);
        } catch (Throwable e) {
            LOGGER.error(String.format("请求远程服务Http(POST)异常。地址:%s,参数:%s", url, jsonContent), e);
            return null;
        }
    }

    /**
     * 流式调用远程服务,会由{@link DefaultStreamDataSubscriber)消费
     * 同时每条数据会调用{@link StreamDataListener#eachData(String, Flow.Subscription)} ,上游控制数据输出
     *
     * @param timeout            超时时间
     * @param streamDataListener 数据监听器
     * @throws URISyntaxException
     */
    @Override
    public void streamExecute(long timeout, StreamDataListener streamDataListener, BiConsumer<Integer, String> errorHandler) throws URISyntaxException {

        HttpRequest httpRequest = buildHttpRequest(timeout);
        try {
            httpClient.send(httpRequest, responseInfo -> {
                if (responseInfo.statusCode() != 200) {
                    return HttpResponse.BodyHandlers
                            .fromSubscriber(new ErrorBodySubscriber(errorResponse->errorHandler.accept(responseInfo.statusCode(), errorResponse)))
                            .apply(responseInfo);
                }
                return HttpResponse.BodyHandlers.fromLineSubscriber(new DefaultStreamDataSubscriber(streamDataListener)).apply(responseInfo);
            });
        } catch (Throwable t) {
            streamDataListener.onError(t);
        }

    }


    /**
     * 流式调用远程服务,会由{@link DefaultStreamDataSubscriber)消费，response会有特殊的消费逻辑
     * 同时每条数据会调用{@link StreamDataListener#eachData(String, Flow.Subscription)} ,上游控制数据输出
     *
     * @param timeout            超时时间
     * @param streamDataListener 数据监听器
     * @throws URISyntaxException
     */
    @Override
    public void streamExecuteWithResponseHandler(long timeout, StreamDataListener streamDataListener, BiConsumer<Integer, String> errorHandler, Consumer<HttpResponse.ResponseInfo> responseInfoConsumer) throws URISyntaxException {

        HttpRequest httpRequest = buildHttpRequest(timeout);
        try {
            httpClient.send(httpRequest, responseInfo -> {
                responseInfoConsumer.accept(responseInfo);
                if (responseInfo.statusCode() != 200) {
                    return HttpResponse.BodyHandlers
                            .fromSubscriber(new ErrorBodySubscriber(errorResponse->errorHandler.accept(responseInfo.statusCode(), errorResponse)))
                            .apply(responseInfo);
                }
                return HttpResponse.BodyHandlers.fromLineSubscriber(new DefaultStreamDataSubscriber(streamDataListener)).apply(responseInfo);
            });
        } catch (Throwable t) {
            streamDataListener.onError(t);
        }

    }

    /**
     * 同步执行，执行报错会抛异常
     *
     * @param timeout
     * @return
     */
    @Override
    public String syncExecuteWithExceptionThrow(long timeout) throws IOException, InterruptedException, URISyntaxException {

        return httpClient.send(buildHttpRequest(timeout), HttpResponse.BodyHandlers.ofString()).body();
    }

    /**
     * 同步执行，执行报错会抛异常，返回完整的response
     * @param timeout 超时时间
     * @return 完整的response
     */
    @Override
    public HttpResponse<String> syncExecuteWithFullResponse(long timeout) throws IOException, InterruptedException, URISyntaxException {
        return httpClient.send(buildHttpRequest(timeout), HttpResponse.BodyHandlers.ofString());
    }


    private HttpRequest buildHttpRequest(long timeout) throws URISyntaxException {
        return HttpRequest.newBuilder()
                .uri(new URI(url))
                .headers(HEADER.toArray(String[]::new))
                .POST(HttpRequest.BodyPublishers.ofString(jsonContent))
                .timeout(Duration.ofMillis(timeout))
                .build();
    }
}
