package com.alipay.codegencore.model.enums.tool.learning;

import com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema.*;

/**
 * 阶段类型枚举
 */
public enum StageTypeEnum {
    /**
     * 模型function call阶段
     */
    FUNCTION_CALL("functionCall",FunctionCallStageInput.class, FunctionCallStageOutput.class),
    /**
     * 调用api的阶段
     */
    API("api", ApiStageInput.class, ApiStageOutput.class),
    /**
     * 大模型推理阶段
     */
    MODEL("model",ModelStageInput.class, ModelStageOutput.class),
    /**
     * 根据输出数据进行总结的阶段
     */
    TEMPLATE("template", TemplateStageInput.class, TemplateStageOutput.class),
    ANSWER("answer", null, null);

    private String name;
    private Class<?> inputSchema;
    private Class<?> outputSchema;

    StageTypeEnum(String name, Class<?> inputSchema, Class<?> outputSchema) {
        this.name = name;
        this.inputSchema = inputSchema;
        this.outputSchema = outputSchema;
    }

    /**
     * 根据name获取枚举
     * @param name
     * @return
     */
    public static StageTypeEnum fromString(String name) {
        for (StageTypeEnum stageTypeEnum : StageTypeEnum.values()) {
            if (stageTypeEnum.getName().equalsIgnoreCase(name)) {
                return stageTypeEnum;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Class<?> getInputSchema() {
        return inputSchema;
    }

    public void setInputSchema(Class<?> inputSchema) {
        this.inputSchema = inputSchema;
    }

    public Class<?> getOutputSchema() {
        return outputSchema;
    }

    public void setOutputSchema(Class<?> outputSchema) {
        this.outputSchema = outputSchema;
    }
}
