/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.codegpt.user;

import com.alipay.codegencore.model.domain.UserAuthDO;

/**
 * 在线用户服务
 *
 * <AUTHOR>
 * @version OnlineUserService.java, v 0.1 2023年03月30日 16:06 xiaobin
 */
public interface OnlineUserService {

    /**
     * 用户心跳
     *
     * @param user 用户
     */
    void heartbeat(UserAuthDO user);

    /**
     * 获取在线用户数量
     *
     * @return 在线用户数量
     */
    Long getOnlineUserNum();

    /**
     * 判断用户是否在线
     *
     * @param user 用户
     * @return 是否在线
     */
    boolean isOnline(UserAuthDO user);

    /**
     * 获取用户限流令牌
     * @param user 用户
     * @return 是否获取到令牌
     */
    boolean tryAcquireUserRateLimiter(UserAuthDO user);

    /**
     * mock用户
     * @param empId 用户工号
     * @param mockEmpId mock用户工号
     */
    void mockUser(String empId, String mockEmpId);
}