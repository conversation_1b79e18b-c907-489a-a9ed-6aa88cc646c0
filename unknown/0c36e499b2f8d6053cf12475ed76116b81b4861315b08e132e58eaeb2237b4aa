/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.codegpt;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version CodeGptModelConfig.java, v 0.1 2023年04月10日 15:07 xiaobin
 */
public class CodeGptModelConfig {

    /**
     * 页面展示的标签名称
     */
    private String label;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型版本
     */
    private String version;

    /**
     * 请求地址
     */
    private String requestURL;

    /**
     * 请求类型  MAYA还是ZARK
     */
    private String requestModel;

    /**
     * prompt列表最大长度 往上追溯一轮 = 3（问题+答案+问题）
     */
    private Integer promptMaxLength;

    /**
     * prompt列表最大token数
     */
    private Integer promptMaxToken;

    /**
     * 是否只有管理员可用
     */
    private Boolean onlyAdmin;

    /**
     * 场景名
     */
    private String  sceneName;
    /**
     * 服务名/版本号
     */
    private String  chainName;
    /**
     * 请求超时时间，单位ms
     */
    private Integer requestTimeOut;

    private String handlerClassName;

    /**
     * 期望模型输出的最大长度
     */
    private Integer outSeqLength;

    /**
     * 对每条传入文本，生成的备选输出文本条数
     */
    private Integer beamWidth;

    /**
     * 模型在每次生成下个token的时候 会以softmax的结果为基础得出一个概率分布 以这个概率分布来选择下一个token temperature调整了这个分布的均匀程
     * 可选，不传入则选择默认分布
     */
    private BigDecimal temperature;

    /**
     * int	将模型置信度排名低于k-th的token 置信度都置为0 排除出随机分布的计算可选，不传入则默认为50(可选，不传入则默认为50)
     */
    private Integer topK;

    /**
     * 在模型置信度排名下 只计算前p比例的token 后续的排除出随机分布的计算(可选，不传入则默认关闭该优化)
     */
    private BigDecimal topP;

    /**
     * 防止被生成的语句置信度随着长度衰减 导致无法与较短的文本对比(可选，不传入则默认关闭该优化)
     */
    private Integer lenPenalty;

    /**
     * 调整上文中已多次重复的token被生成的概率(可选，不传入则默认关闭该优化)
     */
    private BigDecimal repetitionPenalty;

    /**
     * 随机种子(可选，不传入则默认关闭该优化)
     */
    private BigDecimal randomSeed;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getRequestURL() {
        return requestURL;
    }

    public void setRequestURL(String requestURL) {
        this.requestURL = requestURL;
    }

    public String getRequestModel() {
        return requestModel;
    }

    public void setRequestModel(String requestModel) {
        this.requestModel = requestModel;
    }

    public Integer getPromptMaxLength() {
        return promptMaxLength;
    }

    public void setPromptMaxLength(Integer promptMaxLength) {
        this.promptMaxLength = promptMaxLength;
    }

    public Integer getPromptMaxToken() {
        return promptMaxToken;
    }

    public void setPromptMaxToken(Integer promptMaxTokens) {
        this.promptMaxToken = promptMaxTokens;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Boolean getOnlyAdmin() {
        return onlyAdmin;
    }

    public void setOnlyAdmin(Boolean onlyAdmin) {
        this.onlyAdmin = onlyAdmin;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public String getChainName() {
        return chainName;
    }

    public void setChainName(String chainName) {
        this.chainName = chainName;
    }

    public Integer getRequestTimeOut() {
        return requestTimeOut;
    }

    public void setRequestTimeOut(Integer requestTimeOut) {
        this.requestTimeOut = requestTimeOut;
    }

    public String getHandlerClassName() {
        return handlerClassName;
    }

    public void setHandlerClassName(String handlerClassName) {
        this.handlerClassName = handlerClassName;
    }

    public Integer getOutSeqLength() {
        return outSeqLength;
    }

    public void setOutSeqLength(Integer outSeqLength) {
        this.outSeqLength = outSeqLength;
    }

    public Integer getBeamWidth() {
        return beamWidth;
    }

    public void setBeamWidth(Integer beamWidth) {
        this.beamWidth = beamWidth;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public BigDecimal getTopP() {
        return topP;
    }

    public void setTopP(BigDecimal topP) {
        this.topP = topP;
    }

    public Integer getLenPenalty() {
        return lenPenalty;
    }

    public void setLenPenalty(Integer lenPenalty) {
        this.lenPenalty = lenPenalty;
    }

    public BigDecimal getRepetitionPenalty() {
        return repetitionPenalty;
    }

    public void setRepetitionPenalty(BigDecimal repetitionPenalty) {
        this.repetitionPenalty = repetitionPenalty;
    }

    public BigDecimal getRandomSeed() {
        return randomSeed;
    }

    public void setRandomSeed(BigDecimal randomSeed) {
        this.randomSeed = randomSeed;
    }
}