package com.alipay.codegencore.utils.thread;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 上下文工具类
 *
 * <AUTHOR>
 * 创建时间 2022-08-03
 */
public class ContextUtil {
    public static final ThreadLocal<Map<String, Object>> CONTEXT = ThreadLocal.withInitial(LinkedHashMap::new);

    /**
     * 根据key获取上下文值
     *
     * @param key key
     * @return 上下文值
     */

    public static <T> T get(String key, Class<T> clazz) {
        return clazz.cast(CONTEXT.get().get(key));
    }

    /**
     * 根据key获取上下文的值
     *
     * @param key key
     * @return 上下文的值
     */
    public static Object get(String key) {
        return CONTEXT.get().get(key);
    }

    /**
     * 根据key获取上下文值
     * @param key
     * @param clazz
     * @param defaultValue 默认值，若获取不到，直接返回defaultValue
     * @param <T>
     * @return
     */
    public static <T> T getOrDefault(String key, Class<T> clazz, T defaultValue) {
        return clazz.cast(CONTEXT.get().get(key)) == null ? defaultValue : clazz.cast(CONTEXT.get().get(key));
    }

    /**
     * 设置上下文
     *
     * @param key
     * @param value
     */
    public static void set(String key, Object value) {
        CONTEXT.get().put(key, value);
    }

    public static Map<String, Object> getAll() {
        return CONTEXT.get();
    }


    /**
     * 清空上下文
     */
    public static void remove() {
        CONTEXT.remove();
    }

}
