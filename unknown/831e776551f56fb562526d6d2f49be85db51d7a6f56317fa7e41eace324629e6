package com.alipay.codegencore.service.impl;

import java.util.HashMap;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.DimaOpenApiService;
import com.alipay.codegencore.utils.codefuse.SignUtil;
import com.alipay.codegencore.utils.http.HttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.impl
 * @CreateTime : 2023-11-03
 */
@Service
public class DimaOpenApiServiceImpl implements DimaOpenApiService {

    private static final Logger        log = LoggerFactory.getLogger(DimaOpenApiServiceImpl.class);
    @Resource
    private              ConfigService configService;


    private static final String DIMA_OPEN_API = "https://devapi-prepub.alipay.com/arkcooprod/openapi/workItem/create";

    /**
     * 再dima平台创建工作项
     *
     * @param empId 指定人empId
     * @param content 内容
     * @param title 标题
     * @return
     */
    @Override
    public Boolean createWorkItem(String empId, String content, String title) {

        long timestamp = System.currentTimeMillis();
        String ACCESS_KEY = configService.getConfigByKey(AppConstants.DIMA_ACCESS_KEY, false);
        String ACCESS_SECRET = configService.getConfigByKey(AppConstants.DIMA_SECRET_KEY, false);
        String sign = null;
        try {
            sign = SignUtil.dimaProjectSign(ACCESS_KEY, ACCESS_SECRET, timestamp);
        } catch (Exception e) {
            log.error("createWorkItem error", e);
            throw new BizException(ResponseEnum.ERROR_THROW);
        }
        HashMap<String,String> headers = new HashMap<>();
        headers.put("accessKey", ACCESS_KEY);
        headers.put("Signature", sign);
        headers.put("timestamp", timestamp + "");
        headers.put("ARK_OPENAPI_TENANT", "alipay");
        JSONObject body = new JSONObject();
        // codefuse工作空间id
        body.put("workspaceId", AppConstants.WORKSPACE_ID);
        //工作项标题
        body.put("subject", title + "-" + timestamp);
        //工作项类型
        body.put("workItemCategory", "Task");
        //指定人empId
        body.put("processorId", empId);
        //优先级
        body.put("priorityId", "95");
        JSONObject workItemDocument = new JSONObject();
        workItemDocument.put("formatType", "MARKDOWN");
        workItemDocument.put("editorType", "yuque");
        //工作项内容
        workItemDocument.put("content", content);
        body.put("workItemDocument", workItemDocument);
        log.info("createWorkItem url:{} request:{} headers:{}", DIMA_OPEN_API, body.toJSONString(), headers);
        try {
            String s = HttpClient.post(DIMA_OPEN_API + "?staffId=" + AppConstants.CREATE_WORKITEM_ASSIGNEE_EMPID).content(
                    body.toJSONString()).headers(headers).syncExecute(99999);
            log.info("createWorkItem result:{}", s);
            JSONObject result = JSONObject.parseObject(s);
            return result.getBoolean("success");
        } catch (Exception e) {
            log.error("createWorkItem error", e);
            throw new BizException(ResponseEnum.CREATE_DIMA_WORKITEM_FAILED);
        }
    }

}
