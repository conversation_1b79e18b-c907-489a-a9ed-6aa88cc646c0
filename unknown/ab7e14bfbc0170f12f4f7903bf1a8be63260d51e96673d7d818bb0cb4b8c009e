<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.ChatMessageDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.ChatMessageDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="session_uid" jdbcType="VARCHAR" property="sessionUid" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="role" jdbcType="VARCHAR" property="role" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="query_index" jdbcType="BIGINT" property="queryIndex" />
    <result column="generation_index" jdbcType="BIGINT" property="generationIndex" />
    <result column="vote" jdbcType="BIGINT" property="vote" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="review_result" jdbcType="VARCHAR" property="reviewResult" />
    <result column="hit_cache" jdbcType="TINYINT" property="hitCache" />
    <result column="hit_query" jdbcType="VARCHAR" property="hitQuery" />
    <result column="service_abnormal_resp" jdbcType="VARCHAR" property="serviceAbnormalResp" />
    <result column="languages" jdbcType="VARCHAR" property="languages" />
    <result column="plugin_log" jdbcType="VARCHAR" property="pluginLog" />
    <result column="runtime_info" jdbcType="VARCHAR" property="runtimeInfo" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="clear" jdbcType="TINYINT" property="clear" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="parent" jdbcType="VARCHAR" property="parent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    id, gmt_create, gmt_modified, session_uid, user_id, role, content, query_index, generation_index,
    vote, comment, uid, deleted, review_result, hit_cache, hit_query, service_abnormal_resp,
    languages, plugin_log, runtime_info, version, status, clear, error_msg, parent
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.ChatMessageDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.ChatMessageDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    delete from cg_chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.ChatMessageDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_chat_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="sessionUid != null">
        session_uid,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="queryIndex != null">
        query_index,
      </if>
      <if test="generationIndex != null">
        generation_index,
      </if>
      <if test="vote != null">
        vote,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="reviewResult != null">
        review_result,
      </if>
      <if test="hitCache != null">
        hit_cache,
      </if>
      <if test="hitQuery != null">
        hit_query,
      </if>
      <if test="serviceAbnormalResp != null">
        service_abnormal_resp,
      </if>
      <if test="languages != null">
        languages,
      </if>
      <if test="pluginLog != null">
        plugin_log,
      </if>
      <if test="runtimeInfo != null">
        runtime_info,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="clear != null">
        clear,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="parent != null">
        parent,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="sessionUid != null">
        #{sessionUid,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="role != null">
        #{role,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="queryIndex != null">
        #{queryIndex,jdbcType=BIGINT},
      </if>
      <if test="generationIndex != null">
        #{generationIndex,jdbcType=BIGINT},
      </if>
      <if test="vote != null">
        #{vote,jdbcType=BIGINT},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="reviewResult != null">
        #{reviewResult,jdbcType=VARCHAR},
      </if>
      <if test="hitCache != null">
        #{hitCache,jdbcType=TINYINT},
      </if>
      <if test="hitQuery != null">
        #{hitQuery,jdbcType=VARCHAR},
      </if>
      <if test="serviceAbnormalResp != null">
        #{serviceAbnormalResp,jdbcType=VARCHAR},
      </if>
      <if test="languages != null">
        #{languages,jdbcType=VARCHAR},
      </if>
      <if test="pluginLog != null">
        #{pluginLog,jdbcType=VARCHAR},
      </if>
      <if test="runtimeInfo != null">
        #{runtimeInfo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="clear != null">
        #{clear,jdbcType=TINYINT},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="parent != null">
        #{parent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.ChatMessageDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    select count(*) from cg_chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    update cg_chat_message
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sessionUid != null">
        session_uid = #{record.sessionUid,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.role != null">
        role = #{record.role,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.queryIndex != null">
        query_index = #{record.queryIndex,jdbcType=BIGINT},
      </if>
      <if test="record.generationIndex != null">
        generation_index = #{record.generationIndex,jdbcType=BIGINT},
      </if>
      <if test="record.vote != null">
        vote = #{record.vote,jdbcType=BIGINT},
      </if>
      <if test="record.comment != null">
        comment = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.uid != null">
        uid = #{record.uid,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.reviewResult != null">
        review_result = #{record.reviewResult,jdbcType=VARCHAR},
      </if>
      <if test="record.hitCache != null">
        hit_cache = #{record.hitCache,jdbcType=TINYINT},
      </if>
      <if test="record.hitQuery != null">
        hit_query = #{record.hitQuery,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceAbnormalResp != null">
        service_abnormal_resp = #{record.serviceAbnormalResp,jdbcType=VARCHAR},
      </if>
      <if test="record.languages != null">
        languages = #{record.languages,jdbcType=VARCHAR},
      </if>
      <if test="record.pluginLog != null">
        plugin_log = #{record.pluginLog,jdbcType=VARCHAR},
      </if>
      <if test="record.runtimeInfo != null">
        runtime_info = #{record.runtimeInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.clear != null">
        clear = #{record.clear,jdbcType=TINYINT},
      </if>
      <if test="record.errorMsg != null">
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.parent != null">
        parent = #{record.parent,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    update cg_chat_message
    set id = #{record.id,jdbcType=BIGINT},
    gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
    gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
    session_uid = #{record.sessionUid,jdbcType=VARCHAR},
    user_id = #{record.userId,jdbcType=BIGINT},
    role = #{record.role,jdbcType=VARCHAR},
    content = #{record.content,jdbcType=VARCHAR},
    query_index = #{record.queryIndex,jdbcType=BIGINT},
    generation_index = #{record.generationIndex,jdbcType=BIGINT},
    vote = #{record.vote,jdbcType=BIGINT},
    comment = #{record.comment,jdbcType=VARCHAR},
    uid = #{record.uid,jdbcType=VARCHAR},
    deleted = #{record.deleted,jdbcType=TINYINT},
    review_result = #{record.reviewResult,jdbcType=VARCHAR},
    hit_cache = #{record.hitCache,jdbcType=TINYINT},
    hit_query = #{record.hitQuery,jdbcType=VARCHAR},
    service_abnormal_resp = #{record.serviceAbnormalResp,jdbcType=VARCHAR},
    languages = #{record.languages,jdbcType=VARCHAR},
    plugin_log = #{record.pluginLog,jdbcType=VARCHAR},
    runtime_info = #{record.runtimeInfo,jdbcType=VARCHAR},
    version = #{record.version,jdbcType=VARCHAR},
    status = #{record.status,jdbcType=VARCHAR},
    clear = #{record.clear,jdbcType=TINYINT},
    error_msg = #{record.errorMsg,jdbcType=VARCHAR},
    parent = #{record.parent,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.ChatMessageDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 04 17:19:09 CST 2024.
    -->
    update cg_chat_message
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="sessionUid != null">
        session_uid = #{sessionUid,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="role != null">
        role = #{role,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="queryIndex != null">
        query_index = #{queryIndex,jdbcType=BIGINT},
      </if>
      <if test="generationIndex != null">
        generation_index = #{generationIndex,jdbcType=BIGINT},
      </if>
      <if test="vote != null">
        vote = #{vote,jdbcType=BIGINT},
      </if>
      <if test="comment != null">
        comment = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="reviewResult != null">
        review_result = #{reviewResult,jdbcType=VARCHAR},
      </if>
      <if test="hitCache != null">
        hit_cache = #{hitCache,jdbcType=TINYINT},
      </if>
      <if test="hitQuery != null">
        hit_query = #{hitQuery,jdbcType=VARCHAR},
      </if>
      <if test="serviceAbnormalResp != null">
        service_abnormal_resp = #{serviceAbnormalResp,jdbcType=VARCHAR},
      </if>
      <if test="languages != null">
        languages = #{languages,jdbcType=VARCHAR},
      </if>
      <if test="pluginLog != null">
        plugin_log = #{pluginLog,jdbcType=VARCHAR},
      </if>
      <if test="runtimeInfo != null">
        runtime_info = #{runtimeInfo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="clear != null">
        clear = #{clear,jdbcType=TINYINT},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="parent != null">
        parent = #{parent,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>