package com.alipay.codegencore.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ChatMessageDOExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public ChatMessageDOExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andSessionUidIsNull() {
            addCriterion("session_uid is null");
            return (Criteria) this;
        }

        public Criteria andSessionUidIsNotNull() {
            addCriterion("session_uid is not null");
            return (Criteria) this;
        }

        public Criteria andSessionUidEqualTo(String value) {
            addCriterion("session_uid =", value, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidNotEqualTo(String value) {
            addCriterion("session_uid <>", value, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidGreaterThan(String value) {
            addCriterion("session_uid >", value, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidGreaterThanOrEqualTo(String value) {
            addCriterion("session_uid >=", value, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidLessThan(String value) {
            addCriterion("session_uid <", value, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidLessThanOrEqualTo(String value) {
            addCriterion("session_uid <=", value, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidLike(String value) {
            addCriterion("session_uid like", value, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidNotLike(String value) {
            addCriterion("session_uid not like", value, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidIn(List<String> values) {
            addCriterion("session_uid in", values, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidNotIn(List<String> values) {
            addCriterion("session_uid not in", values, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidBetween(String value1, String value2) {
            addCriterion("session_uid between", value1, value2, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andSessionUidNotBetween(String value1, String value2) {
            addCriterion("session_uid not between", value1, value2, "sessionUid");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andRoleIsNull() {
            addCriterion("role is null");
            return (Criteria) this;
        }

        public Criteria andRoleIsNotNull() {
            addCriterion("role is not null");
            return (Criteria) this;
        }

        public Criteria andRoleEqualTo(String value) {
            addCriterion("role =", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotEqualTo(String value) {
            addCriterion("role <>", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThan(String value) {
            addCriterion("role >", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThanOrEqualTo(String value) {
            addCriterion("role >=", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLessThan(String value) {
            addCriterion("role <", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLessThanOrEqualTo(String value) {
            addCriterion("role <=", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLike(String value) {
            addCriterion("role like", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotLike(String value) {
            addCriterion("role not like", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleIn(List<String> values) {
            addCriterion("role in", values, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotIn(List<String> values) {
            addCriterion("role not in", values, "role");
            return (Criteria) this;
        }

        public Criteria andRoleBetween(String value1, String value2) {
            addCriterion("role between", value1, value2, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotBetween(String value1, String value2) {
            addCriterion("role not between", value1, value2, "role");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("content is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("content is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("content =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("content <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("content >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("content >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("content <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("content <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("content like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("content not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("content in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("content not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("content between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("content not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andQueryIndexIsNull() {
            addCriterion("query_index is null");
            return (Criteria) this;
        }

        public Criteria andQueryIndexIsNotNull() {
            addCriterion("query_index is not null");
            return (Criteria) this;
        }

        public Criteria andQueryIndexEqualTo(Long value) {
            addCriterion("query_index =", value, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexNotEqualTo(Long value) {
            addCriterion("query_index <>", value, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexGreaterThan(Long value) {
            addCriterion("query_index >", value, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexGreaterThanOrEqualTo(Long value) {
            addCriterion("query_index >=", value, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexLessThan(Long value) {
            addCriterion("query_index <", value, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexLessThanOrEqualTo(Long value) {
            addCriterion("query_index <=", value, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexIn(List<Long> values) {
            addCriterion("query_index in", values, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexNotIn(List<Long> values) {
            addCriterion("query_index not in", values, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexBetween(Long value1, Long value2) {
            addCriterion("query_index between", value1, value2, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andQueryIndexNotBetween(Long value1, Long value2) {
            addCriterion("query_index not between", value1, value2, "queryIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexIsNull() {
            addCriterion("generation_index is null");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexIsNotNull() {
            addCriterion("generation_index is not null");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexEqualTo(Long value) {
            addCriterion("generation_index =", value, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexNotEqualTo(Long value) {
            addCriterion("generation_index <>", value, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexGreaterThan(Long value) {
            addCriterion("generation_index >", value, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexGreaterThanOrEqualTo(Long value) {
            addCriterion("generation_index >=", value, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexLessThan(Long value) {
            addCriterion("generation_index <", value, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexLessThanOrEqualTo(Long value) {
            addCriterion("generation_index <=", value, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexIn(List<Long> values) {
            addCriterion("generation_index in", values, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexNotIn(List<Long> values) {
            addCriterion("generation_index not in", values, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexBetween(Long value1, Long value2) {
            addCriterion("generation_index between", value1, value2, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andGenerationIndexNotBetween(Long value1, Long value2) {
            addCriterion("generation_index not between", value1, value2, "generationIndex");
            return (Criteria) this;
        }

        public Criteria andVoteIsNull() {
            addCriterion("vote is null");
            return (Criteria) this;
        }

        public Criteria andVoteIsNotNull() {
            addCriterion("vote is not null");
            return (Criteria) this;
        }

        public Criteria andVoteEqualTo(Long value) {
            addCriterion("vote =", value, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteNotEqualTo(Long value) {
            addCriterion("vote <>", value, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteGreaterThan(Long value) {
            addCriterion("vote >", value, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteGreaterThanOrEqualTo(Long value) {
            addCriterion("vote >=", value, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteLessThan(Long value) {
            addCriterion("vote <", value, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteLessThanOrEqualTo(Long value) {
            addCriterion("vote <=", value, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteIn(List<Long> values) {
            addCriterion("vote in", values, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteNotIn(List<Long> values) {
            addCriterion("vote not in", values, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteBetween(Long value1, Long value2) {
            addCriterion("vote between", value1, value2, "vote");
            return (Criteria) this;
        }

        public Criteria andVoteNotBetween(Long value1, Long value2) {
            addCriterion("vote not between", value1, value2, "vote");
            return (Criteria) this;
        }

        public Criteria andCommentIsNull() {
            addCriterion("comment is null");
            return (Criteria) this;
        }

        public Criteria andCommentIsNotNull() {
            addCriterion("comment is not null");
            return (Criteria) this;
        }

        public Criteria andCommentEqualTo(String value) {
            addCriterion("comment =", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotEqualTo(String value) {
            addCriterion("comment <>", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThan(String value) {
            addCriterion("comment >", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentGreaterThanOrEqualTo(String value) {
            addCriterion("comment >=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThan(String value) {
            addCriterion("comment <", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLessThanOrEqualTo(String value) {
            addCriterion("comment <=", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentLike(String value) {
            addCriterion("comment like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotLike(String value) {
            addCriterion("comment not like", value, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentIn(List<String> values) {
            addCriterion("comment in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotIn(List<String> values) {
            addCriterion("comment not in", values, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentBetween(String value1, String value2) {
            addCriterion("comment between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andCommentNotBetween(String value1, String value2) {
            addCriterion("comment not between", value1, value2, "comment");
            return (Criteria) this;
        }

        public Criteria andUidIsNull() {
            addCriterion("uid is null");
            return (Criteria) this;
        }

        public Criteria andUidIsNotNull() {
            addCriterion("uid is not null");
            return (Criteria) this;
        }

        public Criteria andUidEqualTo(String value) {
            addCriterion("uid =", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotEqualTo(String value) {
            addCriterion("uid <>", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThan(String value) {
            addCriterion("uid >", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThanOrEqualTo(String value) {
            addCriterion("uid >=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThan(String value) {
            addCriterion("uid <", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThanOrEqualTo(String value) {
            addCriterion("uid <=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLike(String value) {
            addCriterion("uid like", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotLike(String value) {
            addCriterion("uid not like", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidIn(List<String> values) {
            addCriterion("uid in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotIn(List<String> values) {
            addCriterion("uid not in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidBetween(String value1, String value2) {
            addCriterion("uid between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotBetween(String value1, String value2) {
            addCriterion("uid not between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Byte value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Byte value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Byte value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Byte value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Byte value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Byte value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Byte> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Byte> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Byte value1, Byte value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Byte value1, Byte value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andReviewResultIsNull() {
            addCriterion("review_result is null");
            return (Criteria) this;
        }

        public Criteria andReviewResultIsNotNull() {
            addCriterion("review_result is not null");
            return (Criteria) this;
        }

        public Criteria andReviewResultEqualTo(String value) {
            addCriterion("review_result =", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultNotEqualTo(String value) {
            addCriterion("review_result <>", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultGreaterThan(String value) {
            addCriterion("review_result >", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultGreaterThanOrEqualTo(String value) {
            addCriterion("review_result >=", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultLessThan(String value) {
            addCriterion("review_result <", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultLessThanOrEqualTo(String value) {
            addCriterion("review_result <=", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultLike(String value) {
            addCriterion("review_result like", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultNotLike(String value) {
            addCriterion("review_result not like", value, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultIn(List<String> values) {
            addCriterion("review_result in", values, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultNotIn(List<String> values) {
            addCriterion("review_result not in", values, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultBetween(String value1, String value2) {
            addCriterion("review_result between", value1, value2, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andReviewResultNotBetween(String value1, String value2) {
            addCriterion("review_result not between", value1, value2, "reviewResult");
            return (Criteria) this;
        }

        public Criteria andHitCacheIsNull() {
            addCriterion("hit_cache is null");
            return (Criteria) this;
        }

        public Criteria andHitCacheIsNotNull() {
            addCriterion("hit_cache is not null");
            return (Criteria) this;
        }

        public Criteria andHitCacheEqualTo(Byte value) {
            addCriterion("hit_cache =", value, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheNotEqualTo(Byte value) {
            addCriterion("hit_cache <>", value, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheGreaterThan(Byte value) {
            addCriterion("hit_cache >", value, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheGreaterThanOrEqualTo(Byte value) {
            addCriterion("hit_cache >=", value, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheLessThan(Byte value) {
            addCriterion("hit_cache <", value, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheLessThanOrEqualTo(Byte value) {
            addCriterion("hit_cache <=", value, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheIn(List<Byte> values) {
            addCriterion("hit_cache in", values, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheNotIn(List<Byte> values) {
            addCriterion("hit_cache not in", values, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheBetween(Byte value1, Byte value2) {
            addCriterion("hit_cache between", value1, value2, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitCacheNotBetween(Byte value1, Byte value2) {
            addCriterion("hit_cache not between", value1, value2, "hitCache");
            return (Criteria) this;
        }

        public Criteria andHitQueryIsNull() {
            addCriterion("hit_query is null");
            return (Criteria) this;
        }

        public Criteria andHitQueryIsNotNull() {
            addCriterion("hit_query is not null");
            return (Criteria) this;
        }

        public Criteria andHitQueryEqualTo(String value) {
            addCriterion("hit_query =", value, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryNotEqualTo(String value) {
            addCriterion("hit_query <>", value, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryGreaterThan(String value) {
            addCriterion("hit_query >", value, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryGreaterThanOrEqualTo(String value) {
            addCriterion("hit_query >=", value, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryLessThan(String value) {
            addCriterion("hit_query <", value, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryLessThanOrEqualTo(String value) {
            addCriterion("hit_query <=", value, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryLike(String value) {
            addCriterion("hit_query like", value, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryNotLike(String value) {
            addCriterion("hit_query not like", value, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryIn(List<String> values) {
            addCriterion("hit_query in", values, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryNotIn(List<String> values) {
            addCriterion("hit_query not in", values, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryBetween(String value1, String value2) {
            addCriterion("hit_query between", value1, value2, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andHitQueryNotBetween(String value1, String value2) {
            addCriterion("hit_query not between", value1, value2, "hitQuery");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespIsNull() {
            addCriterion("service_abnormal_resp is null");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespIsNotNull() {
            addCriterion("service_abnormal_resp is not null");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespEqualTo(String value) {
            addCriterion("service_abnormal_resp =", value, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespNotEqualTo(String value) {
            addCriterion("service_abnormal_resp <>", value, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespGreaterThan(String value) {
            addCriterion("service_abnormal_resp >", value, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespGreaterThanOrEqualTo(String value) {
            addCriterion("service_abnormal_resp >=", value, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespLessThan(String value) {
            addCriterion("service_abnormal_resp <", value, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespLessThanOrEqualTo(String value) {
            addCriterion("service_abnormal_resp <=", value, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespLike(String value) {
            addCriterion("service_abnormal_resp like", value, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespNotLike(String value) {
            addCriterion("service_abnormal_resp not like", value, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespIn(List<String> values) {
            addCriterion("service_abnormal_resp in", values, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespNotIn(List<String> values) {
            addCriterion("service_abnormal_resp not in", values, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespBetween(String value1, String value2) {
            addCriterion("service_abnormal_resp between", value1, value2, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andServiceAbnormalRespNotBetween(String value1, String value2) {
            addCriterion("service_abnormal_resp not between", value1, value2, "serviceAbnormalResp");
            return (Criteria) this;
        }

        public Criteria andLanguagesIsNull() {
            addCriterion("languages is null");
            return (Criteria) this;
        }

        public Criteria andLanguagesIsNotNull() {
            addCriterion("languages is not null");
            return (Criteria) this;
        }

        public Criteria andLanguagesEqualTo(String value) {
            addCriterion("languages =", value, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesNotEqualTo(String value) {
            addCriterion("languages <>", value, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesGreaterThan(String value) {
            addCriterion("languages >", value, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesGreaterThanOrEqualTo(String value) {
            addCriterion("languages >=", value, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesLessThan(String value) {
            addCriterion("languages <", value, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesLessThanOrEqualTo(String value) {
            addCriterion("languages <=", value, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesLike(String value) {
            addCriterion("languages like", value, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesNotLike(String value) {
            addCriterion("languages not like", value, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesIn(List<String> values) {
            addCriterion("languages in", values, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesNotIn(List<String> values) {
            addCriterion("languages not in", values, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesBetween(String value1, String value2) {
            addCriterion("languages between", value1, value2, "languages");
            return (Criteria) this;
        }

        public Criteria andLanguagesNotBetween(String value1, String value2) {
            addCriterion("languages not between", value1, value2, "languages");
            return (Criteria) this;
        }

        public Criteria andPluginLogIsNull() {
            addCriterion("plugin_log is null");
            return (Criteria) this;
        }

        public Criteria andPluginLogIsNotNull() {
            addCriterion("plugin_log is not null");
            return (Criteria) this;
        }

        public Criteria andPluginLogEqualTo(String value) {
            addCriterion("plugin_log =", value, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogNotEqualTo(String value) {
            addCriterion("plugin_log <>", value, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogGreaterThan(String value) {
            addCriterion("plugin_log >", value, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogGreaterThanOrEqualTo(String value) {
            addCriterion("plugin_log >=", value, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogLessThan(String value) {
            addCriterion("plugin_log <", value, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogLessThanOrEqualTo(String value) {
            addCriterion("plugin_log <=", value, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogLike(String value) {
            addCriterion("plugin_log like", value, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogNotLike(String value) {
            addCriterion("plugin_log not like", value, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogIn(List<String> values) {
            addCriterion("plugin_log in", values, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogNotIn(List<String> values) {
            addCriterion("plugin_log not in", values, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogBetween(String value1, String value2) {
            addCriterion("plugin_log between", value1, value2, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andPluginLogNotBetween(String value1, String value2) {
            addCriterion("plugin_log not between", value1, value2, "pluginLog");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoIsNull() {
            addCriterion("runtime_info is null");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoIsNotNull() {
            addCriterion("runtime_info is not null");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoEqualTo(String value) {
            addCriterion("runtime_info =", value, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoNotEqualTo(String value) {
            addCriterion("runtime_info <>", value, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoGreaterThan(String value) {
            addCriterion("runtime_info >", value, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoGreaterThanOrEqualTo(String value) {
            addCriterion("runtime_info >=", value, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoLessThan(String value) {
            addCriterion("runtime_info <", value, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoLessThanOrEqualTo(String value) {
            addCriterion("runtime_info <=", value, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoLike(String value) {
            addCriterion("runtime_info like", value, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoNotLike(String value) {
            addCriterion("runtime_info not like", value, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoIn(List<String> values) {
            addCriterion("runtime_info in", values, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoNotIn(List<String> values) {
            addCriterion("runtime_info not in", values, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoBetween(String value1, String value2) {
            addCriterion("runtime_info between", value1, value2, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andRuntimeInfoNotBetween(String value1, String value2) {
            addCriterion("runtime_info not between", value1, value2, "runtimeInfo");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(String value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(String value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(String value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(String value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(String value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(String value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLike(String value) {
            addCriterion("version like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotLike(String value) {
            addCriterion("version not like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<String> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<String> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(String value1, String value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(String value1, String value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andClearIsNull() {
            addCriterion("clear is null");
            return (Criteria) this;
        }

        public Criteria andClearIsNotNull() {
            addCriterion("clear is not null");
            return (Criteria) this;
        }

        public Criteria andClearEqualTo(Boolean value) {
            addCriterion("clear =", value, "clear");
            return (Criteria) this;
        }

        public Criteria andClearNotEqualTo(Boolean value) {
            addCriterion("clear <>", value, "clear");
            return (Criteria) this;
        }

        public Criteria andClearGreaterThan(Boolean value) {
            addCriterion("clear >", value, "clear");
            return (Criteria) this;
        }

        public Criteria andClearGreaterThanOrEqualTo(Boolean value) {
            addCriterion("clear >=", value, "clear");
            return (Criteria) this;
        }

        public Criteria andClearLessThan(Boolean value) {
            addCriterion("clear <", value, "clear");
            return (Criteria) this;
        }

        public Criteria andClearLessThanOrEqualTo(Boolean value) {
            addCriterion("clear <=", value, "clear");
            return (Criteria) this;
        }

        public Criteria andClearIn(List<Boolean> values) {
            addCriterion("clear in", values, "clear");
            return (Criteria) this;
        }

        public Criteria andClearNotIn(List<Boolean> values) {
            addCriterion("clear not in", values, "clear");
            return (Criteria) this;
        }

        public Criteria andClearBetween(Boolean value1, Boolean value2) {
            addCriterion("clear between", value1, value2, "clear");
            return (Criteria) this;
        }

        public Criteria andClearNotBetween(Boolean value1, Boolean value2) {
            addCriterion("clear not between", value1, value2, "clear");
            return (Criteria) this;
        }

        public Criteria andErrorMsgIsNull() {
            addCriterion("error_msg is null");
            return (Criteria) this;
        }

        public Criteria andErrorMsgIsNotNull() {
            addCriterion("error_msg is not null");
            return (Criteria) this;
        }

        public Criteria andErrorMsgEqualTo(String value) {
            addCriterion("error_msg =", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgNotEqualTo(String value) {
            addCriterion("error_msg <>", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgGreaterThan(String value) {
            addCriterion("error_msg >", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgGreaterThanOrEqualTo(String value) {
            addCriterion("error_msg >=", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgLessThan(String value) {
            addCriterion("error_msg <", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgLessThanOrEqualTo(String value) {
            addCriterion("error_msg <=", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgLike(String value) {
            addCriterion("error_msg like", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgNotLike(String value) {
            addCriterion("error_msg not like", value, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgIn(List<String> values) {
            addCriterion("error_msg in", values, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgNotIn(List<String> values) {
            addCriterion("error_msg not in", values, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgBetween(String value1, String value2) {
            addCriterion("error_msg between", value1, value2, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andErrorMsgNotBetween(String value1, String value2) {
            addCriterion("error_msg not between", value1, value2, "errorMsg");
            return (Criteria) this;
        }

        public Criteria andParentIsNull() {
            addCriterion("parent is null");
            return (Criteria) this;
        }

        public Criteria andParentIsNotNull() {
            addCriterion("parent is not null");
            return (Criteria) this;
        }

        public Criteria andParentEqualTo(String value) {
            addCriterion("parent =", value, "parent");
            return (Criteria) this;
        }

        public Criteria andParentNotEqualTo(String value) {
            addCriterion("parent <>", value, "parent");
            return (Criteria) this;
        }

        public Criteria andParentGreaterThan(String value) {
            addCriterion("parent >", value, "parent");
            return (Criteria) this;
        }

        public Criteria andParentGreaterThanOrEqualTo(String value) {
            addCriterion("parent >=", value, "parent");
            return (Criteria) this;
        }

        public Criteria andParentLessThan(String value) {
            addCriterion("parent <", value, "parent");
            return (Criteria) this;
        }

        public Criteria andParentLessThanOrEqualTo(String value) {
            addCriterion("parent <=", value, "parent");
            return (Criteria) this;
        }

        public Criteria andParentLike(String value) {
            addCriterion("parent like", value, "parent");
            return (Criteria) this;
        }

        public Criteria andParentNotLike(String value) {
            addCriterion("parent not like", value, "parent");
            return (Criteria) this;
        }

        public Criteria andParentIn(List<String> values) {
            addCriterion("parent in", values, "parent");
            return (Criteria) this;
        }

        public Criteria andParentNotIn(List<String> values) {
            addCriterion("parent not in", values, "parent");
            return (Criteria) this;
        }

        public Criteria andParentBetween(String value1, String value2) {
            addCriterion("parent between", value1, value2, "parent");
            return (Criteria) this;
        }

        public Criteria andParentNotBetween(String value1, String value2) {
            addCriterion("parent not between", value1, value2, "parent");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_chat_message
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 04 17:19:09 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_chat_message
     *
     * @mbg.generated Mon Mar 04 17:19:09 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}