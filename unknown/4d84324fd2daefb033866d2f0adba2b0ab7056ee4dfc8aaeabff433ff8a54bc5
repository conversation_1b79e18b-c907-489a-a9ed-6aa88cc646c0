package com.alipay.codegencore.model.enums.answer;

/**
 * 任务状态
 */
public enum TaskState {

    INIT, FULL_BUILDING, PART_BUILDING, FINISH;

    /**
     * 判断
     * @param name
     * @return
     */
    public boolean equalsName(String name) {
        return this.name().equals(name);
    }

    /**
     * 索引可以使用
     * @param name
     * @return
     */
    public static boolean indexUsable(String name) {
        return PART_BUILDING.equalsName(name) || FINISH.equalsName(name);
    }

}
