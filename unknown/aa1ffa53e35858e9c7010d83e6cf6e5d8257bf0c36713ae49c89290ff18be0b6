<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.CodeTemplateDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.CodeTemplateDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_scene_enum" jdbcType="TINYINT" property="bizSceneEnum" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="rule" jdbcType="VARCHAR" property="rule" />
    <result column="scene_desc" jdbcType="VARCHAR" property="sceneDesc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    id, biz_scene_enum, name, rule, scene_desc
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.CodeTemplateDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_code_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.CodeTemplateDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    delete from cg_code_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.CodeTemplateDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_code_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizSceneEnum != null">
        biz_scene_enum,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="rule != null">
        rule,
      </if>
      <if test="sceneDesc != null">
        scene_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizSceneEnum != null">
        #{bizSceneEnum,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="rule != null">
        #{rule,jdbcType=VARCHAR},
      </if>
      <if test="sceneDesc != null">
        #{sceneDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.CodeTemplateDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    select count(*) from cg_code_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    update cg_code_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizSceneEnum != null">
        biz_scene_enum = #{record.bizSceneEnum,jdbcType=TINYINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.rule != null">
        rule = #{record.rule,jdbcType=VARCHAR},
      </if>
      <if test="record.sceneDesc != null">
        scene_desc = #{record.sceneDesc,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    update cg_code_template
    set id = #{record.id,jdbcType=BIGINT},
    biz_scene_enum = #{record.bizSceneEnum,jdbcType=TINYINT},
    name = #{record.name,jdbcType=VARCHAR},
    rule = #{record.rule,jdbcType=VARCHAR},
    scene_desc = #{record.sceneDesc,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.CodeTemplateDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 13:41:01 CST 2022.
    -->
    update cg_code_template
    <set>
      <if test="bizSceneEnum != null">
        biz_scene_enum = #{bizSceneEnum,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="rule != null">
        rule = #{rule,jdbcType=VARCHAR},
      </if>
      <if test="sceneDesc != null">
        scene_desc = #{sceneDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>