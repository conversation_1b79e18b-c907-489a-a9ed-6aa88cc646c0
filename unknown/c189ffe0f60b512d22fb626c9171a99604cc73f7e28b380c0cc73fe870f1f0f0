/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.codegpt;

import java.util.List;

import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserSceneRecordsDO;
import com.alipay.codegencore.model.enums.ControlTypeEnum;
import com.alipay.codegencore.model.openai.UserSceneRecordsVO;
import com.alipay.codegencore.model.response.PageResponse;

/**
 * <AUTHOR>
 * @version UserSceneRecordsService.java, v 0.1 2023年08月15日 下午5:13 lqb01337046
 */
public interface UserSceneRecordsService {
    /**
     * 获取助手的权限信息
     *
     * @param sceneId 助手id
     * @return
     */
    PageResponse<List<UserSceneRecordsVO>> getSceneControlInfo(Long sceneId, String query, ControlTypeEnum controlTypeEnum, int pageNo, int pageSize);

    /**
     * 批量导入助手的权限信息
     *
     * @param sceneId 助手id
     * @param empIds  empId
     * @return
     */
    List<String> batchInsertUserScene(SceneDO sceneDO, List<String> empIds, ControlTypeEnum controlTypeEnum);

    /**
     * 修改助手的一个用户的信息
     *
     * @param sceneId     助手id
     * @param userId      用户id
     * @param controlType 权限类型
     * @return
     */
    Boolean updateSceneControl(Long sceneId, Long userId, ControlTypeEnum controlType);

    /**
     * 去除助手的一个用户权限
     *
     * @param sceneId 助手id
     * @param userId  用户id
     * @return
     */
    Boolean deleteSceneUserControl(Long sceneId, Long userId);

    /**
     * 获取user的拥有权限的助手信息
     *
     * @param userId      用户id
     * @param controlType 权限类型
     * @return
     */
    List<UserSceneRecordsDO> getControlInfoByUser(Long userId, ControlTypeEnum controlType);

    /**
     * 获取user可以查看的所有权限助手
     *
     * @param userId      用户id
     * @return
     */
    List<UserSceneRecordsDO> getAllControlInfoByUser(Long userId);

    /**
     * 获取当前用户一个助手的权限信息
     *
     * @param userId      用户id
     * @param sceneId 	助手id
     * @return
     */
    UserSceneRecordsDO getUserPermissionInfo(Long userId, Long sceneId);

    /**
     * 把助手的可见/可编辑用户的历史数据全部删除，然后用新的工号数据覆盖
     * @param sceneId
     * @param controlTypeEnum
     * @param empIdList
     */
    void overrideSceneUserControl(Long sceneId, ControlTypeEnum controlTypeEnum, List<String> empIdList);
}
