package com.alipay.codegencore.model.alg;

import java.util.BitSet;
import java.util.List;
import java.util.Set;

/**
 * ab实验模型
 *
 * <AUTHOR>
 * 创建时间 2021-12-27
 */
public class AbTestDataModel {
    /**
     * 业务场景key
     */
    private String sceneKey;
    /**
     * 默认兜底值
     */
    private String defaultValue;
    /**
     * 实验版本值
     */
    private List<ExpVerModel> expVerModelList;


    public String getSceneKey() {
        return sceneKey;
    }

    public void setSceneKey(String sceneKey) {
        this.sceneKey = sceneKey;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public List<ExpVerModel> getExpVerModelList() {
        return expVerModelList;
    }

    public void setExpVerModelList(List<ExpVerModel> expVerModelList) {
        this.expVerModelList = expVerModelList;
    }

    /**
     * 实验版本对象
     */
    public class ExpVerModel {
        /**
         * 实验版本值
         */
        private String value;
        /**
         * 实验版本桶
         */
        private BitSet bitSet;
        /**
         * 白名单
         */
        private Set<String> whiteSet;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public Set<String> getWhiteSet() {
            return whiteSet;
        }

        public void setWhiteSet(Set<String> whiteSet) {
            this.whiteSet = whiteSet;
        }

        public BitSet getBitSet() {
            return bitSet;
        }

        public void setBitSet(BitSet bitSet) {
            this.bitSet = bitSet;
        }
    }

}
