/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;


import com.alipay.codegencore.model.model.links.Enum.GptMessageTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;



/**
 * <AUTHOR>
 * @version $Id: GptMessageDO.java, v 0.1 2023-05-29 16:30 tanzhigang Exp $$
 */
public class GptMessageModel extends BaseModel {

    /**
     * 会话id
     */
    private String conversationId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 消息类型
     */
    private GptMessageTypeEnum type;

    /**
     * 消息内容
     */
    private GptMessageContent content;

    /**
     * 多数据源消息内容
     */
    private List<GptMessageContent> contents;
    /**
     * 回复id
     */
    private String replayMessageId;

    /**
     * gpt模型
     */
    private String gptModel;
    /**
     * 消息id
     */
    private String localId;
    /**
     * 扩展信息
     */
    private GptMessageExtInfo extInfo;

    /**
     * 是否发送过
     */
    private Boolean hasBeenSend = false;

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public GptMessageTypeEnum getType() {
        return type;
    }

    public void setType(GptMessageTypeEnum type) {
        this.type = type;
    }

    public GptMessageContent getContent() {
        return content;
    }

    public void setContent(GptMessageContent content) {
        this.content = content;
    }

    public List<GptMessageContent> getContents() {
        return contents;
    }

    public void setContents(List<GptMessageContent> contents) {
        this.contents = contents;
    }

    public String getReplayMessageId() {
        return replayMessageId;
    }

    public void setReplayMessageId(String replayMessageId) {
        this.replayMessageId = replayMessageId;
    }

    public String getGptModel() {
        return gptModel;
    }

    public void setGptModel(String gptModel) {
        this.gptModel = gptModel;
    }

    public String getLocalId() {
        return localId;
    }

    public void setLocalId(String localId) {
        this.localId = localId;
    }

    public GptMessageExtInfo getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(GptMessageExtInfo extInfo) {
        this.extInfo = extInfo;
    }

    public Boolean getHasBeenSend() {
        return hasBeenSend;
    }

    public void setHasBeenSend(Boolean hasBeenSend) {
        this.hasBeenSend = hasBeenSend;
    }
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01 
     * @return java.lang.String
     */
    public String getText() {
        return content!= null ?content.getText(): null ;
    }

    /**
     * 获取助手消息id
     *
     * @return 助手消息id
     */
    public String getCopilotMessageId() {
        return content != null && content.getCopilotAnswer() != null ? content.getCopilotAnswer().getId() : null;
    }

    /**
     * 设置发送人id(暂用于 混合模式 AI助手群聊)
     *
     * @param userId 用户ID
     */
    public void setOriginalSenderId(String userId){
        if(StringUtils.isBlank(userId)){
            return;
        }
        GptMessageExtInfo extInfo = defaultIfNull(this.extInfo, new GptMessageExtInfo());
        extInfo.setOriginalSenderId(userId);
        this.extInfo = extInfo;
    }
    /**
     * 如果对象为null，返回默认值
     *
     * @param obj
     * @param defaultValue
     * @param <T>
     * @return
     */
    public static <T> T defaultIfNull(T obj, T defaultValue) {
        if (obj != null) {
            return obj;
        }
        return defaultValue;
    }
}