package com.alipay.codegencore.model.openai;

import com.alipay.codegencore.model.enums.PlanStepType;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0 2024/8/15 14:49
 */
public class PlanFile {

    private String type = PlanStepType.M.name();

    @NotBlank(message = "filePath不能为空")
    private String filePath;

    @NotEmpty(message = "步骤列表不能为空")
    private List<String> step;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public List<String> getStep() {
        return step;
    }

    public void setStep(List<String> step) {
        this.step = step;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", PlanFile.class.getSimpleName() + "[", "]")
                .add("type='" + type + "'")
                .add("filePath='" + filePath + "'")
                .add("step=" + step)
                .toString();
    }
}
