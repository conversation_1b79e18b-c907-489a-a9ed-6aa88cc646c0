package com.alipay.codegencore.model.enums;

/**
 * 权限申请状态
 *
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.model.enums
 * @CreateTime : 2023-07-20
 */
public enum AuditStatusSceneEnum {
    /**
     * 未申请
     */
    NONE(0),
    /**
     * 审核中
     */
    RUNNING(1),
    /**
     * 已通过
     */
    PAAS(2),
    /**
     * 已拒绝
     */
    REFUSE(3);

    private int code;

    AuditStatusSceneEnum(int code) {
        this.code = code;
    }

    AuditStatusSceneEnum getByCode(int code) {
        for (AuditStatusSceneEnum e : AuditStatusSceneEnum.values()) {
            if (e.code == code) {
                return e;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }
}