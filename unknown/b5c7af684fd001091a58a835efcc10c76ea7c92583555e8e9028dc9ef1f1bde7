package com.alipay.codegencore.utils;

import com.alipay.codegencore.model.enums.RecordLogEnum;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

public class CollectLogUtilsTest {

    /**
     * 方法说明：打印收集日志测试方法1
     */
    @Test
    public void printCollectLogTest1() {
        // 创建一个键值对映射，键为字符串类型 "test"，值为整数类型 0
        Map<String, Object> content = new HashMap<>();

        content.put("test", "test");
        // 调用 CollectLogUtils 工具类中的打印采集日志方法，传入 RecordLogEnum 枚举类型和上述创建的内容映射
        CollectLogUtils.printCollectLog(RecordLogEnum.API_ACCESS, content);

    }

    /**
     * 方法说明：打印收集日志测试方法2
     */
    @Test
    public void printCollectLogTest2() {
        // 创建一个键值对映射，键为字符串类型，值为Object类型
        Map<String, Object> content = new HashMap<>();

        // 向映射中添加键值对"test":"test"
        content.put("test", "test");
        // 调用PrintCollectLog方法，传入参数RecordLogEnum.API_ACCESS.name和content作为参数
        CollectLogUtils.printCollectLog(RecordLogEnum.API_ACCESS.name, content);
    }
}
