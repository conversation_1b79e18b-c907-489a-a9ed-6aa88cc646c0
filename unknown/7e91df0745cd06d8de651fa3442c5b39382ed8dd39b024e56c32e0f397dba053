/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.List;

/**
 * <AUTHOR>
 * @version PlatformToolResponse.java, v 0.1 2023年11月23日 下午7:14 wb-tzg858080
 */
public class PlatformToolResponse extends ToString {
    /**
     * 类型
     */
    private String type ;

    /**
     * 诊断数据
     */
    private List<PlatformToolInfo> info ;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<PlatformToolInfo> getInfo() {
        return info;
    }

    public void setInfo(List<PlatformToolInfo> info) {
        this.info = info;
    }
}
