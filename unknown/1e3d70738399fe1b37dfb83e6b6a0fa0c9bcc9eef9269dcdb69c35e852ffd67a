package com.alipay.codegencore.utils.adapter;


import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ConnectException;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * HttpAdapter工具类
 */
public class HttpAdapter {

    private static final Logger log = LoggerFactory.getLogger(HttpAdapter.class);

    /**
     * 发请求
     *
     * @param method  get/post
     * @param url     请求地址
     * @param args    参数
     * @param headers headers
     * @param charset 这个一般情况传null,涉及到解析中文内容的时候可能需要传具体的编码
     * @return response
     */
    public static HttpAdapterResponse request(String method, String url, Map<String, Object> args, Map<String, Object> headers, String charset) {
        log.info("request: method={} url={}", method, url, JSON.toJSONString(args), JSON.toJSONString(headers));
        log.debug("request args={}, headers={}", JSON.toJSONString(args), JSON.toJSONString(headers));
        HttpAdapterResponse httpAdapterResponse = new HttpAdapterResponse();
        HttpResponse httpResponse;
        try {
            if (method.equalsIgnoreCase("GET")) {
                httpResponse = doGetRequest(url, args, headers);
            } else if (method.equalsIgnoreCase("POST")) {
                httpResponse = doPostRequest(url, args, headers);
            } else {
                throw new RuntimeException("unknown request method:" + method);
            }
            //设置结果
            httpAdapterResponse.setStatusCode(httpResponse.getStatusLine().getStatusCode());
            if (httpAdapterResponse.getStatusCode() == 200 || httpAdapterResponse.getStatusCode() == 201 || httpAdapterResponse.getStatusCode() == 400) {
                String strHttpResult;
                if (StringUtils.isBlank(charset)) {
                    strHttpResult = EntityUtils.toString(httpResponse.getEntity());
                } else {
                    strHttpResult = EntityUtils.toString(httpResponse.getEntity(), charset);
                }
                httpAdapterResponse.setRetContent(strHttpResult);
            } else {
                log.warn("httpAdapter request failed with status {}", httpAdapterResponse.getStatusCode());
            }
        } catch (ConnectException e) {
            log.warn("httpAdapter connect failed with ConnectException {}", e);
            httpAdapterResponse.setRetContent("connect fail : " + e.getMessage());
        } catch (IOException e) {
            log.warn("httpAdapter request failed with IOException {}", e);
        }
        return httpAdapterResponse;
    }


    /**
     * doPostRequest
     *
     * @param url
     * @param args
     * @param headers
     * @return
     */
    private static HttpResponse doPostRequest(String url, Map<String, Object> args, Map<String, Object> headers) {
        try {
            HttpClient httpClient = new DefaultHttpClient();
            HttpPost request = new HttpPost(url);
            if (headers != null) {
                for (String key : headers.keySet()) {
                    request.addHeader(key, headers.get(key).toString());
                }
            }
            request.getParams().setParameter("http.socket.timeout", 300000);
            request.getParams().setParameter("http.connection.timeout", 5000);
            if (args != null) {
                List<NameValuePair> urlParameters = new ArrayList<NameValuePair>();
                for (String argName : args.keySet()) {
                    urlParameters.add(new BasicNameValuePair(argName, args.get(argName).toString()));
                }
                request.setEntity(new UrlEncodedFormEntity(urlParameters, "UTF-8"));
            }
            return httpClient.execute(request);
        } catch (IOException e) {
            log.warn("httpAdapter request failed with IOException", e);
        }
        return null;
    }

    private static HttpResponse doGetRequest(String url, Map<String, Object> args, Map<String, Object> headers) {
        try {
            HttpClient httpClient = new DefaultHttpClient();
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setCharset(Charset.forName("Utf-8"));
            if (args != null) {
                for (String name : args.keySet()) {
                    uriBuilder.addParameter(name, args.get(name).toString());
                }
            }
            String completeUrl = uriBuilder.build().toString();
            log.debug("complete get url: {}", completeUrl);
            HttpGet request = new HttpGet(completeUrl);
            if (headers != null) {
                for (String key : headers.keySet()) {
                    request.addHeader(key, headers.get(key).toString());
                }
            }
            request.getParams().setParameter("http.socket.timeout", 300000);
            request.getParams().setParameter("http.connection.timeout", 5000);
            return httpClient.execute(request);
        } catch (URISyntaxException e) {
            log.error(String.format("error uri:%s", url), e);
            throw new RuntimeException("error uri:" + e);
        } catch (IOException e) {
            log.warn("httpAdapter request failed with IOException", e);
        }
        return null;
    }


}
