package com.alipay.codegencore.service.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.OssService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * OSS服务接口实现类
 */
@Service
public class OssServiceImpl implements OssService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OssServiceImpl.class);

    private String bucketName = null;
    private String endpoint = null;
    private OSS ossClient;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private ConfigService configService;

    /**
     * OSS 初始化
     */
    @PostConstruct
    public void init() {
        JSONObject ossConfigJson = JSON.parseObject(codeGPTDrmConfig.getOssConfig());
        bucketName = ossConfigJson.getString("bucketName");
        endpoint = ossConfigJson.getString("endpoint");
        String ossAccessKey = configService.getConfigByKey(AppConstants.OSS_ACCESS_KEY, false);
        String ossSecretKey = configService.getConfigByKey(AppConstants.OSS_SECRET_KEY, false);
        Assert.isTrue(bucketName != null && endpoint != null && ossAccessKey != null && ossSecretKey != null, "oss未在DRM中配置完成");
        ossClient = new OSSClientBuilder().build(endpoint, ossAccessKey, ossSecretKey);
    }

    @Override
    public String putObject(String filePath, InputStream inputStream, Integer expireDays, String contentType) {
        try {
            long startTime = System.currentTimeMillis();
            ObjectMetadata meta = new ObjectMetadata();
            if (StringUtils.isNotBlank(contentType)) {
                meta.setContentType(contentType);
            }
            if (expireDays != null) {
                meta.setContentLength(inputStream.available());
                meta.setLastModified(new Date());
                Date now = new Date();
                Date expireDate = DateUtils.addDays(now, expireDays);
                meta.setExpirationTime(expireDate);
            }else {
                // 默认设置过期时间 99年
                meta.setExpirationTime(new Date(System.currentTimeMillis() + 1000L * 60 * 60 * 24 * 365 * 99));
            }
            ossClient.putObject(bucketName, filePath, inputStream, meta);
            String url = getSignedUrl(filePath);
            LOGGER.info("request oss putObject cost time:{}ms,fileName:{},url:{}", System.currentTimeMillis() - startTime, filePath, url);
            return url;
        } catch (Exception e) {
            LOGGER.error("putObject error,fileName=" + filePath, e);
            throw new BizException(ResponseEnum.OSS_OPERATION_FAILED);
        }
    }

    @Override
    public String getString(String filePath) {
        long startTime = System.currentTimeMillis();
        OSSObject object = getObject(filePath);
        if (object == null) {
            return null;
        }
        long copyStartTime = System.currentTimeMillis();
        try {
            byte[] content = object.getObjectContent().readAllBytes();
            long endTime = System.currentTimeMillis();
            LOGGER.info("request oss getString:{}ms,getObject:{},IOUtils.copy:{}ms,fileName:{}", endTime - startTime, copyStartTime - startTime, endTime - copyStartTime, filePath);
            return new String(content);
        } catch (IOException e) {
            LOGGER.error("read oss error", e);
            throw new BizException(ResponseEnum.OSS_OPERATION_FAILED);
        }
    }

    @Override
    public InputStream getInputStream(String filePath) {
        long startTime = System.currentTimeMillis();
        OSSObject object = getObject(filePath);
        if (object == null) {
            return null;
        }
        long copyStartTime = System.currentTimeMillis();
        InputStream inputStream = object.getObjectContent();
        long endTime = System.currentTimeMillis();
        LOGGER.info("request oss getInputStream:{}ms,getObject:{},IOUtils.copy:{}ms,fileName:{}", endTime - startTime, copyStartTime - startTime, endTime - copyStartTime, filePath);
        return inputStream;
    }

    @Override
    public void deleteFile(String fileName) {
        ossClient.deleteObject(bucketName, fileName);
    }

    @Override
    public Boolean isExist(String fileName) {
        return ossClient.doesObjectExist(bucketName, fileName);
    }

    //获取签名之后的 URL
    private String getSignedUrl(String fileName) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            URL signUrl = ossClient.generatePresignedUrl(bucketName, fileName, sdf.parse("2099-12-31 00:00:00"));
            // 替换为安全 https
            return StringUtil.replace(signUrl.toString(), "http://", "https://");
        } catch (ParseException e) {
            throw new BizException(ResponseEnum.OSS_OPERATION_FAILED);
        }
    }


    private OSSObject getObject(String fileName) {
        OSSObject object = null;
        try {
            object = ossClient.getObject(new GetObjectRequest(bucketName, fileName));
        } catch (OSSException e) {
            if ("NoSuchKey".equals(e.getErrorCode())) {
                return null;
            }
        }
        return object;
    }
}
