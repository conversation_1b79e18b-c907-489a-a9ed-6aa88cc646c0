package com.alipay.codegencore.model.model.tool.learning;

import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.response.PluginStreamPartResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * 方法调用流式数据缓存
 * <AUTHOR>
 */
public class FunctionCallStreamBuffer {

    private Map<Integer,OneRoundFunctionCallBuffer> functionCallBuffer;

    private StringBuilder finalResultBuffer;

    public FunctionCallStreamBuffer() {
        functionCallBuffer = new HashMap<>();
        finalResultBuffer = new StringBuilder();
    }

    /**
     * 缓存方法调用流式数据
     * @param pluginStreamPartResponse
     */
    public void buffer(PluginStreamPartResponse pluginStreamPartResponse){
        if(pluginStreamPartResponse.getIndex() == -1){
            finalResultBuffer.append(pluginStreamPartResponse.getContent());
        }else{
            int index = pluginStreamPartResponse.getIndex();
            OneRoundFunctionCallBuffer oneRoundFunctionCallBuffer = functionCallBuffer.get(index);
            if(oneRoundFunctionCallBuffer == null){
                oneRoundFunctionCallBuffer = new OneRoundFunctionCallBuffer(pluginStreamPartResponse.getPluginInfo());
                functionCallBuffer.put(index,oneRoundFunctionCallBuffer);
            }
            oneRoundFunctionCallBuffer.buffer(pluginStreamPartResponse);
        }
    }

    public Map<Integer, OneRoundFunctionCallBuffer> getFunctionCallBuffer() {
        return functionCallBuffer;
    }

    public void setFunctionCallBuffer(Map<Integer, OneRoundFunctionCallBuffer> functionCallBuffer) {
        this.functionCallBuffer = functionCallBuffer;
    }

    public StringBuilder getFinalResultBuffer() {
        return finalResultBuffer;
    }

    public void setFinalResultBuffer(StringBuilder finalResultBuffer) {
        this.finalResultBuffer = finalResultBuffer;
    }

    public static class OneRoundFunctionCallBuffer {
        private ChatFunctionCall chatFunctionCall;
        private PluginStreamBuffer pluginStreamBuffer;

        public OneRoundFunctionCallBuffer() {
            chatFunctionCall = new ChatFunctionCall();
            pluginStreamBuffer = new PluginStreamBuffer();
        }

        public OneRoundFunctionCallBuffer(PluginInfo pluginInfo) {
            chatFunctionCall = new ChatFunctionCall();
            pluginStreamBuffer = new PluginStreamBuffer(pluginInfo);
        }

        public void buffer(PluginStreamPartResponse pluginStreamPartResponse){
            if(pluginStreamPartResponse.getIndex() != -1){
                if(pluginStreamPartResponse.getChatFunctionCall() != null){
                    chatFunctionCall = pluginStreamPartResponse.getChatFunctionCall();
                }else{
                    pluginStreamBuffer.buffer(pluginStreamPartResponse);
                }
            }
        }

        public ChatFunctionCall getChatFunctionCall() {
            return chatFunctionCall;
        }

        public void setChatFunctionCall(ChatFunctionCall chatFunctionCall) {
            this.chatFunctionCall = chatFunctionCall;
        }

        public PluginStreamBuffer getPluginStreamBuffer() {
            return pluginStreamBuffer;
        }

        public void setPluginStreamBuffer(PluginStreamBuffer pluginStreamBuffer) {
            this.pluginStreamBuffer = pluginStreamBuffer;
        }
    }
}
