package com.alipay.codegencore.service.common;

import com.alipay.codegencore.model.enums.EnvEnum;
import com.alipay.codegencore.model.model.EmbeddingResponseModel;
import com.alipay.codegencore.model.request.ZarkEmbeddingRequestBean;

import java.math.BigDecimal;
import java.util.List;

/**
 * 该接口定义了Zark服务提供的两个方法：splitText和embedding。
 */
public interface ZarkService {

    /**
     * embedding接口
     * @param zarkEmbeddingRequestBean
     */
    List<List<BigDecimal>> embedding(ZarkEmbeddingRequestBean zarkEmbeddingRequestBean);

    /**
     * embedding 一个字符串list
     * @param strList
     * @param embeddingModel
     * @return
     */
    List<EmbeddingResponseModel> embeddingStrList(List<String> strList, String embeddingModel);

    /**
     * 包装zark调用的通用方法
     * @param uri
     * @param env
     * @param requestBody
     * @return
     */
    Object requestCommonZarkUrl(String uri, EnvEnum env, Object requestBody);

}
