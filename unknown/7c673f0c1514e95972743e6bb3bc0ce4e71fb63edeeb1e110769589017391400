package com.alipay.codegencore.service.answer;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.AnswerIndexBuildJobDO;
import com.alipay.codegencore.model.domain.AnswerIndexBuildTaskDO;
import com.alipay.codegencore.model.enums.answer.JobState;
import com.alipay.codegencore.model.response.answer.IndexBuildResponse;
import com.alipay.codegencore.model.response.answer.JobPullResponse;
import com.alipay.codegencore.utils.code.AntCodeClient;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;

/**
 * 问答索引构建服务
 */
public interface AnswerIndexService {

    /**
     * 索引构建
     * @param commitInfo
     * @param repoURL
     * @return
     */
    IndexBuildResponse indexBuild(AntCodeClient.CommitInfo commitInfo, String repoURL, Integer priority);

    /**
     * 拉取job列表
     * 评估后,job队列暂不使用zcache队列,因为索引构建服务本身有限,所以不会有太高的并发,且查询都是走索引的简单查询
     * @param size
     * @return
     */
    List<JobPullResponse> pullJobs(String flag, int size, String handleInfo);

    /**
     * 获取job结果,并更新对应状态
     * @param jobId
     * @param taskId
     * @return
     */
    boolean jobResult(Long jobId, Long taskId, Integer status, String message);


    /**
     * 仓库构建判断
     * 1、仓库没有构建记录：false
     * 2、仓库有构建记录，但是状态是INIT：false
     * 3、其他情况返回 true
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    boolean repoBuildState(String groupPath, String projectPath, String branch);

    /**
     * 获取任务(id、状态、commit)
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    AnswerIndexBuildTaskDO getTask(String groupPath, String projectPath, String branch);

    /**
     * 仓库变更处理
     * @param pushEventObject
     */
    void repoChangeHandle(JSONObject pushEventObject);

    /**
     * 仓库校验
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    AntCodeClient.CommitInfo repoCheck(String groupPath, String projectPath, String branch);

    /**
     * 查询问题列表
     * @param groupPath
     * @param projectPath
     * @param branch
     * @return
     */
    List<String> getQuestion(String groupPath, String projectPath, String branch);

    /**
     * 索引构建回调
     * @param callbackData
     */
    void indexBuildCallback(JSONObject callbackData);


    /**
     * wiki 任务回调
     * @param callbackData
     */
    void wikiBuildCallback(JSONObject callbackData);

    /**
     * 获取需要重跑的任务id
     *
     * <AUTHOR>
     * @since 2024.08.02
     * @param taskId taskId
     * @return java.util.List<java.lang.Long>
     */
    List<Long> getRetryJobsByTask(Long taskId);

    /**
     * 重跑任务
     *
     * <AUTHOR>
     * @since 2024.08.02
     * @param jobIds jobIds
     * @param taskId taskId
     */
    void retryJobs(List<Long> jobIds, Long taskId, JobState targetState);

    /**
     * 获取job列表
     * <AUTHOR>
     * @since 2024.08.02
     * @param jobIds jobIds
     * @return java.util.List<com.alipay.codegencore.model.domain.AnswerIndexBuildJobDO>
     */
    List<AnswerIndexBuildJobDO> getJobByIds(List<Long> jobIds);


    /**
     * 重置失败的 job
     * @param taskIds
     * @param failMessage
     */
    List<Triple<Long, Integer, Long>> resetFailJobHandle(List<Long> taskIds, String failMessage);


    /**
     * 查询仓库索引状态
     * L：索引任务 id
     * R：索引任务状态
     * @param repoPath
     * @param branch
     * @return
     */
    Pair<Long, String> getIndexStatus(String repoPath, String branch);

    /**
     * 根据id和时间查询任务
     * @param id
     * @param startTime
     * @param endTime
     * @return
     */
    List<AnswerIndexBuildTaskDO> getTaskByIdAndTime(Long id, String startTime, String endTime);

    /**
     * 定时任务状态实现
     */
    List<String> checkAndNotifyZSearchStatus(Long id, String startTime, String endTime);


    /**
     * 仓库级索引构建检查
     */
    void repoTaskCheck();

    /**
     * 仓库级索引构建检查
     */
    void repoTaskCheck(String state, Integer limit);

    /**
     * 查询构建任务进度
     * @param repoURL
     * @param branch
     * @return
     */
    JSONObject queryBuildProgress(String repoURL, String branch);

    /**
     * 重构建
     * @param commitInfo
     * @return
     */
    void rebuild(AntCodeClient.CommitInfo commitInfo);
}
