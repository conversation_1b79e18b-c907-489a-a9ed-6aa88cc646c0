package com.alipay.codegencore.model.remote;

import com.alipay.codegencore.model.domain.SceneDO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:20
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgentObject {

    @JsonProperty(value = "id", required = true)
    private String id;

    @JsonProperty(value = "name", required = true)
    private String name;

    @JsonProperty(value = "description", required = true)
    private String description;

    @JsonProperty(value = "metadata")
    private Map<String, Object> metadata;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }


    /**
     * 模型转换
     * @param sceneDO
     * @return
     */
    public static AgentObject of(SceneDO sceneDO) {
        AgentObject agentObject = new AgentObject();
        agentObject.setId("" + sceneDO.getId());
        agentObject.setName(sceneDO.getName());
        agentObject.setDescription(sceneDO.getDescription());
        return agentObject;
    }
}
