package com.alipay.codegencore.web.remote;

import com.alipay.codegencore.model.contant.WebApiContents;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.remote.SessionObject;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.codegencore.web.remote.vo.RemoteAgentBaseResponse;
import com.alipay.codegencore.web.remote.vo.SessionCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 11:34
 */
@Slf4j
@RestController
@RequestMapping("/v1/sessions")
public class SessionController {


    @Autowired
    private ChatSessionManageService chatSessionManageService;

    @Autowired
    private UserAclService userAclService;

    /**
     * 创建会话
     * 创建 Session
     * 使用场景
     * 创建一个会话，并添加初始会话消息
     * @param sessionCreateRequest
     * @return
     */
    @PostMapping()
    public RemoteAgentBaseResponse<SessionObject> createSession(@RequestBody(required = false) SessionCreateRequest sessionCreateRequest) {

        Long userId = userAclService.getCurrentUser().getId();
        String sourcePlatform = ContextUtil.get(WebApiContents.TOKEN_USER, String.class);
        log.info("create session userId:{}, sourcePlatform:{}", userId, sourcePlatform);
        ChatSessionDO chatSessionDO = chatSessionManageService.getNewSession(userId, null,
                null, null, false, sourcePlatform, null,false);
        if (chatSessionDO == null) {
            return RemoteAgentBaseResponse.buildFail(ResponseEnum.SESSION_CREATE_FAIL);
        }
        log.info("create session success :{}", chatSessionDO.getUid());
        return RemoteAgentBaseResponse.buildSuccess(SessionObject.of(chatSessionDO));
    }

    /**
     * 删除会话
     * 删除 Session
     * 请求参数
     * ● Session Id（必须）
     * 返回参数
     * ● 是否删除成功（必须）
     * 使用场景
     * 删除创建的会话
     * @param sessionId
     * @return
     */
    @DeleteMapping("/{session_id}")
    public RemoteAgentBaseResponse deleteSession(@PathVariable("session_id") String sessionId) {

        if (!userAclService.isSessionBelongToUser(sessionId)) {
            log.info("session:{} does not belong to current user", sessionId);
            return RemoteAgentBaseResponse.buildFail(ResponseEnum.USER_SESSION_NOT_MATCH);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("id", sessionId);
        boolean deleted = true;
        try {
            chatSessionManageService.deleteSession(List.of(sessionId));
        } catch (Exception e) {
            log.error("delete session error", e);
            deleted = false;
        }
        map.put("deleted", deleted);
        return RemoteAgentBaseResponse.buildSuccess(map);
    }

}
