/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 * @version $Id: ReplaysTaskStatus.java, v 0.1 2021-09-24 16:39 wb-tzg858080 Exp $$
 */
public enum ReplaysTaskStatus {
    /**
     * 进行中
     */
    DOING("进行中"),
    /**
     * 失败
     */
    ERR("失败"),
    /**
     * 成功
     */
    SUCCESS("成功");

    /**
     * 枚举内容
     */
    private String content;

    ReplaysTaskStatus(String content){
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    /**
     * 通过name获取对应枚举值
     * @param name
     * @return
     */
    public static ReplaysTaskStatus getStatusByName(String name){
        if(StringUtils.isBlank(name)){
            return null;
        }
        for(ReplaysTaskStatus status : values()) {
            if(status.name().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
