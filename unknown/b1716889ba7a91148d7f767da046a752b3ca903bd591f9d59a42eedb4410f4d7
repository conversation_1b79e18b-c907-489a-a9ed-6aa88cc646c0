package com.alipay.codegencore.model.domain;

import java.math.BigDecimal;
import java.util.Date;

public class TokenDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.id
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.gmt_create
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.gmt_modified
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.user
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private String user;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private String token;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.uri_pattern_list
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private String uriPatternList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.description
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.balance
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private BigDecimal balance;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.owner_user_id
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private String ownerUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.enable_status
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private Byte enableStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_token.app_name
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    private String appName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.id
     *
     * @return the value of cg_token.id
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.id
     *
     * @param id the value for cg_token.id
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.gmt_create
     *
     * @return the value of cg_token.gmt_create
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.gmt_create
     *
     * @param gmtCreate the value for cg_token.gmt_create
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.gmt_modified
     *
     * @return the value of cg_token.gmt_modified
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.gmt_modified
     *
     * @param gmtModified the value for cg_token.gmt_modified
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.user
     *
     * @return the value of cg_token.user
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public String getUser() {
        return user;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.user
     *
     * @param user the value for cg_token.user
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setUser(String user) {
        this.user = user;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.token
     *
     * @return the value of cg_token.token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public String getToken() {
        return token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.token
     *
     * @param token the value for cg_token.token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setToken(String token) {
        this.token = token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.uri_pattern_list
     *
     * @return the value of cg_token.uri_pattern_list
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public String getUriPatternList() {
        return uriPatternList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.uri_pattern_list
     *
     * @param uriPatternList the value for cg_token.uri_pattern_list
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setUriPatternList(String uriPatternList) {
        this.uriPatternList = uriPatternList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.description
     *
     * @return the value of cg_token.description
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.description
     *
     * @param description the value for cg_token.description
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.balance
     *
     * @return the value of cg_token.balance
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public BigDecimal getBalance() {
        return balance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.balance
     *
     * @param balance the value for cg_token.balance
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.owner_user_id
     *
     * @return the value of cg_token.owner_user_id
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public String getOwnerUserId() {
        return ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.owner_user_id
     *
     * @param ownerUserId the value for cg_token.owner_user_id
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setOwnerUserId(String ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.enable_status
     *
     * @return the value of cg_token.enable_status
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public Byte getEnableStatus() {
        return enableStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.enable_status
     *
     * @param enableStatus the value for cg_token.enable_status
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setEnableStatus(Byte enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_token.app_name
     *
     * @return the value of cg_token.app_name
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public String getAppName() {
        return appName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_token.app_name
     *
     * @param appName the value for cg_token.app_name
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    public void setAppName(String appName) {
        this.appName = appName;
    }
}