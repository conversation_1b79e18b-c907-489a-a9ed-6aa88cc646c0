package com.alipay.codegencore.web.common;

import com.alipay.codegencore.dal.example.ConfigDOExample;
import com.alipay.codegencore.dal.mapper.ConfigDOMapper;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.ConfigDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.UserAclService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 配置管理controller
 *
 * <AUTHOR>
 * 创建时间 2022-02-28
 */
@RestController
@CodeTalkWebApi
@RequestMapping("/admin/config")
@Slf4j
public class ConfigController {
    @Resource
    private ConfigDOMapper configDOMapper;

    @Resource
    private UserAclService userAclService;

    /**
     * 创建配置
     *
     * @param configDO
     * @return
     */
    @PostMapping(value = "/create")
    public BaseResponse<ConfigDO> create(@RequestBody ConfigDO configDO) {
        if(!userAclService.isAdmin()){
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }
        configDOMapper.insert(configDO);
        return BaseResponse.build(configDO);
    }

    /**
     * 获取所有配置
     *
     * @return
     */
    @GetMapping(value = "/list")
    public BaseResponse<List<ConfigDO>> getAllConfig() {
        if(!userAclService.isAdmin()){
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }

        ConfigDOExample configDOExample = new ConfigDOExample();
        ConfigDOExample.Criteria criteria = configDOExample.createCriteria();
        criteria.andIdIsNotNull();
        return BaseResponse.build(configDOMapper.selectByExample(configDOExample));
    }

    /**
     * 更新配置
     *
     * @param configDO 用于更新的配置，name字段不可为空
     * @return
     */
    @PostMapping(value = "/update")
    public BaseResponse update(@RequestBody ConfigDO configDO) {
        if(!userAclService.isAdmin()){
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }

        if (configDO.getName() == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, "config name is null!");
        }
        ConfigDOExample configDOExample = new ConfigDOExample();
        ConfigDOExample.Criteria criteria = configDOExample.createCriteria();
        criteria.andNameEqualTo(configDO.getName());
        configDOMapper.updateByExampleSelective(configDO, configDOExample);
        return BaseResponse.buildSuccess();
    }

    /**
     * 根据键获取配置
     *
     * @param key 配置的键
     * @return
     */
    @GetMapping(value = "/getConfigByName")
    public BaseResponse<ConfigDO> getConfigByKey(@RequestParam() String key) {
        if(!userAclService.isAdmin()){
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }

        ConfigDOExample configDOExample = new ConfigDOExample();
        ConfigDOExample.Criteria criteria = configDOExample.createCriteria();
        criteria.andNameEqualTo(key);
        List<ConfigDO> configDOList = configDOMapper.selectByExample(configDOExample);
        return BaseResponse.build(configDOList.get(0));
    }

    /**
     * 根据配置键删除配置
     * @param key
     * @return
     */
    @DeleteMapping(value = "/deleteByName")
    public BaseResponse deleteByName(@RequestParam() String key) {
        if(!userAclService.isAdmin()){
            return BaseResponse.build(ResponseEnum.NO_AUTH, "admin permission required");
        }

        ConfigDOExample configDOExample = new ConfigDOExample();
        ConfigDOExample.Criteria criteria = configDOExample.createCriteria();
        criteria.andNameEqualTo(key);
        configDOMapper.deleteByExample(configDOExample);
        return BaseResponse.buildSuccess();
    }

}
