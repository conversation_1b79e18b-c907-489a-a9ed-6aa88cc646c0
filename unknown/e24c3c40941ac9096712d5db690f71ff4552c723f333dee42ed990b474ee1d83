/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;


import java.util.List;

/**
 * <AUTHOR>
 * @version IndustryToolConfigInfo.java, v 0.1 2023年06月05日 下午5:49 admin
 */
public class IndustryToolConfigInfo extends ToString {
    /**
     * 行业工具-code
     */
    private String code;

    /**
     * 行业工具-url
     */
    private String url;

    /**
     * 行业工具-needPid
     */
    private Boolean needPid;

    /**
     * 子级工具
     */
    private List<IndustryGeneratorJumpUrlInfo> urlTools;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Boolean getNeedPid() {
        return needPid;
    }

    public void setNeedPid(Boolean needPid) {
        this.needPid = needPid;
    }

    public List<IndustryGeneratorJumpUrlInfo> getUrlTools() {
        return urlTools;
    }

    public void setUrlTools(List<IndustryGeneratorJumpUrlInfo> urlTools) {
        this.urlTools = urlTools;
    }

    public Boolean getCommonTool() {
        return isCommonTool;
    }

    public void setCommonTool(Boolean commonTool) {
        isCommonTool = commonTool;
    }

    public String getToolPath() {
        return toolPath;
    }

    public void setToolPath(String toolPath) {
        this.toolPath = toolPath;
    }

    /**
     * 是否通用工具，是的话的需要通过toolPath来打开工具详情
     */
    private Boolean isCommonTool;

    /**
     * 工具路由
     */
    private String toolPath;

}
