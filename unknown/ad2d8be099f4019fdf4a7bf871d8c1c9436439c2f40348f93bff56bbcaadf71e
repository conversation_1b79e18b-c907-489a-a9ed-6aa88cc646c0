# spring output
dbmode=dev
spring.output.ansi.enabled=ALWAYS
# log url
logging.path=./logs

# test-url activice(only dev)
run_mode=TEST
# AntScheduler
com.alipay.sofa.antscheduler.enableMultiConnections=true


# zdal config
app_data_source_name=codegencore_ds
app_oss_data_source_name=codegencore_oss_ds
zdal_version=ERA10060602
logging.level.com.alipay.codegencore.dal.mapper=debug
logging.level.com.alipay.codegencore.service=debug

com.alipay.sofa.dds.config.obproxy-host=codegencore-2.gz00b.stable.alipay.net
# zcache config
zdal_console_url=http://zdataconsole-pool.stable.alipay.net:8080
zdal_tail_name=codegencoreTBaseCache
tbase_time_out=10000

# ???? - java???
alg_generator_line=https://codegencore-pre.alipay.com/api/test/innertest
# ???? - python???
alg_generator_python=https://codegencore-pre.alipay.com/api/test/pytest
# ???? - js/ts???
alg_generator_js=https://codegencore-pre.alipay.com/api/test/jstest
tool_search_url=https://zarkag.antgroup.com/deveffinlp/codegpt_sgchain_service/codegpt_tool_manager
