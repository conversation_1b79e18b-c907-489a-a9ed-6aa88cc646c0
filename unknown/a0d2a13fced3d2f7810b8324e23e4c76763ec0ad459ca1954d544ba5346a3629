/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version ChatGptModelHandler.java, v 0.1 2023年04月21日 16:32 xiaobin
 */
public class CodeGPTModelHandler extends AbstractAlgLanguageHandler {

    private LanguageModelService languageModelService;

    /**
     * 抽象handler构造函数
     *
     */
    public CodeGPTModelHandler() {
        this.languageModelService = SpringUtil.getBean("codeGptLanguageModelService");
    }

    @Override
    public ChatMessage chat(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        return languageModelService.chat(gptAlgModelServiceRequest);
    }

    @Override
    public Flux<String> chatOnStream(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        languageModelService.streamChatForServlet(gptAlgModelServiceRequest);
        return null;
    }

}