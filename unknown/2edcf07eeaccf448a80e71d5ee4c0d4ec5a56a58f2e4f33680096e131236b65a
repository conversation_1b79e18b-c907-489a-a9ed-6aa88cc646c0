/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.util;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.openai.SceneConfigVO;

/**
 * <AUTHOR>
 * @version AlgoBackendFieldUtil.java, v 0.1 2023年09月19日 下午4:30 yhw01352860
 */
public class AlgoBackendFieldUtil {

    /**
     * 默认环境
     */
    public static final String defaultEnv = "auto";
    /**
     * 用户可配置的字段以及字段说明
     * key:字段
     * val:字段说明
     */
    public static final  LinkedHashMap<String,String> fieldTypeMap = new LinkedHashMap<>();
    static {
        fieldTypeMap.put("requestTimeOut","请求maya服务的整体超时时间");
        fieldTypeMap.put("topK","较小的topK值会使生成的文本更准确，较大的topK值则会增加生成文本的多样性。");
        fieldTypeMap.put("topP","概率值，0-1。较小的topP值会使生成的文本更准确但可能较短，较大的topP值则会增加生成文本的长度和多样性。   ");
        fieldTypeMap.put("temperature","温度参数。较小的温度值（如0.1）会使得模型更加确定性地生成高概率的单词，而较大的温度值（如1.0或更高）则会增加生成文本的多样性，使得低概率的单词也有机会被选择。较高的温度值会使得生成文本更加随机，但也可能导致生成低质量的文本。");
        fieldTypeMap.put("batchTimeWindow","批次的窗口时间，单位毫秒。");
        fieldTypeMap.put("batchSize","批次的最大请求数，codefuse会把每batchTimeWindow毫秒内积攒的最长batchSize个请求同时发给模型的一个节点(pod)来推理，可增强模型的吞吐量，是一个用时间换吞吐量的方式。如果不需要这个功能batchSize设置为1即可。");
        fieldTypeMap.put("commonStreamDataWaitTime","间隔时间：codefuse等待算法服务写入第2-N个流(数据包)到tbase的时间和上一个流(数据包)写入成功的最长间隔时间，即：第一个流获取到后，等待第二个流的最长时间；第二个获取到后，等待第三个的最长时间；...第N-1个流获取到之后，等待第N个流的最长等待时间。");
        fieldTypeMap.put("repetitionPenalty","惩罚参数");
        fieldTypeMap.put("outSeqLength","模型输出数据的最长token数");
        fieldTypeMap.put("firstStreamDataWaitTime","首包超时时间：codefuse把请求发给maya之后，算法服务会流式的向tbase写入结果，这个时间是codefuse等待算法服务写入第一个流到tbase中的最长等待时间。");
        fieldTypeMap.put("connTimeout","AntGLM的连接时间");
        fieldTypeMap.put("maxOutputLength","AntGLM的最大输出长度-按需设置");
        fieldTypeMap.put("systemPrompt","模型级别默认的systemPrompt");
    }
    /**
     * 获取用户可操作的模型配置
     * modelConfig：会话配置
     */
    public static List<SceneConfigVO> sceneConfigVOList(AlgoBackendDO algoBackendDO, String sessionModelConfig, String sceneSystemPrompt) {
        List<SceneConfigVO> sceneConfigVOList = new ArrayList<>();
        JSONObject algoImplConfig = JSONObject.parseObject(algoBackendDO.getImplConfig());
        //优先取助手的systemPrompt
        if (sceneSystemPrompt != null && sceneSystemPrompt.length() > 0){
            algoImplConfig.put("systemPrompt", sceneSystemPrompt);
        }
        sceneConfigVOList.add(new SceneConfigVO("modelEnv", algoImplConfig.containsKey("modelEnv") ?  algoImplConfig.get("modelEnv") : defaultEnv, "所访问的模型在maya的环境"));
        sceneConfigVOList.add(new SceneConfigVO("maxToken", algoBackendDO.getMaxToken(), "上文最大token数。"));
        sceneConfigVOList.add(new SceneConfigVO("maxRound", algoBackendDO.getMaxRound(), "上文最大消息数。"));

        fieldTypeMap.entrySet().forEach(x ->{
            if (algoImplConfig.containsKey(x.getKey())) {
                sceneConfigVOList.add(new SceneConfigVO(x.getKey(), algoImplConfig.get(x.getKey()), fieldTypeMap.get(x.getKey())));
            }
        });
        //读取会话配置
        if (sessionModelConfig != null){
            JSONObject sessionModelJson = JSONObject.parseObject(sessionModelConfig);
            JSONObject impConfig = null;
            if (sessionModelJson.containsKey(AppConstants.IMPL_CONFIG)){
                impConfig = JSONObject.parseObject(sessionModelJson.getString(AppConstants.IMPL_CONFIG));
            }
            for (SceneConfigVO vo : sceneConfigVOList){
                if (sessionModelJson.containsKey(vo.getKey())){
                    vo.setValue(sessionModelJson.get(vo.getKey()));
                }
                if (impConfig != null && impConfig.containsKey(vo.getKey())){
                    vo.setValue(impConfig.get(vo.getKey()));
                }
            }
        }
        return sceneConfigVOList;
    }

}
