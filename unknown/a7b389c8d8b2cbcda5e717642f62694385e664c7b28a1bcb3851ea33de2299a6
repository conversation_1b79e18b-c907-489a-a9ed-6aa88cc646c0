package com.alipay.codegencore.dal.mapper;


import com.alipay.codegencore.dal.example.ConfigDOExample;
import com.alipay.codegencore.model.domain.ConfigDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface ConfigDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    long countByExample(ConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    int deleteByExample(ConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    @Delete({
        "delete from cg_config",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    @Insert({
        "insert into cg_config (name, value)",
        "values (#{name,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(ConfigDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    int insertSelective(ConfigDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    List<ConfigDO> selectByExample(ConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    @Select({
        "select",
        "id, name, value",
        "from cg_config",
        "where id = #{id,jdbcType=BIGINT}"
    })
    ConfigDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    int updateByExampleSelective(@Param("record") ConfigDO record, @Param("example") ConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    int updateByExample(@Param("record") ConfigDO record, @Param("example") ConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    int updateByPrimaryKeySelective(ConfigDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_config
     *
     * @mbg.generated Mon Mar 06 11:26:50 CST 2023
     */
    @Update({
        "update cg_config",
        "set name = #{name,jdbcType=VARCHAR},",
          "value = #{value,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(ConfigDO record);
}