/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import java.util.List;

/**
 * <AUTHOR>
 * @version Scene.java, v 0.1 2023年11月09日 上午11:20 wb-tzg858080
 */
public class Copilot extends ToString {

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 查询词
     */
    private String  queryTemplateListJson;

    /**
     * 查询词
     */
    private List<CopilotButton> queryTemplateList;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getQueryTemplateListJson() {
        return queryTemplateListJson;
    }

    public List<CopilotButton> getQueryTemplateList() {
        return queryTemplateList;
    }

    public void setQueryTemplateList(List<CopilotButton> queryTemplateList) {
        this.queryTemplateList = queryTemplateList;
    }

    public void setQueryTemplateListJson(String queryTemplateListJson) {
        this.queryTemplateListJson = queryTemplateListJson;
        queryTemplateList =  JSONObject.parseObject(queryTemplateListJson,new TypeReference<List<CopilotButton>>(){});
    }
}
