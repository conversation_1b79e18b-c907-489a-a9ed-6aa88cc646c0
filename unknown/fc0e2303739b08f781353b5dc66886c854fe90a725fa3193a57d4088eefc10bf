/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.codescan;

import com.alipay.codegencore.model.model.analysis.MethodBodyModel;
import com.alipay.codegencore.model.model.analysis.MethodParamModel;
import com.alipay.codegencore.model.model.analysis.TempCodeAnalysisResultContext;
import com.alipay.codegencore.utils.codescan.JavaParser;
import com.alipay.codegencore.utils.codescan.ScanTypeEnum;
import com.alipay.codegencore.utils.codescan.SimpleCodeAnalysisServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;
import org.smartunit.runtime.ViolatedAssumptionAnswer;

import java.util.*;

import static org.junit.Assert.*;
import static org.smartunit.shaded.org.mockito.Mockito.*;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class SimpleCodeAnalysisServiceImpl_SSTest extends SimpleCodeAnalysisServiceImpl_SSTest_scaffolding {
// allCoveredLines:[22, 24, 37, 38, 39, 40, 41, 43, 44, 46, 47, 48, 49, 64, 65, 66, 67, 68, 69, 70, 72, 75, 76, 77, 78, 79, 80, 83, 84, 85, 87]

  @Test(timeout = 4000)
  public void test_analysisCode_0()  throws Throwable  {
      //caseID:c6bb794077ac418fc3cd055cc561d0bc
      //CoveredLines: [22, 24, 37, 38, 39, 40, 41, 43, 44, 46, 47, 48, 49]
      //Input_0_String: org.antlr.v4.runtime.misc.ParseCancellationException
      //Input_1_ScanTypeEnum: ScanTypeEnum.ALL
      //Input_2_Class<JavaParser.AnnotationMethodOrConstantRestContext>: JavaParser.AnnotationMethodOrConstantRestContext.class
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      ScanTypeEnum scanTypeEnum0 = ScanTypeEnum.ALL;
      Class<JavaParser.AnnotationMethodOrConstantRestContext> class0 = JavaParser.AnnotationMethodOrConstantRestContext.class;
      
      //Call method: analysisCode
      simpleCodeAnalysisServiceImpl0.analysisCode("org.antlr.v4.runtime.misc.ParseCancellationException", scanTypeEnum0, class0);
  }

  @Test(timeout = 4000)
  public void test_getReferenceClassName_1()  throws Throwable  {
      //caseID:878ebd3e614ffec637d2f10245392372
      //CoveredLines: [22, 24, 64, 65, 66, 67, 68, 69, 70]
      //Input_0_String: 1617
      //Input_1_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0}
      //Assert: assertNotNull(method_result);
      //Assert: assertEquals("1617", method_result);
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      LinkedList<MethodParamModel> linkedList0 = new LinkedList<MethodParamModel>();
      //mock methodParamModel0
      MethodParamModel methodParamModel0 = mock(MethodParamModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("1617").when(methodParamModel0).getName();
      doReturn("1617").when(methodParamModel0).getType();
      
      linkedList0.add(methodParamModel0);
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(linkedList0).when(methodBodyModel0).getParamList();
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: getReferenceClassName
      String string0 = simpleCodeAnalysisServiceImpl0.getReferenceClassName("1617", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(string0);
      
      //Test Result Assert
      assertEquals("1617", string0);
  }

  @Test(timeout = 4000)
  public void test_getReferenceClassName_2()  throws Throwable  {
      //caseID:f002745b9657da8fe94dc973e50e5bb1
      //CoveredLines: [22, 24, 64, 65, 75, 76, 77, 78, 79, 80]
      //Input_0_String: 'F@BDf[%^{6K
      //Input_1_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=(MethodBodyModel) null, getLocalVariableMap=map0}
      //Assert: assertNotNull(method_result);
      //Assert: assertEquals("'F@BDf[%^{6K", method_result);
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("'F@BDf[%^{6K").when(map0).get(any());
      doReturn(123).when(map0).size();
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn((MethodBodyModel) null).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: getReferenceClassName
      String string0 = simpleCodeAnalysisServiceImpl0.getReferenceClassName("'F@BDf[%^{6K", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNotNull(string0);
      
      //Test Result Assert
      assertEquals("'F@BDf[%^{6K", string0);
  }

  @Test(timeout = 4000)
  public void test_getReferenceClassName_3()  throws Throwable  {
      //caseID:d87b6d43f8cf4979424ce9acdfce4a35
      //CoveredLines: [22, 24, 64, 65, 75, 76, 77, 83, 84, 85, 87]
      //Input_0_String: com.alipay.codegencore.utils.codescan.SimpleCodeAnalysisServiceImpl
      //Input_1_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=(MethodBodyModel) null, getFieldReferenceMap=map0, getLocalVariableMap=(Map<String, String>) null}
      //Assert: assertNull(method_result);
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((String) null).when(map0).get(any());
      doReturn(1).when(map0).size();
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(map0).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn((Map<String, String>) null).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn((MethodBodyModel) null).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: getReferenceClassName
      String string0 = simpleCodeAnalysisServiceImpl0.getReferenceClassName("com.alipay.codegencore.utils.codescan.SimpleCodeAnalysisServiceImpl", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_getReferenceClassName_4()  throws Throwable  {
      //caseID:502da548d8079e7176eba75605b0e7a5
      //CoveredLines: [22, 24, 64, 65, 66, 67, 75, 76, 77, 83, 84, 87]
      //Input_0_String: 1617
      //Input_1_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0}
      //Assert: assertNull(method_result);
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      LinkedList<MethodParamModel> linkedList0 = new LinkedList<MethodParamModel>();
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(linkedList0).when(methodBodyModel0).getParamList();
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: getReferenceClassName
      String string0 = simpleCodeAnalysisServiceImpl0.getReferenceClassName("1617", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_getReferenceClassName_5()  throws Throwable  {
      //caseID:c419b8af8d9a87d8a54d407ba0fa0fbf
      //CoveredLines: [22, 24, 64, 65, 66, 67, 75, 76, 77, 83, 84, 87]
      //Input_0_String: :IF4DLm2-x{*T
      //Input_1_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getFieldReferenceMap=(Map<String, String>) null, getLocalVariableMap=map0}
      //Assert: assertNull(method_result);
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((Map<String, String>) null).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(map0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: getReferenceClassName
      String string0 = simpleCodeAnalysisServiceImpl0.getReferenceClassName(":IF4DLm2-x{*T", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_getReferenceClassName_6()  throws Throwable  {
      //caseID:d16bae73e360829ae1d8fdab7e56ddc7
      //CoveredLines: [22, 24, 64, 65, 75, 76, 77, 78, 79, 83, 84, 87]
      //Input_0_String: 1.0
      //Input_1_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=(MethodBodyModel) null, getFieldReferenceMap=(Map<String, String>) null, getLocalVariableMap=map0}
      //Assert: assertNull(method_result);
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      //mock map0
      Map<String, String> map0 = (Map<String, String>) mock(Map.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((String) null).when(map0).get(any());
      doReturn((-1)).when(map0).size();
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((Map<String, String>) null).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(map0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn((MethodBodyModel) null).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: getReferenceClassName
      String string0 = simpleCodeAnalysisServiceImpl0.getReferenceClassName("1.0", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_getReferenceClassName_7()  throws Throwable  {
      //caseID:847efc21f7c80b28c26febbfd1b4cba1
      //CoveredLines: [22, 24, 64, 65, 66, 67, 68, 69, 72, 75, 76, 77, 83, 84, 87]
      //Input_0_String: $Gvw
      //Input_1_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getFieldReferenceMap=hashMap0, getLocalVariableMap=hashMap0}
      //Assert: assertNull(method_result);
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      Vector<MethodParamModel> vector0 = new Vector<MethodParamModel>();
      //mock methodParamModel0
      MethodParamModel methodParamModel0 = mock(MethodParamModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("com.alipay.codegencore.utils.codescan.SimpleCodeAnalysisServiceImpl").when(methodParamModel0).getName();
      
      vector0.add(methodParamModel0);
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(vector0).when(methodBodyModel0).getParamList();
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(hashMap0).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn(hashMap0).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: getReferenceClassName
      String string0 = simpleCodeAnalysisServiceImpl0.getReferenceClassName("$Gvw", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(string0);
  }

  @Test(timeout = 4000)
  public void test_getReferenceClassName_8()  throws Throwable  {
      //caseID:1f750d5214c1f76e907ef8e3be448197
      //CoveredLines: [22, 24, 64, 65, 66, 67, 68, 69, 72, 75, 76, 77, 83, 84, 87]
      //Input_0_String: 
      //Input_1_TempCodeAnalysisResultContext: {getWritingMethodBodyModel=methodBodyModel0, getFieldReferenceMap=(Map<String, String>) null, getLocalVariableMap=(Map<String, String>) null}
      //Assert: assertNull(method_result);
      
      SimpleCodeAnalysisServiceImpl simpleCodeAnalysisServiceImpl0 = new SimpleCodeAnalysisServiceImpl();
      Stack<MethodParamModel> stack0 = new Stack<MethodParamModel>();
      //mock methodParamModel0
      MethodParamModel methodParamModel0 = mock(MethodParamModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn("-155232867").when(methodParamModel0).getName();
      
      stack0.add(methodParamModel0);
      //mock methodBodyModel0
      MethodBodyModel methodBodyModel0 = mock(MethodBodyModel.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn(stack0).when(methodBodyModel0).getParamList();
      //mock tempCodeAnalysisResultContext0
      TempCodeAnalysisResultContext tempCodeAnalysisResultContext0 = mock(TempCodeAnalysisResultContext.class, withSettings().defaultAnswer(new ViolatedAssumptionAnswer()).stubOnly());
      doReturn((Map<String, String>) null).when(tempCodeAnalysisResultContext0).getFieldReferenceMap();
      doReturn((Map<String, String>) null).when(tempCodeAnalysisResultContext0).getLocalVariableMap();
      doReturn(methodBodyModel0).when(tempCodeAnalysisResultContext0).getWritingMethodBodyModel();
      
      //Call method: getReferenceClassName
      String string0 = simpleCodeAnalysisServiceImpl0.getReferenceClassName("", tempCodeAnalysisResultContext0);
      
      //Test Result Assert
      assertNull(string0);
  }
}
