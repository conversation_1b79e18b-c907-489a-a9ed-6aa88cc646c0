package com.alipay.codegencore.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.alipay.codegencore.dal.example.ChatSessionDOExample;
import com.alipay.codegencore.dal.example.ConfigDOExample;
import com.alipay.codegencore.dal.example.GptConversationDOExample;
import com.alipay.codegencore.dal.example.GptMessageDOExample;
import com.alipay.codegencore.dal.mapper.ChatSessionDOMapper;
import com.alipay.codegencore.dal.mapper.ConfigDOMapper;
import com.alipay.codegencore.dal.mapper.GptConversationDOMapper;
import com.alipay.codegencore.dal.mapper.GptMessageDOMapper;
import com.alipay.codegencore.dal.mapper.GptMessageFeedbackDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.ConfigDO;
import com.alipay.codegencore.model.domain.GptConversationDO;
import com.alipay.codegencore.model.domain.GptMessageDO;
import com.alipay.codegencore.model.domain.GptMessageFeedbackDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.AntcodePageTypeEnum;
import com.alipay.codegencore.model.enums.ConversationTypeEnum;
import com.alipay.codegencore.model.enums.FileAnnotationTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.links.Copilot;
import com.alipay.codegencore.model.model.links.CopilotAnswer;
import com.alipay.codegencore.model.model.links.CopilotButton;
import com.alipay.codegencore.model.model.links.CopilotCommand;
import com.alipay.codegencore.model.model.links.CopilotConfig;
import com.alipay.codegencore.model.model.links.CopilotWelcomeMessage;
import com.alipay.codegencore.model.model.links.Enum.GptConversationChannelEnum;
import com.alipay.codegencore.model.model.links.Enum.GptConversationStatusEnum;
import com.alipay.codegencore.model.model.links.Enum.GptMessageContentTypeEnum;
import com.alipay.codegencore.model.model.links.Enum.GptMessageTypeEnum;
import com.alipay.codegencore.model.model.links.GptConversationExtInfo;
import com.alipay.codegencore.model.model.links.GptConversationModel;
import com.alipay.codegencore.model.model.links.GptMessageContent;
import com.alipay.codegencore.model.model.links.GptMessageFeedbackContent;
import com.alipay.codegencore.model.model.links.GptMessageFeedbackModel;
import com.alipay.codegencore.model.model.links.GptMessageModel;
import com.alipay.codegencore.model.model.links.LinksCovertUtil;
import com.alipay.codegencore.model.model.links.LinksPluginInfo;
import com.alipay.codegencore.model.model.links.LinksStageInfo;
import com.alipay.codegencore.model.model.links.PageResult;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.openai.UserFeedBackVO;
import com.alipay.codegencore.model.request.copilot.CopilotRecommendMessageRequest;
import com.alipay.codegencore.service.answer.AnswerIndexService;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.UserFeedbackService;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.CommonTools;
import com.alipay.codegencore.utils.code.BloopSearchClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.06.21
 */
@Service
@Slf4j
public class LinksApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LinksApiService.class);

    private static final String COPILOT_CONFIG = "linksCopilotConfig";
    private static final String REPO_CAMMND = "tryRepoSearch";
    private static final String REPO_SEARCH = "Repo";
    private static final String PR_CHAT = "Pr";
    @Resource
    private ConfigService configService;
    @Resource
    private ConfigDOMapper configDOMapper;
    @Resource
    private UserAclService userAclService;
    @Resource
    private UserFeedbackService userFeedbackService;
    @Resource
    private ChatSessionManageService chatSessionManageService;
    @Resource
    private GptConversationDOMapper gptConversationDOMapper;
    @Resource
    private GptMessageDOMapper gptMessageDOMapper;
    @Resource
    private GptMessageFeedbackDOMapper gptMessageFeedbackDOMapper;
    @Resource
    private SceneService sceneService;
    @Resource
    private ChatMessageService chatMessageService;

    @Resource
    private ChatSessionDOMapper chatSessionDOMapper;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private AlgoBackendService algoBackendService;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Resource
    private AnswerIndexService answerIndexService;
    /**
     * 更新links助手配置
     *
     * <AUTHOR>
     * @since 2024.06.21
     * @param config config
     */
    public void updateCopilotConfig(Map<String, CopilotConfig> config){
        String originalConfig = configService.getConfigByKey(COPILOT_CONFIG, false);
        ConfigDO configDO = new ConfigDO();
        configDO.setName(COPILOT_CONFIG);
        if(StringUtils.isBlank(originalConfig)){
            configDO.setValue(JSON.toJSONString(config));
            configDOMapper.insert(configDO);
        }else {
            JSONObject jsonObject = JSONObject.parseObject(originalConfig);
            for (String key : config.keySet()) {
                jsonObject.put(key, config.get(key));
            }
            ConfigDOExample configDOExample = new ConfigDOExample();
            ConfigDOExample.Criteria criteria = configDOExample.createCriteria();
            criteria.andNameEqualTo(COPILOT_CONFIG);
            configDO.setValue(JSON.toJSONString(jsonObject));
            configDOMapper.updateByExampleSelective(configDO,configDOExample);
        }
    }

    /**
     * 获取links助手配置
     *
     * <AUTHOR>
     * @since 2024.06.21
     * @param copilotId copilotId
     * @return com.alipay.codegencore.model.model.links.CopilotConfig
     */
    public CopilotConfig getCopilotConfig(String copilotId){
        String copilotConfig = configService.getConfigByKey(COPILOT_CONFIG, false);
        if(StringUtils.isBlank(copilotConfig)){
            return null;
        }
        JSONObject configs = JSONObject.parseObject(copilotConfig);
        return configs.getObject(copilotId, CopilotConfig.class);
    }

    /**
     * 获取antcode助手配置
     * @param request 请求
     * @return
     */
    public List<CopilotButton> getWelcomeMessageByAntcodePageType(CopilotRecommendMessageRequest request) {
        String antcodePageType = request.getPageType();
        String antcodeCopilotRecommendQuestionConfigStr = configService.getConfigByKey(AppConstants.CONFIG_KEY_ANTCODE_COPILOT_RECOMMEND_QUESTION_CONFIG, false);

        if(StringUtils.isBlank(antcodeCopilotRecommendQuestionConfigStr)){
            return new ArrayList<>();
        }

        JSONObject recommendQuestionInfo = JSONObject.parseObject(antcodeCopilotRecommendQuestionConfigStr);

        if(recommendQuestionInfo == null){
            return new ArrayList<>();
        }

        if(StringUtils.isBlank(antcodePageType) || !recommendQuestionInfo.containsKey(antcodePageType)){
            antcodePageType = AntcodePageTypeEnum.MAIN.name();
        }

        JSONArray recommendQuestions = recommendQuestionInfo.getJSONArray(antcodePageType.toUpperCase());
        if (recommendQuestions == null) {
            return new ArrayList<>();
        }
        if (request.getRepoPath() != null && StringUtils.equals(antcodePageType, AntcodePageTypeEnum.REPO.name())) {
            JSONObject body = new JSONObject();
            body.put("repo_url", String.format("https://code.alipay.com/%s.git", request.getRepoPath()));
            body.put("repo_branch", request.getBranch());
            JSONArray result = BloopSearchClient.searchWiki(body);
            if (CollectionUtils.isNotEmpty(result)) {
                result.sort((a, b) -> {
                    String timeA = ((JSONObject)a).getString("gmt_create");
                    String timeB = ((JSONObject)b).getString("gmt_create");
                    return timeB.compareTo(timeA);  // 降序排序
                });
                JSONObject repoWiki = (JSONObject) result.get(0);
                String recommendQuestionStr = repoWiki.getString("recommend_questions");
                if (StringUtils.isNotBlank(recommendQuestionStr)) {
                    List<String> recommendQuestionsList = JSON.parseArray(recommendQuestionStr, String.class);
                    for (String question : recommendQuestionsList) {
                        Map<String, String> questionMap = new HashMap<>();
                        questionMap.put("display", question);
                        questionMap.put("query", question);
                        questionMap.put("command", AntcodePageTypeEnum.REPO.name());
                        recommendQuestions.add(questionMap);
                    }
                }

            }
        }
        return JSON.parseArray(recommendQuestions.toJSONString(), CopilotButton.class);
    }

    /**
     * 反馈接口
     *
     * <AUTHOR>
     * @since 2024.06.24
     * @param copilotId copilotId
     * @param content content
     */
    public void feedBack(String copilotId, String content){
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if (currentUser == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if(StringUtils.isBlank(content)){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if (content.length() > AppConstants.FEEDBACK_MAX_LENGTH) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        CopilotConfig copilotConfig = getCopilotConfig(copilotId);
        if (copilotConfig == null || copilotConfig.getSceneId() == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        UserFeedBackVO userFeedBackVO = new UserFeedBackVO();
        userFeedBackVO.setContent(content);
        userFeedBackVO.setFileUrl(new ArrayList<>());
        userFeedBackVO.setSceneId(Long.valueOf(copilotConfig.getSceneId()));
        Boolean result = userFeedbackService.feedBackViaUrl(userFeedBackVO);
        if (!result) {
            throw new BizException(ResponseEnum.ERROR_THROW, "请联系管理员！");
        }
    }
    /**
     * 消息投票反馈
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param messageId messageId
     * @param agreed agreed
     * @param content content
     */
    public void vote(String messageId, Boolean agreed, GptMessageFeedbackContent content, Long userId) {
        if (StringUtils.isBlank(messageId) || agreed == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        GptMessageFeedbackModel gptMessageFeedbackModel = new GptMessageFeedbackModel();
        gptMessageFeedbackModel.setAgreed(agreed);
        gptMessageFeedbackModel.setContent(content);
        gptMessageFeedbackModel.setUserId(String.valueOf(userId));
        gptMessageFeedbackModel.setMessageId(messageId);
        GptMessageFeedbackDO gptMessageFeedbackDO = new GptMessageFeedbackDO();
        LinksCovertUtil.GptMessageFeedBackModelCoverToDO(gptMessageFeedbackModel, gptMessageFeedbackDO);
        gptMessageFeedbackDO.setMongoId(ShortUid.getUid());
        gptMessageFeedbackDOMapper.insertSelective(gptMessageFeedbackDO);
    }
    /**
     * 创建copilot会话
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param copilotId copilotId
     * @param userId userId
     * @return com.alipay.codegencore.model.model.links.GptConversationModel
     */
    public GptConversationModel createConversation(String copilotId, Long userId,boolean forceCreate) {
        CopilotConfig copilot = getCopilotConfig(copilotId);
        if (copilot == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        if(!forceCreate){
            // 查询是否有空会话，如果有直接返回
            //1. 获取最后一笔会话
            GptConversationModel gptConversationModel = getCopilotLastConversation(copilotId, userId);
            //2. 如果最后一笔会话，是新会话直接返回
            if (gptConversationModel != null) {
                GptMessageModel firstUserMessage = getFirstUserMessage(gptConversationModel.getId(), userId);
                if (firstUserMessage == null) {
                    return gptConversationModel;
                }
            }
        }


        Integer sceneId = copilot.getSceneId();
        SceneDO scene = sceneService.getSceneById(Long.valueOf(sceneId));
        if (scene == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "助手配置不存在，请联系管理员！");
        }
        Copilot copilotConfig = new Copilot();
        sceneConvertToCopilot(scene,copilotConfig);

        // 创建站点助手会话
        ChatSessionDO session = chatSessionManageService.getNewSession(userId, null, null, Long.valueOf(sceneId), false, "copilot", null,forceCreate);
        String sessionId = session.getUid();
        if (sessionId == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "创建会话异常 ！");
        }
        // 删除错误数据
        GptConversationModel conversation = getConversationById(sessionId,false);
        if(conversation!=null){
            deleteCopilotConversationPhysical(sessionId);
        }
        //GPT会话表录入会话id
        GptConversationModel gptConversation = createCopilotConversation(copilotId, String.valueOf(userId),sessionId);

        // 发送欢迎消息
        sendWelcomeMessage(gptConversation.getConversationId(),copilotConfig);
        return gptConversation;
    }
    /**
     * 通过mongoId来查找会话
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param mongoId mongoId
     * @return com.alipay.codegencore.model.model.links.GptConversationModel
     */
    public GptConversationModel getConversationById(String mongoId,Boolean needNotDeleted){
        GptConversationDOExample gptConversationDOExample = new GptConversationDOExample();
        GptConversationDOExample.Criteria criteria = gptConversationDOExample.createCriteria();
        criteria.andMongoIdEqualTo(mongoId);
        if(needNotDeleted){
            criteria.andDeletedEqualTo(false);
        }
        List<GptConversationDO> gptConversationDOS = gptConversationDOMapper.selectByExample(gptConversationDOExample);
        if(CollectionUtils.isEmpty(gptConversationDOS)){
            return null;
        }
        GptConversationModel gptConversationModel = new GptConversationModel();
        LinksCovertUtil.GptConversationDOCoverToModel(gptConversationDOS.get(0), gptConversationModel);
        return gptConversationModel;
    }
    /**
     * 批量获取会话
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param mongoIdList mongoIdList
     * @return java.util.List<com.alipay.codegencore.model.model.links.GptConversationModel>
     */
    public List<GptConversationModel> getCopilotConversationList(List<String> mongoIdList) {
        GptConversationDOExample gptConversationDOExample = new GptConversationDOExample();
        GptConversationDOExample.Criteria criteria = gptConversationDOExample.createCriteria();
        criteria.andMongoIdIn(mongoIdList);
        gptConversationDOExample.setOrderByClause("id desc");
        List<GptConversationDO> gptConversationDOS = gptConversationDOMapper.selectByExample(gptConversationDOExample);
        if(CollectionUtils.isEmpty(gptConversationDOS)){
            return new ArrayList<>();
        }
        List<GptConversationModel> gptConversationModels = new ArrayList<>();
        for (GptConversationDO gptConversationDO: gptConversationDOS) {
            GptConversationModel gptConversationModel = new GptConversationModel();
            LinksCovertUtil.GptConversationDOCoverToModel(gptConversationDO, gptConversationModel);
            gptConversationModels.add(gptConversationModel);
        }
        return gptConversationModels;
    }
    /**
     * 根据用户id删除该copilot的所有会话
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param roomId roomId
     * @param userId userId
     */
    public void deleteAllCopilotConversation(String roomId, Long userId) {
        GptConversationDOExample gptConversationDOExample = new GptConversationDOExample();
        GptConversationDOExample.Criteria criteria = gptConversationDOExample.createCriteria();
        criteria.andRoomIdEqualTo(roomId);
        criteria.andUserIdEqualTo(String.valueOf(userId));
        criteria.andChannelEqualTo(GptConversationChannelEnum.COPILOT.name());
        criteria.andDeletedEqualTo(false);
        List<GptConversationDO> gptConversationDOS = gptConversationDOMapper.selectByExample(gptConversationDOExample);
        if(CollectionUtils.isEmpty(gptConversationDOS)){
            return;
        }
        ArrayList<String> session = new ArrayList<>();
        for (GptConversationDO gptConversationDO: gptConversationDOS) {
            session.add(gptConversationDO.getConversationId());
            gptConversationDO.setDeleted(true);
            gptConversationDOMapper.updateByPrimaryKeySelective(gptConversationDO);
        }
        chatSessionManageService.deleteSession(session);
    }
    /**
     * 删除copilot会话
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param conversationId conversationId
     */
    public void deleteCopilotConversation(String conversationId) {
        GptConversationDOExample gptConversationDOExample = new GptConversationDOExample();
        GptConversationDOExample.Criteria criteria = gptConversationDOExample.createCriteria();
        criteria.andConversationIdEqualTo(conversationId);
        criteria.andDeletedEqualTo(false);
        List<GptConversationDO> gptConversationDOS = gptConversationDOMapper.selectByExample(gptConversationDOExample);
        if (CollectionUtils.isEmpty(gptConversationDOS)) {
            return;
        }
        ArrayList<String> session = new ArrayList<>();
        if(StringUtils.isNotBlank(gptConversationDOS.get(0).getConversationId())){
            session.add(gptConversationDOS.get(0).getConversationId());
        }
        GptConversationDO gptConversationDO = gptConversationDOS.get(0);
        gptConversationDO.setDeleted(true);
        chatSessionManageService.deleteSession(session);
        gptConversationDOMapper.updateByPrimaryKeySelective(gptConversationDO);
    }

    /**
     * 物理删除Copilot会话及其Message
     *
     * <AUTHOR>
     * @since 2024.09.02
     * @param mongoId mongoId
     */
    private void deleteCopilotConversationPhysical(String mongoId){
        GptConversationDOExample gptConversationDOExample = new GptConversationDOExample();
        GptConversationDOExample.Criteria criteria = gptConversationDOExample.createCriteria();
        criteria.andMongoIdEqualTo(mongoId);
        gptConversationDOMapper.deleteByExample(gptConversationDOExample);
        GptMessageDOExample gptMessageDOExample = new GptMessageDOExample();
        GptMessageDOExample.Criteria criteria1 = gptMessageDOExample.createCriteria();
        criteria1.andConversationIdEqualTo(mongoId);
        gptMessageDOMapper.deleteByExample(gptMessageDOExample);
    }

    /**
     * 根据会话id查询消息列表
     *
     * @param conversationId 会话id
     * @return 消息列表
     */
    public List<GptMessageModel> queryMessageByConversationId(String conversationId){
        GptMessageDOExample gptMessageDOExample = new GptMessageDOExample();
        GptMessageDOExample.Criteria criteria = gptMessageDOExample.createCriteria();
        criteria.andConversationIdEqualTo(conversationId);
        criteria.andDeletedEqualTo(false);
        List<GptMessageDO> gptMessageDOS = gptMessageDOMapper.selectByExample(gptMessageDOExample);
        if(CollectionUtils.isEmpty(gptMessageDOS)){
            return null;
        }
        List<GptMessageModel> messageModels = new ArrayList<>();
        for (GptMessageDO gptMessageDO: gptMessageDOS) {
            GptMessageModel gptMessageModel = new GptMessageModel();
            LinksCovertUtil.GptMessageDOCoverToModel(gptMessageDO, gptMessageModel);
            messageModels.add(gptMessageModel);
        }
        return messageModels;
    }
    /**
     * 获取站点助手最后一个会话
     *
     * @param userId 用户id
     * @param roomId 租户id
     * @return 会话id
     */
    public GptConversationModel getCopilotLastConversation(String roomId, Long userId){
        GptConversationDOExample gptConversationDOExample = new GptConversationDOExample();
        GptConversationDOExample.Criteria criteria = gptConversationDOExample.createCriteria();
        criteria.andRoomIdEqualTo(roomId);
        criteria.andUserIdEqualTo(String.valueOf(userId));
        criteria.andChannelEqualTo(GptConversationChannelEnum.COPILOT.name());
        criteria.andDeletedEqualTo(false);
        gptConversationDOExample.setOrderByClause("id desc");
        List<GptConversationDO> gptConversationDOS = gptConversationDOMapper.selectByExample(gptConversationDOExample);
        if(CollectionUtils.isEmpty(gptConversationDOS)){
            return null;
        }
        GptConversationModel gptConversationModel = new GptConversationModel();
        LinksCovertUtil.GptConversationDOCoverToModel(gptConversationDOS.get(0),gptConversationModel);
        return gptConversationModel;
    }
    /**
     * 更新copilot会话
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptConversationModel gptConversationModel
     */
    public void updateCopilotConversation(GptConversationModel gptConversationModel){
        GptConversationDO gptConversationDO = new GptConversationDO();
        LinksCovertUtil.GptConversationModelCoverToDO(gptConversationModel,gptConversationDO);
        gptConversationDO.setId(gptConversationModel.getDbId());
        gptConversationDOMapper.updateByPrimaryKeySelective(gptConversationDO);
    }
    /**
     * 更新会话标题
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param conversation conversation
     */
    private void updateTitle(GptConversationModel conversation,UserAuthDO currentUser) {
        String id = conversation.getId();
        GptMessageModel messageModel = getFirstUserMessage(id, Long.valueOf(conversation.getUserId()));
        String query = messageModel != null ? messageModel.getText() : null;
        String copilotSessionId = conversation.getCopilotSessionId();
        LOGGER.info("updateTitle, conversationId:{}, query:{}, copilotSessionId:{}", id, query, copilotSessionId);
        if (StringUtils.isBlank(query) || StringUtils.isBlank(copilotSessionId)) {
            return;
        }
        ChatMessage message = new ChatMessage("user", query);
        String codeGPTSystemValue = codeGPTDrmConfig.getCodeGPTSystemValue();
        message.setContent(codeGPTSystemValue + message.getContent());
        List<ChatMessage> chatMessages = new ArrayList<>(Collections.singletonList(message));
        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        chatCompletionRequest.setMessages(chatMessages);
        String model = codeGPTDrmConfig.getGenerateTitleUseModel();
        String chatCompletionResponse = null;
        try {

            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
            if (algoBackendDO == null) {
                throw new BizException(ResponseEnum.MODEL_NOT_EXISTED);
            }
            if (!algoBackendDO.getEnable()) {
                throw new BizException(ResponseEnum.MODEL_UNSERVICEABLE);
            }
            // 更新走审核的字段
            updateCheckSwitch(model,chatCompletionRequest,copilotSessionId,currentUser);
            GptAlgModelServiceRequest param = new GptAlgModelServiceRequest(copilotSessionId, AppConstants.CODEGPT_TOKEN_USER, false, algoBackendDO, chatCompletionRequest);
            param.setUniqueAnswerId(copilotSessionId + "_title");
            param.setRequestUserId(currentUser.getId());
            ChatMessage assistantMessage = AlgoModelExecutor.getInstance().executorChat(algoBackendDO, param);
            chatCompletionResponse = assistantMessage.getContent();
        } catch (Exception e) {
            log.info("chatCompletionResponse error, use default title",e);
        }

        //标题为空，说明模型调用异常，使用默认标题
        if (StringUtils.isBlank(chatCompletionResponse)) {
            chatCompletionResponse = AppConstants.SESSION_DEFAULT_TITLE;
        } else if (chatCompletionResponse.length() > 20) {
            //预防ai模型返回超过20字符
            chatCompletionResponse = chatCompletionResponse.substring(0, 20);
        }
        //存生成的标题
        chatSessionManageService.updateSessionTitle(copilotSessionId, chatCompletionResponse);
        if (StringUtils.isBlank(chatCompletionResponse)) {
            return;
        }
        conversation.setTitle(chatCompletionResponse);
        updateCopilotConversation(conversation);
    }
    /**
     * 根据会话id 和用户id获取第一条消息
     * @param conversationId 会话id
     * @param userId 用户id
     * @return 消息内容
     */
    public GptMessageModel getFirstUserMessage(String conversationId, Long userId) {
        GptMessageDOExample gptMessageDOExample = new GptMessageDOExample();
        GptMessageDOExample.Criteria criteria = gptMessageDOExample.createCriteria();
        criteria.andConversationIdEqualTo(conversationId);
        criteria.andUserIdEqualTo(String.valueOf(userId));
        criteria.andTypeEqualTo(GptMessageTypeEnum.USER.name());
        criteria.andDeletedEqualTo(false);
        gptMessageDOExample.setOrderByClause("id asc");
        List<GptMessageDO> gptMessageDOS = gptMessageDOMapper.selectByExample(gptMessageDOExample);
        if(CollectionUtils.isEmpty(gptMessageDOS)){
            return null;
        }
        GptMessageModel gptMessageModel = new GptMessageModel();
        LinksCovertUtil.GptMessageDOCoverToModel(gptMessageDOS.get(0),gptMessageModel);
        return gptMessageModel;
    }
    /**
     * 根据会话id 和用户id获取最后一条消息
     * @param conversationId 会话id
     * @param userId 用户id
     * @return 消息内容
     */
    public GptMessageModel getLastUserMessage(String conversationId, Long userId) {
        GptMessageDOExample gptMessageDOExample = new GptMessageDOExample();
        GptMessageDOExample.Criteria criteria = gptMessageDOExample.createCriteria();
        criteria.andConversationIdEqualTo(conversationId);
        criteria.andUserIdEqualTo(String.valueOf(userId));
        criteria.andTypeEqualTo(GptMessageTypeEnum.USER.name());
        criteria.andDeletedEqualTo(false);
        gptMessageDOExample.setOrderByClause("id desc");
        List<GptMessageDO> gptMessageDOS = gptMessageDOMapper.selectByExample(gptMessageDOExample);
        if(CollectionUtils.isEmpty(gptMessageDOS)){
            return null;
        }
        GptMessageModel gptMessageModel = new GptMessageModel();
        LinksCovertUtil.GptMessageDOCoverToModel(gptMessageDOS.get(0),gptMessageModel);
        return gptMessageModel;
    }
    /**
     * 根据会话id 和用户id获取最后一条机器人消息
     * @param conversationId 会话id
     * @param userId 用户id
     * @return 消息内容
     */
    public GptMessageModel getLastBotMessage(String conversationId, Long userId){
        GptMessageDOExample gptMessageDOExample = new GptMessageDOExample();
        GptMessageDOExample.Criteria criteria = gptMessageDOExample.createCriteria();
        criteria.andConversationIdEqualTo(conversationId);
        criteria.andTypeEqualTo(GptMessageTypeEnum.ROBOT.name());
        criteria.andUserIdEqualTo(String.valueOf(userId));
        criteria.andDeletedEqualTo(false);
        gptMessageDOExample.setOrderByClause("id desc");
        List<GptMessageDO> gptMessageDOS = gptMessageDOMapper.selectByExample(gptMessageDOExample);
        if(CollectionUtils.isEmpty(gptMessageDOS)){
            return null;
        }
        GptMessageModel gptMessageModel = new GptMessageModel();
        LinksCovertUtil.GptMessageDOCoverToModel(gptMessageDOS.get(0),gptMessageModel);
        return gptMessageModel;
    }
    /**
     * 根据mongoId获取用户消息
     *
     * <AUTHOR>
     * @since 2024.07.11
     * @param mongoId mongoId
     * @return com.alipay.codegencore.model.model.links.GptMessageModel
     */
    public GptMessageModel getMessageById(String mongoId){
        GptMessageDOExample gptMessageDOExample = new GptMessageDOExample();
        GptMessageDOExample.Criteria criteria = gptMessageDOExample.createCriteria();
        criteria.andMongoIdEqualTo(mongoId);
        criteria.andDeletedEqualTo(false);
        List<GptMessageDO> gptMessageDOS = gptMessageDOMapper.selectByExample(gptMessageDOExample);
        if(CollectionUtils.isEmpty(gptMessageDOS)){
            return null;
        }
        GptMessageModel gptMessageModel = new GptMessageModel();
        LinksCovertUtil.GptMessageDOCoverToModel(gptMessageDOS.get(0),gptMessageModel);
        return gptMessageModel;
    }
    /**
     * 更新消息
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param mongoId mongoId
     * @param content content
     */
    public void updateMessage(String mongoId, GptMessageContent content){
        GptMessageDOExample gptMessageDOExample = new GptMessageDOExample();
        GptMessageDOExample.Criteria criteria = gptMessageDOExample.createCriteria();
        criteria.andMongoIdEqualTo(mongoId);
        criteria.andDeletedEqualTo(false);
        List<GptMessageDO> gptMessageDOS = gptMessageDOMapper.selectByExample(gptMessageDOExample);
        if(CollectionUtils.isEmpty(gptMessageDOS)){
            return;
        }
        GptMessageDO gptMessageDO = gptMessageDOS.get(0);
        gptMessageDO.setContent(JSON.toJSONString(content));
        gptMessageDOMapper.updateByPrimaryKeySelective(gptMessageDO);
    }
    /**
     * 删除消息
     *
     * <AUTHOR>
     * @since 2024.06.29
     * @param mongoId mongoId
     */
    public void deleteMessageByMongoId(String mongoId){
        GptMessageDOExample gptMessageDOExample = new GptMessageDOExample();
        GptMessageDOExample.Criteria criteria = gptMessageDOExample.createCriteria();
        criteria.andMongoIdEqualTo(mongoId);
        criteria.andDeletedEqualTo(false);
        List<GptMessageDO> gptMessageDOS = gptMessageDOMapper.selectByExample(gptMessageDOExample);
        if(CollectionUtils.isEmpty(gptMessageDOS)){
            return;
        }
        GptMessageDO gptMessageDO = gptMessageDOS.get(0);
        gptMessageDO.setDeleted(true);
        gptMessageDOMapper.updateByPrimaryKeySelective(gptMessageDO);
    }
    /**
     * 用户发送一条消息
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptConversationId gptConversationId
     * @param userId userId
     * @param command command
     * @param query query
     */
    public void createMessage(HttpServletResponse httpServletResponse,String gptConversationId, Long userId, String command, String query, JSONObject extraInfo) throws IOException {
        log.info("createMessage-query = {},command{}", query,command);
        CopilotCommand copilotCommand = null ;
        boolean tryRepoSearch = isRepoChat(command);
        if (StringUtils.isNotBlank(command)){
            GptConversationModel gptConversationModel = getConversationById(gptConversationId,true);
            //1. 匹配指令
            CopilotConfig config = getCopilotConfig(gptConversationModel.getRoomId());
            copilotCommand = findFirstCommand(config.getCommands(),command);
        }
        // command补偿
        if (copilotCommand == null && StringUtils.isNotBlank(command)){
            if(command.equals(REPO_SEARCH)||command.equals(REPO_CAMMND)){
                copilotCommand = new CopilotCommand();
                copilotCommand.setCommand(command);
                copilotCommand.setTemplate("%s");
                copilotCommand.setName("仓库问答");
                copilotCommand.setRemark("仓库问答");
            }else if (command.equals(PR_CHAT)){
                copilotCommand = new CopilotCommand();
                copilotCommand.setCommand(command);
                copilotCommand.setTemplate("%s");
                copilotCommand.setName("PR问答");
                copilotCommand.setRemark("PR问答");
            }
        }
        createUserMessage(gptConversationId, String.valueOf(userId), copilotCommand, query,null,null, GptMessageContentTypeEnum.COPILOT_COMMAND, tryRepoSearch);
        query = copilotCommand!= null? String.format(copilotCommand.getTemplate(),query):query;
        sse(httpServletResponse,gptConversationId, query,tryRepoSearch, extraInfo,false,userId,command);
    }
    /**
     * 重新生成回答
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param httpServletResponse httpServletResponse
     * @param gptConversationId gptConversationId
     */
    public void regenerate(HttpServletResponse httpServletResponse,String gptConversationId,Long userId, JSONObject extraInfo) throws IOException {
        log.info("regenerate-gptConversationId = {}", gptConversationId);
        GptMessageModel gptMessageModel =getLastBotMessage(gptConversationId, userId);
        GptMessageModel lastUserMessage = getLastUserMessage(gptConversationId, userId);
        if (gptMessageModel != null) {
            deleteMessageByMongoId(gptMessageModel.getId());
        }
        if(lastUserMessage == null || lastUserMessage.getContent()==null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "未获取到用户提问");
        }
        GptMessageContent content = lastUserMessage.getContent();
        CopilotCommand copilotCommand = content.getCopilotCommand();
        String command = null;
        if(copilotCommand!=null){
            command = copilotCommand.getCommand();
        }
        boolean tryRepoSearch = isRepoChat(command);
        sse(httpServletResponse,gptConversationId, content.getText(), tryRepoSearch, extraInfo,true,userId,command);
    }
    /**
     * 创建一条message持久化
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptConversationId gptConversationId
     * @param userId userId
     * @param copilotCommand copilotCommand
     * @param query query
     * @param gptMessageId gptMessageId
     * @param fileId fileId
     * @param type type
     * @return com.alipay.codegencore.model.model.links.GptMessageModel
     */
    public GptMessageModel createUserMessage(String gptConversationId, String userId, CopilotCommand copilotCommand, String query,String gptMessageId,String fileId, GptMessageContentTypeEnum type,Boolean tryRepoSearch) {
        GptMessageContent gptMessageContent = new GptMessageContent();
        gptMessageContent.setType(type);
        gptMessageContent.setText(query);
        if(copilotCommand != null){
            gptMessageContent.setCopilotCommand(copilotCommand);
        }else if(tryRepoSearch){
            CopilotCommand command = new CopilotCommand();
            command.setCommand(REPO_CAMMND);
            gptMessageContent.setCopilotCommand(command);
        }
        gptMessageContent.setFileId(fileId);
        GptMessageModel gptMessageModel = new GptMessageModel();
        gptMessageModel.setUserId(userId);
        gptMessageModel.setConversationId(gptConversationId);
        gptMessageModel.setContent(gptMessageContent);
        gptMessageModel.setType(GptMessageTypeEnum.USER);
        gptMessageModel.setMongoId(StringUtils.isNotBlank(gptMessageId)?gptMessageId:ShortUid.getUid());
        GptMessageDO gptMessageDO = new GptMessageDO();
        LinksCovertUtil.GptMessageModelCoverToDO(gptMessageModel,gptMessageDO);
        gptMessageDO.setDeleted(false);
        gptMessageDOMapper.insertSelective(gptMessageDO);
        gptMessageModel.setDbId(gptMessageDO.getId());
        return gptMessageModel;
    }

    /**
     * 获取ChatSessionDO会话列表
     *
     * <AUTHOR>
     * @since 2024.11.05
     * @param sceneId sceneId
     * @param userId userId
     * @param query query
     * @param repoPath repoPath
     * @param page page
     * @param pageSize pageSize
     * @return com.alipay.codegencore.model.model.links.PageResult<com.alipay.codegencore.model.domain.ChatSessionDO>
     */
    public PageResult<ChatSessionDO> getChatSessionUidByCopilot(Long sceneId,Long userId, String query, String repoPath,Integer page,Integer pageSize){
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        ChatSessionDOExample.Criteria criteria = chatSessionDOExample.createCriteria();
//        ChatSessionDOExample.Criteria emptyExtInfocriteria = chatSessionDOExample.createCriteria();
        criteria.andSceneIdEqualTo(sceneId);
        criteria.andUserIdEqualTo(userId);
        criteria.andDeletedEqualTo((byte)0);
//        emptyExtInfocriteria.andSceneIdEqualTo(sceneId);
//        emptyExtInfocriteria.andUserIdEqualTo(userId);
//        emptyExtInfocriteria.andDeletedEqualTo((byte)0);
        if(StringUtils.isNotBlank(query)){
            criteria.andTitleLike("%"+query+"%");
//            emptyExtInfocriteria.andTitleLike("%"+query+"%");
        }
        if(StringUtils.isNotBlank(repoPath)){
            criteria.andExtInfoLike("%"+repoPath+"%");
//            emptyExtInfocriteria.andExtInfoIsNull();
//            chatSessionDOExample.or(emptyExtInfocriteria);
        }
        long total = chatSessionDOMapper.countByExample(chatSessionDOExample);
        chatSessionDOExample.setOrderByClause("id desc limit "+page*pageSize+","+pageSize);
        List<ChatSessionDO> chatSessionDOS = chatSessionDOMapper.selectByExample(chatSessionDOExample);
        return new PageResult<>(chatSessionDOS,(int)total,page,pageSize);
    }
    /**
     * 创建bot消息持久化
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param conversationId conversationId
     * @param copilotAnswer copilotAnswer
     * @return com.alipay.codegencore.model.model.links.GptMessageModel
     */
    private GptMessageModel createCopilotAnswerMsg(String conversationId, CopilotAnswer copilotAnswer) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        GptMessageModel gptMessageModel = new GptMessageModel();
        gptMessageModel.setUserId(String.valueOf(currentUser.getId()));
        gptMessageModel.setType(GptMessageTypeEnum.ROBOT);
        gptMessageModel.setConversationId(conversationId);
        gptMessageModel.setContent(GptMessageContent.getCopilotAnswer(copilotAnswer,null));
        GptMessageDO gptMessageDO = new GptMessageDO();
        LinksCovertUtil.GptMessageModelCoverToDO(gptMessageModel,gptMessageDO);
        gptMessageDO.setDeleted(false);
        gptMessageDO.setMongoId(ShortUid.getUid());
        gptMessageDOMapper.insertSelective(gptMessageDO);
        gptMessageModel.setDbId(gptMessageDO.getId());
        gptMessageModel.setId(gptMessageDO.getMongoId());
        return gptMessageModel;
    }


    /**
     * 会话创建持久化
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param linksCopilotId linksCopilotId
     * @param userId userId
     * @param sessionId sessionId
     * @return com.alipay.codegencore.model.model.links.GptConversationModel
     */
    private GptConversationModel createCopilotConversation(String linksCopilotId, String userId, String sessionId){
        GptConversationModel gptConversationModel = new GptConversationModel();
        gptConversationModel.setConversationId(sessionId);
        gptConversationModel.setRoomId(linksCopilotId);
        gptConversationModel.setUserId(userId);
        gptConversationModel.setChannel(GptConversationChannelEnum.COPILOT);
        gptConversationModel.setStatus(GptConversationStatusEnum.ACTIVE);
        gptConversationModel.setGmtLastMessage(new Date());
        gptConversationModel.setDeleted(false);
        gptConversationModel.setExtInfo(GptConversationExtInfo.build(sessionId));
        GptConversationDO gptConversationDO = new GptConversationDO();
        LinksCovertUtil.GptConversationModelCoverToDO(gptConversationModel,gptConversationDO);
        gptConversationDO.setMongoId(sessionId);
        int count = gptConversationDOMapper.insertSelective(gptConversationDO);
        if(count == 0){
            throw new BizException(ResponseEnum.ERROR_THROW,"创建会话失败");
        }
        gptConversationModel.setDbId(gptConversationDO.getId());
        return gptConversationModel;
    }
    /**
     * 发送欢迎信息
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param gptConversationId gptConversationId
     * @param copilot copilot
     */
    private void sendWelcomeMessage(String gptConversationId, Copilot copilot) {
        CopilotWelcomeMessage welcomeMessage = new CopilotWelcomeMessage();
        welcomeMessage.setPrefix(String.format("Hi，我是%s，请向我提问吧！", defaultIfNull(copilot.getName(), "站点助手")));
        welcomeMessage.setSuffix(copilot.getDescription());
        welcomeMessage.setButtons(copilot.getQueryTemplateList());
        GptMessageModel gptMessageModel = new GptMessageModel();
        gptMessageModel.setUserId(String.valueOf(userAclService.getCurrentUser().getId()));
        gptMessageModel.setType(GptMessageTypeEnum.ROBOT);
        gptMessageModel.setConversationId(gptConversationId);
        gptMessageModel.setContent(GptMessageContent.getCopilotWelcomeMessage(welcomeMessage));
        createGptMessage(gptMessageModel);
    }
    /**
     * 创建消息持久化
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param gptMessageModel gptMessageModel
     * @return com.alipay.codegencore.model.model.links.GptMessageModel
     */
    private GptMessageModel createGptMessage(GptMessageModel gptMessageModel) {
        gptMessageModel.setDeleted(false);
        GptMessageDO gptMessageDO = new GptMessageDO();
        LinksCovertUtil.GptMessageModelCoverToDO(gptMessageModel,gptMessageDO);
        gptMessageDO.setMongoId(ShortUid.getUid());
        int count = gptMessageDOMapper.insertSelective(gptMessageDO);
        if(count == 0){
            throw new BizException(ResponseEnum.ERROR_THROW,"创建消息失败");
        }
        gptMessageModel.setDbId(gptMessageDO.getId());
        return gptMessageModel;
    }
    /**
     * 向模型发送消息
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param httpServletResponse httpServletResponse
     * @param gptConversationId gptConversationId
     * @param query query
     */
    private void sse(HttpServletResponse httpServletResponse,String gptConversationId, String query,Boolean tryRepoSearch, JSONObject extraInfo, boolean regenerate,Long userId, String command) throws IOException {
        GptConversationModel gptConversationModel = getConversationById(gptConversationId,true);
        try {
            String copilotSessionId = gptConversationModel.getCopilotSessionId();
            if (StringUtils.isBlank(copilotSessionId)) {
                return;
            }
            ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(copilotSessionId);
            if(null == chatSessionDO || chatSessionDO.getDeleted() == 1) {
                log.info("session:{} not existed", copilotSessionId);
                throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
            }
            CopilotAnswer copilotAnswer = new CopilotAnswer();
            if(regenerate){
                copilotAnswer.setReGenerate(true);
            }
            GptMessageModel message = createCopilotAnswerMsg(gptConversationId, copilotAnswer);
            String gptMessageId = message.getId();
            ChatUtils.flushSseResponse(httpServletResponse,getIdMsg(gptMessageId));
            if(!regenerate){
                chatMessageService.conversation(httpServletResponse, copilotSessionId, query, false,false,gptMessageId,tryRepoSearch, extraInfo);
            }else {
                chatMessageService.regenerationAnswer(httpServletResponse,copilotSessionId, null,false,gptMessageId);
            }

            // 更新MessageDO内容
            List<ChatMessageDO> latestAssistantMessage = chatMessageService.getLatestAssistantMessage(copilotSessionId);
            if(latestAssistantMessage.size()>=2){
                ChatMessageDO chatMessageDO = latestAssistantMessage.get(1);
                LOGGER.info("获取Codefuse的模型回复信息{}",JSONObject.toJSONString(chatMessageDO));
                if(StringUtils.isBlank(chatMessageDO.getErrorMsg())){
                    String content = chatMessageDO.getContent();
                    String runtimeInfoStr = chatMessageDO.getRuntimeInfo();
                    JSONObject runtimeInfo = runtimeInfoStr==null?new JSONObject():JSONObject.parseObject(runtimeInfoStr);
                    JSONObject docsInfo = runtimeInfo.getJSONObject("docsInfo");
                    JSONObject repoChatInfo = runtimeInfo.getJSONObject("repoChatInfo");
                    JSONObject repoChatConfig = extraInfo.getJSONObject("repoChatConfig");
                    JSONObject repoInfo = new JSONObject();
                    if(repoChatInfo!=null){
                        repoInfo.put("references",repoChatInfo.getJSONArray("references"));
                    }
                    if(repoChatConfig!=null){
                        repoInfo.put("pr_url",repoChatConfig.getString("pr_url"));
                        repoInfo.put("pr_title", repoChatConfig.getString("pr_title"));
                    }
                    GptMessageModel lastUserMessage = getLastUserMessage(gptConversationId, userId);
                    GptMessageContent userMessageContent= lastUserMessage.getContent();
                    userMessageContent.setRepoChatInfo(repoInfo);
                    updateMessage(lastUserMessage.getMongoId(),userMessageContent);
                    copilotAnswer.setRepoChatInfo(repoInfo);
                    LOGGER.info("docInfo{}",JSONObject.toJSONString(docsInfo));
                    content = chatMessageService.rewriteFileAnnotation(content, docsInfo, FileAnnotationTypeEnum.valueOf(codeGPTDrmConfig.getFileAnnotationType()));
                    LOGGER.info("after rewrite file annotation content{}",content);
                    copilotAnswer.setResult(content);
                    copilotAnswer.setId(chatMessageDO.getUid());
                    if(StringUtils.isNotBlank(chatMessageDO.getPluginLog())){
                        parseData(chatMessageDO.getPluginLog(),copilotAnswer);
                    }
                }else {
                    copilotAnswer.setResult(chatMessageDO.getErrorMsg());
                    copilotAnswer.setId(chatMessageDO.getUid());
                }
            }
            LOGGER.info("更新消息内容{}",JSONObject.toJSONString(copilotAnswer));
            updateMessage(gptMessageId, GptMessageContent.getCopilotAnswer(copilotAnswer,command));
        } catch (Exception e) {
            LOGGER.error("CopilotAnswer error, uid{}",gptConversationId,e);
            CommonTools.writeResponse(e.getMessage(),200,httpServletResponse,ResponseEnum.AI_CALL_ERROR,e);
        } finally {
            if (StringUtils.isBlank(gptConversationModel.getTitle())) {
                UserAuthDO currentUser = userAclService.getCurrentUser();
                appThreadPool.execute(()->updateTitle(gptConversationModel,currentUser));
            }
        }
    }

    /**
     * 如果对象为null，返回默认值
     *
     * @param obj
     * @param defaultValue
     * @param <T>
     * @return
     */
    public static <T> T defaultIfNull(T obj, T defaultValue) {
        if (obj != null) {
            return obj;
        }
        return defaultValue;
    }
    /**
     * scene转为copilot
     *
     * <AUTHOR>
     * @since 2024.06.27
     * @param sceneDO sceneDO
     * @param copilot copilot
     */
    private void sceneConvertToCopilot(SceneDO sceneDO,Copilot copilot){
        copilot.setDescription(sceneDO.getDescription());
        copilot.setName(sceneDO.getName());
        copilot.setQueryTemplateList(JSONArray.parseArray(sceneDO.getQueryTemplateListJson(), CopilotButton.class));
        copilot.setQueryTemplateListJson(sceneDO.getQueryTemplateListJson());
    }

    /**
     * 判断当前会话是否为第一轮对话
     *
     * @param sessionUid 会话唯一标识符
     * @return 如果是第一轮对话则返回true，否则返回false
     */
    private boolean isPluginEnabled(String sessionUid) {
        // 获取聊天会话对象
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
        // 初始化场景对象
        SceneDO scene = null;
        if (chatSessionDO.getSceneId() != null) {
            // 根据场景ID获取场景对象
            scene = sceneService.getSceneById(chatSessionDO.getSceneId());
            ConversationTypeEnum conversationTypeEnum = chatMessageService.getConversationType(scene);
            return conversationTypeEnum != ConversationTypeEnum.NORMAL;
        }
        return false;
    }
    /**
     * 更新取消消息
     *
     * @param messageId messageId
     * <AUTHOR>
     * @since 2024.06.27
     */
    public void updateCancelMessage(String messageId){
        GptMessageModel gptMessageModel = getMessageById(messageId);
        if (gptMessageModel.getContent()!= null && gptMessageModel.getContent().getCopilotAnswer()!= null ){
            CopilotAnswer copilotAnswer = gptMessageModel.getContent().getCopilotAnswer();
            copilotAnswer.setIsCancel(true);
            updateMessage(messageId, gptMessageModel.getContent());
        }
    }
    /**
     * 匹配命令
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param commands commands
     * @param target target
     * @return com.alipay.codegencore.model.model.links.CopilotCommand
     */
    private CopilotCommand findFirstCommand(List<CopilotCommand> commands,String target){
        for (CopilotCommand command : commands) {
            if(command.getCommand().equalsIgnoreCase(target)){
                return command;
            }
        }
        return null;
    }

    /**
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param gptMessageId gptMessageId
     * @return java.lang.String
     */
    private String getIdMsg(String gptMessageId) {
        return JSON.toJSONString(buildMap("type", "id", "id", gptMessageId), SerializerFeature.SortField);
    }

    /**
     * 构造HashMap，并put两对K-V
     * @param key1
     * @param value1
     * @param key2
     * @param value2
     * @param <K>
     * @param <V>
     * @return
     */
    public static <K, V> Map<K, V> buildMap(K key1, V value1, K key2, V value2) {
        Map<K, V> map = new HashMap<>();
        map.put(key1, value1);
        map.put(key2, value2);
        return map;
    }
    /**
     * 更新走审核的字段
     *
     * <AUTHOR>
     * @since 2024.06.28
     * @param model model
     * @param chatCompletionRequest chatCompletionRequest
     * @param sessionUid sessionUid
     */
    private void updateCheckSwitch(String model, ChatCompletionRequest chatCompletionRequest, String sessionUid,UserAuthDO currentUser) {
        JSONObject codeFuseCheckSwitch = JSONObject.parseObject(codeGPTDrmConfig.getCodeFuseCheckSwitch());
        JSONObject modelCheckSwitch = codeFuseCheckSwitch.getJSONObject(model.toUpperCase());
        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
        chatRequestExtData.setCodeGPTUser(AppConstants.CODEGPT_TOKEN_USER);
        chatRequestExtData.setEmpId(currentUser.getEmpId());
        chatRequestExtData.setContentIsTitle(true);
        chatRequestExtData.setSessionUid(sessionUid);
        if (codeFuseCheckSwitch.containsKey(model.toUpperCase())) {
            chatRequestExtData.setAntDsrCheck(modelCheckSwitch.getBoolean(ReviewPlatformEnum.ANTDSR.name()));
            chatRequestExtData.setInfoSecCheck(modelCheckSwitch.getBoolean(ReviewPlatformEnum.INFOSEC.name()));
            chatRequestExtData.setKeymapCheck(modelCheckSwitch.getBoolean(ReviewPlatformEnum.KEYMAP.name()));
        }
        chatCompletionRequest.setChatRequestExtData(chatRequestExtData);
    }

    /**
     * 解析插件调用参数
     *
     * <AUTHOR>
     * @since 2024.07.04
     * @param pluginLogStr pluginLogStr
     * @param copilotAnswer copilotAnswer
     */
    public void parseData(String pluginLogStr, CopilotAnswer copilotAnswer){
        try {
            JSONObject pluginLogList = JSONObject.parseObject(pluginLogStr);
            List<JSONObject> pluginLogs = JSONArray.parseArray(pluginLogList.getString("pluginLogList")).toJavaList(JSONObject.class);
            JSONObject pluginLog = pluginLogs.get(0);
            copilotAnswer.setPluginInfo(pluginLog.getObject("pluginInfo", LinksPluginInfo.class));
            LOGGER.info("copilotAnswer:{}",JSONObject.toJSONString(copilotAnswer));
            List<JSONObject> stageLogList = pluginLog.getJSONArray("stageLogList").toJavaList(JSONObject.class);
            List<String> stageList = new ArrayList<>();
            List<LinksStageInfo> stageInfoList = new ArrayList<>();
            for (JSONObject stageLog : stageLogList) {
                if("functionCall".equalsIgnoreCase(stageLog.getString("type"))){
                    copilotAnswer.setStageInfo(stageLog.getObject("stageInfo", LinksStageInfo.class));
                    continue;
                }
                LOGGER.info("copilotAnswer:{}",JSONObject.toJSONString(copilotAnswer));
                stageList.add(stageLog.getString("stageName"));
                JSONObject input = stageLog.getJSONObject("stageInfo").getJSONObject("input");
                copilotAnswer.setForm(input.getJSONObject("formData").getInnerMap());
                if("PAUSE".equalsIgnoreCase(stageLog.getString("finishReason"))){
                    copilotAnswer.setIsForm(true);
                }else {
                    if(input.containsKey("requestBody")){
                        copilotAnswer.setParams(input.getJSONObject("requestBody").getInnerMap());
                    }
                    LinksStageInfo stageInfo = stageLog.getObject("stageInfo", LinksStageInfo.class);
                    stageInfo.setType(stageLog.getString("type"));
                    stageInfoList.add(stageInfo);
                }
                LOGGER.info("copilotAnswer:{}",JSONObject.toJSONString(copilotAnswer));
            }
            copilotAnswer.setStageList(stageList);
            copilotAnswer.setStageLogList(stageInfoList);
        }catch (Exception e){
            LOGGER.error(e.getMessage());
        }

    }

    /**
     * 判断是否走repoChatService链路
     *
     * <AUTHOR>
     * @since 2024.11.11
     * @param command command
     * @return boolean
     */
    public boolean isRepoChat(String command){
        return StringUtils.isNotBlank(command) && (command.equals(REPO_SEARCH) || command.equals(REPO_CAMMND) || command.equals(PR_CHAT));
    }

    /**
     * 判断用户是否有权限查看会话
     *
     * <AUTHOR>
     * @since 2024.11.11
     * @param userId userId
     * @param conversationId conversationId
     * @return boolean
     */
    public boolean checkUserConversationAuth(String userId, String conversationId){
        GptConversationDOExample gptConversationDOExample = new GptConversationDOExample();
        GptConversationDOExample.Criteria criteria = gptConversationDOExample.createCriteria();
        criteria.andMongoIdEqualTo(conversationId);
        criteria.andUserIdEqualTo(userId);
        List<GptConversationDO> gptConversationDOS = gptConversationDOMapper.selectByExample(gptConversationDOExample);
        return CollectionUtils.isNotEmpty(gptConversationDOS);
    }


}
