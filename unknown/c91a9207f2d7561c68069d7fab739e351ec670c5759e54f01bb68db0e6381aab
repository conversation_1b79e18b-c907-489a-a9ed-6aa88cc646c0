package com.alipay.codegencore.service.tool.learning.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.contant.AlgoImplConfigKey;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.enums.ChatMessageStatusEnum;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.tool.learning.PluginTypeEnum;
import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.PluginServiceRequestContext;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLogGroup;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageInfo;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema.ModelStageInput;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatFunction;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import com.alipay.codegencore.model.response.linke.ApprovalUser;
import com.alipay.codegencore.model.response.linke.DefaultPRValueVO;
import com.alipay.codegencore.model.response.linke.WorkItemDefaultVO;
import com.alipay.codegencore.model.response.linke.WorkItemVO;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.impl.model.AlgoModelUtilService;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.tool.learning.PluginConfigService;
import com.alipay.codegencore.service.tool.learning.PluginWorkflowService;
import com.alipay.codegencore.service.tool.learning.ToolLearningUtil;
import com.alipay.codegencore.service.tool.learning.plugin.CodeFuseCallLinke;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.service.utils.SessionUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.service.utils.ShortenLink;
import com.alipay.codegencore.utils.CommonTools;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.http.PostBuilder;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import io.swagger.v3.oas.models.media.Schema;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.http.HttpResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 插件工作量实现类
 *
 * <AUTHOR>
 */
@Service
public class PluginWorkflowServiceImpl implements PluginWorkflowService {
    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    private static final Pattern HTTP_PATTERN = Pattern.compile("(ht|f)tp(s?)\\:\\/\\/[0-9a-zA-Z]([-.\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\/?)([a-zA-Z0-9\\-\\.\\?\\,\\'\\/\\\\&%\\+\\$#_=]*)?");

    private static final String AUTH_TOKEN = "Authorization";

    private static final Logger LOGGER = LoggerFactory.getLogger( PluginWorkflowServiceImpl.class );
    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");
    @Resource private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;
    @Resource private AlgoBackendService algoBackendService;
    @Resource private AlgoModelUtilService algoModelUtilService;
    @Resource private PluginConfigService pluginConfigService;
    @Resource(name = "appThreadPool") private ExecutorService appThreadPool;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private CheckService checkService;

    @Resource
    private ChatSessionManageService chatSessionManageService;

    @Resource
    private ShortenLink shortenLink;

    @Resource
    private ConfigService     configService;
    @Resource
    private CodeFuseCallLinke codeFuseCallLinke;


    @Resource
    private SecAgentHelper secAgentHelper;

    @Resource
    private TokenService tokenService;

    private static final long DEFAULT_STREAM_WAIT_TIME = 200;

    /**
     * 前置请求
     *
     * @param queryParams           用户提问
     * @param preRequestStageConfig 前置请求阶段配置
     * @param paramSchemaList       参数列表
     * @param params                请求参数
     * @return 前置请求返回结果
     */
    @Override
    public Object preRequest(String pluginType, Integer stageIndex, PluginServiceRequestContext queryParams, List<Map<String, Object>> paramSchemaList, Map<String, Object> preRequestStageConfig, Map<String, Object> params, boolean isContinue, long requestTimeOut) {
        String uniqueAnswerId = queryParams.getUniqueAnswerId();
        String answerUid = queryParams.getAnswerUid();

        try {
            // 取出配置中的url，目前apiList只有一个接口
            ArrayList<Map<String, Object>> apiList = (ArrayList<Map<String, Object>>) preRequestStageConfig.get("api_list");
            Map<String, Object> api = apiList.get(0);
            String url = (String) api.get("url");
            boolean userConfirm = api.get("userConfirm") != null ? (boolean) api.get("userConfirm") : false;

            // 获取参数
            Map<String, Object> paramsJson = new LinkedHashMap<>();
            paramsJson.putAll(params);

            // 表单 - 校验是否需要弹出表单
            if (!checkFormData(queryParams, queryParams.getPluginDO(), paramsJson, isContinue, userConfirm)) {
                LOGGER.info("lack required parameter, pause preRequest: url:{}, params:{}", url, JSON.toJSONString(paramsJson));
                Map<String, Object> formData = new HashMap<>();
                JSONObject parameters = ToolLearningUtil.exactFunctionInfoFromPlugin(queryParams.getPluginDO(), false).getFunctionParamConfigDisplay();
                formData.put("jsonSchema", JSONObject.toJSONString(parameters));
                String messageFormDefaultUiSchema = codeGPTDrmConfig.getMessageFormDefaultUiSchema();

                WorkItemDefaultVO defaultIteration = null;
                DefaultPRValueVO defaultPrValue = null;
                String name = parameters.getString("name");
                ChatSessionDO chatSessionDO = queryParams.getChatSessionDO();
                String empId = queryParams.getUserAuthDO().getEmpId();
                if (StrUtil.equals(name, "linke_create_iteration") && empId != null) {

                    defaultIteration = codeFuseCallLinke.getDefaultIteration(empId,
                            chatSessionDO != null ? chatSessionDO.getUid() : null);
                }
                if (StrUtil.equals(name, "linke_create_pullRequest") && empId != null) {

                    defaultPrValue = codeFuseCallLinke.getDefaultPrValue(empId,
                            chatSessionDO != null ? chatSessionDO.getUid() : null);
                }

                formData.put("uiSchema", ToolLearningUtil.exactMessageFormUiSchemaFromPlugin(queryParams.getPluginDO(),
                        messageFormDefaultUiSchema));
                formData.put("arguments", getAllArguments(queryParams, paramsJson,defaultIteration,defaultPrValue));
                StageInfo apiStageInputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.INPUT, "formData", formData);
                ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, uniqueAnswerId, answerUid, queryParams.getPluginIndex(), stageIndex, StageTypeEnum.API.name(), apiStageInputInfo, ChatMessageStatusEnum.PAUSE.name());
                return null;
            }

            // 校验参数
            BaseResponse paramsCheckResult = checkAndAddPreRequestParams(queryParams, paramsJson, paramSchemaList, params);
            if (paramsCheckResult.getErrorCode()!=0){
                sendApiStageErrorInfo(queryParams, stageIndex, paramsCheckResult);
                return null;
            }

            // 表单 - user fill
            if (isContinue) {
                StageInfo apiStageInputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.INPUT, "userFill", paramsJson);
                ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, uniqueAnswerId, answerUid, queryParams.getPluginIndex(), stageIndex, StageTypeEnum.API.name(), apiStageInputInfo, null);
            }

            LOGGER.info("preRequest: url:{}, params:{}", url, JSON.toJSONString(paramsJson));
            //参数发生改变，要更新上下文中的参数
            queryParams.setPluginParams(paramsJson);

            // 发送请求
            //只有内网版本才会展示接口调用参数
            if(codeGPTDrmConfig.isIntranetApplication()){
                StageInfo apiStageInputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.INPUT, "requestBody", paramsJson);
                ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, uniqueAnswerId, answerUid, queryParams.getPluginIndex(), stageIndex, StageTypeEnum.API.getName(), apiStageInputInfo, null);
            }

            PostBuilder postBuilder = HttpClient.post(url);
            if (url.startsWith("https://codegencore")) {
                postBuilder.header(AUTH_TOKEN, configService.getConfigByKey(AUTH_TOKEN, false));
                postBuilder.header("codegpt_user", AppConstants.CODEGPT_TOKEN_USER);
                postBuilder.header("codegpt_token", tokenService.getTokenSystem(AppConstants.CODEGPT_TOKEN_USER).getToken());
            }
            HttpResponse<String> response = postBuilder.content(JSON.toJSONString(paramsJson))
                    .syncExecuteWithFullResponse(requestTimeOut);

            LOGGER.info("preRequest response:{}", response.body());

            // 只处理pipeline类型插件，api类型插件不校验结果
            boolean checkResponseSchema = PluginTypeEnum.PIPELINE.getName().equalsIgnoreCase(pluginType);
            BaseResponse responseCheckResult = checkResponseContent(checkResponseSchema, queryParams, AppConstants.PRE_REQUEST_STAGE, response, (Map<String, Object>) api.get("responses"));
            if (responseCheckResult.getErrorCode()!=0){
                sendApiStageErrorInfo(queryParams, stageIndex, responseCheckResult);
                return null;
            }
            Object responseBody;
            Boolean shortenFlag = (Boolean)api.get("shortenLink");
            if(shortenFlag!=null&&shortenFlag){
                responseBody = shortenLink(response,(Map<String, Object>) api.get("responses"));
            }else {
                responseBody = JSON.parse(response.body());
            }

            StageInfo apiStageOutputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.OUTPUT, "responseBody", responseBody);
            ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, uniqueAnswerId, answerUid, queryParams.getPluginIndex(), stageIndex,  StageTypeEnum.API.getName(), apiStageOutputInfo, ResponseEnum.SUCCESS.name());

            //前置请求后是否要等待
            Integer waitTime = (Integer) api.getOrDefault("waitTime", 0);
            LOGGER.info("preRequest need wait:{}", waitTime);
            if (waitTime != null && waitTime > 0) {
                TimeUnit.SECONDS.sleep(waitTime);
            }

            return responseBody;
        } catch (Exception e) {
            LOGGER.error("preRequest error", e);
            Map<String, Object> errorInfo = MapUtil.of("msg", String.format("调用接口过程发生异常, 异常类型：%s, 异常信息：%s", e.getClass().getName(), e.getMessage()));
            StageInfo apiStageOutputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.OUTPUT, "errorMessage", errorInfo);
            ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, uniqueAnswerId, answerUid, queryParams.getPluginIndex(), stageIndex, StageTypeEnum.API.getName(), apiStageOutputInfo, ResponseEnum.ERROR_THROW.name());
            return null;
        }
    }

    private void sendApiStageErrorInfo(PluginServiceRequestContext queryParams,  Integer stageIndex, BaseResponse paramsCheckResult) {
        Map<String, Object> errorMessage = MapUtil.ofEntries(
                MapUtil.entry("msg", paramsCheckResult.getErrorMsg()),
                MapUtil.entry("code", paramsCheckResult.getErrorCode())
        );
        StageInfo stageInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.OUTPUT, "errorMessage", errorMessage);
        ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), queryParams.getAnswerUid(), queryParams.getPluginIndex(), stageIndex, StageTypeEnum.API.getName(), stageInfo, paramsCheckResult.getErrorType().name());
    }

    /**
     * 大模型调用
     *
     * @param queryParams    用户提问
     * @param llmStageConfig 大模型调用阶段配置
     * @param params         请求参数
     * @param preResponse    前置请求返回结果
     * @return 大模型调用返回结果
     */
    @Override
    public String llm(Integer stageIndex, PluginServiceRequestContext queryParams, Map<String, Object> llmStageConfig, Map<String, Object> params, Map<String, Object> preResponse, boolean llmStageEnd) {
        //判断llmStageConfig是否是空的，如果是空的，要获取modelName，和promptTemplate，模型要从数据库中找到那个全员可见的模型，promptTemplate配置到drm中即可。
        AlgoBackendDO algoBackendDO = null;
        String messagesKey = null;
        String promptTemplate = null;
        String modelEnv = null;
        Integer maxTokens = null;
        Double temperature = null;
        Double topP = null;
        Integer topK = null;

        Boolean linXiMultipleRounds = false;
        OTHERS_LOGGER.info("start to parse llm parameters:{}",JSON.toJSONString(llmStageConfig));

        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        BeanUtil.copyProperties(queryParams.getChatCompletionRequest(), chatCompletionRequest);
        String query = ToolLearningUtil.getSimpleQuery(chatCompletionRequest.getMessages());
        if(llmStageConfig == null || llmStageConfig.isEmpty()){
            algoBackendDO = algoBackendService.getDefaultAlgoBackend();
            promptTemplate = codeGPTDrmConfig.getPluginLLMStageDefaultPromptTemplate();
        }else {
            String modelName = (String) llmStageConfig.get("modelName");
            algoBackendDO = algoBackendService.getAlgoBackendByName(modelName);
            messagesKey = (String) llmStageConfig.get("messages");
            promptTemplate = (String) llmStageConfig.get("promptTemplate");
            modelEnv = (String) llmStageConfig.get("modelEnv");
            maxTokens = (Integer) llmStageConfig.get("maxTokens");
            temperature = (Double) llmStageConfig.get("temperature");
            topP = (Double)llmStageConfig.get("topP");
            topK = (Integer) llmStageConfig.get("topK");

            Map<String, Object> implConfigMap = AlgoBackendUtil.getImplConfigMap(algoBackendDO);
            if(llmStageConfig.get("repetitionPenalty")!=null){
                implConfigMap.put(AlgoImplConfigKey.REPETITION_PENALTY,String.valueOf(llmStageConfig.get("repetitionPenalty")));
            }
            if(llmStageConfig.get("outSeqLength")!=null){
                implConfigMap.put(AlgoImplConfigKey.OUT_SEQ_LENGTH,String.valueOf(llmStageConfig.get("outSeqLength")));
            }
            algoBackendDO.setImplConfig(JSON.toJSONString(implConfigMap));

            if(llmStageConfig.containsKey("linXiMultipleRounds")){
                linXiMultipleRounds = (Boolean) llmStageConfig.get("linXiMultipleRounds");
            }
        }

        queryParams.setAlgoBackendDO(algoBackendDO);

        String systemPrompt = AlgoBackendUtil.exactSystemPromptConfig(algoBackendDO);
        ChatMessage systemMessage = new ChatMessage();
        systemMessage.setRole(ChatRoleEnum.SYSTEM.getName());
        systemMessage.setContent(systemPrompt == null ? "": systemPrompt);

        List<ChatMessage> pluginMessageList = new ArrayList<>();
        // 优先使用messages参数
        if (StringUtils.isNotBlank(messagesKey)){
            Matcher matcher = ToolLearningUtil.FSTRING_PATTERN.matcher(messagesKey);
            String key = null;
            if (matcher.matches()) {
                key = matcher.group(1);
            }
            if (key == null){
                LOGGER.error("parse messages error, messagesKey:{}", messagesKey);
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "messages参数格式错误，无法取到key");
            }
            Object messageObj = ToolLearningUtil.getValueFromPluginContext(key, query, params, preResponse, null, null);
            List<ChatMessage> messages;
            try {
                messages = JSON.parseArray(JSON.toJSONString(messageObj), ChatMessage.class);
            }catch (Exception e){
                LOGGER.error("parse messages error", e);
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "messages参数格式错误");
            }

            if(!ChatRoleEnum.SYSTEM.getName().equalsIgnoreCase(messages.get(0).getRole())){
                pluginMessageList.add(systemMessage);
            }

            pluginMessageList.addAll(messages);
        }else{
            pluginMessageList.add(systemMessage);

            ChatMessage userMessage = new ChatMessage();
            userMessage.setRole(ChatRoleEnum.USER.getName());
            String prompt = ToolLearningUtil.applyPromptTemplate(promptTemplate, query, params,preResponse, null, null);
            userMessage.setContent(prompt);
            pluginMessageList.add(userMessage);
        }

        chatCompletionRequest.setMessages(pluginMessageList);
        if(maxTokens != null){
            chatCompletionRequest.setMaxTokens(maxTokens);
        }
        if(temperature != null){
            chatCompletionRequest.setTemperature(temperature);
        }
        if(topP != null){
            chatCompletionRequest.setTopP(topP);
        }
        if(topK != null){
            chatCompletionRequest.setTopK(topK);
        }
        OTHERS_LOGGER.info("llm model: {}, chatRequest: {}", JSON.toJSONString(algoBackendDO),JSON.toJSONString(chatCompletionRequest));


        //内网版本才会展示模型信息
        if(codeGPTDrmConfig.isIntranetApplication()){
            StageInfo modelStageInputInfo = new StageInfo();
            modelStageInputInfo.setInput(new ModelStageInput(algoBackendDO.getModel(), promptTemplate));

            ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), queryParams.getAnswerUid(), queryParams.getPluginIndex(), stageIndex, StageTypeEnum.MODEL.getName(), modelStageInputInfo, null);
        }

        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = (chatStreamPartResponse)->{
            String finishReason = chatStreamPartResponse.getChoices().get(0).getFinishReason();
            //将模型推理阶段默认的结束原因stop改为success
            if(AppConstants.DEFAULT_STREAM_FINISH_REASON.equalsIgnoreCase(finishReason)){
                finishReason = ResponseEnum.SUCCESS.name();
            }

            CheckResultModel checkResultModel = chatStreamPartResponse.getChoices().get(0).getCheckResultModel();
            if(checkResultModel != null && !checkResultModel.isAllCheckRet()){
                finishReason = checkResultModel.getResponseEnum().name();
            }

            String content = chatStreamPartResponse.getChoices().get(0).getDelta().getContent();

            if (llmStageEnd){
                 ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), queryParams.getAnswerUid(), content, finishReason,null);
            }else{
                StageInfo stageInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.MODEL, StageInfo.StageInfoTypeEnum.OUTPUT, "llmResult", content);
                ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), queryParams.getAnswerUid(), queryParams.getPluginIndex(), stageIndex, StageTypeEnum.MODEL.getName(), stageInfo, finishReason);
            }
        };

        StringBuilder llmResult = new StringBuilder();

        // 更新审核开关
        algoModelUtilService.updateCheckSwitch(chatCompletionRequest, queryParams.getChatSessionDO(), algoBackendDO.getModel());
        // 确保调用灵犀的时候不需要上下文能力
        // fix npe
        if(chatCompletionRequest.getChatRequestExtData() == null){
            chatCompletionRequest.setChatRequestExtData(new ChatRequestExtData());
        }
        chatCompletionRequest.getChatRequestExtData().setLingXiMultipleRounds(linXiMultipleRounds);



        GptAlgModelServiceRequest gptAlgModelServiceRequest = new GptAlgModelServiceRequest(queryParams.getRequestId()
                , queryParams.getUserName()
                , queryParams.isStressTest()
                , queryParams.getAlgoBackendDO()
                , chatCompletionRequest
                , (streamResponseModel -> llmResult.append(streamResponseModel.getAnswerMessage().getContent())),
                queryParams.isRegenerate());
        gptAlgModelServiceRequest.setChatStreamPartResponseHandler(chatStreamPartResponseConsumer);
        gptAlgModelServiceRequest.setUniqueAnswerId(queryParams.getUniqueAnswerId());
        gptAlgModelServiceRequest.setModelEnv(modelEnv);

        // 大模型调用是最后一个阶段，直接发送一个空包，因为模型的流式输出要作为答案
        if (llmStageEnd){
            StageInfo stageInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.MODEL, StageInfo.StageInfoTypeEnum.OUTPUT, "llmResult", "");
            ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), queryParams.getAnswerUid(), queryParams.getPluginIndex(), stageIndex, StageTypeEnum.MODEL.getName(), stageInfo, ResponseEnum.SUCCESS.name());
        }

        //流式传输，要把response设置为流式
        AlgoModelExecutor.getInstance().executorStreamChat(algoBackendDO, gptAlgModelServiceRequest);

        return llmResult.toString();
    }

    /**
     * 后置请求
     *
     * @param queryParams            用户提问
     * @param postRequestStageConfig 后置请求阶段配置
     * @param params                 请求参数
     * @param preResponse            前置请求返回结果
     * @param llmResult              大模型调用返回结果
     * @return 后置请求返回结果
     */
    @Override
    public Map<String, Object> postRequest(Integer stageIndex, PluginServiceRequestContext queryParams, Map<String, Object> postRequestStageConfig, Map<String, Object> params, Map<String, Object> preResponse, String llmResult, long requestTimeOut) {
        String uniqueAnswerId = queryParams.getUniqueAnswerId();
        String answerUid = queryParams.getAnswerUid();

        try {
            // 获取url
            String url = (String) postRequestStageConfig.get("url");

            // 获取参数
            JSONObject paramsJson = new JSONObject();
            paramsJson.put("params", params);
            paramsJson.put("preResponse", preResponse);
            paramsJson.put("llmResult", llmResult);

            LOGGER.info("postRequest: url:{}, params:{}", url, paramsJson.toJSONString());
            // 发送请求
            if(codeGPTDrmConfig.isIntranetApplication()){
                StageInfo apiStageInputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.INPUT, "requestBody", paramsJson);
                ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, uniqueAnswerId, answerUid, queryParams.getPluginIndex(), stageIndex, StageTypeEnum.API.getName(), apiStageInputInfo, null);
            }

            PostBuilder postBuilder = HttpClient.post(url);
            if (url.startsWith("https://codegencore")) {
                postBuilder.header(AUTH_TOKEN, configService.getConfigByKey(AUTH_TOKEN, false));
                postBuilder.header("codegpt_user", AppConstants.CODEGPT_TOKEN_USER);
                postBuilder.header("codegpt_token", tokenService.getTokenSystem(AppConstants.CODEGPT_TOKEN_USER).getToken());
            }
            HttpResponse<String> response = postBuilder.content(paramsJson.toJSONString())
                    .syncExecuteWithFullResponse(requestTimeOut);
            LOGGER.info("postRequest response:{}", response.body());

            BaseResponse responseCheckResult = checkResponseContent(true, queryParams, AppConstants.POST_REQUEST_STAGE, response, (Map<String, Object>) postRequestStageConfig.get("responses"));
            // 处理返回值
            if (responseCheckResult.getErrorCode()!=0){
                sendApiStageErrorInfo(queryParams, stageIndex, responseCheckResult);
                return null;
            }
            JSONObject responseBody;
            Boolean shortenFlag = (Boolean)postRequestStageConfig.get("shortenLink");
            if(shortenFlag!=null&&shortenFlag){
                responseBody = (JSONObject) shortenLink(response,(Map<String, Object>) postRequestStageConfig.get("responses"));
            }else {
                responseBody = JSON.parseObject(response.body());
            }
            StageInfo apiStageOutputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.OUTPUT, "responseBody", responseBody);
            ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, uniqueAnswerId, answerUid, queryParams.getPluginIndex(), stageIndex, StageTypeEnum.API.getName(), apiStageOutputInfo, ResponseEnum.SUCCESS.name());
            return responseBody;
        } catch (Exception e) {
            LOGGER.error("postRequest error", e);
            Map<String, Object> errorInfo = MapUtil.of("msg", String.format("调用接口过程发生异常, 异常类型：%s, 异常信息：%s", e.getClass().getName(), e.getMessage()));
            StageInfo apiStageOutputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.API, StageInfo.StageInfoTypeEnum.OUTPUT, "errorMessage", errorInfo);
            ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, uniqueAnswerId, answerUid, queryParams.getPluginIndex(), stageIndex, StageTypeEnum.API.getName(), apiStageOutputInfo, ResponseEnum.ERROR_THROW.name());
        }
        return null;
    }

    /**
     * 模型总结
     *
     * @param queryParams        用户提问
     * @param summaryStageConfig 模型总结阶段配置
     * @param params             请求参数
     * @param preResponse        前置请求返回结果
     * @param llmResult          大模型调用返回结果
     * @param postResponse       后置请求返回结果
     * @return 模型总结返回结果
     */
    @Override
    public String summary(Integer stageIndex, PluginServiceRequestContext queryParams, Map<String, Object> summaryStageConfig, Map<String, Object> params, Map<String, Object> preResponse, String llmResult, Map<String, Object> postResponse) {
        String template = (String) summaryStageConfig.get("template");

        //只有内网版本才展示答案模板
        if(codeGPTDrmConfig.isIntranetApplication()){
            StageInfo templateStageInputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.TEMPLATE, StageInfo.StageInfoTypeEnum.INPUT, "template", template);
            ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), queryParams.getAnswerUid(), queryParams.getPluginIndex(), stageIndex, StageTypeEnum.TEMPLATE.getName(), templateStageInputInfo, null);
        }

        streamWait(DEFAULT_STREAM_WAIT_TIME);

        String query = ToolLearningUtil.getSimpleQuery(queryParams.getChatCompletionRequest().getMessages());
        String summary = ToolLearningUtil.applyPromptTemplate(template, query, params, preResponse, llmResult, postResponse);

        StageInfo templateStageOutputInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.TEMPLATE, StageInfo.StageInfoTypeEnum.OUTPUT, "result", summary);
        ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), queryParams.getAnswerUid(), queryParams.getPluginIndex(), stageIndex, StageTypeEnum.TEMPLATE.getName(), templateStageOutputInfo, ResponseEnum.SUCCESS.name());

        return summary;
    }

    /**
     * 对话
     *
     * @param params 请求参数
     * @return
     */
    @Override
    public String chat(PluginServiceRequestContext params) {
        return null;
    }

    /**
     * 流式对话
     * @param queryParams 用户提问
     * @param pluginDO 数据库中的插件信息
     */
    public void streamChat(PluginServiceRequestContext queryParams, PluginDO pluginDO) {
        // 集成 agentSecSdk.callTool, 判断用户是否有 工具/插件 使用权限, 根据 agentSecSdk.callTool 返回结果做相应处理
        secAgentHelper.checkPluginAuth(queryParams,pluginDO,queryParams.getNextFunctionCall());
        Map<String, Object> workFlowConfig = pluginConfigService.parseWorkFlowYaml(pluginDO.getWorkflowConfig());
        List<ChatMessage> messages = queryParams.getChatCompletionRequest().getMessages();

        //开启线程去调用插件主流程
        //主线程从tbase中取数据，然后写到servletResponse中去
        //检查提问是否符合安全审查规范
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(queryParams.getRequestId(), messages, queryParams.getChatCompletionRequest().getChatRequestExtData(),false);

        if (!requestCheckResultModel.isAllCheckRet()) {

            ToolLearningUtil.stopStream( null, null, requestCheckResultModel, queryParams.getPluginStreamPartResponseConsumer(), queryParams.getPluginResultHandler(), null, null, algoModelUtilService.getCheckFailedMsg(requestCheckResultModel),null);
            return;
        }
        appThreadPool.execute(()-> {
            try{
                LOGGER.info("pluginMainWorkflow start, pluginName: {}", pluginDO.getName());

                pluginMainWorkflow(queryParams, workFlowConfig, false, false);
            }catch (Throwable e){
                LOGGER.error("pluginMainWorkflow error", e);
            }
        });
        getChatDataFromTBase(queryParams, pluginDO, workFlowConfig);
    }

    @Override
    public String pluginMainWorkflow(PluginServiceRequestContext queryParams,
                                     Map<String, Object> workFlowConfig,
                                     boolean decideByLLM,
                                     boolean isContinue){
        String answerUid = queryParams.getAnswerUid();
        Map<String, Object> pluginBasicInfo = (Map<String, Object>)workFlowConfig.get("info");
        String pluginType = (String) pluginBasicInfo.getOrDefault("type", PluginTypeEnum.PIPELINE.getName());
        long requestTimeout = calRequestTimeout(workFlowConfig);

        //取出params和preRequest阶段配置，调用preRequest阶段，得到preResponse
        List<Map<String, Object>> paramSchemaList = (List<Map<String, Object>>) workFlowConfig.get("params");
        List<Map<String, Object>> stageConfigList = (List<Map<String, Object>>) workFlowConfig.get("stages");
        List<String> stageList = new ArrayList<>();
        Map<String, Object> preRequestStageConfig = null;
        Map<String, Object> llmStageConfig = null;
        Map<String, Object> postRequestStageConfig = null;
        Map<String, Object> summaryStageConfig = null;


        for (int i = 0; i < stageConfigList.size(); i++) {
            Map<String, Object> config = stageConfigList.get(i);
            String stageName = String.format("步骤%s", CommonTools.number2Chinese(i + 1));
            stageList.add(stageName);

            switch ((String) config.get("name")){
                case "preRequest":
                    preRequestStageConfig = config;
                    break;
                case "llm":
                    llmStageConfig = config;
                    break;
                case "postRequest":
                    postRequestStageConfig = config;
                    break;
                case "summary":
                    summaryStageConfig = config;
                    break;
                default:
                    break;
            }
        }

        if (!isContinue) {
            StageInfo stageInfo = ToolLearningUtil.buildStageInfo(StageTypeEnum.FUNCTION_CALL, StageInfo.StageInfoTypeEnum.OUTPUT, "name", pluginBasicInfo.get("name"));
            ToolLearningUtil.addStageInfo(stageInfo, StageTypeEnum.FUNCTION_CALL, StageInfo.StageInfoTypeEnum.OUTPUT, "arguments", JSON.toJSONString(queryParams.getPluginParams()));

            NewPluginStreamPartResponse streamData = new NewPluginStreamPartResponse(answerUid, ResponseEnum.SUCCESS.name(), queryParams.getPluginIndex(), null, StageTypeEnum.FUNCTION_CALL.getName(), stageInfo);
            PluginInfo pluginInfo = queryParams.getPluginInfo();
            pluginInfo.setType(pluginType);

            streamData.setPluginInfo(pluginInfo);
            streamData.setStageList(stageList);
            ToolLearningUtil.sendMessageToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), streamData);
        }


        int stageIndex = 0;
        //初期的时候除了query之外只有默认参数，后续要把param参数加进去
        Object preResponseObject = null;
        Map<String, Object> preResponse = null;
        if(preRequestStageConfig != null){
            preResponseObject = preRequest(pluginType, stageIndex, queryParams, paramSchemaList, preRequestStageConfig, queryParams.getPluginParams(), isContinue, requestTimeout);
            if(preResponseObject == null){
                LOGGER.info("preRequest阶段返回结果为空，直接返回");
                return null;
            }
            if(PluginTypeEnum.API.getName().equalsIgnoreCase (pluginType)){
                LOGGER.info("api 类型的插件在前置接口阶段结束，不会调用后续阶段，用接口返回作为最终答案");
                return JSON.toJSONString(preResponseObject);
            }
            preResponse = (Map<String, Object>)preResponseObject;
            stageIndex++;
        }

        //获取场景中的信息，如果场景只有一个插件，而且llm阶段配置为空的情况下才会走默认模型行为，不然就直接返回了

        String llmResult = null;
        boolean llmStageEnd = postRequestStageConfig==null && summaryStageConfig==null;
        if(llmStageConfig != null){
            // 没有pre请求阶段，需要在llm做请求参数校验
            if(preResponse == null){
                Map<String, Object> paramsJson = new LinkedHashMap<>();
                paramsJson.putAll(queryParams.getPluginParams());
                BaseResponse paramsCheckResult = checkAndAddPreRequestParams(queryParams,paramsJson, paramSchemaList, queryParams.getPluginParams());
                if (paramsCheckResult.getErrorCode()!=0){
                    sendApiStageErrorInfo(queryParams, stageIndex, paramsCheckResult);
                    return null;
                }
                queryParams.setPluginParams(paramsJson);
            }
            llmResult = llm(stageIndex, queryParams, llmStageConfig, queryParams.getPluginParams(), preResponse, llmStageEnd);
            stageIndex++;

            if(llmResult == null ){
                LOGGER.info("llm阶段返回结果为空，直接返回");
                ToolLearningUtil.sendStageInfoToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, queryParams.getPluginIndex(), stageIndex, StageTypeEnum.MODEL.getName(), null, ResponseEnum.AI_CALL_ERROR.name());
                return null;
            }
        }

        if(llmStageEnd){
            LOGGER.info("后续阶段配置为空，直接返回模型推理结果作为答案");
            // ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, llmResult, ResponseEnum.SUCCESS.name(),null);
            return llmResult;
        }

        Map<String, Object> postResponse = null;
        if(postRequestStageConfig!= null){
            postResponse = postRequest(stageIndex, queryParams, postRequestStageConfig, queryParams.getPluginParams(), preResponse, llmResult, requestTimeout);
            stageIndex++;
            if (postResponse == null){
                LOGGER.info("postRequest阶段返回结果为空，直接返回");
                return null;
            }
        }

        String summary = summary(stageIndex, queryParams, summaryStageConfig, queryParams.getPluginParams(), preResponse, llmResult, postResponse);
        ToolLearningUtil.sendAnswerToTbase(noneSerializationCacheManager, queryParams.getUniqueAnswerId(), answerUid, summary, ResponseEnum.SUCCESS.name(),null);
        return summary;
    }


    /**
     * 1、从tbase中获取数据
     * 2、分段送审
     * 3、flush数据给调用方
     * 4、写入gptCatch(如需要)
     *
     * @param params
     */
    public void getChatDataFromTBase(PluginServiceRequestContext params,
                                     PluginDO pluginDO,
                                     Map<String, Object> workflowConfig) {
        //超时时间设置不同，前置接口等待时间，后置接口等待时间，模型等待时间
        //结束条件判定有所不同，需要先获取最后的阶段是什么，然后当currentStage为那个阶段并且有finishReason的时候结束
        //异常结束，每个阶段有finishReason，但是finishReason不是SUCCESS的时候，结束
        Consumer<NewPluginStreamPartResponse> pluginStreamPartResponseConsumer = params.getPluginStreamPartResponseConsumer();
        ChatRequestExtData chatRequestExtData = params.getChatCompletionRequest().getChatRequestExtData();
        String sessionUid = chatRequestExtData != null ? chatRequestExtData.getSessionUid() : null;
        String bizId = chatRequestExtData != null ? chatRequestExtData.getBizId() : null;

        String requestId = params.getRequestId();
        String uniqueAnswerId = params.getUniqueAnswerId();
        Consumer<StreamResponseModel> pluginResultHandler = params.getPluginResultHandler();
        String key = String.format("%s%s", AppConstants.PLUGIN_STREAM_DATA_PREFIX, uniqueAnswerId);

        PluginLogGroup pluginLogGroup = new PluginLogGroup();

        long waitTimeOut = calWaitTimeout(workflowConfig);
        long waitStartTime = System.currentTimeMillis();
        String answerUid = ShortUid.getUid();
        ChatStreamBuffer chatStreamBuffer = new ChatStreamBuffer();
        while (true) {
            if (System.currentTimeMillis() - waitStartTime > waitTimeOut) {
                CHAT_LOGGER.warn("get plugin stream data timeout, requestId:{}, waitTime:{}ms, sessionUid:{}, bizId:{}", requestId, System.currentTimeMillis() - waitStartTime, sessionUid, bizId);
                NewPluginStreamPartResponse abnormalPluginStreamPartResponse = new NewPluginStreamPartResponse();
                abnormalPluginStreamPartResponse.setFinishReason(ResponseEnum.ANSWER_OUTPUT_TIME_OUT.name());
                abnormalPluginStreamPartResponse.setContent(ResponseEnum.ANSWER_OUTPUT_TIME_OUT.getErrorMsg());
                pluginStreamPartResponseConsumer.accept(abnormalPluginStreamPartResponse);
                ToolLearningUtil.addStageInfoToPluginLog(pluginLogGroup, abnormalPluginStreamPartResponse);
                processFinish(abnormalPluginStreamPartResponse, chatStreamBuffer, pluginLogGroup, key, answerUid, pluginResultHandler, params.getNeedDelTBaseKey());
                return;
            }

            String streamStr = ChatUtils.safeTbaseLpop(noneSerializationCacheManager, key);
            if (streamStr == null) {
                try {
                    Thread.sleep(codeGPTDrmConfig.getPluginStreamDataPollingStep());
                } catch (InterruptedException e) {
                    LOGGER.warn("sleep failed", e);
                    continue;
                }
                continue;
            }
            waitStartTime = System.currentTimeMillis();
            LOGGER.debug("get plugin stream data: {}", streamStr);

            NewPluginStreamPartResponse streamPartResponse = JSON.parseObject(streamStr, NewPluginStreamPartResponse.class);
            // 当监听到用户cancel操作时， 补全数据结构， 通知前端并将"用户主动取消"落库
            if(StringUtils.isNotBlank(streamPartResponse.getFinishReason())
                    && ResponseEnum.USER_CANCELED.name().equalsIgnoreCase(streamPartResponse.getFinishReason())) {
                streamPartResponse.setContent(ResponseEnum.USER_CANCELED.getErrorMsg());
                streamPartResponse.setFinishReason(ResponseEnum.USER_CANCELED.name());
                pluginStreamPartResponseConsumer.accept(streamPartResponse);
                ToolLearningUtil.addStageInfoToPluginLog(pluginLogGroup, streamPartResponse);
                processFinish(streamPartResponse, chatStreamBuffer, pluginLogGroup, key, answerUid, pluginResultHandler, params.getNeedDelTBaseKey());
                return;
            }

            ToolLearningUtil.addStageInfoToPluginLog(pluginLogGroup, streamPartResponse);
            pluginStreamPartResponseConsumer.accept(streamPartResponse);
            if (StageTypeEnum.ANSWER.getName().equalsIgnoreCase(streamPartResponse.getType())){
                chatStreamBuffer.buffer(streamPartResponse.getContent());
            }

            //结束处理
            if(ToolLearningUtil.isPluginCallEnd(streamPartResponse)){
                processFinish(streamPartResponse,chatStreamBuffer, pluginLogGroup, key, answerUid, pluginResultHandler, params.getNeedDelTBaseKey());
                return;
            }
        }
    }

    private long calWaitTimeout(Map<String, Object> workFlowConfig) {
        int waitTimeOut = codeGPTDrmConfig.getPluginCommonStreamDataWaitTime();

        Map<String, Object> pluginBasicInfo = (Map<String, Object>)workFlowConfig.get("info");

        if(null == pluginBasicInfo || null == pluginBasicInfo.get("waitTimeOut")) {
            return waitTimeOut;
        }

        long waitTimeOutInWorkflow = (Integer)pluginBasicInfo.get("waitTimeOut");
        if(0 == waitTimeOutInWorkflow) {
            return waitTimeOut;
        }

        return waitTimeOutInWorkflow;
    }

    private long calRequestTimeout(Map<String, Object> workFlowConfig) {
        int waitTimeOut = codeGPTDrmConfig.getPluginRequestDefaultTimeOut();

        Map<String, Object> pluginBasicInfo = (Map<String, Object>)workFlowConfig.get("info");

        if(null == pluginBasicInfo || null == pluginBasicInfo.get("requestTimeOut")) {
            return waitTimeOut;
        }

        long waitTimeOutInWorkflow = (Integer)pluginBasicInfo.get("requestTimeOut");
        if(0 == waitTimeOutInWorkflow) {
            return waitTimeOut;
        }

        return waitTimeOutInWorkflow;
    }

    /**
     * 获取最后一个阶段
     * @param workFlowConfig 工作流配置
     * @return
     */
    private String getEndStage(Map<String, Object> workFlowConfig){
        List<Map<String, Object>> stageConfigList = (List<Map<String, Object>>) workFlowConfig.get("stages");
        Map<String, Object> lastStage = stageConfigList.get(stageConfigList.size() - 1);
        String lastStageName = (String) lastStage.get("name");

        //llm阶段即使不配置也会存在
        if(AppConstants.PRE_REQUEST_STAGE.equalsIgnoreCase(lastStageName)){
            return AppConstants.LLM_STAGE;
        }
        return lastStageName;
    }

    /**
     * 获取llm阶段模型
     * @param workFlowConfig
     * @return
     */
    private AlgoBackendDO getLLMStageModel(Map<String, Object> workFlowConfig){
        List<Map<String, Object>> stageConfigList = (List<Map<String, Object>>) workFlowConfig.get("stages");
        String modelName = null;
        for(Map<String, Object> stageConfig : stageConfigList){
            String stageName = (String) stageConfig.get("name");
            if(AppConstants.LLM_STAGE.equalsIgnoreCase(stageName)){
                modelName = (String) stageConfig.get("modelName");
                break;
            }
        }
        if(modelName == null){
            return algoBackendService.getDefaultAlgoBackend();
        }else{
            return algoBackendService.getAlgoBackendByName(modelName);
        }
    }

    /**
     * 给content添加代码块标识
     * @param content
     * @return
     */
    private String getCodeBlockContent(String content) {
        return getCodeBlockContent(content, "");
    }

    /**
     * 给content添加代码块标识
     * @param content
     * @return
     */
    private String getCodeBlockContent(String content, String language) {
        return String.format("```%s\n%s\n```", language, content);
    }

    /**
     * 校验object的数据类型
     * @param object
     * @param type
     * @return
     */
    private boolean checkObjectType(Object object, String type) {
        switch(type) {
            case "string":
                return object instanceof String;
            case "integer":
                return object instanceof Integer;
            case "boolean":
                return object instanceof Boolean;
            default:
                return true;
        }
    }

    /**
     * 校验并添加请求http的参数，如果错误则向tbase写入错误信息
     * @param paramsJson 请求http接口的参数，paramsJson是引用传递，可以在校验过程中put参数
     * @param paramSchemaList
     * @param params
     * @return
     */
    private BaseResponse checkAndAddPreRequestParams(PluginServiceRequestContext queryParams, Map<String, Object> paramsJson, List<Map<String, Object>> paramSchemaList, Map<String, Object> params) {
        for (Map<String, Object> paramMap : paramSchemaList) {
            String paramKey = (String) paramMap.get("name");
            String type = (String) ((Map) paramMap.get("schema")).get("type");
            Boolean required = (Boolean) paramMap.get("required");
            Object defaultVal = paramMap.get("default");
            // 匹配 ${ 和 } 之间的内容,如果defaultVal是类似${sessionUid}格式的，给默认赋值
            if (defaultVal != null) {
                Matcher matcher = FunctionCallServiceImpl.DEFAULT_PARAM_PATTERN.matcher(defaultVal.toString());
                if (matcher.matches()) {
                    String key = matcher.group(2);
                    JSONObject sessionContext = algoModelUtilService.getSessionContext(queryParams);
                    if(sessionContext != null){
                        defaultVal = sessionContext.get(key);
                    }
                }
            }
            Object keyValue = params!=null && params.get(paramKey) != null ? params.get(paramKey) : defaultVal;

            if (keyValue == null) {
                if (!required) {
                    continue;
                }
                return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, String.format("请求缺少必要参数，缺少的参数为：%s", paramKey));
            }

            // 对keyValue的类型进行判断，参数类型不匹配报错
            if (!checkObjectType(keyValue, type)) {
                return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, String.format("请求参数类型错误，具体参数为：%s，类型应为: %s, 当前参数值为: %s", paramKey, type, keyValue));
            }

            paramsJson.put(paramKey, keyValue);
        }
        return BaseResponse.build(ResponseEnum.SUCCESS);
    }

    /**
     * 校验http请求返回的response
     * @param stage
     * @param response 请求接口返回的response
     * @param responsesConfig 阶段配置里的response配置
     * @return
     */
    private BaseResponse checkResponseContent(boolean checkResponseSchema, PluginServiceRequestContext queryParams, String stage, HttpResponse<String> response, Map<String, Object> responsesConfig) {

        // 处理返回结果
        if (response == null) {
            return BaseResponse.build(ResponseEnum.HTTP_ERROR, String.format("请求%s接口错误，response为null", stage));
        }

        if(response.statusCode()< 200 && response.statusCode() >= 300){
            return BaseResponse.build(ResponseEnum.HTTP_ERROR, String.format("请求%s接口错误，状态码为%d, 错误信息为: \n%s\n", stage, response.statusCode(), getCodeBlockContent(response.body())));
        }

        // 根据配置校验http请求返回值
        // 解析responses配置
        Map<String, Object> contentMap = (Map<String, Object>) ((Map<String, Object>) responsesConfig.get("200")).get("content");
        Map<String, Object> schemaMap = (Map<String, Object>) contentMap.get("application/json");
        Map<String, Object> schema = (Map<String, Object>) schemaMap.get("schema");

        List<String> requiredList = (List<String>) schema.get("required");
        Map<String, Object> updateToContextInfo = (Map<String, Object>) schema.get("updateToContext");
        Map<String, Schema> propMap = JSON.parseObject(JSON.toJSONString(schema.get("properties")), new TypeReference<>() {
        });

        if(propMap == null){
            LOGGER.info("response不是map结构，跳过response校验和上下文更新， 可以查看插件:{}的返回值schema", queryParams.getPluginInfo().getName());
            return BaseResponse.build(ResponseEnum.SUCCESS);
        }

        ChatSessionDO chatSessionDO = queryParams.getChatSessionDO();
        JSONObject responseJson;
        try {
            responseJson = JSON.parseObject(response.body());
        }catch (Exception e){
            //TODO: 后续上下文更新可能也要支持非map结构的response，用json path取值，比如 $[0].name
            LOGGER.info("response不是map结构，跳过response校验和上下文更新， response: {}", response.body());
            return BaseResponse.build(ResponseEnum.SUCCESS);
        }

        for (Map.Entry<String, Schema> entry : propMap.entrySet()) {
            String contentKey = entry.getKey();
            String keyType = entry.getValue().getType();
            Object keyValue = responseJson.get(contentKey);
            if(checkResponseSchema){
                // 校验为空参数是否为必须参数
                if (keyValue == null) {
                    if (requiredList.contains(contentKey)) {
                        LOGGER.error("缺失必要参数: {}", contentKey);
                        return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, String.format("调用失败，接口返回结果缺少必要参数，具体参数key为: %s，response如下\n%s\n", contentKey, getCodeBlockContent(JSON.toJSONString(responseJson, SerializerFeature.PrettyFormat))));
                    }
                    continue;
                }
                // 校验参数类型
                if (!checkObjectType(keyValue, keyType)) {
                    LOGGER.error("参数类型错误，key: {}, value:{}，期待类型:{}", contentKey, keyValue, keyType);
                    return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER,  String.format("接口返回结果参数类型错误，具体参数key为：%s，参数类型应为: %s， response如下\n%s\n", contentKey, keyType, getCodeBlockContent(response.body())));
                }
            }

            //更新上下文信息
            //TODO: 上下文更新放到checkResponse函数中逻辑比较奇怪，需要重构
            if(chatSessionDO!=null){
                updateToSessionContext(updateToContextInfo, responseJson, chatSessionDO, contentKey);
            }
        }

        if(chatSessionDO!=null){
            updateSessionContextByResponseInfo(responseJson, chatSessionDO);
            dumpSessionContextToDb(chatSessionDO);
        }
        return BaseResponse.build(ResponseEnum.SUCCESS);
    }

    private void processFinish(NewPluginStreamPartResponse pluginStreamPartResponse,
                               ChatStreamBuffer chatStreamBuffer,
                               PluginLogGroup pluginLogGroup,
                               String key,
                               String answerUid,
                               Consumer<StreamResponseModel> pluginResultHandler,
                               Consumer<String> needDelTBaseKey) {
        LOGGER.info("plugin work flow finished, finishReason:{}", pluginStreamPartResponse.getFinishReason());

        String serviceAbnormalResp = null;
        String answer = null;

        if(ResponseEnum.SUCCESS.name().equalsIgnoreCase(pluginStreamPartResponse.getFinishReason())){
            chatStreamBuffer.flush();
            answer = chatStreamBuffer.getContent().toString();
        }else{
            //失败的情况处理
            serviceAbnormalResp = pluginStreamPartResponse.getFinishReason();
            if(ResponseEnum.USER_CANCELED.name().equalsIgnoreCase(serviceAbnormalResp)){
                answer = ResponseEnum.USER_CANCELED.getErrorMsg();
            }else{
                answer = "插件调用失败，请稍后重试:"+pluginStreamPartResponse.getContent();
            }

            CheckResultModel checkResult = pluginStreamPartResponse.getCheckResultModel();
            if (checkResult!=null && !checkResult.isAllCheckRet()){
                answer = algoModelUtilService.getCheckFailedMsg(checkResult);
            }
        }

        StreamResponseModel streamResponseModel = new StreamResponseModel();
        streamResponseModel.setAnswerUid(answerUid);
        streamResponseModel.setPluginLogGroup(pluginLogGroup);
        streamResponseModel.setAnswerMessage(new ChatMessage(ChatRoleEnum.ASSISTANT.getName() ,answer));
        streamResponseModel.setServiceAbnormalResp(serviceAbnormalResp);

        if (pluginResultHandler != null) {
            pluginResultHandler.accept(streamResponseModel);
        }
        if (needDelTBaseKey != null) {
            needDelTBaseKey.accept(key);
        }
    }

    /**
     * 流式传输的时候等待，用于让前端刷新的不要太快
     * @param waitTime 等待时间，单位是毫秒
     */
    private void streamWait(long waitTime){
        try {
            Thread.sleep(waitTime);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private void updateToSessionContext(Map<String, Object> updateToContextInfo,
                                        JSONObject responseJson,
                                        ChatSessionDO chatSessionDO,
                                        String contentKey){
        if(updateToContextInfo!=null && updateToContextInfo.containsKey(contentKey)){
            Object updateToContextValue = updateToContextInfo.get(contentKey);
            if(updateToContextValue == null){
                SessionUtils.deleteSessionConfig(chatSessionDO, contentKey);
            }else if(updateToContextValue instanceof String){
                Matcher matcher = FunctionCallServiceImpl.DEFAULT_PARAM_PATTERN.matcher(updateToContextValue.toString());
                if (matcher.matches()) {
                    String responseKey = matcher.group(2);
                    Object responseValue = responseJson.get(responseKey);
                    SessionUtils.addSessionConfig(chatSessionDO, contentKey, responseValue);
                }
            }else{
                SessionUtils.addSessionConfig(chatSessionDO, contentKey, updateToContextValue);
            }
        }
    }

    /**
     *
     * @param responseJson
     * @param chatSessionDO
     */
    private void updateSessionContextByResponseInfo(Map<String, Object> responseJson, ChatSessionDO chatSessionDO){
        if(responseJson.containsKey("updateToContext")){
            JSONObject updateToContextInfo = (JSONObject) responseJson.get("updateToContext");
            for(String key : updateToContextInfo.keySet()){
                Object updateToContextValue = updateToContextInfo.get(key);
                if(updateToContextValue == null){
                    SessionUtils.deleteSessionConfig(chatSessionDO, key);
                } else{
                    SessionUtils.addSessionConfig(chatSessionDO, key, updateToContextValue);
                }
            }
        }
    }

    /**
     * 把会话上下文存储到数据库
     * @param chatSessionDO
     */
    private void dumpSessionContextToDb(ChatSessionDO chatSessionDO){
        ChatSessionDO updateDO = new ChatSessionDO();
        updateDO.setUid(chatSessionDO.getUid());
        updateDO.setExtInfo(chatSessionDO.getExtInfo());
        chatSessionManageService.updateSession(updateDO);
    }
    /**
     * 将长链接转短链
     * <AUTHOR>
     * @since 2023.11.01
     * @param response response
     * @param responsesConfig responsesConfig
     * @return com.alibaba.fastjson.JSONObject
     */
    private Object shortenLink(HttpResponse<String> response,Map<String, Object> responsesConfig){
        // 解析responses配置
        Map<String, Object> contentMap = (Map<String, Object>) ((Map<String, Object>) responsesConfig.get("200")).get("content");
        Map<String, Object> schemaMap = (Map<String, Object>) contentMap.get("application/json");
        Map<String, Object> schema = (Map<String, Object>) schemaMap.get("schema");
        Map<String, Schema> propMap = JSON.parseObject(JSON.toJSONString(schema.get("properties")), new TypeReference<>() {
        });
        if(propMap == null){
            LOGGER.info("response不是map结构，跳过链接缩短");
            return JSON.parse(response.body());
        }
        JSONObject responseJson;
        try {
            responseJson = JSON.parseObject(response.body());
        }catch (Exception e){
            //TODO: 后续上下文更新可能也要支持非map结构的response，用json path取值，比如 $[0].name
            LOGGER.info("response不是map结构，跳过链接缩短， response: {}", response.body());
            return JSON.parse(response.body());
        }
        for (Map.Entry<String, Schema> entry : propMap.entrySet()) {
            String contentKey = entry.getKey();
            Object keyValue = responseJson.get(contentKey);
            if (keyValue instanceof String && HTTP_PATTERN.matcher((String) keyValue).matches() && ((String) keyValue).length() > codeGPTDrmConfig.getLinkLengthConfig()) {
                String url = shortenLink.createShort((String) keyValue);
                if (StringUtils.isNotBlank(url)) {
                    responseJson.put(contentKey, url);
                }
            }
        }
        return responseJson;
    }

    /**
     * 校验参数，看用户是否需要填写表单
     * @return
     */
    private boolean checkFormData(PluginServiceRequestContext queryParams, PluginDO pluginDO, Map<String, Object> paramsJson, boolean isContinue, boolean userConfirm) {
        // 通过配置看是否需要表单，即使参数正确也要有表单
        if (isContinue == false && userConfirm == true) {
            return  false;
        }

        // jsonSchema
        ChatFunction chatFunction = ToolLearningUtil.exactFunctionInfoFromPlugin(pluginDO, false);

        // required
        List<String> requiredList = chatFunction.getParameters().getObject("required", List.class);

        // 校验，先只校验第一层key是否存在
        for (Map.Entry<String, Object> entry : chatFunction.getParameters().getJSONObject("properties").entrySet()) {
            String key = entry.getKey();
            Map<String, Object> entryValue = (Map<String, Object>) entry.getValue();
            // 如果params存在key或者key不是required，则该字段校验通过
            if (paramsJson.get(key) != null || !requiredList.contains(key)) {
                continue;
            }
            // params不存在，查看default是否存在
            if (!entryValue.containsKey("default")) {
                return false;
            } else {
                Object defaultValue = entryValue.get("default");
                // default存在还要看{sessionKey}的值是否能取到，如果查找不到校验不通过
                Matcher matcher = FunctionCallServiceImpl.DEFAULT_PARAM_PATTERN.matcher(defaultValue.toString());
                if (matcher.matches()) {
                    String sessionKey = matcher.group(2);
                    JSONObject sessionContext = algoModelUtilService.getSessionContext(queryParams);
                    if (sessionContext == null || !sessionContext.containsKey(sessionKey)) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取全部参数（包括默认参数）
     *
     * @param queryParams
     * @param paramsJson
     * @return
     */
    private String getAllArguments(PluginServiceRequestContext queryParams, Map<String,Object> paramsJson,
                                   WorkItemDefaultVO defaultIteration, DefaultPRValueVO defaultPrValue) {
        // 获取pluginDO的jsonSchema
        Map<String,Object> pluginParams = queryParams.getPluginParams();
        ChatFunction chatFunction = ToolLearningUtil.exactFunctionInfoFromPlugin(queryParams.getPluginDO(), false);
        String name = chatFunction.getParameters().getString("name");
        for (Map.Entry<String,Object> entry : chatFunction.getParameters().getJSONObject("properties").entrySet()) {
            String key = entry.getKey();
            Map<String,Object> entryValue = (Map<String,Object>) entry.getValue();
            // 对创建迭代的默认值进行定制处理
            Object defaultValue = entryValue.get("default");
            /**
             * 对创建迭代和创建pr插件的表单进行定制化处理
             */
            defaultValue = getLinkeCreateIterationDefaultValue(queryParams, defaultIteration, pluginParams, name, key, defaultValue);
            defaultValue = getLinkeCreatePullRequestDefaultValue(defaultPrValue, pluginParams, name, key, defaultValue);
            if (StrUtil.equals("empId", key)) {
                defaultValue = queryParams.getUserAuthDO().getEmpId();
            }
            paramsJson.put(key, defaultValue);
            if (paramsJson.containsKey(key) || !entryValue.containsKey("default")) {
                continue;
            }

            Matcher matcher = FunctionCallServiceImpl.DEFAULT_PARAM_PATTERN.matcher(defaultValue.toString());
            if (matcher.matches()) {
                String sessionKey = matcher.group(2);
                JSONObject sessionContext = algoModelUtilService.getSessionContext(queryParams);
                if (sessionContext != null && sessionContext.containsKey(sessionKey)) {
                    paramsJson.put(key, sessionContext.get(sessionKey));
                }
            }
        }

        return JSONObject.toJSONString(paramsJson, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 获取linke_create_pullRequest插件的表单定制化参数
     *
     * @param queryParams
     * @param defaultIteration
     * @param pluginParams
     * @param name
     * @param key
     * @param defaultValue
     * @return
     */
    @Nullable
    private Object getLinkeCreateIterationDefaultValue(PluginServiceRequestContext queryParams, WorkItemDefaultVO defaultIteration,
                                                       Map<String,Object> pluginParams,
                                                       String name, String key, Object defaultValue) {
        if (name.equals("linke_create_iteration") && defaultIteration != null) {
            if (StrUtil.equals("type", key)) {
                if (pluginParams.get("type") != null) {
                    defaultValue = pluginParams.get("type");
                }
                else {
                    defaultValue = defaultIteration.getDefaultIterationTypeValue();
                }

            }
            if (StrUtil.equals("appNames", key)) {
                if (pluginParams.get("appNames") != null) {
                    defaultValue = pluginParams.get("appNames");
                }
                else {
                    JSONObject sessionContext = algoModelUtilService.getSessionContext(queryParams);
                    if (sessionContext.get("appNames") != null) {
                        defaultValue = Collections.singletonList(sessionContext.get("appNames"));
                    }
                    else {
                        String defaultAppName = defaultIteration.getDefaultAppName();
                        if (defaultAppName != null){
                            defaultValue = Collections.singletonList(defaultAppName);
                        }
                    }
                }
            }
            if (StrUtil.equals("name", key)) {
                if (pluginParams.get("name") != null) {
                    defaultValue = pluginParams.get("name");
                }
                else {
                    LocalDate date = LocalDate.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                    String formattedDate = date.format(formatter);
                    defaultValue = AppConstants.ITERATION_NAME_DEFAULT + formattedDate;
                }

            }
            if (StrUtil.equals("workItems", key)) {
                List<WorkItemVO> defaultWorkItems = defaultIteration.getDefaultWorkItems();
                if (defaultWorkItems != null && defaultWorkItems.size() > 2) {
                    defaultWorkItems = defaultWorkItems.subList(0, 2);
                }
                if (defaultWorkItems == null){
                    defaultWorkItems = new ArrayList<>();
                }
                defaultValue = defaultWorkItems;

            }
        }
        return defaultValue;
    }

    /**
     * 获取linke_create_pullRequest插件的表单定制化参数
     *
     * @param defaultPrValue
     * @param pluginParams
     * @param name
     * @param key
     * @param defaultValue
     * @return
     */
    private Object getLinkeCreatePullRequestDefaultValue(DefaultPRValueVO defaultPrValue, Map<String,Object> pluginParams, String name,
                                                         String key,
                                                         Object defaultValue) {
        // 对提交pr的表单的默认值进行定制处理
        if (name.equals("linke_create_pullRequest") && defaultPrValue != null) {
            if (StrUtil.equals("sourceBranch", key)) {
                if (pluginParams.get("sourceBranch") != null) {
                    defaultValue = pluginParams.get("sourceBranch");
                }
                else {
                    defaultValue = defaultPrValue.getSourceBranch();
                }
            }
            if (StrUtil.equals("targetBranch", key)) {
                if (pluginParams.get("targetBranch") != null) {
                    defaultValue = pluginParams.get("targetBranch");
                }
                else {
                    defaultValue = defaultPrValue.getTargetBranch();
                }
            }
            if (StrUtil.equals("message", key)) {
                defaultValue = defaultPrValue.getMessage();
            }
            if (StrUtil.equals("iterationId", key)) {
                defaultValue = defaultPrValue.getIterationId();
            }
            if (StrUtil.equals("reviewers", key)) {
                List<ApprovalUser> reviewers = defaultPrValue.getReviewers();
                if (reviewers != null && reviewers.size() > 2) {
                    reviewers = reviewers.subList(0, 2);
                }
                if (reviewers == null){
                    reviewers = new ArrayList<>();
                }
                defaultValue = reviewers;
            }
            if (StrUtil.equals("workItems", key)) {
                List<WorkItemVO> items = defaultPrValue.getItems();
                if (items != null && items.size() > 2) {
                    items = items.subList(0, 2);
                }
                if (items == null){
                    items = new ArrayList<>();
                }
                defaultValue = items;
            }
            if (StrUtil.equals("title", key)) {
                if (pluginParams.get("title") != null) {
                    defaultValue = pluginParams.get("title");
                }
                else {
                    defaultValue = defaultPrValue.getTitle();
                }

            }
        }
        return defaultValue;
    }
}
