package com.alipay.codegencore.web.openapi;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ChatGPTModelEnum;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RateLimitTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.AlgoBackendModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.openai.ModelInfoVO;
import com.alipay.codegencore.model.request.ZarkEmbeddingRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.codegpt.CostService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.common.ZarkService;
import com.alipay.codegencore.service.common.limiter.qpx.QpxLimiterFactory;
import com.alipay.codegencore.service.impl.model.AlgoModelHealthUtilService;
import com.alipay.codegencore.service.impl.model.AlgoModelUtilService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.CommonTools;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.javers.common.collections.Lists;
import org.javers.common.collections.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.alipay.codegencore.model.AppConstants.STREAM_PROTOCOL_VERSION;

/**
 * 利用ai模型进行对话的controller
 *
 * <AUTHOR>
 * 创建时间 2022-02-28
 */
@RestController
@RequestMapping("/api/chat")
@Slf4j
public class ChatController {

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    private static final Logger LOGGER = LoggerFactory.getLogger(ChatController.class);

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private UserAclService userAclService;

    @Resource
    private CostService costService;

    @Resource
    private ConfigService configService;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private CalculateTokenService calculateTokenService;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private ZarkService zarkService;

    @Resource
    private SceneService sceneService;

    @Resource
    private MayaService mayaService;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;


    /**
     * 对话
     *
     * @param model                 模型
     * @param chatCompletionRequest 对话请求内容
     * @param codeGPTUser           用户
     * @param codeGPTToken          token
     * @return
     */
    @PostMapping(path = "/{model}/completion", produces = {"application/json;charset=UTF-8", "text/event-stream;charset=UTF-8"})
    public void completion(HttpServletRequest httpServletRequest,
                           HttpServletResponse httpServletResponse,
                           @PathVariable("model") String model,
                           @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                           @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,

                           @RequestHeader(value = "OpenAI-Organization", required = false) String openAiOrg,
                           @RequestHeader(value = "Authorization", required = false) String openAiAuth,
                           @RequestHeader(value = "stress_test", defaultValue = "false") Boolean stressTest,
                           @RequestHeader(value = "modelEnv", defaultValue = "auto") String modelEnv,
                           @RequestBody ChatCompletionRequest chatCompletionRequest) throws IOException {
        String requestId = ShortUid.getUid();
        if ((codeGPTUser == null && openAiOrg == null) || (codeGPTToken == null && openAiAuth == null)) {
            writeResponse(400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new IllegalArgumentException("codegpt_user and codegpt_token header is required!"));
            return;
        }
        Pair<Boolean, String> checkParamRet = checkParam(chatCompletionRequest);
        if (!checkParamRet.left()) {
            writeResponse(400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new IllegalArgumentException(checkParamRet.right()));
            return;
        }
        String apiUser = codeGPTUser != null ? codeGPTUser : openAiOrg;
        String apiToken = codeGPTToken != null ? codeGPTToken : (openAiAuth != null && openAiAuth.contains(" ") ? openAiAuth.split(" ")[1]
                : null);
        if (chatCompletionRequest.getChatRequestExtData() != null) {
            chatCompletionRequest.getChatRequestExtData().setCodeGPTUser(apiUser);
        }

        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
        if (algoBackendDO == null) {
            log.info("model config not existed: {}", model);
            writeResponse(400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new Exception("model config not existed"));
            return;
        }
        // token user限流判断
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.TOKEN_USER, apiUser, algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_TOKEN_LIMITING_ANOMALY);
        }

        // 执行环境校验
        modelAvailableCheck(algoBackendDO, modelEnv);

        //验证问题长度是否超过配置上限
        Long tokenQty = null;
        if(chatCompletionRequest.getMessages() != null){
            List<ChatMessage> messages = chatCompletionRequest.getMessages();
            tokenQty = calculateTokenService.getTokenQty(messages.get(messages.size() - 1).getContent());
        }else{
            tokenQty = calculateTokenService.getTokenQty(chatCompletionRequest.getPrompt());
        }
        try {
            // 把chatCompletionRequest的传参覆盖到algoBackendDO中
            overwriteModelConfig(algoBackendDO, chatCompletionRequest);
        } catch (Exception e) {
            log.info("chat completion request not authorized, user: {} error：{}", apiUser,e.getMessage());
            writeResponse(400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new Exception(e.getMessage()));
            return;
        }
        if (tokenQty != null && algoBackendDO.getMaxToken() != null && tokenQty.compareTo(Long.valueOf(algoBackendDO.getMaxToken())) > 0){
            writeResponse(400, httpServletResponse, ResponseEnum.QUESTION_LENGTH_EXCEEDS_LIMIT, new BizException(ResponseEnum.QUESTION_LENGTH_EXCEEDS_LIMIT));
            return;
        }
        if (!algoBackendDO.getEnable()) {
            log.info("model Unserviceable: {}", model);
            writeResponse(400, httpServletResponse, ResponseEnum.MODEL_UNSERVICEABLE, new Exception("model Unserviceable"));
            return;
        }

        if(chatCompletionRequest.getMessages() != null){
            AlgoModelUtilService.updateDefaultSystemPrompt(chatCompletionRequest, algoBackendDO);
        }

        if (!codeGPTDrmConfig.isIntranetApplication()) {
            if (chatCompletionRequest.getChatRequestExtData()==null || StringUtils.isBlank(chatCompletionRequest.getChatRequestExtData().getPhoneNumber())) {
                writeResponse(400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER,  new IllegalArgumentException("phoneNumber is required!"));
                return;
            }
            buildExternalNetworkChatRequestExtData(requestId, chatCompletionRequest.getChatRequestExtData(), algoBackendDO.getModel(), apiUser);
        }
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(apiUser, apiToken, uri)) {
            log.info("chat completion request not authorized, user: {}", apiUser);
            writeResponse(403, httpServletResponse, ResponseEnum.NO_AUTH, new Exception("Not authorized!"));
            return;
        }

        CHAT_LOGGER.info("open api chat completion request model {}, user:{}, id:{}, content:{}", algoBackendDO.getModel(), apiUser, requestId, JSON.toJSONString(chatCompletionRequest));

        GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId, apiUser, stressTest, algoBackendDO, chatCompletionRequest, null);

        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey)-> noneSerializationCacheManager.del(tBaseKey);
        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        params.setUserOriginalModel(algoBackendDO.getModel());
        params.setUniqueAnswerId(requestId);
        params.setModelEnv(modelEnv);
        params.setRequestTokenUser(apiUser);
        // 流式会用到这个Handler,非流式set了也没有影响
        Consumer<ChatStreamPartResponse> chatStreamPartResponseHandler = e -> {
            boolean newStreamProtocol = STREAM_PROTOCOL_VERSION.equals(chatCompletionRequest.getStreamProtocolVersion());
            // 是否开启openapi数据日志记录：引入自定义包装类
            if (newStreamProtocol) {
                ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(new NewPluginStreamPartResponse(e)));
            } else {
                ChatUtils.flushSseResponse(httpServletResponse, JSON.toJSONString(e));
            }
        };
        params.setChatStreamPartResponseHandler(chatStreamPartResponseHandler);
        executeModel(params, chatCompletionRequest, httpServletResponse, algoBackendDO);
    }
    
    private void executeModel(GptAlgModelServiceRequest params, ChatCompletionRequest chatCompletionRequest,
                             HttpServletResponse httpServletResponse, AlgoBackendDO algoBackendDO) throws IOException {
        if (chatCompletionRequest.getStream()) {
            //流式传输，要把response设置为流式
            ChatUtils.setServletToEventStream(httpServletResponse);
            AlgoModelExecutor.getInstance().executorStreamChat(algoBackendDO, params);
            return;
        } else {
            ChatMessage assistantChatMessage = AlgoModelExecutor.getInstance().executorChat(algoBackendDO, params);
            httpServletResponse.setContentType("application/json;charset=UTF-8");
            if (assistantChatMessage != null) {
                if(assistantChatMessage.getContent()!=null){
                    CommonTools.writeResponse(assistantChatMessage.getContent(), 200, httpServletResponse, null, null);
                }else{
                    CommonTools.writeResponse(assistantChatMessage, 200, httpServletResponse, null, null);
                }
                return;
            }
        }
        CommonTools.writeResponse(null,400, httpServletResponse, ResponseEnum.ILLEGAL_PARAMETER, new IllegalArgumentException(String.format("%s model not supported", algoBackendDO.getModel())));
    }

    /**
     * 覆盖模型默认参数
     *
     * @param algoBackendDO
     * @param chatCompletionRequest
     * @return
     */
    private void overwriteModelConfig(AlgoBackendDO algoBackendDO, ChatCompletionRequest chatCompletionRequest) {
        if (chatCompletionRequest.getMaxTokens() != null) {
            if (chatCompletionRequest.getMaxTokens() < 1) {
                log.info(" maxTokens failed: {}", chatCompletionRequest.getMaxTokens());
                throw new RuntimeException(" maxTokens failed illegal");
            }
            algoBackendDO.setMaxToken(chatCompletionRequest.getMaxTokens());
        }
        if (chatCompletionRequest.getMaxRound() != null) {
            if (chatCompletionRequest.getMaxRound() < 1) {
                log.info(" maxRound failed: {}", chatCompletionRequest.getMaxRound());
                throw new RuntimeException(" maxRound failed illegal");
            }
            algoBackendDO.setMaxRound(chatCompletionRequest.getMaxRound());
        }
        if (chatCompletionRequest.getEnableGptCache() != null) {
            algoBackendDO.setEnableGptCache(chatCompletionRequest.getEnableGptCache());
        }
        JSONObject implConfig = JSONObject.parseObject(algoBackendDO.getImplConfig());
        if (chatCompletionRequest.getModelOverrideConfig() != null) {
            JSONObject modelOverrideConfig = chatCompletionRequest.getModelOverrideConfig();
            implConfig.forEach((key, value) -> {
                if (modelOverrideConfig.get(key) != null) {
                    if (StringUtil.equals("firstStreamDataWaitTime", key)) {
                        if (modelOverrideConfig.getLong(key) > AppConstants.FIRST_STREAM_DATA_WAIT_TIME || modelOverrideConfig.getLong(key) < 0) {
                            throw new RuntimeException(" modelOverrideConfig.firstStreamDataWaitTime failed illegal");
                        }
                    }
                    if (StringUtil.equals("requestTimeOut", key)) {
                        if (modelOverrideConfig.getLong(key) > AppConstants.REQUEST_TIME_OUT || modelOverrideConfig.getLong(key) < 0) {
                            throw new RuntimeException(" modelOverrideConfig.requestTimeOut failed illegal");
                        }
                    }
                    implConfig.put(key, modelOverrideConfig.get(key));
                }
            });
        }
        if (chatCompletionRequest.getTopK() != null){
            if (chatCompletionRequest.getTopK() < 1) {
                log.info(" topK failed: {}", chatCompletionRequest.getTopK());
                throw new RuntimeException(" topK failed illegal");
            }
            implConfig.put("topK", chatCompletionRequest.getTopK());
        }
        if (chatCompletionRequest.getTopP() != null){
            if (chatCompletionRequest.getTopP() < 0 || chatCompletionRequest.getTopP() > 1) {
                log.info(" topP failed: {}", chatCompletionRequest.getTopP());
                throw new RuntimeException(" topP failed illegal");
            }
            implConfig.put("topP", String.valueOf(chatCompletionRequest.getTopP()));
        }
        if (chatCompletionRequest.getStop() != null) {
            implConfig.put("stopWords", chatCompletionRequest.getStop());
        }
        algoBackendDO.setImplConfig(JSONObject.toJSONString(implConfig));
    }


    private Pair<Boolean, String> checkParam(ChatCompletionRequest chatCompletionRequest) {
        if (chatCompletionRequest == null) {
            return new Pair<>(false, "chatCompletionRequest is blank");
        }

        if (chatCompletionRequest.getMessages() == null && chatCompletionRequest.getPrompt() == null) {
            return new Pair<>(false, "chatCompletionRequest is blank, messages or prompt must not null");
        }

        if(chatCompletionRequest.getMessages() != null && chatCompletionRequest.getPrompt() != null){
            return new Pair<>(false, "chatCompletionRequest is invalid, messages or prompt must only one notnull");
        }

        if(chatCompletionRequest.getMessages() != null){

            if(chatCompletionRequest.getMessages().size() == 0){
                return new Pair<>(false, "chatCompletionRequest is blank, messages is empty");
            }

            List<ChatMessage> messages = chatCompletionRequest.getMessages();
            for (ChatMessage chatMessage : messages) {
                if (ChatRoleEnum.getChatRoleEnumByName(chatMessage.getRole()) == null) {
                    return new Pair<>(false, "role is illegal");
                }
            }
        }

        return new Pair<>(true,null);
    }

    /**
     * openai 获取所有模型信息
     *
     * @param httpServletRequest
     * @param codeGPTUser
     * @param codeGPTToken
     * @return
     */
    @GetMapping(path = "/getUserModelInfo")
    public BaseResponse<List<ModelInfoVO>> userModelInfo(HttpServletRequest httpServletRequest,
                                                         @RequestHeader(value = "codegpt_user") String codeGPTUser,
                                                         @RequestHeader(value = "codegpt_token") String codeGPTToken) {
        if (StringUtil.isBlank(codeGPTUser) || StringUtil.isBlank(codeGPTToken)) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);

        }
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            log.info(" getUserModelInfo request not authorized, user: {}", codeGPTUser);
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        List<AlgoBackendDO> allAlgoBackend = algoBackendService.getAllAlgoBackend();
        List<ModelInfoVO> result = allAlgoBackend.stream()
                .filter(AlgoBackendDO::getEnable)
                .map(ModelInfoVO::new).collect(Collectors.toList());
        return BaseResponse.build(result);
    }

    private void buildExternalNetworkChatRequestExtData(String requestId, ChatRequestExtData chatRequestExtData, String model, String apiUser) {
        model = model.toUpperCase();
        JSONObject codeFuseCheckSwitch = JSONObject.parseObject(codeGPTDrmConfig.getCodeFuseCheckSwitch());
        // 配置里面没有配置的模型默认需要走审核
        boolean infoSecSwitch = true;
        boolean intentionSwitch = true;
        boolean rcSmartSwitch = true;
        if (codeFuseCheckSwitch.containsKey(model)) {
            JSONObject modelCheckSwitch = codeFuseCheckSwitch.getJSONObject(model);
            infoSecSwitch = modelCheckSwitch.getBoolean(ReviewPlatformEnum.INFOSEC.name());
            intentionSwitch = modelCheckSwitch.getBoolean(ReviewPlatformEnum.INTENTION.name());
            rcSmartSwitch = modelCheckSwitch.getBoolean(ReviewPlatformEnum.RCSMART.name());
        }
        chatRequestExtData.setSessionUid(requestId);
        chatRequestExtData.setCodeGPTUser(apiUser);
        chatRequestExtData.setInfoSecCheck(infoSecSwitch);
        chatRequestExtData.setIntentionCheck(intentionSwitch);
        chatRequestExtData.setRcSmartCheck(rcSmartSwitch);
    }

    //调用 openai 能力的通用接口
    @RequestMapping(value = "/commonPower/**", produces = MediaType.ALL_VALUE)
    public void commonPower(HttpServletRequest request, HttpServletResponse response) throws IOException, URISyntaxException {

        //鉴权
        String codeGPTUser = request.getHeader("codegpt_user");
        String codeGPTToken = request.getHeader("codegpt_token");
        String openAiOrg = request.getHeader("OpenAI-Organization");
        String openAiAuth = request.getHeader("Authorization");

        if ((codeGPTUser == null && openAiOrg == null) || (codeGPTToken == null && openAiAuth == null)) {
            log.info("commonPower request codegpt_user and codegpt_token header is required");

            writeResponse(403, response, ResponseEnum.ILLEGAL_PARAMETER, new IllegalArgumentException("codegpt_user and codegpt_token header is required!"));
            return;
        }

        String apiUser = codeGPTUser != null ? codeGPTUser : openAiOrg;
        String apiToken = codeGPTToken != null ? codeGPTToken : (openAiAuth != null && openAiAuth.contains(" ") ? openAiAuth.split(" ")[1] : null);
        String url = new UrlPathHelper().getLookupPathForRequest(request);
        if (VisableEnvUtil.isPrePub() && !JSON.parseArray(codeGPTDrmConfig.getPreNeedUseCommonPowerTokenUser(),String.class).contains(apiToken)) {
            writeResponse(403, response, ResponseEnum.PRE_NOT_ALLOW_USE_COMMON_POWER, new BizException(ResponseEnum.PRE_NOT_ALLOW_USE_COMMON_POWER));
        }
        if (!userAclService.isAuthorizedByToken(apiUser, apiToken, url)) {
            log.info("commonPower request not authorized, user: {}", codeGPTUser);
            writeResponse(403, response, ResponseEnum.NO_AUTH, new Exception("Not authorized!"));
            return;
        }

        if (!costService.hasBalance(apiUser)) {
            log.info("commonPower request user arrears fee, user: {}", codeGPTUser);
            writeResponse(403, response, ResponseEnum.ACCOUNT_ARREARS, new Exception("User arrears fee!"));
            return;
        }

        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(AppConstants.CHATGPT);
        // token user限流判断
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.TOKEN_USER, apiUser, algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_TOKEN_LIMITING_ANOMALY);
        }
        URI uri = new URI(url);
        String relativeAPI = uri.toString().substring(9);
        String query = request.getQueryString();
        String target = AlgoBackendUtil.exactServerConfig(algoBackendDO) + "/openai" + relativeAPI;

        log.info("commonPower url={} target={}", uri, target);

        String requestId = ShortUid.getUid();

        if (query != null && !query.equals("") && !query.equals("null")) {
            target = target + "?" + query;
        }

        URI newUri = new URI(target);

        // 执行代理查询
        String methodName = request.getMethod();
        HttpMethod httpMethod = HttpMethod.resolve(methodName);
        if (httpMethod == null) {
            writeResponse(400, response, ResponseEnum.HTTP_ERROR, new Exception("Not support method!"));
            return;
        }

        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(180000);

        ClientHttpRequest delegate = factory.createRequest(newUri, httpMethod);

        String contentType = request.getHeader("content-type");
        if (contentType != null) {
            delegate.getHeaders().addAll("content-type", Lists.asList(contentType));
        }
        byte[] requestBytes = StreamUtils.copyToByteArray(request.getInputStream());
        StreamUtils.copy(requestBytes, delegate.getBody());
        String requestBody = new String(requestBytes, StandardCharsets.UTF_8);
        CHAT_LOGGER.info("chat commonPower request url {}, user:{}, id:{}, target:{}, request:{}", relativeAPI, apiUser, requestId, target,
                requestBody);
        // todo 这里做安全审核
        ClientHttpResponse clientHttpResponse = null;
        // 执行远程调用
        try {
            String openaiApiKey = configService.getConfigByKey(AppConstants.CONFIG_KEY_OPENAI_API_KEY, false);
            delegate.getHeaders().add("Authorization", "Bearer " + openaiApiKey);
            clientHttpResponse = delegate.execute();
            String responseContentType = clientHttpResponse.getHeaders().getFirst("Content-Type");
            if (responseContentType != null) {
                response.setHeader("Content-Type", responseContentType);
            }

            response.setStatus(clientHttpResponse.getStatusCode().value());
        } catch (Exception e) {
            log.info("commonPower request failed: {}", e.getMessage());
            writeResponse(500, response, ResponseEnum.ERROR_THROW, e);
            return;
        }

        byte[] responseByte = StreamUtils.copyToByteArray(clientHttpResponse.getBody());
        StreamUtils.copy(responseByte, response.getOutputStream());
        String responseBody = new String(responseByte, StandardCharsets.UTF_8);
        // 扣费
        costFee(apiUser, responseBody);
        // todo 这里进行安全审核
        CHAT_LOGGER.info("chat commonPower response id:{}, user:{}, response:{}", requestId, apiUser, responseBody);
    }

    /**
     * 查看用户余额
     *
     * @param codeGPTUser 用户
     * @return 余额
     */
    @GetMapping("/getBalance")
    public BaseResponse<BigDecimal> getBalance(HttpServletRequest request,
                                               @RequestHeader(value = "codegpt_user") String codeGPTUser,
                                               @RequestHeader(value = "codegpt_token") String codeGPTToken) {
        if (StringUtils.isBlank(codeGPTUser) || StringUtils.isBlank(codeGPTToken)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        String uri = new UrlPathHelper().getLookupPathForRequest(request);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            log.info("commonPower request not authorized, user: {}", codeGPTUser);
            throw new BizException(ResponseEnum.NO_AUTH, ResponseEnum.NO_AUTH.getErrorMsg());
        }
        return BaseResponse.build(costService.getBalance(codeGPTUser));
    }

    /**
     * 查看用户下的所有模型
     * @param userToken
     * @return
     */
    @GetMapping("/getUserModelList")
    public BaseResponse<List<AlgoBackendDO>> getUserModelList(@RequestHeader(value = "user_token") String userToken) {
        if (StringUtils.isBlank(userToken)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        UserAuthDO userAuthDO = userAclService.queryUserByToken(userToken);
        List<AlgoBackendDO> userModelList = algoBackendService.getUserModelList(userAuthDO.getId());
        return BaseResponse.build(userModelList);
    }

    /**
     * 创建模型
     * @param userToken
     * @param algoBackendModel
     * @return
     */
    @PostMapping("/createModel")
    public BaseResponse createModel(@RequestHeader(value = "user_token") String userToken, @RequestBody AlgoBackendModel algoBackendModel) {
        if (StringUtils.isBlank(userToken)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        //验证参数是否合规
        if (needAlgoBackendParams(algoBackendModel, 1)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        UserAuthDO userAuthDO = userAclService.queryUserByToken(userToken);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "用户不存在，请检查userToken");
        }
        //用户下的模型数量不能超过10个
        List<AlgoBackendDO> userModelList = algoBackendService.getUserModelList(userAuthDO.getId());
        if (userModelList.size() == AppConstants.MODEL_UPPER_LIMIT) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "该用户下的模型数量已到达上限");
        }
        //创建者即为管理者
        algoBackendModel.setOwnerUserId(userAuthDO.getId().intValue());
        //默认启用
        algoBackendModel.setEnable(true);
        return BaseResponse.build(algoBackendService.addModel(algoBackendModel, userAuthDO));
    }

    /**
     * 编辑模型信息
     * @param userToken
     * @param algoBackendModel
     * @return
     */
    @PostMapping("/updateModel")
    public BaseResponse updateModel(@RequestHeader(value = "user_token") String userToken, @RequestBody AlgoBackendModel algoBackendModel) {
        if (StringUtils.isBlank(userToken)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        //验证必填项是否为空
        if (needAlgoBackendParams(algoBackendModel, 2)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        UserAuthDO userAuthDO = userAclService.queryUserByToken(userToken);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "用户不存在，请检查userToken");
        }
        AlgoBackendDO backendDO = algoBackendService.selectByPrimaryKey(algoBackendModel.getId());
        if (backendDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "无法查询到模型信息");
        }
        if (userAuthDO.getId().intValue() != backendDO.getOwnerUserId().intValue()) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "用户与模型管理者身份不匹配");
        }
        //参考codeFuse编辑页面。模型名称不支持修改
        algoBackendModel.setModel(backendDO.getModel());
        return BaseResponse.build(algoBackendService.updateModel(algoBackendModel, userAuthDO));
    }

    private void costFee(String user, String responseBody) {
        if (StringUtils.isBlank(responseBody)) {
            return;
        }
        JSONObject response = JSON.parseObject(responseBody);
        if (!response.containsKey("model") || !response.containsKey("usage")) {
            return;
        }
        String model = response.getString("model");
        Long promptTokens = response.getJSONObject("usage").getLong("prompt_tokens");
        Long completionTokens = response.getJSONObject("usage").getLong("completion_tokens");
        promptTokens = promptTokens == null ? 0L : promptTokens;
        completionTokens = completionTokens == null ? 0L : completionTokens;
        costService.cost(user, ChatGPTModelEnum.getByModelName(model), promptTokens, completionTokens);
    }

    private void writeResponse(int statusCode, HttpServletResponse response, ResponseEnum responseEnum, Throwable throwable) throws IOException {
        CommonTools.writeResponse(null, statusCode, response, responseEnum, throwable);
    }

    /**
     * 验证入参是否合法
     *
     * @param backendDO
     * @param type      1、insert  2、update
     * @return
     */
    private boolean needAlgoBackendParams(AlgoBackendDO backendDO, int type) {
        boolean needParams = true;
        if (backendDO.getVisableUser() == null || backendDO.getVisableEnv() == null || backendDO.getMaxToken() == null ||
                backendDO.getMaxRound() == null || backendDO.getNeedHealthCheck() == null || StringUtils.isAnyEmpty(
                backendDO.getImplConfig(), backendDO.getImpl())) {
            return true;
        }
        if (type == 1) {
            needParams = StringUtils.isEmpty(backendDO.getModel());
        }
        else if (type == 2) {
            needParams = backendDO.getId() == null || backendDO.getEnable() == null || backendDO.getOwnerUserId() != null;
        }
        if (needParams) {
            return true;
        }
        //验证实现类是否存在
        List<String> implConfigList = (List<String>) JSONObject.parseObject(codeGPTDrmConfig.getModelHandler()).get("admin");
        if (!implConfigList.contains(backendDO.getImpl())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型处理器不存在");
        }
        //验证实际使用的模型参数是否存在
        List<String> modelJumpConfigList = algoBackendService.getModelJumpConfigList();
        if (StringUtils.isNotEmpty(backendDO.getJump()) && !modelJumpConfigList.contains(backendDO.getJump())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "实际使用的模型不存在");
        }
        return false;
    }


    /**
     * 向量服务接口
     * 接口文档：
     */
    @PostMapping(path = "/embeddings")
    public BaseResponse<List<List<BigDecimal>>> embeddings(HttpServletRequest httpServletRequest,
                                                           @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                           @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                                           @RequestBody ZarkEmbeddingRequestBean requestBean) {

        if (StringUtil.isBlank(codeGPTUser) || StringUtil.isBlank(codeGPTToken)) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            log.info(" embeddings request not authorized, user: {}", codeGPTUser);
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(zarkService.embedding(requestBean));
    }


    /**
     * 查看默认助手配置
     */
    @GetMapping(path = "/getDefaultHelperConfig")
    public BaseResponse<AlgoBackendDO> getDefaultHelperConfig(HttpServletRequest httpServletRequest,
                                                              @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                              @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken) {
            if (StringUtil.isBlank(codeGPTUser) || StringUtil.isBlank(codeGPTToken)) {
                return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
            }
            String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
            if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
                log.info(" getDefaultHelperConfig request not authorized, user: {}", codeGPTUser);
                return BaseResponse.build(ResponseEnum.NO_AUTH);
            }
            //只允许codegptevaluation用户请求
            if (!"codegptevaluation".equals(codeGPTUser)){
                return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
            }
            List<SceneDO> allScene = sceneService.getAllScene();
            SceneDO sceneDO = allScene.stream()
                    .filter(x -> x.getId() == codeGPTDrmConfig.getSceneDefaultId().longValue())
                    .findAny()
                    .get();
            if (sceneDO == null) {
                return BaseResponse.build(ResponseEnum.MODEL_CONFIG_ERROR, "默认助手不存在");
            }
            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(sceneDO.getModel());
            if (algoBackendDO == null) {
                return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, "模型不存在");
            }
            if (StringUtils.isNotEmpty(sceneDO.getSystemPrompt())) {
                JSONObject impl = JSONObject.parseObject(algoBackendDO.getImplConfig());
                impl.put("systemPrompt", sceneDO.getSystemPrompt());
                algoBackendDO.setImplConfig(impl.toJSONString());
            }
            return BaseResponse.build(algoBackendDO);
    }

    private void modelAvailableCheck(AlgoBackendDO algoBackendDO, String modelEnv) {

        if(!codeGPTDrmConfig.isMayaModelDeploymentCheck()) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("mayaModelDeploymentCheck is false, don't need check");
            }
            return;
        }
        String modelImpl = algoBackendDO.getImpl();
        String modelName = algoBackendDO.getModel();
        if(modelImpl.equalsIgnoreCase("ChatGptModelHandler")
                || modelImpl.equalsIgnoreCase("AntGLMModelHandler")
                || modelImpl.equalsIgnoreCase("ChatGptModelHubHandler") ) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("unMAYA model not need check, {}",algoBackendDO.getModel());
            }
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(algoBackendDO.getImplConfig());
        String modelEnvFromDb = jsonObject.getString("modelEnv");

        if(modelEnv.equalsIgnoreCase("auto")
                && StringUtils.isNotBlank(modelEnvFromDb)
                && !modelEnvFromDb.equalsIgnoreCase("auto")) {
            modelEnv = modelEnvFromDb;
        }

        String actualEnv = mayaService.getActualEnv(modelEnv);
        JSONObject availableServers = mayaService.getModelAvailableServers(modelName, false);

        // cover new model logic, max gap is 10 mins
        // try to check again, when no server available
        if(null == availableServers
                || availableServers.get(actualEnv) == null
                || availableServers.getJSONObject(actualEnv).get("summary") == null
                || availableServers.getJSONObject(actualEnv).getJSONObject("summary").get("isAvailable") == null
                || !availableServers.getJSONObject(actualEnv).getJSONObject("summary").getBoolean("isAvailable") ) {
            availableServers = mayaService.getModelAvailableServers(modelName, true);
            if(null != availableServers) {
                if(LOGGER.isInfoEnabled()) {
                    LOGGER.info("maybe found a new model, {}", modelName);
                }
            }
        }
        if(availableServers == null
                || availableServers.get(actualEnv) == null
                || availableServers.getJSONObject(actualEnv).get("summary") == null
                || availableServers.getJSONObject(actualEnv).getJSONObject("summary").get("isAvailable") == null
                || !availableServers.getJSONObject(actualEnv).getJSONObject("summary").getBoolean("isAvailable")) {
            LOGGER.warn("no available server, {}, {}", modelName, actualEnv);
            algoModelHealthUtilService.costHealth(modelEnv, modelName, ResponseEnum.MODEL_NO_AVAILABLE_SERVER_ERROR.name());
            throw new BizException(ResponseEnum.MODEL_NO_AVAILABLE_SERVER_ERROR);
        }
    }
}
