package com.alipay.codegencore.web.filter;

import com.alibaba.fastjson.JSON;
import com.alipay.sofa.rpc.core.exception.SofaRpcException;
import com.alipay.sofa.rpc.core.request.SofaRequest;
import com.alipay.sofa.rpc.core.response.SofaResponse;
import com.alipay.sofa.rpc.filter.Filter;
import com.alipay.sofa.rpc.filter.FilterInvoker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @version 1.0 2024/11/28 16:08
 */
public class TRLogFilter extends Filter {

    private static final Logger logger = LoggerFactory.getLogger(TRLogFilter.class);

    @Override
    public SofaResponse invoke(FilterInvoker filterInvoker, SofaRequest sofaRequest) throws SofaRpcException {

        SofaResponse sofaResponse = null;
        try {
            logger.info("tr invoke appName:{} method:{} args:{}",
                    sofaRequest.getTargetAppName(), sofaRequest.getMethodName(), JSON.toJSONString(sofaRequest.getMethodArgs()));
            sofaResponse = filterInvoker.invoke(sofaRequest);
        } catch (SofaRpcException e) {
            logger.error("tr invoke SofaRpcException. targetAppName: {} service: {} method: {}, elapsedTime: {}",
                    sofaRequest.getTargetAppName(), sofaRequest.getTargetServiceUniqueName(), sofaRequest.getMethodName(), e);
        }

        return sofaResponse;
    }
}
