package com.alipay.codegencore.utils.code;

import com.alipay.codegencore.model.exception.BizException;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.List;


public class CodeSearchClientTest {

    @Before
    public void init() {

        String config = "{\n" +
                "     \"aosHost\": \"https://searcher.alipay.com\",\n" +
                "     \"opHost\": \"https://techasst.antgroup-inc.cn\",\n" +
                "     \"appName\": \"codexmuse\",\n" +
                "     \"aosToken\": \"240b2c834c40b972d06831dda37027cc\",\n" +
                "     \"opToken\": \"c3f1cf6602f811ef9aae02420b210e80\",\n" +
                "     \"aosEmpId\": \"347214\",\n" +
                "     \"opEmpId\": \"347214\",\n" +
                "     \"aosCookie\": \"spanner=vvRvgmKy/U4Q5UnEK7NwXV1dLOOp9mZs\",\n" +
                "     \"opCookie\": \"spanner=VDcNXwxPRp5GAclfXOecJOOA3vXV0z14\",\n" +
                "     \"page\": 1,\n" +
                "     \"pageSize\": 10,\n" +
                "     \"type\": \"enum OR class OR interface OR controller OR dao OR impl\",\n" +
                "     \"skipCache\": true,\n" +
                "     \"version\": \"ai\",\n" +
                "     \"blackPattern\": [\"**/test/**\", \"**/*Test.java\"]\n" +
                "}";

        CodeSearchClient.init(config);

    }

    @Test
    public void searchRelatedCodeByQuery() {

        CodeSearchClient.SearchRequestData searchRequestData = new CodeSearchClient.SearchRequestData();
        searchRequestData.setSessionId("xxxx");
        searchRequestData.setQuery("archcompass 应用查询无效drm树的接口中删除关于flag参数判断逻辑");
        searchRequestData.setRepoPath("LinkedE/archcompass");
        searchRequestData.setDeployAppName("archcompass");

        List<CodeSearchClient.Item> items = CodeSearchClient.searchRelatedCodeByQuery(searchRequestData);
        Assert.assertNotNull(items);
    }

    @Test
    public void searchRelatedCodeByQuery1() {

        CodeSearchClient.SearchRequestData searchRequestData = new CodeSearchClient.SearchRequestData();

        try {
            CodeSearchClient.searchRelatedCodeByQuery(searchRequestData);
        } catch (BizException e) {

        }
    }

    @Test
    public void searchRelatedCodeByQueryAndPath() {

        CodeSearchClient.SearchByPathRequestData searchByPathRequestData = new CodeSearchClient.SearchByPathRequestData();
        searchByPathRequestData.setQuery("getHost方法中将 host 打印到日志");
        searchByPathRequestData.setFilePath("common/src/main/java/org/cloudfoundry/autosleep/access/cloudfoundry/CloudFoundryApi.java");
        searchByPathRequestData.setRepoPath("cloudfoundry-community/autosleep");
        searchByPathRequestData.setSessionId("xxxx");

        List<CodeSearchClient.Item> items = CodeSearchClient.searchRelatedCodeByQueryAndPath(searchByPathRequestData);
        Assert.assertNotNull(items);
    }

    @Test
    public void filter() {

        List<CodeSearchClient.Item> items = Lists.newArrayList();
        CodeSearchClient.Item item1 = new CodeSearchClient.Item();
        item1.setFilePath("smartunit.com.alipay.compass.biz.coverreport.impl.ReportConfigManagerImpl_SSTest.java");
        CodeSearchClient.Item item2 = new CodeSearchClient.Item();
        item2.setFilePath("com.alipay.compass.biz.coverreport.impl.ReportConfigManagerImplTest.java");
        CodeSearchClient.Item item3 = new CodeSearchClient.Item();
        item3.setFilePath("com.alipay.compass.common.service.integration.clientcoverage.result.GenerateReportTask.java");
        CodeSearchClient.Item item4 = new CodeSearchClient.Item();
        item4.setFilePath("com.alipay.compass.core.model.enums.CoverPlatformEnum.java");
        items.add(item1);
        items.add(item2);
        items.add(item3);
        items.add(item4);


        List<CodeSearchClient.Item> itemList = CodeSearchClient.filter(items);
        Assert.assertEquals(itemList.size(), 2);

    }
}