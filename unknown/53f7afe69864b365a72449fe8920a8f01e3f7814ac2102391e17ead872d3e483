package com.alipay.codegencore.model.enums.limit;

/**
 * Caller或者Target的字段值的前缀
 */
public enum CallerTargetPrefixEnum {

    ALL_CALLER("ALL_CALLER:", "全部的请求来源Caller"),
    USER_ID("USER_ID:", "WebApi的请求的用户ID"),
    TOKEN_ID("TOKEN_ID:", "OpenApi的请求的Token的ID"),

    ALL_TARGET("ALL_TARGET:", "全部的请求目标Target"),
    API_PATH("API_PATH:", "Api的请求的请求路径URI"),
    MODEL_ID("MODEL_ID:", "推理模型的ID"),
    ;


    private final String prefix;
    private final String desc;

    CallerTargetPrefixEnum(String prefix, String desc) {
        this.prefix = prefix;
        this.desc = desc;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getDesc() {
        return desc;
    }
}
