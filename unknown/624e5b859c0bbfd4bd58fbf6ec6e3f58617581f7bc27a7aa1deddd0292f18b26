/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.middle.dingding;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.domain.DingDingMessageDO;
import com.google.common.cache.Cache;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 *
 * 该示例内包含了【机器人主动推送单聊文本消息给用户】的能力
 * 接口文档：https://open.dingtalk.com/document/orgapp/chatbots-send-one-on-one-chat-messages-in-batches
 */
@Service
public class RobotSingleMessageImpl implements RobotSingleMessageService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RobotSingleMessageImpl.class);

    @Resource(name = "cachePool")
    private Cache<String,String> cachePool;

    /**
     * 获取accessTokenKey的Key
     */
    private static final String ACCESS_TOKEN_KEY = "DING_MESSAGE_TOKEN_KEY";
    /**
     * 钉钉机器人appKey
     */
    private static final String APP_KEY          = "dingwqwdg7slook5alkx";
    /**
     * 钉钉机器人appSecret
     */
    private static final String APP_SECRET       = "YHcdZYIGW2am2cJVS95B1YOjGronQjvIqy7btasSujfpSQRyaf8GuhA2SVzQ0gPN";
    /**
     * 钉钉机器人robotCode
     * 这里的robotCode 需要在轻研查看应用的【凭证信息】 前往查看 https://mapp.alibaba-inc.com/apps
     */
    private static final String ROBOT_CODE       = "dingwqwdg7slook5alkx";

    /**
     * @param userIds
     * @param messageDO
     * @throws Exception
     */
    @Override
    public void sendMessage(List<String> userIds, DingDingMessageDO messageDO) {
        try {
            if (CollectionUtils.isEmpty(userIds)){
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "DingDing userIds is null");
            }
            if (messageDO == null || StringUtils.isAnyEmpty(messageDO.getTitle(),messageDO.getText()) ){
                throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
            }
            userIds = userIds.stream().map(userId -> getEmpIdAdd0(userId)).collect(Collectors.toList());
            // 设置请求头内的token，接口请求鉴权需要
            String accessToken = getAccessToken(APP_KEY, APP_SECRET);
            String msgKey = messageDO.getSingleURL() != null ? "sampleActionCard" : "sampleMarkdown";
            JSONObject params = new JSONObject();
            params.put("userIds", userIds);
            params.put("robotCode", ROBOT_CODE);
            params.put("msgKey", msgKey);
            params.put("msgParam", JSONObject.toJSONString(messageDO));

            HttpClient httpClient = null;
            HttpPost post = new HttpPost("https://api.dingtalk.com/v1.0/robot/oToMessages/batchSend");
            httpClient = HttpClientBuilder.create().build();
            post.addHeader("x-acs-dingtalk-access-token", accessToken);
            post.addHeader("Content-Type", "application/json");
            ByteArrayEntity entity = new ByteArrayEntity(JSON.toJSONString(params).getBytes(StandardCharsets.UTF_8));
            post.setEntity(entity);
            HttpResponse response = httpClient.execute(post);
            String content = EntityUtils.toString(response.getEntity());
            JSONObject bodyJson = JSONObject.parseObject(content);
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("response is {}", bodyJson);
            }
            LOGGER.info("钉钉单聊机器人接口返回:{}", response);
        } catch (Exception e) {
            LOGGER.error("钉钉单聊机器人接口返回", e);
        }
    }

    /**
     * 如果传入的是<6位数的,那么在前面补全0到6位数
     * example:
     * param: 23239 return 023239
     *
     * @return empId
     */
    public String getEmpIdAdd0(String empId) {
        if (empId.length() < 6) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 6 - empId.length(); i++) {
                sb.append("0");
            }
            sb.append(empId);
            return sb.toString();
        }
        return empId;
    }

    /**
     * 获取AccessToken
     *
     * @param appKey
     * @param appSecret
     * @return
     * @throws Exception
     */
    public String getAccessToken(String appKey, String appSecret) throws ExecutionException {
        String accessToken = cachePool.get(ACCESS_TOKEN_KEY, new Callable<String>() {
            @Override
            public String call() {
                try {
                    JSONObject params = new JSONObject();
                    params.put("appKey", appKey);
                    params.put("appSecret", appSecret);
                    HttpClient httpClient = HttpClientBuilder.create().build();
                    ByteArrayEntity entity = new ByteArrayEntity(JSON.toJSONString(params).getBytes(StandardCharsets.UTF_8));
                    HttpPost post = new HttpPost("https://api.dingtalk.com/v1.0/oauth2/accessToken");
                    post.setHeader("Content-Type", "application/json");
                    post.setEntity(entity);
                    HttpResponse response = httpClient.execute(post);
                    String content = EntityUtils.toString(response.getEntity());

                    if (StringUtils.isNotBlank(content)) {
                        if(LOGGER.isInfoEnabled()) {
                            LOGGER.info("get scret key response is : {}", content);
                        }
                        JSONObject bodyJson = JSONObject.parseObject(content);
                        if(bodyJson != null && null != bodyJson.get("accessToken")) {
                            return bodyJson.getString("accessToken");
                        }
                    } else {
                        LOGGER.warn("get secret key response is empty");
                    }
                } catch (Exception e) {
                    LOGGER.error("钉钉机器人获取accessToken错误,", e);
                }
                return null;
            }
        });
        return accessToken;
    }
}
