/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2019 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 对象序列化
 *
 * <AUTHOR>
 * @version $Id: ToString.java, v 0.1 2019-04-22 15:44 zhi.huangcz Exp $$
 */
public class ToString implements Serializable {
    /**
     * @see Object#toString()
     */
    private static final long serialVersionUID = 397936866111964474L;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
