package com.alipay.codegencore.model.model.links;

import com.alipay.common.tracer.util.TracerContextUtil;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.06.24
 */
public class LinksResult<T> {
    /**
     * 成功标志
     */
    private boolean success      = false;
    /**
     * 消息
     */
    private String  msg;
    /**
     * 警告信息
     */
    private String  warningMessage;
    /**
     * 错误码
     */
    private String  errorCode;
    /**
     * 错误信息
     */
    private String  errorMessage = "";
    /**
     * 结果
     */
    private T       result;
    /**
     * 耗时
     */
    private Long    processingTime;
    /**
     * 链路ID
     */
    private String traceId;
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01 
     * @return null
     */
    public LinksResult() {
    }
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01
     * @param errorCode errorCode
     * @param errorMessage errorMessage 
     * @return null
     */
    public LinksResult(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setWarningMessage(String warningMessage) {
        this.warningMessage = warningMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public String getWarningMessage() {
        return warningMessage;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    /**
     * 创建一个API 调用结果实例
     *
     * @param resultCode 结果码，大多是错误结果码
     */
    public LinksResult(ResultCode resultCode) {
        this(resultCode, null);
    }

    /**
     * 创建一个API 调用结果实例
     *
     * @param resultCode
     * @param result
     */
    public LinksResult(ResultCode resultCode, T result) {
        this.success = resultCode.isSuccessCode();
        if (this.success) {
            this.msg = resultCode.getMessage();
        } else {
            this.errorCode = resultCode.getCode();
            this.errorMessage = resultCode.getMessage();
        }
        this.result = result;
    }

    /**
     * ApiResult
     * @param result
     */
    public LinksResult(T result) {
        this(result, WebCommonResultCode.API_SUCCESS.getMessage());
    }

    /**
     * 创建一个API 调用结果实例
     *
     * @param result 结果数据
     * @param msg 结果消息
     */
    public LinksResult(T result, String msg) {
        this.success = true;
        this.result = result;
        this.msg = msg;
        this.traceId = TracerContextUtil.getTraceId();
    }

    /**
     * 创建一个API调用结果成功实例
     *
     * @return
     */
    public static <T> LinksResult<T> success() {
        return success(null);
    }

    /**
     * 创建一个API调用结果成功实例
     *
     * @param data 结果数据
     * @param <T>
     * @return
     */
    public static <T> LinksResult<T> success(T data) {
        return new LinksResult<>(data);
    }

    /**
     * 创建一个API调用结果失败实例
     *
     * @param resultCode
     * @param parames
     * @return
     */
    public static <T> LinksResult<T> fail(ResultCode resultCode, Object... parames) {
        LinksResult<T> apiResult = new LinksResult<>(resultCode);
        if (!apiResult.isSuccess() && parames != null && parames.length > 0) {
            apiResult.setErrorMessage(MessageFormat.format(apiResult.getErrorMessage(), parames));
        }
        apiResult.setTraceId(TracerContextUtil.getTraceId());
        return apiResult;
    }
}
