package com.alipay.codegencore.model.response;

import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.openai.ChatFunctionCall;

import java.util.List;

/**
 * <AUTHOR>
 * @version : PluginStreamPartResponse.java, v 0.1 2023年07月12日 17:57 baoping Exp $
 */
public class PluginStreamPartResponse {
    private String id;
    private PluginInfo pluginInfo;
    /**
     * 支持多插件，index表示插件的序号
     */
    private Integer index;
    private String stage;
    private String stageName;
    private String finishReason;
    private String content;
    /**
     * 插件阶段list，插件最开始输出，方便前端了解插件共有几个阶段
     */
    private List<String> stageList;
    /**
     * 安全审查结果，一般只有大模型调用阶段才有
     */
    private CheckResultModel checkResultModel;
    /**
     * 关键数据标识
     */
    private boolean keyContent;

    private ChatFunctionCall chatFunctionCall;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public String getFinishReason() {
        return finishReason;
    }

    public void setFinishReason(String finishReason) {
        this.finishReason = finishReason;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getStageList() {
        return stageList;
    }

    public void setStageList(List<String> stageList) {
        this.stageList = stageList;
    }

    public CheckResultModel getCheckResultModel() {
        return checkResultModel;
    }

    public void setCheckResultModel(CheckResultModel checkResultModel) {
        this.checkResultModel = checkResultModel;
    }

    public boolean isKeyContent() {
        return keyContent;
    }

    public void setKeyContent(boolean keyContent) {
        this.keyContent = keyContent;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public PluginInfo getPluginInfo() {
        return pluginInfo;
    }

    public void setPluginInfo(PluginInfo pluginInfo) {
        this.pluginInfo = pluginInfo;
    }

    public ChatFunctionCall getChatFunctionCall() {
        return chatFunctionCall;
    }

    public void setChatFunctionCall(ChatFunctionCall chatFunctionCall) {
        this.chatFunctionCall = chatFunctionCall;
    }
}
