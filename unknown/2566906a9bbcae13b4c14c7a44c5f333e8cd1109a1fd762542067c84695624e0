package com.alipay.codegencore.model.request.tsingyan;

import com.alipay.codegencore.model.enums.OsArchEnum;

/**
 * 查询配置信息
 *
 * <AUTHOR>
 * 创建时间 2022-10-18
 */
public class QueryConfigRequestBean extends AbstractRequestBean {
    /**
     * 系统类型,取值来自{@link com.alipay.tsingyancodegen.model.enums.OsTypeEnum}
     */
    private int osType;

    /**
     * 系统架构,取值来自{@link OsArchEnum}
     */
    private int osArch;

    public int getOsType() {
        return osType;
    }

    public void setOsType(int osType) {
        this.osType = osType;
    }

    public int getOsArch() {
        return osArch;
    }

    public void setOsArch(int osArch) {
        this.osArch = osArch;
    }
}
