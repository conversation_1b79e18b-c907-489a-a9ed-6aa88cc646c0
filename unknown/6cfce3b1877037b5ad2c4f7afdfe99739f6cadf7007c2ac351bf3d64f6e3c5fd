package com.alipay.codegencore.service.middle.msgbroker;

import cn.hutool.core.date.DateUtil;
import com.alipay.codegencore.service.answer.AnswerIndexService;
import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.common.event.UniformEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.middle.msgbroker
 * @CreateTime : 2024-09-05
 */
@Service
public class RepoIndexServiceListener implements CodegencoreEventHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(RepoIndexServiceListener.class);

    /**
     * topic|eventCode的Key 一般用于注册
     */
    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_F_SC|EC_codegencore_check_notify_zsearch_status";

    @Resource
    private AnswerIndexService answerIndexService;

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;

    @Override
    public void handle(UniformEvent message) {
        LOGGER.info("RepoIndexServiceListener.handle 定时任务开始");
        String endTime = DateUtil.formatLocalDateTime(LocalDateTime.now());
        String startTime = DateUtil.formatLocalDateTime(LocalDateTime.now().minusDays(7));
        appThreadPool.execute(() -> answerIndexService.checkAndNotifyZSearchStatus(null, startTime, endTime));
    }

    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }

}
