package com.alipay.codegencore.web.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.util.UrlPathHelper;

/**
 * 对语雀来得uri进行重定向
 *
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.web.filter
 * @CreateTime : 2023-09-26
 */
@Configuration
@WebFilter(urlPatterns = "/yuque/**")
public class YuQueUriFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(YuQueUriFilter.class);

    /**
     * 对来自语雀助手的请求进行重定向
     *
     * @param req
     * @param res
     * @param chain
     * @throws IOException
     * @throws ServletException
     */
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        String requestURI = new UrlPathHelper().getLookupPathForRequest(request);
        if (requestURI.startsWith("/yuque")) {
            requestURI = requestURI.replace("yuque", "webapi");
            request.getRequestDispatcher(requestURI).forward(request, res);
            LOGGER.info("语雀请求 requestURI：{},uri:{},", request.getRequestURI(), requestURI);
        }
        else {
            chain.doFilter(req, res);
        }

    }

    /**
     * 初始化
     *
     * @param filterConfig
     */
    public void init(FilterConfig filterConfig) {LOGGER.info("YuQueUriFilter过滤器初始化！");}

    /**
     * 这是个注释
     */
    public void destroy() {}
}

