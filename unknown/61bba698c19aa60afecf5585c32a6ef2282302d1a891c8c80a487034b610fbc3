package com.alipay.codegencore.model.response;

import com.alipay.codegencore.model.enums.ResponseEnum;

/**
 * 分页结果
 *
 * <AUTHOR>
 * 创建时间 2022-04-07
 */
public class PageResponse<E> extends BaseResponse {
    /**
     * 返回值总数
     */
    private Long totalCount = 0L;


    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public static <E> PageResponse<E> build(ResponseEnum responseEnum, E data, Long totalCount) {
        PageResponse<E> pageResponse = new PageResponse<>();
        pageResponse.setData(data);
        pageResponse.setErrorCode(responseEnum.getErrorCode());
        pageResponse.setErrorMsg(responseEnum.getErrorMsg());
        pageResponse.setTotalCount(totalCount);
        return pageResponse;
    }
}