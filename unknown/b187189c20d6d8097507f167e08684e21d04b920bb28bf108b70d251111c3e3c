/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version $Id: EntityReference.java, v 0.1 2020-05-11 16:59 zhi.huangcz Exp $$
 */
public class EntityReference extends BeanStringSwitcherImpl {
    private EntityReferenceType type = EntityReferenceType.SNIPPET;
    private String              id   = "";
    private String              title;
    private String              answer;
    private String              markdown;
    private SimpleActionCard    actionCard;
    private ReplaysConfigInfo   replaysConfigInfo;
    private Card                card ;
    private SnippetListCard     listCard ;
    private String html;
    private GptMessageContent gptMessageContent;
    private DiagTool diagTool;
    private GptTask gptTask;
    /**
     * 会话转派附加配置信息
     */
    private DispatchConfigInfo dispatchConfigInfo;
    /**
     * 诊断任务结果条目详情
     */
    private ReplayDetailInfo replayDetailInfo;

    /**
     * 复合卡片信息
     */
    private ComplexCard complexCard;

    public EntityReferenceType getType() {
        return type;
    }

    public void setType(EntityReferenceType type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getMarkdown() {
        return markdown;
    }

    public void setMarkdown(String markdown) {
        this.markdown = markdown;
    }

    public SimpleActionCard getActionCard() {
        return actionCard;
    }

    public void setActionCard(SimpleActionCard actionCard) {
        this.actionCard = actionCard;
    }

    public ReplaysConfigInfo getReplaysConfigInfo() {
        return replaysConfigInfo;
    }

    public void setReplaysConfigInfo(ReplaysConfigInfo replaysConfigInfo) {
        this.replaysConfigInfo = replaysConfigInfo;
    }

    public Card getCard() {
        return card;
    }

    public void setCard(Card card) {
        this.card = card;
    }

    public SnippetListCard getListCard() {
        return listCard;
    }

    public void setListCard(SnippetListCard listCard) {
        this.listCard = listCard;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public GptMessageContent getGptMessageContent() {
        return gptMessageContent;
    }

    public void setGptMessageContent(GptMessageContent gptMessageContent) {
        this.gptMessageContent = gptMessageContent;
    }

    public DiagTool getDiagTool() {
        return diagTool;
    }

    public void setDiagTool(DiagTool diagTool) {
        this.diagTool = diagTool;
    }

    public GptTask getGptTask() {
        return gptTask;
    }

    public void setGptTask(GptTask gptTask) {
        this.gptTask = gptTask;
    }

    public DispatchConfigInfo getDispatchConfigInfo() {
        return dispatchConfigInfo;
    }

    public void setDispatchConfigInfo(DispatchConfigInfo dispatchConfigInfo) {
        this.dispatchConfigInfo = dispatchConfigInfo;
    }

    public ReplayDetailInfo getReplayDetailInfo() {
        return replayDetailInfo;
    }

    public void setReplayDetailInfo(ReplayDetailInfo replayDetailInfo) {
        this.replayDetailInfo = replayDetailInfo;
    }

    public ComplexCard getComplexCard() {
        return complexCard;
    }

    public void setComplexCard(ComplexCard complexCard) {
        this.complexCard = complexCard;
    }

    /**
     * Constructor.
     */
    public EntityReference() {}

    /**
     * Constructor.
     *
     * @param type the type
     */
    public EntityReference(EntityReferenceType type) {
        this.type = type;
    }

    /**
     * Constructor.
     *
     * @param type the type
     * @param id   the id
     */
    public EntityReference(EntityReferenceType type, String id) {
        this.type = type;
        this.id = id;
    }

    /**
     * Constructor.
     *
     * @param type   the type
     * @param id     the id
     * @param title  the title
     * @param answer the answer
     */
    public EntityReference(EntityReferenceType type, String id, String title, String answer) {
        this.type = type;
        this.id = id;
        this.title = title;
        this.answer = answer;
    }

    /**
     * Constructor.
     *
     * @param type   the type
     * @param id     the id
     * @param title  the title
     * @param answer the answer
     */
    public EntityReference(EntityReferenceType type, String id, String title, String answer, DispatchConfigInfo dispatchConfigInfo) {
        this.type = type;
        this.id = id;
        this.title = title;
        this.answer = answer;
        this.dispatchConfigInfo = dispatchConfigInfo;
    }

    /**
     * Constructor.
     *
     * @param type              the type
     * @param replaysConfigInfo the replays config info
     */
    public EntityReference(EntityReferenceType type, ReplaysConfigInfo   replaysConfigInfo) {
        this.type = type;
        this.setReplaysConfigInfo(replaysConfigInfo);
    }

    /**
     * 获取转派表单的提交引用
     * 返回的title默认值为ONLINE，如果当前表单已经提交过了则title值为OFFLINE
     *
     * @param roomId 租户id
     * @return
     */
    public static EntityReference structureDispatchFormReference(String roomId) {
        return new EntityReference(EntityReferenceType.CONVERSATION_DISPATCH_FORM, roomId, "ONLINE", "ONLINE");
    }
}