package com.alipay.codegencore.dal.config;

import com.alipay.common.event.UniformEventMessageListener;
import com.alipay.common.event.tbnotify.adapter.UniformEventSubscriberAdapter;
import com.taobao.hsf.notify.client.SubscriptMsgDetailInfo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息队列的实例工厂
 */
@Configuration
public class MqBeanFactory {

    @Resource
    private UniformEventMessageListener infosecMsgBrokerListener;

    private SubscriptMsgDetailInfo subscriptMsgDetailInfo;

    private static final String INFOSEC_TOPIC = "TP_O_SECURITYPROD";
    private static final String INFOSEC_EVENT_CODE = "EC_codegencore_infosec_checkResult";
    private static final String INFOSEC_SUBSCRIBE_GROUP_ID = "S-codegencore-infosecevent";

    /**
     * 消息订阅所需的bean
     * @return SubscriptMsgDetailInfo
     */
    @Bean(name = "subscriptMsgDetailInfo")
    public SubscriptMsgDetailInfo getSubscriptMsgDetailInfo(){
        SubscriptMsgDetailInfo subscriptMsgDetailInfo = new SubscriptMsgDetailInfo();
        subscriptMsgDetailInfo.setPersistence(true);
        subscriptMsgDetailInfo.setWaterMark(-1);
        this.subscriptMsgDetailInfo = subscriptMsgDetailInfo;
        return subscriptMsgDetailInfo;
    }

    /**
     * 消息订阅
     * @return infosecMsgBrokerListener
     */
    @Bean(name = "uniformEventSubscriberAdapter",initMethod = "init")
    @DependsOn({"subscriptMsgDetailInfo","defaultCacheManager","infosecMsgBrokerListener"})
    public UniformEventSubscriberAdapter getUniformEventSubscriberAdapter(){
        UniformEventSubscriberAdapter uniformEventSubscriberAdapter = new UniformEventSubscriberAdapter();
        Map<String, Map<String, ?>> subscribeMeta = new HashMap<>();
        Map<String, SubscriptMsgDetailInfo> val = new HashMap<>();
        val.put(INFOSEC_EVENT_CODE,subscriptMsgDetailInfo);
        subscribeMeta.put(INFOSEC_TOPIC,val);
        uniformEventSubscriberAdapter.setSubscribeMeta(subscribeMeta);
        uniformEventSubscriberAdapter.setUniformEventListener(infosecMsgBrokerListener);
        uniformEventSubscriberAdapter.setGroupId(INFOSEC_SUBSCRIBE_GROUP_ID);
        return uniformEventSubscriberAdapter;
    }

}
