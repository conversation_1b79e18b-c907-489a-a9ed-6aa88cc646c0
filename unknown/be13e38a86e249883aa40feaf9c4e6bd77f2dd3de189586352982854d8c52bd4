/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.totoro;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.TotoroService;
import com.alipay.codegencore.service.common.UserAclService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version TototoController.java, v 0.1 2023年05月31日 上午10:54 changxing.cx
 */
@RestController
@RequestMapping("/api/totoro")
public class TotoroController {
    private static final Logger logger = LoggerFactory.getLogger(TotoroController.class);

    @Resource
    private TotoroService  totoroService;
    @Resource
    private UserAclService userAclService;

    /**
     * totoro-gpt 自然语言转DSL命令
     *
     * @param param 用户输入内容
     * @return data: 返回dsl 命令
     */
    @PostMapping(path = "/dsl", produces = {"application/json;charset=UTF-8", "text/event-stream;charset=UTF-8"})
    public BaseResponse<String> nl2dsl(HttpServletRequest httpServletRequest,
                                       @RequestHeader(value = "codegpt_user", required = true) String codeUser,
                                       @RequestHeader(value = "codegpt_token", required = true) String codeToken,
                                       @RequestBody JSONObject param) {
        logger.info("nl2dsl request, user: {}", codeUser);
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeUser, codeToken, uri)) {
            logger.info("request not authorized, user: {}", codeUser);
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }

        String data = param.getString("data");

        return BaseResponse.build(totoroService.nl2TotoroDSL(data));
    }

    /**
     * totoro-gpt 自然语言转Task命令
     *
     * @param param 用户输入内容
     * @return data: 返回dsl 命令的任务列表
     */
    @PostMapping(path = "/task", produces = {"application/json;charset=UTF-8", "text/event-stream;charset=UTF-8"})
    public BaseResponse<String> nl2task(HttpServletRequest httpServletRequest,
                                       @RequestHeader(value = "codegpt_user", required = true) String codeUser,
                                       @RequestHeader(value = "codegpt_token", required = true) String codeToken,
                                       @RequestBody JSONObject param) {
        logger.info("task request, user: {}", codeUser);
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeUser, codeToken, uri)) {
            logger.info("request not authorized, user: {}", codeUser);
            return BaseResponse.build(ResponseEnum.NO_AUTH);
        }

        JSONObject data = param.getJSONObject("data");
        return BaseResponse.build(totoroService.nl2task(data));
    }

}
