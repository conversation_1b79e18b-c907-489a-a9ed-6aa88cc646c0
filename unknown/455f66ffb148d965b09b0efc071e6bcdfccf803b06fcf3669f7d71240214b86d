package com.alipay.codegencore.service.impl.codegpt.user;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserStatusEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.request.UpdateAllowAccessTypeVO;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.impl.codegpt.DevInsightServiceImpl;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import static com.alipay.codegencore.model.contant.WebApiContents.ORIGIN_USER;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.service.impl.codegpt.user
 * @CreateTime : 2023-04-24
 */
@Service
public class CodeFuseUserAuthServiceImpl implements CodeFuseUserAuthService {

    @Resource
    private UserAuthDOMapper userAuthDOMapper;


    @Resource
    private DevInsightServiceImpl devInsightService;

    @Resource
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private CodeGPTDrmConfig drmConfig;

    private static final Logger LOGGER = LoggerFactory.getLogger( CodeFuseUserAuthServiceImpl.class );
    /**
     * 查看所有用户
     *
     * @param pageNo       分页
     * @param pageSize     分页
     * @param filterField  过滤条件 名字和工号字段模糊搜索
     * @param startTime    根据创建时间过滤 开始时间
     * @param endTime      结束时间
     * @param filterAdmin  过滤是否是admin
     * @param filterStatus 筛选 是否是正常用户
     * @return
     */
    @Override
    public PageResponse<List<UserAuthDO>> selectUserAuth(int pageNo, int pageSize, String filterField, String startTime
            , String endTime, String filterAdmin, String filterStatus) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        if (StrUtil.isNotBlank(filterField)) {
            userAuthDOExample.or().andUserNameLike("%" + filterField + "%");
            if (drmConfig.isIntranetApplication()) {
                userAuthDOExample.or().andEmpIdLike("%" + filterField + "%");
            }
            else {
                userAuthDOExample.or().andPhoneNumberLike("%" + filterField + "%");
                userAuthDOExample.or().andAlipayAccountLike("%" + filterField + "%");
                if (NumberUtil.isInteger(filterField)){
                    userAuthDOExample.or().andIdEqualTo(Long.valueOf(filterField));
                }

            }
        }

        UserAuthDOExample.Criteria criteria = userAuthDOExample.createCriteria();
        if (StrUtil.isNotBlank(filterStatus)) {
            criteria.andStatusEqualTo(UserStatusEnum.getUserStatusEnumByValue(Integer.parseInt(filterStatus)));
        }
        if (StrUtil.isNotBlank(filterAdmin)) {
            criteria.andAdminEqualTo(Byte.valueOf(filterAdmin));
        }
        if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
            criteria.andGmtCreateBetween(DateUtil.parse(startTime), DateUtil.parse(endTime));
        }
        Page<UserAuthDO> pageInfo = PageHelper.startPage(pageNo, pageSize);


        userAuthDOExample.setOrderByClause("gmt_create DESC");

        List<UserAuthDO> userAuthDOS = userAuthDOMapper.selectByExample(userAuthDOExample);
        return PageResponse.build(ResponseEnum.SUCCESS, userAuthDOS, pageInfo.getTotal());
    }


    /**
     * 添加用户
     */
    @Override
    public List<String> insertUserAuth(List<String> empIds) {
        // 新增用户失败 把empid 返回
        List<String> userIds = new ArrayList<>();
        for (String empId : empIds) {

            UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
            userAuthDOExample.createCriteria().andEmpIdEqualTo(empId);
            List<UserAuthDO> userAuthDOS = userAuthDOMapper.selectByExample(userAuthDOExample);
            if (!CollectionUtil.isEmpty(userAuthDOS)) {
                // 已有用户并且是已通过不进行更改
                if (userAuthDOS.get(0).getStatus() == UserStatusEnum.ACTIVE) {
                    continue;
                }
                // 已有用户直接更改状态为通过
                updateUserStatus(Collections.singletonList(userAuthDOS.get(0).getId()), true);
                continue;
            }
            JSONObject user = null;
            // 调用研发洞察失败 保存empId 继续执行
            try {
                user = JSON.parseObject(devInsightService.queryUser(empId).get(0).toString());
            } catch (Exception e) {
                LOGGER.error("empId:{}查询buc失败 error{}", empId, e.getMessage());
            }
            if (user == null) {
                userIds.add(empId);
                continue;
            }
            UserAuthDO userAuthDO = new UserAuthDO();
            userAuthDO.setEmpId(empId);
            userAuthDO.setUserName((String) user.get("nickName"));
            userAuthDO.setBuName("");
            String token = ShortUid.getUid();
            userAuthDO.setToken(token);
            userAuthDO.setAdmin((byte) 0);
            userAuthDO.setAlipayAccount((String) user.get("account"));
            userAuthDO.setStatus(UserStatusEnum.ACTIVE);
            int insert = userAuthDOMapper.insertSelective(userAuthDO);
            if (insert != 1) {
                userIds.add(empId);
            }
        }
        UserAuthDO curUserAuthDO = ContextUtil.get(ORIGIN_USER, UserAuthDO.class);
        LOGGER.info("操作人:{},添加用户:{},时间:{}", curUserAuthDO != null ? curUserAuthDO.getUserName() : "system", empIds, new DateTime());
        return userIds;
    }

    @Override
    public void updateUserStatus(List<Long> userIdList, Boolean cancel) {
        userIdList.forEach(userId ->{
            UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
            userAuthDOExample.createCriteria().andIdEqualTo(userId);
            UserAuthDO userAuthDO = new UserAuthDO();
            if (cancel) {
                userAuthDO.setStatus(UserStatusEnum.ACTIVE);
            } else {
                userAuthDO.setStatus(UserStatusEnum.VERIFY);
            }
            userAuthDOMapper.updateByExampleSelective(userAuthDO, userAuthDOExample);
            UserAuthDO userAuthDODb = userAuthDOMapper.selectByPrimaryKey(userId);
            defaultCacheManager.del(AppConstants.CACHE_PREFIX + userAuthDODb.getEmpId());
        });
        UserAuthDO curUserAuth = ContextUtil.get(ORIGIN_USER, UserAuthDO.class);
        LOGGER.info("操作人:{},{}白名单:{},时间:{}", curUserAuth.getUserName(),cancel ? "添加":"取消",userIdList,new DateTime());
    }

    /**
     * 根据empId转换成内部自增用户Id
     * @param empId 用户empId
     * @return
     */
    @Override
    public Long empId2UserId(String empId) {

        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdEqualTo(empId);
        List<UserAuthDO> userAuthDOList = userAuthDOMapper.selectByExample(userAuthDOExample);
        if(CollectionUtils.isEmpty(userAuthDOList)) {
            LOGGER.warn("getting userAuth empty, empId : {}", empId);
            return null;
        }

        if(userAuthDOList.size() > 1) {
            LOGGER.warn("multi user auth records existed, empId : {}", empId);
        }

        return userAuthDOList.get(0).getId();
    }

    @Override
    public String selectEmpIdByUserId(Long userId) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andIdEqualTo(userId);
        List<UserAuthDO> userAuthDOList = userAuthDOMapper.selectByExample(userAuthDOExample);
        if(CollectionUtils.isEmpty(userAuthDOList) || userAuthDOList.size() != 1) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"userId repeat,userId:"+userId);
        }
        return userAuthDOList.get(0).getEmpId();
    }

    /**
     * 根据id查询用户信息
     *
     * @param ids
     * @return
     */
    @Override
    public List<UserAuthDO> getUserByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andIdIn(ids);
        return userAuthDOMapper.selectByExample(userAuthDOExample);
    }

    /**
     * 根据empId查询用户信息
     *
     * @param empIds
     * @return
     */
    @Override
    public List<UserAuthDO> getUserByEmpIds(List<String> empIds) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdIn(empIds);
        return userAuthDOMapper.selectByExample(userAuthDOExample);
    }

    @Override
    public void updateAllowAccessType(UpdateAllowAccessTypeVO updateAllowAccessTypeVO) {
        UserAuthDOExample example = new UserAuthDOExample();
        example.createCriteria().andIdEqualTo(updateAllowAccessTypeVO.getUserId());
        UserAuthDO record = new UserAuthDO();
        record.setAllowAccessType(updateAllowAccessTypeVO.getAllowAccessType().getValue());
        userAuthDOMapper.updateByExampleSelective(record,example);
    }

    @Override
    public UserAuthDO selectByPrimaryKey(Long id) {
        return userAuthDOMapper.selectByPrimaryKey(id);
    }
}
