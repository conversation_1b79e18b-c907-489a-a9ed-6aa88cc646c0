package com.alipay.codegencore.model.openai;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0 2024/8/15 14:49
 */
public class CodeInfoFile {

    private String filePath;

    private String codeContent;

    private String diffCodeSnippet;

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getCodeContent() {
        return codeContent;
    }

    public void setCodeContent(String codeContent) {
        this.codeContent = codeContent;
    }

    public String getDiffCodeSnippet() {
        return diffCodeSnippet;
    }

    public void setDiffCodeSnippet(String diffCodeSnippet) {
        this.diffCodeSnippet = diffCodeSnippet;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", CodeInfoFile.class.getSimpleName() + "[", "]")
                .add("filePath='" + filePath + "'")
                .add("codeContent='" + codeContent + "'")
                .add("diffCodeSnippet='" + diffCodeSnippet + "'")
                .toString();
    }
}
