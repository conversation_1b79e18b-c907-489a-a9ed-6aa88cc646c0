package com.alipay.codegencore.service.codegpt.user;

import cn.hutool.core.lang.Pair;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.AlgoBackendImplEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.common.limiter.RateLimitFactory;
import com.alipay.codegencore.service.handler.*;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;

/**
 * 算法模型的执行器
 */
public class AlgoModelExecutor {

    private final static AlgoModelExecutor INSTANCE = new AlgoModelExecutor();

    /**
     * 获取实例
     *
     * @return
     */
    public static AlgoModelExecutor getInstance() {
        return INSTANCE;
    }

    private AlgoModelExecutor() {
    }

    /**
     * 非流式对话调用
     *
     * @param algoBackendDO
     * @param param
     * @return
     */
    public ChatMessage executorChat(AlgoBackendDO algoBackendDO, GptAlgModelServiceRequest param) {
        if (algoBackendDO == null || StringUtils.isBlank(algoBackendDO.getImpl())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型的的impl配置是空");
        }
        // 执行模型限流
        executeModelRateLimit(algoBackendDO, param);
        AbstractAlgLanguageHandler abstractAlgLanguageHandler = getHandler(algoBackendDO.getImpl());
        if (abstractAlgLanguageHandler == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "当前handler不存在");
        }
        return abstractAlgLanguageHandler.chat(param);
    }

    /**
     * 流式对话调用
     *
     * @param algoBackendDO
     * @param param
     * @return
     */
    public Flux<String> executorStreamChat(AlgoBackendDO algoBackendDO, GptAlgModelServiceRequest param) {
        if (algoBackendDO == null || StringUtils.isBlank(algoBackendDO.getImpl())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "模型的的impl配置是空");
        }
        // 执行模型限流
        executeModelRateLimit(algoBackendDO, param);
        AbstractAlgLanguageHandler abstractAlgLanguageHandler = getHandler(algoBackendDO.getImpl());
        if (abstractAlgLanguageHandler == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "当前handler不存在");
        }
        return abstractAlgLanguageHandler.chatOnStream(param);
    }

    private AbstractAlgLanguageHandler getHandler(String impl) {
        AlgoBackendImplEnum algoBackendImpl = AlgoBackendImplEnum.valueOf(impl);
        if (AlgoBackendImplEnum.ChatGptModelHandler == algoBackendImpl) {
            return new ChatGptModelHandler();
        } else if (AlgoBackendImplEnum.CodeGPTModelHandler == algoBackendImpl) {
            return new CodeGPTModelHandler();
        } else if (AlgoBackendImplEnum.AntGLMModelHandler == algoBackendImpl) {
            return new AntGLMModelHandler();
        } else if (AlgoBackendImplEnum.MayaStreamModelHandler == algoBackendImpl) {
            return new MayaStreamModelHandler();
        } else if (AlgoBackendImplEnum.ChatGptModelHubHandler == algoBackendImpl) {
            return new ChatGptModelHubHandler();
        }
        return null;
    }

    private void executeModelRateLimit(AlgoBackendDO algoBackendDO, GptAlgModelServiceRequest param) {
        // webapi进来的请求有请求的用户id
        if (param.getRequestUserId() != null) {
            Pair<Boolean, Long> limitPair = RateLimitFactory.getInstance().tryAcquireAlgoFromWebApi(param.getRequestUserId(), algoBackendDO.getModel());
            if (!limitPair.getKey()) {
                throw new BizException(ResponseEnum.WEB_API_REQUEST_MODEL_LIMITING_ANOMALY, ResponseEnum.WEB_API_REQUEST_MODEL_LIMITING_ANOMALY.getErrorMsg() + ",触发限流规则id:" + limitPair.getValue());
            }
        } else if (StringUtils.isNotBlank(param.getRequestTokenUser())) {
            // openapi进来的请求有请求的tokenUser
            Pair<Boolean, Long> limitPair = RateLimitFactory.getInstance().tryAcquireAlgoFromOpenApi(param.getRequestTokenUser(), algoBackendDO.getModel());
            if (!limitPair.getKey()) {
                throw new BizException(ResponseEnum.OPEN_API_REQUEST_MODEL_LIMITING_ANOMALY, ResponseEnum.OPEN_API_REQUEST_MODEL_LIMITING_ANOMALY.getErrorMsg() + ",触发限流规则id:" + limitPair.getValue());
            }
        }
    }

}
