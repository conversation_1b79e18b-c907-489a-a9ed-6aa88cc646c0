package com.alipay.codegencore.model.openai;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.11.14
 */
public class PluginContext {
    /**
     * 光标之前的代码
     */
    private String codeBeforeCursor;
    /**
     * 光标之后的代码
     */
    private String codeAfterCursor;
    /**
     * 当前打开的文件
     */
    private String currentOpenFile;
    /**
     * 最近打开的文件
     */
    private List<String> recentFiles;

    @JSONField(name = "code_before_cursor")
    public String getCodeBeforeCursor() {
        return codeBeforeCursor;
    }

    public void setCodeBeforeCursor(String codeBeforeCursor) {
        this.codeBeforeCursor = codeBeforeCursor;
    }

    @JSONField(name = "code_after_cursor")
    public String getCodeAfterCursor() {
        return codeAfterCursor;
    }

    public void setCodeAfterCursor(String codeAfterCursor) {
        this.codeAfterCursor = codeAfterCursor;
    }

    @JSONField(name = "current_open_file")
    public String getCurrentOpenFile() {
        return currentOpenFile;
    }

    public void setCurrentOpenFile(String currentOpenFile) {
        this.currentOpenFile = currentOpenFile;
    }

    @JSONField(name = "recent_files")
    // 最近打开的文件
    public List<String> getRecentFiles() {
        return recentFiles;
    }

    public void setRecentFiles(List<String> recentFiles) {
        this.recentFiles = recentFiles;
    }
}
