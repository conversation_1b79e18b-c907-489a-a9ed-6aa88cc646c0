/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alipay.codegencore.model.model.links.Enum.ReplaysSystemEnum;

import java.util.Date;
import java.util.List;

/**
 * 诊断工具配置信息
 * <AUTHOR>
 * @version $Id: ReplaysConfigModel.java, v 0.1 2021-09-24 10:10 wb-tzg858080 Exp $$
 */
public class ReplaysConfigInfo extends ToString {
    /**
     *  系统名称
     */
    private  String name ;
    /**
     *  来源系统
     */

    private ReplaysSystemEnum system ;
    /**
     * 第三方系统id
     */
    private  String   configId ;

    /**
     *  是否收藏
     */
    private  boolean collected ;

    /**
     *  表单配置
     * @param system
     * @param configId
     * @param name
     */
    private List<FormItemInfo>  formItems;

    /**
     *  links的id
     */
    private  String linksId ;

    /**
     * 创建时间
     */
    private Date created ;
    /**
     * 更新时间
     */
    private Date updated;

    /**
     * links展示的结果信息
     */
    private LinksReplaysResultInfo linksResultData;

    /**
     * 诊断工具执行状态
     */
    private ReplaysTaskStatus status ;

    /**
     * 是否对外
     */
    private boolean isOut = false;

    /**
     * 描述
     */
    private String remarks ;

    /**
     * 当前工具的环境值
     * 主站为main
     * 网商为bank
     */
    private String tenant;

    /**
     * 自定义字段
     * （不一定有，有值的情况，执行工具要带上）
     */
    private String customFields;

    /**
     * 非必需字段
     * （不一定有，有值的情况，执行工具要带上）
     */
    private List<Object> notRequiredFields;

    /**
     * 行业工具扩展字段
     */
    private IndustryToolConfigInfo industryToolConfig;

    /**
     * 任务id
     */
    private String taskId;

    /**
     *  构造器
     * @param system
     * @param configId
     * @param name
     * @param collected
     * @param linksId
     */
    public ReplaysConfigInfo(ReplaysSystemEnum system, String configId , String name, boolean collected , String linksId, boolean isOut) {
        this.name = name;
        this.system = system;
        this.configId = configId;
        this.linksId = linksId ;
        this.collected = collected ;
        this.isOut = isOut;
    }

    /**
     *  无参数构造器
     */
    public ReplaysConfigInfo() {
    }
}
