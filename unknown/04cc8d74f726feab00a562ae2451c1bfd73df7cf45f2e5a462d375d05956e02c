package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.domain.GptConversationDO;
import com.alipay.codegencore.dal.example.GptConversationDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface GptConversationDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    long countByExample(GptConversationDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    int deleteByExample(GptConversationDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    @Delete({
        "delete from links_gpt_conversation",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    @Insert({
        "insert into links_gpt_conversation (gmt_create, gmt_modified, ",
        "mongo_id, deleted, ",
        "room_id, user_id, ",
        "status, channel, ",
        "biz_id, gmt_last_message, ",
        "title, conversation_id, ",
        "ext_info)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{mongoId,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, ",
        "#{roomId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, ",
        "#{status,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, ",
        "#{bizId,jdbcType=VARCHAR}, #{gmtLastMessage,jdbcType=TIMESTAMP}, ",
        "#{title,jdbcType=VARCHAR}, #{conversationId,jdbcType=VARCHAR}, ",
        "#{extInfo,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(GptConversationDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    int insertSelective(GptConversationDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    List<GptConversationDO> selectByExample(GptConversationDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, mongo_id, deleted, room_id, user_id, status, channel, ",
        "biz_id, gmt_last_message, title, conversation_id, ext_info",
        "from links_gpt_conversation",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("com.alipay.codegencore.dal.mapper.GptConversationDOMapper.BaseResultMap")
    GptConversationDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    int updateByExampleSelective(@Param("record") GptConversationDO record, @Param("example") GptConversationDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    int updateByExample(@Param("record") GptConversationDO record, @Param("example") GptConversationDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    int updateByPrimaryKeySelective(GptConversationDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table links_gpt_conversation
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    @Update({
        "update links_gpt_conversation",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "mongo_id = #{mongoId,jdbcType=VARCHAR},",
          "deleted = #{deleted,jdbcType=TINYINT},",
          "room_id = #{roomId,jdbcType=VARCHAR},",
          "user_id = #{userId,jdbcType=VARCHAR},",
          "status = #{status,jdbcType=VARCHAR},",
          "channel = #{channel,jdbcType=VARCHAR},",
          "biz_id = #{bizId,jdbcType=VARCHAR},",
          "gmt_last_message = #{gmtLastMessage,jdbcType=TIMESTAMP},",
          "title = #{title,jdbcType=VARCHAR},",
          "conversation_id = #{conversationId,jdbcType=VARCHAR},",
          "ext_info = #{extInfo,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(GptConversationDO record);
}