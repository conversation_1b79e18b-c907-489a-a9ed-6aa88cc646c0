package com.alipay.codegencore.utils.http;

import org.apache.http.util.CharsetUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.concurrent.Flow;
import java.util.function.Consumer;

/**
 * 关闭body的订阅者
 *
 * <AUTHOR>
 * 创建时间 2023-04-11
 */
public class ErrorBodySubscriber implements Flow.Subscriber<List<ByteBuffer>> {

    private static final Logger LOGGER = LoggerFactory.getLogger( ErrorBodySubscriber.class );

    private final Consumer<String> errorHandler;

    private Flow.Subscription subscription;

    /**
     * 构造函数
     *
     * @param errorHandler 错误处理器
     */
    public ErrorBodySubscriber(Consumer<String> errorHandler) {
        this.errorHandler = errorHandler;
    }

    @Override
    public void onSubscribe(Flow.Subscription subscription) {
        this.subscription = subscription;
        subscription.request(1);
    }

    /**
     * Method invoked with a Subscription's next item.  If this
     * method throws an exception, resulting behavior is not
     * guaranteed, but may cause the Subscription to be cancelled.
     *
     * @param item the item
     */
    @Override
    public void onNext(List<ByteBuffer> item) {

        try {
            String response = CharsetUtils.get("UTF-8").decode(item.get(0)).toString();
            LOGGER.info("error response:{}",response);

            errorHandler.accept(response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        subscription.request(1);
    }

    /**
     * 接收信息过程中发生异常
     * 如果接收器不为null,则cancel接收器,断开连接
     * @param throwable the exception
     */
    @Override
    public void onError(Throwable throwable) {
        if(subscription!=null){
            subscription.cancel();
        }
    }

    /**
     * 请求完成
     */
    @Override
    public void onComplete() {

    }

}
