package com.alipay.codegencore.utils.http;

import java.util.concurrent.Flow;

/**
 * 流式数据监听器
 *
 * <AUTHOR>
 * 创建时间 2023-04-10
 */
public interface StreamDataListener {
    /**
     * 数据连接成功
     *
     * @param data
     */
    void onConnect(Flow.Subscription subscription);

    /**
     * 监听到每一个数据
     *
     * @param data
     */
    void eachData(String data, Flow.Subscription subscription);

    /**
     * 接收到错误
     *
     * @param throwable
     */
    void onError(Throwable throwable);

    /**
     * 接收到完成
     */
    void onComplete();
}
