package com.alipay.codegencore.web.remote;

import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.remote.AgentObject;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.web.remote.vo.RemoteAgentBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 11:33
 */
@Slf4j
@RestController
@RequestMapping("/v1/agent")
public class AgentController {


    @Autowired
    private SceneService sceneService;

    @Autowired
    private UserAclService userAclService;

    /**
     * 检索 Agent
     * 使用场景
     * 通过 agent_id 查询 Agent 元数据
     * @param agentId
     * @return
     */
    @GetMapping("/{agent_id}")
    public RemoteAgentBaseResponse<AgentObject> getAgent(@PathVariable("agent_id") String agentId) {

        Long sceneId = NumberUtils.toLong(agentId);
        Long userId = userAclService.getCurrentUser().getId();
        boolean available = sceneService.availableSceneUser(sceneId, userId);
        log.info("get agent:{} user:{} available:{}", sceneId, userId, available);
        if (!available) {
            return RemoteAgentBaseResponse.buildFail(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }

        SceneDO sceneDO = sceneService.getSceneById(sceneId);

        return RemoteAgentBaseResponse.buildSuccess(AgentObject.of(sceneDO));
    }

}
