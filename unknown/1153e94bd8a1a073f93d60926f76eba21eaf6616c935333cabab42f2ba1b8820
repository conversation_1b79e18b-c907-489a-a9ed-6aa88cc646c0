/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.aop.handler;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.web.remote.TaskController;
import com.alipay.codegencore.web.remote.vo.RemoteAgentBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @version GlobalExceptionHandler.java, v 0.1 2023年03月27日 15:57 xiaobin
 */
@Slf4j
@RestControllerAdvice(assignableTypes = {TaskController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RemoteAgentTaskExceptionHandler {


    /**
     * 对象传入的JSON格式错误异常
     * @param e
     * @return
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public RemoteAgentBaseResponse handleException(HttpMessageNotReadableException e) {
        log.error("统一异常处理捕获HttpMessageNotReadableException异常", e);
        return RemoteAgentBaseResponse.buildFail(ResponseEnum.HTTP_MESSAGE_ERROR);
    }

}