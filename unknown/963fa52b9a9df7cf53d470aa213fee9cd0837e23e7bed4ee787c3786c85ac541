package com.alipay.codegencore.model.request;

/**
 * 文件内容请求bean
 *
 * <AUTHOR>
 * 创建时间 2022-03-01
 */
public class FileContentRequestBean extends AbstractClientModel {
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件内容
     */
    private String fileContent;

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }
}
