package com.alipay.codegencore.service.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import reactor.core.publisher.Flux;

/**
 * maya平台的antglm模型处理器
 */
public class MayaStreamModelHandler extends AbstractAlgLanguageHandler{

    private final LanguageModelService mayaStreamModelService;

    public MayaStreamModelHandler() {
        this.mayaStreamModelService = SpringUtil.getBean("mayaStreamModelService");
    }

    @Override
    public ChatMessage chat(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        return mayaStreamModelService.chat(gptAlgModelServiceRequest);
    }

    @Override
    public Flux<String> chatOnStream(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        mayaStreamModelService.streamChatForServlet(gptAlgModelServiceRequest);
        return null;
    }

}
