package com.alipay.codegencore.model.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.contant.AlgoImplConfigKey;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 模型配置工具类
 * <AUTHOR>
 */
public class AlgoBackendUtil {
    /**
     * 获取配置值map
     * @param algoBackendDO
     * @return
     */
    public static Map<String,Object> getImplConfigMap(AlgoBackendDO algoBackendDO){
        return (Map<String, Object>) JSON.parseObject(algoBackendDO.getImplConfig(), Map.class);
    }

    /**
     * 获取配置值基础方法
     * @param key
     * @return
     */
    public static Object getImplConfigByKey(AlgoBackendDO algoBackendDO, String key){
        Map<String,Object> config = getImplConfigMap(algoBackendDO);
        if(config == null) {
            return null;
        }
        return config.get(key);
    }

    /**
     * 通用返回配置
     * @param algoBackendDO
     * @param key
     * @param clazz
     * @return
     * @param <T>
     */
    public static <T> T getImplConfigByKey(AlgoBackendDO algoBackendDO, String key, Class<T> clazz) {
        JSONObject configObject = JSON.parseObject(algoBackendDO.getImplConfig());
        return configObject.getObject(key, clazz);
    }

    /**
     * 提取服务器地址配置
     * @return
     */
    public static String exactServerConfig(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.SERVER);
    }

    /**
     * 提取频率惩罚配置
     * @return
     */
    public static Double exactFrequencyPenaltyConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.FREQUENCY_PENALTY);
        if (implConfigByKey instanceof String){
            return Double.valueOf((String)implConfigByKey);
        }
        return (Double) implConfigByKey;
    }

    /**
     * 提取存在惩罚配置
     * @return
     */
    public static Double exactPresencePenaltyConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.PRESENCE_PENALTY);
        if (implConfigByKey instanceof String){
            return Double.valueOf((String)implConfigByKey);
        }
        return (Double) implConfigByKey;
    }

    /**
     * 提取n值
     * @return
     */
    public static Integer exactNConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.N);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 提取温度值配置
     * @return
     */
    public static BigDecimal exactTemperatureConfig(AlgoBackendDO algoBackendDO){
        String value = (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.TEMPERATURE);
        if(value == null){
            return null;
        }

        return new BigDecimal(value);
    }

    /**
     * 提取topP值配置
     * @return
     */
    public static BigDecimal exactTopPConfig(AlgoBackendDO algoBackendDO){
        String value = (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.TOP_P);
        if(value == null){
            return null;
        }

        return new BigDecimal(value);
    }

    /**
     * 提取topK值配置
     * @return
     */
    public static Integer exactTopKConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.TOP_K);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }
    /**
     * 提取maxToken值配置
     * @return
     */
    public static Integer exactOutSeqLengthConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.OUT_SEQ_LENGTH);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }


    /**
     * 提取maya场景名配置
     * @return
     */
    public static String exactSceneNameConfig(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.SCENE_NAME);
    }

    /**
     * 提取maya chainName配置
     * @return
     */
    public static String exactChainNameConfig(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.CHAIN_NAME);
    }

    /**
     * 提取模型配置中的token
     * @return
     */
    public static String exactTokenConfig(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.TOKEN);
    }

    /**
     * 提取请求超时时间配置
     * @return
     */
    public static Integer exactRequestTimeOutConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.REQUEST_TIME_OUT);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 提取beamWidth配置
     * @return
     */
    public static Integer exactBeamWidthConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.BEAM_WIDTH);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 提取长度惩罚配置
     * @return
     */
    public static Integer exactLenPenaltyConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.LEN_PENALTY);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }
    /**
     * 获取默认的systemPrompt
     * @return
     */
    public static String exactSystemPromptConfig(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.SYSTEM_PROMPT);
    }

    /**
     * 获取批量化处理时的时间窗口大小，时间单位为毫秒
     */
    public static Integer exactBatchTimeWindowConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.BATCH_TIME_WINDOW);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 获取批量化处理时的批处理大小，大于此值就发送
     */
    public static Integer exactBatchSizeConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.BATCH_SIZE);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 流式回答数据包轮询步长,单位毫秒
     * @return
     */
    public static Integer exactStreamDataPollingStepConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.STREAM_DATA_POLLING_STEP);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 第一个流式回答数据包等待超时时间,单位毫秒
     * @return
     */
    public static Integer exactFirstStreamDataWaitTimeConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.FIRST_STREAM_DATA_WAIT_TIME);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 流式回答数据包等待超时时间,单位毫秒
     * @return
     */
    public static Integer exactCommonStreamDataWaitTimeConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.COMMON_STREAM_DATA_WAIT_TIME);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 流式接口中句子的最小长度
     * @return
     */
    public static Integer exactSentenceMinLengthConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.SENTENCE_MIN_LENGTH);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 流式接口中句子长度的增长步长
     * @return
     */
    public static Integer exactSentenceLengthGrowthStepConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.SENTENCE_LENGTH_GROWTH_STEP);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 流式接口中句子的最大长度
     * @return
     */
    public static Integer exactSentenceMaxLengthConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.SENTENCE_MAX_LENGTH);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 流式接口中缓冲器的最小长度
     * @return
     */
    public static Integer exactBufferMinLengthConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.BUFFER_MIN_LENGTH);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 流式接口中缓冲器的长度的增长步长
     * @return
     */
    public static Integer exactBufferLengthGrowthStepConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.BUFFER_LENGTH_GROWTH_STEP);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 流式接口中缓冲器的最大长度
     * @return
     */
    public static Integer exactBufferMaxLengthConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.BUFFER_MAX_LENGTH);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 请求maya数据参数的map的key
     * @param algoBackendDO
     * @return
     */
    public static String exactRequestMayaDataKeyConfig(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.REQUEST_MAYA_DATA_KEY);
    }

    /**
     * maya返回值的参数中map的key
     * @param algoBackendDO
     * @return
     */
    public static String exactResponseMayaDataKeyConfig(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.RESPONSE_MAYA_DATA_KEY);
    }

    /**
     * maya输入输出的格式版本
     * @param algoBackendDO
     * @return
     */
    public static String exactMayaDataVersion(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.RESPONSE_MAYA_DATA_VERSION);
    }


    /**
     * 请求法务大模型的算法的参数
     */
    public static Integer exactNumBeamsConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.NUM_BEAMS);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }


    /**
     * 获取请求模型配置
     * @return
     */
    public static String exactRequestModelModel(AlgoBackendDO algoBackendDO){
        return (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.REQUEST_MODEL);
    }
    /**
     * 获取重复惩罚配置
     * @return
     */
    public static BigDecimal exactRepetitionPenaltyConfig(AlgoBackendDO algoBackendDO){
        String value = (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.REPETITION_PENALTY);
        if(value == null){
            return null;
        }

        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,
                    "value:" + value + "__algoBackendDO:" + JSONObject.toJSONString(algoBackendDO));
        }
    }

    /**
     * 获取随机种子配置
     * @return
     */
    public static BigDecimal exactRandomSeedConfig(AlgoBackendDO algoBackendDO){
        String value = (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.RANDOM_SEED);
        if(value == null){
            return null;
        }

        return new BigDecimal(value);
    }

    /**
     * 获取AntGLM的模型名称信息
     * index1 模型名称
     * index2 模型版本
     * @return
     */
    public static String[] exactModelInfoConfig(AlgoBackendDO algoBackendDO) {
        //配置有更新 如果可以读取到modelId和version优先使用
        String modelId = (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.MODEL_ID);
        String version = (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.VERSION);
        return new String[]{modelId, version};
    }
    /**
     * AntGLM的连接时间
     * @return
     */
    public static Integer exactConnTimeoutConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.CONN_TIMEOUT);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }
    /**
     * AntGLM的最大输出长度-按需设置
     * @return
     */
    public static Integer exactMaxOutputLengthConfig(AlgoBackendDO algoBackendDO){
        Object implConfigByKey = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.MAX_OUTPUT_LENGTH);
        if (implConfigByKey instanceof String){
            return Integer.valueOf((String)implConfigByKey);
        }
        return (Integer) implConfigByKey;
    }

    /**
     * 获取句子长度
     *
     * @param algoBackendDO 配置
     * @param batchId       从1开始
     * @return
     */
    public static Integer getSentenceLength(AlgoBackendDO algoBackendDO, int batchId) {
        int sentenceLength = exactSentenceMinLengthConfig(algoBackendDO) + exactSentenceLengthGrowthStepConfig(algoBackendDO) * (batchId - 1);
        return Math.min(sentenceLength, exactSentenceMaxLengthConfig(algoBackendDO));
    }

    /**
     * 获取缓冲器大小
     *
     * @param algoBackendDO 配置
     * @param batchId       从1开始
     * @return
     */
    public static Integer getBufferWindowLength(AlgoBackendDO algoBackendDO, int batchId, boolean repoChat) {
        if(repoChat){
            return 1;
        }
        int bufferLength = exactBufferMinLengthConfig(algoBackendDO) + exactBufferLengthGrowthStepConfig(algoBackendDO) * (batchId - 1);
        return Math.min(bufferLength, exactBufferMaxLengthConfig(algoBackendDO));
    }

    /**
     * 获取maya模型部署环境信息
     * @return
     */
    public static String exactModelEnvConfig(AlgoBackendDO algoBackendDO){
        return  (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.MODEL_ENV);
    }

    /**
     * 获取maya模型停止词信息
     * @return
     */
    public static List<String> exactStopWordsConfig(AlgoBackendDO algoBackendDO){
        Object stopWords = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.STOP_WORDS);
        if (stopWords == null) {
            return null;
        }
        if (stopWords instanceof String) {
            return JSON.parseArray((String) stopWords, String.class);
        } else if (stopWords instanceof List) {
            return JSON.parseArray(JSON.toJSONString(stopWords), String.class);
        } else {
            return null;
        }
    }

    /**
     * 获取maya模型停止token信息
     * @return
     */
    public static List<String> exactLastTokensConfig(AlgoBackendDO algoBackendDO){
        Object lastTokens = getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.LAST_TOKENS);
        if (lastTokens == null) {
            return null;
        }
        if (lastTokens instanceof String) {
            return JSON.parseArray((String) lastTokens, String.class);
        } else if (lastTokens instanceof List) {
            return JSON.parseArray(JSON.toJSONString(lastTokens), String.class);
        } else {
            return null;
        }
    }

    /**
     * 获取扩展字段
     * @param algoBackendDO
     * @return
     */
    public static Map<String, Object> exactExtInfoConfig(AlgoBackendDO algoBackendDO){
        String extInfo = (String) getImplConfigByKey(algoBackendDO, AlgoImplConfigKey.EXT_INFO);
        if (extInfo == null || extInfo.isEmpty()) {
            return null;
        }
        return JSON.parseObject(extInfo).getInnerMap();
    }

}
