package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.antwork.facade.enums.ChatMessageStatusEnum;
import com.alipay.antwork.facade.request.ChatMessageStatusUpdateRequest;
import com.alipay.antwork.facade.result.Result;
import com.alipay.antwork.facade.service.ChatMessageWriteFacade;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ContentCheckSceneCodeEnum;
import com.alipay.codegencore.model.model.InfoSecAsyncUpdateDbModel;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.common.ProcessInfoSecAsyncMsgService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.common.event.UniformEvent;
import com.alipay.common.event.UniformEventContext;
import com.alipay.common.event.UniformEventMessageListener;
import com.alipay.infosec.common.shared.model.consant.SecCheckResultCode;
import com.alipay.infosec.content.service.facade.result.InfoSecCheckResult;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * infosec平台的异步消息监听
 */
@Service("infosecMsgBrokerListener")
public class InfosecMsgBrokerListener implements UniformEventMessageListener {

    private static final String INFOSEC_RISK_CONTENT_RESULT = "INFOSEC_RISK_CONTENT_RESULT";

    private static final Integer INFOSEC_TBASE_TIMEOUT_SECOND = 5 * 60;

    private static final Logger LOGGER = LoggerFactory.getLogger(InfosecMsgBrokerListener.class);

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private ChatMessageWriteFacade chatMessageWriteFacade;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private ChatSessionManageService chatSessionManageService;
    @Resource
    private ProcessInfoSecAsyncMsgService processInfoSecAsyncMsgService;

    // PASSED 和 RECOVER 这两个状态码是可以正常展示给用户的数据,除此之外都不能展示给用户
    @Override
    public void onUniformEvent(UniformEvent uniformEvent, UniformEventContext uniformEventContext) {
        JSONObject payload = JSON.parseObject(JSON.toJSONString(uniformEvent.getEventPayload()));
        JSONObject checkResult = payload.getJSONObject("data").getJSONObject(INFOSEC_RISK_CONTENT_RESULT);
        InfoSecCheckResult infoSecCheckResult = JSON.parseObject(JSON.toJSONString(checkResult), InfoSecCheckResult.class);
        try {
            // resultCode 的取值是 com.alipay.infosec.common.shared.model.consant.SecCheckResultCode 的 name
            String resultCode = infoSecCheckResult.getResultValue().getResultAction();
            // 对于消息的文本送检,appSceneDataId存储的是「messageUid_批次号_批次号」的格式,类似:a4ab9435-2267-44a4-a8cb-8f212f1c38d6_1_1
            String appSceneDataId = infoSecCheckResult.getBaseContent().getAppSceneDataId();
            Date publishDate = infoSecCheckResult.getBaseContent().getPublishDate();
            Map<String,Object> extDataMap = infoSecCheckResult.getBaseContent().getExtData();
            String appScene = infoSecCheckResult.getBaseContent().getAppScene();
            ContentCheckSceneCodeEnum contentCheckSceneCode = ContentCheckSceneCodeEnum.getBySceneCode(appScene);
            OTHERS_LOGGER.info("infosec的回调数据,appScene:{},extData:{},appSceneDataId:{},resultCode:{},publishDate:{}",
                    appScene, JSON.toJSONString(extDataMap), appSceneDataId, resultCode, publishDate);
            // 从 dataId 中拆出来消息的uid
            String messageUid = appSceneDataId.split("_")[0];
            ReviewResultModel reviewResultModel = new ReviewResultModel();
            if (SecCheckResultCode.PASSED.name().equals(resultCode) ||
                    SecCheckResultCode.RECOVER.name().equals(resultCode)) {
                reviewResultModel.setRet(true);
            } else {
                reviewResultModel.setRet(false);
            }
            reviewResultModel.setCode(resultCode);
            // 记录数据到tbase,必须先写tbase,因为同步阻塞的正在等待这个结果
            refreshableCommonTbaseCacheManager.setex(AppConstants.CODEGENCORE_INFOSEC_REVIEW_KEY_+appSceneDataId,INFOSEC_TBASE_TIMEOUT_SECOND,JSON.toJSONString(reviewResultModel));

            // 异步更新db
            processInfoSecAsyncMsgService.addInfoSecAsyncRequest(new InfoSecAsyncUpdateDbModel(messageUid,contentCheckSceneCode,reviewResultModel,System.currentTimeMillis()));

            // 如果发给infoSec的时间距离此刻收到回调>同步阻塞等待的时间才需要处理,否则说明同步阻塞等待时已经处理
            boolean needProcess = false;
            long costTime = System.currentTimeMillis() - publishDate.getTime();
            if (contentCheckSceneCode == ContentCheckSceneCodeEnum.CTO_TECHPLAY_QUESTION) {
                needProcess = costTime > codeGPTDrmConfig.getAsyCheckMaxWaitTimeQuestion();
            } else if (contentCheckSceneCode == ContentCheckSceneCodeEnum.CTO_TECHPLAY_ANSWER) {
                needProcess = costTime > codeGPTDrmConfig.getAsyCheckMaxWaitTimeAnswer();
            }
            List<String> list = Lists.newArrayList(AppConstants.TECHPLAY_TOKEN_USER,AppConstants.WORK_CHAT_TOKEN_USER,AppConstants.ANT_CHAT_TOKEN_USER);
            if (list.contains(extDataMap.get("codeGPTUser")) && needProcess) {
                // TechPlay的数据把状态写回TechPlay系统
                ChatMessageStatusUpdateRequest chatMessageStatusUpdateRequest = new ChatMessageStatusUpdateRequest();
                chatMessageStatusUpdateRequest.setMessageId(extDataMap.get("bizId").toString());
                chatMessageStatusUpdateRequest.setStatus(reviewResultModel.isRet() ? ChatMessageStatusEnum.NORMAL.getCode() : ChatMessageStatusEnum.NON_COMPLIANT.getCode());
                Result<Boolean> result = chatMessageWriteFacade.updateChatMessageStatus(chatMessageStatusUpdateRequest);
                OTHERS_LOGGER.info("回调TechPlay完成,appScene:{},extData:{},appSceneDataId:{},resultCode:{},result:{}",
                        appScene,JSON.toJSONString(extDataMap),appSceneDataId,resultCode,JSON.toJSONString(result));
                if (result==null || !result.isSuccess()) {
                    CHAT_LOGGER.warn("回调TechPlay失败,appScene:{},extData:{},appSceneDataId:{},resultCode:{},result:{}",
                            appScene,JSON.toJSONString(extDataMap),appSceneDataId,resultCode,JSON.toJSONString(result));
                }
            }
        } catch (Exception e) {
            OTHERS_LOGGER.error("处理infosec回调异常,infoSecCheckResult:"+JSON.toJSONString(infoSecCheckResult),e);
            uniformEventContext.setRollbackOnly();
        }
    }
}
