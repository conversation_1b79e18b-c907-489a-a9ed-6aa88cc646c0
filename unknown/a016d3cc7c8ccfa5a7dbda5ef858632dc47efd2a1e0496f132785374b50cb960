<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.AnswerIndexBuildTaskMapper">

  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.AnswerIndexBuildTaskDO">

    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="repo_url" jdbcType="VARCHAR" property="repoUrl" />
    <result column="group_path" jdbcType="VARCHAR" property="groupPath" />
    <result column="project_path" jdbcType="VARCHAR" property="projectPath" />
    <result column="branch" jdbcType="VARCHAR" property="branch" />
    <result column="last_build_commit" jdbcType="VARCHAR" property="lastBuildCommit" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="last_build_start_time" jdbcType="TIMESTAMP" property="lastBuildStartTime" />
    <result column="last_build_end_time" jdbcType="TIMESTAMP" property="lastBuildEndTime" />
    <result column="summary_status" jdbcType="VARCHAR" property="summaryStatus" />
    <result column="question" jdbcType="VARCHAR" property="question" />
    <result column="build_scope_type" jdbcType="VARCHAR" property="buildScopeType" />
    <result column="priority" javaType="INTEGER" property="priority"/>
    <result column="task_record_id" jdbcType="BIGINT" property="taskRecordId"/>
  </resultMap>


  <sql id="all_column">
    id,
    gmt_create,
    gmt_modified,
    repo_url,
    group_path,
    project_path,
    branch,
    last_build_commit,
    state,
    last_build_start_time,
    last_build_end_time,
    summary_status,
    question,
    build_scope_type,
    task_record_id,
    priority
  </sql>


  <insert id="insertTask" useGeneratedKeys="true" keyProperty="id">
    insert into answer_index_build_task
    (repo_url, group_path, project_path,
    branch, state, last_build_commit, build_scope_type, priority)
    values
    (#{repoUrl}, #{groupPath}, #{projectPath},
    #{branch}, #{state}, #{lastBuildCommit}, #{buildScopeType}, #{priority})
  </insert>

  <select id="getById" resultMap="BaseResultMap">
    select id, repo_url, group_path, project_path,
    branch, state, last_build_commit, build_scope_type,
    last_build_start_time, last_build_end_time, summary_status,
    question, priority, task_record_id
    <where>
      id = #{id}
    </where>
  </select>

  <select id="getByRepoInfo" resultMap="BaseResultMap">
    select <include refid="all_column"/>
    from answer_index_build_task
      <where>
        group_path = #{groupPath}
        and project_path = #{projectPath}
        and branch = #{branch}
      </where>
  </select>

  <select id="getQuestion" resultMap="BaseResultMap">
    select id, summary_status, question
    from answer_index_build_task
    <where>
      group_path = #{groupPath}
      and project_path = #{projectPath}
      and branch = #{branch}
    </where>
  </select>

  <update id="updateTaskBuilding">
    update answer_index_build_task
    <set>
      state = #{state},
      <if test="lastBuildCommit != null">
        last_build_commit = #{lastBuildCommit},
      </if>
      <if test="taskRecordId != null">
        task_record_id = #{taskRecordId},
      </if>
      last_build_start_time = #{lastBuildStartTime}
    </set>
    <where>
      id = #{taskId}
    </where>
  </update>

  <update id="updateTaskFinish">
    update answer_index_build_task
    <set>
      state = "FINISH",
      last_build_end_time = #{lastBuildEndTime}
    </set>
    <where>
      id = #{taskId}
    </where>
  </update>

  <update id="updateWikiInfo">
    update answer_index_build_task
    <set>
      summary_status = #{summaryStatus}
      <if test="question != null">
        ,question = #{question}
      </if>
    </set>
    <where>
      id = #{taskId}
    </where>
  </update>

  <select id="queryBuildingTaskId" resultType="java.lang.Long">
    select id
    from answer_index_build_task
    <where>
      state = #{state}
      <if test="buildScopeType != null">
        and build_scope_type = #{buildScopeType}
      </if>
    </where>
    order by priority desc,id asc
    <if test="limit != null">
      limit #{limit}
    </if>
  </select>


  <select id="queryBuildingTask" resultMap="BaseResultMap">
    select id, repo_url, group_path, project_path,
    branch, state, last_build_commit, build_scope_type,
    last_build_start_time, last_build_end_time, summary_status,
    question, priority, task_record_id
    from answer_index_build_task
    <where>
      state = #{state}
      <if test="buildScopeType != null">
        and build_scope_type = #{buildScopeType}
      </if>
    </where>
    order by priority desc, id asc
    <if test="limit != null">
      limit #{limit}
    </if>
  </select>

  <select id="getTaskByIdAndTime" resultMap="BaseResultMap">
    select <include refid="all_column"/>
    from answer_index_build_task
    where state = "FINISH"
    <if test="id != null">
      <if test="endTime == null and startTime == null">
        and id = #{id}
      </if>
      <if test="startTime != null and endTime != null">
        and id > #{id} and last_build_end_time BETWEEN #{startTime} AND #{endTime}
      </if>
    </if>
    <if test="startTime != null and endTime != null and id == null">
      and last_build_end_time BETWEEN #{startTime} AND #{endTime}
    </if>
    limit 100
  </select>

  <select id="getTaskById" resultMap="BaseResultMap">
    select <include refid="all_column"/>
    from answer_index_build_task
    where state = "FINISH"
    <if test="id != null">
       and id > #{id}
    </if>
    order by id asc
    limit 50
  </select>


  <delete id="deleteByRepoInfo">
    delete from answer_index_build_task
    where
    group_path = #{groupPath}
    and project_path = #{projectPath}
    and branch = #{branch}
  </delete>

</mapper>