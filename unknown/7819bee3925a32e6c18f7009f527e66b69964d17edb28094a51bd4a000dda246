package com.alipay.codegencore.service.common.segment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.SegmentationStrategyTypeEnum;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.OssService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.http.HttpClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.09.03
 */
@Service
public class YuQueSegmentationStrategy implements SegmentationStrategy{
    private static final Logger LOGGER = LoggerFactory.getLogger(YuQueSegmentationStrategy.class);
    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");
    private static final int MAX_RETRY = 3;

    @Resource
    private OssService ossService;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private ConfigService configService;

    @Override
    public List<String> segment(String text, JSONObject documentChatConfig) {
        // 先写入一个jsonl文件
        int maxLength = documentChatConfig.getInteger("everyPartMaxSize");
        String uid = ShortUid.getUid();
        JSONObject jsonl = generateJsonlContent(text,uid);
        String ossUrl = ossService.putObject(AppConstants.DOCUMENT_SOURCE_FILE+uid + ".jsonl", IOUtils.toInputStream(jsonl.toJSONString(), "UTF-8"), 1, AppConstants.CONTENT_TYPE_DOWNLOAD);
        // 请求分段服务
        return getSegmentList(maxLength, ossUrl);
    }

    @Override
    public SegmentationStrategyTypeEnum getType() {
        return SegmentationStrategyTypeEnum.YUQUE_STRATEGY;
    }
    /**
     * 获取分段结果
     *
     * <AUTHOR>
     * @since 2024.09.05
     * @param maxLength maxLength
     * @param ossUrl ossUrl
     * @return java.util.List<java.lang.String>
     */
    @Nullable
    private List<String> getSegmentList(int maxLength, String ossUrl) {
        String strategyOssPath = getYuqueSegmentationStrategyOssUrl(ossUrl, maxLength);
        int retry = 0;
        while (true) {
            retry++;
            // 语雀分段是异步处理，需要检查文件是否生成
            if(ossService.isExist(strategyOssPath)){
                String content = ossService.getString(strategyOssPath);
                if(StringUtils.isNotBlank(content)){
                    // 删除分段生成的文件
                    ossService.deleteFile(strategyOssPath);
                    return buildSegmentResult(content);
                }
            }
            if(retry > MAX_RETRY){
                LOGGER.info("can not find the jsonl file,ossPath:{}",strategyOssPath);
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                LOGGER.error("YuQueSegmentationStrategy sleep error",e);
            }
        }
        return null;
    }

    /**
     * 生成jsonl格式内容
     *
     * <AUTHOR>
     * @since 2024.09.05
     * @param content content
     * @param fileName fileName
     * @return com.alibaba.fastjson.JSONObject
     */
    private JSONObject generateJsonlContent(String content,String fileName){
        JSONObject jsonl = new JSONObject();
        jsonl.put("content", content);
        jsonl.put("name",fileName);
        jsonl.put("type","text");
        jsonl.put("doc_id",fileName);
        jsonl.put("meta",new JSONObject());
        return jsonl;
    }

    /**
     * 请求语雀分段接口
     *
     * <AUTHOR>
     * @since 2024.09.05
     * @param originalOssUrl originalOssUrl
     * @param maxSize maxSize
     * @return java.lang.String
     */
    private String getYuqueSegmentationStrategyOssUrl(String originalOssUrl,int maxSize) {
        JSONObject ossConfigJson = JSON.parseObject(codeGPTDrmConfig.getOssConfig());
        String bucketName = ossConfigJson.getString("bucketName");
        String endpoint = ossConfigJson.getString("endpoint");
        String ossAccessKey = configService.getConfigByKey(AppConstants.OSS_ACCESS_KEY, false);
        String ossSecretKey = configService.getConfigByKey(AppConstants.OSS_SECRET_KEY, false);
        JSONObject requestData = new JSONObject();
        JSONObject features = new JSONObject();
        JSONObject ossParam = new JSONObject();
        ossParam.put("oss_access_key_id",ossAccessKey);
        ossParam.put("oss_access_key_secret",ossSecretKey);
        ossParam.put("oss_bucket",bucketName);
        ossParam.put("oss_endpoint",endpoint);
        ossParam.put("oss_key_prefix","yuQueSegmentation/");
        features.put("oss_url",originalOssUrl);
        features.put("doc_id_name","doc_id");
        features.put("content_name","content");
        features.put("dataset_name","codegencore_yuque_segmentation");
        features.put("max_doc_chunk_size",String.valueOf(maxSize));
        features.put("ext",JSONObject.toJSONString(ossParam));
        requestData.put("features",features);
        JSONObject langChainResponse = null;
        try {
            String response = HttpClient.post(codeGPTDrmConfig.getYuQueSegmentationStrategyUrl())
                    .content(requestData.toJSONString())
                    .header("content-type", "application/json")
                    .header("MPS-app-name", "maya")
                    .header("MPS-http-version", "1.0")
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSON.parseObject(response);
            if (langChainResponse!=null&&langChainResponse.getBoolean("success")){
                return langChainResponse.getJSONObject("resultMap").getJSONArray("oss_obj_names").toJavaList(String.class).get(0);
            }
            if (langChainResponse != null) {
                LOGGER.info("YuQueSegmentationStrategy getYuqueSegmentationStrategyOssUrl error for {}",langChainResponse.getString("errorMessage"));
            }
        }catch (Exception e){
            LOGGER.error("YuQueSegmentationStrategy getYuqueSegmentationStrategyOssUrl error",e);
        }
        return null;
    }
    /**
     * 组装分段结果
     *
     * <AUTHOR>
     * @since 2024.09.05
     * @param content content
     * @return java.util.List<java.lang.String>
     */
    private List<String> buildSegmentResult(String content) {
        List<String> result = new ArrayList<>();
        try {
            JSONObject segmentJson = JSON.parseObject(content);
            List<String> contents = segmentJson.getJSONArray("content").toJavaList(String.class);
            JSONArray splitTextHeadings = segmentJson.getJSONArray("split_text_headings");
            for (int i = 0; i < contents.size(); i++) {
                List<String> heads = splitTextHeadings.getJSONArray(i).toJavaList(String.class);
                if(CollectionUtils.isNotEmpty(heads)){
                    StringBuilder sb = new StringBuilder();
                    heads.forEach(head->{sb.append(head).append("\n");});
                    result.add(sb + contents.get(i));
                }else {
                    result.add(contents.get(i));
                }
            }
        }catch (Exception e){
            LOGGER.info("YuQueSegmentationStrategy buildSegmentResult error",e);
        }
        OTHERS_LOGGER.info("YuQueSegmentationStrategy buildSegmentResult result:{}", JSON.toJSONString(result));
        return result;
    }
}
