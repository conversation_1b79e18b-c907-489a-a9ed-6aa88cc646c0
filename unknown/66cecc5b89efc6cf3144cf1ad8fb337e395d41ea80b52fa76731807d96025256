package com.alipay.codegencore.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PluginDOExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public PluginDOExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigIsNull() {
            addCriterion("workflow_config is null");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigIsNotNull() {
            addCriterion("workflow_config is not null");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigEqualTo(String value) {
            addCriterion("workflow_config =", value, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigNotEqualTo(String value) {
            addCriterion("workflow_config <>", value, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigGreaterThan(String value) {
            addCriterion("workflow_config >", value, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigGreaterThanOrEqualTo(String value) {
            addCriterion("workflow_config >=", value, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigLessThan(String value) {
            addCriterion("workflow_config <", value, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigLessThanOrEqualTo(String value) {
            addCriterion("workflow_config <=", value, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigLike(String value) {
            addCriterion("workflow_config like", value, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigNotLike(String value) {
            addCriterion("workflow_config not like", value, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigIn(List<String> values) {
            addCriterion("workflow_config in", values, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigNotIn(List<String> values) {
            addCriterion("workflow_config not in", values, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigBetween(String value1, String value2) {
            addCriterion("workflow_config between", value1, value2, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andWorkflowConfigNotBetween(String value1, String value2) {
            addCriterion("workflow_config not between", value1, value2, "workflowConfig");
            return (Criteria) this;
        }

        public Criteria andEnableIsNull() {
            addCriterion("enable is null");
            return (Criteria) this;
        }

        public Criteria andEnableIsNotNull() {
            addCriterion("enable is not null");
            return (Criteria) this;
        }

        public Criteria andEnableEqualTo(Integer value) {
            addCriterion("enable =", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotEqualTo(Integer value) {
            addCriterion("enable <>", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThan(Integer value) {
            addCriterion("enable >", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableGreaterThanOrEqualTo(Integer value) {
            addCriterion("enable >=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThan(Integer value) {
            addCriterion("enable <", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableLessThanOrEqualTo(Integer value) {
            addCriterion("enable <=", value, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableIn(List<Integer> values) {
            addCriterion("enable in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotIn(List<Integer> values) {
            addCriterion("enable not in", values, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableBetween(Integer value1, Integer value2) {
            addCriterion("enable between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andEnableNotBetween(Integer value1, Integer value2) {
            addCriterion("enable not between", value1, value2, "enable");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Integer value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Integer value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Integer value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Integer value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Integer> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Integer> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Integer value1, Integer value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUsageCountIsNull() {
            addCriterion("usage_count is null");
            return (Criteria) this;
        }

        public Criteria andUsageCountIsNotNull() {
            addCriterion("usage_count is not null");
            return (Criteria) this;
        }

        public Criteria andUsageCountEqualTo(Long value) {
            addCriterion("usage_count =", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountNotEqualTo(Long value) {
            addCriterion("usage_count <>", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountGreaterThan(Long value) {
            addCriterion("usage_count >", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountGreaterThanOrEqualTo(Long value) {
            addCriterion("usage_count >=", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountLessThan(Long value) {
            addCriterion("usage_count <", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountLessThanOrEqualTo(Long value) {
            addCriterion("usage_count <=", value, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountIn(List<Long> values) {
            addCriterion("usage_count in", values, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountNotIn(List<Long> values) {
            addCriterion("usage_count not in", values, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountBetween(Long value1, Long value2) {
            addCriterion("usage_count between", value1, value2, "usageCount");
            return (Criteria) this;
        }

        public Criteria andUsageCountNotBetween(Long value1, Long value2) {
            addCriterion("usage_count not between", value1, value2, "usageCount");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNull() {
            addCriterion("owner_user_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNotNull() {
            addCriterion("owner_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdEqualTo(Long value) {
            addCriterion("owner_user_id =", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotEqualTo(Long value) {
            addCriterion("owner_user_id <>", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThan(Long value) {
            addCriterion("owner_user_id >", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("owner_user_id >=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThan(Long value) {
            addCriterion("owner_user_id <", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThanOrEqualTo(Long value) {
            addCriterion("owner_user_id <=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIn(List<Long> values) {
            addCriterion("owner_user_id in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotIn(List<Long> values) {
            addCriterion("owner_user_id not in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdBetween(Long value1, Long value2) {
            addCriterion("owner_user_id between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotBetween(Long value1, Long value2) {
            addCriterion("owner_user_id not between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andIconUrlIsNull() {
            addCriterion("icon_url is null");
            return (Criteria) this;
        }

        public Criteria andIconUrlIsNotNull() {
            addCriterion("icon_url is not null");
            return (Criteria) this;
        }

        public Criteria andIconUrlEqualTo(String value) {
            addCriterion("icon_url =", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotEqualTo(String value) {
            addCriterion("icon_url <>", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlGreaterThan(String value) {
            addCriterion("icon_url >", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlGreaterThanOrEqualTo(String value) {
            addCriterion("icon_url >=", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLessThan(String value) {
            addCriterion("icon_url <", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLessThanOrEqualTo(String value) {
            addCriterion("icon_url <=", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlLike(String value) {
            addCriterion("icon_url like", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotLike(String value) {
            addCriterion("icon_url not like", value, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlIn(List<String> values) {
            addCriterion("icon_url in", values, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotIn(List<String> values) {
            addCriterion("icon_url not in", values, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlBetween(String value1, String value2) {
            addCriterion("icon_url between", value1, value2, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconUrlNotBetween(String value1, String value2) {
            addCriterion("icon_url not between", value1, value2, "iconUrl");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorIsNull() {
            addCriterion("icon_background_color is null");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorIsNotNull() {
            addCriterion("icon_background_color is not null");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorEqualTo(String value) {
            addCriterion("icon_background_color =", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorNotEqualTo(String value) {
            addCriterion("icon_background_color <>", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorGreaterThan(String value) {
            addCriterion("icon_background_color >", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorGreaterThanOrEqualTo(String value) {
            addCriterion("icon_background_color >=", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorLessThan(String value) {
            addCriterion("icon_background_color <", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorLessThanOrEqualTo(String value) {
            addCriterion("icon_background_color <=", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorLike(String value) {
            addCriterion("icon_background_color like", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorNotLike(String value) {
            addCriterion("icon_background_color not like", value, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorIn(List<String> values) {
            addCriterion("icon_background_color in", values, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorNotIn(List<String> values) {
            addCriterion("icon_background_color not in", values, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorBetween(String value1, String value2) {
            addCriterion("icon_background_color between", value1, value2, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andIconBackgroundColorNotBetween(String value1, String value2) {
            addCriterion("icon_background_color not between", value1, value2, "iconBackgroundColor");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsIsNull() {
            addCriterion("use_instructions is null");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsIsNotNull() {
            addCriterion("use_instructions is not null");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsEqualTo(String value) {
            addCriterion("use_instructions =", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsNotEqualTo(String value) {
            addCriterion("use_instructions <>", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsGreaterThan(String value) {
            addCriterion("use_instructions >", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsGreaterThanOrEqualTo(String value) {
            addCriterion("use_instructions >=", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsLessThan(String value) {
            addCriterion("use_instructions <", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsLessThanOrEqualTo(String value) {
            addCriterion("use_instructions <=", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsLike(String value) {
            addCriterion("use_instructions like", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsNotLike(String value) {
            addCriterion("use_instructions not like", value, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsIn(List<String> values) {
            addCriterion("use_instructions in", values, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsNotIn(List<String> values) {
            addCriterion("use_instructions not in", values, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsBetween(String value1, String value2) {
            addCriterion("use_instructions between", value1, value2, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andUseInstructionsNotBetween(String value1, String value2) {
            addCriterion("use_instructions not between", value1, value2, "useInstructions");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andAciInfoIsNull() {
            addCriterion("aci_info is null");
            return (Criteria) this;
        }

        public Criteria andAciInfoIsNotNull() {
            addCriterion("aci_info is not null");
            return (Criteria) this;
        }

        public Criteria andAciInfoEqualTo(String value) {
            addCriterion("aci_info =", value, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoNotEqualTo(String value) {
            addCriterion("aci_info <>", value, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoGreaterThan(String value) {
            addCriterion("aci_info >", value, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoGreaterThanOrEqualTo(String value) {
            addCriterion("aci_info >=", value, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoLessThan(String value) {
            addCriterion("aci_info <", value, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoLessThanOrEqualTo(String value) {
            addCriterion("aci_info <=", value, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoLike(String value) {
            addCriterion("aci_info like", value, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoNotLike(String value) {
            addCriterion("aci_info not like", value, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoIn(List<String> values) {
            addCriterion("aci_info in", values, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoNotIn(List<String> values) {
            addCriterion("aci_info not in", values, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoBetween(String value1, String value2) {
            addCriterion("aci_info between", value1, value2, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andAciInfoNotBetween(String value1, String value2) {
            addCriterion("aci_info not between", value1, value2, "aciInfo");
            return (Criteria) this;
        }

        public Criteria andVisableUserIsNull() {
            addCriterion("visable_user is null");
            return (Criteria) this;
        }

        public Criteria andVisableUserIsNotNull() {
            addCriterion("visable_user is not null");
            return (Criteria) this;
        }

        public Criteria andVisableUserEqualTo(Integer value) {
            addCriterion("visable_user =", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotEqualTo(Integer value) {
            addCriterion("visable_user <>", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserGreaterThan(Integer value) {
            addCriterion("visable_user >", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserGreaterThanOrEqualTo(Integer value) {
            addCriterion("visable_user >=", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserLessThan(Integer value) {
            addCriterion("visable_user <", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserLessThanOrEqualTo(Integer value) {
            addCriterion("visable_user <=", value, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserIn(List<Integer> values) {
            addCriterion("visable_user in", values, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotIn(List<Integer> values) {
            addCriterion("visable_user not in", values, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserBetween(Integer value1, Integer value2) {
            addCriterion("visable_user between", value1, value2, "visableUser");
            return (Criteria) this;
        }

        public Criteria andVisableUserNotBetween(Integer value1, Integer value2) {
            addCriterion("visable_user not between", value1, value2, "visableUser");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_plugin
     *
     * @mbg.generated do_not_delete_during_merge Fri Aug 09 11:17:31 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_plugin
     *
     * @mbg.generated Fri Aug 09 11:17:31 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}