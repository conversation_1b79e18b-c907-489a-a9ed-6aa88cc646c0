package com.alipay.codegencore.web.codegpt;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ControlTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.openai.AciInfo;
import com.alipay.codegencore.model.openai.PluginVO;
import com.alipay.codegencore.model.openai.UserPluginRecordsVO;
import com.alipay.codegencore.model.openai.UserSaveSceneVO;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.PluginService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.UserPluginRecordsService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.tool.learning.PluginConfigService;
import com.alipay.codegencore.utils.ListUtils;
import com.alipay.codegencore.utils.thread.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alipay.codegencore.model.contant.WebApiContents.ORIGIN_USER;

/**
 * 插件管理
 *
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.web.codegpt
 * @CreateTime : 2023-07-11
 */
@Slf4j
@CodeTalkWebApi
@RestController
@RequestMapping("/webapi/plugin")
public class PluginController {

    @Resource
    private PluginService pluginService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private SceneService sceneService;

    @Resource
    private PluginConfigService pluginConfigService;

    @Resource
    private UserPluginRecordsService userPluginRecordsService;

    /**
     * 获取所有插件
     *
     * @return
     */
    @GetMapping(path = "/getAllPlugin")
    public BaseResponse<List<PluginDO>> getAllPlugin() {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(pluginService.getAllPlugin());
    }

    /**
     * 获取所有插件
     *
     * @return
     */
    @GetMapping(path = "/getPluginByPage")
    public PageResponse<List<PluginVO>> getPluginByPage(@RequestParam(required = false) String query,
                                                        @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                        @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        List<PluginDO> pluginDOList = pluginService.getEnablePlugin(query);
        List<PluginVO> pluginVOList = new ArrayList<>();
        pluginDOList.forEach(pluginDO -> {
            PluginVO pluginVO = new PluginVO();
            BeanUtils.copyProperties(pluginDO, pluginVO);
            pluginVO.setAciConfig(JSONObject.parseObject(pluginDO.getAciInfo(), AciInfo.class));
            pluginVOList.add(pluginVO);
        });
        pluginVOList.forEach(pluginVO -> {
            if (pluginVO.getOwnerUserId() != null) {
                UserAuthDO userAuth = userAclService.selectByUserId(pluginVO.getOwnerUserId());
                if (userAuth != null) {
                    pluginVO.setOwnerUserEmpId(userAuth.getEmpId());
                    pluginVO.setOwnerUserName(userAuth.getUserName());
                }
            }
            if(StringUtils.isBlank(pluginVO.getType())){
                Map<String, Object> data = pluginConfigService.parseWorkFlowYaml(pluginVO.getWorkflowConfig());
                pluginVO.setType(pluginConfigService.getPluginTypeByInfo(data.get("info")));
            }
        });
        List<PluginVO> retList = ListUtils.getPageList(pluginVOList, pageSize, pageNo);
        return PageResponse.build(ResponseEnum.SUCCESS, retList, (long) pluginVOList.size());
    }

    /**
     * 根据插件id获取插件
     *
     * @param pluginId
     * @return
     */
    @GetMapping(path = "/getPluginById")
    public BaseResponse<PluginVO> getPluginById(@RequestParam Long pluginId) {
        UserAuthDO userAuthDO = ContextUtil.get(ORIGIN_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if(!pluginService.checkEditPluginAuth(pluginId, userAuthDO.getId())){
            throw new BizException(ResponseEnum.NO_AUTH,"你没有编辑该插件的权限");
        }
        PluginDO pluginDO = pluginService.getPluginById(pluginId);
        if (pluginDO == null) {
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        PluginVO pluginVO = new PluginVO();
        BeanUtils.copyProperties(pluginDO, pluginVO);
        pluginVO.setAciConfig(JSONObject.parseObject(pluginDO.getAciInfo(), AciInfo.class));
        List<UserSaveSceneVO> allSceneByEnable = sceneService.getAllSceneByEnable("");
        List<UserSaveSceneVO> useScene = allSceneByEnable.stream()
                .filter(sceneVO -> sceneVO.getMode() == 1)
                .filter(sceneVO -> JSONArray.parseArray(sceneVO.getPluginList(), Long.class).contains(pluginId)).collect(Collectors.toList());
        // 隐藏语雀敏感信息
        for (UserSaveSceneVO userSaveSceneVO : useScene) {
            userSaveSceneVO.setYuqueTokenList("[]");
        }
        pluginVO.setUseScene(useScene);
        if (pluginVO.getOwnerUserId() != null) {
            UserAuthDO userAuth = userAclService.selectByUserId(pluginVO.getOwnerUserId());
            if (userAuth != null) {
                pluginVO.setOwnerUserEmpId(userAuth.getEmpId());
                pluginVO.setOwnerUserName(userAuth.getUserName());
            }
        }
        if(StringUtils.isBlank(pluginVO.getType())){
            Map<String, Object> data = pluginConfigService.parseWorkFlowYaml(pluginVO.getWorkflowConfig());
            pluginVO.setType(pluginConfigService.getPluginTypeByInfo(data.get("info")));
        }
        return BaseResponse.build(pluginVO);
    }

    /**
     * 根据工具名称获取工具
     *
     * @param pluginName
     * @return
     */
    @GetMapping(path = "/getPluginByName")
    public BaseResponse<PluginVO> getPluginByName(@RequestParam String pluginName){
        UserAuthDO userAuthDO = ContextUtil.get(ORIGIN_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        PluginDO pluginDO = pluginService.getPluginByName(pluginName);
        PluginVO pluginVO = new PluginVO();
        if(pluginDO != null){
            BeanUtils.copyProperties(pluginDO, pluginVO);
        }
        return BaseResponse.build(pluginVO);
    }

    /**
     * 新增插件
     *
     * @param pluginDO
     * @return
     */
    @PostMapping(path = "/addPlugin")
    public BaseResponse<Long> addPlugin(@RequestBody PluginDO pluginDO) {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(pluginService.addPlugin(pluginDO));
    }

    /**
     * 更新插件信息
     *
     * @param pluginDO
     * @return
     */
    @PostMapping(path = "/updatePlugin")
    public BaseResponse<Boolean> updatePlugin(@RequestBody PluginDO pluginDO) {
        if (!userAclService.isAdmin()) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        return BaseResponse.build(pluginService.updatePlugin(pluginDO));
    }

    /**
     * 删除插件
     *
     * @param id 插件id
     * @return
     */
    @GetMapping(path = "/deletePlugin")
    public BaseResponse<Boolean> deletePlugin(@RequestParam Long id) {
        return BaseResponse.build(pluginService.deletePlugin(id));
    }

    /**
     * 覆盖权限
     * <AUTHOR>
     * @since 2024.08.12
     * @param pluginVO pluginVO
     * @return com.alipay.codegencore.model.response.BaseResponse<java.lang.Boolean>
     */
    @PostMapping(path = "/overridePluginUserControl")
    public BaseResponse<Boolean> overrideSceneUserControl(@RequestBody PluginVO pluginVO) {
        if (pluginVO == null ||
                pluginVO.getId() == null ||
                pluginVO.getControlTypeEnum() == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        if (!pluginService.checkEditPluginAuth(pluginVO.getId(), userAuthDO.getId())) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        PluginDO pluginDO = pluginService.getPluginById(pluginVO.getId());
        if(pluginDO == null){
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"没有此工具");
        }
        userPluginRecordsService.overridePluginUserControl(pluginDO, pluginVO.getControlTypeEnum(), pluginVO.getEmpIdList());
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取工具的权限信息
     * <AUTHOR>
     * @since 2024.08.12
     * @param pluginId pluginId
     * @param query query
     * @param controlTypeEnum controlTypeEnum
     * @param pageNo pageNo
     * @param pageSize pageSize
     * @return com.alipay.codegencore.model.response.PageResponse<java.util.List<com.alipay.codegencore.model.openai.UserPluginRecordsVO>>
     */
    @GetMapping(path = "/getPluginControlInfo")
    public PageResponse<List<UserPluginRecordsVO>> getSceneControlInfo(@RequestParam Long pluginId,
                                                                       @RequestParam(required = false) String query,
                                                                       @RequestParam(required = false) ControlTypeEnum controlTypeEnum,
                                                                       @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                       @RequestParam(value = "pageSize", defaultValue = "99999") int pageSize) {

        return userPluginRecordsService.getPluginControlInfo(pluginId, query, controlTypeEnum, pageNo, pageSize);
    }
}
