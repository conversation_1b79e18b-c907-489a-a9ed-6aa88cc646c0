package com.alipay.codegencore.model.model.yuque;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 语雀目录返回数据
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.01.17
 */
public class YuQueTocResponseModel {
    /**
     * @param uid
     */
    @JSONField(name = "uuid")
    private String uuid;
    /**
     * @param 类型 DOC,TITLE
     */
    @JSONField(name = "type")
    private String type;
    /**
     * @param 名称 (当前节点/文档的标题)
     */
    @JSONField(name = "title")
    private String title;
    /**
     * @param slug (当前节点/文档的slug)
     */
    @JSONField(name = "slug")
    private String slug;
    /**
     * @param 层级 从1开始，数字越大，层级越深
     */
    @JSONField(name = "depth")
    private Integer depth;
    /**
     * @param 文档id，非文档此字段为空
     */
    @JSONField(name = "doc_id")
    private Long docId;
    /**
     * @param 同级前（上）一个节点 uuid
     */
    @JSONField(name = "prev_uuid")
    private String prevUuid;
    /**
     * @param 同级后（下）一个节点 uuid
     */
    @JSONField(name = "sibling_uuid")
    private String siblingUuid;
    /**
     * @param 子级第一个节点uuid
     */
    @JSONField(name = "child_uuid")
    private String childUuid;
    /**
     * @param 父节点uid
     */
    @JSONField(name = "parent_uuid")
    private String parentUuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSlug() {
        return slug;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public Integer getDepth() {
        return depth;
    }

    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public String getPrevUuid() {
        return prevUuid;
    }

    public void setPrevUuid(String prevUuid) {
        this.prevUuid = prevUuid;
    }

    public String getSiblingUuid() {
        return siblingUuid;
    }

    public void setSiblingUuid(String siblingUuid) {
        this.siblingUuid = siblingUuid;
    }

    public String getChildUuid() {
        return childUuid;
    }

    public void setChildUuid(String childUuid) {
        this.childUuid = childUuid;
    }

    public String getParentUuid() {
        return parentUuid;
    }

    public void setParentUuid(String parentUuid) {
        this.parentUuid = parentUuid;
    }
}
