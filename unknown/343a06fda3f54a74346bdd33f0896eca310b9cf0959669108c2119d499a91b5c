package com.alipay.codegencore.model.openai;

import com.alipay.codegencore.model.domain.AlgoBackendDO;

/**
 * 请求携带的不需要序列化的额外字段
 */
public class ChatRequestExtData {

    /**
     * 用户工号
     */
    private String empId;

    /**
     * 用户支付宝账户(外网用户需要)
     */
    private String phoneNumber;

    /**
     * 业务id,发给审查平台
     */
    private String bizId;
    /**
     * 是否需要 keymap 做数据安全审查
     */
    private boolean keymapCheck;
    /**
     * 是否需要 antDsr 做数据安全审查
     */
    private boolean antDsrCheck;
    /**
     * 是否需要 infoSec 做内容安全审查
     */
    private boolean infoSecCheck;

    /**
     * 是否需要进行意图识别
     */
    private boolean intentionCheck;

    /**
     * 是否需要合规科技解语花平台审核
     */
    private boolean rcSmartCheck;
    /**
     * codeGPTUser
     */
    private String codeGPTUser;

    /**
     * 会话的uid,可以关联到这个会话的多轮问答的全部内容
     */
    private String sessionUid;
    /**
     * 是否支持灵犀多轮对话
     */
    private boolean lingXiMultipleRounds = true;
    /**
     * 内容是否是标题
     */
    private boolean contentIsTitle;

    /**
     * 审核内容的类型
     */
    private String contentType;

    /**
     * 使用的模型
     */
    private AlgoBackendDO algoBackendDO;

    public AlgoBackendDO getAlgoBackendDO() {
        return algoBackendDO;
    }

    public void setAlgoBackendDO(AlgoBackendDO algoBackendDO) {
        this.algoBackendDO = algoBackendDO;
    }

    public boolean isContentIsTitle() {
        return contentIsTitle;
    }

    public void setContentIsTitle(boolean contentIsTitle) {
        this.contentIsTitle = contentIsTitle;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public boolean getKeymapCheck() {
        return keymapCheck;
    }

    public void setKeymapCheck(boolean keymapCheck) {
        this.keymapCheck = keymapCheck;
    }

    public boolean getInfoSecCheck() {
        return infoSecCheck;
    }

    public void setInfoSecCheck(boolean infoSecCheck) {
        this.infoSecCheck = infoSecCheck;
    }

    public String getCodeGPTUser() {
        return codeGPTUser;
    }

    public void setCodeGPTUser(String codeGPTUser) {
        this.codeGPTUser = codeGPTUser;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public boolean getAntDsrCheck() {
        return antDsrCheck;
    }

    public void setAntDsrCheck(boolean antDsrCheck) {
        this.antDsrCheck = antDsrCheck;
    }

    public boolean getIntentionCheck() {
        return intentionCheck;
    }

    public void setIntentionCheck(boolean intentionCheck) {
        this.intentionCheck = intentionCheck;
    }

    public String getSessionUid() {
        return sessionUid;
    }

    public void setSessionUid(String sessionUid) {
        this.sessionUid = sessionUid;
    }

    public boolean getRcSmartCheck() {
        return rcSmartCheck;
    }

    public void setRcSmartCheck(boolean rcSmartCheck) {
        this.rcSmartCheck = rcSmartCheck;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public boolean isLingXiMultipleRounds() {
        return lingXiMultipleRounds;
    }

    public void setLingXiMultipleRounds(boolean lingXiMultipleRounds) {
        this.lingXiMultipleRounds = lingXiMultipleRounds;
    }
}
