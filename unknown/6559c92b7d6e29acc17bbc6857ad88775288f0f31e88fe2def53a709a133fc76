package com.alipay.codegencore.service.codegpt;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.model.FormUploadFileResponse;
import com.alipay.codegencore.model.model.SessionFileResponse;
import com.alipay.codegencore.model.openai.SceneConfigVO;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会话管理服务
 *
 * <AUTHOR>
 */
public interface ChatSessionManageService {


    /**
     * 获取一个空回话
     * @param userId
     * @param prompt
     * @param title
     * @param sceneId
     * @param modelTest
     * @param sourcePlatform
     * @param modelId
     * @return
     */
    ChatSessionDO getNewSession(Long userId, String prompt, String title,Long sceneId,Boolean modelTest,String sourcePlatform,Long modelId,boolean forceCreate);

    /**
     * 获取会话
     * @param sessionUId 会话uid
     * @return 会话信息
     */
    ChatSessionDO getChatSession(String sessionUId);

    /**
     * 获取当前用户会话列表
     *
     * @param userId 用户id
     * @return 会话列表
     */
    List<ChatSessionDO> listChatSession(Long userId);

    /**
     * 更新会话标题
     * @param sessionUid session uid
     * @param newTitle 新标题
     * @return 更新成功的条数
     */
    int updateSessionTitle(String sessionUid, String newTitle);

    /**
     * 更新会话使用的模型
     * 只会在会话中没有消息的时候才可以更新成功
     * @param sessionUid session uid
     * @param modelName 模型类型
     * @return 更新成功的条数
     */
    int updateSessionModelType(String sessionUid, String modelName);

    /**
     * 删除会话
     * @param sessionUid 会话uid
     */
    void deleteSession(List<String> sessionUidList);

    /**
     * 全部清除会话
     * @param userId 用户id
     */
    void clearSession(Long userId);


    /**
     * 插入一对消息
     * @param queryMessage 提问
     * @param answerMessage 答案
     */
    void insertPairChatMessage(ChatMessageDO queryMessage, ChatMessageDO answerMessage);

    /**
     * 是否允许用户更新标题为newTitle
     * @param sessionUid 会话id
     * @param newTitle 标题
     * @param currentUser 当前用户
     * @return true=允许更新
     */
    boolean checkUserUpdateTitle(UserAuthDO currentUser, String sessionUid, String newTitle);

    /**
     * 更新回话的模型配置
     * 只会在会话中没有消息的时候才可以更新成功
     *
     * @param sessionUid
     * @param requestBean
     * @return
     */
    int updateSessionModelConfig(String sessionUid, JSONObject requestBean);

    /**
     * 获取回话级别模型配置
     *
     * @param sessionUid
     * @return
     */
    JSONObject getSessionConfig(String sessionUid,Long sceneId);

    /**
     * 获取回话级别指定模型配置
     *
     * @param sessionUid
     * @return
     */
    List<SceneConfigVO> getSessionModelConfig(String sessionUid, Long sceneId, Boolean defaultConfig);

    /**
     * 修改会话信息
     *
     * @param chatSessionDO
     * @return
     */
    Boolean updateSession(ChatSessionDO chatSessionDO);


    /**
     * 上传session绑定的文件
     * @param sessionUid
     * @param file
     */
    SessionFileResponse uploadSessionFile(String sessionUid, MultipartFile file);

    /**
     * 上传文件到oss
     * @param sessionUid
     * @param file
     * @return
     */
    FormUploadFileResponse formUploadFile(MultipartFile file);

    boolean deleteSessionFile(String sessionUid, String fileUid);

    /**
     * 获取字符串的 Embedding 值
     * @param needEmbeddingStr 字符串
     * @return list
     */
    List<BigDecimal> getEmbeddingList(String needEmbeddingStr);

    /**
     * 用户是否有对应助手的会话记录
     * @param userId
     * @param sceneId
     * @return
     */
    boolean hasSceneSession(Long userId, Long sceneId);

    /**
     * 用户是否有对应模型的会话记录
     * @param userId
     * @param model
     * @return
     */
    boolean hasModelSession(Long userId, String model);

    /**
     * 绑定文档到会话
     * @param documentUid
     */
    void bindDocument(String sessionUid, String documentUid);

    /**
     * 解绑会话的文档
     * @param documentUid
     */
    void unbindDocument(String sessionUid, String documentUid);

    /**
     * 根据会话uidList获取未删除的非测试会话列表
     *

     * @return 会话列表
     */
    List<ChatSessionDO> getSessionByIdList(List<String> sessionUidList);
}
