package com.alipay.codegencore.model.model;

/**
 * 远程gpt模型配置
 *
 * <AUTHOR>
 * 创建时间 2023-02-06
 */
public class AntGptConfigModel {
    /**
     * top得分阈值
     */
    private double topScoreThreshold = -2;
    /**
     * 无效得分阈值
     */
    private double invalidScoreThreshold = -5;

    public double getTopScoreThreshold() {
        return topScoreThreshold;
    }

    public void setTopScoreThreshold(double topScoreThreshold) {
        this.topScoreThreshold = topScoreThreshold;
    }

    public double getInvalidScoreThreshold() {
        return invalidScoreThreshold;
    }

    public void setInvalidScoreThreshold(double invalidScoreThreshold) {
        this.invalidScoreThreshold = invalidScoreThreshold;
    }
}
