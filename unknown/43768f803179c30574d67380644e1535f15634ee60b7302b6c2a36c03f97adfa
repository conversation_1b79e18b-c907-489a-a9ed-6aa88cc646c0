package com.alipay.codegencore.model.domain;

import java.util.Date;

public class DocumentDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.gmt_create
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.gmt_modified
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.uid
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String uid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.document_name
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String documentName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.document_size
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private Long documentSize;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.document_status
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String documentStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.source
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.content_oss_url
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String contentOssUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.content_length
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private Long contentLength;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.segment_oss_url
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String segmentOssUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.summary
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String summary;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.ext_info
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String extInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.create_user_id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private Long createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.update_user_id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private Long updateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_document.zsearch_client
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    private String zsearchClient;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.id
     *
     * @return the value of cg_document.id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.id
     *
     * @param id the value for cg_document.id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.gmt_create
     *
     * @return the value of cg_document.gmt_create
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.gmt_create
     *
     * @param gmtCreate the value for cg_document.gmt_create
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.gmt_modified
     *
     * @return the value of cg_document.gmt_modified
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.gmt_modified
     *
     * @param gmtModified the value for cg_document.gmt_modified
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.uid
     *
     * @return the value of cg_document.uid
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getUid() {
        return uid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.uid
     *
     * @param uid the value for cg_document.uid
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setUid(String uid) {
        this.uid = uid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.document_name
     *
     * @return the value of cg_document.document_name
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getDocumentName() {
        return documentName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.document_name
     *
     * @param documentName the value for cg_document.document_name
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.document_size
     *
     * @return the value of cg_document.document_size
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Long getDocumentSize() {
        return documentSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.document_size
     *
     * @param documentSize the value for cg_document.document_size
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setDocumentSize(Long documentSize) {
        this.documentSize = documentSize;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.document_status
     *
     * @return the value of cg_document.document_status
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getDocumentStatus() {
        return documentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.document_status
     *
     * @param documentStatus the value for cg_document.document_status
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setDocumentStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.source
     *
     * @return the value of cg_document.source
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.source
     *
     * @param source the value for cg_document.source
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setSource(String source) {
        this.source = source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.content_oss_url
     *
     * @return the value of cg_document.content_oss_url
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getContentOssUrl() {
        return contentOssUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.content_oss_url
     *
     * @param contentOssUrl the value for cg_document.content_oss_url
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setContentOssUrl(String contentOssUrl) {
        this.contentOssUrl = contentOssUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.content_length
     *
     * @return the value of cg_document.content_length
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Long getContentLength() {
        return contentLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.content_length
     *
     * @param contentLength the value for cg_document.content_length
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setContentLength(Long contentLength) {
        this.contentLength = contentLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.segment_oss_url
     *
     * @return the value of cg_document.segment_oss_url
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getSegmentOssUrl() {
        return segmentOssUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.segment_oss_url
     *
     * @param segmentOssUrl the value for cg_document.segment_oss_url
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setSegmentOssUrl(String segmentOssUrl) {
        this.segmentOssUrl = segmentOssUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.summary
     *
     * @return the value of cg_document.summary
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getSummary() {
        return summary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.summary
     *
     * @param summary the value for cg_document.summary
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setSummary(String summary) {
        this.summary = summary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.ext_info
     *
     * @return the value of cg_document.ext_info
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getExtInfo() {
        return extInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.ext_info
     *
     * @param extInfo the value for cg_document.ext_info
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.create_user_id
     *
     * @return the value of cg_document.create_user_id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Long getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.create_user_id
     *
     * @param createUserId the value for cg_document.create_user_id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.update_user_id
     *
     * @return the value of cg_document.update_user_id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public Long getUpdateUserId() {
        return updateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.update_user_id
     *
     * @param updateUserId the value for cg_document.update_user_id
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_document.zsearch_client
     *
     * @return the value of cg_document.zsearch_client
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public String getZsearchClient() {
        return zsearchClient;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_document.zsearch_client
     *
     * @param zsearchClient the value for cg_document.zsearch_client
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    public void setZsearchClient(String zsearchClient) {
        this.zsearchClient = zsearchClient;
    }
}