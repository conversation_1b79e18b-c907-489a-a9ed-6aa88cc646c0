package com.alipay.codegencore.service.ideaevo;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0 2024/9/22 22:09
 */
public enum VatPlanType {

    PART_GEN, ALL_GEN;

    /**
     * 根据名称获取枚举
     * @param name
     * @return
     */
    public static VatPlanType getByName(String name) {
        return Arrays.stream(VatPlanType.values())
                .filter(item -> item.name().equalsIgnoreCase(name))
                .findFirst()
                .orElse(PART_GEN);
    }

    /**
     * 判断名称是否合法
     * @param name
     * @return
     */
    public static boolean valid(String name) {
        return Arrays.stream(VatPlanType.values())
                .anyMatch(item -> item.name().equalsIgnoreCase(name));
    }

}
