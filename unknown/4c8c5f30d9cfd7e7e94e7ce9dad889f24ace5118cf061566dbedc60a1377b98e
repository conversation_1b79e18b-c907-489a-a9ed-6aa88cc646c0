package com.alipay.codegencore.service.middle.msgbroker;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.dingding.DingDingUtil;
import com.alipay.common.event.UniformEvent;
import com.alipay.remoting.util.StringUtils;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.service.msgbroker
 * @CreateTime : 2023-04-19
 */
@Slf4j
@Service("codegencoreModelHealthInspectionListener")
public class CodegencoreModelHealthInspectionListener implements CodegencoreEventHandler {

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    /**
     * topic|eventCode的Key 一般用于注册
     */
    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_F_SC|EC_codegencore_model_health_inspection";

    private static final String DEFAULT_QUESTION="hello";

    private static final Logger LOGGER = LoggerFactory.getLogger(CodegencoreModelHealthInspectionListener.class);

    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;


    /**
     * 对ai模型进行健康监控
     *
     * @param message 统一事件
     */
    @Override
    public void handle(UniformEvent message) {

        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to health inspection");
        }

        appThreadPool.submit(()->{
            List<AlgoBackendDO> algoBackendDOList = algoBackendService.getAllAlgoBackend();

            String prefix = codeGPTDrmConfig.isIntranetApplication() ? "内网应用" : "外网应用";

            for (AlgoBackendDO algoBackendDO: algoBackendDOList) {

                boolean success = false;
                int maxRetry = 5;

                if(algoBackendDO.getEnable()!=null && !algoBackendDO.getEnable()){
                    continue;
                }
                if (algoBackendDO.getNeedHealthCheck()!=null && !algoBackendDO.getNeedHealthCheck()) {
                    continue;
                }

                String modelName = algoBackendDO.getModel();
                AlgoBackendDO realBackend = null;

                if(algoBackendDO.getJump() != null){
                    // 处理 jump 逻辑
                    realBackend = algoBackendService.getAlgoBackendByName(modelName);
                }else {
                    realBackend = algoBackendDO;
                }

                if(realBackend != null){
                    // 健康检查不走gptCache
                    realBackend.setEnableGptCache(false);
                    for (int i = 0; i < maxRetry; i++) {
                        try {
                            List<ChatMessage> chatMessages = List.of(new ChatMessage(ChatRoleEnum.USER.getName(), DEFAULT_QUESTION));
                            ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
                            chatCompletionRequest.setMessages(chatMessages);
                            chatCompletionRequest.setMaxTokens(10);

                            String requestId = ShortUid.getUid();
                            GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId,
                                    AppConstants.CODEGPT_TOKEN_USER, false, realBackend, chatCompletionRequest);
                            params.setUniqueAnswerId(requestId);
                            ChatMessage answer = AlgoModelExecutor.getInstance().executorChat(algoBackendDO, params);
                            if (isAnswerValid(answer, algoBackendDO.getJump())) {
                                LOGGER.info("{},定时任务调用ai模型成功: {}, {}", modelName,prefix, answer.getContent());
                                success = true;
                                break;
                            }
                        } catch (Throwable e) {
                            LOGGER.info("{},定时任务调用ai模型异常:{}", modelName, e);
                        }
                    }
                }

                if(!success){
                    DingDingUtil.sendMessage(prefix+",ai模型调用异常(来自codegencore): " + modelName+",realMode:"+ JSONObject.toJSONString(realBackend));
                }
            }
        });
    }

    private boolean isAnswerValid(ChatMessage answer, String model){
        if(answer == null || StringUtils.isBlank(answer.getContent())) {
            LOGGER.warn("{} : answer is null or content is empty", model);
            return false;
        }
        return true;
    }

    /**
     * 注册任务id
     *
     * @return
     */
    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }
}
