package com.alipay.codegencore.dal.mapper;

import java.util.List;

import com.alipay.codegencore.model.openai.UserSceneRecordsVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.dal.mapper
 * @CreateTime : 2023-08-21
 */
public interface UserSceneRecordsManualMapper {

    /**
     * 通过用户ID查询场景记录列表总数
     *
     * @param sceneId
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Select({"<script>"
            + "select  records.user_id "
            + "        ,records.scene_id "
            + "        ,records.id "
            + "        ,records.control_type "
            + "        ,user.user_name "
            + "        ,user.emp_id "
            + "from    cg_user_scene_records as records "
            + "        ,cg_user_auth as user "
            + "where   records.user_id = user.id  "
            + "and records.scene_id =#{sceneId} "
            + "and records.deleted =0 "
            + "<if test='controlType != null'> "
            + "and records.control_type = #{controlType}"
            + "</if>"
            + "<if test='query != null'> "
            + "and user.user_name like concat('%',#{query},'%') "
            + "</if>"
            + "  limit #{pageNo},#{pageSize} "
            + "</script>"})
    List<UserSceneRecordsVO> selectUser(@Param("sceneId") Long sceneId, @Param("query") String query, @Param("controlType") Integer controlType, @Param("pageNo") int pageNo,
                                        @Param("pageSize") int pageSize);

    /**
     * 通过用户ID查询场景记录列表总数
     *
     * @param sceneId
     * @param query
     * @return
     */
    @Select({"<script>"
            + "select  count(*) "
            + "from    cg_user_scene_records as records "
            + "        ,cg_user_auth as user "
            + "where   records.user_id = user.id  "
            + "and records.scene_id =#{sceneId} "
            + "and records.deleted =0 "
            + "<if test='controlType != null'> "
            + "and records.control_type = #{controlType}"
            + "</if>"
            + "<if test='query != null'> "
            + "and user.user_name like concat('%',#{query},'%') "
            + "</if>"
            + "</script>"})
    Long selectUserCount(@Param("sceneId") Long sceneId, @Param("query") String query, @Param("controlType") Integer controlType);

}
