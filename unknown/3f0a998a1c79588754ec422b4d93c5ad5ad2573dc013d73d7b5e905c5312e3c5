package com.alipay.codegencore.model.enums.limit;

/**
 * 限流模版
 */
public enum LimitTempleteEnum {

    /**
     * 这个模版值,说明当前模版规则对应了一条具体的模版,生效的限流key就是这个规则的id
     */
    RULE_ID("规则ID"),
    /**
     * 这个模版值,RULE_ID会被替换为规则ID,CALLER会被替换为真实的调用方的值
     */
    RULE_ID__CALLER("规则ID__调用方"),
    /**
     * 这个模版值,RULE_ID会被替换为规则ID,TARGET会被替换为真实的被调用目标的值
     */
    RULE_ID__TARGET("规则ID__被调用目标"),
    ;

    private final String desc;

    LimitTempleteEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
