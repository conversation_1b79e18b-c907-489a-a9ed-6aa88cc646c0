package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.DocumentDOExample;
import com.alipay.codegencore.model.domain.DocumentDO;

import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface DocumentDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    long countByExample(DocumentDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    int deleteByExample(DocumentDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    @Delete({
        "delete from cg_document",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    @Insert({
        "insert into cg_document (gmt_create, gmt_modified, ",
        "uid, document_name, ",
        "document_size, document_status, ",
        "source, content_oss_url, ",
        "content_length, segment_oss_url, ",
        "summary, ext_info, ",
        "create_user_id, update_user_id, ",
        "zsearch_client)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{uid,jdbcType=VARCHAR}, #{documentName,jdbcType=VARCHAR}, ",
        "#{documentSize,jdbcType=BIGINT}, #{documentStatus,jdbcType=VARCHAR}, ",
        "#{source,jdbcType=VARCHAR}, #{contentOssUrl,jdbcType=VARCHAR}, ",
        "#{contentLength,jdbcType=BIGINT}, #{segmentOssUrl,jdbcType=VARCHAR}, ",
        "#{summary,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR}, ",
        "#{createUserId,jdbcType=BIGINT}, #{updateUserId,jdbcType=BIGINT}, ",
        "#{zsearchClient,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(DocumentDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    int insertSelective(DocumentDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    List<DocumentDO> selectByExample(DocumentDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, uid, document_name, document_size, document_status, ",
        "source, content_oss_url, content_length, segment_oss_url, summary, ext_info, ",
        "create_user_id, update_user_id, zsearch_client",
        "from cg_document",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("com.alipay.codegencore.dal.mapper.DocumentDOMapper.BaseResultMap")
    DocumentDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    int updateByExampleSelective(@Param("record") DocumentDO record, @Param("example") DocumentDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    int updateByExample(@Param("record") DocumentDO record, @Param("example") DocumentDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    int updateByPrimaryKeySelective(DocumentDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_document
     *
     * @mbg.generated Tue Oct 15 14:27:02 CST 2024
     */
    @Update({
        "update cg_document",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "uid = #{uid,jdbcType=VARCHAR},",
          "document_name = #{documentName,jdbcType=VARCHAR},",
          "document_size = #{documentSize,jdbcType=BIGINT},",
          "document_status = #{documentStatus,jdbcType=VARCHAR},",
          "source = #{source,jdbcType=VARCHAR},",
          "content_oss_url = #{contentOssUrl,jdbcType=VARCHAR},",
          "content_length = #{contentLength,jdbcType=BIGINT},",
          "segment_oss_url = #{segmentOssUrl,jdbcType=VARCHAR},",
          "summary = #{summary,jdbcType=VARCHAR},",
          "ext_info = #{extInfo,jdbcType=VARCHAR},",
          "create_user_id = #{createUserId,jdbcType=BIGINT},",
          "update_user_id = #{updateUserId,jdbcType=BIGINT},",
          "zsearch_client = #{zsearchClient,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(DocumentDO record);
}