/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.links;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.copilot.CodeReference;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.CodeReferenceTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserTypeEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.links.*;
import com.alipay.codegencore.model.request.PRReportRequest;
import com.alipay.codegencore.model.request.copilot.CopilotRecommendMessageRequest;
import com.alipay.codegencore.model.response.PRReportItem;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.LinksApiService;
import com.alipay.codegencore.utils.CommonTools;
import com.alipay.codegencore.utils.RockCodeUtil;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.http.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 站点助手配置相关代码
 *
 * <AUTHOR>
 * @version CopilotConfigApi.java, v 0.1 2023年11月30日 上午10:46 wb-tzg858080
 */
@RestController
@CodeTalkWebApi
@Slf4j
@RequestMapping("/cors")
public class CopilotApi{

    @Resource
    private LinksApiService linksApiService;

    @Resource
    private SceneService sceneService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private ConfigService configService;
    private String rockcodeSecret;

    @PostConstruct
    public void init(){
        rockcodeSecret = configService.getConfigByKey(AppConstants.CONFIG_KEY_ROCKCODE_API_KEY, true);
    }

    /**
     * 站点助手配置
     *
     * @param configMap 站点助手配置
     * @return 添加租户
     */
    @PostMapping("/config/copilotConfig")
    public LinksResult<String> updateCopilotConfig(@RequestBody Map<String, CopilotConfig> configMap) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        // 管理类接口添加权限校验
        if(currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        if(configMap != null){
            linksApiService.updateCopilotConfig(configMap);
        }
        return LinksResult.success();
    }

    /**
     * 获取命令词
     *
     * @param copilotId 助手id
     * @return 命令词列表
     */
    @GetMapping("/copilot/{copilotId}/commands")
    public LinksResult<List<CopilotCommand>> getCommands(@PathVariable String copilotId) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        CopilotConfig config = linksApiService.getCopilotConfig(copilotId);
        return LinksResult.success(config != null ? config.getCommands() : new ArrayList<>());
    }

    /**
     * 获取欢迎消息
     *
     * @param copilotId 助手id
     * @return 命令词列表
     */
    @RequestMapping(value = "/copilot/{copilotId}/welcomeMessage", method = {RequestMethod.GET, RequestMethod.POST})
    public LinksResult<CopilotWelcomeMessage> welcomeMessage(@PathVariable String copilotId,
                                                             @RequestBody(required = false) CopilotRecommendMessageRequest request) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        CopilotConfig config = linksApiService.getCopilotConfig(copilotId);
        SceneDO scene = sceneService.getSceneById(Long.valueOf(config.getSceneId()));
        if (scene == null) {
            return LinksResult.success(null);
        }

        CopilotWelcomeMessage welcomeMessage = new CopilotWelcomeMessage();
        welcomeMessage.setPrefix(String.format("Hi，我是 %s，请向我提问吧", StringUtils.isNotBlank(scene.getName())?scene.getName():"站点助手"));
        welcomeMessage.setSuffix(scene.getDescription());
        List<CopilotButton> copilotButtons = JSONArray.parseArray(scene.getQueryTemplateListJson(), CopilotButton.class);
        // 针对antcode推荐问题做特殊处理
        if (scene.getId() == AppConstants.ANTCODE_COPILOT_SCENE_ID && request!=null){
            List<CopilotButton> recommendQuestions = linksApiService.getWelcomeMessageByAntcodePageType(request);
            if (CollectionUtils.isNotEmpty(recommendQuestions)){
                copilotButtons = recommendQuestions;
            }
        }
        welcomeMessage.setButtons(copilotButtons);
        return LinksResult.success(welcomeMessage);
    }

    /**
     * 全局反馈
     *
     * @param copilotId 会话id
     * @return 是否成功
     */
    @PostMapping("/copilot/{copilotId}/feedback")
    public LinksResult feedback(@PathVariable String copilotId, @RequestBody Map<String,String> paramMap) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        String content = paramMap.get("content");
        linksApiService.feedBack(copilotId,content);
        return LinksResult.success();
    }


    /**
     * 搜索代码参考
     *
     * @return 代码参考列表
     */
    @GetMapping("/copilot/searchCodeReference")
    public LinksResult<List<CodeReference>> searchCodeReference(@RequestParam("repoPath")String repoPath,
                                                                @RequestParam("branch")String branch,
                                                                @RequestParam("query")String query,
                                                                @RequestParam(value = "limit", defaultValue = "20") int limit) {
        String caseInsensitiveRegex = CommonTools.toCaseInsensitiveRegex(query);
        List<String> hitFiles = AntCodeClient.fileSearch(repoPath, branch, caseInsensitiveRegex, limit);
        if (CollectionUtils.isEmpty(hitFiles)) {
            return LinksResult.success();
        }
        List<CodeReference> codeReferences = new ArrayList<>();

        for (String hitFile : hitFiles) {
            CodeReference codeReference = new CodeReference();
            codeReference.setType(CodeReferenceTypeEnum.FILE);
            codeReference.setName(new File(hitFile).getName());
            codeReference.setUrl(String.format("%s/blob/%s/%s", repoPath, branch, hitFile));
            codeReferences.add(codeReference);
        }
        return LinksResult.success(codeReferences);
    }

    /**
     * 获取PR分析报告
     *
     * @param request PR分析请求
     * @return PR分析报告
     */
    @PostMapping("/copilot/prReport")
    public LinksResult<List<PRReportItem>> getPRReport(@RequestBody PRReportRequest request) {
        // 参数校验
        if (StringUtils.isBlank(request.getPrUrl())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "PR URL不能为空");
        }

        try {
            // 生成时间戳和签名
            long timestamp = System.currentTimeMillis();
            String signature = RockCodeUtil.generateSignature(AppConstants.ROCKCODE_APPID, timestamp, rockcodeSecret);

            // 构建请求头
            Map<String, String> headers = RockCodeUtil.buildRequestHeaders(AppConstants.ROCKCODE_APPID, signature, timestamp);

            String rockCodeUrl = RockCodeUtil.getRockCodeHost() + "/openapi/pr/report";
            // 发送请求到RockCode
            String response = HttpClient.post(rockCodeUrl)
                    .headers(headers)
                    .content(JSON.toJSONString(request))
                    .syncExecute(10000);


            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(response);
            if (!jsonResponse.getBooleanValue("success")) {
                String message = jsonResponse.getString("message");
                log.error("获取PR分析报告失败: {}", response);
                throw new BizException(ResponseEnum.ANSWER_PR_REPORT_ERROR, message);
            }

            List<PRReportItem> reportItems = JSON.parseArray(jsonResponse.getString("data"), PRReportItem.class);
            return LinksResult.success(reportItems);
        } catch (Exception e) {
            log.error("获取PR分析报告失败", e);
            throw new BizException(ResponseEnum.ANSWER_PR_REPORT_ERROR, e.getMessage());
        }
    }
}
