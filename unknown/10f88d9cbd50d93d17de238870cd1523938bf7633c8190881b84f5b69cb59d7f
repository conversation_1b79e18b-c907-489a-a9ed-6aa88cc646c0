/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version $Id: EntityReferenceType.java, v 0.1 2020-05-11 18:27 zhi.huangcz Exp $$
 */
public enum EntityReferenceType {
    /**
     *
     */
    SNIPPET("SNIPPET"),
    LARK("LARK"),
    KNOWLEDGE("KNOWLEDGE"),
    LEGO("LEGO"),
    FILE("FILE"),
    CONVERSATION_FORM("CONVERSATION_FORM"),
    UPGRADE_ACTIVE("UPGRADE_ACTIVE"),
    CONVERSATION_FORM_SIMPLE("CONVERSATION_FORM_SIMPLE"),
    CONVERSATION_DISPATCH_FORM("CONVERSATION_DISPATCH_FORM"),
    MARKDOWN("MARKDOWN"),
    ACTION_CARD("ACTION_CARD"),
    REPLAYS("REPLAYS"),
    REPLAYS_CREATE("REPLAYS_CREATE"),
    REPLAYS_RESULT("REPLAYS_RESULT"),
    INDUSTRY_TOOL("INDUSTRY_TOOL"),
    ATECHS_TOOL("ATECHS_TOOL"),
    CARD("CARD"),
    ANSWERS("ANSWERS"),
    HTML("HTML"),
    /** 诊断任务新卡片 */
    REPLAYS_ALL_IN_ONE("REPLAYS_ALL_IN_ONE"),
    /**
     * 小二发送一条诊断结果卡片（新版）
     */
    REPLAYS_ONE_DETAIL_INFO("REPLAYS_ONE_DETAIL_INFO"),

    /** 多诊断任务新表单卡片 */
    MULTI_REPLAYS("MULTI_REPLAYS"),

    /** 多诊断任务新结果卡片 */
    MULTI_REPLAYS_RESULT("MULTI_REPLAYS_RESULT"),
    /**
     * GPT消息
     */
    GPT_MESSAGE("GPT_MESSAGE"),
    /**
     * 转人工
     */
    TO_SUPPORT("TO_SUPPORT"),

    /**
     * GPT task
     */
    GPT_TASK("GPT_TASK"),
    /**
     * 转人工表单卡片
     */
    TO_SUPPORT_CONVERSATION_FORM("TO_SUPPORT_CONVERSATION_FORM")
    ;
    private final String value;

    public String getValue() {
        return value;
    }

    EntityReferenceType(String value) {
        this.value = value;
    }
}