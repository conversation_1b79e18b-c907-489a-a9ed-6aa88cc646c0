package com.alipay.codegencore.model.exception;

import com.alipay.codegencore.model.enums.ResponseEnum;

/**
 * 业务异常
 *
 * <AUTHOR>
 * 创建时间 2021-12-30
 */
public class BizException extends RuntimeException {

    private ResponseEnum errorType;

    /**
     * 直接返回完整的errorMsg
     * @param errorType 错误码
     * @param errorMsg 错误原因
     */
    public BizException(ResponseEnum errorType, String errorMsg) {
        super(errorMsg);
        this.errorType = errorType;
    }

    /**
     * 直接返回完整的errorMsg
     *
     * @param errorType 错误码
     */
    public BizException(ResponseEnum errorType) {
        super(errorType.getErrorMsg());
        this.errorType = errorType;
    }

    /**
     * 业务异常
     *
     * @param errorType 错误类型
     * @param e         异常
     */
    public BizException(ResponseEnum errorType, Throwable e) {
        super(e.getMessage(), e);
        this.errorType = errorType;
    }

    public ResponseEnum getErrorType() {
        return errorType;
    }

    public void setErrorType(ResponseEnum errorType) {
        this.errorType = errorType;
    }
}
