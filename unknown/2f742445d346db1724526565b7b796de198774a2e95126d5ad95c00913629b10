/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @version MessageForm.java, v 0.1 2023年11月08日 下午4:27 wb-tzg858080
 */
public class MessageForm extends ToString {

    /**
     * 内容
     */
    private String query;

    /**
     * 指令
     */
    private String command ;

    private JSONObject extraInfo;

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public JSONObject getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(JSONObject extraInfo) {
        this.extraInfo = extraInfo;
    }
}
