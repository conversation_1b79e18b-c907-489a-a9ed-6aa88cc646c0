/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version ChatGptModelHandler.java, v 0.1 2023年04月21日 16:32 xiaobin
 */
public class ChatGptModelHandler extends AbstractAlgLanguageHandler {

    private LanguageModelService chatGptModelService;

    /**
     * 抽象handler构造函数
     *
     */
    public ChatGptModelHandler() {
        this.chatGptModelService = SpringUtil.getBean("chatGPTLanguageModelService");
    }

    @Override
    public ChatMessage chat(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        return chatGptModelService.chat(gptAlgModelServiceRequest);
    }

    @Override
    public Flux<String> chatOnStream(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        chatGptModelService.streamChatForServlet(gptAlgModelServiceRequest);
        return null;
    }

}