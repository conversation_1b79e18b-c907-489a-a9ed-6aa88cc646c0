package com.alipay.codegencore.model.model.analysis;

import com.alipay.codegencore.model.enums.ClassTypeEnum;

import java.util.List;

/**
 * 代码分析结果
 *
 * <AUTHOR>
 * 创建时间 2022-08-08
 */
public  class AbstractCodeAnalysisResult {
    /**
     * 包名
     */
    protected String packageName;
    /**
     * 类名
     */
    protected String className;

    /**
     * class类型
     */
    private ClassTypeEnum classType;
    /**
     * 继承的class
     */
    private String extendClassName;
    /**
     * 实现的接口列表
     */
    private String[] implementClassNameArr;
    /**
     * 内部类列表
     */
    private List<AbstractCodeAnalysisResult> innerClassList;
    /**
     * 字段列表
     */
    private List<FieldBodyModel> fieldList;
    /**
     * 方法列表
     */
    private List<MethodBodyModel> methodList;

    public List<AbstractCodeAnalysisResult> getInnerClassList() {
        return innerClassList;
    }

    public void setInnerClassList(List<AbstractCodeAnalysisResult> innerClassList) {
        this.innerClassList = innerClassList;
    }

    public List<FieldBodyModel> getFieldList() {
        return fieldList;
    }

    public void setFieldList(List<FieldBodyModel> fieldList) {
        this.fieldList = fieldList;
    }

    public List<MethodBodyModel> getMethodList() {
        return methodList;
    }

    public void setMethodList(List<MethodBodyModel> methodList) {
        this.methodList = methodList;
    }

    public String getExtendClassName() {
        return extendClassName;
    }

    public void setExtendClassName(String extendClassName) {
        this.extendClassName = extendClassName;
    }

    public String[] getImplementClassNameArr() {
        return implementClassNameArr;
    }

    public void setImplementClassNameArr(String[] implementClassNameArr) {
        this.implementClassNameArr = implementClassNameArr;
    }

    public ClassTypeEnum getClassType() {
        return classType;
    }

    public void setClassType(ClassTypeEnum classType) {
        this.classType = classType;
    }



    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }
}
