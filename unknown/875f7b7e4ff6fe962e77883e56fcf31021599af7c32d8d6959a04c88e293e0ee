package com.alipay.codegencore.model.enums;

/**
 * 业务场景枚举
 *
 * <AUTHOR>
 * 创建时间 2022-04-13
 */
public enum BizSceneEnum {
    /**
     * 业务服务接入
     */
    BIZ_SERVICE(1),
    /**
     * 中间件相关
     */
    MIDDLEWARE(2),
    /**
     * 工具方法
     */
    TOOLS(3),
    /**
     * 常见业务逻辑
     */
    BIZ_LOGIC(4),
    /**
     * 代码片段
     */
    CODE_FRAGMENT(5),
    /**
     * 未知
     */
    UNKNOWN(99);

    BizSceneEnum(int code) {
        this.code = code;
    }

    private int code;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static BizSceneEnum getBizSceneByCode(int code) {
        for (BizSceneEnum bizSceneEnum : BizSceneEnum.values()) {
            if (bizSceneEnum.getCode() == code) {
                return bizSceneEnum;
            }
        }
        return UNKNOWN;
    }
}
