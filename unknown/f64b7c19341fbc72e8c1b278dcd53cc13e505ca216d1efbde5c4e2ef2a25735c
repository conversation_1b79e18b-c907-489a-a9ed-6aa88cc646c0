package com.alipay.codegencore.model.model;

import java.util.List;
import java.util.Map;

/**
 * 代码补全推荐结果
 *
 * <AUTHOR>
 * 创建时间 2022-01-17
 */
public class CompletionsCodeModel {
    /**
     * 会话id
     */
    private Long sessionId;
    /**
     * 推荐结果
     */
    private List<CodeModel> codeModelList;
    /**
     * 算法策略返回结果数量统计
     */
    private Map<String,Integer> algStrategyCount;
    /**
     * 算法耗时
     */
    private Long processingTime;

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public Map<String, Integer> getAlgStrategyCount() {
        return algStrategyCount;
    }

    public void setAlgStrategyCount(Map<String, Integer> algStrategyCount) {
        this.algStrategyCount = algStrategyCount;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public List<CodeModel> getCodeModelList() {
        return codeModelList;
    }

    public void setCodeModelList(List<CodeModel> codeModelList) {
        this.codeModelList = codeModelList;
    }
}
