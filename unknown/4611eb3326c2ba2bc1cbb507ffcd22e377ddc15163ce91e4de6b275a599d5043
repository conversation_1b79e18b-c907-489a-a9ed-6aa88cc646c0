package com.alipay.codegencore.service.impl.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.antq.common.utils.StringUtils;
import com.alipay.arks.client.enums.RouterPriority;
import com.alipay.arks.client.enums.RpcMode;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RateLimitTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.ChatStreamBuffer;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.model.codegpt.StreamResponseModel;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatFunction;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.request.CodegptBatchRequestBean;
import com.alipay.codegencore.model.request.CodegptRequestBean;
import com.alipay.codegencore.model.request.maya.MayaBLStreamRequestBean;
import com.alipay.codegencore.model.request.maya.MayaStreamRequestBean;
import com.alipay.codegencore.model.response.ChatStreamPartResponse;
import com.alipay.codegencore.model.util.AlgoBackendUtil;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.MayaService;
import com.alipay.codegencore.service.common.limiter.qpx.QpxLimiterFactory;
import com.alipay.codegencore.service.gptcache.bean.GptCacheResponse;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ChatUtils;
import com.alipay.maya.MayaClient;
import com.alipay.maya.config.MayaClientConfig;
import com.alipay.maya.model.MayaRequest;
import com.alipay.maya.model.MayaResponse;
import com.alipay.maya.model.MayaStreamObserver;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

import static com.alipay.codegencore.model.enums.ResponseEnum.STREAM_THROW_EXCEPTION;

/**
 * AntGLM模型maya平台接入实现
 */
@Service("mayaStreamModelService")
@Slf4j
public class MayaStreamModelServiceImpl implements LanguageModelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MayaStreamModelServiceImpl.class);

    @AppConfig("spring.application.name")
    private String appName;

    @Resource
    private MayaService mayaService;
    @Resource
    private CheckService checkService;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource(name = "appThreadPool")
    private ExecutorService appThreadPool;
    @Resource
    private AlgoModelUtilService algoModelUtilService;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource
    private AlgoModelHealthUtilService algoModelHealthUtilService;

    @Resource
    private StreamDataQueueUtilService streamDataQueueUtilService;


    @Override
    public boolean isServiceOk() {
        return true;
    }

    @Override
    public ChatMessage chat(GptAlgModelServiceRequest params) {
        // 获取请求参数
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        String requestId = params.getRequestId();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        if (chatRequestExtData != null) {
            chatRequestExtData.setAlgoBackendDO(algoBackendDO);
        }
        // 限流检查
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        // 只保留最后一个问题
        String query;
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        if (CollectionUtils.isNotEmpty(chatCompletionRequest.getMessages())) {
            query = messages.get(messages.size() - 1).getContent();
        } else {
            query = chatCompletionRequest.getPrompt();
        }
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(requestId, query, chatRequestExtData, false);

        if (!requestCheckResultModel.isAllCheckRet()) {
            throw new BizException(ResponseEnum.CHECK_FAILED);
        }

        //构建请求参数
        //输入输出数据格式: https://yuque.antfin.com/iikq97/lozx5s/nt5ow5cttmcbgyvu
        String mayaDataVersion = AlgoBackendUtil.exactMayaDataVersion(algoBackendDO);
        String requestBeanJson = null;
        if ("2".equals(mayaDataVersion)) {
            requestBeanJson = getRequestBeanJson(algoBackendDO, query, false, null);
        } else if ("3".equals(mayaDataVersion)) {
            // 适配 codegpt 格式，用于支持 codegpt 输入输出格式，但是没有写 tbase 的模型
            // 目前使用的模型有 qwen14B
            requestBeanJson = getCodeGPTRequestBeanJson(algoBackendDO,params);
            LOGGER.info("mayaDataVersion 3, requestBeanJson:{}", requestBeanJson);
        } else if ("4".equals(mayaDataVersion)) {
            // 适配 modelops 中的qwen模型
            CodegptRequestBean request = algoModelUtilService.beforeModelRequest(chatCompletionRequest, algoBackendDO, params.getUniqueAnswerId(), false);
            JSONObject prompt = new JSONObject();
            prompt.put("prompt", request.getPrompt());
            requestBeanJson = getRequestBeanJson(algoBackendDO, prompt, false, chatCompletionRequest.getFunctions());
        } else if ("5".equals(mayaDataVersion)) {
            requestBeanJson = getBLRequestBeanJson(algoBackendDO, chatCompletionRequest, false);
        }   else {
            algoModelHealthUtilService.costHealth(params.getModelEnv(), algoBackendDO.getModel(), ResponseEnum.MODEL_CONFIG_ERROR.name());
            throw new BizException(ResponseEnum.MODEL_CONFIG_ERROR, "invalid mayaDataVersion");
        }

        //模型调用
        String orgAnswer;
        try {
            orgAnswer = mayaService.getInferenceResult(requestBeanJson,
                    AlgoBackendUtil.exactSceneNameConfig(algoBackendDO),
                    AlgoBackendUtil.exactChainNameConfig(algoBackendDO),
                    AlgoBackendUtil.exactRequestTimeOutConfig(algoBackendDO),
                    AlgoBackendUtil.exactRequestMayaDataKeyConfig(algoBackendDO),
                    AlgoBackendUtil.exactResponseMayaDataKeyConfig(algoBackendDO),false, algoBackendDO, params.getModelEnv());
        }catch (BizException e){
            // 同步请求过程， 统一异常处理健康度
            algoModelHealthUtilService.costHealth(params.getModelEnv(), algoBackendDO.getModel(), ResponseEnum.MAYA_CLIENT_EXCEPTION.name());
            throw new BizException(e.getErrorType(),e);
        }

        ChatMessage answer = null;
        if ("2".equals(mayaDataVersion)) {
            Optional<JSONObject> responseJson = Optional.of(JSON.parseObject(orgAnswer));
            String response = responseJson.map(retJson -> retJson.getJSONArray("generated_code").getJSONArray(0).getString(0)).orElse(null);
            answer = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), response);
        } else if ("3".equals(mayaDataVersion)) {
            String response = algoModelUtilService.afterModelResponse(orgAnswer, algoBackendDO);
            answer = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), response);
        } else if("4".equals(mayaDataVersion)){
            Optional<JSONObject> responseJson = Optional.of(JSON.parseObject(orgAnswer));
            answer = responseJson.map(retJson -> retJson.getJSONObject("generated_code").toJavaObject(ChatMessage.class)).orElse(null);
            // 将空字符串转换为null，不然后续判断是否是function call会出错
            answer.convertEmptyPropertyStringToNull();
        } else if ("5".equals(mayaDataVersion)) {
            answer = getBLResponseMessage(orgAnswer, false);
        } else {
            throw new BizException(ResponseEnum.MODEL_CONFIG_ERROR, "invalid mayaDataVersion");
        }

        // 同步请求过程， 统一reset逻辑
        algoModelHealthUtilService.resetHealth(params.getModelEnv(), algoBackendDO.getModel(), false);
        if(answer!=null && StringUtils.isNotBlank(answer.getContent())){
            // 审核ai的输出
            CheckResultModel resultCheckResultModel = checkService.getAnswerCheckResultLongContent(requestId, messages, answer.getContent(), chatRequestExtData, requestId,false);
            if (!resultCheckResultModel.isAllCheckRet()) {
                throw new BizException(ResponseEnum.CHECK_FAILED);
            }
        }

        return answer;
    }
   
    @Override
    public void streamChatForServlet(GptAlgModelServiceRequest params) {
        // 获取请求参数
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        String requestId = params.getRequestId();
        Consumer<ChatStreamPartResponse> chatStreamPartResponseConsumer = params.getChatStreamPartResponseHandler();
        Consumer<StreamResponseModel> resultHandler = params.getResultHandler();
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        ChatRequestExtData chatRequestExtData = chatCompletionRequest.getChatRequestExtData();
        if (chatRequestExtData != null) {
            chatRequestExtData.setAlgoBackendDO(algoBackendDO);
        }
        // 限流检查
        if (!QpxLimiterFactory.getInstance().tryAcquire(RateLimitTypeEnum.ALGO_MODEL, algoBackendDO.getModel(), algoBackendDO.getModel())) {
            throw new BizException(ResponseEnum.CURRENT_ALGO_LIMITING_ANOMALY);
        }
        // 必须是流式请求
        if (!chatCompletionRequest.getStream()) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "stream must be true");
        }
        // 只保留最后一个问题
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        String query;
        if (CollectionUtils.isNotEmpty(messages)) {
            query = messages.get(messages.size() - 1).getContent();
        } else {
            query = chatCompletionRequest.getPrompt();
        }
        List<ChatMessage> lastQuery = new ArrayList<>();
        lastQuery.add(new ChatMessage(ChatRoleEnum.USER.getName(),query));
        CheckResultModel requestCheckResultModel = checkService.getQuestionCheckResult(requestId, query, chatRequestExtData, false);
        if (!requestCheckResultModel.isAllCheckRet()) {
            // 结果不通过, 停止流响应
            algoModelUtilService.processWhenQuestionFailCheck(requestCheckResultModel, chatStreamPartResponseConsumer, resultHandler);
            return;
        }
        // 开启gptCache并从gptCache写入TBase数据成功 不发送请求
        GptCacheResponse gptCacheResponse  = null;
        if(algoBackendDO.getEnableGptCache()){
            gptCacheResponse =  algoModelUtilService.getGPTCache(algoBackendDO, params);
        }
        if(gptCacheResponse !=  null  && StringUtils.isNotBlank(gptCacheResponse.getAnswer())){
            GptCacheResponse finalGptCacheResponse = gptCacheResponse;
            if (codeGPTDrmConfig.isModelEnableQueue()){
                appThreadPool.execute(() -> algoModelUtilService.pushToQueue(finalGptCacheResponse, params));
            }else {
                appThreadPool.execute(() -> algoModelUtilService.pushToTBase(finalGptCacheResponse, params));
            }

        }else{
            appThreadPool.execute(() -> sendRequest(AlgoModelUtilService.copyParamWithoutServletResponse(params)));
        }
        algoModelUtilService.getChatDataFromTBase(params, lastQuery, requestCheckResultModel,algoBackendDO.getEnableGptCache(),new ChatStreamBuffer(),codeGPTDrmConfig.isModelEnableQueue());
    }

    private void sendRequest(GptAlgModelServiceRequest params) {
        AlgoBackendDO algoBackendDO = params.getAlgoBackendDO();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        String uniqueAnswerId = params.getUniqueAnswerId();
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        String query;
        if (CollectionUtils.isNotEmpty(messages)) {
            query = messages.get(messages.size() - 1).getContent();
        } else {
            query = chatCompletionRequest.getPrompt();
        }
        //构建请求参数
        String mayaDataVersion = AlgoBackendUtil.exactMayaDataVersion(algoBackendDO);
        String requestBeanJson;
        if ("2".equals(mayaDataVersion)) {
            requestBeanJson = getRequestBeanJson(algoBackendDO, query, true, null);
        } else if ("3".equals(mayaDataVersion)) {
            requestBeanJson = getCodeGPTRequestBeanJson(algoBackendDO,params);
        } else if ("4".equals(mayaDataVersion)) {
            CodegptRequestBean request = algoModelUtilService.beforeModelRequest(chatCompletionRequest, algoBackendDO, params.getUniqueAnswerId(), false);
            JSONObject prompt = new JSONObject();
            prompt.put("prompt", request.getPrompt());
            requestBeanJson = getRequestBeanJson(algoBackendDO, prompt, true, chatCompletionRequest.getFunctions());
        } else if ("5".equals(mayaDataVersion)) {
            requestBeanJson = getBLRequestBeanJson(algoBackendDO, chatCompletionRequest, true);
        }  else {
            throw new BizException(ResponseEnum.MODEL_CONFIG_ERROR, "invalid mayaDataVersion");
        }

        MayaClientConfig config = new MayaClientConfig();
        // 可选,当前app名,和部署单元名一致,不填会从pod的环境变量中获取
        config.setAppName(appName);
        MayaClient mayaClient = MayaClient.getInstance(config);
        MayaRequest request = mayaService.getMayaRequest(
                AlgoBackendUtil.exactRequestMayaDataKeyConfig(algoBackendDO),
                requestBeanJson,
                AlgoBackendUtil.exactSceneNameConfig(algoBackendDO),
                AlgoBackendUtil.exactChainNameConfig(algoBackendDO),
                AlgoBackendUtil.exactRequestTimeOutConfig(algoBackendDO),
                AlgoBackendUtil.exactTokenConfig(algoBackendDO));
        // 流式调用必须使用grpc协议
        request.setRpcMode(RpcMode.GRPC);
        List<String> serverList = mayaService.getServerListByEnv(algoBackendDO, params.getModelEnv(), true);
        if(serverList != null && !serverList.isEmpty()) {
            if (LOGGER.isInfoEnabled()) {
                LOGGER.info("get serverList : {}, for {}", serverList, algoBackendDO.getModel());
            }
            request.setServerList(serverList);
            request.setRouterPriority(RouterPriority.CONFIG);
        }


        LOGGER.info("request maya param:{}", JSON.toJSONString(request));
        // 流式响应缓存
        ChatStreamBuffer chatStreamBuffer = new ChatStreamBuffer();
        final boolean[] stopOnMessage = {false};
        String streamInputId = String.format("%s%s", AppConstants.STREAM_INPUT_PREFIX, params.getUniqueAnswerId());

        try {
            mayaClient.modelStreamInfer(request, new MayaStreamObserver<>() {
                @Override
                public void onNext(MayaResponse response) {
                    if(algoModelUtilService.needCloseInputStream(streamInputId)) {
                        if(LOGGER.isInfoEnabled()) {
                            LOGGER.info("AIGC input get close info : {}", streamInputId);
                        }
                        return;
                    }

                    if (response.getErrorCode() != 0) {
                        LOGGER.warn("request maya process failed,response:{}", JSON.toJSONString(response));
                        return;
                    }
                    LOGGER.info("request maya process success,response:{}", JSON.toJSONString(response));
                    try {
                        if (stopOnMessage[0]) {
                            return;
                        }
                        Map<String, String> responseMap = response.getItems().get(0).getAttributes();
                        String orgAnswer = responseMap.get(AlgoBackendUtil.exactResponseMayaDataKeyConfig(algoBackendDO));

                        ChatMessage delta = null;
                        if ("2".equals(mayaDataVersion)) {
                            Optional<JSONObject> responseJson = Optional.of(JSON.parseObject(orgAnswer));
                            String content = responseJson.map(retJson -> retJson.getJSONArray("generated_code").getJSONArray(0).getString(0)).orElse(null);
                            delta = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), content);
                        } else if ("3".equals(mayaDataVersion)) {
                            String content = algoModelUtilService.afterModelResponse(orgAnswer,algoBackendDO);
                            delta = new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), content);
                        } else if ("4".equals(mayaDataVersion)) {
                            Optional<JSONObject> responseJson = Optional.of(JSON.parseObject(orgAnswer));
                            delta = responseJson.map(retJson -> retJson.toJavaObject(ChatMessage.class)).orElse(null);
                            // 将空字符串转换为null，不然后续判断是否是function call会出错
                            if(delta != null){
                                delta.convertEmptyPropertyStringToNull();
                            }
                        } else if ("5".equals(mayaDataVersion)) {
                            delta = getBLResponseMessage(orgAnswer, true);
                        } else {
                            throw new BizException(ResponseEnum.MODEL_CONFIG_ERROR, "invalid mayaDataVersion");
                        }
                        if (delta == null){
                            throw new BizException(ResponseEnum.AI_CALL_ERROR, "delta message is null");
                        }
                        if (codeGPTDrmConfig.isModelEnableQueue()) {
                            algoModelUtilService.memoryQueueStreamData(delta, null, uniqueAnswerId);
                        }
                        else {
                            ChatUtils.handleEveryStreamData(delta, null, noneSerializationCacheManager, uniqueAnswerId);
                        }
                    } catch (Exception e) {
                        LOGGER.error("处理模型流式响应异常", e);
                        if (codeGPTDrmConfig.isModelEnableQueue()) {
                            algoModelUtilService.memoryQueueStreamError(e.getMessage(), uniqueAnswerId, STREAM_THROW_EXCEPTION);
                        }
                        else {
                            algoModelUtilService.handleEveryStreamError(e.getMessage(), noneSerializationCacheManager, uniqueAnswerId,
                                    STREAM_THROW_EXCEPTION);
                        }
                    }
                }

                @Override
                public void onError(Throwable t) {
                    LOGGER.error("request maya onError failed", t);
                    if (codeGPTDrmConfig.isModelEnableQueue()) {
                        algoModelUtilService.memoryQueueStreamError("request maya onError failed", uniqueAnswerId, STREAM_THROW_EXCEPTION);
                    }
                    else {
                        algoModelUtilService.handleEveryStreamError("request maya onError failed", noneSerializationCacheManager,
                                uniqueAnswerId, STREAM_THROW_EXCEPTION);
                    }
                }

                @Override
                public void onCompleted() {
                    LOGGER.info("request maya response content:{}", chatStreamBuffer.getContent());
                    if (codeGPTDrmConfig.isModelEnableQueue()) {
                        algoModelUtilService.memoryQueueStreamData(null, "stop", uniqueAnswerId);
                    }
                    else {
                        ChatUtils.handleEveryStreamData(null, "stop"
                                , noneSerializationCacheManager, uniqueAnswerId);
                    }
                }
            });
        } catch (Exception e) {
            LOGGER.error("请求模型异常", e);
            if (codeGPTDrmConfig.isModelEnableQueue()) {
                algoModelUtilService.memoryQueueStreamError(e.getMessage(), uniqueAnswerId, STREAM_THROW_EXCEPTION);
            }
            else {
                algoModelUtilService.handleEveryStreamError(e.getMessage(), noneSerializationCacheManager, uniqueAnswerId,
                        STREAM_THROW_EXCEPTION);
            }
        }
    }


    private ChatMessage getBLResponseMessage(String orgAnswer, boolean stream) {
        if (StringUtils.isBlank(orgAnswer)) {
            LOGGER.error("orgAnswer is null");
            return new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), null);
        }

        String answer = null;
        try {
            JSONObject answerObject = JSON.parseObject(orgAnswer);
            JSONArray choices = answerObject.getJSONArray("choices");
            if (CollectionUtils.isEmpty(choices)) {
                LOGGER.error("choices is null");
                return new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), null);
            }

            if (stream) {
                JSONObject message = choices.getJSONObject(0).getJSONObject("delta");
                answer = Optional.ofNullable(message).map(m -> m.getString("content")).orElse(null);
            } else {
                JSONObject message = choices.getJSONObject(0).getJSONObject("message");
                answer = Optional.ofNullable(message).map(m -> m.getString("content")).orElse(null);
            }
        } catch (Exception e) {
            LOGGER.error("response parsing failure", e);
        }
        return new ChatMessage(ChatRoleEnum.ASSISTANT.getName(), answer);
    }

    private String getBLRequestBeanJson(AlgoBackendDO algoBackendDO, ChatCompletionRequest chatCompletionRequest, Boolean stream) {
        MayaBLStreamRequestBean mayaBLStreamRequestBean = new MayaBLStreamRequestBean();
        mayaBLStreamRequestBean.setModel(AlgoBackendUtil.getImplConfigByKey(algoBackendDO, "model", String.class));
        mayaBLStreamRequestBean.setStream(stream);
        mayaBLStreamRequestBean.setEntryPoint(AlgoBackendUtil.getImplConfigByKey(algoBackendDO, "entryPoint", String.class));
        mayaBLStreamRequestBean.setTemperature(AlgoBackendUtil.exactTemperatureConfig(algoBackendDO));
        mayaBLStreamRequestBean.setTopK(AlgoBackendUtil.exactTopKConfig(algoBackendDO));
        mayaBLStreamRequestBean.setTopP(AlgoBackendUtil.exactTopPConfig(algoBackendDO));
        mayaBLStreamRequestBean.setMaxTokens(algoBackendDO.getMaxToken());
        mayaBLStreamRequestBean.setRepetitionPenalty(AlgoBackendUtil.exactRepetitionPenaltyConfig(algoBackendDO));
        List<ChatMessage> messages = chatCompletionRequest.getMessages();
        if (CollectionUtils.isEmpty(messages)) {
            messages = new ArrayList<>();
            messages.add(new ChatMessage(ChatRoleEnum.USER.getName(), chatCompletionRequest.getPrompt()));
        }
        mayaBLStreamRequestBean.setMessages(messages);
        return JSON.toJSONString(mayaBLStreamRequestBean);
    }

    private String getRequestBeanJson(AlgoBackendDO algoBackendDO, Object prompt, boolean stream, List<ChatFunction> functions) {
        MayaStreamRequestBean mayaStreamRequestBean = new MayaStreamRequestBean();
        mayaStreamRequestBean.setDialogue(false);
        mayaStreamRequestBean.setDialogueRounds(1);
        mayaStreamRequestBean.setBeamWidth(AlgoBackendUtil.exactNumBeamsConfig(algoBackendDO));
        mayaStreamRequestBean.setOutSeqLength(AlgoBackendUtil.exactOutSeqLengthConfig(algoBackendDO));
        mayaStreamRequestBean.setTemperature(AlgoBackendUtil.exactTemperatureConfig(algoBackendDO));
        mayaStreamRequestBean.setTopK(AlgoBackendUtil.exactTopKConfig(algoBackendDO));
        mayaStreamRequestBean.setTopP(AlgoBackendUtil.exactTopPConfig(algoBackendDO));
        mayaStreamRequestBean.setRandomSeed(AlgoBackendUtil.exactRandomSeedConfig(algoBackendDO));
        mayaStreamRequestBean.setStopWords(AlgoBackendUtil.exactStopWordsConfig(algoBackendDO));
        mayaStreamRequestBean.setLastTokens(AlgoBackendUtil.exactLastTokensConfig(algoBackendDO));
        mayaStreamRequestBean.setRepetitionPenalty(AlgoBackendUtil.exactRepetitionPenaltyConfig(algoBackendDO));
        mayaStreamRequestBean.setStream(stream);
        mayaStreamRequestBean.setFunctions(functions);

        ArrayList<Object> prompts = new ArrayList<>();
        prompts.add(prompt);
        mayaStreamRequestBean.setPrompts(prompts);
        JSONObject ret = JSON.parseObject(JSON.toJSONString(mayaStreamRequestBean));
        Map<String, Object> extInfoMap = AlgoBackendUtil.exactExtInfoConfig(algoBackendDO);
        if (MapUtils.isNotEmpty(extInfoMap)) {
            ret.putAll(extInfoMap);
        }
        return ret.toString();
    }

    private String getCodeGPTRequestBeanJson(AlgoBackendDO algoBackendDO, GptAlgModelServiceRequest params) {
        CodegptBatchRequestBean codegptBatchRequestBean=new CodegptBatchRequestBean();
        codegptBatchRequestBean.setApiVersion("v2");
        codegptBatchRequestBean.setBeamWidth(AlgoBackendUtil.exactBeamWidthConfig(algoBackendDO));
        codegptBatchRequestBean.setOutSeqLength(AlgoBackendUtil.exactOutSeqLengthConfig(algoBackendDO));
        codegptBatchRequestBean.setStream(Optional.of(params.getChatCompletionRequest().getStream()).orElse(true));
//        codegptBatchRequestBean.setMaxLength(algoBackendDO.getMaxToken());
        List<CodegptRequestBean> requestList = new ArrayList<>();
        ChatCompletionRequest chatCompletionRequest = params.getChatCompletionRequest();
        CodegptRequestBean request = algoModelUtilService.beforeModelRequest(chatCompletionRequest, params.getAlgoBackendDO(), params.getUniqueAnswerId());
        requestList.add(request);
        codegptBatchRequestBean.setPrompts(requestList);
        return JSON.toJSONString(codegptBatchRequestBean);
    }
}
