package com.alipay.codegencore.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 会话相关工具类
 */
public class SessionUtils {

    /**
     * 更新会话绑定文件列表
     *
     * @param chatSessionDO 会话对象
     * @param newFileList   新的文件列表
     */
    public static void updateSessionBindingFile(ChatSessionDO chatSessionDO, JSONArray newFileList) {
        // 获取原始扩展信息
        String extInfo = chatSessionDO.getExtInfo();
        JSONObject extInfoJson = StringUtils.isBlank(extInfo) ? new JSONObject() : JSON.parseObject(extInfo);
        // 将新的文件列表放入JSONObject中
        extInfoJson.put("fileList", newFileList);
        // 更新会话扩展信息
        chatSessionDO.setExtInfo(extInfoJson.toJSONString());
    }

    /**
     * 添加会话绑定文件到文件列表中
     *
     * @param chatSessionDO 会话对象
     * @param fileOss       文件对象
     */
    public static void addSessionBindingFile(ChatSessionDO chatSessionDO, JSONObject fileOss) {
        // 获取原始扩展信息
        String extInfo = chatSessionDO.getExtInfo();
        JSONObject extInfoJson = StringUtils.isBlank(extInfo) ? new JSONObject() : JSON.parseObject(extInfo);
        JSONArray fileList = extInfoJson.containsKey("fileList") ? extInfoJson.getJSONArray("fileList") : new JSONArray();
        // 将新增的文件添加到文件列表中
        fileList.add(fileOss);
        extInfoJson.put("fileList", fileList);
        // 更新会话扩展信息
        chatSessionDO.setExtInfo(extInfoJson.toJSONString());
    }

    /**
     * 获取会话绑定文件数量
     *
     * @param chatSessionDO 会话对象
     * @return 绑定文件数量
     */
    public static int getSessionBindingFileSize(ChatSessionDO chatSessionDO) {
        // 获取原始扩展信息
        String extInfo = chatSessionDO.getExtInfo();
        if (StringUtils.isBlank(extInfo) ||
                !JSONObject.parseObject(extInfo).containsKey("fileList") ||
                JSONObject.parseObject(extInfo).getJSONArray("fileList").size() == 0) {
            return 0;
        }
        // 返回文件列表长度
        return JSONObject.parseObject(extInfo).getJSONArray("fileList").size();
    }

    /**
     * 获取会话扩展信息
     * @param chatSessionDO
     * @return
     */
    public static JSONObject getSessionExtInfo(ChatSessionDO chatSessionDO) {
        String extInfo = chatSessionDO != null ? chatSessionDO.getExtInfo() : null;
        JSONObject result = new JSONObject();
        if(StringUtils.isNotBlank(extInfo)){
            result = JSON.parseObject(extInfo);
        }
        return result;
    }

    /**
     * 向会话的extInfo字段添加or更新配置
     * @param chatSessionDO
     * @param key
     * @param value
     */
    public static void addSessionConfig(ChatSessionDO chatSessionDO, String key, Object value) {
        String extInfo = chatSessionDO.getExtInfo();
        JSONObject extInfoJson = StringUtils.isBlank(extInfo) ? new JSONObject() : JSON.parseObject(extInfo);
        extInfoJson.put(key, value);
        // 更新会话扩展信息
        chatSessionDO.setExtInfo(extInfoJson.toJSONString());
    }

    /**
     * 向会话的extInfo字段批量添加or更新配置
     * @param chatSessionDO
     * @param sessionExtInfo
     */
    public static void addSessionConfig(ChatSessionDO chatSessionDO, JSONObject sessionExtInfo) {
        String extInfo = chatSessionDO.getExtInfo();
        JSONObject extInfoJson = StringUtils.isBlank(extInfo) ? new JSONObject() : JSON.parseObject(extInfo);

        for (String key : sessionExtInfo.keySet()) {
            extInfoJson.put(key, sessionExtInfo.get(key));
        }

        // 更新会话扩展信息
        chatSessionDO.setExtInfo(extInfoJson.toJSONString());
    }

    /**
     * 删除会话的extInfo字段的配置
     * @param chatSessionDO
     * @param key
     * @param value
     */
    public static void deleteSessionConfig(ChatSessionDO chatSessionDO, String key) {
        String extInfo = chatSessionDO.getExtInfo();
        JSONObject extInfoJson = StringUtils.isBlank(extInfo) ? new JSONObject() : JSON.parseObject(extInfo);
        extInfoJson.remove(key);
        // 更新会话扩展信息
        chatSessionDO.setExtInfo(extInfoJson.toJSONString());
    }

    /**
     * 向会话的extInfo字段批量添加or更新配置
     * @param chatSessionDO
     * @param keys
     */
    public static void deleteSessionConfig(ChatSessionDO chatSessionDO, List<String> keys) {
        String extInfo = chatSessionDO.getExtInfo();
        JSONObject extInfoJson = StringUtils.isBlank(extInfo) ? new JSONObject() : JSON.parseObject(extInfo);

        for (String key : keys) {
            extInfoJson.remove(key);
        }

        // 更新会话扩展信息
        chatSessionDO.setExtInfo(extInfoJson.toJSONString());
    }
}

