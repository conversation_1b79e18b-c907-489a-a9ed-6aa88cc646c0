package com.alipay.codegencore.model.openai;

import java.util.List;

/**
 * Object containing a response from the chat completions api.
 */
public class ChatCompletionResult {

    /**
     * Unique id assigned to this chat completion.
     */
    private String id;

    /**
     * The type of object returned, should be "chat.completion"
     */
    private String object;

    /**
     * The creation time in epoch seconds.
     */
    private long created;
    
    /**
     * The GPT-3.5 model used.
     */
    private String model;

    /**
     * A list of all generated completions.
     */
    private List<ChatCompletionChoice> choices;

    /**
     * The API usage for this request.
     */
    private Usage usage;

    /**
     * openai error
     */
    private OpenAiErrorDetail error;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public long getCreated() {
        return created;
    }

    public void setCreated(long created) {
        this.created = created;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<ChatCompletionChoice> getChoices() {
        return choices;
    }

    public void setChoices(List<ChatCompletionChoice> choices) {
        this.choices = choices;
    }

    public Usage getUsage() {
        return usage;
    }

    public void setUsage(Usage usage) {
        this.usage = usage;
    }

    public OpenAiErrorDetail getError() {
        return error;
    }

    public void setError(OpenAiErrorDetail error) {
        this.error = error;
    }
}
