<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.UserAuthDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.UserAuthDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="bu_name" jdbcType="VARCHAR" property="buName" />
    <result column="emp_id" jdbcType="VARCHAR" property="empId" />
    <result column="admin" jdbcType="TINYINT" property="admin" />
    <result column="alipay_account" jdbcType="VARCHAR" property="alipayAccount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="save_scenes" jdbcType="VARCHAR" property="saveScenes" />
    <result column="top_session_uids" jdbcType="VARCHAR" property="topSessionUids" />
    <result column="application_reason" jdbcType="VARCHAR" property="applicationReason" />
    <result column="allow_access_type" jdbcType="INTEGER" property="allowAccessType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    id, gmt_create, gmt_modified, user_name, token, bu_name, emp_id, admin, alipay_account, 
    status, phone_number, save_scenes, top_session_uids, application_reason, allow_access_type
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.UserAuthDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_user_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.UserAuthDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    delete from cg_user_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.UserAuthDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_user_auth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="buName != null">
        bu_name,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="admin != null">
        admin,
      </if>
      <if test="alipayAccount != null">
        alipay_account,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="saveScenes != null">
        save_scenes,
      </if>
      <if test="topSessionUids != null">
        top_session_uids,
      </if>
      <if test="applicationReason != null">
        application_reason,
      </if>
      <if test="allowAccessType != null">
        allow_access_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="buName != null">
        #{buName,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=VARCHAR},
      </if>
      <if test="admin != null">
        #{admin,jdbcType=TINYINT},
      </if>
      <if test="alipayAccount != null">
        #{alipayAccount,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="saveScenes != null">
        #{saveScenes,jdbcType=VARCHAR},
      </if>
      <if test="topSessionUids != null">
        #{topSessionUids,jdbcType=VARCHAR},
      </if>
      <if test="applicationReason != null">
        #{applicationReason,jdbcType=VARCHAR},
      </if>
      <if test="allowAccessType != null">
        #{allowAccessType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.UserAuthDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    select count(*) from cg_user_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    update cg_user_auth
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.token != null">
        token = #{record.token,jdbcType=VARCHAR},
      </if>
      <if test="record.buName != null">
        bu_name = #{record.buName,jdbcType=VARCHAR},
      </if>
      <if test="record.empId != null">
        emp_id = #{record.empId,jdbcType=VARCHAR},
      </if>
      <if test="record.admin != null">
        admin = #{record.admin,jdbcType=TINYINT},
      </if>
      <if test="record.alipayAccount != null">
        alipay_account = #{record.alipayAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.phoneNumber != null">
        phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.saveScenes != null">
        save_scenes = #{record.saveScenes,jdbcType=VARCHAR},
      </if>
      <if test="record.topSessionUids != null">
        top_session_uids = #{record.topSessionUids,jdbcType=VARCHAR},
      </if>
      <if test="record.applicationReason != null">
        application_reason = #{record.applicationReason,jdbcType=VARCHAR},
      </if>
      <if test="record.allowAccessType != null">
        allow_access_type = #{record.allowAccessType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    update cg_user_auth
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      user_name = #{record.userName,jdbcType=VARCHAR},
      token = #{record.token,jdbcType=VARCHAR},
      bu_name = #{record.buName,jdbcType=VARCHAR},
      emp_id = #{record.empId,jdbcType=VARCHAR},
      admin = #{record.admin,jdbcType=TINYINT},
      alipay_account = #{record.alipayAccount,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      save_scenes = #{record.saveScenes,jdbcType=VARCHAR},
      top_session_uids = #{record.topSessionUids,jdbcType=VARCHAR},
      application_reason = #{record.applicationReason,jdbcType=VARCHAR},
      allow_access_type = #{record.allowAccessType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.UserAuthDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Oct 17 16:33:42 CST 2023.
    -->
    update cg_user_auth
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="buName != null">
        bu_name = #{buName,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=VARCHAR},
      </if>
      <if test="admin != null">
        admin = #{admin,jdbcType=TINYINT},
      </if>
      <if test="alipayAccount != null">
        alipay_account = #{alipayAccount,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="saveScenes != null">
        save_scenes = #{saveScenes,jdbcType=VARCHAR},
      </if>
      <if test="topSessionUids != null">
        top_session_uids = #{topSessionUids,jdbcType=VARCHAR},
      </if>
      <if test="applicationReason != null">
        application_reason = #{applicationReason,jdbcType=VARCHAR},
      </if>
      <if test="allowAccessType != null">
        allow_access_type = #{allowAccessType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>