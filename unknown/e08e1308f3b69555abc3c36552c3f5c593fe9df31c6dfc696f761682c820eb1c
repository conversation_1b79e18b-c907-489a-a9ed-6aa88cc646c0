package com.alipay.codegencore.model.model.links;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.06.25
 */
public class GptConversationExtInfo {
    /**
     * 聆图会话id
     */
    private String extConversationId;

    /**
     * 站点助手id
     */
    private String copilotSessionId ;


    private JSONObject extInfo ;

    public String getExtConversationId() {
        return extConversationId;
    }

    public void setExtConversationId(String extConversationId) {
        this.extConversationId = extConversationId;
    }

    public String getCopilotSessionId() {
        return copilotSessionId;
    }

    public void setCopilotSessionId(String copilotSessionId) {
        this.copilotSessionId = copilotSessionId;
    }

    public JSONObject getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(JSONObject extInfo) {
        this.extInfo = extInfo;
    }

    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param copilotSessionId copilotSessionId
     * @return com.alipay.codegencore.model.model.links.GptConversationExtInfo
     */
    public static GptConversationExtInfo build(String copilotSessionId){
        GptConversationExtInfo gptConversationExtInfo = new GptConversationExtInfo();
        gptConversationExtInfo.setCopilotSessionId(copilotSessionId);
        return gptConversationExtInfo ;
    }
}
