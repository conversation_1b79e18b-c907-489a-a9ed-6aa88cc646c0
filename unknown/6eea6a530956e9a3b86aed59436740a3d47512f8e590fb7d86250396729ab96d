package com.alipay.codegencore.model.request.maya;

import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.codegencore.model.openai.ChatFunction;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 大模型请求算法的request参数
 */
public class MayaStreamRequestBean {

    /**
     * 提问内容
     */
    private ArrayList<Object> prompts;

    private Boolean dialogue;

    private Integer dialogueRounds;

    @JSONField(name = "beam_width")
    private Integer beamWidth;

    @JSONField(name = "out_seq_length")
    private Integer outSeqLength;

    private BigDecimal temperature;

    @JSONField(name = "top_k")
    private Integer topK;
    @JSONField(name = "top_p")
    private BigDecimal topP;

    @JSONField(name = "random_seed")
    private BigDecimal randomSeed;
    /**
     * 最大长度为 4 的字符串列表，一旦生成的 tokens 包含其中的内容，将停止生成并返回结果
     */
    @JSONField(name = "stop_words")
    private List<String> stopWords;
    @JSONField(name = "last_tokens")
    private List<String> lastTokens;
    @JSONField(name = "repetition_penalty")
    private BigDecimal repetitionPenalty;

    private Boolean stream;

    private List<ChatFunction> functions;

    @JSONField(name = "max_length")
    private Integer maxLength;

    public List<String> getStopWords() {
        return stopWords;
    }

    public void setStopWords(List<String> stopWords) {
        this.stopWords = stopWords;
    }
    public ArrayList<Object> getPrompts() {
        return prompts;
    }

    public void setPrompts(ArrayList<Object> prompts) {
        this.prompts = prompts;
    }

    public Boolean getDialogue() {
        return dialogue;
    }

    public void setDialogue(Boolean dialogue) {
        this.dialogue = dialogue;
    }

    public Integer getDialogueRounds() {
        return dialogueRounds;
    }

    public void setDialogueRounds(Integer dialogueRounds) {
        this.dialogueRounds = dialogueRounds;
    }

    public Integer getBeamWidth() {
        return beamWidth;
    }

    public void setBeamWidth(Integer beamWidth) {
        this.beamWidth = beamWidth;
    }

    public Integer getOutSeqLength() {
        return outSeqLength;
    }

    public void setOutSeqLength(Integer outSeqLength) {
        this.outSeqLength = outSeqLength;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public BigDecimal getTopP() {
        return topP;
    }

    public void setTopP(BigDecimal topP) {
        this.topP = topP;
    }

    public BigDecimal getRandomSeed() {
        return randomSeed;
    }

    public void setRandomSeed(BigDecimal randomSeed) {
        this.randomSeed = randomSeed;
    }

    public List<String> getLastTokens() {
        return lastTokens;
    }

    public void setLastTokens(List<String> lastTokens) {
        this.lastTokens = lastTokens;
    }

    public BigDecimal getRepetitionPenalty() {
        return repetitionPenalty;
    }

    public void setRepetitionPenalty(BigDecimal repetitionPenalty) {
        this.repetitionPenalty = repetitionPenalty;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    public List<ChatFunction> getFunctions() {
        return functions;
    }

    public void setFunctions(List<ChatFunction> functions) {
        this.functions = functions;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }
}
