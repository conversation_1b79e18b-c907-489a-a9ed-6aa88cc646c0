package com.alipay.codegencore.service.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023.10.25
 */
public class SqlTokenUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(SqlTokenUtils.class);
    /**
     * 生成sql执行所需token
     * <AUTHOR>
     * @since 2023.10.25
     * @param sql sql
     * @param table table
     * @param operator operator
     * @return java.lang.String
     */
    public static String getToken(String sql,String table,String operator){
        if(StringUtils.isNotBlank(sql)&&StringUtils.isNotBlank(table)&&StringUtils.isNotBlank(operator)){
            return md5String(sql+table+operator+"codefuse-granada");
        }
        return null;
    }
    /**
     * MD5加密
     * <AUTHOR>
     * @since 2023.10.25
     * @param str str
     * @return java.lang.String
     */
    private static String md5String(String str){
        StringBuilder sb = new StringBuilder();
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] bytes = str.getBytes();
            byte[] md5Bytes = md5.digest(bytes);
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b & 0xff));
            }
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("Unable to generate token");
        }
        return sb.toString();
    }
}
