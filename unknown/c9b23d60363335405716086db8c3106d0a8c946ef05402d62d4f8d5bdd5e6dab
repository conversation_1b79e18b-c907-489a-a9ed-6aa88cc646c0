package com.alipay.codegencore.utils;

import com.alipay.codegencore.utils.adapter.HttpAdapter;
import com.alipay.codegencore.utils.adapter.HttpAdapterResponse;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.utils
 * @CreateTime : 2023-04-27
 */
public class HttpAdapterTest extends AbstractTestBase{
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpAdapterTest.class);
    /**
     * 测试
     */
    @Test
    public void test_sendMessage() {
        Map<String, Object> cesi = new HashMap<>();
        cesi.put("systemId", "cesi");
        HttpAdapterResponse response = HttpAdapter.request("GET",  "https://aims.alipay.com/service/api/dept/user/list", getAimsParams(),cesi,null);
        Assertions.assertEquals(response.getStatusCode(),200);
        HttpAdapterResponse response2 = HttpAdapter.request("POST",  "https://aims.alipay.com/service/api/dept/user/list", getAimsParams(), new HashMap<>(),null);
        Assertions.assertEquals(response2.getStatusCode(),200);
        try {
            HttpAdapterResponse response3 = HttpAdapter.request("测试",  "https://aims.alipay.com/service/api/dept/user/list", getAimsParams(), new HashMap<>(),null);
        }catch (Exception e){
            LOGGER.info(e.getMessage());
        }
        Map<String, Object> aimsParams = getAimsParams();
        aimsParams.remove("systemId");

        HttpAdapterResponse response4 = HttpAdapter.request("POST",  "https://aims.alipay.com/service/api/dept/user/list",aimsParams,cesi,null);
        Assertions.assertEquals(response4.getStatusCode(),200);
    }

    private Map<String, Object> getAimsParams() {
        String systemId = "C-Stone";
        String secretKey = DigestUtils.md5Hex(systemId + "SMk8v3rj&y8OUvei");
        Map<String, Object> params = new HashMap<>();
        params.put("systemId", systemId);
        params.put("secretKey", secretKey);
        return params;
    }
}
