package com.alipay.codegencore.model.request;

import com.alipay.codegencore.model.enums.BizSceneEnum;
import com.alipay.codegencore.model.model.CodeTemplateRuleModel;

/**
 * 代码模版请求
 *
 * <AUTHOR>
 * 创建时间 2022-02-24
 */
public class CodeTemplateRequestBean {
    /**
     * 模版id
     */
    private Long id;
    /**
     * 模版源代码
     */
    private String sourceCode;
    /**
     * 模版名（唯一)
     */
    private String templateName;
    /**
     * 模版规则
     */
    private CodeTemplateRuleModel ruleModel;

    /**
     * 模版场景类型
     */
    private BizSceneEnum bizSceneEnum;
    /**
     * 模版所用于的场景描述
     */
    private String sceneDesc;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BizSceneEnum getBizSceneEnum() {
        return bizSceneEnum;
    }

    public void setBizSceneEnum(BizSceneEnum bizSceneEnum) {
        this.bizSceneEnum = bizSceneEnum;
    }

    public String getSceneDesc() {
        return sceneDesc;
    }

    public void setSceneDesc(String sceneDesc) {
        this.sceneDesc = sceneDesc;
    }

    public CodeTemplateRuleModel getRuleModel() {
        return ruleModel;
    }

    public void setRuleModel(CodeTemplateRuleModel ruleModel) {
        this.ruleModel = ruleModel;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }


}
