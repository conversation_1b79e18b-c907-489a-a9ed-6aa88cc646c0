package com.alipay.codegencore.utils.http;

import java.io.IOException;
import java.net.URISyntaxException;
import java.net.http.HttpResponse;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * http请求构建builder
 * 抽象出通用函数
 *
 * <AUTHOR>
 * 创建时间 2022-10-17
 */
public abstract class AbstractRequestBuilder {
    protected String url;
    protected java.net.http.HttpClient httpClient;

    public AbstractRequestBuilder(String url, java.net.http.HttpClient httpClient) {
        this.url = url;
        this.httpClient = httpClient;
    }

    /**
     * 同步执行，执行报错返回空
     *
     * @param timeout
     * @return
     */
    protected abstract String syncExecute(long timeout);

    /**
     * 流式调用远程服务,会由{@link DefaultStreamDataSubscriber)消费
     * 同时每条数据会调用{@link StreamDataListener#eachData(String, Flow.Subscription)} ,上游控制数据输出
     *
     * @param timeout            超时时间
     * @param streamDataListener 数据监听器
     * @throws URISyntaxException
     */
    protected abstract void streamExecute(long timeout, StreamDataListener streamDataListener, BiConsumer<Integer,String> errorHandler) throws URISyntaxException;

    /**
     * 流式调用远程服务,会由{@link DefaultStreamDataSubscriber)消费，response会有特殊的消费逻辑
     * 同时每条数据会调用{@link StreamDataListener#eachData(String, Flow.Subscription)} ,上游控制数据输出
     *
     * @param timeout            超时时间
     * @param streamDataListener 数据监听器
     * @throws URISyntaxException
     */
    public abstract void streamExecuteWithResponseHandler(long timeout, StreamDataListener streamDataListener, BiConsumer<Integer, String> errorHandler, Consumer<HttpResponse.ResponseInfo> responseInfoConsumer) throws URISyntaxException;

    /**
     * 同步执行，执行报错会抛异常
     *
     * @param timeout
     * @return
     */
    protected abstract String syncExecuteWithExceptionThrow(long timeout) throws IOException, InterruptedException, URISyntaxException;

    /**
     * 同步执行，执行报错会抛异常，返回完整的response
     * @param timeout 超时时间
     * @return 完整的response
     */
    protected abstract HttpResponse<String> syncExecuteWithFullResponse(long timeout) throws IOException, InterruptedException, URISyntaxException;

}
