package com.alipay.codegencore.model.enums;

/**
 * 记录日志枚举类
 */
public enum RecordLogEnum {

    // 记录用户访问数据
    USER_ACCESS("userAccess", "用户访问"),
    // 记录API访问数据
    API_ACCESS("apiAccess", "api访问"),
    // 依赖服务的分析数据, MAYA, 解语花， infosec等
    DEPENDENCY_DATA("dependencyData", "依赖服务的分析数据"),
    // 模型使用分析
    MODEL_USED("modelUsed", "模型使用统计"),

    // 模型环境可用性分析
    MODEL_ENV_INFO("modelEnvInfo", "模型部署环境信息"),

    // 插件调用结果， 包含普通插件和functionCall
    PLUGIN_USED("pluginUsed", "pluginUsed调用结果"),
    
    // 记录API调用输入输出数据
    API_DATA("apiDataVersion2", "api调用传输数据")
    ;

    RecordLogEnum(String name, String nameCn) {
        this.name = name;
        this.nameCn = nameCn;
    }

    // 日志名称
    public final String name;
    // 日志中文名称
    public final String nameCn;

    /**
     * 根据记录日志枚举类型的名称获取对应的枚举对象。
     *
     * @param name 记录日志枚举类型的名称，忽略大小写。
     * @return 返回对应的枚举对象，如果不存在则返回null。
     */
    public static RecordLogEnum getByName(String name) {
        // 遍历RecordLogEnum枚举类型中的所有元素
        for (RecordLogEnum logRecord : RecordLogEnum.values()) {
            // 如果当前元素的名称与传入的名称相同，则返回该元素
            if (logRecord.getName().equalsIgnoreCase(name)) {
                return logRecord;
            }
        }
        // 如果遍历完所有元素都没有找到匹配的，则说明传入的名称不合法或者没有对应的枚举对象，返回null
        return null;
    }


    /**
     * 根据中文名获取对应枚举值
     *
     * @param nameCn 中文名
     * @return 枚举值，若不存在则返回null
     */
    public static RecordLogEnum getsByNameCn(String nameCn) {
        // 遍历RecordLogEnum枚举类中所有的元素
        for (RecordLogEnum logRecord : RecordLogEnum.values()) {
            // 若当前元素的中文名与传入的nameCn相同，则返回该元素
            if (logRecord.getNameCn().equalsIgnoreCase(nameCn)) {
                return logRecord;
            }
        }
        // 若遍历完所有元素都未匹配到，则返回null
        return null;
    }

    public String getName() {
        return name;
    }

    public String getNameCn() {
        return nameCn;
    }

}
