package com.alipay.codegencore.service.impl;

import com.alipay.codegencore.dal.example.ConfigDOExample;
import com.alipay.codegencore.dal.mapper.ConfigDOMapper;
import com.alipay.codegencore.model.domain.ConfigDO;
import com.alipay.codegencore.service.common.ConfigService;
import com.google.common.cache.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Callable;
/**
 * 配置服务实现
 * <AUTHOR>
 */
@Service
public class ConfigServiceImpl implements ConfigService {
    private static final Logger LOGGER = LoggerFactory.getLogger( ConfigServiceImpl.class );

    @Resource
    private ConfigDOMapper configDOMapper;
    @Resource(name = "cachePool")
    private Cache<String, String> cachePool;
    /**
     * 通过key获取配置表中的value，默认从缓存中获取
     *
     * @param key            配置key
     * @param forceGetFromDb 强制从数据库获取
     * @return
     */
    @Override
    public String getConfigByKey(String key, boolean forceGetFromDb) {
        String value = null;
        try {
            if (forceGetFromDb) {
                cachePool.invalidate(key);
            }
            value = cachePool.get(key, new Callable() {
                @Override
                public Object call() {
                    ConfigDOExample configDOExample = new ConfigDOExample();
                    configDOExample.createCriteria().andNameEqualTo(key);

                    List<ConfigDO> configDOList = configDOMapper.selectByExample(configDOExample);
                    return configDOList.isEmpty() ? null : configDOList.get(0).getValue();
                }
            });
        } catch (Exception e) {
            LOGGER.error("获取配置错误", e);
        }
        return value;
    }
}
