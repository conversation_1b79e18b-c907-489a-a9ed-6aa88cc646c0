/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.request;

/**
 * 用户登录通知插件端请求
 *
 * <AUTHOR>
 * @version UserLoginNotifyPluginRequest.java, v 0.1 2023年06月20日 17:29 xiaobin
 */
public class UserLoginNotifyPluginRequest {

    /**
     * 用户uid 内网为工号  外网为手机号
     */
    private String userUid;

    /**
     * 用户类型  BUC 或者 ALIPAY
     */
    private String userType;

    /**
     * 透传的参数列表
     */
    private String params;

    public String getUserUid() {
        return userUid;
    }

    public void setUserUid(String userUid) {
        this.userUid = userUid;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }
}