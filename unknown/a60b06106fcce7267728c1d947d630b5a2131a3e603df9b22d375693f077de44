package com.alipay.codegencore.model.remote;

import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:30
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SessionObject {

    @JsonProperty(value = "id", required = true)
    private String id;

    @JsonProperty(value = "created_at", required = true)
    private Long createdAt;

    @JsonProperty(value = "metadata")
    private Map<String, Object> metadata;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    /**
     * 模型转换
     * @param chatSessionDO
     * @return
     */
    public static SessionObject of(ChatSessionDO chatSessionDO) {
        SessionObject sessionObject = new SessionObject();
        sessionObject.setCreatedAt(System.currentTimeMillis());
        sessionObject.setId(chatSessionDO.getUid());
        return sessionObject;
    }
}
