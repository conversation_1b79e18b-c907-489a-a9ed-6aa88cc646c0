package com.alipay.codegencore.utils.codescan;

import com.alipay.codegencore.model.model.analysis.TempCodeAnalysisResultContext;

/**
 * 代码静态扫描
 *
 * <AUTHOR>
 * 创建时间 2022-08-11
 */
public interface CodeStaticScanService {
    /**
     * 分析源代码，根据不同的扫描类型返回不同结果
     *
     * @param content  源代码内容
     * @param scanType 扫描类型
     * @param clazz    结果对象
     * @param <T>
     * @return
     */
    public <T> T analysisCode(String content, ScanTypeEnum scanType, Class<T> clazz);

    /**
     * 从{@link TempCodeAnalysisResultContext}中查询 referenceName对应的class类型
     * @param referenceName
     * @param tempCodeAnalysisResultContext
     * @return
     */
    public String getReferenceClassName(String referenceName, TempCodeAnalysisResultContext tempCodeAnalysisResultContext);
}
