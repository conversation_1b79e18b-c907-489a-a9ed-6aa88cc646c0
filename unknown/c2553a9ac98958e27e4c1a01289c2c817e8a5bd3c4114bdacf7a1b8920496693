package com.alipay.codegencore.model.openai;

import com.alipay.codegencore.model.domain.ChatSessionDO;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.openai
 * @CreateTime : 2023-08-09
 */
public class ChatSessionVO extends ChatSessionDO {

    /**
     * 助手是否下线
     */
    private Boolean sceneOffline;

    /**
     * 会话是否置顶
     */
    private Boolean topSession;

    public Boolean getSceneOffline() {
        return sceneOffline != null && sceneOffline;
    }

    public void setSceneOffline(Boolean sceneOffline) {
        this.sceneOffline = sceneOffline;
    }


    public Boolean getTopSession() {
        return topSession;
    }

    public void setTopSession(Boolean topSession) {
        this.topSession = topSession;
    }
}
