package com.alipay.codegencore.service.utils;

import com.alipay.codegencore.service.impl.model.CodeGptLanguageModelServiceImpl;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 锁相关的service
 */
@Service
public class TbaseCacheService {

    private static final Logger LOGGER = LoggerFactory.getLogger( CodeGptLanguageModelServiceImpl.class );

    @Resource
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    private String buildKey(String prefix, String itemName){
        return String.format("codegencore:%s:%s",prefix, itemName);
    }

    private static String preFixLock = "lock";

    /**
     * 获取锁,拿不到的话最长等待waitMilSecond毫秒
     * @param lockName
     * @param expireMilSecond 毫秒
     * @param waitMilSecond  毫秒
     * @return
     */
    public boolean getLock(String lockName, int expireMilSecond, int waitMilSecond) {
        boolean ret = getLock(lockName,expireMilSecond);
        long startTime = System.currentTimeMillis();
        while (!ret) {
            try {
                long costTime = System.currentTimeMillis() - startTime;
                if (costTime >= waitMilSecond) {
                    return false;
                }
                Thread.sleep(50);
                ret = getLock(lockName, expireMilSecond);
                if (ret) {
                    return ret;
                }
            } catch (Exception e) {
                LOGGER.error("获取锁异常,lockName:" + lockName, e);
            }
        }
        return ret;
    }


    /**
     * 直接获取锁
     * @param lockName
     * @param expireMilSecond   毫秒
     * @return
     */
    public boolean getLock(String lockName, int expireMilSecond){
        String lockKey = buildKey(preFixLock, lockName);
        String ret = defaultCacheManager.set( lockKey, "ownerId1", "nx", "px", expireMilSecond);
        if (!"OK".equalsIgnoreCase(ret)) {
            LOGGER.debug("getLock ret: lockName={} expire={} ret={}",lockName, expireMilSecond, ret);
        }
        return "OK".equalsIgnoreCase(ret);
    }

    /**
     * 释放锁
     * @param lockName
     * @return
     */
    public boolean releaseLock(String lockName){
        String lockKey = buildKey(preFixLock, lockName);
        Long ret = defaultCacheManager.compareDel(lockKey, "ownerId1");
        if (ret != 1L) {
            LOGGER.info("releaseLock ret: lockName={}  ret={}",lockName, ret);
        }
        return ret == 1L;
    }


}
