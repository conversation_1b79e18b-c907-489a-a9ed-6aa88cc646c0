package com.alipay.codegencore.model.model;

import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * chatgpt流式输出时的缓冲器
 *
 * <AUTHOR>
 */
public class ChatStreamBuffer {
    /**
     * 缓存下来的内容
     */
    private StringBuilder content;
    private ChatFunctionCallBuffer functionCall;
    /**
     * token缓存器，存储的是一个个的token
     */
    private List<String> tokenBuffer;

    /**
     * 审核结果
     */
    private CheckResultModel checkResultModel;

    public ChatStreamBuffer() {
        content = new StringBuilder();
        tokenBuffer = new ArrayList<>();
        functionCall = new ChatFunctionCallBuffer();
    }

    public StringBuilder getContent() {
        return content;
    }

    public void setContent(StringBuilder content) {
        this.content = content;
    }

    public List<String> getTokenBuffer() {
        return tokenBuffer;
    }

    public void setTokenBuffer(List<String> tokenBuffer) {
        this.tokenBuffer = tokenBuffer;
    }

    public ChatFunctionCallBuffer getFunctionCall() {
        return functionCall;
    }

    public void setFunctionCall(ChatFunctionCallBuffer functionCall) {
        this.functionCall = functionCall;
    }


    /**
     * 获取缓冲区的大小
     * @return
     */
    public int getBufferSize(){
        // 如果functionCall不为空，默认认为buffer中数据极大，会直接触发flush操作
        if(!functionCall.getArgumentsBuffer().isEmpty() || !functionCall.getNameBuffer().isEmpty()){
            return Integer.MAX_VALUE;
        }
        return tokenBuffer.size();
    }

    /**
     * 刷新缓冲
     */
    public void flush() {
        for(String token: tokenBuffer){
            if(token!=null){
                content.append(token);
            }
        }
        tokenBuffer.clear();

        functionCall.flush();
    }

    /**
     * 给content追加内容
     * @param token 单个token
     */
    public void buffer(String token) {
        this.tokenBuffer.add(token);
    }

    /**
     * 缓存ChatMessage
     * @param chatMessage 流式消息
     */
    public void buffer(ChatMessage chatMessage) {
        if (chatMessage.getContent() != null) {
            buffer(chatMessage.getContent());
        } else if (chatMessage.getFunctionCall() != null) {
            this.functionCall.buffer(chatMessage);
        }
    }

    /**
     * 获取缓冲区的内容
     * @return 缓冲区中token列表代表的内容
     */
    public String getBufferContent() {
        if(tokenBuffer.isEmpty()){
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for(String token: tokenBuffer){
            sb.append(token);
        }
        return sb.toString();
    }

    public CheckResultModel getCheckResultModel() {
        return checkResultModel;
    }

    public void setCheckResultModel(CheckResultModel checkResultModel) {
        this.checkResultModel = checkResultModel;
    }

    /**
     * function call缓冲器
     */
    public class ChatFunctionCallBuffer {

        /**
         * 方法名缓冲区
         */
        List<String> nameBuffer;
        /**
         * 方法名推理结果
         */
        StringBuilder nameBuilder;

        /**
         * The arguments of the call produced by the model, represented as a JsonNode for easy manipulation.
         */
        List<String> argumentsBuffer;

        /**
         * 参数推理结果
         */
        StringBuilder argumentsBuilder;

        public ChatFunctionCallBuffer() {
            nameBuffer = new ArrayList<>();
            nameBuilder = new StringBuilder();
            argumentsBuffer = new ArrayList<>();
            argumentsBuilder = new StringBuilder();
        }

        public List<String> getNameBuffer() {
            return nameBuffer;
        }

        public void setNameBuffer(List<String> nameBuffer) {
            this.nameBuffer = nameBuffer;
        }

        public StringBuilder getNameBuilder() {
            return nameBuilder;
        }

        public void setNameBuilder(StringBuilder nameBuilder) {
            this.nameBuilder = nameBuilder;
        }

        public List<String> getArgumentsBuffer() {
            return argumentsBuffer;
        }

        public void setArgumentsBuffer(List<String> argumentsBuffer) {
            this.argumentsBuffer = argumentsBuffer;
        }

        public StringBuilder getArgumentsBuilder() {
            return argumentsBuilder;
        }

        public void setArgumentsBuilder(StringBuilder argumentsBuilder) {
            this.argumentsBuilder = argumentsBuilder;
        }

        /**
         * 缓存ChatMessage
         * @param chatMessage
         */
        public void buffer(ChatMessage chatMessage){
            ChatFunctionCall functionCall = chatMessage.getFunctionCall();
            if (functionCall.getName() != null) {
                nameBuffer.add(functionCall.getName());
            } else if (functionCall.getArguments() != null) {
                argumentsBuffer.add(functionCall.getArguments());
            }
        }

        /**
         * 获取参数缓冲区的内容
         * @return 缓冲区中token列表代表的内容
         */
        public String getArgumentBufferContent() {
            if(argumentsBuffer.isEmpty()){
                return null;
            }
            StringBuilder sb = new StringBuilder();
            for(String token: argumentsBuffer){
                sb.append(token);
            }
            return sb.toString();
        }

        /**
         * 刷新缓冲
         */
        public void flush(){
            for (String nameToken: nameBuffer){
                if(nameToken!=null){
                    nameBuilder.append(nameToken);
                }
            }
            nameBuffer.clear();

            for(String argumentToken: argumentsBuffer){
                if(argumentToken!=null){
                    argumentsBuilder.append(argumentToken);
                }
            }
            argumentsBuffer.clear();
        }

        /**
         * 转换为ChatFunctionCall
         * @return
         */
        public ChatFunctionCall toChatFunctionCall() {
            if (nameBuilder.length()==0) {
                return null;
            }

            return new ChatFunctionCall(nameBuilder.toString(), argumentsBuilder.length() == 0 ? null : argumentsBuilder.toString());
        }
    }
}