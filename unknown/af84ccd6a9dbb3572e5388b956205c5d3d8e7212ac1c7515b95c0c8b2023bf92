package com.alipay.codegencore.service.impl.model;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.RecordLogEnum;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.CollectLogUtils;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 模型健康检测
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023.11.23
 */
@Service
public class AlgoModelHealthUtilService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlgoModelHealthUtilService.class);

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;


    /*** health check data format
     * {
     *     "prod" : {
     *         "summary" : {
     *             "healthDegree" : 10,
     *             "lastFailedReason": "XXXXX"
     *         },
     *         "checkTime" : "2023-10-30 14:11:20",
     *     	"checkHistory" : {
     *             "lastAvailable" : "2023-10-30 14:11:20",
     *             "lastUnavailable" : "2023-10-29 14:11:20"
     *         }
     *     },
     *     "pre" :{
     *          "summary" : {
     *             "healthDegree" : 10,
     *             "lastFailedReason": "XXXXX"
     *         },
     *         "checkTime" : "2023-10-30 14:11:20",
     *     	"checkHistory" : {
     *             "lastAvailable" : "2023-10-30 14:11:20",
     *             "lastUnavailable" : "2023-10-29 14:11:20"
     *         }
     *     }
     * }
     */

    /*** health drm config
     * {
     * 	"fullHealth": 100,
     * 	"costHealth" : 1,
     * 	"healthThreshold" : 90,
     * 	"keepModelMinIntervals" : 1200
     * }
     */

    /**
     * 重置健康度
     * <AUTHOR>
     * @since 2023.11.23
     * @param modelEnv modelEnv
     * @param modelName modelName
     */
    public void resetHealth(String modelEnv, String modelName, boolean init){
        modelEnv = transferEnv(modelEnv);
        if(StringUtils.isEmpty(modelName)) {
            LOGGER.warn("found a invalid request resetHealth, {}, {}", modelEnv, init);
            return;
        }
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to resetHealth, {}, {}, {}", modelEnv, modelName, init);
        }
        JSONObject healthDegreeJson = getHealthDegreeCacheByModel(modelName);
        if(null == healthDegreeJson) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("first time set parent cache, {}, {}, {}", modelEnv, modelName, init);
            }
            healthDegreeJson = new JSONObject();
        }
        if(null == healthDegreeJson.get(modelEnv)) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("first time set env cache , {}, {}, {}", modelEnv, modelName, init);
            }
            healthDegreeJson.put(modelEnv, new JSONObject());
        }

        if(!init){
            // 将最新一次调用成功的时间更新
            if(null == healthDegreeJson.getJSONObject(modelEnv).get("summary")) {
                healthDegreeJson.getJSONObject(modelEnv).put("summary", new JSONObject());
            }
            healthDegreeJson.getJSONObject(modelEnv).getJSONObject("summary").put("healthDegree", getDrmValueOfHealthDegree("fullHealth"));
            String curTime = getCurTime();
            if(null == healthDegreeJson.getJSONObject(modelEnv).get("checkHistory")) {
                healthDegreeJson.getJSONObject(modelEnv).put("checkHistory", new JSONObject());
            }
            healthDegreeJson.getJSONObject(modelEnv).getJSONObject("checkHistory").put("lastAvailable", curTime);
            healthDegreeJson.getJSONObject(modelEnv).put("checkTime", curTime);
        } else {
            JSONObject summary = new JSONObject();
            summary.put("healthDegree", getDrmValueOfHealthDegree("fullHealth"));
            healthDegreeJson.getJSONObject(modelEnv).put("summary", summary);
        }

        defaultCacheManager.setex(generateKey(modelName), AppConstants.HEALTH_CHECK_CACHE_EXPIRED, healthDegreeJson);

        if(!init) {
            Map<String, Object> recordDetail = new HashMap<>();
            recordDetail.put("model", modelName);
            recordDetail.put("modelEnv", modelEnv);
            recordDetail.put("status", true);
            recordDetail.put("errorInfo", "");
            recordDetail.put("healthDegree", healthDegreeJson.getJSONObject(modelEnv).getJSONObject("summary").get("healthDegree"));
            CollectLogUtils.printCollectLog(RecordLogEnum.MODEL_USED, recordDetail);
        }
    }

    /**
     * 减少健康度
     * <AUTHOR>
     * @since 2023.11.23
     * @param modelEnv modelEnv
     * @param modelName modelName
     */
    public void costHealth(String modelEnv, String modelName, String reasonEnum){
        modelEnv = transferEnv(modelEnv);
        if(StringUtils.isEmpty(modelName)) {
            LOGGER.warn("found a invalid request costHealth, {}, {}", modelEnv, reasonEnum);
            return;
        }
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to cost health : {}, {}, {}", modelEnv, modelName, reasonEnum);
        }
        JSONObject jsonObject = getHealthDegreeCacheByModel(modelName);
        if(jsonObject == null) {
            jsonObject = new JSONObject();
        }
        if(null == jsonObject.get(modelEnv)) {
            jsonObject.put(modelEnv, new JSONObject());
        }
        if(null == jsonObject.getJSONObject(modelEnv).get("summary")) {
            jsonObject.getJSONObject(modelEnv).put("summary", new JSONObject());
        }
        if(null == jsonObject.getJSONObject(modelEnv).getJSONObject("summary").get("healthDegree")) {
            jsonObject.getJSONObject(modelEnv).getJSONObject("summary").put("healthDegree", getDrmValueOfHealthDegree("fullHealth"));
        }

        int curFullHealth = jsonObject.getJSONObject(modelEnv).getJSONObject("summary").getIntValue("healthDegree");
        String curTime = getCurTime();
        Integer updatedHealthDegree = curFullHealth - 1;
        jsonObject.getJSONObject(modelEnv).getJSONObject("summary").put("healthDegree",updatedHealthDegree);
        jsonObject.getJSONObject(modelEnv).getJSONObject("summary").put("lastFailedReason",reasonEnum);
        jsonObject.getJSONObject(modelEnv).put("checkTime", curTime);
        if(null == jsonObject.getJSONObject(modelEnv).get("checkHistory")) {
            jsonObject.getJSONObject(modelEnv).put("checkHistory", new JSONObject());
        }
        jsonObject.getJSONObject(modelEnv).getJSONObject("checkHistory").put("lastUnavailable", curTime);

        defaultCacheManager.setex(generateKey(modelName), AppConstants.HEALTH_CHECK_CACHE_EXPIRED, jsonObject);
        if(LOGGER.isInfoEnabled()){
            LOGGER.info("model health degree decress : {}, {}, {}, {}, {}", modelName, modelEnv, curFullHealth, updatedHealthDegree, reasonEnum);
        }
        Map<String, Object> recordDetail = new HashMap<>();
        recordDetail.put("model", modelName);
        recordDetail.put("modelEnv", modelEnv);
        recordDetail.put("status", false);
        recordDetail.put("errorInfo", reasonEnum);
        recordDetail.put("healthDegree", jsonObject.getJSONObject(modelEnv).getJSONObject("summary").get("healthDegree"));

        CollectLogUtils.printCollectLog(RecordLogEnum.MODEL_USED, recordDetail);
    }

    /**
     * 判断当前模型的健康度情况，是否能继续调用
     * <AUTHOR>
     * @since 2023.11.23
     * @param modelEnv modelEnv
     * @param modelName modelName
     * @return boolean
     */
    public Integer isHealth(String modelEnv, String modelName){
        modelEnv = transferEnv(modelEnv);
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to get health degree value : {}, {}",
                    modelName, modelEnv);
        }
        JSONObject jsonObject = getHealthDegreeCacheByModel(modelName);
        if(null == jsonObject || null == jsonObject.get(modelEnv)) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("can't found health degree info, so default return true, {}, {}", modelName, modelEnv);
            }
            return HealthDegreeEnum.UNKNOWN.getCode();
        }

        int healthThreshold = getDrmValueOfHealthDegree("healthThreshold");
        int healthDegree = jsonObject.getJSONObject(modelEnv).getJSONObject("summary").getIntValue("healthDegree");
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("found health degree info, {}, {}, {}, {}", modelName, modelEnv, healthDegree, healthThreshold);
        }
        return healthDegree >= healthThreshold?HealthDegreeEnum.USABLE.getCode():HealthDegreeEnum.UNUSABLE.getCode();
    }

    public JSONObject getModelHealthDegree(String modelName){
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to get health degree detail : {}",
                    modelName);
        }
        return getHealthDegreeCacheByModel(modelName);
    }

    /**
     * 生成tbase键
     * <AUTHOR>
     * @since 2023.11.23
     * @param modelName modelName
     * @return java.lang.String
     */
    private String generateKey(String modelName){
        return String.format("%s%s",AppConstants.MODEL_HEALTH_DEGREE_CHECK_PREFIX,modelName);
    }
    /**
     * 生成map数据结构的健康度
     * <AUTHOR>
     * @since 2023.11.24
     * @param health health
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    private Map<String, String> generateValue(Integer health){
        Map<String, String> value = new HashMap<>();
        value.put("health", String.valueOf(health));
        value.put("modelCallLastTime",String.valueOf(System.currentTimeMillis()));
        return value;
    }


    public int getDrmValueOfHealthDegree(String value) {
        String healthDegreeCheckConfigStr = codeGPTDrmConfig.getHealthDegreeCheckConfig();

        JSONObject healthDegreeCheckConfig = null;
        try {
            healthDegreeCheckConfig = JSONObject.parseObject(healthDegreeCheckConfigStr);
        } catch(Exception ex) {
            LOGGER.warn("transfer json failed, {}", healthDegreeCheckConfigStr, ex);
        }
        if(healthDegreeCheckConfig != null
            && healthDegreeCheckConfig.get(value) != null) {
            return healthDegreeCheckConfig.getIntValue(value);
        }

        LOGGER.warn("can't get drm config for health degree : {}", value);

        // logic for execption
        if(value.equalsIgnoreCase("fullHealth")) {
            return 100;
        } else if(value.equalsIgnoreCase("costHealth")) {
            return 1;
        } else if(value.equalsIgnoreCase("healthThreshold")) {
            return 90;
        } else if(value.equalsIgnoreCase("keepModelMinIntervals")) {
            return 1200;
        }
        LOGGER.warn("invalid key for drm config, {}", value);
        return 0;
    }

    public JSONObject getHealthDegreeCacheByModel(String modelName) {
        if(LOGGER.isInfoEnabled()) {
            LOGGER.info("start to getHealthDegreeByModel, {}", modelName);
        }

        String tbKey = AppConstants.MODEL_HEALTH_DEGREE_CHECK_PREFIX + modelName;
        Serializable value = null;
        try {
            value = defaultCacheManager.get(tbKey);
        } catch (Exception ex) {
            LOGGER.warn("get tbase failed : {}", tbKey);
        }

        if(null == value) {
            if(LOGGER.isInfoEnabled()) {
                LOGGER.info("get tbase info is null : {}", tbKey);
            }
            return null;
        }

        return (JSONObject) value;
    }

    private String getCurTime() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date current = new Date(System.currentTimeMillis());
        return dateFormat.format(current);
    }

    private String transferEnv(String modelEnv) {
        if(StringUtils.isBlank(modelEnv)){
            modelEnv = "auto";
        }

        if("auto".equalsIgnoreCase(modelEnv)) {
            return VisableEnvUtil.isPrePub() ? "pre" : "prod";
        }
        return modelEnv;
    }

    public enum HealthDegreeEnum {

        UNUSABLE(0, "不可用"),
        USABLE(1, "可用"),
        UNKNOWN(2, "未知");


        HealthDegreeEnum(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        /**
         * 健康度值
         */
        private Integer code;
        /**
         * 健康度描述
         */
        private String description;

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
