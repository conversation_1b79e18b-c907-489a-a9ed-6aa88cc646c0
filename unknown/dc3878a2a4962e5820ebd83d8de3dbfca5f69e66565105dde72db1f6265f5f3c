package com.alipay.codegencore.utils.code;

import com.alipay.codegencore.model.openai.Plan;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.List;

class ActionGenCodePromptTest {

    @Test
    void buildPlanGenPrompt() {

        String systemPrompt = "system";

        String requ = "requ";

        List<CodeSearchClient.Item> codes = Lists.newArrayList();
        CodeSearchClient.Item item = new CodeSearchClient.Item();
        item.setId(1);
        item.setMethodContent("code info");
        item.setFilePath("/code/path/app.java");
        codes.add(item);

        String prompt = ActionGenCodePrompt.buildPlanGenPrompt(systemPrompt, requ, codes);
        Assert.assertNotNull(prompt);
    }

    @Test
    void buildCodeGenPrompt() {

        String systemPrompt = "system";

        String requ = "requ";

        List<Plan> plans = Lists.newArrayList();
        Plan plan = new Plan();
        plan.setId(1L);
        plan.setSimilarityCode("code info");
        plan.setFilePath("/code/path");
        plan.setStep("plan step");
        plans.add(plan);

        String prompt = ActionGenCodePrompt.buildCodeGenPrompt(systemPrompt, requ, plans);
        Assert.assertNotNull(prompt);

    }
}