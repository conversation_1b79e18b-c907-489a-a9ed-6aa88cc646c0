data: {"id":"a7149e54-e2da-485a-859c-981c7760aa3d", "pluginIndex":0, "pluginInfo":{"description":"可以根据用户的提问搜索相关信息，并据此得到更准确和丰富的回复","id":1,"name":"知识搜索"}, "finishReason":"SUCCESS", "stageList":["步骤1","步骤2","步骤3","步骤4"],"stageInfo":{"output":{"arguments":"{\n  \"query\":\"变更的三板斧是什么\"}","name":"common_search"}},"type":"functionCall"}

data: {"pluginIndex":0, "stageIndex": 0, "stageInfo":{"input":{"formData":{"jsonSchema":{}, "uiSchema":{}}}}, "type":"api"}

data: {"pluginIndex":0, "stageIndex": 0, "stageInfo":{"input":{"userFill":{}}}, "type":"api"}

data: {"pluginIndex":0, "stageIndex": 0, "stageInfo":{"input":{"requestBody":{"query":"变更的三板斧是什么"}}}, "type":"api"}

data: {"pluginIndex":0, "stageIndex": 0, "stageInfo":{"output":{"responseBody":{"urlMessage":"xxxx","processingTime":3.01168}}},"finishReason":"SUCCESS",  "type":"api"}

data: {"pluginIndex":0, "stageIndex": 1, "stageInfo":{"input":{"prompt":"xxxx"}}, "type":"model"}

data: {"pluginIndex":0, "stageIndex": 1, "stageInfo":{"output":{"llmResult":"变更的三板"}}, "type":"model"}

data: {"pluginIndex":0, "stageIndex": 1, "stageInfo":{"output":{"llmResult":"斧包括可监控、可灰度和"}}, "type":"model"}

data: {"pluginIndex":0, "stageIndex": 1, "stageInfo":{"output":{"llmResult":"可回滚〔1〕。通过控制影响面和快速发现和"}}, "type":"model"}

data: {"pluginIndex":0, "stageIndex": 1, "stageInfo":{"output":{"llmResult":"处理问题，可以降低错误发生的概率〔1〕。在线变更三板斧还需要建"}}, "type":"model"}

data: {"pluginIndex":0, "stageIndex": 1, "stageInfo":{"output":{"llmResult":"设仿真环境和红蓝攻防等能力〔2〕。此外，变更三板斧"}}, "type":"model"}

data: {"pluginIndex":0, "stageIndex": 1, "stageInfo":{"output":{"llmResult":"还涉及到故障处罚条例和变更管理规范〔3〕。"}}, "type":"model", "finishReason":"SUCCESS"}

data: {"pluginIndex":0, "stageIndex": 2, "stageInfo":{"input":{"requestBody":{"preResponse":{"urlMessage":"xxxx","processingTime":0.3065199851989746},"params":{"query":"变更的三板斧是什么"},"llmResult":"变更的三板斧包括可监控、可灰度和可回滚〔1〕。通过控制影响面和快速发现和处理问题，可以降低错误发生的概率〔1〕。在线变更三板斧还需要建设仿真环境和红蓝攻防等能力〔2〕。此外，变更三板斧还涉及到故障处罚条例和变更管理规范〔3〕。"}}}, "type":"api"}

data: {"pluginIndex":0, "stageIndex": 2, "stageInfo":{"output":{"responseBody":{"postAnswer":"xxxx","processingTime":3.01168}}}, "type":"api", "finishReason":"SUCCESS"}

data: {"pluginIndex":0, "stageIndex": 3, "stageInfo":{"input":{"template":"{postResponse.postAnswer}"}},  "type":"template"}

data: {"pluginIndex":0, "stageIndex": 3, "stageInfo":{"output":{"result":"xxxxx"}}, "type":"template", "finishReason":"SUCCESS"}

data: {"type":"answer", "content":"1"}

data: {"type":"answer", "content":"2"}

data: {"type":"answer", "content":"3", "finishReason":"SUCCESS"}