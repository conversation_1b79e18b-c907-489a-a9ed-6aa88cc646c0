package com.alipay.codegencore.web.openapi;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.openai.CodeInfoFile;
import com.alipay.codegencore.model.openai.GenCodeFileRequest;
import com.alipay.codegencore.model.openai.GenPlanRequest;
import com.alipay.codegencore.model.openai.PlanFile;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.ideaevo.ActionGenCodeSearchInfoService;
import com.alipay.codegencore.service.ideaevo.ActionGenCodeService;
import com.alipay.codegencore.service.middle.tbase.TbaseCacheService;
import com.alipay.codegencore.utils.thread.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 16:38
 */
@Slf4j
@RestController
@RequestMapping("/v1/generate")
public class ActionGenCodeController {


    @Autowired
    private ActionGenCodeService actionGenCodeService;

    @Autowired
    private TbaseCacheService tbaseCacheService;

    @Autowired
    private ActionGenCodeSearchInfoService actionGenCodeSearchInfoService;

    /**
     * 生成计划
     * @param modelEnv
     * @param genPlanRequest
     * @return
     */
    @PostMapping("/plan/file")
    public BaseResponse genPlanFile(HttpServletResponse httpServletResponse,
                                    @RequestHeader(name = "modelEnv", defaultValue = "auto") String modelEnv,
                                    @RequestHeader(name = "maxTimeout", defaultValue = "0") Long maxTimeout,
                                    @RequestHeader(name = "searchType", required = false) String searchType,
                                    @RequestHeader(name = "planType", required = false) String planType,
                                    @RequestHeader(name = "planPromptName", required = false) String planPromptName,
                                    @RequestHeader(name = "modelName", required = false) String modelName,
                                    @RequestBody @Validated GenPlanRequest genPlanRequest) {
        log.info("session:{} plan gen evn:{} maxTimeout:{} searchType:{} planType:{} planPromptName:{} modelName:{} req:{} ",
                genPlanRequest.getSessionId(), modelEnv, maxTimeout, searchType, planType, planPromptName, modelName, genPlanRequest);

        Future<List<PlanFile>> planFileFuture = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,
                () -> actionGenCodeService.genPlanFile(genPlanRequest, modelEnv, searchType, planType, modelName, planPromptName));

        return KeepAliveUtils.keepAlive(planFileFuture, maxTimeout, httpServletResponse);
    }

    /**
     * 生成代码
     * @param modelEnv
     * @param genCodeFileRequest
     * @return
     */
    @PostMapping("/code/file")
    public BaseResponse genCodeFile(HttpServletResponse httpServletResponse,
                                    @RequestHeader(name = "modelEnv", defaultValue = "auto") String modelEnv,
                                    @RequestHeader(name = "maxTimeout", defaultValue = "0") Long maxTimeout,
                                    @RequestHeader(name = "modelName", required = false) String modelName,
                                    @RequestHeader(name = "codeType", required = false) String codeType,
                                    @RequestBody @Validated GenCodeFileRequest genCodeFileRequest) {
        log.info("session:{} code gen env:{} maxTimeout:{} modelName:{} codeType:{} req:{}",
                genCodeFileRequest.getSessionId(), modelEnv, maxTimeout, modelName, codeType, genCodeFileRequest);
        Future<CodeInfoFile> codeInfoFileFuture = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,
                () -> actionGenCodeService.genCodeFile(genCodeFileRequest, modelEnv, modelName, codeType));

        return KeepAliveUtils.keepAlive(codeInfoFileFuture, maxTimeout, httpServletResponse);
    }

    /**
     * action gen code
     * @param modelEnv
     * @param genPlanRequest
     * @return
     */
    @PostMapping("/evaluation/file")
    public BaseResponse evaluationFile(HttpServletResponse httpServletResponse,
                                   @RequestHeader(name = "modelEnv", defaultValue = "auto") String modelEnv,
                                   @RequestHeader(name = "maxTimeout", defaultValue = "0") Long maxTimeout,
                                   @RequestHeader(name = "searchType", defaultValue = "V2_ANSWER") String searchType,
                                   @RequestHeader(name = "planType", defaultValue = "PART_GEN") String planType,
                                   @RequestHeader(name = "codeType", required = false) String codeType,
                                   @RequestHeader(name = "planPromptName", required = false) String planPromptName,
                                   @RequestHeader(name = "planModelName", required = false) String planModelName,
                                   @RequestHeader(name = "codeModelName", required = false) String codeModelName,
                                   @RequestHeader(name = "codeModelCount", defaultValue = "5") Integer codeModelCount,
                                   @RequestBody GenPlanRequest genPlanRequest) {
        log.info("session:{} evaluation evn:{} maxTimeout:{} searchType:{} planType:{} codeType:{} planPromptName:{} planModelName:{} codeModelName:{} codeModelCount:{} req:{} ",
                genPlanRequest.getSessionId(), modelEnv, maxTimeout, searchType, planType, codeType, planPromptName, planModelName, codeModelName, codeModelCount, genPlanRequest);
        Future<JSONObject> evaluationInfoFuture = ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,
                () -> actionGenCodeService.evaluationFile(genPlanRequest, modelEnv, searchType, planType,
                        codeType, planModelName, codeModelName, codeModelCount, planPromptName));

        return KeepAliveUtils.keepAlive(evaluationInfoFuture, maxTimeout, httpServletResponse);

    }

    /**
     * 相关代码文件查询
     * @param searchRequest
     * @return
     */
    @PostMapping("/search")
    public BaseResponse search(@RequestBody GenPlanRequest searchRequest) {
        log.info("session:{} search code file. req:{}", searchRequest.getSessionId(), searchRequest);

        if (searchRequest.getRepoInfo() == null
                || StringUtils.isBlank(searchRequest.getRepoInfo().getRepoPath())) {
            return BaseResponse.build(ResponseEnum.SVAT_REPO_INFO_ILL);
        }

        return BaseResponse.build(actionGenCodeService.search(searchRequest));
    }

    /**
     * 获取 sessionId 对应搜索结果数据
     * @param sessionId
     * @return
     */
    @GetMapping("/search/info")
    public BaseResponse searchInfo(String sessionId) {
        log.info("query search info sessionId:{}", sessionId);
        return BaseResponse.build(actionGenCodeSearchInfoService.queryBySessionId(sessionId));
    }

}
