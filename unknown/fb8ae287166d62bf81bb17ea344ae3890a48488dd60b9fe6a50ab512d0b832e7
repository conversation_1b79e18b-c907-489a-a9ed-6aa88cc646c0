package com.alipay.codegencore.model.model.tool.learning.plugin.stage;

/**
 * 插件中调用阶段的信息
 */
public class StageLog {

    // 阶段名称
    private String stageName;

    // 类型
    private String type;

    // 状态
    private boolean status;

    //结束原因
    private String finishReason;

    // 阶段信息
    private StageInfo stageInfo;

    public StageLog() {
        stageInfo = new StageInfo();
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getFinishReason() {
        return finishReason;
    }

    public void setFinishReason(String finishReason) {
        this.finishReason = finishReason;
    }

    public StageInfo getStageInfo() {
        return stageInfo;
    }

    public void setStageInfo(StageInfo stageInfo) {
        this.stageInfo = stageInfo;
    }
}