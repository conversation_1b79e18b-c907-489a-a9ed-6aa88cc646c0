package com.alipay.codegencore.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ChatSessionDOExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public ChatSessionDOExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andUidIsNull() {
            addCriterion("uid is null");
            return (Criteria) this;
        }

        public Criteria andUidIsNotNull() {
            addCriterion("uid is not null");
            return (Criteria) this;
        }

        public Criteria andUidEqualTo(String value) {
            addCriterion("uid =", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotEqualTo(String value) {
            addCriterion("uid <>", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThan(String value) {
            addCriterion("uid >", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThanOrEqualTo(String value) {
            addCriterion("uid >=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThan(String value) {
            addCriterion("uid <", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThanOrEqualTo(String value) {
            addCriterion("uid <=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLike(String value) {
            addCriterion("uid like", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotLike(String value) {
            addCriterion("uid not like", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidIn(List<String> values) {
            addCriterion("uid in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotIn(List<String> values) {
            addCriterion("uid not in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidBetween(String value1, String value2) {
            addCriterion("uid between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotBetween(String value1, String value2) {
            addCriterion("uid not between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Byte value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Byte value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Byte value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Byte value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Byte value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Byte value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Byte> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Byte> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Byte value1, Byte value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Byte value1, Byte value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andPromptIsNull() {
            addCriterion("prompt is null");
            return (Criteria) this;
        }

        public Criteria andPromptIsNotNull() {
            addCriterion("prompt is not null");
            return (Criteria) this;
        }

        public Criteria andPromptEqualTo(String value) {
            addCriterion("prompt =", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptNotEqualTo(String value) {
            addCriterion("prompt <>", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptGreaterThan(String value) {
            addCriterion("prompt >", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptGreaterThanOrEqualTo(String value) {
            addCriterion("prompt >=", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptLessThan(String value) {
            addCriterion("prompt <", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptLessThanOrEqualTo(String value) {
            addCriterion("prompt <=", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptLike(String value) {
            addCriterion("prompt like", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptNotLike(String value) {
            addCriterion("prompt not like", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptIn(List<String> values) {
            addCriterion("prompt in", values, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptNotIn(List<String> values) {
            addCriterion("prompt not in", values, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptBetween(String value1, String value2) {
            addCriterion("prompt between", value1, value2, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptNotBetween(String value1, String value2) {
            addCriterion("prompt not between", value1, value2, "prompt");
            return (Criteria) this;
        }

        public Criteria andModelConfigIsNull() {
            addCriterion("model_config is null");
            return (Criteria) this;
        }

        public Criteria andModelConfigIsNotNull() {
            addCriterion("model_config is not null");
            return (Criteria) this;
        }

        public Criteria andModelConfigEqualTo(String value) {
            addCriterion("model_config =", value, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigNotEqualTo(String value) {
            addCriterion("model_config <>", value, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigGreaterThan(String value) {
            addCriterion("model_config >", value, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigGreaterThanOrEqualTo(String value) {
            addCriterion("model_config >=", value, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigLessThan(String value) {
            addCriterion("model_config <", value, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigLessThanOrEqualTo(String value) {
            addCriterion("model_config <=", value, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigLike(String value) {
            addCriterion("model_config like", value, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigNotLike(String value) {
            addCriterion("model_config not like", value, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigIn(List<String> values) {
            addCriterion("model_config in", values, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigNotIn(List<String> values) {
            addCriterion("model_config not in", values, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigBetween(String value1, String value2) {
            addCriterion("model_config between", value1, value2, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andModelConfigNotBetween(String value1, String value2) {
            addCriterion("model_config not between", value1, value2, "modelConfig");
            return (Criteria) this;
        }

        public Criteria andSceneIdIsNull() {
            addCriterion("scene_id is null");
            return (Criteria) this;
        }

        public Criteria andSceneIdIsNotNull() {
            addCriterion("scene_id is not null");
            return (Criteria) this;
        }

        public Criteria andSceneIdEqualTo(Long value) {
            addCriterion("scene_id =", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotEqualTo(Long value) {
            addCriterion("scene_id <>", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThan(Long value) {
            addCriterion("scene_id >", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThanOrEqualTo(Long value) {
            addCriterion("scene_id >=", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThan(Long value) {
            addCriterion("scene_id <", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThanOrEqualTo(Long value) {
            addCriterion("scene_id <=", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdIn(List<Long> values) {
            addCriterion("scene_id in", values, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotIn(List<Long> values) {
            addCriterion("scene_id not in", values, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdBetween(Long value1, Long value2) {
            addCriterion("scene_id between", value1, value2, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotBetween(Long value1, Long value2) {
            addCriterion("scene_id not between", value1, value2, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneTestIsNull() {
            addCriterion("scene_test is null");
            return (Criteria) this;
        }

        public Criteria andSceneTestIsNotNull() {
            addCriterion("scene_test is not null");
            return (Criteria) this;
        }

        public Criteria andSceneTestEqualTo(Boolean value) {
            addCriterion("scene_test =", value, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestNotEqualTo(Boolean value) {
            addCriterion("scene_test <>", value, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestGreaterThan(Boolean value) {
            addCriterion("scene_test >", value, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestGreaterThanOrEqualTo(Boolean value) {
            addCriterion("scene_test >=", value, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestLessThan(Boolean value) {
            addCriterion("scene_test <", value, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestLessThanOrEqualTo(Boolean value) {
            addCriterion("scene_test <=", value, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestIn(List<Boolean> values) {
            addCriterion("scene_test in", values, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestNotIn(List<Boolean> values) {
            addCriterion("scene_test not in", values, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestBetween(Boolean value1, Boolean value2) {
            addCriterion("scene_test between", value1, value2, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andSceneTestNotBetween(Boolean value1, Boolean value2) {
            addCriterion("scene_test not between", value1, value2, "sceneTest");
            return (Criteria) this;
        }

        public Criteria andOssAddressListIsNull() {
            addCriterion("oss_address_list is null");
            return (Criteria) this;
        }

        public Criteria andOssAddressListIsNotNull() {
            addCriterion("oss_address_list is not null");
            return (Criteria) this;
        }

        public Criteria andOssAddressListEqualTo(String value) {
            addCriterion("oss_address_list =", value, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListNotEqualTo(String value) {
            addCriterion("oss_address_list <>", value, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListGreaterThan(String value) {
            addCriterion("oss_address_list >", value, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListGreaterThanOrEqualTo(String value) {
            addCriterion("oss_address_list >=", value, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListLessThan(String value) {
            addCriterion("oss_address_list <", value, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListLessThanOrEqualTo(String value) {
            addCriterion("oss_address_list <=", value, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListLike(String value) {
            addCriterion("oss_address_list like", value, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListNotLike(String value) {
            addCriterion("oss_address_list not like", value, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListIn(List<String> values) {
            addCriterion("oss_address_list in", values, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListNotIn(List<String> values) {
            addCriterion("oss_address_list not in", values, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListBetween(String value1, String value2) {
            addCriterion("oss_address_list between", value1, value2, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andOssAddressListNotBetween(String value1, String value2) {
            addCriterion("oss_address_list not between", value1, value2, "ossAddressList");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformIsNull() {
            addCriterion("source_platform is null");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformIsNotNull() {
            addCriterion("source_platform is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformEqualTo(String value) {
            addCriterion("source_platform =", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformNotEqualTo(String value) {
            addCriterion("source_platform <>", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformGreaterThan(String value) {
            addCriterion("source_platform >", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformGreaterThanOrEqualTo(String value) {
            addCriterion("source_platform >=", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformLessThan(String value) {
            addCriterion("source_platform <", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformLessThanOrEqualTo(String value) {
            addCriterion("source_platform <=", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformLike(String value) {
            addCriterion("source_platform like", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformNotLike(String value) {
            addCriterion("source_platform not like", value, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformIn(List<String> values) {
            addCriterion("source_platform in", values, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformNotIn(List<String> values) {
            addCriterion("source_platform not in", values, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformBetween(String value1, String value2) {
            addCriterion("source_platform between", value1, value2, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andSourcePlatformNotBetween(String value1, String value2) {
            addCriterion("source_platform not between", value1, value2, "sourcePlatform");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListIsNull() {
            addCriterion("document_uid_list is null");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListIsNotNull() {
            addCriterion("document_uid_list is not null");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListEqualTo(String value) {
            addCriterion("document_uid_list =", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListNotEqualTo(String value) {
            addCriterion("document_uid_list <>", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListGreaterThan(String value) {
            addCriterion("document_uid_list >", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListGreaterThanOrEqualTo(String value) {
            addCriterion("document_uid_list >=", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListLessThan(String value) {
            addCriterion("document_uid_list <", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListLessThanOrEqualTo(String value) {
            addCriterion("document_uid_list <=", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListLike(String value) {
            addCriterion("document_uid_list like", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListNotLike(String value) {
            addCriterion("document_uid_list not like", value, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListIn(List<String> values) {
            addCriterion("document_uid_list in", values, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListNotIn(List<String> values) {
            addCriterion("document_uid_list not in", values, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListBetween(String value1, String value2) {
            addCriterion("document_uid_list between", value1, value2, "documentUidList");
            return (Criteria) this;
        }

        public Criteria andDocumentUidListNotBetween(String value1, String value2) {
            addCriterion("document_uid_list not between", value1, value2, "documentUidList");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_chat_session
     *
     * @mbg.generated do_not_delete_during_merge Thu Jan 04 10:32:25 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table cg_chat_session
     *
     * @mbg.generated Thu Jan 04 10:32:25 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}