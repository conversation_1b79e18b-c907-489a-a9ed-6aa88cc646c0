package com.alipay.codegencore.model.enums.tool.learning;

/**
 * 插件类型
 */
public enum PluginTypeEnum {
    /**
     * 流水线插件类型
     */
    PIPELINE("pipeline"),
    /**
     * API插件类型
     */
    API("api");

    private String name;

    PluginTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

