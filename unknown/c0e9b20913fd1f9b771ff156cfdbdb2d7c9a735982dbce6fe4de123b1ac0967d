package com.alipay.codegencore.service.common;

import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.UserStatusEnum;
import com.alipay.codegencore.model.model.codegpt.CodeGptUserModelItemModel;
import com.alipay.codegencore.model.openai.SceneVO;

import java.util.List;

/**
 * 用户鉴权控制服务
 * <AUTHOR>
 */
public interface UserAclService {
    /**
     * 判断是否是管理员
     * 只有在controller层才能调用
     * @return 是否是管理员
     */
    boolean isAdmin();

    /**
     * 分配管理员权限
     * @param empId 工号
     * @param cancelAdmin 是否取消管理员权限
     * @return 是否分配成功
     */
    boolean assignAdmin(String empId, boolean cancelAdmin, byte adminType);

    /**
     * 根据token判断是否鉴权通过
     * @param user 用户
     * @param token token
     * @param uri 要访问的uri
     * @return
     */
    boolean isAuthorizedByToken(String user, String token, String uri);

    /**
     * 获取用户id
     * @return
     */
    UserAuthDO getCurrentUser();

    /**
     * 会话是否属于用户
     * @param uid 会话id
     * @return
     */
    boolean isSessionBelongToUser(String uid);

    /**
     * 会话是否属于用户
     * @param uidList 会话id
     * @return
     */
    boolean isSessionBelongToUser(List<String> uidList);

    /**
     * chat_message是否属于用户
     * @param uid 消息id
     * @return
     */
    boolean isMsgBelongToUser(String uid);

    /**
     * 根据empID查询用户信息
     * @param empId
     * @return
     */
    UserAuthDO queryUserByEmpId(String empId);

    /**
     * 根据userToken查询用户信息
     *
     * @param userToken
     * @return
     */
    UserAuthDO queryUserByToken(String userToken);

    /**
     * 根据codeGPTUser查询token负责人
     * @param codeGPTUser
     * @return
     */
    TokenDO queryTokenOwner(String codeGPTUser);

    /**
     * 保存一个新用户
     * @param userName
     * @param empId
     * @param allowAccessType
     */
    UserAuthDO saveNewUser(String userName,String empId, Integer allowAccessType);

    /**
     * 按状态查询用户数量
     * @param status
     * @return
     */
    long queryUserCountByStatus(UserStatusEnum status);

    /**
     * 获取用户有权限的模型
     * @return
     */
    List<CodeGptUserModelItemModel> getUserModelInfo();

    /**
     * 判断用户是否有指定模型的使用权限
     * @param model
     * @return
     */
    boolean userHasModelAuthorized(String model);

    /**
     * 根据手机号查询用户信息
     * @param phoneNumber
     * @return
     */
    List<UserAuthDO> getUserAuthDOByPhoneNumber(String phoneNumber);

    /**
     * 通知其他产品用户登录
     * @param param
     */
    void notifyOtherProduce(String param);

    /**
     * 用户收藏场景助手
     *
     * @param sceneId 场景助手 id
     * @param cancel 取消收藏
     * @return
     */
    Boolean userSaveScene(Long sceneId,Boolean cancel);


    /**
     * 用户的会话置顶
     * @param sessionUid
     */
    boolean topSession(String sessionUid, Boolean cancel);

    /**
     * 清除当前登陆用户所有数据
     * @param userId
     */
    void clearCurrentUserAllData();

    /**
     * 创建助手
     * @param scene
     * @param userAuthDO
     */
    void createScene(SceneVO scene, UserAuthDO userAuthDO);

    /**
     * 更新助手
     * @param scene
     * @param userAuthDO
     */
    void updateScene(SceneVO scene, UserAuthDO userAuthDO);

    /**
     * 根据自增ID查询用户
     * @param userId
     * @return
     */
    UserAuthDO selectByUserId(Long userId);

    /**
     * 更新场景权限状态
     *
     * @param scene      场景对象
     * @param userAuthDO 用户权限对象
     */
    void updateSceneAuthStatus(SceneVO scene, UserAuthDO userAuthDO);

}
