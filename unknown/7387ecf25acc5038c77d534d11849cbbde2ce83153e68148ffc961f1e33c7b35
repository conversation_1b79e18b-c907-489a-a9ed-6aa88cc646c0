package com.alipay.codegencore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.ChatMessageDOExample;
import com.alipay.codegencore.dal.example.ChatSessionDOExample;
import com.alipay.codegencore.dal.mapper.ChatMessageDOMapper;
import com.alipay.codegencore.dal.mapper.ChatSessionDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.SegmentationStrategyTypeEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.AlgoBackendModel;
import com.alipay.codegencore.model.model.EmbeddingResponseModel;
import com.alipay.codegencore.model.model.FormUploadFileResponse;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.model.SessionFileResponse;
import com.alipay.codegencore.model.openai.ChatRequestExtData;
import com.alipay.codegencore.model.openai.SceneConfigVO;
import com.alipay.codegencore.model.request.ZarkEmbeddingRequestBean;
import com.alipay.codegencore.model.util.AlgoBackendFieldUtil;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.CheckService;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.OssService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.common.ZarkService;
import com.alipay.codegencore.service.common.segment.SegmentationStrategyFactory;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.SessionUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.service.utils.TbaseCacheService;
import com.alipay.codegencore.utils.file.FileProcessUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 会话管理服务实现
 *
 * <AUTHOR>
 */
@Service
public class ChatSessionManageServiceImpl implements ChatSessionManageService {

    private static final Logger LOGGER = LoggerFactory.getLogger( ChatSessionManageServiceImpl.class );

    private static final String ORDER_BY_PREFIX = "gmt_create desc limit ";

    @Resource
    private ChatSessionDOMapper chatSessionDOMapper;

    @Resource
    private ChatMessageDOMapper chatMessageDOMapper;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private CheckService checkService;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private TbaseCacheService tbaseCacheService;

    @Resource
    private SceneService sceneService;

    @Resource
    private OssService ossService;

    @Resource
    private TokenService tokenService;

    @Resource
    private ExecutorService appThreadPool;

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private UserAclService userAclService;

    @Resource
    private ZarkService zarkService;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private DocumentHandleService documentHandleService;

    @Resource
    private SecAgentHelper secAgentHelper;

    @Resource
    private SegmentationStrategyFactory segmentationStrategyFactory;

    /**
     * 创建会话
     *
     * @param userId 用户id
     * @param prompt 提示语，用于约束模型表现
     */
    @Override
    public ChatSessionDO getNewSession(Long userId, String prompt, String title, Long sceneId, Boolean modelTest, String sourcePlatform,
                                    Long modelId,boolean forceCreate) {
        boolean lock = tbaseCacheService.getLock(AppConstants.CODEGENCORE_NEW_SESSION_LOCK + userId, 5 * 1000);
        if (!lock) {
            throw new BizException(ResponseEnum.REPEAT_QUESTIONS);
        }
        try {
            ChatSessionDO chatSessionDO = new ChatSessionDO();
            chatSessionDO.setUserId(userId);
            String model;
            boolean isNewSession = true;
            ChatSessionDO firstNotTopSession = this.getFirstNotTopSession();
            // 如果是模型调试 不走助手逻辑
            if (!modelTest) {
                // 没有指定场景id设置默认场景
                SceneDO scene;
                if (sceneId == null) {
                    scene = sceneService.getDefaultScene();
                }
                else {
                    scene = sceneService.getSceneById(sceneId);
                }
                if (Objects.isNull(scene)) {
                    throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
                }
                if (scene.getEnable() == 0) {
                    throw new BizException(ResponseEnum.SCENE_NOT_ENABLE);
                }
                chatSessionDO.setSceneId(scene.getId());
                model = scene.getModel();
                if (StrUtil.isBlank(model)) {
                    AlgoBackendDO defaultAlgoBackendDO = algoBackendService.getDefaultAlgoBackend();
                    model = defaultAlgoBackendDO.getModel();
                }
                // 集成agentSecSdk.threadCreate, 判断用户是否有agent使用权限, 根据agentSecSdk.threadCreate返回结果作相应处理
                chatSessionDO.setSourcePlatform(sourcePlatform);
                secAgentHelper.createAgentSessionCheck(chatSessionDO);

                // 非置顶回话为空
                if (!forceCreate && firstNotTopSession != null && firstNotTopSession.getSceneId() != null
                        && firstNotTopSession.getSceneId().longValue() == scene.getId().longValue()) {
                    //不为空但助手id为与上一个助手id相同 再判断会话里是否有会话内容
                    List<ChatMessageDO> chatMessageDOS = chatMessageService.listChatMessage(firstNotTopSession.getUid(), false, false);
                    LOGGER.info("getNewSession chatMessageDOS {}", JSONObject.toJSONString(chatMessageDOS));
                    if (CollectionUtil.isEmpty(chatMessageDOS)) {
                        isNewSession = false;
                    }
                }
            }
            else {
                AlgoBackendModel algoBackendModel = algoBackendService.selectByPrimaryKey(modelId);
                model = algoBackendModel.getModel();
            }
            LOGGER.info("getNewSession  isNewSession{} chatMessageDOS {}", isNewSession, JSONObject.toJSONString(firstNotTopSession));
            chatSessionDO.setModel(isNewSession ? model : firstNotTopSession.getModel());
            chatSessionDO.setPrompt(prompt);
            chatSessionDO.setTitle(Objects.requireNonNullElse(title, AppConstants.SESSION_DEFAULT_TITLE));
            chatSessionDO.setUid(isNewSession ? ShortUid.getUid() : firstNotTopSession.getUid());
            chatSessionDO.setSceneTest(modelTest);
            chatSessionDO.setSourcePlatform(sourcePlatform);
            if (isNewSession) {
                chatSessionDOMapper.insertSelective(chatSessionDO);
            }
            return chatSessionDO;
        } finally {
            tbaseCacheService.releaseLock(AppConstants.CODEGENCORE_NEW_SESSION_LOCK + userId);
        }
    }


    /**
     * 获取会话
     *
     * @param sessionUId 会话uid
     */
    @Override
    public ChatSessionDO getChatSession(String sessionUId) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidEqualTo(sessionUId);

        List<ChatSessionDO> resultList = chatSessionDOMapper.selectByExample(chatSessionDOExample);
        if (resultList.size() == 1) {
            return resultList.get(0);
        }
        return null;
    }

    /**
     * 获取会话列表
     *
     * @param userId 用户id
     * @return 会话列表
     */
    @Override
    public List<ChatSessionDO> listChatSession(Long userId) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUserIdEqualTo(userId).andDeletedEqualTo((byte) 0).andSceneTestNotEqualTo(true);
        String orderByStr = ORDER_BY_PREFIX + codeGPTDrmConfig.getSessionListMaxSize();
        chatSessionDOExample.setOrderByClause(orderByStr);
        return chatSessionDOMapper.selectByExample(chatSessionDOExample);
    }

    /**
     * 更新会话标题
     *
     * @param sessionUid session uid
     * @param newTitle   新标题
     */
    @Override
    public int updateSessionTitle(String sessionUid, String newTitle) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidEqualTo(sessionUid).andDeletedEqualTo((byte)0);

        ChatSessionDO record = new ChatSessionDO();
        record.setTitle(newTitle);

        return chatSessionDOMapper.updateByExampleSelective(record, chatSessionDOExample);
    }

    /**
     * 更新会话使用的模型
     * 只会在会话中没有消息的时候才可以更新成功
     *
     * @param sessionUid session uid
     * @param modelName  模型类型
     * @return 更新成功的条数
     */
    @Override
    public int updateSessionModelType(String sessionUid, String modelName) {
        // 更新模型需要对会话加锁,防止正在回复的过程中尚未落库时更新数据库
        boolean lock = tbaseCacheService.getLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid, 5 * 1000);
        if (!lock) {
            throw new BizException(ResponseEnum.REPEAT_QUESTIONS);
        }
        try {
            ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
            chatMessageDOExample.createCriteria().andSessionUidEqualTo(sessionUid).andDeletedEqualTo((byte) 0);

            List<ChatMessageDO> chatMessageDOList = chatMessageDOMapper.selectByExample(chatMessageDOExample);
            if (!chatMessageDOList.isEmpty()) {
                LOGGER.info("sessionUid:{} has message, can not update modelType", sessionUid);
                throw new BizException(ResponseEnum.SESSION_HAS_MESSAGE_CAN_NOT_UPDATE_MODEL);
            }
            ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
            chatSessionDOExample.createCriteria().andUidEqualTo(sessionUid).andDeletedEqualTo((byte) 0);

            ChatSessionDO record = new ChatSessionDO();
            record.setModel(modelName);
            record.setModelConfig("");

            return chatSessionDOMapper.updateByExampleSelective(record, chatSessionDOExample);
        } finally {
            tbaseCacheService.releaseLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid);
        }
    }

    /**
     * 删除会话
     *
     * @param sessionUidList 会话uid
     */
    @Override
    public void deleteSession(List<String> sessionUidList) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidIn(sessionUidList);

        ChatSessionDO record = new ChatSessionDO();
        record.setDeleted((byte) 1);

        chatSessionDOMapper.updateByExampleSelective(record, chatSessionDOExample);
    }

    /**
     * 全部清除会话
     *
     * @param userId 用户id
     */
    @Override
    public void clearSession(Long userId) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUserIdEqualTo(userId).andDeletedEqualTo((byte) 0);

        ChatSessionDO record = new ChatSessionDO();
        record.setDeleted((byte) 1);

        chatSessionDOMapper.updateByExampleSelective(record, chatSessionDOExample);
    }

    /**
     * 事务性的插入用户提问和AI回答
     * @param queryMessage 用户提问
     * @param answerMessage AI回答
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertPairChatMessage(ChatMessageDO queryMessage, ChatMessageDO answerMessage) {
        chatMessageDOMapper.insertSelective(queryMessage);
        chatMessageDOMapper.insertSelective(answerMessage);
    }

    @Override
    public boolean checkUserUpdateTitle(UserAuthDO currentUser, String sessionUid, String newTitle) {
        List<ChatSessionDO> chatSessionDOList = listChatSession(currentUser.getId());
        StringBuilder sb = new StringBuilder();
        for (ChatSessionDO chatSessionDO : chatSessionDOList) {
            if (chatSessionDO.getUid().equals(sessionUid)) {
                sb.append(newTitle);
            } else {
                sb.append(chatSessionDO.getTitle());
            }
            sb.append("#");
        }
        sb.replace(sb.length() - 1, sb.length(), "");
        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
        chatRequestExtData.setCodeGPTUser(AppConstants.CODEGPT_TOKEN_USER);
        chatRequestExtData.setEmpId(currentUser.getEmpId());
        chatRequestExtData.setContentIsTitle(true);
        ReviewResultModel reviewResultModel = checkService.getInfoSecCheckRet(sb.toString(), ChatRoleEnum.ASSISTANT, 1, chatRequestExtData, sessionUid, sessionUid);
        return reviewResultModel.isRet();
    }

    /**
     * 更新回话的模型配置
     * 只会在会话中没有消息的时候才可以更新成功
     *
     * @param sessionUid
     * @param requestBean
     * @return
     */
    @Override
    public int updateSessionModelConfig(String sessionUid, JSONObject requestBean) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if (currentUser == null) {
            throw new BizException(ResponseEnum.NO_AUTH,"未登录");
        }
        if (requestBean == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidEqualTo(sessionUid).andDeletedEqualTo((byte) 0);
        List<ChatSessionDO> chatSessionDOS = chatSessionDOMapper.selectByExample(chatSessionDOExample);
        ChatSessionDO chatSessionDO = null;
        if (CollectionUtil.isEmpty(chatSessionDOS)){
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        chatSessionDO = chatSessionDOS.get(0);
        if(chatSessionDO.getUserId().longValue() != currentUser.getId().longValue()){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        if (!chatSessionDO.getSceneTest()){
            ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
            chatMessageDOExample.createCriteria().andSessionUidEqualTo(sessionUid).andDeletedEqualTo((byte) 0);
            List<ChatMessageDO> chatMessageDOList = chatMessageDOMapper.selectByExample(chatMessageDOExample);
            if (!chatMessageDOList.isEmpty()) {
                LOGGER.info("sessionUid:{} has message, can not update model config.", sessionUid);
                throw new BizException(ResponseEnum.SESSION_HAS_MESSAGE_CAN_NOT_UPDATE_MODEL);
            }
        }
        SceneDO scene = new SceneDO();
        if (!chatSessionDO.getSceneTest()) {
            scene = sceneService.getSceneById(chatSessionDO.getSceneId());
            if (scene == null) {
                throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
            }
            if (StringUtils.isEmpty(scene.getModel())) {
                throw new BizException(ResponseEnum.MODEL_NOT_EXISTED);
            }

        }
        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(chatSessionDO.getModel());
        if (algoBackendDO == null) {
            throw new BizException(ResponseEnum.MODEL_NOT_EXISTED);
        }
        if (!algoBackendDO.getEnable() && !chatSessionDO.getSceneTest()) {
            throw new BizException(ResponseEnum.MODEL_UNSERVICEABLE);
        }
        JSONObject newModelConfig = getNewModelConfig(chatSessionDO, algoBackendDO, requestBean, sessionUid, scene);
        ChatSessionDO newChatSessionDO = new ChatSessionDO();
        newChatSessionDO.setModelConfig(JSON.toJSONString(newModelConfig));
        LOGGER.info("updateSceneConfig ,sessionUid:{} requestBean:{}, newChatSessionDO:{},algoBackendDO:{}", sessionUid, requestBean,
                JSON.toJSONString(newModelConfig), JSONObject.toJSONString(algoBackendDO));
        return chatSessionDOMapper.updateByExampleSelective(newChatSessionDO, chatSessionDOExample);
    }


    private JSONObject getNewModelConfig(ChatSessionDO chatSessionDO, AlgoBackendDO algoBackendDO, JSONObject requestJson, String sessionUid, SceneDO scene) {
        //session表存储的配置
        JSONObject modelConfig = JSONObject.parseObject(chatSessionDO.getModelConfig());
        if (CollectionUtil.isEmpty(modelConfig)) {
            modelConfig = new JSONObject();
            JSONObject algoImplConfig = JSONObject.parseObject(algoBackendDO.getImplConfig());
            if (StringUtils.isNotBlank(scene.getSystemPrompt())){
                algoImplConfig.put("systemPrompt", scene.getSystemPrompt());
            }
            JSONObject newSessionImplConfig = new JSONObject();
            if (requestJson.getInteger(AppConstants.MAX_TOKEN) != null && algoBackendDO.getMaxToken().intValue() != requestJson.getInteger(
                    AppConstants.MAX_TOKEN).intValue()) {
                modelConfig.put(AppConstants.MAX_TOKEN, requestJson.get(AppConstants.MAX_TOKEN));
            }
            if (requestJson.getInteger(AppConstants.MAX_ROUND) != null && algoBackendDO.getMaxRound().intValue() != requestJson.getInteger(
                    AppConstants.MAX_ROUND).intValue()) {
                modelConfig.put(AppConstants.MAX_ROUND, requestJson.get(AppConstants.MAX_ROUND));
            }
            for (Map.Entry<String,Object> entry : algoImplConfig.entrySet()) {
                Object sessionConfigValue = requestJson.get(entry.getKey());
                Object value = entry.getValue();
                if (sessionConfigValue != null) {

                    if (!(sessionConfigValue instanceof String)) {
                        sessionConfigValue = sessionConfigValue + "";
                    }
                    if (!(entry.getValue() instanceof String)) {
                        value = entry.getValue() + "";
                    }
                    if (!StrUtil.equals((String) sessionConfigValue, (String) value)) {
                        newSessionImplConfig.put(entry.getKey(), sessionConfigValue);
                    }
                }
            }
            //如果选择的环境不是默认环境，则保存
            String modelEnv = requestJson.getString("modelEnv");
            if (!algoImplConfig.containsKey("modelEnv") && !AlgoBackendFieldUtil.defaultEnv.equals(modelEnv)){
                newSessionImplConfig.put("modelEnv",requestJson.get("modelEnv"));
            }
            modelConfig.put(AppConstants.IMPL_CONFIG, newSessionImplConfig);
        } else {
            // 现在是没有修改之前但是已经覆盖过的配置
            List<SceneConfigVO> sceneConfig = getSessionModelConfig(sessionUid, scene.getId(), false);
            // session存储的implConfig
            JSONObject modelImplConfig = JSONObject.parseObject(JSON.toJSONString(modelConfig.get(AppConstants.IMPL_CONFIG)));
            if (ObjectUtil.isEmpty(modelImplConfig)) {
                modelImplConfig = new JSONObject();
            }
            for (SceneConfigVO vo : sceneConfig){
                if (requestJson.containsKey(vo.getKey()) && !ObjectUtil.equals(vo.getValue(), requestJson.get(vo.getKey()))) {
                    // 有不一致 新增session表存储的model配置
                    if (AppConstants.MAX_TOKEN.equals(vo.getKey()) || AppConstants.MAX_ROUND.equals(vo.getKey())){
                        modelConfig.put(vo.getKey(), requestJson.get(vo.getKey()));
                    }else {
                        modelImplConfig.put(vo.getKey(), requestJson.get(vo.getKey()));
                    }
                }
            }
            modelConfig.put(AppConstants.IMPL_CONFIG, modelImplConfig);
        }
        return modelConfig;
    }

    /**
     * 获取回话级别模型配置
     *
     * @param sessionUid
     * @return
     */
    @Override
    public JSONObject getSessionConfig(String sessionUid,Long sceneId) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidEqualTo(sessionUid).andDeletedEqualTo((byte) 0);
        ChatSessionDO chatSessionDO = chatSessionDOMapper.selectByExample(chatSessionDOExample).get(0);

        SceneDO scene = null;
        if (chatSessionDO.getSceneId() != null) {
            // 兼容前端 没有修改会话的助手id时查询的会话配置
            if (sceneId != null) {
                chatSessionDO.setSceneId(sceneId);
            }
            scene = sceneService.getSceneById(chatSessionDO.getSceneId());
        }

        AlgoBackendDO algoBackendDO;
        if (scene != null && scene.getMode() == 0){
            algoBackendDO = algoBackendService.getAlgoBackendByName(scene.getModel());
        }else{
            algoBackendDO = algoBackendService.getAlgoBackendByName(chatSessionDO.getModel());
        }

        if (algoBackendDO == null) {
            throw new BizException(ResponseEnum.MODEL_NOT_EXISTED);
        }
        if (!algoBackendDO.getEnable()&& !chatSessionDO.getSceneTest()) {
            throw new BizException(ResponseEnum.MODEL_UNSERVICEABLE);
        }
        JSONObject algoImplConfig = JSONObject.parseObject(algoBackendDO.getImplConfig());
        JSONObject result = new JSONObject(true);
        result.put(AppConstants.MAX_TOKEN, algoBackendDO.getMaxToken());
        result.put(AppConstants.MAX_ROUND, algoBackendDO.getMaxRound());
        result.put(AppConstants.IMPL_CONFIG,algoImplConfig);
        // 如果会话模型配置为空返回algobackend模型配置
        JSONObject sessionModelConfig = JSONObject.parseObject(chatSessionDO.getModelConfig());
        if (CollectionUtil.isEmpty(sessionModelConfig)) {
            return result;
        }
        if (!ObjectUtil.isEmpty(sessionModelConfig.get(AppConstants.MAX_TOKEN))){
            result.put(AppConstants.MAX_TOKEN,sessionModelConfig.get(AppConstants.MAX_TOKEN));
        }
        if (!ObjectUtil.isEmpty(sessionModelConfig.get(AppConstants.MAX_ROUND))){
            result.put(AppConstants.MAX_ROUND,sessionModelConfig.get(AppConstants.MAX_ROUND));
        }
        if (!ObjectUtil.isEmpty(sessionModelConfig.get(AppConstants.IMPL_CONFIG))){
            JSONObject sessionModelConfigImpl = JSONObject.parseObject(JSON.toJSONString(sessionModelConfig.get(AppConstants.IMPL_CONFIG)));
            JSONObject resultImplConfig = JSONObject.parseObject(JSON.toJSONString(result.get(AppConstants.IMPL_CONFIG)));
            resultImplConfig.putAll(sessionModelConfigImpl);
            result.put(AppConstants.IMPL_CONFIG,resultImplConfig);
        }
        return result;
    }

    /**
     * 获取指定会话模型配置
     *
     * @param sessionUid
     * @param sceneId
     * @return
     */
    @Override
    public List<SceneConfigVO> getSessionModelConfig(String sessionUid, Long sceneId, Boolean defaultConfig) {
        if (StringUtils.isEmpty(sessionUid)) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        ChatSessionDO chatSessionDO = this.getChatSession(sessionUid);
        if (chatSessionDO == null){
            throw new BizException(ResponseEnum.SESSION_IS_NOT_EXIST);
        }
        SceneDO scene = null;
        AlgoBackendDO algoBackendDO;
        if (!chatSessionDO.getSceneTest()) {
            scene = sceneService.getSceneById(sceneId);
            if (scene == null) {
                throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
            }
            if (StringUtils.isEmpty(scene.getModel())) {
                throw new BizException(ResponseEnum.MODEL_NOT_EXISTED);
            }
            algoBackendDO = algoBackendService.getAlgoBackendByName(scene.getModel());
        }
        else {
            algoBackendDO = algoBackendService.getAlgoBackendByName(chatSessionDO.getModel());
        }

        if (algoBackendDO == null) {
            throw new BizException(ResponseEnum.MODEL_NOT_EXISTED);
        }
        //读取原始配置
        if (defaultConfig){
            chatSessionDO.setModelConfig(null);
        }
        String systemPrompt = null;
        if (scene != null){
            systemPrompt = scene.getSystemPrompt();
        }
        LOGGER.info("getSceneConfig algoBackendDO:{},chatSessionDO:{}",JSONObject.toJSONString(algoBackendDO),JSONObject.toJSONString(chatSessionDO));
        return AlgoBackendFieldUtil.sceneConfigVOList(algoBackendDO, chatSessionDO.getModelConfig(), systemPrompt);
    }

    /**
     * 修改会话信息
     *
     * @param chatSessionDO
     * @return
     */
    @Override
    public Boolean updateSession(ChatSessionDO chatSessionDO) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidEqualTo(chatSessionDO.getUid());
        return  chatSessionDOMapper.updateByExampleSelective(chatSessionDO, chatSessionDOExample) == 1;
    }

    @Override
    public SessionFileResponse uploadSessionFile(String sessionUid, MultipartFile file) {
        String fileUid = ShortUid.getUid();
        LOGGER.info("uploadSessionFile,开始上传文件,sessionUid:{},fileUid:{},name:{},type:{},orgFileName:{},size:{}", sessionUid, fileUid, file.getName(), file.getContentType(), file.getOriginalFilename(), file.getSize());
        try {
            // 判断文件格式和大小
            JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
            List<String> supportFileSuffixes = documentChatConfig.getJSONArray("supportFileSuffixes").toJavaList(String.class);
            Long fileMaxSize = documentChatConfig.getLong("fileMaxSize");
            Integer maxSumPartSize = documentChatConfig.getInteger("maxSumPartSize");
            Integer fileSaveTbaseMaxSeconds = documentChatConfig.getInteger("fileSaveTbaseMaxSeconds");
            String fileExtension = org.springframework.util.StringUtils.getFilenameExtension(file.getOriginalFilename());
            if (!supportFileSuffixes.contains(fileExtension)) {
                throw new BizException(ResponseEnum.FILE_TYPE_NOT_SUPPORTED);
            }
            if (file.getSize() > fileMaxSize) {
                throw new BizException(ResponseEnum.FILE_TOO_BIG, ResponseEnum.FILE_TOO_BIG.getErrorMsg() + ",不能超过:" + (fileMaxSize / 1024 / 1024) + "MB");
            }
            // 获取文件的文本内容
            String fileContent = FileProcessUtils.getFileContent(file);
            if (StringUtils.isBlank(fileContent) || StringUtils.isBlank(fileContent.replace("\n", ""))) {
                throw new BizException(ResponseEnum.UPLOAD_FILE_FAILED, "您上传的文档里未解析出文字内容，请上传含有文本内容的文件");
            }
            // 文件审查(包括文件名也要审核)
            ReviewResultModel fileCheckResult = checkContent(file.getOriginalFilename() + fileContent, sessionUid, "file", ChatRoleEnum.USER);
            LOGGER.info("uploadSessionFile,文件审核完成,sessionUid:{},fileUid:{},reviewResultModel:{}", sessionUid, fileUid, JSON.toJSONString(fileCheckResult));
            if (!fileCheckResult.isRet()) {
                throw new BizException(ResponseEnum.CHECK_FAILED);
            }
            // 文件内容拆分，对话上传文件先都按照默认策略。
            List<String> splitFileList = segmentationStrategyFactory.getSegmentationStrategy(SegmentationStrategyTypeEnum.DELIMITER_STRATEGY).segment(fileContent, documentChatConfig);
            List<Integer> lengthList = splitFileList.stream()
                    .map(String::length)
                    .collect(Collectors.toList());
            LOGGER.info("uploadSessionFile,文件拆分成功,sessionUid:{},fileUid:{},size:{},partLength:{}", sessionUid, fileUid, splitFileList.size(), JSON.toJSONString(lengthList));
            if (splitFileList.size() > maxSumPartSize) {
                throw new BizException(ResponseEnum.FILE_CONTENT_TOO_MANY);
            }
            // 对每一段进行embedding
            List<EmbeddingResponseModel> embeddingResponseModelList = embeddingStrList(fileUid, splitFileList, documentChatConfig);
            LOGGER.info("uploadSessionFile,段落embedding成功,sessionUid:{},fileUid:{}", sessionUid, fileUid);
            String fileSummary = getFileSummary(sessionUid, splitFileList, documentChatConfig);
            // 审查summary结果
            ReviewResultModel fileSummaryCheckResult = checkContent(fileSummary, sessionUid, "fileSummary", ChatRoleEnum.ASSISTANT);
            LOGGER.info("uploadSessionFile,文件Summary审核完成,sessionUid:{},fileUid:{},reviewResultModel:{}", sessionUid, fileUid, JSON.toJSONString(fileSummaryCheckResult));
            if (!fileSummaryCheckResult.isRet()) {
                throw new BizException(ResponseEnum.CHECK_FAILED);
            }
            LOGGER.info("uploadSessionFile,文件summary成功,sessionUid:{},fileUid:{},fileSummary:{}", sessionUid, fileUid, fileSummary);
            // 段落信息写入oss中
            JSONObject partJson = new JSONObject();
            partJson.put("fileSummary", fileSummary);
            partJson.put("embeddingResponseModelList",embeddingResponseModelList);
            String partOssUrl = ossService.putObject(AppConstants.CHAT_DOCUMENT_SUMMARY + fileUid, IOUtils.toInputStream(partJson.toJSONString(), "UTF-8"), null, null);
            LOGGER.info("uploadSessionFile,文件part写入oss成功,sessionUid:{},fileUid:{},partOssUrl:{}", sessionUid, fileUid, partOssUrl);
            // 完整文件上传到OSS
            String fileOssUrl = ossService.putObject(AppConstants.CHAT_DOCUMENT + fileUid, file.getInputStream(), null, null);
            LOGGER.info("uploadSessionFile,完整文件写入oss成功,sessionUid:{},fileUid:{},fileOssUrl:{}", sessionUid, fileUid, fileOssUrl);
            JSONObject fileOss = new JSONObject(true);
            fileOss.put("fileUid", fileUid);
            fileOss.put("fileName", file.getOriginalFilename());
            // 记录summary是否已经返回给用户了
            fileOss.put("summaryReturnUser", false);
            fileOss.put("fileOssUrl", fileOssUrl);
            fileOss.put("partOssUrl", partOssUrl);
            defaultCacheManager.setex(AppConstants.SESSION_FILE_TBASE_PREFIX + fileUid, fileSaveTbaseMaxSeconds, fileOss);
            LOGGER.info("uploadSessionFile,finished,sessionUid:{},fileUid:{},fileOss:{}", sessionUid, fileUid, fileOss.toJSONString());
            return new SessionFileResponse(fileUid, file.getOriginalFilename(), false);
        } catch (BizException e) {
            LOGGER.warn("uploadSessionFile failed, sessionUid:{}, fileUid:{}, msg:{}", sessionUid, fileUid, e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("uploadSessionFile failed,sessionUid:" + sessionUid + ",fileUid" + fileUid, e);
            throw new BizException(ResponseEnum.UPLOAD_FILE_FAILED);
        }
    }

    @Override
    public FormUploadFileResponse formUploadFile(MultipartFile file) {
        String fileUid = ShortUid.getUid();
        try {
            String filePath = AppConstants.FORM_UPLOAD_FILE + fileUid + file.getOriginalFilename();
            // 完整文件上传到OSS
            String fileOssUrl = ossService.putObject(filePath, file.getInputStream(), null, null);
            LOGGER.info("formUploadFile,完整文件写入oss成功,userId:{},fileUid:{},fileOssUrl:{}", userAclService.getCurrentUser().getId(), fileUid, fileOssUrl);
            return new FormUploadFileResponse(file.getOriginalFilename(), filePath, fileOssUrl);
        } catch (BizException e) {
            LOGGER.warn("formUploadFile failed, userId:{}, fileUid:{}, msg:{}", userAclService.getCurrentUser().getId(), fileUid, e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("formUploadFile failed,userId:" + userAclService.getCurrentUser().getId() + ",fileUid" + fileUid, e);
            throw new BizException(ResponseEnum.UPLOAD_FILE_FAILED);
        }
    }

    private ReviewResultModel checkContent(String fileContent, String sessionUid, String contentType, ChatRoleEnum chatRoleEnum){
        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
        chatRequestExtData.setEmpId(userAclService.getCurrentUser().getEmpId());
        chatRequestExtData.setSessionUid(sessionUid);
        chatRequestExtData.setInfoSecCheck(true);
        chatRequestExtData.setCodeGPTUser(AppConstants.CODEGPT_TOKEN_USER);
        chatRequestExtData.setContentType(contentType);
        return checkService.getInfoSecCheckRet(fileContent, chatRoleEnum, 1, chatRequestExtData, sessionUid, sessionUid);
    }

    @Override
    public boolean deleteSessionFile(String sessionUid, String fileUid) {
        boolean lock = tbaseCacheService.getLock(AppConstants.SESSION_DELETE_FILE_LOCK + sessionUid, 10000, 2000);
        if (!lock) {
            throw new BizException(ResponseEnum.SESSION_DELETE_PRE_FILE);
        }
        ChatSessionDO chatSessionDO = getChatSession(sessionUid);
        boolean needDel = true;
        if (SessionUtils.getSessionBindingFileSize(chatSessionDO) > 0) {
            JSONObject extInfo = JSONObject.parseObject(chatSessionDO.getExtInfo());
            JSONArray fileList = extInfo.getJSONArray("fileList");
            for (int i = 0; i < fileList.size(); i++) {
                JSONObject fileJson = fileList.getJSONObject(i);
                String thisFileUid = fileJson.getString("fileUid");
                // 如果该文件已经绑定会话,则这里不需要删除,最后提交的时候再判断删除
                if (fileUid.equals(thisFileUid)) {
                    needDel = false;
                }
            }
        }
        if (needDel) {
            ossService.deleteFile(AppConstants.CHAT_DOCUMENT + fileUid);
            ossService.deleteFile(AppConstants.CHAT_DOCUMENT_SUMMARY + fileUid);
            defaultCacheManager.del(AppConstants.SESSION_FILE_TBASE_PREFIX + fileUid);
        }
        tbaseCacheService.releaseLock(AppConstants.SESSION_DELETE_FILE_LOCK + sessionUid);
        return true;
    }

    private String getFileSummary(String sessionUid, List<String> splitFileList, JSONObject documentChatConfig){
        documentChatConfig.getString("fileSummaryMethod");
        String fileSummary;
        ChatSessionDO chatSessionDO = getChatSession(sessionUid);
        UserAuthDO userAuthDO = userAclService.selectByUserId(chatSessionDO.getUserId());
        fileSummary = llmFileSummaryFrontPart(splitFileList, documentChatConfig, userAuthDO.getEmpId());
        /*if (fileSummaryMethod.equalsIgnoreCase("frontPart")) {
        } else {
            fileSummary = llmFileSummary(splitFileList, documentChatConfig, summaryModelDO, sessionUid, fileUid, 1);
        }*/
        return fileSummary;
    }

    /**
     * 根据一个文件的前面一部分进行文件总结
     * @param splitFileList
     * @param documentChatConfig
     * @param empId
     * @return
     */
    private String llmFileSummaryFrontPart(List<String> splitFileList, JSONObject documentChatConfig, String empId) {
        String fileStr = String.join("", splitFileList);
        return documentHandleService.summeryContent(fileStr, empId, documentChatConfig);
    }
/*
    private String llmFileSummary(List<String> splitFileList, JSONObject documentChatConfig, AlgoBackendDO summaryModelDO, String sessionUid, String fileUid, Integer callTimes) {
        Integer everyPartMinSize = documentChatConfig.getInteger("everyPartMinSize");
        Integer maxCallTimes = documentChatConfig.getInteger("maxCallTimes");
        if (callTimes >= maxCallTimes) {
            LOGGER.warn("模型总结段落异常,超过最大递归次数,callTimes:{},sessionUid:{},fileUid:{},splitFileList:{}", callTimes, sessionUid, fileUid, JSON.toJSONString(splitFileList));
            return null;
        }
        // 总长度较短,调用llm进行最终总结
        int totalSize = splitFileList.stream().mapToInt(String::length).sum();
        if (totalSize <= everyPartMinSize) {
            StringBuilder fileSummaryOriginalStr = new StringBuilder();
            splitFileList.forEach(fileSummaryOriginalStr::append);
            String fileSummary = llmSummary(documentChatConfig, fileSummaryOriginalStr.toString(), summaryModelDO);
            LOGGER.info("文件总结全部完成,callTimes:{},sessionUid:{},fileUid:{},fileSummary{},fileSummaryOriginalStr:{}", callTimes, sessionUid, fileUid, fileSummary, fileSummaryOriginalStr);
            return fileSummary;
        }
        List<String> summaryList = new ArrayList<>();
        List<Future<String>> summaryFutureList = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        for (String orgStr : splitFileList) {
            sb.append(orgStr);
            if (sb.length() >= everyPartMinSize) {
                StringBuffer finalSb = sb;
                summaryFutureList.add(appThreadPool.submit(() -> llmSummary(documentChatConfig, finalSb.toString(), summaryModelDO)));
                sb = new StringBuffer();
            }
        }
        if (sb.length() > 0) {
            StringBuffer finalSb = sb;
            summaryFutureList.add(appThreadPool.submit(() -> llmSummary(documentChatConfig, finalSb.toString(), summaryModelDO)));
        }
        for (Future<String> summaryFuture : summaryFutureList) {
            try {
                summaryList.add(summaryFuture.get());
            } catch (Exception e) {
                LOGGER.error("模型总结段落异常,跳过本段,sessionUid:" + sessionUid + ",fileUid:" + fileUid + ",partText:" + sb, e);
            }
        }
        return llmFileSummary(summaryList, documentChatConfig, summaryModelDO, sessionUid, fileUid, ++callTimes);
    }
    */

    private List<EmbeddingResponseModel> embeddingStrList(String fileUid, List<String> splitFileList, JSONObject documentChatConfig) {
        String embeddingService = documentChatConfig.getString("embeddingService");
        String embeddingModel = documentChatConfig.getString("embeddingModel");
        if ("zark".equals(embeddingService)) {
            List<EmbeddingResponseModel> embeddingResponseModelList = zarkService.embeddingStrList(splitFileList, embeddingModel);
            embeddingResponseModelList.forEach(e -> e.setFileUid(fileUid));
            return embeddingResponseModelList;
        }
        TokenDO tokenDO = tokenService.getTokenSystem(AppConstants.CODEGPT_TOKEN_USER);
        Map<EmbeddingResponseModel, Future<List<BigDecimal>>> futureMap = new HashMap<>();
        for (String orgStr : splitFileList) {
            String partUid = ShortUid.getUid();
            Future<List<BigDecimal>> future = appThreadPool.submit(() -> getOpenAiEmbeddingList(orgStr, tokenDO));
            EmbeddingResponseModel embeddingResponseModel = new EmbeddingResponseModel();
            embeddingResponseModel.setFileUid(fileUid);
            embeddingResponseModel.setPartUid(partUid);
            embeddingResponseModel.setOriginalStr(orgStr);
            futureMap.put(embeddingResponseModel, future);
        }
        List<EmbeddingResponseModel> resultList = new ArrayList<>();
        try {
            for (Map.Entry<EmbeddingResponseModel, Future<List<BigDecimal>>> entry : futureMap.entrySet()) {
                EmbeddingResponseModel embeddingResponseModel = entry.getKey();
                embeddingResponseModel.setOriginalEmbeddingList(entry.getValue().get());
                resultList.add(embeddingResponseModel);
            }
        } catch (Exception e) {
            LOGGER.error("EMBEDDING_FAILED", e);
            throw new BizException(ResponseEnum.FILE_EMBEDDING_FAILED);
        }
        return resultList;
    }

    @Override
    public List<BigDecimal> getEmbeddingList(String needEmbeddingStr) {
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        String embeddingService = documentChatConfig.getString("embeddingService");
        String embeddingModel = documentChatConfig.getString("embeddingModel");
        if ("zark".equals(embeddingService)) {
            ZarkEmbeddingRequestBean zarkEmbeddingRequestBean = new ZarkEmbeddingRequestBean(Lists.newArrayList(needEmbeddingStr), embeddingModel);
            return zarkService.embedding(zarkEmbeddingRequestBean).get(0);
        }
        TokenDO tokenDO = tokenService.getTokenSystem(AppConstants.CODEGPT_TOKEN_USER);
        return getOpenAiEmbeddingList(needEmbeddingStr, tokenDO);
    }

    @Override
    public boolean hasSceneSession(Long userId, Long sceneId) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUserIdEqualTo(userId).andSceneIdEqualTo(sceneId);
        List<ChatSessionDO> chatSessionDOList = chatSessionDOMapper.selectByExample(chatSessionDOExample);
        return CollectionUtil.isNotEmpty(chatSessionDOList);
    }

    @Override
    public boolean hasModelSession(Long userId, String model) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        ChatSessionDOExample.Criteria criteria1 = chatSessionDOExample.createCriteria();
        criteria1.andUserIdEqualTo(userId);
        criteria1.andModelEqualTo(model);
        List<SceneDO> sceneDOList = sceneService.getSceneByModelName(model);
        if (CollectionUtils.isNotEmpty(sceneDOList)) {
            List<Long> sceneIdList = sceneDOList.stream().map(SceneDO::getId).collect(Collectors.toList());
            ChatSessionDOExample.Criteria criteria2 = chatSessionDOExample.createCriteria();
            criteria2.andUserIdEqualTo(userId);
            criteria2.andSceneIdIn(sceneIdList);
            chatSessionDOExample.or(criteria2);
        }
        List<ChatSessionDO> chatSessionDOList = chatSessionDOMapper.selectByExample(chatSessionDOExample);
        return CollectionUtil.isNotEmpty(chatSessionDOList);
    }

    @Override
    public void bindDocument(String sessionUid, String documentUid) {
        ChatSessionDO chatSessionDO = getChatSession(sessionUid);
        if (chatSessionDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "sessionUid 错误");
        }
        JSONArray documentUidList = new JSONArray();
        if (StringUtils.isNotBlank(chatSessionDO.getDocumentUidList())) {
            documentUidList = JSONArray.parseArray(chatSessionDO.getDocumentUidList());
        }
        documentUidList.add(documentUid);
        chatSessionDO.setDocumentUidList(documentUidList.toJSONString());
        chatSessionDOMapper.updateByPrimaryKeySelective(chatSessionDO);
    }

    @Override
    public void unbindDocument(String sessionUid, String documentUid) {
        ChatSessionDO chatSessionDO = getChatSession(sessionUid);
        if (chatSessionDO == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "sessionUid 错误");
        }
        if (StringUtils.isBlank(chatSessionDO.getDocumentUidList()) ||
                !JSONArray.parseArray(chatSessionDO.getDocumentUidList()).contains(documentUid)) {
            return;
        }
        JSONArray documentUidList = JSONArray.parseArray(chatSessionDO.getDocumentUidList());
        documentUidList.remove(documentUid);
        chatSessionDO.setDocumentUidList(documentUidList.toJSONString());
        chatSessionDOMapper.updateByPrimaryKeySelective(chatSessionDO);
    }

    @Override
    public List<ChatSessionDO> getSessionByIdList(List<String> sessionUidList) {
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andDeletedEqualTo((byte) 0).andSceneTestNotEqualTo(true).andUidIn(sessionUidList);
        return chatSessionDOMapper.selectByExample(chatSessionDOExample);
    }

    private List<BigDecimal> getOpenAiEmbeddingList(String needEmbeddingStr, TokenDO tokenDO) {
        JSONObject embeddingParam = new JSONObject();
        embeddingParam.put("model", "text-embedding-ada-002");
        embeddingParam.put("input", needEmbeddingStr);
        String ret = null;
        try {
            ret = HttpClient.post(AppConstants.OPENAI_EMBEDDING_URL)
                    .header("codegpt_user", tokenDO.getUser())
                    .header("codegpt_token",tokenDO.getToken())
                    .content(embeddingParam.toJSONString())
                    .syncExecuteWithExceptionThrow(10000);
            JSONObject retJson = JSON.parseObject(ret);
            JSONArray embeddingArr = retJson.getJSONArray("data").getJSONObject(0).getJSONArray("embedding");
            return embeddingArr.toJavaList(BigDecimal.class);
        } catch (Exception e) {
            LOGGER.error("embedding failed,ret:" + ret, e);
            throw new BizException(ResponseEnum.FILE_EMBEDDING_FAILED);
        }
    }

    /*private String llmSummary(JSONObject documentChatConfig, String originalStr, AlgoBackendDO summaryModelDO) {
        String requestId = ShortUid.getUid();
        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        List<ChatMessage> messages = new ArrayList<>();
        String promptFormat = documentChatConfig.getString("promptFormat");
        String summaryFileQuestionPrompt = documentChatConfig.getString("summaryFileQuestionPrompt");
        promptFormat = promptFormat.replace("question", summaryFileQuestionPrompt);
        StringBuilder searchResult = new StringBuilder("Source 〔1〕");
        searchResult.append("\n").append(originalStr);
        promptFormat = promptFormat.replace("searchResult", searchResult);
        messages.add(new ChatMessage("user", promptFormat));
        chatCompletionRequest.setMessages(messages);
        // 请求模型的时候加上工号，兼容AntGLM模型
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        ChatRequestExtData chatRequestExtData = new ChatRequestExtData();
        chatRequestExtData.setEmpId(userAuthDO.getEmpId());
        chatCompletionRequest.setChatRequestExtData(chatRequestExtData);
        GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId, AppConstants.CODEGPT_TOKEN_USER, false, summaryModelDO, chatCompletionRequest, null);
        Consumer<String> needDelTBaseKeyConsumer = (tBaseKey) -> noneSerializationCacheManager.del(tBaseKey);
        params.setNeedDelTBaseKey(needDelTBaseKeyConsumer);
        params.setUniqueAnswerId(requestId);
        ChatMessage chatMessage = AlgoModelExecutor.getInstance().executorChat(summaryModelDO, params);
        return chatMessage.getContent();
    }*/

    /**
     * 获取第一个非置顶回话
     *
     * @return
     */
    //获取第一个非置顶回话
    private ChatSessionDO getFirstNotTopSession() {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        LinkedList<String> topSessionUids = new LinkedList<>();
        if (StringUtils.isNotBlank(currentUser.getTopSessionUids())) {
            topSessionUids.addAll(JSON.parseArray(currentUser.getTopSessionUids(), String.class));
        }
        List<ChatSessionDO> chatSessionDOS = this.listChatSession(currentUser.getId());
        for (ChatSessionDO chatSessionDO : chatSessionDOS) {
            boolean contains = topSessionUids.contains(chatSessionDO.getUid());
            if (!contains) {
                return chatSessionDO;
            }
        }
        return null;
    }
}
