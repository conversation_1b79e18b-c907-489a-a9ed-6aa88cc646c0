package com.alipay.codegencore.model.remote;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:41
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RequiredAction {

    public enum RequiredActionType {
        @JsonProperty("add_message")
        ADD_MESSAGE,
        @JsonProperty("confirm_progress")
        CONFIRM_PROGRESS
    }

    @JsonProperty(value = "type", required = true)
    @JSONField(name = "type", serializeUsing = EnumSerializer.class)
    private RequiredActionType type;

    @JsonProperty(value = "reason", required = true)
    private String reason;

    // Getters and Setters

    public RequiredActionType getType() {
        return type;
    }

    public void setType(RequiredActionType type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

}
