package com.alipay.codegencore.service.ideaevo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.*;
import com.alipay.codegencore.service.codegpt.CalculateTokenService;
import com.alipay.codegencore.service.codegpt.user.AlgoModelExecutor;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.PromptTemplateService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.middle.drm.SvatDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.SvatLogUtils;
import com.alipay.codegencore.utils.code.ActionGenCodePrompt;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.code.BloopSearchClient;
import com.alipay.codegencore.utils.code.JavaATS;
import com.alipay.codegencore.utils.codefuse.PromptCommonUtils;
import com.alipay.codegencore.utils.thread.ThreadPoolUtils;
import com.github.difflib.DiffUtils;
import com.github.difflib.patch.Patch;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/26 15:17
 */
@Slf4j
@Component
public class ModelRequestHandler {

    /**
     * CODE
     * 正则
     */
    private static final Pattern CODE_BLOCK_PATTERN = Pattern.compile("```(\\S+)?\\n([\\s\\S]*?)\\n```");

    /**
     * PLAN
     * 正则
     */
    private static final Pattern PLAN_BLOCK_PATTERN = Pattern.compile("```json\\n([\\s\\S]*?)\\n```");

    /**
     * code diff
     * 正则
     */
    private static final Pattern CODE_DIFF_PATTERN = Pattern.compile(".*\n*(<<<<<<< HEAD)\n*([\\s\\S]*?)(=======)\n*([\\s\\S]*?)(>>>>>>> updated)\n*");

    /**
     * PLAN
     * 正则
     */
    private static final String PLAN_NO_REGEX = "\\d+\\.\\s+";

    /**
     * PLAN
     * 正则
     */
    private static final String PLAN_SPLIT_REGEX = "(?<=。)\\s*(?=\\d+\\.)";



    /**
     * 代码格式
     */
    private static final String BAD_CODE_FORMAT = "\"\"\"";

    /**
     * 来源：SVAT
     */
    private static final String SOURCE = "SVAT";

    private static final String DEFAULT_SVAT_CODE_GEN_PROMPT = "svat_code_gen_prompt";

    private static final String DEFAULT_SVAT_HTTP_MODIFY_PLAN_GEN_PROMPT = "svat_http_modify_plan_gen_prompt";

    /**
     * path
     */
    private static final String PLAN_PATH_PREFIX = "-path：";
    /**
     * plan
     */
    private static final String PLAN_PLAN_PREFIX = "-plan：";
    /**
     * 分隔符
     */
    private static final String PLAN_PLAN_SPLIT = "###";
    /**
     * plan 生成模型结束符
     */
    private static final String PLAN_MODEL_END = "<|code_im_end|>";


    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private SvatDrmConfig svatDrmConfig;

    @Resource
    private PromptTemplateService promptTemplateService;

    @Autowired
    private CalculateTokenService calculateTokenService;

    @Autowired
    private ActionGenCodeSearchInfoService actionGenCodeSearchInfoService;

    /**
     * 分片发送请求
     * @param requirementDesc
     * @param codes
     * @param modelEnv
     * @return
     */
    public List<PlanFile> splitSendModelPlanFileGen(String requirementDesc, List<BloopSearchClient.CodeResult> codes, String modelEnv, String modelName) {

        Stopwatch stopwatch = Stopwatch.createStarted();

        //同一个文件代码片段合并
        Map<String, List<BloopSearchClient.CodeResult>> codeMap = codes.stream()
                .collect(Collectors.groupingBy(BloopSearchClient.CodeResult::getPath));
        List<BloopSearchClient.CodeResult> codeList = Lists.newArrayList();
        codeMap.forEach((path, codeResultList) -> {
            BloopSearchClient.CodeResult codeItem = new BloopSearchClient.CodeResult();
            codeItem.setPath(path);
            codeItem.setSnippet(codeResultList.stream().map(item -> item.getSnippet()).collect(Collectors.joining("\n")));
            codeList.add(codeItem);
        });

        List<List<BloopSearchClient.CodeResult>> codeLists = Lists.partition(codeList, codeGPTDrmConfig.getActionGenCodeSplitCount());
        List<Future<List<PlanFile>>> futureList = codeLists
                .stream()
                .map(codeItemList -> ThreadPoolUtils.submit(ThreadPoolUtils.actionGenCodePool,
                        () -> sendModelPlanFileGen(requirementDesc, codeItemList, modelEnv, modelName)))
                .collect(Collectors.toList());

        List<PlanFile> planList = Lists.newArrayList();
        futureList.forEach(future -> {
            try {
                planList.addAll(future.get());
            } catch (Exception e) {
                log.error("get gen plan result failed.", e);
            }
        });
        stopwatch.stop();
        log.info("gen file plan duration cost:{} size:{} info:{}",
                stopwatch.elapsed(TimeUnit.MILLISECONDS), planList.size(), planList);

        if (CollectionUtils.isEmpty(planList)) {
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_PLAN);
        }

        return planList;
    }


    /**
     * 分片发送请求
     * @param query
     * @param genRequirementInfo
     * @param repoInfo
     * @param modelEnv
     * @return
     */
    public List<PlanFile> splitSendModelPlanFileGenForHttpModify(String sessionId, String query, GenRequirementInfo genRequirementInfo,
                                                                 RepoInfo repoInfo, String modelEnv, String modelName) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        final String mainFilePath = genRequirementInfo.getFilePath();
        final String mainMethod = genRequirementInfo.getMethodName();
        final String repoPath = repoInfo.getRepoPath();
        if (StringUtils.isBlank(mainFilePath)
                || StringUtils.isBlank(mainMethod)) {
            log.warn("main file path or method is blank. skip");
            return List.of();
        }

        //1. 获取文件内容
        String branch = AntCodeClient.defaultIfBlank(repoInfo.getBranch());
        String mainFileContent = AntCodeClient.getFileContent(repoPath,
                branch, genRequirementInfo.getFilePath());
        if (StringUtils.isBlank(mainFileContent)) {
            log.error("[SVAT] get main file content failed. skip");
            return List.of();
        }

        //获取方法体
        String mainMethodContent = JavaATS.getMethodContent(mainFileContent, mainMethod);

        Collection<String> useServiceList = JavaATS.confirmInterface(repoInfo.getRepoPath(), branch,
                mainFileContent, mainMethod);
        log.info("use service:{}", useServiceList);

        List<BloopSearchClient.CodeResult> codeResultList = Lists.newArrayList();
        codeResultList.add(new BloopSearchClient.CodeResult(mainFilePath, mainMethodContent));
        List<Future<List<PlanFile>>> futureList;
        if (CollectionUtils.isNotEmpty(useServiceList)) {
            futureList = useServiceList
                    .stream()
                    .map(useService -> {
                        String serviceFileContent = AntCodeClient.getFileContent(repoPath, branch, useService);
                        codeResultList.add(new BloopSearchClient.CodeResult(useService, serviceFileContent));
                        return ThreadPoolUtils.submit(ThreadPoolUtils.indexBuildPool,
                                () -> sendModelPlanFileGenForHttpModify(query, useService, serviceFileContent, mainFilePath, mainMethodContent, modelEnv, modelName));
                    })
                    .collect(Collectors.toList());
        } else {
            futureList = Lists.newArrayList(ThreadPoolUtils.submit(ThreadPoolUtils.indexBuildPool,
                    () -> sendModelPlanFileGenForHttpModify(query, null, null, mainFilePath, mainMethodContent, modelEnv, modelName)));
        }

        //异步保存搜索结果
        ThreadPoolUtils.execute(ThreadPoolUtils.indexBuildPool,
                () -> actionGenCodeSearchInfoService.save(sessionId, query, JSON.toJSONString(codeResultList)));

        List<PlanFile> planList = Lists.newArrayList();
        futureList.forEach(future -> {
            try {
                planList.addAll(future.get());
            } catch (Exception e) {
                log.error("[SVAT] get gen plan result failed.", e);
            }
        });

        stopwatch.stop();
        final boolean planIsEmpty = CollectionUtils.isEmpty(planList);
        final long cost = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("gen file plan duration cost:{} size:{} info:{}", cost, planList.size(), planList);

        SvatLogUtils.log(SvatLogUtils.BizType.PLAN_GEN, !planIsEmpty, cost);
        if (planIsEmpty) {
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_PLAN);
        }

        //处理主入口步骤问题
        Map<String, List<PlanFile>> mainFilePlanGroup = planList.stream()
                .collect(Collectors.groupingBy(PlanFile::getFilePath));

        List<PlanFile> resultPlans = Lists.newArrayList();
        mainFilePlanGroup.forEach((path, plans) -> {
            //取步骤最多的
            if (plans.size() == 1) {
                resultPlans.add(plans.get(0));
                return;
            }

            PlanFile itemPlan = plans.get(0);
            for (PlanFile item : plans) {
                String itemStr = String.join("", item.getStep());
                String itemPlanStr = String.join("", itemPlan.getStep());
                if (StringUtils.length(itemStr) > StringUtils.length(itemPlanStr)) {
                    itemPlan = item;
                }
            }
            resultPlans.add(itemPlan);
        });

        return resultPlans;
    }


    /**
     * 调用模型生成 plan
     * @param requirementDesc
     * @param codes
     * @param modelEnv
     * @return
     */
    public List<PlanFile> sendModelPlanFileGen(String requirementDesc, List<BloopSearchClient.CodeResult> codes, String modelEnv, String modelName) {

        String prompt = ActionGenCodePrompt.buildPlanFileGenPrompt(codeGPTDrmConfig.getIdeaEvoCodePlanPromptTemplate(), requirementDesc, codes);

        Optional<String> modelResponse = sendModel(getPlanModel(modelName), codeGPTDrmConfig.getIdeaEvoCodePlanSystemPrompt(), SOURCE, prompt, modelEnv);
        if (modelResponse.isEmpty()) {
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_PLAN);
        }
        modelResponse = ModelRequestHandler.parsePlanBlocks(modelResponse.get());
        if (modelResponse.isEmpty()) {
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_PLAN);
        }

        List<PlanFile> plans = Lists.newArrayList();
        try {
            JSONObject modelJsonObject = JSON.parseObject(modelResponse.get());
            JSONArray planList = modelJsonObject.getJSONArray("plan_info");


            for (int i = 0; i < planList.size(); i++) {
                JSONObject item = planList.getJSONObject(i);

                String filePath = item.getString("path");
                String step = item.getString("plan");
                log.info("plan gen path:{} step:{}", filePath, step);

                PlanFile planFile = new PlanFile();
                planFile.setFilePath(filePath);
                planFile.setStep(planStepSplit(step));
                plans.add(planFile);
            }
        } catch (Exception e) {
            log.error("json format ill. res:{}", modelResponse.get(), e);
        }

        return plans;
    }

    /**
     * 新方案索引构建
     * @param query
     * @param codeResults
     * @param modelEnv
     * @return
     */
    public List<PlanFile> sendModelPlanFileGenNew(String query, List<BloopSearchClient.CodeResult> codeResults,
                                                  String modelEnv, String modelName, String planPromptName) {

        //拼接 prompt
        Map<String, Object> params = new HashMap<>();
        params.put("query", query);
        params.put("codeList", codeResults);

        if (StringUtils.isBlank(planPromptName)) {
            planPromptName = svatDrmConfig.getPlanGenPromptName();
        }
        log.info("use prompt name :{}", planPromptName);
        String promptTemplateText = promptTemplateService.getPromptTemplateText(planPromptName,false);
        String prompt = PromptCommonUtils.buildPrompt(promptTemplateText, params);

        Optional<String> modelResponse = sendModel(getPlanModel(modelName), codeGPTDrmConfig.getIdeaEvoCodePlanSystemPrompt(), SOURCE, prompt, modelEnv);
        if (modelResponse.isEmpty()) {
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_PLAN);
        }

        if ("plan_gen_text".equalsIgnoreCase(planPromptName)) {
            return textToPlan(modelResponse.get());
        }

        return jsonToPlan(modelResponse.get());
    }

    /**
     * json 格式 plan 生成解析
     * @param content
     * @return
     */
    private List<PlanFile> jsonToPlan(String content) {
        Optional<String> modelResponse = ModelRequestHandler.parsePlanBlocks(content);
        if (modelResponse.isEmpty()) {
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_PLAN);
        }

        Map<String, List<String>> planMap = Maps.newHashMap();
        try {
            JSONArray planList = JSON.parseArray(modelResponse.get());

            Pattern pattern = Pattern.compile(svatDrmConfig.getPlanStepBlackKeyword());

            for (int i = 0; i < planList.size(); i++) {
                JSONObject item = planList.getJSONObject(i);

                String filePath = item.getString("path");
                String step = item.getString("plan");
                log.info("plan gen path:{} step:{}", filePath, step);

                if (StringUtils.isBlank(step)) {
                    continue;
                }

                Matcher matcher = pattern.matcher(step);
                if (matcher.find()) {
                    log.warn("step:{} match black:{}", step, svatDrmConfig.getPlanStepBlackKeyword());
                    continue;
                }

                filePath = StringUtils.removeStart(filePath, "/");
                List<String> steps = planStepSplit(step);

                if (planMap.containsKey(filePath)) {
                    planMap.get(filePath).addAll(steps);
                } else {
                    planMap.put(filePath, steps);
                }
            }
        } catch (Exception e) {
            log.error("[SVAT] json format ill. res:{}", modelResponse.get(), e);
        }

        List<PlanFile> planFiles = Lists.newArrayList();
        planMap.forEach((path, steps) -> {
            PlanFile planFile = new PlanFile();
            planFile.setFilePath(path);
            planFile.setStep(steps);
            planFiles.add(planFile);
        });
        return planFiles;
    }

    /**
     * 文本格式 plan 生成解析
     * @param content
     * @return
     */
    private List<PlanFile> textToPlan(String content) {
        log.info("text to plan content:{}", content);
        String[] contentList = StringUtils.splitByWholeSeparatorPreserveAllTokens(content, "\n");
        log.info("split content line: {}", JSON.toJSONString(contentList));
        Pattern pattern = Pattern.compile(svatDrmConfig.getPlanStepBlackKeyword());

        List<PlanFile> planFiles = new ArrayList<>();
        for (String line : contentList) {
            if (StringUtils.isBlank(line)) {
                continue;
            }

            line = StringUtils.removeEnd(line, PLAN_MODEL_END);

            if (StringUtils.startsWithIgnoreCase(line, PLAN_PATH_PREFIX)) {
                String filePath = StringUtils.trim(StringUtils.removeStart(line, PLAN_PATH_PREFIX));
                if (StringUtils.isNotBlank(filePath)) {
                    PlanFile planFile = new PlanFile();
                    planFile.setFilePath(filePath);
                    planFiles.add(planFile);
                }
                continue;
            }

            if (StringUtils.startsWithIgnoreCase(line, PLAN_PLAN_PREFIX)) {

                if (planFiles.isEmpty()) {
                    log.error("line:{} not handle", line);
                    continue;
                }

                PlanFile planFile = planFiles.get(planFiles.size() - 1);
                String steps = StringUtils.trim(StringUtils.removeStart(line, PLAN_PLAN_PREFIX));
                if (StringUtils.isNotBlank(steps)) {
                    String[] stepList = StringUtils.split(steps, PLAN_PLAN_SPLIT);
                    planFile.setStep(Arrays.stream(stepList)
                            .filter(StringUtils::isNotBlank)
                            .filter(item -> {
                                Matcher matcher = pattern.matcher(item);
                                return !matcher.find();
                            })
                            .collect(Collectors.toList()));
                }
            }
        }
        return planFiles;
    }

    /**
     * 调用模型生成 plan
     * @param requirementDesc
     * @param useServicePath
     * @param useServiceContent
     * @param mainPath
     * @param mainMethodContent
     * @param modelEnv
     * @return
     */
    public List<PlanFile> sendModelPlanFileGenForHttpModify(String requirementDesc, String useServicePath, String useServiceContent,
                                                            String mainPath, String mainMethodContent, String modelEnv, String modelName) {

        Map<String, Object> params = Maps.newHashMap();
        params.put("requirement", requirementDesc);
        params.put("mainPath", mainPath);
        params.put("mainMethodContent", mainMethodContent);
        params.put("servicePath", useServicePath);
        params.put("serviceContent", useServiceContent);
        String promptTemplateText = null;
        if(codeGPTDrmConfig.isEnablePromptTemplateFromDB()){
            promptTemplateText = promptTemplateService.getPromptTemplateText(DEFAULT_SVAT_HTTP_MODIFY_PLAN_GEN_PROMPT,false);
        }else {
            promptTemplateText = svatDrmConfig.getHttpModifyPlanGenPrompt();
        }
        String prompt = PromptCommonUtils.buildPrompt(promptTemplateText, params);

        Optional<String> modelResponse = sendModel(getPlanModel(modelName), codeGPTDrmConfig.getIdeaEvoCodePlanSystemPrompt(), SOURCE, prompt, modelEnv);
        if (modelResponse.isEmpty()) {
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_PLAN);
        }
        modelResponse = ModelRequestHandler.parsePlanBlocks(modelResponse.get());
        if (modelResponse.isEmpty()) {
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_PLAN);
        }

        Map<String, List<String>> planMap = Maps.newHashMap();
        try {
            JSONArray planList = JSON.parseArray(modelResponse.get());

            for (int i = 0; i < planList.size(); i++) {
                JSONObject item = planList.getJSONObject(i);

                String filePath = item.getString("path");
                String step = item.getString("plan");
                log.info("plan gen path:{} step:{}", filePath, step);

                if (svatDrmConfig.getPlanStepBlackKeyword().contains(step)) {
                    continue;
                }

                filePath = StringUtils.removeStart(filePath, "/");
                List<String> steps = planStepSplit(step);

                if (planMap.containsKey(filePath)) {
                    planMap.get(filePath).addAll(steps);
                } else {
                    planMap.put(filePath, steps);
                }
            }
        } catch (Exception e) {
            log.error("json format ill. res:{}", modelResponse.get(), e);
        }

        List<PlanFile> planFiles = Lists.newArrayList();
        planMap.forEach((path, steps) -> {
            PlanFile planFile = new PlanFile();
            planFile.setFilePath(path);
            planFile.setStep(steps);
            planFiles.add(planFile);
        });

        return planFiles;
    }

    /**
     * 文件级别代码生成
     * @param requirementDesc
     * @param plan
     * @param codeContent
     * @param modelEnv
     * @return
     */
    public CodeInfoFile sendModelCodeFileGen(String requirementDesc, PlanFile plan, String codeContent,
                                             String modelEnv, String modelName, String codePromptName) {

        //代码文件添加行号
        StringBuilder codeLineNumber = new StringBuilder();
        String[] codeLines = AntCodeClient.splitCodeLine(codeContent);
        if (StringUtils.isNotBlank(codeContent)) {
            if (svatDrmConfig.isCodeGenAddLineNumber()) {
                for (int i = 0; i < codeLines.length; i++) {
                    codeLineNumber.append(i + 1).append(": ").append(codeLines[i]).append("\n");
                }
                codeLineNumber.deleteCharAt(codeLineNumber.length() - 1);
            } else {
                codeLineNumber.append(codeContent);
            }
            //增加token数量阈值判断
            long tokenSize = calculateTokenService.getTokenQty(codeLineNumber.toString());
            int maxTokens = svatDrmConfig.getCodeGenMaxTokens();
            if (tokenSize > maxTokens) {
                throw new BizException(ResponseEnum.SVAT_CODE_OVER_MAX_LINES,
                        String.format(ResponseEnum.SVAT_CODE_OVER_MAX_LINES.getErrorMsg(), maxTokens, tokenSize));
            }
        }

        //步骤处理
        StringBuilder steps = new StringBuilder();
        for (int i = 0; i < plan.getStep().size(); i++) {
            steps.append(i + 1).append(". ").append(plan.getStep().get(i)).append("\n");
        }
        steps.deleteCharAt(steps.length() - 1);

        Map<String, Object> params = Maps.newHashMap();
        params.put("codeContent", codeLineNumber.toString());
        params.put("planStep", steps.toString());
        params.put("requirement", requirementDesc);
        if (StringUtils.isBlank(codePromptName)) {
            codePromptName = svatDrmConfig.getCodeGenPromptName();
        }
        log.info("use prompt name :{}", codePromptName);
        String promptTemplateText = getPromptTemplateByLang(plan.getFilePath(), codePromptName);
        String prompt = PromptCommonUtils.buildPrompt(promptTemplateText, params);

        Optional<String> modelResponse = sendModel(getCodeModel(modelName), codeGPTDrmConfig.getIdeaEvoCodeGenSystemPrompt(),
                SOURCE, prompt, modelEnv);
        if (modelResponse.isEmpty()) {
            log.error("[SVAT] code gen fail");
            throw new BizException(ResponseEnum.SVAT_MODEL_NOT_GEN_CODE);
        }

        if ("code_gen_diff_prompt".equalsIgnoreCase(codePromptName)) {
            modelResponse = ModelRequestHandler.replaceDiff(codeContent, modelResponse.get());
        } else {
            modelResponse = ModelRequestHandler.parseCodeBlocks(modelResponse.get());
        }

        CodeInfoFile codeInfoFile = new CodeInfoFile();
        codeInfoFile.setFilePath(plan.getFilePath());
        codeInfoFile.setCodeContent(modelResponse.orElse(codeContent));

        //处理 diff
        if (codeLines == null) {
            codeInfoFile.setDiffCodeSnippet(codeInfoFile.getCodeContent());
        } else {
            Patch<String> patch = DiffUtils.diff(Arrays.asList(codeLines), Arrays.asList(AntCodeClient.splitCodeLine(codeInfoFile.getCodeContent())));
            String diffCodeSnippet = patch.getDeltas().stream().map(delta -> delta.getTarget().getLines())
                    .flatMap(Collection::stream)
                    .collect(Collectors.joining("\n"));
            codeInfoFile.setDiffCodeSnippet(diffCodeSnippet);
        }

        return codeInfoFile;
    }

    /**
     * 通过语言获取对应的 prompt
     * @param filePath
     * @param codePromptName
     * @return
     */
    private String getPromptTemplateByLang(String filePath, String codePromptName) {
        try {
            //获取文件后缀
            String fileSuffix = StringUtils.substringAfterLast(filePath, ".");
            String useCodePromptName = codePromptName + "_" + fileSuffix;
            log.info("file path:{} use prompt name:{}", filePath, useCodePromptName);
            String promptTemplateContent = promptTemplateService.getPromptTemplateText(useCodePromptName,false);
            if (StringUtils.isNotBlank(promptTemplateContent)) {
                return promptTemplateContent;
            }
        } catch (Exception e) {
            log.error("code gen prompt template lang failed. use default", e);

        }
        return promptTemplateService.getPromptTemplateText(codePromptName,false);
    }

    /**
     * 文件级别代码生成
     * @param requirementDesc
     * @param plan
     * @param codeContent
     * @param modelEnv
     * @return
     */
    public void sendModelCodeFileGenStream(String requirementDesc, PlanFile plan, String codeContent, String modelEnv, String modelName) {

        //增加token数量阈值判断
        if (StringUtils.isNotBlank(codeContent)) {
            long tokenSize = calculateTokenService.getTokenQty(codeContent);
            int maxTokens = svatDrmConfig.getCodeGenMaxTokens();
            if (tokenSize > maxTokens) {
                throw new BizException(ResponseEnum.SVAT_CODE_OVER_MAX_LINES,
                        String.format(ResponseEnum.SVAT_CODE_OVER_MAX_LINES.getErrorMsg(), maxTokens, tokenSize));
            }
        }

        //步骤处理
        StringBuilder steps = new StringBuilder();
        for (int i = 0; i < plan.getStep().size(); i++) {
            steps.append(i + 1).append(". ").append(plan.getStep().get(i)).append("\n");
        }
        steps.deleteCharAt(steps.length() - 1);

        Map<String, Object> params = Maps.newHashMap();
        params.put("codeContent", codeContent);
        params.put("planStep", steps);
        params.put("requirement", requirementDesc);
        String promptTemplateText = null;
        if (codeGPTDrmConfig.isEnablePromptTemplateFromDB()) {
            promptTemplateText = promptTemplateService.getPromptTemplateText(DEFAULT_SVAT_CODE_GEN_PROMPT,false);
        } else {
            promptTemplateText = codeGPTDrmConfig.getSvatCodeGenPromptTemplate();
        }

        String prompt = PromptCommonUtils.buildPrompt(promptTemplateText, params);
        sendModelStream(getCodeModel(modelName), codeGPTDrmConfig.getIdeaEvoCodeGenSystemPrompt(), SOURCE, prompt, modelEnv);
    }

    /**
     * 请求模型处理
     * @param systemPrompt
     * @param source
     * @param prompt
     * @param modelEnv
     * @return
     */
    public Optional<String> sendModel(String systemPrompt, String source, String prompt, String modelEnv) {
        return sendModel(codeGPTDrmConfig.getIdeaEvoCodePlanModel(), systemPrompt, source, prompt, modelEnv);
    }

    /**
     * 请求模型处理
     * @param model
     * @param systemPrompt
     * @param source
     * @param prompt
     * @param modelEnv
     * @return
     */
    public Optional<String> sendModel(String model, String systemPrompt, String source, String prompt, String modelEnv) {

        String requestId = ShortUid.getUid();
        ChatMessage systemMessage = new ChatMessage(ChatRoleEnum.SYSTEM.getName(), systemPrompt);
        ChatMessage userMessage = new ChatMessage(ChatRoleEnum.USER.getName(), prompt);

        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        chatCompletionRequest.setMessages(Lists.newArrayList(systemMessage, userMessage));

        Stopwatch stopwatch = Stopwatch.createStarted();
        boolean success = false;
        try {
            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
            GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId, source, false, algoBackendDO, chatCompletionRequest);
            params.setModelEnv(modelEnv);
            ChatMessage assistantMessage = AlgoModelExecutor.getInstance().executorChat(algoBackendDO, params);

            String content = assistantMessage.getContent();
            Optional<String> result = Optional.ofNullable(content);
            success = result.isPresent();
            return result;
        } catch (Exception e) {
            log.error("[SVAT] send model request error, requestId:{}, source:{}, prompt:{}", requestId, source, prompt, e);
        } finally {
            stopwatch.stop();
            long cost = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            log.info("send model request duration cost:{}", cost);
            SvatLogUtils.log(SvatLogUtils.BizType.MODEL, success, cost);
        }
        return Optional.empty();
    }

    /**
     * 请求模型处理
     * @param model
     * @param systemPrompt
     * @param source
     * @param prompt
     * @param modelEnv
     */
    public void sendModelStream(String model, String systemPrompt, String source, String prompt, String modelEnv) {
        String requestId = ShortUid.getUid();
        ChatMessage systemMessage = new ChatMessage(ChatRoleEnum.SYSTEM.getName(), systemPrompt);
        ChatMessage userMessage = new ChatMessage(ChatRoleEnum.USER.getName(), prompt);

        ChatCompletionRequest chatCompletionRequest = new ChatCompletionRequest();
        chatCompletionRequest.setMessages(Lists.newArrayList(systemMessage, userMessage));
        chatCompletionRequest.setStream(true);

        Stopwatch stopwatch = Stopwatch.createStarted();
        boolean success = true;
        try {
            AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
            GptAlgModelServiceRequest params = new GptAlgModelServiceRequest(requestId, source, false, algoBackendDO, chatCompletionRequest);
            params.setModelEnv(modelEnv);
            AlgoModelExecutor.getInstance().executorStreamChat(algoBackendDO, params);
        } catch (Exception e) {
            log.error("[SVAT] send model request error, requestId:{}, source:{}, prompt:{}", requestId, source, prompt, e);
            success = false;
        } finally {
            stopwatch.stop();
            long cost = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            log.info("send model request duration cost:{}", cost);
            SvatLogUtils.log(SvatLogUtils.BizType.MODEL, success, cost);
        }
    }


    /**
     * code 内容提取
     * @param input
     * @return
     */
    public static Optional<String> parseCodeBlocks(String input) {
        Matcher matcher = CODE_BLOCK_PATTERN.matcher(input);
        //这里默认取第一个匹配的内容
        if (matcher.find()) {
            String codeContent = matcher.group(2);
            if (StringUtils.isNotBlank(codeContent)) {
                while (StringUtils.startsWith(codeContent, BAD_CODE_FORMAT)) {
                    codeContent = StringUtils.removeStart(codeContent, BAD_CODE_FORMAT);
                    codeContent = StringUtils.removeStart(codeContent, "\n");
                }

                while (StringUtils.endsWith(codeContent, BAD_CODE_FORMAT)) {
                    codeContent = StringUtils.removeEnd(codeContent, BAD_CODE_FORMAT);
                }
            }
            return Optional.ofNullable(codeContent);
        }
        return Optional.empty();
    }

    /**
     * plan 内容提取
     * @param input
     * @return
     */
    public static Optional<String> parsePlanBlocks(String input) {
        Matcher matcher = PLAN_BLOCK_PATTERN.matcher(input);
        //这里默认取第一个匹配的内容
        if (matcher.find()) {
            return Optional.ofNullable(matcher.group(1));
        }
        return Optional.empty();
    }

    /**
     * diff 内容提取
     * @param codeContent
     * @param input
     * @return
     */
    private static Optional<String> replaceDiff(String codeContent, String input) {
        Matcher matcher = CODE_BLOCK_PATTERN.matcher(input);
        List<String> diffList = Lists.newArrayList();
        //这里默认取第一个匹配的内容
        while (matcher.find()) {
            diffList.add(matcher.group(2));
        }

        //提取 diff 进行替换
        for (String diff : diffList) {
            Matcher diffMatcher = CODE_DIFF_PATTERN.matcher(diff);
            if (diffMatcher.find()) {
                String oldCodeChunk = diffMatcher.group(2);
                String newCodeChunk = diffMatcher.group(4);
                codeContent = StringUtils.replace(codeContent, oldCodeChunk, newCodeChunk);
            }
        }
        return Optional.ofNullable(codeContent);
    }


    private String getPlanModel(String modelName) {
        if (StringUtils.isBlank(modelName)) {
            return codeGPTDrmConfig.getIdeaEvoCodePlanModel();
        }
        return modelName;
    }

    private String getCodeModel(String modelName) {
        if (StringUtils.isBlank(modelName)) {
            return codeGPTDrmConfig.getActionGenCodeFileModel();
        }
        return modelName;
    }

    /**
     * 步骤拆分
     * @param step
     * @return
     */
    private List<String> planStepSplit(String step) {
        if (StringUtils.isBlank(step)) {
            return Lists.newArrayList();
        }
        try {
            String[] steps = step.split(PLAN_SPLIT_REGEX);
            List<String> stepList = Lists.newArrayList();
            for (String item : steps) {
                if (StringUtils.isNotBlank(item)) {
                    stepList.add(item.replaceFirst(PLAN_NO_REGEX, "").replace("\n", ""));
                }
            }
            return stepList;
        } catch (Exception e) {
            log.warn("plan step:{} split error", step, e);
        }
        return Lists.newArrayList(step);
    }

}
