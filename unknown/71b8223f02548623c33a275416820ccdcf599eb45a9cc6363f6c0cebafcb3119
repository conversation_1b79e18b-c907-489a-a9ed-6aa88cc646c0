package com.alipay.codegencore.model.util;

import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.tool.learning.PluginTypeEnum;
import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLog;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageLog;
import com.alipay.codegencore.model.openai.ChatFunctionCall;
import com.alipay.codegencore.model.openai.ChatMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * 插件日志工具类
 */
public class PluginStageUtil {

    /**
     * 从pluginLog中获取答案
     * @param pluginLog
     * @return
     */
    public static String getAnswerFromPluginLog(PluginLog pluginLog){
//        if(pluginLog!=null){
//            for (StageLog stageLog : pluginLog.getStageLogList()) {
//
//            }
//        }
        throw new UnsupportedOperationException();
    }

    /**
     * 从pluginLog中获取role为function的chatMessage
     * @param pluginLog
     * @return 有两个元素
     */
    private static ChatMessage getFunctionChatMessageFromPluginLog(PluginLog pluginLog){
        if(PluginTypeEnum.API.getName().equalsIgnoreCase(pluginLog.getPluginInfo().getType())){
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setRole(ChatRoleEnum.FUNCTION.getName());
            chatMessage.setFunctionCall(getChatFunctionCallFromPluginLog(pluginLog));
            return chatMessage;
        }else if(PluginTypeEnum.PIPELINE.getName().equalsIgnoreCase(pluginLog.getPluginInfo().getType())){
            String answer = getAnswerFromPluginLog(pluginLog);
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setRole(ChatRoleEnum.FUNCTION.getName());
            chatMessage.setContent(answer);
            return chatMessage;
        }
        throw new UnsupportedOperationException();
    }
    /**
     * 获取plugin中的chatMessageList
     * <AUTHOR>
     * @since 2023.11.03
     * @param pluginlogs pluginlogs
     * @return java.util.List<com.alipay.codegencore.model.openai.ChatMessage>
     */
    public static List<ChatMessage> getMessageList(List<PluginLog> pluginlogs){
        List<ChatMessage> messageList = new ArrayList<>();
        for (PluginLog pluginlog : pluginlogs) {
            messageList.add(getAssistantChatMessageFromPluginLog(pluginlog));
            messageList.add(getFunctionChatMessageFromPluginLog(pluginlog));
        }
        return messageList;
    }

    /**
     * 获取取助手的functioncall
     * <AUTHOR>
     * @since 2023.11.02
     * @param pluginLog pluginLog
     * @return com.alipay.codegencore.model.openai.ChatFunctionCall
     */
    public static ChatFunctionCall getChatFunctionCallFromPluginLog(PluginLog pluginLog){
        if(pluginLog ==null){
            return null;
        }
        for (StageLog stageLog : pluginLog.getStageLogList()) {
            if (StageTypeEnum.FUNCTION_CALL.name().equalsIgnoreCase(stageLog.getType())){
                return (ChatFunctionCall) stageLog.getStageInfo().getOutput();
            }
        }
        return null;
    }
    /**
     * 从pluginlog中获取assistant的ChatMessage
     * <AUTHOR>
     * @since 2023.11.03
     * @param pluginLog pluginLog
     * @return com.alipay.codegencore.model.openai.ChatMessage
     */
    private static ChatMessage getAssistantChatMessageFromPluginLog(PluginLog pluginLog){
        if(pluginLog !=null){
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setRole(ChatRoleEnum.ASSISTANT.name());
            chatMessage.setFunctionCall(getChatFunctionCallFromPluginLog(pluginLog));
            return chatMessage;
        }
        throw new UnsupportedOperationException();
    }
    /**
     * 检查是否追问
     * <AUTHOR>
     * @since 2023.11.03
     * @param pluginLog pluginLog
     * @return java.lang.Boolean
     */
    public static Boolean isAdditionalInquiry(PluginLog pluginLog){
        if(pluginLog ==null){
            return false;
        }
        if(pluginLog.getStageLogList().size()==1&&StageTypeEnum.FUNCTION_CALL.name().equalsIgnoreCase(pluginLog.getStageLogList().get(0).getType())){
            return true;
        }
        return false;
    }
}



