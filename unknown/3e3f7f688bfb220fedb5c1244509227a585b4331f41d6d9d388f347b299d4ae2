/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.middle.tbase;

import com.alibaba.fastjson.JSON;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * tbase缓存服务
 *
 * <AUTHOR>
 * @version TbaseCacheServiceImpl.java, v 0.1 2023年06月02日 11:30 xiaobin
 */
@Service
public class TbaseCacheServiceImpl implements TbaseCacheService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TbaseCacheServiceImpl.class);

    private String prefixGlobalCache = "globalCache";

    private String prefixLock = "lock";

    private static final String SUCCESS_RET = "OK";

    @Resource(name = "defaultCacheManager")
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Override
    public boolean getLock(String lockName, int expireMilSecond) {
        String lockKey = buildKey(prefixLock, lockName);
        String ret = defaultCacheManager.set(lockKey, "ownerId1", "nx", "px", expireMilSecond);
        boolean lock = SUCCESS_RET.equalsIgnoreCase(ret);
        if (!lock) {
            LOGGER.warn("getLock ret: lockName={} expire={} ret={}", lockName, expireMilSecond, ret);
        }
        return lock;
    }

    @Override
    public boolean releaseLock(String lockName) {
        String lockKey = buildKey(prefixLock, lockName);
        Long ret = defaultCacheManager.compareDel(lockKey, "ownerId1");
        if (ret != 1L) {
            LOGGER.info("releaseLock ret: lockName={}  ret={}", lockName, ret);
        }
        return ret == 1L;
    }

    @Override
    public Serializable getCache(String key) {
        return defaultCacheManager.get(buildKey(prefixGlobalCache, key));
    }

    @Override
    public void putCache(String key, Serializable value, int expire) {
        //过期时间单位为秒
        String result = defaultCacheManager.setex(buildKey(prefixGlobalCache, key), expire, value);
        LOGGER.info("put cache key:{} result:{}", key, result);
    }

    @Override
    public boolean delKey(String key) {
        Long ret =  defaultCacheManager.del(key);
        if (ret != 1L) {
            LOGGER.warn("delete ret: key={}  ret={}", key, ret);
            return false;
        }
        return true;
    }

    /**
     * @Description:不设置过期时间
     **/
    @Override
    public void putCache(String key, Serializable value) {
        defaultCacheManager.set(buildKey(prefixGlobalCache, key), value);
    }

    @Override
    public Long rPushList(String key, Serializable[] jobList) {
        return defaultCacheManager.rpush(key, jobList);
    }

    @Override
    public <T extends Serializable> List<T> lPopList(String key, int size, Class<T> clazz) {
        List<Serializable> list = defaultCacheManager.lmpopex(key, size, -1);
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return JSON.parseArray(JSON.toJSONString(list), clazz);
    }

    @Override
    public Long incrbyex(String key, int value, int defaultValue, int expire) {
        return defaultCacheManager.incrbyex(key, 1, 0, expire);
    }

    private String buildKey(String prefix, String itemName) {
        return String.format("%s:%s", prefix, itemName);
    }

}