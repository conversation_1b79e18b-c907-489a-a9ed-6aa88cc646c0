package com.alipay.codegencore.model.model;

/**
 * <AUTHOR>
 * @version : FormUploadFileResponse.java, v 0.1 2023年11月20日 16:17 baoping Exp $
 */
public class FormUploadFileResponse {

    private String fileName;
    private String filePath;
    private String fileOssUrl;

    /**
     * 构造方法
     * @param fileName
     * @param filePath
     * @param fileOssUrl
     */
    public FormUploadFileResponse(String fileName, String filePath, String fileOssUrl) {
        this.fileName = fileName;
        this.filePath = filePath;
        this.fileOssUrl = fileOssUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileOssUrl() {
        return fileOssUrl;
    }

    public void setFileOssUrl(String fileOssUrl) {
        this.fileOssUrl = fileOssUrl;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
