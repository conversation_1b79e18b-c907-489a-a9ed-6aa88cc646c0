package com.alipay.codegencore.service.impl.codegpt;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.RecordLogEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.IntentionRecognitionService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.AsyncLogUtils;
import com.alipay.codegencore.utils.CollectLogUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 意图识别服务实现
 */
@Service
public class IntentionRecognitionServiceImpl implements IntentionRecognitionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IntentionRecognitionService.class);

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private AsyncLogUtils asyncLogUtils;

    @AppConfig("spring.application.name")
    private String appName;


    @Override
    public boolean isCodeDomain(String messageUid, List<ChatMessage> chatMessageList) {
        if (CollectionUtils.isEmpty(chatMessageList)) {
            return false;
        }
        JSONObject param = new JSONObject();
        JSONObject features = new JSONObject();
        JSONObject query = new JSONObject();
        JSONArray prompts = new JSONArray();
        for (ChatMessage chatMessage : chatMessageList) {
            if (ChatRoleEnum.USER.getName().equals(chatMessage.getRole())) {
                prompts.add(AppConstants.HUMAN + ": " + chatMessage.getContent());
            } else if (ChatRoleEnum.ASSISTANT.getName().equals(chatMessage.getRole())) {
                prompts.add(AppConstants.BOT + ": " + chatMessage.getContent());
            }
        }
        if (prompts.isEmpty()) {
            LOGGER.warn("意图识别没有问题数据,param:{}", JSONObject.toJSONString(chatMessageList));
            return false;
        }
        query.put("prompts", prompts);
        features.put("query", query);
        param.put("features", features);
        String ret = null;
        try {
            long startTime = System.currentTimeMillis();
            ret = HttpClient.post(codeGPTDrmConfig.getIntentionRecognitionServiceUrl())
                    .header("MPS-app-name", appName)
                    .header("MPS-http-version", "1.0")
                    .content(param.toJSONString())
                    .syncExecuteWithExceptionThrow(10000);
            asyncLogUtils.logThirdPartyCallTime(AppConstants.SAFETY_LOG_PREFIX, ReviewPlatformEnum.INTENTION.name(), startTime, true, ChatRoleEnum.USER.getName(),null);
            JSONObject retJson = JSONObject.parseObject(ret);
            Boolean success = retJson.getBoolean("success");
            long during = System.currentTimeMillis() - startTime;
            Map<String, Object> detail = new HashMap<>();
            detail.put("serviceName", "intention");
            detail.put("status", success);
            detail.put("during", during);
            detail.put("requestId", messageUid);
            detail.put("errorInfo", retJson.toJSONString());
            CollectLogUtils.printCollectLog(RecordLogEnum.DEPENDENCY_DATA, detail);
            LOGGER.info("意图识别,messageUid:{},结果:{}", messageUid, ret);
            if (!success) {
                return true;
            }
            return retJson.getJSONObject("resultMap").getJSONArray("algorithmResult").getBoolean(0);
        } catch (Exception e) {
            LOGGER.error("获取代码领域意图识别失败,ret:" + ret + ",chatMessageList:" + JSONObject.toJSONString(chatMessageList), e);
            return true;
        }
    }


}
