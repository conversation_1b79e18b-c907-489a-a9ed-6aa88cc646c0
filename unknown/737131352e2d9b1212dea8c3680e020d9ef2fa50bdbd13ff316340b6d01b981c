package com.alipay.codegencore.web.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.model.DocumentToChatResponse;
import com.alipay.codegencore.model.request.DocumentToChatRequestBean;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.tool.learning.DocumentChatService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 文档对话OpenAPI
 */
@RestController
@RequestMapping("/api/documentChat")
@Slf4j
public class DocumentChatController {

    // 日志记录
    private static final Logger LOGGER = LoggerFactory.getLogger(DocumentChatController.class);

    /**
     * 文档聊天服务接口
     */
    @Resource
    private DocumentChatService documentChatService;

    @Resource
    private UserAclService userAclService;

    /**
     * 文档插件service，组装需要推理的prompt
     * @param documentToChatRequestBean
     * @return
     */
    @PostMapping(value = "/documentToChatPluginService")
    public JSONObject documentToChatPluginService(HttpServletRequest httpServletRequest,
                                                  @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser,
                                                  @RequestHeader(value = "codegpt_token", required = false) String codeGPTToken,
                                                  @RequestBody DocumentToChatRequestBean documentToChatRequestBean) {
        LOGGER.info("documentToChatPluginService param:{}", JSON.toJSONString(documentToChatRequestBean));
        Map<String, Object> errorMsg = checkOpenapiToken(httpServletRequest, codeGPTUser, codeGPTToken);
        if(errorMsg != null){
            return JSON.parseObject(JSON.toJSONString(errorMsg));
        }

        JSONObject result = new JSONObject();
        try {
            DocumentToChatResponse documentToChatResponse = documentChatService.documentToChatPluginService(documentToChatRequestBean);
            result.put("prompt",documentToChatResponse.getPrompt());
            result.put("processingTime",documentToChatResponse.getProcessingTime());
        } catch (Exception e) {
            result.put("code","500");
            result.put("msg",e.getMessage());
        }
        return result;
    }

    private Map<String, Object> checkOpenapiToken(HttpServletRequest httpServletRequest, String codeGPTUser, String codeGPTToken) {
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            LOGGER.info("assistant conversation request not authorized, user: {}", codeGPTUser);
            HashMap<String, Object> errorMsg = new HashMap<>();
            errorMsg.put("code", "6");
            errorMsg.put("errorMsg", "权限不足");
            return errorMsg;
        }
        return null;
    }


}

