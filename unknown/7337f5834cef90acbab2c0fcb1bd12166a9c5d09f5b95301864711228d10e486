package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.openai.UserPluginRecordsVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.08.12
 */
public interface UserPluginRecordsManualMapper {
    /**
     * 通过用户ID查询场景记录列表总数
     *
     * @param pluginId
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Select({"<script>"
            + "select  records.user_id "
            + "        ,records.plugin_id "
            + "        ,records.id "
            + "        ,records.control_type "
            + "        ,user.user_name "
            + "        ,user.emp_id "
            + "from    cg_user_plugin_records as records "
            + "        ,cg_user_auth as user "
            + "where   records.user_id = user.id  "
            + "and records.plugin_id =#{pluginId} "
            + "and records.deleted =0 "
            + "<if test='controlType != null'> "
            + "and records.control_type = #{controlType}"
            + "</if>"
            + "<if test='query != null'> "
            + "and user.user_name like concat('%',#{query},'%') "
            + "</if>"
            + "  limit #{pageNo},#{pageSize} "
            + "</script>"})
    List<UserPluginRecordsVO> selectUser(@Param("pluginId") Long pluginId, @Param("query") String query, @Param("controlType") Integer controlType, @Param("pageNo") int pageNo,
                                         @Param("pageSize") int pageSize);

    /**
     * 通过用户ID查询场景记录列表总数
     *
     * @param pluginId
     * @param query
     * @return
     */
    @Select({"<script>"
            + "select  count(*) "
            + "from    cg_user_plugin_records as records "
            + "        ,cg_user_auth as user "
            + "where   records.user_id = user.id  "
            + "and records.plugin_id =#{pluginId} "
            + "and records.deleted =0 "
            + "<if test='controlType != null'> "
            + "and records.control_type = #{controlType}"
            + "</if>"
            + "<if test='query != null'> "
            + "and user.user_name like concat('%',#{query},'%') "
            + "</if>"
            + "</script>"})
    Long selectUserCount(@Param("pluginId") Long pluginId, @Param("query") String query, @Param("controlType") Integer controlType);
}
