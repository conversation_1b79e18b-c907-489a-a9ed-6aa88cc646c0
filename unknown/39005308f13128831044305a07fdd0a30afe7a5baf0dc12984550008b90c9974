/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.aop.handler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 对用户的webAPI接口的前置处理器
 *
 * <AUTHOR>
 * @version WebApiPreHandler.java, v 0.1 2023年03月29日 20:36 xiaobin
 */
public interface WebApiPreHandler {
    /**
     * 前置操作
     *
     * @param request
     * @param response
     * @param methodName
     * @param args
     */
    void execute(HttpServletRequest request, HttpServletResponse response, String methodName, Object[] args);

}