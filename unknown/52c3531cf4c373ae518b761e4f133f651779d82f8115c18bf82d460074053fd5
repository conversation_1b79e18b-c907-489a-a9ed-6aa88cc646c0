package com.alipay.codegencore.model.openai;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>Each object has a role (either “system”, “user”, or “assistant”) and content (the content of the message). Conversations can be as short as 1 message or fill many pages.</p>
 * <p>Typically, a conversation is formatted with a system message first, followed by alternating user and assistant messages.</p>
 * <p>The system message helps set the behavior of the assistant. In the example above, the assistant was instructed with “You are a helpful assistant.”<br>
 * The user messages help instruct the assistant. They can be generated by the end users of an application, or set by a developer as an instruction.<br>
 * The assistant messages help store prior responses. They can also be written by a developer to help give examples of desired behavior.
 * </p>
 *
 * see <a href="https://platform.openai.com/docs/guides/chat/introduction">OpenAi documentation</a>
 */

public class ChatMessage {

	/**
	 * Must be either 'system', 'user', or 'assistant'.<br>
	 */
	String role;
	String content;

	String name;

	@JSONField(name = "functionCall", alternateNames = "function_call")
	ChatFunctionCall functionCall;

	/**
	 * 无参构造函数
	 */
	public ChatMessage() {
	}

	/**
	 * 构造函数
	 * @param role Must be either 'system', 'user', 'assistant' or 'function'
	 * @param content  对话内容
	 */
	public ChatMessage(String role, String content) {
		this.role = role;
		this.content = content;
	}

	/**
	 * 将空字符串转换为null
	 */
	public void convertEmptyPropertyStringToNull(){
		if ("".equals(role)) {
			role = null;
		}
		if ("".equals(content)) {
			content = null;
		}
		if ("".equals(name)) {
			name = null;
		}
		if (functionCall != null) {
			functionCall.convertEmptyPropertyStringToNull();
		}
	}

	/**
	 * 全参构造函数
	 * @param role Must be either 'system', 'user', 'assistant' or 'function'
	 * @param content
	 * @param functionCall
	 */
	public ChatMessage(String role, String content, String name, ChatFunctionCall functionCall) {
		this.role = role;
		this.content = content;
		this.name = name;
		this.functionCall = functionCall;
	}

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@JSONField(name = "function_call")
	public ChatFunctionCall getFunctionCall() {
		return functionCall;
	}
	@JsonProperty("functionCall")
	@JsonAlias("function_call")
	public void setFunctionCall(ChatFunctionCall functionCall) {
		this.functionCall = functionCall;
	}
}
