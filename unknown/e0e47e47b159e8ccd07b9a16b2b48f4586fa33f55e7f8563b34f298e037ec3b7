package com.alipay.codegencore.service.middle.drm;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.response.tsingyan.TsingyanConfigDataModel;
import com.alipay.codegencore.service.CodeRecommandService;
import com.alipay.drm.client.api.annotation.DAttribute;
import com.alipay.drm.client.api.annotation.DResource;
import com.alipay.sofa.specs.annotation.drm.DrmAttributeSpec;
import com.alipay.sofa.specs.annotation.drm.DrmResourceSpec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.alipay.codegencore.model.AppConstants.DEFAULT_TSINGYAN_PLUGIN_CONFIG_JSON;


/**
 * 业务服务开关
 *
 * <AUTHOR>
 * 创建时间 2021-12-20
 */
@Component
@DResource(id = "com.alipay.codegencore.bizswitch")
@DrmResourceSpec(name = "代码生成推荐业务开关集合")
public class BizSwitch {
    private static final Logger LOGGER = LoggerFactory.getLogger(BizSwitch.class);


    @Resource
    private CodeRecommandService codeRecommandService;


    /**
     * 代码生成近端服务配置数据
     */
    @DAttribute
    @DrmAttributeSpec(name = "青燕插件配置")
    private String pluginConfig = DEFAULT_TSINGYAN_PLUGIN_CONFIG_JSON;

    /**
     * 获取青燕插件配置(json)
     * @return
     */
    public String getPluginConfig() {
        return pluginConfig;
    }

    /**
     * 设置青燕插件配置(json),然后转为对象，设置到{@link AppConstants#TSINGYAN_PLUGIN_CONFIG_MODEL}
     * 插件侧请求{@code  url=/api/v1/queryCompletionData} 时使用
     * @param pluginConfig
     */
    public void setPluginConfig(String pluginConfig) {
        this.pluginConfig = pluginConfig;
        LOGGER.info("drm开关 - pluginConfig 更改。最新值: {}", pluginConfig);
        try {
            TsingyanConfigDataModel tsingyanConfigDataModel = JSONObject.parseObject(pluginConfig, TsingyanConfigDataModel.class);
            LOGGER.info("设置静态配置。最新值: {}", JSONObject.toJSONString(tsingyanConfigDataModel));
            AppConstants.TSINGYAN_PLUGIN_CONFIG_MODEL = tsingyanConfigDataModel;
        }catch (Throwable e){
            LOGGER.error("drm开关 - pluginConfig 更改失败。",e);
            //失败后还原默认值
            AppConstants.TSINGYAN_PLUGIN_CONFIG_MODEL = new TsingyanConfigDataModel();
        }
    }
}
