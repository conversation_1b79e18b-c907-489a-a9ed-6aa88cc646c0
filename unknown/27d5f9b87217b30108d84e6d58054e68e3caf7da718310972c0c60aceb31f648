/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version $Id: SimpleActionCardButton.java, v 0.1 2021-11-19 9:59 wb-tzg858080 Exp $$
 */
public class SimpleActionCardButton extends ToString {
    /**
     * 按钮名称
     */
    private String name ;
    /**
     * 链接地址
     */
    private String  url ;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01 
 * @return null
     */
    public SimpleActionCardButton() {
    }
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01
     * @param name name
     * @param url url 
     * @return null
     */
    public SimpleActionCardButton(String name, String url) {
        this.name = name;
        this.url = url;
    }
}
