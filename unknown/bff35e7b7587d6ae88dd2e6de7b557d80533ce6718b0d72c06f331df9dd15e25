package com.alipay.codegencore.model.domain;

import java.util.Date;

public class UserSceneRecordsDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_scene_records.id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_scene_records.gmt_create
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_scene_records.gmt_modified
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_scene_records.user_id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_scene_records.scene_id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    private Long sceneId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_scene_records.control_type
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    private Integer controlType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_scene_records.deleted
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    private Byte deleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_scene_records.id
     *
     * @return the value of cg_user_scene_records.id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_scene_records.id
     *
     * @param id the value for cg_user_scene_records.id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_scene_records.gmt_create
     *
     * @return the value of cg_user_scene_records.gmt_create
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_scene_records.gmt_create
     *
     * @param gmtCreate the value for cg_user_scene_records.gmt_create
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_scene_records.gmt_modified
     *
     * @return the value of cg_user_scene_records.gmt_modified
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_scene_records.gmt_modified
     *
     * @param gmtModified the value for cg_user_scene_records.gmt_modified
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_scene_records.user_id
     *
     * @return the value of cg_user_scene_records.user_id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_scene_records.user_id
     *
     * @param userId the value for cg_user_scene_records.user_id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_scene_records.scene_id
     *
     * @return the value of cg_user_scene_records.scene_id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public Long getSceneId() {
        return sceneId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_scene_records.scene_id
     *
     * @param sceneId the value for cg_user_scene_records.scene_id
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public void setSceneId(Long sceneId) {
        this.sceneId = sceneId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_scene_records.control_type
     *
     * @return the value of cg_user_scene_records.control_type
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public Integer getControlType() {
        return controlType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_scene_records.control_type
     *
     * @param controlType the value for cg_user_scene_records.control_type
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public void setControlType(Integer controlType) {
        this.controlType = controlType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_scene_records.deleted
     *
     * @return the value of cg_user_scene_records.deleted
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public Byte getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_scene_records.deleted
     *
     * @param deleted the value for cg_user_scene_records.deleted
     *
     * @mbg.generated Mon Aug 21 11:41:37 CST 2023
     */
    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }
}