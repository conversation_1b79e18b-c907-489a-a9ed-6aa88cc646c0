/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.utils;

import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * RockCode API签名工具类
 *
 * <AUTHOR>
 * @version RockCodeSignatureUtil.java, v 0.1 2024年12月12日 yunchen
 */
public class RockCodeUtil {

    /** 内容格式化模板 */
    private static final String CONTENT_FORMAT = "clientId=%s;timestamp=%d";
    
    /** 加密类型 */
    private static final String CIPHER_TYPE = "AES";

    /**
     * 生成RockCode API签名
     *
     * @param clientId  应用ID
     * @param timestamp 时间戳
     * @param secret    密钥
     * @return 签名字符串
     * @throws Exception 加密异常
     */
    public static String generateSignature(String clientId, Long timestamp, String secret) throws Exception {
        String content = String.format(CONTENT_FORMAT, clientId, timestamp);
        SecretKeySpec key = new SecretKeySpec(secret.getBytes(), CIPHER_TYPE);
        Cipher cipher = Cipher.getInstance(CIPHER_TYPE);
        byte[] byteContent = content.getBytes("utf-8");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] result = cipher.doFinal(byteContent);
        return Base64.getUrlEncoder().encodeToString(result);
    }

    /**
     * 构建RockCode API请求头
     *
     * @param appName   应用名称
     * @param signature 签名
     * @param timestamp 时间戳
     * @return 请求头Map
     */
    public static Map<String, String> buildRequestHeaders(String appName, String signature, Long timestamp) {
        Map<String, String> headers = new HashMap<>();
        headers.put("x-rockcodeprod-appid", appName);
        headers.put("x-rockcodeprod-signature", signature);
        headers.put("x-rockcodeprod-timestamp", String.valueOf(timestamp));
        headers.put("Content-Type", "application/json");
        return headers;
    }


    /**
     * 获取RockCode API host
     * @return
     */
    public static String getRockCodeHost(){
        if(VisableEnvUtil.isPrePub()){
            return "https://rockcode-pre.antgroup-inc.cn";
        }else{
            return "https://rockcode.antgroup-inc.cn";
        }
    }
}
