/*
 * Ant Group
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.alipay.codegencore.dal.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @version RateLimitManualMapper.java, v 0.1 2024年02月27日 下午2:31 lqb01337046
 */
public interface RateLimitManualMapper extends SceneDOMapper {
    @Select({"<script>\n"
            + "  select id\n"
            + "  from cg_rate_limit\n"
            + "  where 1=1\n"
            + "  <if test='type != null'>\n"
            + "    and type = #{type}\n"
            + "  </if>\n"
            + "  <if test='template != null'>\n"
            + "    and template = #{template}\n"
            + "  </if>\n"
            + "  <if test='needLimit != null'>\n"
            + "    and need_limit = #{needLimit}\n"
            + "  </if>\n"
            + "  <if test='query != null'>\n"
            + "    and (\n"
            + "      id like concat('%', #{query}, '%') or\n"
            + "      caller like concat('%', #{query}, '%') or\n"
            + "      target like concat('%', #{query}, '%') or\n"
            + "      template like concat('%', #{query}, '%')\n"
            + "    )\n"
            + "  </if>\n"
            + "  <if test='sort == \"DESC\"'>\n"
            + "    order by sorted DESC\n"
            + "  </if>\n"
            + "  <if test='sort == \"ASC\"'>\n"
            + "    order by sorted ASC\n"
            + "  </if>\n"
            + "</script>\n"})
    List<Long> getAlLRateLimitId(@Param("query") String query,@Param("type") String type,@Param("template") String template,@Param("needLimit") Boolean needLimit, @Param("sort") String sort);
}
