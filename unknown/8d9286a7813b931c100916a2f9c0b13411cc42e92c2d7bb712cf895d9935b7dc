package com.alipay.codegencore.model.response;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.common.tracer.util.TracerContextUtil;

import java.io.Serializable;

/**
 * 基础返回值对象
 * <p>
 * 包含默认的返回值属性
 * errorCode: 错误码
 * errorMsg: 错误信息
 * </p>
 *
 * <AUTHOR>
 * 创建时间 2021-11-10
 */
public class BaseResponse<T> implements Serializable {
    /**
     * 防止字段变化导致反序列化失败
     */
    private static final long serialVersionUID = 1L;
    /**
     * 错误码
     */
    private int errorCode;
    /**
     * 错误类型，和错误码一一对应
     */
    private ResponseEnum errorType;
    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 异常堆栈信息
     */
    private String stackTrace;

    /**
     * 实际返回值
     */
    private T data;

    /**
     * 请求traceId
     */
    private String traceId;

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public ResponseEnum getErrorType() {
        return errorType;
    }

    public void setErrorType(ResponseEnum errorType) {
        this.errorType = errorType;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public static <E> BaseResponse<E> build(E data) {
        return build(ResponseEnum.SUCCESS, data, null);
    }

    public static <E> BaseResponse<E> build(ResponseEnum responseEnum, Throwable throwable) {
        return build(responseEnum, null, throwable);
    }

    /**
     * 构建返回值对象
     *
     * @param responseEnum 返回值枚举
     * @param <E>          返回值类型
     * @return 返回值对象
     */
    public static <E> BaseResponse<E> build(ResponseEnum responseEnum) {
        BaseResponse<E> baseResponse = new BaseResponse<>();
        baseResponse.setErrorCode(responseEnum.getErrorCode());
        baseResponse.setErrorMsg(responseEnum.getErrorMsg());
        if(baseResponse.getErrorType() != ResponseEnum.SUCCESS){
            baseResponse.setErrorType(responseEnum);
        }
        baseResponse.setTraceId(TracerContextUtil.getTraceId());
        return baseResponse;
    }

    /**
     * 构建返回值对象
     * @param responseEnum 返回值枚举
     * @param errorMsg 错误信息
     * @return 返回值对象
     */
    public static <E> BaseResponse<E> build(ResponseEnum responseEnum, String errorMsg) {
        BaseResponse<E> baseResponse = new BaseResponse<>();
        baseResponse.setErrorCode(responseEnum.getErrorCode());
        if(baseResponse.getErrorType() != ResponseEnum.SUCCESS){
            baseResponse.setErrorType(responseEnum);
        }

        baseResponse.setErrorMsg(errorMsg);
        baseResponse.setTraceId(TracerContextUtil.getTraceId());
        return baseResponse;
    }

    /**
     * 构建返回值对象
     *
     * @param responseEnum 返回值枚举
     * @param data         返回值
     * @param throwable    异常
     * @param <E>          返回值类型
     * @return 返回值对象
     */
    public static <E> BaseResponse<E> build(ResponseEnum responseEnum, E data, Throwable throwable) {
        BaseResponse<E> baseResponse = new BaseResponse<>();
        baseResponse.setData(data);
        baseResponse.setErrorCode(responseEnum.getErrorCode());
        if(baseResponse.getErrorType() != ResponseEnum.SUCCESS){
            baseResponse.setErrorType(responseEnum);
        }

        if (throwable != null) {
            baseResponse.setErrorMsg(throwable.getMessage());
        }
        baseResponse.setTraceId(TracerContextUtil.getTraceId());
        return baseResponse;
    }

    /**
     * 构建返回值对象
     *
     * @param responseEnum 返回值枚举
     * @param data         返回值
     * @param <E>          返回值类型
     * @return 返回值对象
     */
    private static <E> BaseResponse<E> build(ResponseEnum responseEnum, E data) {
        BaseResponse<E> baseResponse = new BaseResponse<>();
        baseResponse.setData(data);
        baseResponse.setErrorCode(responseEnum.getErrorCode());
        baseResponse.setErrorMsg(responseEnum.getErrorMsg());
        if(baseResponse.getErrorType() != ResponseEnum.SUCCESS){
            baseResponse.setErrorType(responseEnum);
        }
        baseResponse.setTraceId(TracerContextUtil.getTraceId());
        return baseResponse;
    }


    public static <E> BaseResponse<E> buildSuccess() {
        return build(ResponseEnum.SUCCESS, null, null);
    }


}
