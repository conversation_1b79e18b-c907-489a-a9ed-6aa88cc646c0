package com.alipay.codegencore.model.openai;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 2024/10/29 14:05
 */
public class GenPlanResponse {

    private List<PlanFile> planList;

    private List<CodeChunk> codeChunkList;

    public List<PlanFile> getPlanList() {
        return planList;
    }

    public void setPlanList(List<PlanFile> planList) {
        this.planList = planList;
    }

    public List<CodeChunk> getCodeChunkList() {
        return codeChunkList;
    }

    public void setCodeChunkList(List<CodeChunk> codeChunkList) {
        this.codeChunkList = codeChunkList;
    }

    public static class CodeChunk {

        private String path;

        private String content;

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

}
