package com.alipay.codegencore.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.ChatMessageDOExample;
import com.alipay.codegencore.dal.example.ChatSessionDOExample;
import com.alipay.codegencore.dal.example.TokenDOExample;
import com.alipay.codegencore.dal.example.UserAuthDOExample;
import com.alipay.codegencore.dal.mapper.ChatMessageDOMapper;
import com.alipay.codegencore.dal.mapper.ChatSessionDOMapper;
import com.alipay.codegencore.dal.mapper.TokenDOMapper;
import com.alipay.codegencore.dal.mapper.UserAuthDOMapper;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.domain.*;
import com.alipay.codegencore.model.enums.*;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.codegpt.CodeGptUserModelItemModel;
import com.alipay.codegencore.model.model.tool.learning.FunctionCallConfig;
import com.alipay.codegencore.model.openai.SceneVO;
import com.alipay.codegencore.model.request.UserLoginNotifyPluginRequest;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.UserSceneRecordsService;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.OssService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.codefuse.VisableEnvUtil;
import com.alipay.codegencore.utils.http.HttpClient;
import com.alipay.codegencore.utils.thread.ContextUtil;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.google.common.cache.Cache;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.alipay.codegencore.model.contant.WebApiContents.*;

/**
 * 用户鉴权控制
 * <AUTHOR>
 */
@Service
public class UserAclServiceImpl implements UserAclService {

    private final Logger LOGGER = LoggerFactory.getLogger(UserAclServiceImpl.class);

    @Resource
    private TokenDOMapper tokenDOMapper;

    @Resource
    private UserAuthDOMapper userAuthDOMapper;

    @Resource
    private ChatSessionDOMapper chatSessionDOMapper;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private CodeGPTDrmConfig drmConfig;

    @Resource
    private RefreshableCommonTbaseCacheManager defaultCacheManager;

    @Resource
    private ChatMessageDOMapper chatMessageDOMapper;

    @Resource
    private OssService ossService;

    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;
    @Resource
    private SceneService sceneService;
    @Resource
    private UserSceneRecordsService userSceneRecordsService;

    @Resource(name = "tokenCachePool")
    private Cache<String,TokenDO> tokenCachePool;

    private final PathMatcher pathMatcher=new AntPathMatcher();


    /**
     * 判断是否是管理员
     * 只有在controller层才能调用
     *
     * @return 是否是管理员
     */
    @Override
    public boolean isAdmin() {
        UserAuthDO userAuthDO = ContextUtil.get(ORIGIN_USER, UserAuthDO.class);
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        return userAuthDO.getAdmin() == 2;
    }

    /**
     * 分配管理员权限
     *
     * @param empId 工号
     * @return 是否分配成功
     */
    @Override
    public boolean assignAdmin(String empId, boolean cancelAdmin, byte adminType) {
        if(empId == null){
            LOGGER.info("assignAdmin failed, empId is null");
            return false;
        }

        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdEqualTo(empId);
        List<UserAuthDO> userAuthDOList = userAuthDOMapper.selectByExample(userAuthDOExample);
        if (userAuthDOList.isEmpty()) {
            LOGGER.info("assignAdmin failed, user not found, empId:{}", empId);
            return false;
        }
        UserAuthDO userAuthDO = userAuthDOList.get(0);
        if(cancelAdmin) {
            userAuthDO.setAdmin((byte) 0);
        }else{
            userAuthDO.setAdmin(adminType);
        }
        userAuthDOMapper.updateByPrimaryKeySelective(userAuthDO);
        return true;
    }

    /**
     * 根据token判断是否鉴权通过
     *
     * @param user  用户
     * @param token token
     * @param uri   要访问的uri
     * @return
     */
    @Override
    public boolean isAuthorizedByToken(String user, String token, String uri) {

        if(StringUtils.isBlank(user) || StringUtils.isBlank(token)){
            return false;
        }
        TokenDO tokenDO;
        try {
            tokenDO = tokenCachePool.get(user, () -> {

                TokenDOExample tokenDOExample = new TokenDOExample();
                tokenDOExample.createCriteria().andUserEqualTo(user);
                List<TokenDO> tokenDOList = tokenDOMapper.selectByExample(tokenDOExample);
                if (tokenDOList.isEmpty()) {
                    return null;
                }
                return tokenDOList.get(0);

            });
        } catch (Exception e) {
            LOGGER.info("getCurrentUser failed, token not found, user:{},token:{}", user, token);
            return false;
        }

        if(!tokenDO.getToken().equals(token)){
            return false;
        }

        //将当前用户身份放入上下文
        ContextUtil.set(TOKEN_DO, tokenDO);

        String uriPatternListStr=tokenDO.getUriPatternList();
        List<String> uriPatternList= JSON.parseArray(uriPatternListStr,String.class);
        boolean matched = false;
        for(String pattern: uriPatternList){
            if(pathMatcher.match(pattern,uri)){
                matched = true;
                break;
            }
        }

        return matched;
    }

    /**
     * 获取用户id
     *
     * @return
     */
    @Override
    public UserAuthDO getCurrentUser() {
        return ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
    }

    /**
     * 会话是否属于用户
     *
     * @param uid 会话id
     * @return
     */
    @Override
    public boolean isSessionBelongToUser(String uid) {
        UserAuthDO currentUser = getCurrentUser();
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidEqualTo(uid);
        List<ChatSessionDO> resultList = chatSessionDOMapper.selectByExample(chatSessionDOExample);
        if (resultList.size() != 1) {
            return false;
        }
        return resultList.get(0).getUserId().equals(currentUser.getId());
    }

    @Override
    public boolean isSessionBelongToUser(List<String> uidList) {
        UserAuthDO currentUser = getCurrentUser();
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidIn(uidList);
        List<ChatSessionDO> resultList = chatSessionDOMapper.selectByExample(chatSessionDOExample);
        if (resultList.size() != uidList.size()) {
            return false;
        }
        for (ChatSessionDO chatSessionDO : resultList) {
            if (!chatSessionDO.getUserId().equals(currentUser.getId())) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param uid 消息所属用户ID
     * @return 如果消息属于当前登录用户则返回true，否则返回false
     */
    @Override
    public boolean isMsgBelongToUser(String uid) {
        // 获取当前登录用户信息
        UserAuthDO currentUser = getCurrentUser();
        // 创建查询条件，筛选出属于当前登录用户的消息
        ChatMessageDOExample chatMessageDOExample = new ChatMessageDOExample();
        chatMessageDOExample.createCriteria().andUidEqualTo(uid).andUserIdEqualTo(currentUser.getId());
        // 查询符合条件的消息列表
        List<ChatMessageDO> resultList = chatMessageDOMapper.selectByExample(chatMessageDOExample);
        // 如果查询结果为空，说明当前登录用户并不存在指定ID的消息，因此返回false；反之，返回true
        if (resultList.size() == 0) {
            return false;
        } else {
            return true;
        }
    }


    /**
     * 根据用户empId查询用户
     *
     * @param empId
     * @return
     */
    @Override
    public UserAuthDO queryUserByEmpId(String empId) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andEmpIdEqualTo(empId);
        List<UserAuthDO> userAuthDOList = userAuthDOMapper.selectByExample(userAuthDOExample);
        //根据工号查的话，最多只能查到1个，所以size判断条件是1， 后续如果对接其他登陆系统，此处需要验证
        if (userAuthDOList == null || userAuthDOList.size() != 1) {
            return null;
        }
        return userAuthDOList.get(0);
    }

    @Override
    public UserAuthDO queryUserByToken(String userToken) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andTokenEqualTo(userToken);
        List<UserAuthDO> userAuthDOList = userAuthDOMapper.selectByExample(userAuthDOExample);
        if (userAuthDOList == null || userAuthDOList.size() != 1) {
            return null;
        }
        return userAuthDOList.get(0);
    }

    @Override
    public TokenDO queryTokenOwner(String codeGPTUser) {
        TokenDOExample tokenDOExample = new TokenDOExample();
        tokenDOExample.createCriteria().andUserEqualTo(codeGPTUser);

        List<TokenDO> tokenDOList = tokenDOMapper.selectByExample(tokenDOExample);
        if (tokenDOList == null || tokenDOList.size() != 1) {
            return null;
        }
        return tokenDOList.get(0);
    }


    /**
     * 保存一个新用户，状态默认是排队等待审核
     * @param userName
     * @param empId
     */
    @Override
    public UserAuthDO saveNewUser(String userName, String empId, Integer allowAccessType) {
        UserAuthDO userAuthDO = new UserAuthDO();
        userAuthDO.setEmpId(empId);
        userAuthDO.setUserName(userName);
        userAuthDO.setBuName("");
        String token = ShortUid.getUid();
        userAuthDO.setToken(token);
        userAuthDO.setAdmin((byte) 0);
        userAuthDO.setStatus(UserStatusEnum.VERIFY);
        userAuthDO.setAllowAccessType(allowAccessType);
        userAuthDOMapper.insertSelective(userAuthDO);
        return userAuthDO;
    }

    /**
     * 按状态查询用户数量
     * @param status
     * @return
     */
    @Override
    public long queryUserCountByStatus(UserStatusEnum status) {
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andStatusEqualTo(status);
        return userAuthDOMapper.countByExample(userAuthDOExample);
    }

    @Override
    public List<CodeGptUserModelItemModel> getUserModelInfo() {
        List<AlgoBackendDO> algoBackendDOList;
        if (isAdmin()){
             algoBackendDOList = algoBackendService.getAllAlgoBackend();
        }else {
            algoBackendDOList = (List<AlgoBackendDO>) algoBackendService.getUserAlgoBackend(1, 999999, null).getData();
            algoBackendDOList.addAll(algoBackendService.getAllSeeModel());
        }
        List<CodeGptUserModelItemModel> collect = new ArrayList<>();
        boolean prePub = VisableEnvUtil.isPrePub();
        // 过滤环境不可见模型和已下线模型
        algoBackendDOList = algoBackendDOList.stream()
                .filter(AlgoBackendDO::getEnable)
                .filter(s -> {
                    // 预发环境模型都可见
                    if (prePub) {
                        return true;
                    }
                    // 生产环境过滤掉 仅预发可见模型
                    return !(s.getVisableEnv() == VisableEnvEnum.PREPUB.getCode());
                })
                .collect(Collectors.toList());
        boolean isAdmin = false;
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        // 管理员和超级管理员可见
        if (userAuthDO == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        } else if (userAuthDO.getAdmin() == 1 || userAuthDO.getAdmin() == 2) {
            isAdmin = true;
        }
        //非仅管理员可用的放前面
        algoBackendDOList.sort(Comparator.comparing(o -> o.getVisableUser() != VisableUserEnum.ALL.getCode()));
        for (AlgoBackendDO item : algoBackendDOList) {
            if (item.getVisableUser() == VisableUserEnum.ADMIN.getCode() && !isAdmin) {
                continue;
            }
            if (item.getVisableUser() == VisableUserEnum.NONE.getCode()) {
                continue;
            }
            // 仅管理员可选基座模型
            if (item.getModelBase() && !isAdmin){
                continue;
            }
            collect.add(new CodeGptUserModelItemModel(item.getModel(), item.getModel(),item.getModelType(),item.getSupportPlugin()));
        }
        return collect;
    }


    @Override
    public boolean userHasModelAuthorized(String model) {
        List<CodeGptUserModelItemModel> codeGptUserModelItemModels = getUserModelInfo();
        for (CodeGptUserModelItemModel codeGptUserModelItemModel : codeGptUserModelItemModels) {
            if (model.equalsIgnoreCase(codeGptUserModelItemModel.getValue())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<UserAuthDO> getUserAuthDOByPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return new ArrayList<>();
        }
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andPhoneNumberEqualTo(phoneNumber);
        List<UserAuthDO> userAuthDOList = userAuthDOMapper.selectByExample(userAuthDOExample);
        if (CollectionUtils.isEmpty(userAuthDOList)) {
            return new ArrayList<>();
        }
        return userAuthDOList;
    }

    @Override
    public void notifyOtherProduce(String params) {
        UserAuthDO userAuthDO = ContextUtil.get(ORIGIN_USER, UserAuthDO.class);
        UserLoginNotifyPluginRequest request = new UserLoginNotifyPluginRequest();
        request.setParams(params);
        request.setUserUid(userAuthDO.getEmpId());
        request.setUserType("BUC");
        String result = HttpClient.post(drmConfig.getUserLoginNotifyInnerPluginURL()).content(JSON.toJSONString(request)).syncExecute(
                99999);
        LOGGER.info("notifyOtherProduce inner request:{}", JSON.toJSONString(request));
        LOGGER.info("notifyOtherProduce inner result:{}", result);
    }

    /**
     * 用户收藏场景助手
     *
     * @param sceneId 场景助手 id
     * @param cancel 取消收藏
     * @return
     */
    @Override
    public Boolean userSaveScene(Long sceneId,Boolean cancel) {
        UserAuthDO user = this.getCurrentUser();
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andIdEqualTo(user.getId());
        UserAuthDO userAuth = new UserAuthDO();
        List<Long> sceneIds ;
        if (!cancel){
            if (StringUtil.isBlank(user.getSaveScenes())) {
                sceneIds = Collections.singletonList(sceneId);
            } else {
                String saveScenes = user.getSaveScenes();
                sceneIds = JSONArray.parseArray(saveScenes,Long.class);
                sceneIds.add(sceneId);
            }
        }else {
            String saveScenes = user.getSaveScenes();
            sceneIds = JSONArray.parseArray(saveScenes,Long.class);
            sceneIds.remove(sceneId);
        }
        sceneIds = sceneIds.stream().distinct().collect(Collectors.toList());
        userAuth.setSaveScenes(JSON.toJSONString(sceneIds));
        userAuth.setId(user.getId());
        //清除缓存
        int update = userAuthDOMapper.updateByExampleSelective(userAuth, userAuthDOExample);
        defaultCacheManager.del(AppConstants.CACHE_PREFIX + user.getEmpId());
        return update == 1;
    }

    @Override
    public boolean topSession(String sessionUid, Boolean cancel) {
        UserAuthDO currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        LinkedList<String> topSessionUids = new LinkedList<>();
        if (StringUtils.isNotBlank(currentUser.getTopSessionUids())) {
            topSessionUids.addAll(JSON.parseArray(currentUser.getTopSessionUids(), String.class));
        }
        // 如果本身已经在置顶列表里,那先删除,再重新添加到列表头
        topSessionUids.remove(sessionUid);
        topSessionUids.addFirst(sessionUid);
        // 把已经删除的会话剔除
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUidIn(topSessionUids).andDeletedEqualTo((byte) 1);
        List<ChatSessionDO> resultList = chatSessionDOMapper.selectByExample(chatSessionDOExample);
        if (CollectionUtil.isNotEmpty(resultList)) {
            topSessionUids.removeAll(resultList.stream().map(ChatSessionDO::getUid).collect(Collectors.toList()));
        }
        if (cancel) {
            topSessionUids.remove(sessionUid);
        }
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andIdEqualTo(currentUser.getId());
        UserAuthDO userAuth = new UserAuthDO();
        userAuth.setTopSessionUids(JSON.toJSONString(topSessionUids));
        int update = userAuthDOMapper.updateByExampleSelective(userAuth, userAuthDOExample);
        defaultCacheManager.del(AppConstants.CACHE_PREFIX + currentUser.getEmpId());
        return update == 1;
    }

    @Override
    public void clearCurrentUserAllData() {
        UserAuthDO user = getCurrentUser();
        // 1、删除所有会话
        ChatSessionDOExample chatSessionDOExample = new ChatSessionDOExample();
        chatSessionDOExample.createCriteria().andUserIdEqualTo(user.getId());
        ChatSessionDO record = new ChatSessionDO();
        record.setDeleted((byte) 1);
        chatSessionDOMapper.updateByExampleSelective(record, chatSessionDOExample);
        // 2、取消所有收藏的助手
        UserAuthDOExample userAuthDOExample = new UserAuthDOExample();
        userAuthDOExample.createCriteria().andIdEqualTo(user.getId());
        UserAuthDO userAuth = new UserAuthDO();
        userAuth.setSaveScenes(JSON.toJSONString(new ArrayList<>()));
        userAuthDOMapper.updateByExampleSelective(userAuth, userAuthDOExample);
        //清除缓存
        defaultCacheManager.del(AppConstants.CACHE_PREFIX + user.getEmpId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createScene(SceneVO scene, UserAuthDO userAuthDO) {
        SceneDO sceneDO = new SceneDO();
        BeanUtil.copyProperties(scene, sceneDO);
        sceneDO.setUserId(userAuthDO.getId());
        Long ownerUserId = userAuthDO.getId();
        if (StringUtils.isNotBlank(scene.getOwnerUserEmpId())) {
            codeFuseUserAuthService.insertUserAuth(Lists.newArrayList(scene.getOwnerUserEmpId()));
            ownerUserId = codeFuseUserAuthService.getUserByEmpIds(Lists.newArrayList(scene.getOwnerUserEmpId())).get(0).getId();
        }
        sceneDO.setOwnerUserId(ownerUserId);
        sceneDO.setEnable(0);
        if (sceneDO.getVisableUser() == SceneVisableUserEnum.ALL.getCode()) {
            // 如果是要创建全部人可见的助手,需要手动提交审核,审核通过够才能启用
            sceneDO.setAuditStatus(AuditStatusSceneEnum.NONE.getCode());
        } else {
            // 如果要创建不是全员可见的话,就不需要审核
            sceneDO.setAuditStatus(AuditStatusSceneEnum.PAAS.getCode());
        }
        Long sceneId = sceneService.addScene(sceneDO);
        SceneDO sceneDODb = sceneService.getSceneById(sceneId);
        if (CollectionUtil.isNotEmpty(scene.getViewUserEmpIdList())) {
            userSceneRecordsService.batchInsertUserScene(sceneDODb, scene.getViewUserEmpIdList(), ControlTypeEnum.SEE);
        }
        if (CollectionUtil.isNotEmpty(scene.getEditUserEmpIdList())) {
            userSceneRecordsService.batchInsertUserScene(sceneDODb, scene.getEditUserEmpIdList(), ControlTypeEnum.UPDATE);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateScene(SceneVO scene, UserAuthDO userAuthDO) {
        boolean hasAuth = sceneService.editableSceneUser(scene.getId(), userAuthDO.getId());
        if (!hasAuth) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        SceneDO sceneDO = new SceneDO();
        BeanUtil.copyProperties(scene, sceneDO);
        SceneDO sceneDODb = sceneService.getSceneById(sceneDO.getId());
        Long ownerUserId = userAuthDO.getId();
        if (StringUtils.isNotBlank(scene.getOwnerUserEmpId())) {
            codeFuseUserAuthService.insertUserAuth(Lists.newArrayList(scene.getOwnerUserEmpId()));
            ownerUserId = codeFuseUserAuthService.getUserByEmpIds(Lists.newArrayList(scene.getOwnerUserEmpId())).get(0).getId();
        }
        sceneDO.setOwnerUserId(ownerUserId);
        // 3.15 agent默认更改为审核通过
        sceneDO.setAuditStatus(AuditStatusSceneEnum.PAAS.getCode());
        // 新增工具指令集
        if(CollectionUtils.isNotEmpty(scene.getPluginCommandList())){
            sceneDO.setPluginCommand(JSONObject.toJSONString(scene.getPluginCommandList()));
        }
        if (scene.getModel() != null) {
            AlgoBackendDO algoBackendByName = algoBackendService.getAlgoBackendByName(scene.getModel());
            if (algoBackendByName != null && !algoBackendByName.getSupportPlugin()){
                sceneDO.setPluginList("");
                sceneDO.setMode(0);
                sceneDO.setFunctionCallConfig("");
            }
            else if (algoBackendByName != null && StrUtil.isNotBlank(scene.getPluginList()) && algoBackendByName.getSupportPlugin()) {
                FunctionCallConfig functionCallConfig = sceneService.getFunctionCallConfig(sceneDODb);
                functionCallConfig.setFunctionCallModel(sceneDO.getModel());
                functionCallConfig.setCallFunctionEveryRound(scene.getCallFunctionEveryRound());
                if(functionCallConfig.getCallFunctionEveryRound() == null){
                    functionCallConfig.setCallFunctionEveryRound(false);
                }
                sceneDO.setFunctionCallConfig(JSONObject.toJSONString(functionCallConfig));
                sceneDO.setMode(1);
            }
        }
        LOGGER.info("updateSceneById ,operator empId:{} sceneDODb:{}", userAuthDO.getId(), JSON.toJSONString(sceneDODb));
        sceneService.updateSceneById(sceneDO);
        // 删除助手的全部用户权限
        userSceneRecordsService.deleteSceneUserControl(sceneDO.getId(),null);
        // 按照最新提交的用户权限重新添加
        if (CollectionUtil.isNotEmpty(scene.getViewUserEmpIdList())) {
            userSceneRecordsService.batchInsertUserScene(sceneDODb, scene.getViewUserEmpIdList(), ControlTypeEnum.SEE);
        }
        if (CollectionUtil.isNotEmpty(scene.getEditUserEmpIdList())) {
            userSceneRecordsService.batchInsertUserScene(sceneDODb, scene.getEditUserEmpIdList(), ControlTypeEnum.UPDATE);
        }
    }

    @Override
    public UserAuthDO selectByUserId(Long userId) {
        if (userId == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        return userAuthDOMapper.selectByPrimaryKey(userId);
    }

    @Override
    public void updateSceneAuthStatus(SceneVO scene, UserAuthDO userAuthDO) {
        boolean hasAuth = sceneService.editableSceneUser(scene.getId(), userAuthDO.getId());
        if (!hasAuth) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        // 拦截全员可见
        if(scene.getVisableUser() !=null && scene.getVisableUser() == 2 && userAuthDO.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
            throw new BizException(ResponseEnum.NO_AUTH,"要设置全员可见，请联系云辰审核！");
        }
        SceneDO sceneDO = new SceneDO();
        sceneDO.setId(scene.getId());
        sceneDO.setEnable(scene.getEnable());
        sceneDO.setPluginEnable(scene.getPluginEnable());
        sceneDO.setVisableUser(scene.getVisableUser());
        // 3.15 agent默认更改为审核通过
        sceneDO.setAuditStatus(AuditStatusSceneEnum.PAAS.getCode());
        sceneService.updateSceneById(sceneDO);
    }
}
