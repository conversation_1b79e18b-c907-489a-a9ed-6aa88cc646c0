package com.alipay.codegencore.service.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.alipay.codegencore.model.openai.CodeInfo;
import com.alipay.codegencore.model.openai.GenCodeRequest;
import com.alipay.codegencore.model.openai.Plan;
import com.github.difflib.DiffUtils;
import com.github.difflib.patch.AbstractDelta;
import com.github.difflib.patch.Chunk;
import com.github.difflib.patch.DeltaType;
import com.github.difflib.patch.Patch;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * diff 工具类
 */
public class Action2CodeUtil {
    /**
     * 把diff转换成markdown文本
     * @param oldCode
     * @param newCode
     * @return
     */
    public static String generateDiffMarkdown(String oldCode, String newCode) {
        List<String> oldLines = Arrays.asList(oldCode.split("\n"));
        List<String> newLines = Arrays.asList(newCode.split("\n"));

        Patch<String> patch = DiffUtils.diff(oldLines, newLines);

        StringBuilder diffBuilder = new StringBuilder();
        diffBuilder.append("```diff\n");

        int oldLineNumber = 0;

        for (AbstractDelta<String> delta : patch.getDeltas()) {
            Chunk<String> source = delta.getSource();
            Chunk<String> target = delta.getTarget();

            // Add unchanged lines before the diff
            while (oldLineNumber < source.getPosition()) {
                diffBuilder.append(" ").append(oldLines.get(oldLineNumber)).append("\n");
                oldLineNumber++;
            }

            if (delta.getType() == DeltaType.DELETE || delta.getType() == DeltaType.CHANGE) {
                for (String line : source.getLines()) {
                    diffBuilder.append("-").append(line).append("\n");
                    oldLineNumber++;
                }
            }

            if (delta.getType() == DeltaType.INSERT || delta.getType() == DeltaType.CHANGE) {
                for (String line : target.getLines()) {
                    diffBuilder.append("+").append(line).append("\n");
                }
            }
        }

        // Add remaining unchanged lines
        while (oldLineNumber < oldLines.size()) {
            diffBuilder.append(" ").append(oldLines.get(oldLineNumber)).append("\n");
            oldLineNumber++;
        }

        diffBuilder.append("```");
        return diffBuilder.toString();
    }

    public static String generateReport(List<Plan> planList, List<CodeInfo> codeList, GenCodeRequest genCodeRequest) {
        StringBuilder report = new StringBuilder();
        report.append("# 代码变更报告\n\n");

        if (CollectionUtil.isEmpty(planList) && CollectionUtil.isEmpty(codeList)) {
            report.append("AI助手并未生成可用的变更\n");
            return report.toString();
        }

        // Group plans by filePath
        Map<String, List<Plan>> plansByFile = planList.stream()
                .collect(Collectors.groupingBy(Plan::getFilePath));

        // Group codeInfos by filePath
        Map<String, List<CodeInfo>> codeInfoByFile = codeList.stream()
                .collect(Collectors.groupingBy(CodeInfo::getFilePath));

        // Combine all filePaths
        Set<String> allFilePaths = new HashSet<>(plansByFile.keySet());
        allFilePaths.addAll(codeInfoByFile.keySet());

        int f = 0;
        for (String filePath : allFilePaths) {
            report.append("## 文件").append(++f).append("：\n\n")
                    .append("[**").append(filePath).append("**](")
                    .append("https://code.alipay.com/")
                    .append(genCodeRequest.getRepoInfo().getRepoPath())
                    .append("/blob/")
                    .append(genCodeRequest.getRepoInfo().getBranch())
                    .append("/")
                    .append(filePath)
                    .append(")\n\n");

            // Add plan information
            List<Plan> plans = plansByFile.get(filePath);
            if (CollectionUtils.isNotEmpty(plans)) {
                report.append("### 变更计划：\n\n");
                for (int p = 0; p < plans.size(); p++) {
                    Plan plan = plans.get(p);
                    report.append(p+1).append(". ").append(plan.getStep()).append("\n");
                }
                report.append("\n");
            }

            // Add code diff
            List<CodeInfo> codeInfoList = codeInfoByFile.get(filePath);
            if (CollectionUtils.isNotEmpty(codeInfoList)) {
                report.append("### 具体变更:\n\n");
                for (CodeInfo codeInfo : codeInfoList) {
                    String diff = generateDiffMarkdown(codeInfo.getOldCode(), codeInfo.getNewCode());
                    report.append(diff).append("\n\n");
                }
            }
        }

        return report.toString();
    }
}
