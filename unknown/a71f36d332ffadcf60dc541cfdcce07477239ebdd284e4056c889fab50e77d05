package com.alipay.codegencore.model.tool.learning;

/**
 * 工作流配置校验结果
 * <AUTHOR>
 */
public class WorkflowConfigCheckResult {
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 错误信息
     */
    private String message;

    /**
     * yaml中的位置
     */
    private String location;

    /**
     * 错误码
     */
    private int errorCode;

    public WorkflowConfigCheckResult() {
    }

    /**
     * 构造函数
     * @param success 是否成功
     * @param message 错误信息
     * @param location yaml中的位置
     */
    public WorkflowConfigCheckResult(boolean success, String message, String location) {
        this.success = success;
        this.message = message;
        this.location = location;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
