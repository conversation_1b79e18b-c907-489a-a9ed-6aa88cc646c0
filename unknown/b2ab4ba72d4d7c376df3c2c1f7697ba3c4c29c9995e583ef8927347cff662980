package com.alipay.codegencore.web.codegpt;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.common.logging.Logger;
import com.alibaba.common.logging.LoggerFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.model.FormUploadFileResponse;
import com.alipay.codegencore.model.model.SessionFileResponse;
import com.alipay.codegencore.model.model.SessionFileSummaryRequest;
import com.alipay.codegencore.model.openai.ChatSessionVO;
import com.alipay.codegencore.model.openai.SceneConfigVO;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.TbaseCacheService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 利用ai模型进行对话的controller
 *
 * <AUTHOR>
 * 创建时间 2022-02-28
 */
@Slf4j
@RestController
@CodeTalkWebApi
@RequestMapping("/webapi/session")
public class ChatSessionManageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChatSessionManageController.class);

    @Resource
    private ChatSessionManageService chatSessionManageService;

    @Resource
    private UserAclService userAclService;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Resource
    private AlgoBackendService algoBackendService;

    @Resource
    private ChatMessageService chatMessageService;

    @Resource
    private SceneService sceneService;

    @Resource
    private TbaseCacheService tbaseCacheService;

    @Resource
    private DocumentHandleService documentHandleService;

    @Resource
    private SecAgentHelper secAgentHelper;

    /**
     * 测试的case
     * @return
     */
    @RequestMapping("/test")
    public BaseResponse<String> test(){
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if (currentUser == null) {
            return BaseResponse.build(ResponseEnum.USER_NOT_LOGIN);
        }
        return BaseResponse.build(currentUser.getEmpId());
    }

    /**
     * 创建会话
     *
     * @param sceneId 场景id
     * @param prompt 会话提示语，用于约束模型的表现
     * @return 创建好的会话信息
     */
    @PostMapping(value = "/create")
    public BaseResponse<ChatSessionDO> create(@RequestParam(required = false) String prompt,
                                              @RequestParam(required = false) String title,
                                              @RequestParam(required = false) Long sceneId,
                                              @RequestParam(required = false) String sourcePlatform,
                                              @RequestParam(defaultValue = "false") Boolean sceneTest,
                                              @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser) {
        if(title!=null && (title.length()<3|| title.length()> 20)){
            return BaseResponse.build(ResponseEnum.SESSION_TITLE_LENTH_ILLEGAL, "要求会话长度在2-20之间");
        }

        String platform = sourcePlatform;
        if (StringUtils.isBlank(sourcePlatform)){
            // 如果没有传sourcePlatform，并且codeGPTUser头部不为空，则使用codeGPTUser头部作为来源平台
            if(StringUtils.isNotBlank(codeGPTUser)){
                platform = codeGPTUser;
            }else{
                platform = "codeFuse";
            }
        }
        UserAuthDO currentUser = userAclService.getCurrentUser();
        ChatSessionDO chatSessionDO = chatSessionManageService.getNewSession(currentUser.getId(), prompt, title,sceneId,sceneTest,platform,null,false);
        return BaseResponse.build(chatSessionDO);
    }

    /**
     * 获取一个空会话
     *
     * @param sceneId 场景id
     * @param prompt 会话提示语，用于约束模型的表现
     * @return 创建好的会话信息
     */
    @PostMapping(value = "/getNewSession")
    public BaseResponse<ChatSessionDO> getNewSession(@RequestParam(required = false) String prompt,
                                              @RequestParam(required = false) String title,
                                              @RequestParam(required = false) Long sceneId,
                                              @RequestParam(required = false) String sourcePlatform,
                                              @RequestParam(defaultValue = "false") Boolean modelTest,
                                              @RequestParam(required = false) Long modelId,
                                              @RequestHeader(value = "codegpt_user", required = false) String codeGPTUser) {
        if(title!=null && (title.length()<3|| title.length()> 20)){
            return BaseResponse.build(ResponseEnum.SESSION_TITLE_LENTH_ILLEGAL, "要求会话长度在2-20之间");
        }

        String platform = sourcePlatform;
        if (StringUtils.isBlank(sourcePlatform)){
            // 如果没有传sourcePlatform，并且codeGPTUser头部不为空，则使用codeGPTUser头部作为来源平台
            if(StringUtils.isNotBlank(codeGPTUser)){
                platform = codeGPTUser;
            }else{
                platform = "codeFuse";
            }
        }
        UserAuthDO currentUser = userAclService.getCurrentUser();
        ChatSessionDO chatSessionDO = chatSessionManageService.getNewSession(currentUser.getId(), prompt, title,sceneId,modelTest,platform,modelId,false);
        return BaseResponse.build(chatSessionDO);
    }
    /**
     * 更新会话名称
     * @param uid
     * @param newTitle
     * @return
     */
    @PostMapping(value = "/updateTitle")
    public BaseResponse<Integer> updateTitle(@RequestParam String uid, @RequestParam String newTitle) {
        if (!userAclService.isSessionBelongToUser(uid)) {
            log.info("session:{} does not belong to current user", uid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", uid));
        }
        if(newTitle==null || newTitle.length()<2|| newTitle.length()> 20){
            return BaseResponse.build(ResponseEnum.SESSION_TITLE_LENTH_ILLEGAL, "要求会话长度在2-20之间");
        }
        if (!chatSessionManageService.checkUserUpdateTitle(userAclService.getCurrentUser(), uid, newTitle)) {
            return BaseResponse.build(ResponseEnum.CHECK_FAILED);
        }
        int updatedSessionCount = chatSessionManageService.updateSessionTitle(uid, newTitle);
        if (updatedSessionCount != 1) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
        }
        return BaseResponse.buildSuccess();
    }


    /**
     * 更新会话使用的AI模型
     * @param uid 会话uid
     * @param model 模型名称
     * @return  更新的会话数量
     */
    @PostMapping(value = "/updateModel")
    public BaseResponse<Integer> updateModel(@RequestParam String uid, @RequestParam String model) {
        if (!userAclService.isSessionBelongToUser(uid)) {
            log.info("session:{} does not belong to current user", uid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", uid));
        }

        AlgoBackendDO algoBackendDO = algoBackendService.getAlgoBackendByName(model);
        if (algoBackendDO == null) {
            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER, String.format("模型:%s 暂不支持", model));
        }
        if (!algoBackendDO.getEnable()) {
            return BaseResponse.build(ResponseEnum.MODEL_UNSERVICEABLE);
        }

        int updatedSessionCount = chatSessionManageService.updateSessionModelType(uid, model);
        return BaseResponse.build(updatedSessionCount);
    }

    /**
     * 更新会话使用的助手id
     *
     * @param uid     会话uid
     * @param sceneId sceneId
     * @return
     */
    @PostMapping(value = "/updateSessionScene")
    public BaseResponse<Boolean> updateSessionScene(@RequestParam String uid, @RequestParam Long sceneId) {
        if (!userAclService.isSessionBelongToUser(uid)) {
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH);
        }
        List<ChatMessageDO> chatMessageDOS = chatMessageService.listChatMessage(uid, false, false);
        if (CollectionUtil.isNotEmpty(chatMessageDOS)) {
            return BaseResponse.build(ResponseEnum.SESSION_HAS_MESSAGE_CAN_NOT_UPDATE_MODEL);
        }
        ChatSessionDO chatSession = chatSessionManageService.getChatSession(uid);
        SceneDO scene = sceneService.getSceneById(sceneId);
        if (scene == null) {
            return BaseResponse.build(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        ChatSessionDO chatSessionDO = new ChatSessionDO();
        chatSessionDO.setUid(uid);
        chatSessionDO.setSceneId(sceneId);
        chatSessionDO.setModel(scene.getModel());
        chatSessionDO.setSourcePlatform(chatSession.getSourcePlatform());
        if (StrUtil.isBlank(scene.getModel())) {
            AlgoBackendDO defaultAlgoBackendDO = algoBackendService.getDefaultAlgoBackend();
            chatSessionDO.setModel(defaultAlgoBackendDO.getModel());
        }
        // 集成agentSecSdk.threadCreate, 判断用户是否有agent使用权限, 根据agentSecSdk.threadCreate返回结果作相应处理
        secAgentHelper.createAgentSessionCheck(chatSessionDO);
        Boolean updateSession = chatSessionManageService.updateSession(chatSessionDO);
        return BaseResponse.build(updateSession);
    }


    /**
     * 获取指定会话模型配置
     *
     * @param sessionUid
     * @param sceneId
     * @param defaultConfig 是否获取原始配置
     * @return
     */
    @GetMapping(value = "/getSessionModelConfig")
    public BaseResponse<List<SceneConfigVO>> getSessionModelConfig(@RequestParam String sessionUid,
                                                                   @RequestParam(required = false) Long sceneId,
                                                                   @RequestParam(defaultValue = "false") Boolean defaultConfig) {
        if (!userAclService.isAdmin() && !userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        List<SceneConfigVO> sceneConfig = chatSessionManageService.getSessionModelConfig(sessionUid, sceneId, defaultConfig);
        return BaseResponse.build(sceneConfig);
    }

    /**
     * 更新回话的模型配置
     * 只会在会话中没有消息的时候才可以更新成功
     *
     * @param sessionUid
     * @param requestBean
     * @return
     */
    @PostMapping(value = "/updateSessionModelConfig")
    public BaseResponse<Integer> updateSessionModelConfig(@RequestParam String sessionUid, @RequestBody JSONObject requestBean) {
        int updatedSessionCount = chatSessionManageService.updateSessionModelConfig(sessionUid, requestBean);
        return BaseResponse.build(updatedSessionCount);
    }

    /**
     * 获取会话列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public BaseResponse<List<ChatSessionVO>> list(@RequestParam(required = false) String sourcePlatform) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        LinkedList<String> topSessionUids = new LinkedList<>();
        if (StringUtils.isNotBlank(currentUser.getTopSessionUids())) {
            topSessionUids.addAll(JSON.parseArray(currentUser.getTopSessionUids(), String.class));
        }
        LinkedList<ChatSessionVO> chatSessionVOS = new LinkedList<>();
        if (CollectionUtil.isNotEmpty(topSessionUids)) {
            List<ChatSessionDO> topSessionList = chatSessionManageService.getSessionByIdList(topSessionUids);
            Map<String,ChatSessionDO> topSessionMap = topSessionList.stream().collect(
                    Collectors.toMap(ChatSessionDO::getUid, chatSessionDO -> chatSessionDO));
            topSessionUids.forEach(s -> {
                        ChatSessionDO chatSessionDO = topSessionMap.get(s);
                        if (chatSessionDO != null) {
                            ChatSessionVO chatSessionVO = new ChatSessionVO();
                            BeanUtil.copyProperties(chatSessionDO, chatSessionVO);
                            chatSessionVO.setTopSession(true);
                            // 根据会话sourcePlatform筛选
                            if(StringUtils.isNotBlank(sourcePlatform)){
                                if(StringUtils.equalsIgnoreCase(chatSessionDO.getSourcePlatform(),sourcePlatform)){
                                    chatSessionVOS.add(chatSessionVO);
                                }
                            }else {
                                chatSessionVOS.add(chatSessionVO);
                            }

                        }
                    }
            );
        }
        List<ChatSessionDO> chatSessionDOList = chatSessionManageService.listChatSession(currentUser.getId());
        chatSessionDOList.stream()
                .filter(chatSessionDO -> !topSessionUids.contains(chatSessionDO.getUid()))
                .forEach(chatSessionDO -> {
                    ChatSessionVO chatSessionVO = new ChatSessionVO();
                    BeanUtil.copyProperties(chatSessionDO, chatSessionVO);
                    chatSessionVO.setTopSession(false);
                    // 根据会话sourcePlatform筛选
                    if(StringUtils.isNotBlank(sourcePlatform)){
                        if(StringUtils.equalsIgnoreCase(chatSessionDO.getSourcePlatform(),sourcePlatform)){
                            chatSessionVOS.add(chatSessionVO);
                        }
                    }else {
                        chatSessionVOS.add(chatSessionVO);
                    }
                });
        List<ChatSessionVO> result = chatSessionVOS.stream().limit(codeGPTDrmConfig.getSessionListMaxSize()).collect(Collectors.toList());
        return BaseResponse.build(result);
    }



    /**
     * 删除会话
     * @param uid 会话uid
     */
    @PostMapping(value = "/delete")
    public BaseResponse delete(@RequestParam String uid) {
        if (!userAclService.isSessionBelongToUser(uid)) {
            log.info("session:{} does not belong to current user", uid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", uid));
        }
        chatSessionManageService.deleteSession(Lists.newArrayList(uid));
        return BaseResponse.buildSuccess();
    }

    /**
     * 批量删除会话
     * @param param 会话uid列表
     */
    @PostMapping(value = "/batchDelete")
    public BaseResponse batchDelete(@RequestBody JSONObject param) {
        List<String> uidList = JSON.parseArray(param.getJSONArray("uidList").toJSONString(), String.class);
        if (!userAclService.isSessionBelongToUser(uidList)) {
            log.info("session:{} does not belong to current user", JSON.toJSONString(uidList));
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("session:%s does not belong to current user", JSON.toJSONString(uidList)));
        }
        chatSessionManageService.deleteSession(uidList);
        return BaseResponse.buildSuccess();
    }


    /**
     * 删除全部会话
     */
    @PostMapping(value = "/clear")
    public BaseResponse clear() {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        chatSessionManageService.clearSession(currentUser.getId());
        return BaseResponse.buildSuccess();
    }

    /**
     * 获取提问模板列表
     * @return 提问模板列表
     */
    @GetMapping(value = "getQueryTemplateList")
    public BaseResponse<List<JSONObject>> getQueryTemplateList(){
        String queryTemplateListStr = codeGPTDrmConfig.getCodeGPTQueryTemplateList();
        return BaseResponse.build(JSON.parseArray(queryTemplateListStr, JSONObject.class));
    }

    /**
     * 给会话上传文件
     * @param sessionUid
     * @param file
     * @return
     */
    @PostMapping(value = "/uploadSessionFile")
    public BaseResponse<SessionFileResponse> uploadSessionFile(@RequestParam String sessionUid, @RequestParam MultipartFile file) {
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        return BaseResponse.build(chatSessionManageService.uploadSessionFile(sessionUid, file));
    }

    /**
     * 上传文件接口
     * @param file
     * @return
     */
    @PostMapping(value = "/formUploadFile")
    public BaseResponse<FormUploadFileResponse> formUploadFile(@RequestParam MultipartFile file) {
        return BaseResponse.build(chatSessionManageService.formUploadFile(file));
    }

    /**
     * 删除会话关联的文件
     * @param sessionUid
     * @param fileUid
     * @return
     */
    @PostMapping(value = "/deleteSessionFile")
    public BaseResponse<Boolean> deleteSessionFile(@RequestParam String sessionUid, @RequestParam String fileUid) {
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        return BaseResponse.build(chatSessionManageService.deleteSessionFile(sessionUid, fileUid));
    }

    /**
     * 获取需要配置的助手类型
     * @return
     */
    @GetMapping(value = "getSceneConfigType")
    public BaseResponse<JSONArray> getSceneConfigType() {
        JSONObject documentChatConfig = JSON.parseObject(codeGPTDrmConfig.getDocumentChatConfig());
        Integer documentChatSceneId = documentChatConfig.getInteger("documentChatSceneId");
        JSONArray retJson = new JSONArray();
        JSONObject documentChatScene = new JSONObject();
        documentChatScene.put("sceneId", documentChatSceneId);
        documentChatScene.put("sceneType", "DocumentChat");
        retJson.add(documentChatScene);
        return BaseResponse.build(retJson);
    }

    /**
     * 绑定文件到会话上，然后获取会话绑定文件的总结
     * @param sessionFileSummaryRequest
     * @return 总结文本
     */
    @PostMapping("/bindSessionFileAndGetSummary")
    public BaseResponse<String> bindSessionFileAndGetSummary(@RequestBody SessionFileSummaryRequest sessionFileSummaryRequest) {
        if (!userAclService.isSessionBelongToUser(sessionFileSummaryRequest.getSessionUid())) {
            log.info("session:{} does not belong to current user", sessionFileSummaryRequest.getSessionUid());
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionFileSummaryRequest.getSessionUid()));
        }
        return BaseResponse.build(chatMessageService.bindSessionFileAndGetSummary(sessionFileSummaryRequest));
    }


    /**
     * 获取指定会话绑定的文件列表的方法
     *
     * @param sessionUid 会话UID，必填参数
     * @return 包含文件列表的响应对象，其中每个元素对应一个文件记录
     */
    @GetMapping("/getSessionFileList")
    public BaseResponse<List<SessionFileResponse>> getSessionFileList(@RequestParam String sessionUid) {
        // 判断当前用户是否有权限访问该会话
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            // 如果没有权限，则返回无权限错误响应
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        // 返回包含文件列表的响应对象
        return BaseResponse.build(chatMessageService.getSessionFileResponse(sessionUid));
    }

    /**
     * 向cg_chat_session的ext_info字段添加配置信息
     * sessionExtInfo的key存在ext_info中就是更新，不存在就是添加
     * @param sessionUid 会话UID
     * @param sessionExtInfo 需要添加的配置信息
     * @return
     */
    @PostMapping("/addSessionExtInfo")
    public BaseResponse<Boolean> addSessionExtInfo(@RequestParam String sessionUid, @RequestBody JSONObject sessionExtInfo) {
        // 判断当前用户是否有权限访问该会话
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        return BaseResponse.build(chatMessageService.addSessionExtInfo(sessionUid, sessionExtInfo));
    }

    /**
     * 获取cg_chat_session的ext_info
     * 返回全量的json，前端再get具体需要的
     * @param sessionUid
     * @return
     */
    @GetMapping("/getSessionExtInfo")
    public BaseResponse<JSONObject> getSessionExtInfo(@RequestParam String sessionUid) {
        // 判断当前用户是否有权限访问该会话
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        return BaseResponse.build(chatMessageService.getSessionExtInfo(sessionUid));
    }

    /**
     * 删除cg_chat_session的ext_info字段中的第一层key信息
     * @param sessionUid
     * @param keys
     * @return
     */
    @PostMapping("/deleteSessionExtInfo")
    public BaseResponse<Boolean> deleteSessionExtInfo(@RequestParam String sessionUid, @RequestBody List<String> keys) {
        // 判断当前用户是否有权限访问该会话
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        return BaseResponse.build(chatMessageService.deleteSessionExtInfo(sessionUid, keys));
    }

    /**
     * 等待session会话处理完毕
     * @param sessionUid
     * @return
     */
    @GetMapping("/waitProcessSession")
    public BaseResponse<Object> waitProcessSession(@RequestParam String sessionUid) {
        // 判断当前用户是否有权限访问该会话
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        // 拿到锁之后只持有1ms
        boolean lock = tbaseCacheService.getLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid, 1);
        if (lock) {
            return BaseResponse.buildSuccess();
        }
        // 尝试等待60秒获取锁,拿到锁之后只持有1ms
        tbaseCacheService.getLock(AppConstants.CODEGENCORE_TBASE_KEY_SESSION_UID + sessionUid, 1, 60 * 1000);
        return BaseResponse.buildSuccess();
    }

    /**
     * 上传文件到会话上
     * @param sessionUid
     * @param document
     * @return
     */
    @PostMapping("/uploadDocument")
    public BaseResponse<Object> uploadFile(@RequestParam String sessionUid, @RequestParam MultipartFile document, @RequestParam(required = false) String segmentationStrategy) {
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        // 判断当前用户是否有权限访问该会话
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        documentHandleService.uploadSessionDocument(sessionUid, Lists.newArrayList(document), userAuthDO, segmentationStrategy);
        return BaseResponse.buildSuccess();
    }

    /**
     * 会话文档解绑
     * @param sessionUid
     * @param documentUid 文档UID
     * @return
     */
    @PostMapping("/unbindDocument")
    public BaseResponse<Object> unbindDocument(@RequestParam String sessionUid, @RequestParam String documentUid) {
        // 判断当前用户是否有权限访问该会话
        if (!userAclService.isSessionBelongToUser(sessionUid)) {
            log.info("session:{} does not belong to current user", sessionUid);
            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH, String.format("会话:%s 不属于当前用户", sessionUid));
        }
        chatSessionManageService.unbindDocument(sessionUid, documentUid);
        return BaseResponse.buildSuccess();
    }

}
