package com.alipay.codegencore.model.response.linke;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.model.response.linke
 * @CreateTime : 2024-04-10
 */
public class ApprovalUser {

    /**
     * id
     */
    private Long id;

    /**
     * 访问级别
     */
    private Long   accessLevel;
    /**
     * 失效
     */
    private String expiresAt;
    /**
     * 头像地址
     */
    private String avatarUrl;
    /**
     * Web地址
     */
    private String webUrl;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态
     */
    private String state;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 外部UID
     */
    private String externUid;
    /**
     * 用户名
     */
    private String username;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccessLevel() {
        return accessLevel;
    }

    public void setAccessLevel(Long accessLevel) {
        this.accessLevel = accessLevel;
    }

    public String getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(String expiresAt) {
        this.expiresAt = expiresAt;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getExternUid() {
        return externUid;
    }

    public void setExternUid(String externUid) {
        this.externUid = externUid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
