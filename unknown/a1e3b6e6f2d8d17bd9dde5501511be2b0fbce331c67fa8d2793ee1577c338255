package com.alipay.codegencore.service.common;

import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.openai.ChatRequestExtData;

/**
 * 数据安全审查service
 */
public interface DataCheckService {

    /**
     * 数据安全检查,通过keymap,后续不再使用
     * @param messageUid 消息id
     * @param batch 批次
     * @param text 检测的内容文本
     * @return ReviewResultModel,
     */
    @Deprecated
    ReviewResultModel checkData(String messageUid, Integer batch, String text);

    /**
     * 数据安全审查,通过antdsr
     * @param messageUid 消息id
     * @param text 文本内容
     * @param batch 批次
     * @param chatRequestExtData 额外参数
     * @return 审核结果
     */
    ReviewResultModel antDsrCheckData(String messageUid, Integer batch, String text, ChatRequestExtData chatRequestExtData);

}
