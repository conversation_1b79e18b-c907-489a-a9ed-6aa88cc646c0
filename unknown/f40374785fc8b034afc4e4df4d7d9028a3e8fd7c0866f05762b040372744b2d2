package com.alipay.codegencore.utils.codefuse;

import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class PromptCommonUtilsTest {

    @Test
    public void buildPrompt() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("name", "vat");
        String result = PromptCommonUtils.buildPrompt("模板测试${name}", params);
        Assert.assertEquals(result, "模板测试vat");
    }


    @Test
    public void buildPrompt1() {
        Map<String, Object> params = Maps.newHashMap();
        List<Name> names = new ArrayList<>();
        Name name = new Name();
        name.setAge(19);
        name.setName("zhunian");
        names.add(name);
        Name name1 = new Name();
        name1.setName("张健");
        name1.setAge(91);
        names.add(name1);
        params.put("names", names);
        params.put("query", "query");
        String template = "用户需求：${query}\n" +
                "<#list names as na>\n" +
                "文件路径：${na.age}\n" +
                "代码片段：${na.name}\n" +
                "</#list>\n";
        String result = PromptCommonUtils.buildPrompt(template, params);
        Assert.assertNotNull(result);
    }

    @Test
    public void buildPrompt2() {
        Map<String, Object> params = Maps.newHashMap();
        List<String> names = new ArrayList<>();
        names.add("zhunian");
        names.add("张健");
        params.put("names", names);
        params.put("query", "query");
        String template = "用户需求：${query}\n" +
                "<#list names as na>\n" +
                "文件路径：${na}\n" +
                "代码片段：${na}\n" +
                "</#list>\n";
        String result = PromptCommonUtils.buildPrompt(template, params);
        Assert.assertNotNull(result);
    }

    public class Name {
        String name;
        Integer age;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }
    }


    @Test
    public void buildPromptException() {
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("name", "vat");
            PromptCommonUtils.buildPrompt("模板测试${name}", null);
        } catch (Exception e) {

        }
    }

    @Test
    public void buildPromptIf() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("name", null);
        params.put("query", "query");
        String template = "用户需求：${query}\n" +
                "<#if name??>\n" +
                "文件路径：${name}\n" +
                "<#else>\n" +
                "</#if>\n";
        String result = PromptCommonUtils.buildPrompt(template, params);
        Assert.assertNotNull(result);
    }

}