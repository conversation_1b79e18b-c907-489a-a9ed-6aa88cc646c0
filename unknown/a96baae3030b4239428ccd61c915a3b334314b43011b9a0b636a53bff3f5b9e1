/**
 * Alipay.com Inc. Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;

/**
 * 仓库信息
 *
 * <AUTHOR>
 * @version : RepoInfoModel.java, v 0.1 2021年02月25日 8:04 下午 yunchen Exp $
 */
public class RepoInfoModel {
    /**
     * git仓库地址，如gitlab.alipay-inc.com和code.alipay.com
     */
    private String gitDomain;
    /**
     * 代码group，如operating_release
     */
    private String repoGroup;
    /**
     * 仓库标识，如mrchstm
     */
    private String repoName;
    /**
     * 分支
     */
    private String branch;
    /**
     * 仓库原始地址
     */
    private String repoAddr;
    /**
     * 仓库id
     */
    private Long id;

    /**
     * 无参构造
     */
    public RepoInfoModel() {
    }

    /**
     * 构造方法
     * @param repoPath
     * @param branch
     */
    public RepoInfoModel(String repoPath, String branch) {
        String[] split = repoPath.split("/");
        if (split.length != 2) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER,"invalid repo path:" + repoPath);
        }
        this.gitDomain = AppConstants.ANTCODE_GIT_DOMAIN;
        this.repoGroup = split[0];
        this.repoName = split[1];
        this.branch = branch;
        this.repoAddr = this.gitDomain + "/" + this.repoGroup + "/" + this.repoName;
    }

    /**
     * 构造方法
     * @param gitDomain
     * @param repoGroup
     * @param repoName
     * @param branch
     */
    public RepoInfoModel(String gitDomain, String repoGroup, String repoName, String branch) {
        this.gitDomain = gitDomain;
        this.repoGroup = repoGroup;
        this.repoName = repoName;
        this.branch = branch;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRepoAddr() {
        return repoAddr;
    }

    public void setRepoAddr(String repoAddr) {
        this.repoAddr = repoAddr;
    }

    public String getGitDomain() {
        return gitDomain;
    }

    public void setGitDomain(String gitDomain) {
        this.gitDomain = gitDomain;
    }

    public String getRepoGroup() {
        return repoGroup;
    }

    public void setRepoGroup(String repoGroup) {
        this.repoGroup = repoGroup;
    }

    public String getRepoName() {
        return repoName;
    }

    public void setRepoName(String repoName) {
        this.repoName = repoName;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }
}