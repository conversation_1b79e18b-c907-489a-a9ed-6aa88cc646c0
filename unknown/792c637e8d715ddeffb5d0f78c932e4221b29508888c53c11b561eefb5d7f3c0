/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.request;

import java.util.List;

/**
 * zark请求接收实体
 */
public class ZarkEmbeddingRequestBean {

    private List<String> queries;

    private String model = "defult_model";

    public ZarkEmbeddingRequestBean() {
    }

    /**
     * 构造器
     * @param queries
     */
    public ZarkEmbeddingRequestBean(List<String> queries, String model) {
        this.queries = queries;
        if (model != null && model.length() > 0) {
            this.model = model;
        }
    }

    public List<String> getQueries() {
        return queries;
    }

    public void setQueries(List<String> queries) {
        this.queries = queries;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
