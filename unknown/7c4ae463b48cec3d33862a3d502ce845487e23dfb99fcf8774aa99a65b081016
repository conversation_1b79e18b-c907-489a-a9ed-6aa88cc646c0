package com.alipay.codegencore.model.remote;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 14:35
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MessageObject {

    public enum Role {
        @JsonProperty("user")
        USER,
        @JsonProperty("assistant")
        ASSISTANT
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Attachment {

        @JsonProperty(value = "file_id")
        private String fileId;

        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }
    }

    @JsonProperty(value = "role", required = true)
    private Role role;

    @JsonProperty(value = "content", required = true)
    private MessageContent content;

    @JsonProperty(value = "metadata")
    private Map<String, Object> metadata;

    @JsonProperty(value = "attachments")
    private List<Attachment> attachments;

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }

    public MessageContent getContent() {
        return content;
    }

    public void setContent(MessageContent content) {
        this.content = content;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public List<Attachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Attachment> attachments) {
        this.attachments = attachments;
    }
}
