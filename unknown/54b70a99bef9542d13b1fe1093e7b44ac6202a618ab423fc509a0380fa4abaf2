package com.alipay.codegencore.utils.codefuse;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.utils.codeFuse
 * @CreateTime : 2023-08-01
 */
public class VisableEnvUtil {

    /**
     * 预发
     */
    private static final String HOST_ENV_PREPUB = "prepub";

    /**
     * 是否是预发环境
     *
     * @return
     */
    public static boolean isPrePub() {
        String env = System.getenv("SERVER_ENV");
        return !StringUtils.isBlank(env) && env.equalsIgnoreCase(HOST_ENV_PREPUB);
    }
}
