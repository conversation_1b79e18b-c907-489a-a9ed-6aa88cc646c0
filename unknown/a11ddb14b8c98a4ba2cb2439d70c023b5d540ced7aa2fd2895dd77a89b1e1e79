package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.EnvEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.EmbeddingResponseModel;
import com.alipay.codegencore.model.request.ZarkEmbeddingRequestBean;
import com.alipay.codegencore.service.common.ZarkService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.codegencore.utils.http.HttpClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * zark服务实现类
 */
@Service
public class ZarkServiceImpl implements ZarkService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ZarkServiceImpl.class);

    private static final String PROD_ZARK_URL = "http://zark.sh.global.alipay.com";

    private static final String PRE_ZARK_URL = "http://zark.sh.global.alipay.com";
    private static final String DEV_ZARK_URL = "https://zarkag.antgroup.com";
    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;


    @Override
    public List<EmbeddingResponseModel> embeddingStrList(List<String> strList, String embeddingModel) {
        ZarkEmbeddingRequestBean zarkEmbeddingRequestBean = new ZarkEmbeddingRequestBean(strList, embeddingModel);
        List<List<BigDecimal>> embeddingList = embedding(zarkEmbeddingRequestBean);
        if (embeddingList.size() != strList.size()) {
            throw new BizException(ResponseEnum.FILE_EMBEDDING_FAILED);
        }
        List<EmbeddingResponseModel> resultList = new ArrayList<>();
        for (int i = 0; i < embeddingList.size(); i++) {
            String partUid = ShortUid.getUid();
            EmbeddingResponseModel embeddingResponseModel = new EmbeddingResponseModel();
            embeddingResponseModel.setPartUid(partUid);
            embeddingResponseModel.setOriginalStr(strList.get(i));
            embeddingResponseModel.setOriginalEmbeddingList(embeddingList.get(i));
            resultList.add(embeddingResponseModel);
        }
        return resultList;
    }

    @Override
    public List<List<BigDecimal>> embedding(ZarkEmbeddingRequestBean zarkEmbeddingRequestBean) {
        if (zarkEmbeddingRequestBean == null ||
                StringUtils.isBlank(zarkEmbeddingRequestBean.getModel()) ||
                CollectionUtils.isEmpty(zarkEmbeddingRequestBean.getQueries())) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER);
        }
        JSONObject zarkConfig = JSONObject.parseObject(codeGPTDrmConfig.getZarkConfig());
        String zarkUrl = zarkConfig.getString("zarkUrl");
        String embeddingInterface = zarkConfig.getString("embeddingInterface");
        String data = null;
        try {
            data = HttpClient.post(zarkUrl + embeddingInterface)
                    .content(JSONObject.toJSONString(zarkEmbeddingRequestBean)).syncExecute(10000L);
        }catch (Exception e){
            LOGGER.error("HTTP POST Request failed: " + e.getMessage(), e);
            throw new BizException(ResponseEnum.HTTP_ERROR, "zark服务请求失败，文件embedding失败");
        }
        JSONObject response = JSONObject.parseObject(data);
        if (response == null || response.getInteger("errorCode") != 0) {
            LOGGER.warn("embedding failed,request:{},response:{}", JSONObject.toJSONString(zarkEmbeddingRequestBean), data);
            throw new BizException(ResponseEnum.FILE_EMBEDDING_FAILED);
        }
        JSONArray embeddings = response.getJSONObject("result").getJSONObject("VectorService").getJSONArray("embeddings");
        List<List<BigDecimal>> embeddingList = new ArrayList<>();
        for (int i = 0; i < embeddings.size(); i++) {
            List<BigDecimal> embedding = embeddings.getJSONArray(i).toJavaList(BigDecimal.class);
            embeddingList.add(embedding);
        }
        return embeddingList;
    }

    @Override
    public Object requestCommonZarkUrl(String uri, EnvEnum env, Object requestBody) {
        String zarkUrl = PROD_ZARK_URL;
        if(EnvEnum.PROD.equals(env)){
            zarkUrl = PROD_ZARK_URL;
        }else if(EnvEnum.PRE.equals(env)) {
            zarkUrl = PRE_ZARK_URL;
        }else if(EnvEnum.DEV.equals(env)) {
            zarkUrl = DEV_ZARK_URL;
        }

        String data = HttpClient.post(zarkUrl + uri)
                .content(JSONObject.toJSONString(requestBody)).syncExecute(10000L);
        JSONObject response = JSONObject.parseObject(data);
        if (response==null || response.getInteger("errorCode") != 0) {
            LOGGER.warn("zark request failed:{},response:{}", JSONObject.toJSONString(requestBody), data);
            throw new BizException(ResponseEnum.HTTP_ERROR);
        }
        return response.get("result");
    }
}
