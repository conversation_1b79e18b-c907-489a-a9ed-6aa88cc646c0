/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.Date;

/**
 * 模型基类
 *
 * <AUTHOR>
 * @version $Id: BaseModel.java, v 0.1 2020-04-20 19:58 zhi.huangcz Exp $$
 */
public class BaseModel extends ToString {
    private Long    dbId;
    private Date    gmtCreate;
    private Date    gmtModified;
    private String  mongoId;
    private Boolean deleted = false;

    /**
     * 返回mongoId（方便使用）
     *
     * @return
     */
    public String getId() {
        return this.mongoId;
    }

    /**
     * 设置mongoId（方便使用）
     * @param mongoId
     */
    public void setId(String mongoId) {
        this.mongoId = mongoId;
    }

    /**
     * Getter method for property dbId.
     *
     * @return property value of dbId
     */
    public Long getDbId() {
        return dbId;
    }

    /**
     * Setter method for property dbId.
     *
     * @param dbId value to be assigned to property dbId
     */
    public void setDbId(Long dbId) {
        this.dbId = dbId;
    }

    /**
     * Getter method for property gmtCreate.
     *
     * @return property value of gmtCreate
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * Setter method for property gmtCreate.
     *
     * @param gmtCreate value to be assigned to property gmtCreate
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * Getter method for property gmtModified.
     *
     * @return property value of gmtModified
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * Setter method for property gmtModified.
     *
     * @param gmtModified value to be assigned to property gmtModified
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * Getter method for property mongoId.
     *
     * @return property value of mongoId
     */
    public String getMongoId() {
        return mongoId;
    }

    /**
     * Setter method for property mongoId.
     *
     * @param mongoId value to be assigned to property mongoId
     */
    public void setMongoId(String mongoId) {
        this.mongoId = mongoId;
    }

    /**
     * Getter method for property deleted.
     *
     * @return property value of deleted
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * Setter method for property deleted.
     *
     * @param deleted value to be assigned to property deleted
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}