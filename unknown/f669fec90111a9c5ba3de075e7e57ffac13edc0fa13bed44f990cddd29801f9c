package com.alipay.codegencore.service.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


/**
 * <AUTHOR>
 * @version 1.0
 * @description 将实体类转换成markdown表格返回字符串
 * @date 2023.10.23
 */
public class AsynToMarkdownTable {
    private static final String ID = "id";
    private static final String GMT_MODIFY = "gmt_modified";
    public static final String GMT_CREATE = "gmt_create";

    /**
     * 将JSONArray转为markdown表格输出
     * <AUTHOR>
     * @since 2023.10.24
     * @param jsonArray jsonArray
     * @return java.lang.String
     */
    public static String classListToMarkdownTable(JSONArray jsonArray) {
        if(jsonArray.isEmpty()){
            return "该表中没有查询到数据";
        }
        // 获取表头
        JSONObject firstObject = jsonArray.getJSONObject(0);
        StringBuilder headerBuilder = new StringBuilder();
        headerBuilder.append(sort(firstObject,true));
        headerBuilder.append("\n");
        headerBuilder.append("|");
        headerBuilder.append(" --- |".repeat(firstObject.keySet().size()));
        headerBuilder.append("\n");
        // 获取表数据
        StringBuilder dataBuilder = new StringBuilder();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            dataBuilder.append(sort(jsonObject, false));
            dataBuilder.append("\n ");
        }
        headerBuilder.append(dataBuilder);
        return headerBuilder.toString();
    }
    /**
     * 重排JSONObject里面的键值对，看起来更像数据库查表
     * <AUTHOR>
     * @since 2023.10.24
     * @param jsonObject jsonObject
     * @param isHead isHead
     * @return java.lang.StringBuilder
     */
    private static StringBuilder sort(JSONObject jsonObject,Boolean isHead){
        StringBuilder result = new StringBuilder("| ");
        if(isHead){
            if (jsonObject.containsKey(ID)) {
                result.append(ID).append(" | ");
            }
            if (jsonObject.containsKey(GMT_CREATE)) {
                result.append(GMT_CREATE).append(" | ");
            }
            if (jsonObject.containsKey(GMT_MODIFY)) {
                result.append(GMT_MODIFY).append(" | ");
            }
            for (String key : jsonObject.keySet()) {
                if(!key.equals(GMT_CREATE)&&!key.equals(GMT_MODIFY)&&!key.equals(ID)){
                    result.append(key).append(" | ");
                }
            }
        }else {
            if (jsonObject.containsKey(ID)) {
                result.append(jsonObject.get(ID)).append(" | ");
            }
            if (jsonObject.containsKey(GMT_CREATE)) {
                result.append(jsonObject.get(GMT_CREATE)).append(" | ");
            }
            if (jsonObject.containsKey(GMT_MODIFY)) {
                result.append(jsonObject.get(GMT_MODIFY)).append(" | ");
            }
            for (String key : jsonObject.keySet()) {
                if(!key.equals(GMT_CREATE)&&!key.equals(GMT_MODIFY)&&!key.equals(ID)){
                    Object value = jsonObject.get(key);
                    if(value instanceof String){
                        value = ((String) value).replace("\n"," ");
                    }
                    result.append(value).append(" | ");
                }
            }
        }
        return result;
    }
}
