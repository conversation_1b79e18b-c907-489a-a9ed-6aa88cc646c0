/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version CopilotCommand.java, v 0.1 2023年11月21日 下午2:58 wb-tzg858080
 */

public class CopilotCommand {

    /**
     * 命令词
     */
    private String command;

    /**
     * 描述
     */
    private String remark;

    /**
     * 模板 中 %s 替换文本
     */
    private String template;

    /**
     * 名称
     */
    private String  name;

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
