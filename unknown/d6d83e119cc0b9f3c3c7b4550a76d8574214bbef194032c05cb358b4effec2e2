/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.web.links;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.annocation.CodeTalkWebApi;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.links.CopilotCommand;
import com.alipay.codegencore.model.model.links.Enum.CommonResultEnum;
import com.alipay.codegencore.model.model.links.GptConversationModel;
import com.alipay.codegencore.model.model.links.GptMessageContent;
import com.alipay.codegencore.model.model.links.GptMessageFeedbackContent;
import com.alipay.codegencore.model.model.links.GptMessageFeedbackForm;
import com.alipay.codegencore.model.model.links.GptMessageModel;
import com.alipay.codegencore.model.model.links.LinksResult;
import com.alipay.codegencore.model.request.CodeGPTVoteRequestBean;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.impl.LinksApiService;
import com.alipay.codegencore.web.codegpt.ChatMessageController;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version CopilotConversationMessageApi.java, v 0.1 2023年11月30日 上午11:22 wb-tzg858080
 */
@RestController
@CodeTalkWebApi
@RequestMapping("/cors")
public class CopilotConversationMessageApi{
    private final static Logger LOGGER = LoggerFactory.getLogger(CopilotConversationMessageApi.class);
    @Resource
    private UserAclService userAclService;

    @Resource
    private LinksApiService linksApiService;

    @Resource
    private ChatMessageController chatMessageController;

    @Resource
    private ChatMessageService chatMessageService;


    /**
     * 消息进行反馈
     *
     * @param messageId 会话id
     * @return 是否成功
     */
    @PostMapping("/copilot/message/{messageId}/vote")
    public LinksResult feedbackMessage(@PathVariable String messageId, @RequestBody GptMessageFeedbackForm form) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        //1. 调用反馈接口
        GptMessageModel gptMessageModel = linksApiService.getMessageById(messageId);
        String copilotMessageId = gptMessageModel.getCopilotMessageId();
        GptMessageFeedbackContent content = form.getContent();
        CodeGPTVoteRequestBean codeGPTVoteRequestBean = new CodeGPTVoteRequestBean();
        if (StringUtils.isNotBlank(copilotMessageId)) {
            if(content !=null){
                Map<String, Integer> checkFeedback = form.getContent().getCheckFeedback();
                Map<ReviewPlatformEnum,Integer> checkFeedbackMap = new HashMap<>();
                if(checkFeedback != null){
                    for (String key : checkFeedback.keySet()) {
                        checkFeedbackMap.put(ReviewPlatformEnum.valueOf(key), checkFeedback.get(key));
                    }
                    codeGPTVoteRequestBean.setCheckFeedback(checkFeedbackMap);
                }
                codeGPTVoteRequestBean.setText(content.getRemark());
                codeGPTVoteRequestBean.setTags(content.getTags());
            }
            codeGPTVoteRequestBean.setVote(form.getAgreed()?1:0);
            codeGPTVoteRequestBean.setUid(copilotMessageId);
            chatMessageController.vote(codeGPTVoteRequestBean);
            linksApiService.vote(messageId, form.getAgreed(), form.getContent(),currentUser.getId());
        }
        return LinksResult.success();
    }

    /**
     * 中断消息
     *
     * @param messageId 会话id
     * @return 是否成功
     */
    @PostMapping("/copilot/message/{messageId}/cancelMessage")
    public LinksResult cancelMessage(@PathVariable String messageId) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        return cancelSee(messageId,false,currentUser.getId());
    }

    /**
     * 中断表单消息
     *
     * @param messageId 会话id
     * @return 是否成功
     */
    @PostMapping("/copilot/message/{messageId}/cancelMessageForm")
    public LinksResult cancelMessageForm(@PathVariable String messageId) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        if(currentUser == null){
            throw new BizException(ResponseEnum.USER_NOT_LOGIN);
        }
        return cancelSee(messageId,true,currentUser.getId());
    }
    /**
     * 中断消息
     *
     * <AUTHOR>
     * @since 2024.07.04
     * @param messageId messageId
     * @param isForm isForm
     * @return com.alipay.codegencore.model.model.links.LinksResult
     */
    private LinksResult cancelSee(String messageId, Boolean isForm,Long userId){
        long queryIndex = 1L;
        long generationIndex = 0L;
        GptMessageModel gptMessageModel = linksApiService.getMessageById(messageId);
        Boolean regenerate = gptMessageModel.getContent().getCopilotAnswer().getReGenerate();
        GptConversationModel gptConversationModel = linksApiService.getConversationById(gptMessageModel.getConversationId(),true);
        String copilotSessionId = gptConversationModel.getCopilotSessionId();
        List<ChatMessageDO> chatMessageDOS = chatMessageService.listChatMessage(copilotSessionId, false, false);
        if(CollectionUtils.isNotEmpty(chatMessageDOS)){
            ChatMessageDO lastChatMessageDO = chatMessageDOS.get(chatMessageDOS.size() - 1);
            if(regenerate){
                queryIndex = lastChatMessageDO.getQueryIndex();
            }else {
                queryIndex = lastChatMessageDO.getQueryIndex()+1;
            }
            generationIndex = lastChatMessageDO.getGenerationIndex()+1;
        }
        GptMessageModel lastUserMessage = linksApiService.getLastUserMessage(gptConversationModel.getConversationId(), userId);
        GptMessageContent content = lastUserMessage.getContent();
        CopilotCommand copilotCommand = content.getCopilotCommand();
        boolean tryRepoSearch = false;
        if(copilotCommand != null){
            tryRepoSearch = linksApiService.isRepoChat(copilotCommand.getCommand());
        }
        LOGGER.info("lastUserMessage:{}",JSON.toJSONString(lastUserMessage));
        if (StringUtils.isNotBlank(copilotSessionId)) {
            BaseResponse<ResponseEnum> response;
            if(isForm){
                response = chatMessageController.cancelMessage(copilotSessionId, queryIndex, generationIndex, gptMessageModel.getCopilotMessageId(),false);
            }else {
                response = chatMessageController.cancelMessage(copilotSessionId, queryIndex, generationIndex, null,tryRepoSearch);
            }
            LOGGER.info("response:{}", JSON.toJSONString(response));
            if(response!=null&&!ResponseEnum.SUCCESS.name().equalsIgnoreCase(response.getErrorType().name())){
                return LinksResult.fail(CommonResultEnum.SYSTEM_ERROR,"中断消息失败");
            }
        }
        linksApiService.updateCancelMessage(messageId);
        return LinksResult.success();
    }
}
