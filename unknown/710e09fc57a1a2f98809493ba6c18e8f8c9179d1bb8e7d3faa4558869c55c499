package com.alipay.codegencore.web.filter;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.codegpt.user.TokenService;
import com.alipay.codegencore.service.common.UserAclService;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * open api token 校验
 */
@Slf4j
@WebFilter(urlPatterns = {"/api/answer/*", "/v1/generate/*"})
public class OpenApiTokenCheckFilter implements Filter {

    /**
     * token
     */
    private static final String HEADER_TOKEN = "codegpt_token";

    /**
     * user
     */
    private static final String HEADER_USER = "codegpt_user";


    @Autowired
    private TokenService tokenService;

    @Autowired
    private UserAclService userAclService;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        final String token = request.getHeader(HEADER_TOKEN);
        final String user = request.getHeader(HEADER_USER);

        String requestURI = new UrlPathHelper().getLookupPathForRequest(request);
        boolean authorized = userAclService.isAuthorizedByToken(user, token, requestURI);
        if (!authorized) {
            responseNoAuth(servletResponse);
            return;
        }

        //日志应该单独 filter 打印，先在这里统计吧
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            filterChain.doFilter(servletRequest, servletResponse);
        } finally {
            stopwatch.stop();
            log.debug("request:{} handle cost:{}", requestURI, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        }

    }

    /**
     * 打印通用无权限结果
     * @param servletResponse
     * @throws IOException
     */
    private void responseNoAuth(ServletResponse servletResponse) throws IOException {
        servletResponse.setContentType(ContentType.APPLICATION_JSON.toString());
        servletResponse.getWriter().write(JSON.toJSONString(BaseResponse.build(ResponseEnum.NO_AUTH)));
    }


}
