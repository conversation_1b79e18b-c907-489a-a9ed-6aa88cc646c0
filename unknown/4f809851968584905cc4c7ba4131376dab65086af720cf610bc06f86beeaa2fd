package com.alipay.codegencore.service.common.segment;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.SegmentationStrategyTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version : QASegmentationStrategy.java, v 0.1 2024年01月11日 14:42 baoping Exp $
 */
@Service
public class QASegmentationStrategy implements SegmentationStrategy{

    /**
     * QA约定的分隔符
     */
    private static final String regex = "\\n---|---\\n";

    /**
     * @param text
     * @param documentChatConfig
     * @return
     */
    @Override
    public List<String> segment(String text, JSONObject documentChatConfig) {
        int maxTokenCount = documentChatConfig.getInteger("everyPartMaxSize");
        List<String> segments = new ArrayList<>();
        String[] splitByDelimiters = text.split(regex);
        for (String s : splitByDelimiters) {
            if (StringUtils.isBlank(s)){
                continue;
            }
            // 如果内容超出最大长度，直接截断
            if (s.length() > maxTokenCount) {
                s = s.substring(0, maxTokenCount);
            }
            segments.add(s);
        }
        return segments;
    }

    @Override
    public SegmentationStrategyTypeEnum getType() {
        return SegmentationStrategyTypeEnum.QA_STRATEGY;
    }

}
