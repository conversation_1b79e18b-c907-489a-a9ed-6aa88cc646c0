package com.alipay.codegencore.web.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alipay.common.tracer.util.TracerContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.util.UrlPathHelper;

/**
 * 给 http 响应头中加入 traceId
 * 使流式返回时，调用者也可以拿到 trace
 *
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.web.filter
 * @CreateTime : 2023-09-26
 */
@Configuration
@WebFilter(urlPatterns = {"/api/**", "/webapi/**"})
public class TraceFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(YuQueUriFilter.class);

    /**
     *
     * @param req
     * @param res
     * @param chain
     * @throws IOException
     * @throws ServletException
     */
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        String requestURI = new UrlPathHelper().getLookupPathForRequest(request);
        if (requestURI.startsWith("/api/") || requestURI.startsWith("/webapi/")) {
            HttpServletResponse response = (HttpServletResponse) res;
            response.setHeader("TraceId", TracerContextUtil.getTraceId());
            chain.doFilter(request, response);

            //LOGGER.info("add trace requestURI：{},uri:{},", request.getRequestURI(), requestURI);
        }
        else {
            chain.doFilter(req, res);
        }

    }

    /**
     * 初始化
     *
     * @param filterConfig
     */
    public void init(FilterConfig filterConfig) {LOGGER.info("TraceFilter过滤器初始化！");}

    /**
     * 这是个注释
     */
    public void destroy() {}
}

