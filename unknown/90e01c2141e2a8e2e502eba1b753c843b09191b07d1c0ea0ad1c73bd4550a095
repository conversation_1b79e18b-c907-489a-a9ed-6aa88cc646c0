package com.alipay.codegencore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.dal.example.PluginDOExample;
import com.alipay.codegencore.dal.example.PluginDOExample.Criteria;
import com.alipay.codegencore.dal.mapper.PluginDOMapper;
import com.alipay.codegencore.dal.mapper.PluginManualMapper;
import com.alipay.codegencore.dal.mapper.UserPluginRecordsDOMapper;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.domain.UserPluginRecordsDO;
import com.alipay.codegencore.model.enums.ControlTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.enums.UserTypeEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.service.codegpt.PluginService;
import com.alipay.codegencore.service.codegpt.UserPluginRecordsService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.agentsecsdk.SecAgentHelper;
import com.alipay.codegencore.utils.thread.ContextUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.alipay.codegencore.model.contant.WebApiContents.CONTEXT_USER;

/**
 * plugin 插件service
 *
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.service.impl.model
 * @CreateTime : 2023-07-11
 */
@Service
public class PluginServiceImpl implements PluginService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PluginServiceImpl.class);

    @Resource
    private PluginDOMapper pluginDOMapper;

    @Resource
    private UserAclService userAclService;

    @Resource
    private SecAgentHelper secAgentHelper;

    @Resource
    private UserPluginRecordsService userPluginRecordsService;

    @Resource
    private PluginManualMapper pluginManualMapper;
    @Resource
    private UserPluginRecordsDOMapper userPluginRecordsDOMapper;


    /**
     * 通过id查找插件
     *
     * @param id
     * @return
     */
    @Override
    public PluginDO getPluginById(Long id) {
        PluginDO pluginDO = pluginDOMapper.selectByPrimaryKey(id);
        if(pluginDO == null){
            return null;
        }
        if(checkVisibleAuth(pluginDO,userAclService.getCurrentUser())){
            return pluginDO;
        }else {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
    }

    /**
     * 通过id列表查找插件
     *
     * @param idList id列表
     * @return 插件列表
     */
    @Override
    public List<PluginDO> getPluginByIdList(List<Long> idList, Boolean needEnable) {
        PluginDOExample pluginDOExample = new PluginDOExample();
        Criteria criteria = pluginDOExample.createCriteria().andIdIn(idList).andDeletedEqualTo(0);
        if (needEnable) {
            criteria.andEnableEqualTo(1);
        }
        return pluginDOMapper.selectByExample(pluginDOExample);
    }

    /**
     * 通过名字查找插件
     *
     * @param pluginName 插件名
     * @return
     */
    @Override
    public PluginDO getPluginByName(String pluginName) {
        PluginDOExample pluginDOExample = new PluginDOExample();
        pluginDOExample.createCriteria().andNameEqualTo(pluginName).andDeletedEqualTo(0);
        List<PluginDO> pluginDOS = pluginDOMapper.selectByExample(pluginDOExample);
        PluginDO pluginDO = CollectionUtil.isEmpty(pluginDOS) ? null : pluginDOS.get(0);
        if(pluginDO == null){
            return null;
        }
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if(checkVisibleAuth(pluginDO,userAuthDO)){
            return pluginDO;
        }
        return null;
    }

    /**
     * 获取所有插件
     *
     * @return
     */
    @Override
    public List<PluginDO> getAllPlugin() {
        PluginDOExample pluginDOExample = new PluginDOExample();
        pluginDOExample.createCriteria().andDeletedEqualTo(0);
        return pluginDOMapper.selectByExample(pluginDOExample);
    }

    /**
     * 新增插件
     *
     * @param pluginDO
     * @return
     */
    @Override
    public Long addPlugin(PluginDO pluginDO) {
        PluginDOExample pluginDOExample = new PluginDOExample();
        pluginDOExample.createCriteria().andNameEqualTo(pluginDO.getName());
        List<PluginDO> pluginDOS = pluginDOMapper.selectByExample(pluginDOExample);
        if (CollectionUtil.isNotEmpty(pluginDOS)) {
            throw new BizException(ResponseEnum.SCENE_REPEAT);
        }
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        pluginDO.setUserId(userAuthDO.getId());
        // 新创建的工具默认仅自己可见
        pluginDO.setVisableUser(1);
        pluginDOMapper.insertSelective(pluginDO);
        LOGGER.info("addPlugin ,operator empId:{} pluginDO:{}", userAuthDO.getEmpId(), JSON.toJSONString(pluginDO));
        // 向 agent安全网关 同步插件信息
        secAgentHelper.pluginInit(pluginDO,userAuthDO);

        return pluginDO.getId();
    }

    /**
     * 更新插件信息
     *
     * @param pluginDO
     * @return
     */
    @Override
    public Boolean updatePlugin(PluginDO pluginDO) {
        UserAuthDO userAuthDO = userAclService.getCurrentUser();
        if (!checkEditPluginAuth(pluginDO.getId(), userAuthDO.getId())) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        PluginDOExample pluginDOExample = new PluginDOExample();
        Criteria criteria = pluginDOExample.createCriteria();
        criteria.andIdEqualTo(pluginDO.getId()).andDeletedEqualTo(0);
        List<PluginDO> pluginDOS = pluginDOMapper.selectByExample(pluginDOExample);
        if (CollectionUtil.isEmpty(pluginDOS)) {
            throw new BizException(ResponseEnum.SCENE_PLUGIN_NOT_PRESENCE);
        }
        int update = pluginDOMapper.updateByExampleSelective(pluginDO, pluginDOExample);
        LOGGER.info("updatePlugin ,operator empId:{} pluginDO:{}", userAuthDO.getEmpId(), JSON.toJSONString(pluginDO));
        // 向 agent安全网关 同步插件信息
        secAgentHelper.pluginModify(pluginDO,userAuthDO);

        return update == 1;
    }


    /**
     * 增加插件使用次数
     *
     * @param pluginId
     * @return
     */
    @Override
    public Boolean addPluginUsageCount(Long pluginId) {
        PluginDOExample pluginDOExample = new PluginDOExample();
        pluginDOExample.createCriteria()
                .andIdEqualTo(pluginId)
                .andDeletedEqualTo(0);
        PluginDO pluginById = pluginDOMapper.selectByPrimaryKey(pluginId);
        PluginDO pluginDO = new PluginDO();
        pluginDO.setId(pluginId);
        pluginDO.setUsageCount(pluginById.getUsageCount() + 1);
        int update = pluginDOMapper.updateByExampleSelective(pluginDO, pluginDOExample);
        return update == 1;
    }

    /**
     * 删除插件
     *
     * @param id 插件id
     * @return
     */
    @Override
    public Boolean deletePlugin(long id) {
        PluginDO pluginDODb = getPluginById(id);
        if (pluginDODb == null) {
            throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "插件不存在");
        }
        UserAuthDO userAuthDO = ContextUtil.get(CONTEXT_USER, UserAuthDO.class);
        if (!Objects.equals(pluginDODb.getOwnerUserId(), userAuthDO.getId()) &&
                !Objects.equals(pluginDODb.getUserId(), userAuthDO.getId())) {
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        PluginDOExample pluginDOExample = new PluginDOExample();
        PluginDOExample.Criteria criteria = pluginDOExample.createCriteria();
        criteria.andIdEqualTo(id);
        PluginDO pluginDO = new PluginDO();
        pluginDO.setDeleted(1);
        pluginDO.setName(pluginDODb.getName() + "_delete_" + System.currentTimeMillis());
        int update = pluginDOMapper.updateByExampleSelective(pluginDO, pluginDOExample);

        LOGGER.info("deletePlugin ,operator empId:{} pluginId:{}", userAuthDO.getEmpId(),id);

        // 向 agent安全网关 同步插件信息
        secAgentHelper.pluginDelete(id);

        return update == 1;
    }

    /**
     * 获取用户插件
     *
     * @param userId
     * @param query
     * @return
     */
    @Override
    public List<PluginDO> getUserPlugin(Long userId, String query) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        List<PluginDO> allUserPlugin = new ArrayList<>();
        if(currentUser.getAdmin()!= UserTypeEnum.SUPER_ADMIN.getCode()){
            List<Long> authPluginIdList = userPluginRecordsDOMapper.selectEditPluginIdListByUserId(userId);
            List<Long> pluginIdList = pluginManualMapper.getEditablePluginByUser(userId, query);
            pluginIdList.addAll(authPluginIdList);
            if(CollectionUtils.isNotEmpty(pluginIdList)){
                allUserPlugin = getPluginByIdList(pluginIdList,false);
            }
        }else {
            List<Long> pluginIdList = pluginManualMapper.getEditablePluginByAdmin(userId, query);
            if(CollectionUtils.isNotEmpty(pluginIdList)){
                allUserPlugin = getPluginByIdList(pluginIdList,false);
            }

        }
        return allUserPlugin;
    }

    @Override
    public List<PluginDO> getEnablePlugin(String query) {
        UserAuthDO currentUser = userAclService.getCurrentUser();
        List<PluginDO> allEnablePlugin = new ArrayList<>();
        if (currentUser.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()) {
            List<Long> authPluginIdList = userPluginRecordsDOMapper.selectPluginIdListByUserId(currentUser.getId());
            List<Long> pluginIdList = pluginManualMapper.getAllPluginByEnable(currentUser.getId(), query);
            pluginIdList.addAll(authPluginIdList);
            if (CollectionUtil.isNotEmpty(pluginIdList)) {
                allEnablePlugin = getPluginByIdList(pluginIdList,true);
            }
        }
        else {
            List<Long> pluginIdList = pluginManualMapper.getAdminAllPluginByEnable(currentUser.getId(), query);
            if (CollectionUtil.isNotEmpty(pluginIdList)) {
                allEnablePlugin = getPluginByIdList(pluginIdList,true);
            }
        }

        return allEnablePlugin;
    }

    @Override
    public void updatePluginAuth(PluginDO plugin,UserAuthDO userAuthDO) {
        Boolean hasAuth = checkEditPluginAuth(plugin.getId(), userAuthDO.getId());
        if(!hasAuth){
            throw new BizException(ResponseEnum.NO_AUTH);
        }
        // 拦截全员可见
        if(plugin.getVisableUser()!=null && plugin.getVisableUser() == 2 && userAuthDO.getAdmin() != UserTypeEnum.SUPER_ADMIN.getCode()){
            throw new BizException(ResponseEnum.NO_AUTH,"要设置全员可见，请联系云辰审核！");
        }
        PluginDO pluginDO = new PluginDO();
        pluginDO.setId(plugin.getId());
        pluginDO.setVisableUser(plugin.getVisableUser());
        updatePlugin(pluginDO);
    }

    @Override
    public Boolean checkEditPluginAuth(Long pluginId, Long userId) {
        PluginDO plugin = getPluginById(pluginId);
        UserAuthDO userAuthDO = userAclService.selectByUserId(userId);
        // 创建者或者owner可以编辑助手
        if (Objects.equals(plugin.getOwnerUserId(), userId) ||
                Objects.equals(plugin.getUserId(), userId)) {
            return true;
        }
        // 管理员有权限编辑
        if (userAuthDO.getAdmin() == 2) {
            return true;
        }
        UserPluginRecordsDO userPermissionInfo = userPluginRecordsService.getUserPermissionInfo(userId , pluginId);
        if(userPermissionInfo==null){
            return false;
        }
        return userPermissionInfo.getControlType().equals(ControlTypeEnum.UPDATE.getCode());
    }
    /**
     * 校验可见权限
     *
     * <AUTHOR>
     * @since 2024.09.11
     * @param pluginDO pluginDO
     * @param userAuthDO userAuthDO
     * @return java.lang.Boolean
     */
    private Boolean checkVisibleAuth(PluginDO pluginDO, UserAuthDO userAuthDO){
        if(pluginDO.getVisableUser().equals(2)){
            return true;
        }
        if (Objects.equals(pluginDO.getOwnerUserId(), userAuthDO.getId()) ||
                Objects.equals(pluginDO.getUserId(), userAuthDO.getId())) {
            return true;
        }
        if (userAuthDO.getAdmin() == 2) {
            return true;
        }
        UserPluginRecordsDO userPermissionInfo = userPluginRecordsService.getUserPermissionInfo(userAuthDO.getId() , pluginDO.getId());
        return userPermissionInfo != null;
    }
}
