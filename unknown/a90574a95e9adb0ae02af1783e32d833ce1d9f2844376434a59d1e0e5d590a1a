package com.alipay.codegencore.service.middle.msgbroker;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import com.alibaba.common.logging.Logger;
import com.alibaba.common.logging.LoggerFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.alipay.codegencore.service.middle.msgbroker.handler.CodegencoreEventHandler;
import com.alipay.common.event.UniformEvent;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zcache.serialize.BytesObject;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.service.middle.msgbroker.handler
 * @CreateTime : 2023-08-31
 */
@Slf4j
@Service("HubChatGptModelResponseListener")
public class HubChatGptModelResponseListener implements CodegencoreEventHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(HubChatGptModelResponseListener.class);

    @Resource(name = "noneSerializationCacheManager")
    private RefreshableCommonTbaseCacheManager noneSerializationCacheManager;

    /**
     * topic|eventCode的Key 一般用于注册
     */
    private static final String TOPIC_EVENT_CODE_PAIR_KEY = "TP_F_EXTDATASYNC|EC_datahub_cto_code";

    @Override
    public void handle(UniformEvent message) {

        LOGGER.info(String.format("HubChatGptModelResponseListener message: %s", JSON.toJSONString(message)));
        JSONObject eventPayload = JSONObject.parseObject(JSON.toJSONString(message.getEventPayload()));
        JSONObject responseBody = JSONObject.parseObject(JSON.toJSONString(eventPayload.get("responseBody")));
        JSONArray choices = JSONArray.parseArray(JSON.toJSONString(responseBody.get("choices")));
        JSONObject choicesIndex = JSONObject.parseObject(JSON.toJSONString(choices.get(0)));
        JSONObject chatMessage = JSONObject.parseObject(JSON.toJSONString(choicesIndex.get("message")));
        JSONObject outputConfig = JSONObject.parseObject(JSON.toJSONString(eventPayload.get("outputConfig")));
        String messageKey = String.valueOf(outputConfig.get("messageKey"));
        BytesObject bytesObject = new BytesObject(chatMessage.toJSONString().getBytes(StandardCharsets.UTF_8));
        noneSerializationCacheManager.set(messageKey, bytesObject);

    }

    @Override
    public String getTopicEventCodePair() {
        return TOPIC_EVENT_CODE_PAIR_KEY;
    }
}
