package com.alipay.codegencore.model.model.tool.learning.plugin;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.enums.tool.learning.StageTypeEnum;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.StageLog;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema.ApiStageOutput;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema.ModelStageOutput;
import com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema.TemplateStageOutput;

import java.util.ArrayList;
import java.util.List;

/**
 * 插件日志信息组
 */
public class PluginLogGroup {
    private List<PluginLog> pluginLogList;

    public PluginLogGroup() {
        pluginLogList = new ArrayList<>();
    }

    public List<PluginLog> getPluginLogList() {
        return pluginLogList;
    }


    public void setPluginLogList(List<PluginLog> pluginLogList) {
        this.pluginLogList = pluginLogList;
    }

    /**
     * 解析出答案
     * @return
     */
    public String parseAnswer(){
        if(pluginLogList.isEmpty()){
            return null;
        }
        PluginLog lastPluginLog = pluginLogList.get(pluginLogList.size() - 1);
        if(lastPluginLog.getStageLogList().isEmpty()){
            return null;
        }
        StageLog lastStageLog = lastPluginLog.getStageLogList().get(lastPluginLog.getStageLogList().size() - 1);
        StageTypeEnum lastStageType = StageTypeEnum.fromString(lastStageLog.getType());
        if(lastStageType==null || lastStageLog.getStageInfo()==null || lastStageLog.getStageInfo().getOutput()==null){
            return null;
        }
        switch (lastStageType){
            case MODEL:
                ModelStageOutput modelStageOutput = (ModelStageOutput) lastStageLog.getStageInfo().getOutput();
                return modelStageOutput.getLlmResult();
            case TEMPLATE:
                TemplateStageOutput templateStageOutput = (TemplateStageOutput) lastStageLog.getStageInfo().getOutput();
                return templateStageOutput.getResult();
            case API:
                ApiStageOutput apiStageOutput =(ApiStageOutput) lastStageLog.getStageInfo().getOutput();
                return JSON.toJSONString(apiStageOutput.getResponseBody());
            default:
                return null;
        }
    }
}
