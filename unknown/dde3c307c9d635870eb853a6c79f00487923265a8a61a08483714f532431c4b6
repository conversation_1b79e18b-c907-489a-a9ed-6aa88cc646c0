package com.alipay.codegencore.model.request.tsingyan;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.LanguageEnum;

/**
 * 代码补全请求参数
 *
 * <AUTHOR>
 * 创建时间 2022-10-12
 */
public class CompletionRequestBean extends AbstractRequestBean {
    /**
     * 预测所需的代码内容
     */
    private String prompt;

    /**
     * 会话id
     */
    private Long sessionId;
    /**
     * 代码语言,默认值为java
     * 取值参考{@link com.alipay.codegencore.model.enums.LanguageEnum}
     */
    private String lang = LanguageEnum.JAVA.name();
    /**
     * 补全候选集长度
     */
    private int completionNum = AppConstants.TSINGYAN_PLUGIN_CONFIG_MODEL.getCompletionConfigModel().getCompletionNum();

    public int getCompletionNum() {
        return completionNum;
    }

    public void setCompletionNum(int completionNum) {
        this.completionNum = completionNum;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }
}
