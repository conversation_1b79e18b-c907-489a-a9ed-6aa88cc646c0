package com.alipay.codegencore.model.domain;

import java.util.Date;

public class AlgoBackendDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.gmt_create
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.gmt_modified
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.enable
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Boolean enable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.model
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private String model;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.jump
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private String jump;

    /**
     *
     * 1全员可见 2仅管理员可见 0全员不可见
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.visable_user
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer visableUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.visable_env
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer visableEnv;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.max_token
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer maxToken;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.max_round
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer maxRound;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.impl
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private String impl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.impl_config
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private String implConfig;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.need_health_check
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Boolean needHealthCheck;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.model_description
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private String modelDescription;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.enable_gpt_cache
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Boolean enableGptCache;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.owner_user_id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer ownerUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.model_base
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Boolean modelBase;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.usage_user_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer usageUserCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.usage_session_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer usageSessionCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.usage_message_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer usageMessageCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.create_user_id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.model_type
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Integer modelType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_algo_backend.support_plugin
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    private Boolean supportPlugin;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.id
     *
     * @return the value of cg_algo_backend.id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.id
     *
     * @param id the value for cg_algo_backend.id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.gmt_create
     *
     * @return the value of cg_algo_backend.gmt_create
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.gmt_create
     *
     * @param gmtCreate the value for cg_algo_backend.gmt_create
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.gmt_modified
     *
     * @return the value of cg_algo_backend.gmt_modified
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.gmt_modified
     *
     * @param gmtModified the value for cg_algo_backend.gmt_modified
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.enable
     *
     * @return the value of cg_algo_backend.enable
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.enable
     *
     * @param enable the value for cg_algo_backend.enable
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.model
     *
     * @return the value of cg_algo_backend.model
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public String getModel() {
        return model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.model
     *
     * @param model the value for cg_algo_backend.model
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.jump
     *
     * @return the value of cg_algo_backend.jump
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public String getJump() {
        return jump;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.jump
     *
     * @param jump the value for cg_algo_backend.jump
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setJump(String jump) {
        this.jump = jump;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.visable_user
     *
     * @return the value of cg_algo_backend.visable_user
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getVisableUser() {
        return visableUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.visable_user
     *
     * @param visableUser the value for cg_algo_backend.visable_user
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setVisableUser(Integer visableUser) {
        this.visableUser = visableUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.visable_env
     *
     * @return the value of cg_algo_backend.visable_env
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getVisableEnv() {
        return visableEnv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.visable_env
     *
     * @param visableEnv the value for cg_algo_backend.visable_env
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setVisableEnv(Integer visableEnv) {
        this.visableEnv = visableEnv;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.max_token
     *
     * @return the value of cg_algo_backend.max_token
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getMaxToken() {
        return maxToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.max_token
     *
     * @param maxToken the value for cg_algo_backend.max_token
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setMaxToken(Integer maxToken) {
        this.maxToken = maxToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.max_round
     *
     * @return the value of cg_algo_backend.max_round
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getMaxRound() {
        return maxRound;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.max_round
     *
     * @param maxRound the value for cg_algo_backend.max_round
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setMaxRound(Integer maxRound) {
        this.maxRound = maxRound;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.impl
     *
     * @return the value of cg_algo_backend.impl
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public String getImpl() {
        return impl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.impl
     *
     * @param impl the value for cg_algo_backend.impl
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setImpl(String impl) {
        this.impl = impl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.impl_config
     *
     * @return the value of cg_algo_backend.impl_config
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public String getImplConfig() {
        return implConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.impl_config
     *
     * @param implConfig the value for cg_algo_backend.impl_config
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setImplConfig(String implConfig) {
        this.implConfig = implConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.need_health_check
     *
     * @return the value of cg_algo_backend.need_health_check
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Boolean getNeedHealthCheck() {
        return needHealthCheck;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.need_health_check
     *
     * @param needHealthCheck the value for cg_algo_backend.need_health_check
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setNeedHealthCheck(Boolean needHealthCheck) {
        this.needHealthCheck = needHealthCheck;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.model_description
     *
     * @return the value of cg_algo_backend.model_description
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public String getModelDescription() {
        return modelDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.model_description
     *
     * @param modelDescription the value for cg_algo_backend.model_description
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setModelDescription(String modelDescription) {
        this.modelDescription = modelDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.enable_gpt_cache
     *
     * @return the value of cg_algo_backend.enable_gpt_cache
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Boolean getEnableGptCache() {
        return enableGptCache;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.enable_gpt_cache
     *
     * @param enableGptCache the value for cg_algo_backend.enable_gpt_cache
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setEnableGptCache(Boolean enableGptCache) {
        this.enableGptCache = enableGptCache;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.owner_user_id
     *
     * @return the value of cg_algo_backend.owner_user_id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getOwnerUserId() {
        return ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.owner_user_id
     *
     * @param ownerUserId the value for cg_algo_backend.owner_user_id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setOwnerUserId(Integer ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.model_base
     *
     * @return the value of cg_algo_backend.model_base
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Boolean getModelBase() {
        return modelBase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.model_base
     *
     * @param modelBase the value for cg_algo_backend.model_base
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setModelBase(Boolean modelBase) {
        this.modelBase = modelBase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.usage_user_count
     *
     * @return the value of cg_algo_backend.usage_user_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getUsageUserCount() {
        return usageUserCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.usage_user_count
     *
     * @param usageUserCount the value for cg_algo_backend.usage_user_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setUsageUserCount(Integer usageUserCount) {
        this.usageUserCount = usageUserCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.usage_session_count
     *
     * @return the value of cg_algo_backend.usage_session_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getUsageSessionCount() {
        return usageSessionCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.usage_session_count
     *
     * @param usageSessionCount the value for cg_algo_backend.usage_session_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setUsageSessionCount(Integer usageSessionCount) {
        this.usageSessionCount = usageSessionCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.usage_message_count
     *
     * @return the value of cg_algo_backend.usage_message_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getUsageMessageCount() {
        return usageMessageCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.usage_message_count
     *
     * @param usageMessageCount the value for cg_algo_backend.usage_message_count
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setUsageMessageCount(Integer usageMessageCount) {
        this.usageMessageCount = usageMessageCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.create_user_id
     *
     * @return the value of cg_algo_backend.create_user_id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.create_user_id
     *
     * @param createUserId the value for cg_algo_backend.create_user_id
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.model_type
     *
     * @return the value of cg_algo_backend.model_type
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Integer getModelType() {
        return modelType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.model_type
     *
     * @param modelType the value for cg_algo_backend.model_type
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setModelType(Integer modelType) {
        this.modelType = modelType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_algo_backend.support_plugin
     *
     * @return the value of cg_algo_backend.support_plugin
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public Boolean getSupportPlugin() {
        return supportPlugin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_algo_backend.support_plugin
     *
     * @param supportPlugin the value for cg_algo_backend.support_plugin
     *
     * @mbg.generated Mon Mar 11 15:53:53 CST 2024
     */
    public void setSupportPlugin(Boolean supportPlugin) {
        this.supportPlugin = supportPlugin;
    }
}