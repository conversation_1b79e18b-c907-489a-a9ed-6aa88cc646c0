/**
 * Scaffolding file used to store all the setups needed to run 
 * tests automatically generated by smartunit
 */

package smartunit.com.alipay.codegencore.utils.codescan;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.smartunit.runtime.annotation.SmartSuiteClassExclude;
import org.smartunit.runtime.sandbox.Sandbox;

import static org.smartunit.shaded.org.mockito.Mockito.mock;
import static org.smartunit.shaded.org.mockito.Mockito.withSettings;
@SmartSuiteClassExclude
public class JavaParserBaseListener_SSTest_scaffolding {

  @org.junit.Rule 
  public org.smartunit.runtime.vnet.NonFunctionalRequirementRule nfr = new org.smartunit.runtime.vnet.NonFunctionalRequirementRule();

  private static final java.util.Properties defaultProperties = (java.util.Properties) java.lang.System.getProperties().clone(); 

  private final org.smartunit.runtime.thread.ThreadStopper threadStopper =  new org.smartunit.runtime.thread.ThreadStopper (org.smartunit.runtime.thread.KillSwitchHandler.getInstance(), 3000);


  @BeforeClass 
  public static void initSmartSuiteFramework() { 
    org.smartunit.runtime.RuntimeSettings.className = "com.alipay.codegencore.utils.codescan.JavaParserBaseListener"; 
    org.smartunit.runtime.GuiSupport.initialize(); 
    org.smartunit.runtime.RuntimeSettings.maxNumberOfThreads = 100; 
    org.smartunit.runtime.RuntimeSettings.maxNumberOfIterationsPerLoop = Long.MAX_VALUE; 
    org.smartunit.runtime.RuntimeSettings.mockSystemIn = true; 
    org.smartunit.runtime.RuntimeSettings.sandboxMode = org.smartunit.runtime.sandbox.Sandbox.SandboxMode.RECOMMENDED; 
    org.smartunit.runtime.sandbox.Sandbox.initializeSecurityManagerForSUT(); 
    org.smartunit.runtime.classhandling.JDKClassResetter.init();
    setSystemProperties();
    initializeClasses();
    org.smartunit.runtime.Runtime.getInstance().resetRuntime(); 
    try { initMocksToAvoidTimeoutsInTheTests(); } catch(ClassNotFoundException e) {} 
  } 

  @AfterClass 
  public static void clearSmartSuiteFramework(){ 
    resetClasses(); 
    Sandbox.resetDefaultSecurityManager(); 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
  } 

  @Before 
  public void initTestCase(){ 
    threadStopper.storeCurrentThreads();
    threadStopper.startRecordingTime();
    org.smartunit.runtime.jvm.ShutdownHookHandler.getInstance().initHandler(); 
    org.smartunit.runtime.sandbox.Sandbox.goingToExecuteSUTCode(); 
    setSystemProperties(); 
    org.smartunit.runtime.GuiSupport.setHeadless(); 
    org.smartunit.runtime.Runtime.getInstance().resetRuntime(); 
    org.smartunit.runtime.agent.InstrumentingAgent.activate(); 
  } 

  @After 
  public void doneWithTestCase(){ 
    threadStopper.killAndJoinClientThreads();
    org.smartunit.runtime.jvm.ShutdownHookHandler.getInstance().safeExecuteAddedHooks(); 
    org.smartunit.runtime.classhandling.JDKClassResetter.reset(); 
    org.smartunit.runtime.classhandling.ClassStateSupport.resetCUT(); 
    org.smartunit.runtime.sandbox.Sandbox.doneWithExecutingSUTCode(); 
    org.smartunit.runtime.agent.InstrumentingAgent.deactivate(); 
    org.smartunit.runtime.GuiSupport.restoreHeadlessMode(); 
  } 

  public static void setSystemProperties() {
 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
    java.lang.System.setProperty("java.io.tmpdir", "/var/folders/2g/v426t5v53msd4lghgvvbxq3h0000gn/T/"); 
  }
  private static void initMocksToAvoidTimeoutsInTheTests() throws ClassNotFoundException { 
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AltAnnotationQualifiedNameContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationConstantRestContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationMethodOrConstantRestContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationMethodRestContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationTypeBodyContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationTypeDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationTypeElementDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$AnnotationTypeElementRestContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ArgumentsContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ArrayCreatorRestContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ArrayInitializerContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$BlockContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$BlockStatementContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$CatchClauseContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$CatchTypeContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassBodyContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassBodyDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassCreatorRestContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassOrInterfaceModifierContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassOrInterfaceTypeContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ClassTypeContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$CompilationUnitContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ConstDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ConstantDeclaratorContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ConstructorDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$CreatedNameContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$CreatorContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$DefaultValueContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ElementValueArrayInitializerContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ElementValueContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ElementValuePairContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ElementValuePairsContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnhancedForControlContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnumBodyDeclarationsContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnumConstantContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnumConstantsContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$EnumDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ExplicitGenericInvocationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ExplicitGenericInvocationSuffixContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ExpressionContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ExpressionListContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FieldDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FinallyBlockContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FloatLiteralContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ForControlContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ForInitContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FormalParameterContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FormalParameterListContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$FormalParametersContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$GenericConstructorDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$GenericInterfaceMethodDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$GenericMethodDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$GuardedPatternContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$IdentifierContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ImportDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InnerCreatorContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$IntegerLiteralContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceBodyContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceBodyDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceCommonBodyDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceMemberDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceMethodDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$InterfaceMethodModifierContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaBodyContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaExpressionContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaLVTIListContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaLVTIParameterContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LambdaParametersContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LastFormalParameterContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LiteralContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LocalTypeDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$LocalVariableDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$MemberDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$MethodBodyContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$MethodCallContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$MethodDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ModifierContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ModuleBodyContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ModuleDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ModuleDirectiveContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$NonWildcardTypeArgumentsContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$NonWildcardTypeArgumentsOrDiamondContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$PackageDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ParExpressionContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$PatternContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$PrimaryContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$PrimitiveTypeContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$QualifiedNameContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$QualifiedNameListContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ReceiverParameterContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordBodyContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordComponentContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordComponentListContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RecordHeaderContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$RequiresModifierContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ResourceContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ResourceSpecificationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$ResourcesContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$StatementContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SuperSuffixContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchBlockStatementGroupContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchExpressionContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchLabelContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchLabeledRuleContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$SwitchRuleOutcomeContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeArgumentContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeArgumentsContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeArgumentsOrDiamondContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeBoundContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeDeclarationContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeListContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeParameterContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeParametersContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeTypeContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$TypeTypeOrVoidContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableDeclaratorContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableDeclaratorIdContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableDeclaratorsContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableInitializerContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("com.alipay.codegencore.utils.codescan.JavaParser$VariableModifierContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("org.antlr.v4.runtime.ParserRuleContext", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("org.antlr.v4.runtime.tree.ErrorNode", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
    mock(Class.forName("org.antlr.v4.runtime.tree.TerminalNode", false, JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()), withSettings().stubOnly());
  }

  private static void initializeClasses() {
    org.smartunit.runtime.classhandling.ClassStateSupport.initializeClasses(JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader() , ""
    );
  } 

  private static void resetClasses() {
    org.smartunit.runtime.classhandling.ClassResetter.getInstance().setClassLoader(JavaParserBaseListener_SSTest_scaffolding.class.getClassLoader()); 

    org.smartunit.runtime.classhandling.ClassStateSupport.resetClasses();
  }
}
