package com.alipay.codegencore.utils.code;

import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.exception.BizException;
import com.github.javaparser.Range;
import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.ImportDeclaration;
import com.github.javaparser.ast.Node;
import com.github.javaparser.ast.NodeList;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.EnumDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.expr.Name;
import com.github.javaparser.ast.expr.SimpleName;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0 2024/9/5 20:25
 */
public class JavaATS {

    private static final Logger log = LoggerFactory.getLogger(JavaATS.class);

    /**
     * alipay import prefix
     */
    private static final String COM_ALIPAY_IMPORT_PREFIX = "com.alipay";


    /**
     * 获取指定文件的方法列表
     * @param repoPath
     * @param branch
     * @param paths
     * @return
     */
    public static Map<String, List<SimpleName>> getFileMethods(String repoPath, String branch, Set<String> paths) {

        Map<String, List<SimpleName>> pathMethods = Maps.newHashMap();
        paths.forEach(path -> {
            //获取代码内容
            String fileContent = AntCodeClient.getFileContent(repoPath, branch, path);

            List<SimpleName> methods = getMethodList(fileContent);

            if (CollectionUtils.isNotEmpty(methods)) {
                pathMethods.put(path, methods);
            }
        });

        return pathMethods;
    }


    /**
     * 将文件的 chunk 转成完整方案 chunk 和方法签名
     * @param pathCodes
     * @param pathMethods
     * @return
     */
    public static List<BloopSearchClient.CodeResult> convertMethodChunk(Map<String, List<BloopSearchClient.CodeResult>> pathCodes,
                                                                        Map<String, List<SimpleName>> pathMethods) {

        List<BloopSearchClient.CodeResult> methodChunkList = Lists.newArrayList();
        pathCodes.forEach((path, chunkList) -> {

            StringBuilder methodChunkContent = new StringBuilder();

            List<SimpleName> methods = pathMethods.get(path);
            if (CollectionUtils.isEmpty(methods)) {
                return;
            }

            chunkList.forEach(chunk -> {

                int chunkStartLine = chunk.getStartLine();
                int chunkEndLine = chunk.getEndLine();

                boolean have = false;
                for (SimpleName method : methods) {

                    Optional<Node> nodeOptional = method.getParentNode();
                    if (nodeOptional.isEmpty()) {
                        return;
                    }
                    MethodDeclaration methodDeclaration = (MethodDeclaration) nodeOptional.get();

                    Optional<Range> rangeOptional = methodDeclaration.getRange();
                    if (rangeOptional.isEmpty()) {
                        return;
                    }

                    Range range = rangeOptional.get();
                    int methodStartLine = range.begin.line;
                    int methodEndLine = range.end.line;

                    if (headMethodChunk(methodStartLine, methodEndLine, chunkStartLine)
                            || tailMethodChunk(methodStartLine, methodEndLine, chunkEndLine)
                            || containMethodChunk(methodStartLine, methodEndLine, chunkStartLine, chunkEndLine)) {
                        methodChunkContent.append(methodDeclaration)
                                .append("\n");
                        have = true;
                    } else {
                        methodChunkContent.append(methodDeclaration.getDeclarationAsString())
                                .append("\n");
                    }
                }

                if (!have) {
                    methodChunkContent.insert(0, chunk.getContent());
                }
            });

            BloopSearchClient.CodeResult methodChunk = new BloopSearchClient.CodeResult();
            methodChunk.setContent(methodChunkContent.toString());
            methodChunk.setPath(path);
            methodChunkList.add(methodChunk);
        });

        return methodChunkList;
    }

    /**
     * 获取指定方法依赖的下层类
     * @param repoPath
     * @param branch
     * @param fileContent
     * @param method
     * @return
     */
    public static Collection<String> confirmInterface(String repoPath, String branch, String fileContent, String method) {
        log.info("begin ats repoPath:{} branch:{} method:{}", repoPath, branch, method);
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {

            //解析语法树
            //2、获取 import 信息
            List<Name> imports = getAlipayImports(fileContent);
            log.info("ats import size:{}", imports.size());
            if (CollectionUtils.isEmpty(imports)) {
                return Lists.newArrayList();
            }

            Map<String, String> importNameAndPath = Maps.newHashMap();

            //3、获取仓库文件列表
            Pair<String, String> repoInfo = AntCodeClient.getRepoInfoByRepoPath(repoPath);
            if (repoInfo == null) {
                throw new BizException(ResponseEnum.SVAT_REPO_INFO_ILL);
            }

            List<AntCodeClient.FileCompare> repoFileList = AntCodeClient.getFileListByProject(repoInfo.getLeft(),
                    repoInfo.getRight(), branch);
            log.info("repo file size:{}", repoFileList.size());
            if (CollectionUtils.isEmpty(repoFileList)) {
                throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL);
            }
            List<String> repoFilePathList = repoFileList.stream().map(AntCodeClient.FileCompare::getNewPath).collect(Collectors.toList());
            repoFilePathList.forEach(newPath -> {
                String importPath = StringUtils.substringAfterLast(newPath, "java/");
                String importName = StringUtils.replace(StringUtils.replace(importPath, ".java", ""),
                        "/", ".");
                String name = StringUtils.substringAfterLast(importName, ".");

                //3.1 处理 * 号导入
                for (Name importItem : imports) {
                    if (importName.startsWith(importItem.asString())) {
                        importNameAndPath.put(name, newPath);
                        break;
                    }
                }
            });

            log.info("ats import add * size:{}", importNameAndPath.size());

            //4、获取指定方法内容
            String methodContent = JavaATS.getMethodContent(fileContent, method);
            if (StringUtils.isBlank(methodContent)) {
                throw new BizException(ResponseEnum.SVAT_SEARCH_CODE_FAIL);
            }

            //5、从方法中匹配 import
            Map<String, String> useImport = Maps.newHashMap();
            String methodContentLower = StringUtils.lowerCase(methodContent.toLowerCase());
            importNameAndPath.forEach((name, path) -> {
                if (methodContentLower.contains(StringUtils.lowerCase(name))) {
                    useImport.put(name, path);
                }
            });
            log.info("method match use import: {}", useImport);

            //6、补充 Impl
            Map<String, String> importNameAndPathImpl = Maps.newHashMap();
            useImport.forEach((name, path) -> {
                String implName = name + "Impl";
                importNameAndPathImpl.put(implName, implName + ".java");
            });

            //7、判断路径正确性
            repoFilePathList.forEach(path -> {
                importNameAndPathImpl.forEach((implName, implNameJava) -> {
                    if (path.endsWith(implNameJava)) {
                        useImport.put(implName, path);
                    }
                });
            });
            log.info("method match use service and impl: {}", useImport);

            return useImport.values();
        } catch (Exception e) {
            log.error("ats found exception", e);
        } finally {
            log.info("ats cost:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
            stopwatch.stop();
        }

        return List.of();
    }

    /**
     * 获取 指定方法信息
     *
     * @param javaFileContent java源文件内容
     * @return unit
     */
    public static Optional<SimpleName> getMethodInfo(String javaFileContent, String methodName) {
        return getMethodList(javaFileContent).stream().filter(item -> methodName.equals(item.getIdentifier())).findFirst();
    }


    /**
     * 获取方法列表
     * @param javaFileContent
     * @return
     */
    public static List<SimpleName> getMethodList(String javaFileContent) {

        try {
            CompilationUnit cu = StaticJavaParser.parse(javaFileContent);
            List<Node> childNodes = cu.getChildNodes();

            Optional<Node> classOrInterfaceNode = childNodes.stream()
                    .filter(item -> item instanceof ClassOrInterfaceDeclaration || item instanceof EnumDeclaration).findFirst();

            return classOrInterfaceNode.map(node -> node
                            .getChildNodes().stream()
                            .filter(item -> item instanceof MethodDeclaration)
                            .map(item -> ((MethodDeclaration) item).getName())
                            .collect(Collectors.toList()))
                    .orElse(List.of());
        } catch (Exception e) {
            log.error("parse failed. not support", e);
            return List.of();
        }
    }

    /**
     * 获取 指定方法代码块
     *
     * @param javaFileContent java源文件内容
     * @return unit
     */
    public static String getMethodContent(String javaFileContent, String methodName) {

        Optional<SimpleName> methodSimpleName = getMethodInfo(javaFileContent, methodName);

        return methodSimpleName.map(item -> item.getParentNode().map(Node::toString).orElse(null)).orElse(null);
    }

    /**
     * 获取 alipay import 信息
     * @param javaFileContent
     * @return
     */
    public static List<Name> getAlipayImports(String javaFileContent) {
        List<Name> imports = getImports(javaFileContent);
        if (CollectionUtils.isEmpty(imports)) {
            return imports;
        }

        return imports.stream().filter(item ->
                        item.toString().startsWith(COM_ALIPAY_IMPORT_PREFIX))
                .collect(Collectors.toList());
    }

    /**
     * 获取 import 信息
     *
     * @param javaFileContent java源文件内容
     * @return unit
     */
    public static List<Name> getImports(String javaFileContent) {
        CompilationUnit cu = StaticJavaParser.parse(javaFileContent);
        NodeList<ImportDeclaration> importDeclarations = cu.getImports();
        return importDeclarations.stream().map(ImportDeclaration::getName).collect(Collectors.toList());
    }

    /**
     * chunk 首行是否落在方法中
     * @param methodStartLine
     * @param methodEndLine
     * @param chunkStartLine
     * @return
     */
    private static boolean headMethodChunk(int methodStartLine, int methodEndLine,
                                           int chunkStartLine) {
        return chunkStartLine >= methodStartLine && chunkStartLine <= methodEndLine;
    }

    /**
     * chunk 尾行是否落在方法中
     * @param methodStartLine
     * @param methodEndLine
     * @param chunkEndLine
     * @return
     */
    private static boolean tailMethodChunk(int methodStartLine, int methodEndLine,
                                           int chunkEndLine) {
        return chunkEndLine >= methodStartLine && chunkEndLine <= methodEndLine;
    }

    /**
     * chunk 包含整个方法
     * @param methodStartLine
     * @param methodEndLine
     * @param chunkStartLine
     * @param chunkEndLine
     * @return
     */
    private static boolean containMethodChunk(int methodStartLine, int methodEndLine,
                                           int chunkStartLine, int chunkEndLine) {
        return chunkStartLine <= methodStartLine && chunkEndLine >= methodEndLine;
    }

    /**
     * 扩写 method chunk
     */
    public static class MethodChunk {

        private int startLine = 0;

        private int endLine = 0;

        private String path;

        private String content;

        private double score = 0;

        public int getStartLine() {
            return startLine;
        }

        public void setStartLine(int startLine) {
            this.startLine = startLine;
        }

        public int getEndLine() {
            return endLine;
        }

        public void setEndLine(int endLine) {
            this.endLine = endLine;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public double getScore() {
            return score;
        }

        public void setScore(double score) {
            this.score = score;
        }
    }

}
