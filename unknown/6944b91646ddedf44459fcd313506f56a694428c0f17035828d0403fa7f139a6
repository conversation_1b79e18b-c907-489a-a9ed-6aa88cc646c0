package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.domain.TokenDO;
import com.alipay.codegencore.dal.example.TokenDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.Update;

public interface TokenDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    long countByExample(TokenDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    int deleteByExample(TokenDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    @Delete({
        "delete from cg_token",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    @Insert({
        "insert into cg_token (gmt_create, gmt_modified, ",
        "user, token, uri_pattern_list, ",
        "description, balance, ",
        "owner_user_id, enable_status, ",
        "app_name)",
        "values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, ",
        "#{user,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR}, #{uriPatternList,jdbcType=VARCHAR}, ",
        "#{description,jdbcType=VARCHAR}, #{balance,jdbcType=DECIMAL}, ",
        "#{ownerUserId,jdbcType=VARCHAR}, #{enableStatus,jdbcType=TINYINT}, ",
        "#{appName,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(TokenDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    int insertSelective(TokenDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    List<TokenDO> selectByExample(TokenDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, user, token, uri_pattern_list, description, balance, ",
        "owner_user_id, enable_status, app_name",
        "from cg_token",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @ResultMap("com.alipay.codegencore.dal.mapper.TokenDOMapper.BaseResultMap")
    TokenDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    int updateByExampleSelective(@Param("record") TokenDO record, @Param("example") TokenDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    int updateByExample(@Param("record") TokenDO record, @Param("example") TokenDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    int updateByPrimaryKeySelective(TokenDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_token
     *
     * @mbg.generated Mon Mar 11 15:11:56 CST 2024
     */
    @Update({
        "update cg_token",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "user = #{user,jdbcType=VARCHAR},",
          "token = #{token,jdbcType=VARCHAR},",
          "uri_pattern_list = #{uriPatternList,jdbcType=VARCHAR},",
          "description = #{description,jdbcType=VARCHAR},",
          "balance = #{balance,jdbcType=DECIMAL},",
          "owner_user_id = #{ownerUserId,jdbcType=VARCHAR},",
          "enable_status = #{enableStatus,jdbcType=TINYINT},",
          "app_name = #{appName,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(TokenDO record);
}