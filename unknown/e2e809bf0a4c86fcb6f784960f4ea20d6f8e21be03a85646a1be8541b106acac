package com.alipay.codegencore.model.request;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.model.rag.DocSearchResultItem;
import com.alipay.codegencore.model.openai.ChatCompletionRequest;
import com.alipay.codegencore.model.openai.ChatFunction;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.util.AlgoBackendUtil;

import java.math.BigDecimal;
import java.util.List;

/**
 * codeGeex请求数据格式
 *
 * <AUTHOR>
 */
public class CodegptRequestBean {
    /**
     * 请求id
     */
    @JSONField(name = "tbase_id")
    private String id;

    /**
     * 输入模型的上文, List<ChatMessage> 或者 message
     */
    private Object prompt;

    private List<ChatFunction> functions;

    /**
     * 文档搜索结果
     */
    private List<DocSearchResultItem> docs;


    /**
     * 对每条传入文本，生成的备选输出文本条数
     */
    @JSONField(name = "beam_width")
    private Integer beamWidth;

    /**
     * 模型在每次生成下个token的时候 会以softmax的结果为基础得出一个概率分布 以这个概率分布来选择下一个token temperature调整了这个分布的均匀程
     * 可选，不传入则选择默认分布
     */
    private BigDecimal temperature;

    /**
     * 期望模型输出的最大长度
     */
    @JSONField(name = "out_seq_length")
    private int outSeqLength;

    /**
     * int	将模型置信度排名低于k-th的token 置信度都置为0 排除出随机分布的计算可选，不传入则默认为50(可选，不传入则默认为50)
     */
    @JSONField(name = "top_k")
    private Integer topK;

    /**
     * 在模型置信度排名下 只计算前p比例的token 后续的排除出随机分布的计算(可选，不传入则默认关闭该优化)
     */
    @JSONField(name = "top_p")
    private BigDecimal topP;

    /**
     * 防止被生成的语句置信度随着长度衰减 导致无法与较短的文本对比(可选，不传入则默认关闭该优化)
     */
    @JSONField(name = "len_penalty")
    private Integer lenPenalty;

    /**
     * 调整上文中已多次重复的token被生成的概率(可选，不传入则默认关闭该优化)
     */
    @JSONField(name = "repetition_penalty")
    private BigDecimal repetitionPenalty;

    /**
     * 随机种子(可选，不传入则默认关闭该优化)
     */
    @JSONField(name = "random_seed")
    private BigDecimal randomSeed;
    /**
     * 最大长度为 4 的字符串列表，一旦生成的 tokens 包含其中的内容，将停止生成并返回结果
     */
    @JSONField(name = "stop_words")
    private List<String> stopWords;

    @JSONField(name = "max_length")
    private Integer maxLength;

    public List<String> getStopWords() {
        return stopWords;
    }

    public void setStopWords(List<String> stopWords) {
        this.stopWords = stopWords;
    }

    public Object getPrompt() {
        return prompt;
    }

    public void setPrompt(List<ChatMessage> prompt) {
        this.prompt = prompt;
    }

    public void setPrompt(String prompt){
        this.prompt = prompt;
    }

    public List<ChatFunction> getFunctions() {
        return functions;
    }

    public void setFunctions(List<ChatFunction> functions) {
        this.functions = functions;
    }

    public List<DocSearchResultItem> getDocs() {
        return docs;
    }

    public void setDocs(List<DocSearchResultItem> docs) {
        this.docs = docs;
    }

    public int getOutSeqLength() {
        return outSeqLength;
    }

    public void setOutSeqLength(int outSeqLength) {
        this.outSeqLength = outSeqLength;
    }

    public CodegptRequestBean() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getTopK() {
        return topK;
    }

    public void setTopK(int topK) {
        this.topK = topK;
    }

    public BigDecimal getRepetitionPenalty() {
        return repetitionPenalty;
    }

    public void setRepetitionPenalty(BigDecimal repetitionPenalty) {
        this.repetitionPenalty = repetitionPenalty;
    }

    public Integer getBeamWidth() {
        return beamWidth;
    }

    public void setBeamWidth(Integer beamWidth) {
        this.beamWidth = beamWidth;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public BigDecimal getTopP() {
        return topP;
    }

    public void setTopP(BigDecimal topP) {
        this.topP = topP;
    }

    public Integer getLenPenalty() {
        return lenPenalty;
    }

    public void setLenPenalty(Integer lenPenalty) {
        this.lenPenalty = lenPenalty;
    }

    public BigDecimal getRandomSeed() {
        return randomSeed;
    }

    public void setRandomSeed(BigDecimal randomSeed) {
        this.randomSeed = randomSeed;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }

    /**
     * 通过request生产 类GodeGEEX请求数据格式
     *
     * @param request
     */
    public CodegptRequestBean(ChatCompletionRequest request) {
        this.outSeqLength = request.getMaxTokens() == null ? 128 : request.getMaxTokens();
        this.maxLength = request.getMaxTokens();

        if(request.getMessages() != null){
            this.prompt = request.getMessages();
        }else {
            this.prompt = request.getPrompt();
        }

        this.topK = 40;
        this.repetitionPenalty = new BigDecimal("1.1");
        this.topP = new BigDecimal("0.9");
        this.temperature = new BigDecimal("0.2");
    }

    /**
     * 通过request生产 类GodeGEEX请求数据格式
     * @param request
     * @param algoBackendDO
     * @param requestId
     */
    public CodegptRequestBean(ChatCompletionRequest request, AlgoBackendDO algoBackendDO, String requestId) {
        this.outSeqLength = AlgoBackendUtil.exactOutSeqLengthConfig(algoBackendDO) == null ? 512 : AlgoBackendUtil.exactOutSeqLengthConfig(algoBackendDO);
        this.maxLength = algoBackendDO.getMaxToken();
        this.topK = AlgoBackendUtil.exactTopKConfig(algoBackendDO);
        this.topP = AlgoBackendUtil.exactTopPConfig(algoBackendDO);
        this.repetitionPenalty = AlgoBackendUtil.exactRepetitionPenaltyConfig(algoBackendDO);
        this.temperature = AlgoBackendUtil.exactTemperatureConfig(algoBackendDO);
        this.beamWidth = AlgoBackendUtil.exactBeamWidthConfig(algoBackendDO);
        this.lenPenalty = AlgoBackendUtil.exactLenPenaltyConfig(algoBackendDO);
        this.randomSeed = AlgoBackendUtil.exactRandomSeedConfig(algoBackendDO);
        this.stopWords = AlgoBackendUtil.exactStopWordsConfig(algoBackendDO);

        if(request.getMessages() != null){
            this.prompt = JSON.parseArray(JSON.toJSONString(request.getMessages()), ChatMessage.class);
        }else {
            this.prompt = request.getPrompt();
        }
        this.functions = request.getFunctions();
        this.docs = request.getDocs();

        this.id = requestId;
    }
}
