/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils.codescan;

import com.alipay.codegencore.utils.codescan.JavaLexer;
import org.antlr.v4.runtime.Vocabulary;
import org.antlr.v4.runtime.atn.ATN;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.smartunit.runtime.SmartAssertions.verifyException;
import static org.smartunit.shaded.org.mockito.Mockito.CALLS_REAL_METHODS;
import static org.smartunit.shaded.org.mockito.Mockito.mock;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class JavaLexer_SSTest extends JavaLexer_SSTest_scaffolding {
// allCoveredLines:[148, 154, 165, 170, 175, 180, 185, 190, 197, 198]

  @Test(timeout = 4000)
  public void test_cleanData_0()  throws Throwable  {
      //caseID:0fd4724d00ff0fd090b0a44d51e1be97
      //CoveredLines: [197, 198]
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: cleanData
      // Undeclared exception!
      try { 
        javaLexer0.cleanData();
      } catch(Throwable e) {
         verifyException("com.alipay.codegencore.utils.codescan.JavaLexer", e);
         assertEquals("java.lang.NullPointerException", e.getClass().getName());
      }
  }

  @Test(timeout = 4000)
  public void test_getATN_1()  throws Throwable  {
      //caseID:4fa0902874b7826f14ac08f80be0101e
      //CoveredLines: [190]
      //Assert: assertEquals(51, method_result.getNumberOfDecisions());
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: getATN
      ATN aTN0 = javaLexer0.getATN();
      
      //Test Result Assert
      assertEquals(51, aTN0.getNumberOfDecisions());
  }

  @Test(timeout = 4000)
  public void test_getChannelNames_2()  throws Throwable  {
      //caseID:2eef5102b4fc4588077277773666ff54
      //CoveredLines: [180]
      //Assert: assertEquals(2, method_result.length);
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: getChannelNames
      String[] stringArray0 = javaLexer0.getChannelNames();
      
      //Test Result Assert
      assertEquals(2, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test_getGrammarFileName_3()  throws Throwable  {
      //caseID:9ac9eba8d6c0fcae4b076613eb167aa0
      //CoveredLines: [165]
      //Assert: assertEquals("JavaLexer.g4", method_result);
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: getGrammarFileName
      String string0 = javaLexer0.getGrammarFileName();
      
      //Test Result Assert
      assertEquals("JavaLexer.g4", string0);
  }

  @Test(timeout = 4000)
  public void test_getModeNames_4()  throws Throwable  {
      //caseID:5af9a7678b4b30ac138020f492e47254
      //CoveredLines: [185]
      //Assert: assertEquals(1, method_result.length);
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: getModeNames
      String[] stringArray0 = javaLexer0.getModeNames();
      
      //Test Result Assert
      assertEquals(1, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test_getRuleNames_5()  throws Throwable  {
      //caseID:8684ff7995ae691b0235bc49bded05cd
      //CoveredLines: [170]
      //Assert: assertEquals(135, method_result.length);
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: getRuleNames
      String[] stringArray0 = javaLexer0.getRuleNames();
      
      //Test Result Assert
      assertEquals(135, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test_getSerializedATN_6()  throws Throwable  {
      //caseID:d44ccd39c59ea0be8bd5a04438887008
      //CoveredLines: [175]
      //Assert: assertNotNull(method_result);
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: getSerializedATN
      String string0 = javaLexer0.getSerializedATN();
      
      //Test Result Assert
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test_getTokenNames_7()  throws Throwable  {
      //caseID:8bdd17af0e624f3aef75f9b7159f131f
      //CoveredLines: [148]
      //Assert: assertEquals(129, method_result.length);
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: getTokenNames
      String[] stringArray0 = javaLexer0.getTokenNames();
      
      //Test Result Assert
      assertEquals(129, stringArray0.length);
  }

  @Test(timeout = 4000)
  public void test_getVocabulary_8()  throws Throwable  {
      //caseID:6dd5d67554902de056f90c85fca6f670
      //CoveredLines: [154]
      //Assert: assertEquals(128, method_result.getMaxTokenType());
      
      //mock javaLexer0
      JavaLexer javaLexer0 = mock(JavaLexer.class, CALLS_REAL_METHODS);
      
      //Call method: getVocabulary
      Vocabulary vocabulary0 = javaLexer0.getVocabulary();
      
      //Test Result Assert
      assertEquals(128, vocabulary0.getMaxTokenType());
  }
}
