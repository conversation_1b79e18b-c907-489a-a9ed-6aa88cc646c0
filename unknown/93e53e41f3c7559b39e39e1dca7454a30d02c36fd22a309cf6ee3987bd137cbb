package com.alipay.codegencore.web.openapi;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.mutable.MutablePair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.aclinkelib.common.service.facade.model.v2.AntCIComponentRestRequest;
import com.alipay.aclinkelib.common.service.facade.model.v2.AntCIComponentRestResponse;
import com.alipay.aclinkelib.common.service.facade.model.v2.AntCIComponentStatus;
import com.alipay.codegencore.model.domain.AlgoBackendDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.enums.*;
import com.alipay.codegencore.model.exception.BizException;
import com.alipay.codegencore.model.model.AciOperateAlgoBackendModel;
import com.alipay.codegencore.model.model.AlgoBackendModel;
import com.alipay.codegencore.service.codegpt.SceneService;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.AlgoBackendService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.utils.CommonTools;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UrlPathHelper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * ACI组件控制器，处理组件相关请求
 */
@RestController
@RequestMapping("/api/aciComponent")
@Slf4j
public class AciComponentController {

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private AlgoBackendService algoBackendService;
    @Resource
    private UserAclService userAclService;
    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;
    @Resource
    private SceneService sceneService;

    /**
     * 创建或更新算法后端模型接口
     *
     * @param antCIComponentRestRequest ACI组件的入参
     * @return 响应结果
     */
    @PostMapping("/operateModel")
    public AntCIComponentRestResponse operateModel(HttpServletRequest httpServletRequest,
                                                   @RequestHeader(value = "codegpt_user") String codeGPTUser,
                                                   @RequestHeader(value = "codegpt_token") String codeGPTToken,
                                                   @RequestBody AntCIComponentRestRequest antCIComponentRestRequest) {
        log.info("接收到aci组件的操作模型请求,codeGPTUser:{},param:{}", codeGPTUser, JSON.toJSONString(antCIComponentRestRequest));
        AntCIComponentRestResponse response = new AntCIComponentRestResponse();
        Map<String, String> outputs = new HashMap<>();
        response.setOutputs(outputs);
        response.setExecutionTaskId(antCIComponentRestRequest.getExecutionTaskId());
        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
        if (!userAclService.isAuthorizedByToken(codeGPTUser, codeGPTToken, uri)) {
            log.warn("operateModel request not authorized, user: {}", codeGPTUser);
            response.setStatus(AntCIComponentStatus.FAILED);
            outputs.put("content", "您无权操作");
            return response;
        }
        // 先更新补全工号
        updateEmpId(antCIComponentRestRequest);
        Pair<Boolean, AntCIComponentRestResponse> verifyRet = verifyParam(antCIComponentRestRequest);
        if (!verifyRet.getKey()) {
            return verifyRet.getValue();
        }
        JSONObject aciComponentConfig = JSON.parseObject(codeGPTDrmConfig.getAciComponentConfig());
        String operateModelDocumentUrl = aciComponentConfig.getString("operateModelDocumentUrl");
        String sceneUrl = aciComponentConfig.getString("sceneUrl");
        String sceneDesc = aciComponentConfig.getString("sceneDesc");
        AciOperateAlgoBackendModel aciOperateAlgoBackendModel = getAciOperateAlgoBackendModel(antCIComponentRestRequest, codeGPTUser);
        SceneDO sceneDO = getSceneDO(aciOperateAlgoBackendModel, sceneDesc);
        log.info("接收到aci组件的操作模型请求,实际操作参数:{},sceneDO:{}", JSON.toJSONString(aciOperateAlgoBackendModel), JSON.toJSONString(sceneDO));
        String empId = antCIComponentRestRequest.getInputs().get("empId");
        String environment = antCIComponentRestRequest.getInputs().get("environment");
        String modelName = aciOperateAlgoBackendModel.getModel();
        UserAuthDO userAuthDO = userAclService.queryUserByEmpId(empId);
        AlgoBackendModel algoBackendModel = new AlgoBackendModel();
        algoBackendModel.setCreateUserId(userAuthDO.getId().intValue());
        algoBackendModel.setOwnerUserId(userAuthDO.getId().intValue());
        sceneDO.setOwnerUserId(userAuthDO.getId());
        sceneDO.setUserId(userAuthDO.getId());
        BeanUtils.copyProperties(aciOperateAlgoBackendModel, algoBackendModel);
        if (OperateType.CREATE == aciOperateAlgoBackendModel.getOperateType()) {
            boolean ret = algoBackendService.addModel(algoBackendModel, userAuthDO);
            if (ret) {
                response.setStatus(AntCIComponentStatus.SUCCESS);
                outputs.put("content", "模型在CodeFuse平台创建成功");
                outputs.put("modelName", modelName);
                outputs.put("operateModelDocumentUrl", operateModelDocumentUrl);
                outputs.put("modelAllConfig", JSON.toJSONString(aciOperateAlgoBackendModel));
            } else {
                response.setStatus(AntCIComponentStatus.FAILED);
                outputs.put("content", "模型在CodeFuse平台创建失败");
                return response;
            }
            try {
                Long sceneId = sceneService.addScene(sceneDO);
                outputs.put("sceneUrl", sceneUrl + sceneId);
            } catch (BizException e) {
                outputs.put("content", "模型在CodeFuse平台创建成功,场景创建失败");
            }
        } else if (OperateType.UPDATE == aciOperateAlgoBackendModel.getOperateType()) {
            boolean ret = algoBackendService.updateModel(algoBackendModel, userAuthDO);
            if (ret) {
                outputs.put("content", "模型在CodeFuse平台更新成功");
                outputs.put("modelName", modelName);
                outputs.put("operateModelDocumentUrl", operateModelDocumentUrl);
                outputs.put("modelAllConfig", JSON.toJSONString(aciOperateAlgoBackendModel));
                response.setStatus(AntCIComponentStatus.SUCCESS);
            } else {
                response.setStatus(AntCIComponentStatus.FAILED);
                outputs.put("content", "模型在CodeFuse平台更新失败");
                return response;
            }
            SceneDO sceneDODb = sceneService.getSceneByBizId(getAciSceneBizId(aciOperateAlgoBackendModel.getModel()));
            if (sceneDODb == null) {
                try {
                    Long sceneId = sceneService.addScene(sceneDO);
                    outputs.put("sceneUrl", sceneUrl + sceneId);
                } catch (BizException e) {
                    outputs.put("content", "模型在CodeFuse平台更新成功,场景创建失败");
                }
                return response;
            } else {
                sceneDO.setId(sceneDODb.getId());
                sceneDO.setUsageCount(sceneDODb.getUsageCount());
                sceneDO.setVisableEnv(sceneDODb.getVisableEnv());
                sceneService.updateScene(sceneDO);
            }
            outputs.put("sceneUrl", sceneUrl + sceneDODb.getId());
        } else {
            response.setStatus(AntCIComponentStatus.FAILED);
            outputs.put("content", "操作失败,OperateType无法识别");
            return response;
        }
        if ("pre".equals(environment)) {
            String completeSceneUrl = outputs.get("sceneUrl");
            completeSceneUrl = completeSceneUrl + "&modelEnv=pre";
            outputs.put("sceneUrl", completeSceneUrl);
        }
        return response;
    }

    private void updateEmpId(AntCIComponentRestRequest antCIComponentRestRequest) {
        if (antCIComponentRestRequest == null ||
                antCIComponentRestRequest.getInputs() == null ||
                !antCIComponentRestRequest.getInputs().containsKey("empId")) {
            return;
        }
        Map<String, String> inputMap = antCIComponentRestRequest.getInputs();
        String empId = inputMap.get("empId");
        inputMap.put("empId", CommonTools.getEmpIdAdd0(empId));
        antCIComponentRestRequest.setInputs(inputMap);
    }

    private SceneDO getSceneDO(AciOperateAlgoBackendModel aciOperateAlgoBackendModel, String sceneDesc) {
        SceneDO sceneDO = new SceneDO();
        sceneDO.setName(aciOperateAlgoBackendModel.getModel() + "助手");
        sceneDO.setDescription(sceneDesc);
        sceneDO.setMode(0);
        sceneDO.setModel(aciOperateAlgoBackendModel.getModel());
        sceneDO.setUserId(aciOperateAlgoBackendModel.getOwnerUserId().longValue());
        // aci创建助手默认所有人可见
        sceneDO.setVisableUser(SceneVisableUserEnum.VISABLE_USER.getCode());
        // 审核通过
        sceneDO.setAuditStatus(AuditStatusSceneEnum.PAAS.getCode());
        // 启用
        sceneDO.setEnable(1);
        sceneDO.setSceneTag("other");
        sceneDO.setVisableEnv(VisableEnvEnum.ALL.getCode());
        sceneDO.setBizId(getAciSceneBizId(aciOperateAlgoBackendModel.getModel()));
        return sceneDO;
    }

    private String getAciSceneBizId(String modelName) {
        return modelName;
    }

    private AciOperateAlgoBackendModel getAciOperateAlgoBackendModel(AntCIComponentRestRequest antCIComponentRestRequest, String codeGPTUser) {
        AciOperateAlgoBackendModel aciOperateAlgoBackendModel = new AciOperateAlgoBackendModel();
        Map<String, String> inputMap = antCIComponentRestRequest.getInputs();
        String empId = inputMap.get("empId");
        String model = inputMap.get("model");
        String modelDescription = inputMap.get("modelDescription");
        String baseModel = inputMap.get("baseModel");
        String sceneName = inputMap.get("sceneName");
        String chainName = inputMap.get("chainName");
        String config = inputMap.get("config");
        JSONObject configJson = new JSONObject();
        if (StringUtils.isNotBlank(config)) {
            configJson = JSONObject.parseObject(config);
            if (configJson.containsKey("model")) {
                model = configJson.getString("model");
            }
            if (configJson.containsKey("modelDescription")) {
                modelDescription = configJson.getString("modelDescription");
            }
            if (configJson.containsKey("sceneName")) {
                sceneName = configJson.getString("sceneName");
            }
            if (configJson.containsKey("chainName")) {
                chainName = configJson.getString("chainName");
            }
            if (configJson.containsKey("baseModel")) {
                baseModel = configJson.getString("baseModel");
            }
        }
        if ("mass".equalsIgnoreCase(codeGPTUser)) {
            model = "MASS_" + model.replace("-", "_").toUpperCase();
        } else {
            model = "ACI_" + model.replace("-", "_").toUpperCase();
        }
        AlgoBackendDO algoBackendDODb = algoBackendService.getAlgoBackendByNameNoJump(model);
        // 如果模型已存在则更新模型配置
        if (algoBackendDODb != null) {
            BeanUtils.copyProperties(algoBackendDODb, aciOperateAlgoBackendModel);
            aciOperateAlgoBackendModel.setOperateType(OperateType.UPDATE);
            if (StringUtils.isNotBlank(modelDescription)) {
                aciOperateAlgoBackendModel.setModelDescription(modelDescription);
            }
            updateFromConfigJson(aciOperateAlgoBackendModel, sceneName, chainName, configJson, baseModel);
            return aciOperateAlgoBackendModel;
        }
        // 如果模型不存在则新增模型
        Long userId = codeFuseUserAuthService.empId2UserId(empId);
        buildDefaultAlgoBackendModel(aciOperateAlgoBackendModel, model, modelDescription, userId, baseModel);
        updateFromConfigJson(aciOperateAlgoBackendModel, sceneName, chainName, configJson, baseModel);
        return aciOperateAlgoBackendModel;
    }

    private void updateFromConfigJson(AciOperateAlgoBackendModel aciOperateAlgoBackendModel, String sceneName, String chainName, JSONObject configJson, String baseModel) {
        String implConfig = aciOperateAlgoBackendModel.getImplConfig();
        JSONObject implConfigObj = StringUtils.isBlank(implConfig)? new JSONObject() : JSONObject.parseObject(implConfig);
        implConfigObj.put("sceneName", sceneName);
        implConfigObj.put("chainName", chainName);
        updateModelDiffParam(aciOperateAlgoBackendModel, baseModel, implConfigObj);
        if (configJson.containsKey("enable")) {
            aciOperateAlgoBackendModel.setEnable(configJson.getInteger("enable") != 0);
        }
        if (configJson.containsKey("jump")) {
            aciOperateAlgoBackendModel.setJump(configJson.getString("jump"));
        }
        if (configJson.containsKey("visableUser")) {
            aciOperateAlgoBackendModel.setVisableUser(configJson.getInteger("visableUser"));
        }
        if (configJson.containsKey("visableEnv")) {
            aciOperateAlgoBackendModel.setVisableEnv(configJson.getInteger("visableEnv"));
        }
        if (configJson.containsKey("maxToken")) {
            aciOperateAlgoBackendModel.setMaxToken(configJson.getInteger("maxToken"));
        }
        if (configJson.containsKey("maxRound")) {
            aciOperateAlgoBackendModel.setMaxRound(configJson.getInteger("maxRound"));
        }
        if (configJson.containsKey("impl")) {
            aciOperateAlgoBackendModel.setImpl(configJson.getString("impl"));
        }
        if (configJson.containsKey("needHealthCheck")) {
            aciOperateAlgoBackendModel.setNeedHealthCheck(configJson.getInteger("needHealthCheck") != 0);
        }
        if (configJson.containsKey("modelDescription")) {
            aciOperateAlgoBackendModel.setModelDescription(configJson.getString("modelDescription"));
        }
        if (configJson.containsKey("enableGptCache")) {
            aciOperateAlgoBackendModel.setEnableGptCache(configJson.getBoolean("enableGptCache"));
        }
        if (configJson.containsKey("implConfig")) {
            JSONObject implConfigParam = configJson.getJSONObject("implConfig");
            implConfigObj.putAll(implConfigParam.getInnerMap());
        }
        aciOperateAlgoBackendModel.setImplConfig(implConfigObj.toJSONString());
    }

    private void buildDefaultAlgoBackendModel(AciOperateAlgoBackendModel aciOperateAlgoBackendModel, String model, String modelDescription, Long userId, String baseModel) {
        aciOperateAlgoBackendModel.setOperateType(OperateType.CREATE);
        aciOperateAlgoBackendModel.setModel(model);
        aciOperateAlgoBackendModel.setModelDescription(modelDescription);
        aciOperateAlgoBackendModel.setOwnerUserId(userId.intValue());
        // 启用
        aciOperateAlgoBackendModel.setEnable(true);
        // 所有环境可见
        aciOperateAlgoBackendModel.setVisableEnv(1);
        // 所有人可见
        aciOperateAlgoBackendModel.setVisableUser(1);
        // 输入的最长token
        aciOperateAlgoBackendModel.setMaxToken(1280);
        aciOperateAlgoBackendModel.setMaxRound(1);
        // 默认关闭健康检查
        aciOperateAlgoBackendModel.setNeedHealthCheck(false);
        // 默认关闭缓存
        aciOperateAlgoBackendModel.setEnableGptCache(false);
        JSONObject implConfig = getImplConfig();
        updateModelDiffParam(aciOperateAlgoBackendModel, baseModel, implConfig);
        aciOperateAlgoBackendModel.setImplConfig(implConfig.toJSONString());
    }

    @NotNull
    private static JSONObject getImplConfig() {
        JSONObject implConfig = new JSONObject(true);
        implConfig.put("requestTimeOut", "40000");
        implConfig.put("topK", "40");
        implConfig.put("topP", "0.9");
        implConfig.put("outSeqLength", "768");
        implConfig.put("temperature", "0.2");
        implConfig.put("firstStreamDataWaitTime", "20000");
        implConfig.put("streamDataPollingStep", "50");
        implConfig.put("commonStreamDataWaitTime", "5000");
        implConfig.put("bufferMinLength", "5");
        implConfig.put("bufferLengthGrowthStep", "5");
        implConfig.put("bufferMaxLength", "20");
        return implConfig;
    }

    private void updateModelDiffParam(AciOperateAlgoBackendModel aciOperateAlgoBackendModel, String baseModel, JSONObject implConfig) {
        JSONObject aciModelConfig = JSONObject.parseObject(codeGPTDrmConfig.getAciModelConfig());
        JSONArray mayaStream = JSONArray.parseArray(aciModelConfig.getString("MayaStream"));
        boolean contains = false;
        for (Object drmModel : mayaStream) {
            contains = baseModel.contains(drmModel + "");
            if (contains) {
                break;
            }
        }
        JSONObject drmImplConfig;
        if (contains) {
            aciOperateAlgoBackendModel.setImpl(AlgoBackendImplEnum.MayaStreamModelHandler.name());
            drmImplConfig = JSONObject.parseObject(aciModelConfig.getString(AlgoBackendImplEnum.MayaStreamModelHandler.name()));
        }
        else {
            aciOperateAlgoBackendModel.setImpl(AlgoBackendImplEnum.CodeGPTModelHandler.name());
            drmImplConfig = JSONObject.parseObject(aciModelConfig.getString(AlgoBackendImplEnum.CodeGPTModelHandler.name()));
        }
        implConfig.putAll(drmImplConfig);
    }

    private Pair<Boolean, AntCIComponentRestResponse> verifyParam(AntCIComponentRestRequest antCIComponentRestRequest) {
        AntCIComponentRestResponse response = new AntCIComponentRestResponse();
        Map<String, String> outputs = new HashMap<>();
        response.setOutputs(outputs);
        response.setExecutionTaskId(antCIComponentRestRequest.getExecutionTaskId());
        Map<String, String> inputMap = antCIComponentRestRequest.getInputs();
        String empId = inputMap.get("empId");
        String model = inputMap.get("model");
        String environment = inputMap.get("environment");
        String baseModel = inputMap.get("baseModel");
        String sceneName = inputMap.get("sceneName");
        String chainName = inputMap.get("chainName");
        String config = inputMap.get("config");
        if (StringUtils.isAnyBlank(empId, model, baseModel, sceneName, chainName, environment)) {
            response.setStatus(AntCIComponentStatus.FAILED);
            outputs.put("content", "必填参数不能为空");
            return new MutablePair<>(false, response);
        }
        if (StringUtils.isNotBlank(config)) {
            try {
                JSONObject configJson = JSONObject.parseObject(config);
                if (configJson.containsKey("model")) {
                    model = configJson.getString("model");
                }
                if (configJson.containsKey("implConfig")) {
                    JSONObject.parseObject(configJson.getString("implConfig"));
                }
            } catch (Exception e) {
                response.setStatus(AntCIComponentStatus.FAILED);
                outputs.put("content", "config格式不正确,必须为json格式,如有implConfig字段也必须为json格式");
                return new MutablePair<>(false, response);
            }
        }
        model = "ACI_" + model.replace("-","_").toUpperCase();
        if (model.length() >= 64) {
            response.setStatus(AntCIComponentStatus.FAILED);
            outputs.put("content", "模型名称:" + model + "太长,无法操作,请在config中指定更短的模型名称");
            return new MutablePair<>(false, response);
        }
        JSONObject aciModelConfig = JSONObject.parseObject(codeGPTDrmConfig.getAciModelConfig());
        JSONArray codeGpt = JSONArray.parseArray(aciModelConfig.getString("CodeGpt"));
        JSONArray mayaStream = JSONArray.parseArray(aciModelConfig.getString("MayaStream"));
        codeGpt.addAll(mayaStream);
        boolean contains = false;
        for (Object drmModel : codeGpt) {
            contains = baseModel.contains(drmModel + "");
            if (contains) {
                break;
            }
        }
        if (!contains) {
            // 不支持的底座模型直接取消该组件执行
            response.setStatus(AntCIComponentStatus.CANCELED);
            outputs.put("content", "baseModel不正确");
            return new MutablePair<>(false, response);
        }
        if (!"pre".equals(environment) && !"prod".equals(environment)) {
            response.setStatus(AntCIComponentStatus.FAILED);
            outputs.put("content", "环境参数不正确,需要传pre或者prod");
            return new MutablePair<>(false, response);
        }
        Long userId = codeFuseUserAuthService.empId2UserId(empId);
        if (userId == null) {
            codeFuseUserAuthService.insertUserAuth(Lists.newArrayList(empId));
            userId = codeFuseUserAuthService.empId2UserId(empId);
            if (userId == null) {
                response.setStatus(AntCIComponentStatus.FAILED);
                outputs.put("content", "工号异常:" + empId + ",无法操作,请联系管理员确认");
                return new MutablePair<>(false, response);
            }
        }
        return new MutablePair<>(true, null);
    }


}

