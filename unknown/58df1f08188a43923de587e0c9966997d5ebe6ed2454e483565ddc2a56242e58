package com.alipay.codegencore.service.middle.drm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.enums.FileAnnotationTypeEnum;
import com.alipay.codegencore.model.enums.LackParamStrategyEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.drm.client.api.annotation.DAttribute;
import com.alipay.drm.client.api.annotation.DResource;
import com.alipay.sofa.specs.annotation.drm.DrmAttributeSpec;
import com.alipay.sofa.specs.annotation.drm.DrmResourceSpec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * codeGPT相关的DRM配置
 * <AUTHOR>
 * 创建时间 2023-03-13
 */
@Component
@DResource(id = "com.alipay.codegencore.codegptdrmconfig")
@DrmResourceSpec(name = "CodeGPT相关的DRM配置")
public class CodeGPTDrmConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(CodeGPTDrmConfig.class);

    @DAttribute
    @DrmAttributeSpec(name = "意图识别的接口地址")
    private String intentionRecognitionServiceUrl = "https://paiplusinference.alipay.com/inference/fafeaf2466ab52c1_code_intent/v1";

    @DAttribute
    @DrmAttributeSpec(name = "不需要限制流量的模型")
    private String noNeedRateLimitModels = "[\"ANTGLM-10B-RLHF\"]";

    private List<String> noNeedRateLimitModelList = JSON.parseArray(noNeedRateLimitModels,String.class);

    @DAttribute
    @DrmAttributeSpec(name = "antnluservice地址")
    private String antnluServiceUrl = "https://antnluservice-pre.alipay.com";

    @DAttribute
    @DrmAttributeSpec(name = "是否默认注册用户")
    private boolean defaultCreateUser = true;

    @DAttribute
    @DrmAttributeSpec(name = "是否默认审批通过")
    private boolean defaultApproval = true;

    @DAttribute
    @DrmAttributeSpec(name = "默认余额,单位美元")
    private Integer defaultBalance = 5;

    @DAttribute
    @DrmAttributeSpec(name = "异步审查时问题的等待时间,单位毫秒")
    private Integer asyCheckMaxWaitTimeQuestion = 200;

    @DAttribute
    @DrmAttributeSpec(name = "异步审查时答案的等待时间,单位毫秒")
    private Integer asyCheckMaxWaitTimeAnswer = 200;

    @DAttribute
    @DrmAttributeSpec(name = "等待异步审查结果的轮训间隔时间,单位毫秒")
    private Integer loopWaitTime = 50;

    @DAttribute
    @DrmAttributeSpec(name = "分段送检时两段的重叠长度")
    private Integer overlapLength = 10;

    @DAttribute
    @DrmAttributeSpec(name = "codeFuse安全审查开关")
    private String codeFuseCheckSwitch = "{\"CHATGPT\":{\"INFOSEC\":false,\"KEYMAP\":false,\"ANTDSR\":false},\"NEOX\":{\"INFOSEC\":false,\"KEYMAP\":false,\"ANTDSR\":false}}";

    @DAttribute
    @DrmAttributeSpec(name = "允许用户自己关闭codeFuse审核提示开关")
    private String userCodeFuseCheckSwitch = "OFF";

    @DAttribute
    @DrmAttributeSpec(name = "codeFuse安全审查失败的信息")
    private String codeFuseCheckFailedMsg = "{\n" +
            "    \"USER\": {\n" +
            "        \"INFOSEC\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\",\n" +
            "        \"KEYMAP\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\",\n" +
            "        \"ANTDSR\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\",\n" +
            "        \"RCSMART\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\",\n" +
            "        \"DEFAULT\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\"\n" +
            "    },\n" +
            "    \"ASSISTANT\": {\n" +
            "        \"INFOSEC\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\",\n" +
            "        \"KEYMAP\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\",\n" +
            "        \"ANTDSR\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\",\n" +
            "        \"RCSMART\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\",\n" +
            "        \"DEFAULT\":\"我擅长代码相关问题，您可以尝试发送写代码、加注释、优化代码等技术问题，比如：写一个快速排序代码\"\n" +
            "    }\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "送审数据的最大size")
    private Integer checkDataMaxSize = 200;

    @DAttribute
    @DrmAttributeSpec(name = "含标点符号送审数据的最大size")
    private Integer checkDataSizeWithPunctuation = 50;

    @DAttribute
    @DrmAttributeSpec(name = "最大在线用户数量")
    private Integer maxOnlineUserNum = 1000;

    @DAttribute
    @DrmAttributeSpec(name = "Qpx限流器的配置")
    private String qpxRateLimiterConfig = "{\n" +
            "    \"ALGO_MODEL_CODEGPT-13B-ANT-SFT\": {\n" +
            "        \"windowTimeMills\": 20000,\n" +
            "        \"windowTotalQuota\": 120\n" +
            "    },\n" +
            "    \"ALGO_MODEL_CODEGPT-13B-SFT-PLUS\": {\n" +
            "        \"windowTimeMills\": 20000,\n" +
            "        \"windowTotalQuota\": 60\n" +
            "    },\n" +
            "    \"ALGO_MODEL_CHATGPT-3.5-TURBO\": {\n" +
            "        \"windowTimeMills\": 60000,\n" +
            "        \"windowTotalQuota\": 9000\n" +
            "    },\n" +
            "    \"ALGO_MODEL_DEFAULT\": {\n" +
            "        \"windowTimeMills\": 20000,\n" +
            "        \"windowTotalQuota\": 5\n" +
            "    },\n" +
            "    \"TOKEN_USER_codegpt\": {\n" +
            "        \"windowTimeMills\": 60000,\n" +
            "        \"windowTotalQuota\": 100\n" +
            "    },\n" +
            "    \"TOKEN_USER_TechPlay\": {\n" +
            "        \"windowTimeMills\": 60000,\n" +
            "        \"windowTotalQuota\": 5\n" +
            "    },\n" +
            "    \"TOKEN_USER_work_chat\": {\n" +
            "        \"windowTimeMills\": 60000,\n" +
            "        \"windowTotalQuota\": 5\n" +
            "    },\n" +
            "    \"TOKEN_USER_codefuse_plugin\": {\n" +
            "        \"windowTimeMills\": 60000,\n" +
            "        \"windowTotalQuota\": 20\n" +
            "    },\n" +
            "    \"TOKEN_USER_DEFAULT\": {\n" +
            "        \"windowTimeMills\": 60000,\n" +
            "        \"windowTotalQuota\": 60\n" +
            "    },\n" +
            "    \"USER_HTTP\": {\n" +
            "        \"windowTimeMills\": 60000,\n" +
            "        \"windowTotalQuota\": 100\n" +
            "    },\n" +
            "    \"USER_CONVERSATION\": {\n" +
            "        \"windowTimeMills\": 60000,\n" +
            "        \"windowTotalQuota\": 5\n" +
            "    }\n" +
            "}";

    private JSONObject qpxRateLimiterConfigJson = JSONObject.parseObject(qpxRateLimiterConfig);

    @DAttribute
    @DrmAttributeSpec(name = "Qpx的根据TokenUser限流允许使用默认池的TokenUser")
    private String qpxRateLimiterAllowUseDefaultTokenUser = "[\"TechPlay\",\"codegpt\",\"work_chat\",\"codefuse_plugin\"]";

    private List<String> qpxRateLimiterAllowUseDefaultTokenUserList = JSON.parseArray(qpxRateLimiterAllowUseDefaultTokenUser,String.class);

    @DAttribute
    @DrmAttributeSpec(name = "获取令牌桶间隔时间")
    private Integer rateLimiterSleepMills = 100;

    @DAttribute
    @DrmAttributeSpec(name = "获取令牌桶最大等待时间")
    private Integer rateLimiterMaxWaitMills = 3 * 1000;

    @DAttribute
    @DrmAttributeSpec(name = "请求keymap的超时时间,单位毫秒")
    private Integer keymapTimeOut = 200;

    @DAttribute
    @DrmAttributeSpec(name = "数据安全平台开关")
    private String keymapSwitch = "ON";

    @DAttribute
    @DrmAttributeSpec(name = "数据安全平台antdsr开关")
    private String antDsrSwitch = "ON";

    @DAttribute
    @DrmAttributeSpec(name = "数据安全平台的URL")
    private String keymapUrl = "https://keymap.alipay.com";

    @DAttribute
    @DrmAttributeSpec(name = "内容安全平台开关")
    private String infosecSwitch = "ON";

    @DAttribute
    @DrmAttributeSpec(name = "内容安全需要审核的user相关配置")
    private String infoSecNeedCheckUser = "[\n" +
            "    {\n" +
            "        \"codeGPTUser\": \"codegpt\",\n" +
            "        \"questionSceneCode\": \"cto_codefuse_question\",\n" +
            "        \"answerSceneCode\": \"cto_codefuse_answer\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"codeGPTUser\": \"codefuse_plugin\",\n" +
            "        \"questionSceneCode\": \"cto_codefuse_question\",\n" +
            "        \"answerSceneCode\": \"cto_codefuse_answer\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"codeGPTUser\": \"TechPlay\",\n" +
            "        \"questionSceneCode\": \"cto_techplay_question\",\n" +
            "        \"answerSceneCode\": \"cto_techplay_answer\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"codeGPTUser\": \"work_chat\",\n" +
            "        \"questionSceneCode\": \"cto_techplay_question\",\n" +
            "        \"answerSceneCode\": \"cto_techplay_answer\"\n" +
            "    }\n" +
            "]";

    @DAttribute
    @DrmAttributeSpec(name = "CodeGPT_neox输出最大长度")
    private Integer neoxOutputMaxLength = 512;

    @DAttribute
    @DrmAttributeSpec(name = "CodeGPT_neox请求地址")
    private String neoxRequestUrl = "http://100.67.119.122/deveffinlp/codegpt_neox/codegpt_neox";

    @DAttribute
    @DrmAttributeSpec(name = "codeGPT默认的提问模板列表")
    private String codeGPTQueryTemplateList = "";

    @DAttribute
    @DrmAttributeSpec(name = "模拟参考链接的数据")
    private String mockReferenceDataList = "[\n" +
            "    {\n" +
            "        \"text\": \"缓存失败\",\n" +
            "        \"url\": \"https://me.antfin.com/my/report\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"text\": \"数据库过载\",\n" +
            "        \"url\": \"https://me.antfin.com/my/report\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"text\": \"多级缓存\",\n" +
            "        \"url\": \"https://me.antfin.com/my/report\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"text\": \"缓存雪崩\",\n" +
            "        \"url\": \"https://me.antfin.com/my/report\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"text\": \"缓存击穿\",\n" +
            "        \"url\": \"https://me.antfin.com/my/report\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"text\": \"缓存穿透\",\n" +
            "        \"url\": \"https://me.antfin.com/my/report\"\n" +
            "    }\n" +
            "]";

    @DAttribute
    @DrmAttributeSpec(name = "fass工具信息")
    private String faasToolInfo = "{\n" +
            "    \"createChart\": {\n" +
            "        \"env\": \"DEV\",\n" +
            "        \"functionName\": \"myjf.common.codefusejavafaas.demo.coedfuse.pluginapidrawdiagram\"\n" +
            "    },\n" +
            "    \"googleSearch\": {\n" +
            "        \"env\": \"DEV\",\n" +
            "        \"functionName\": \"myjf.common.codefusejavafaas.demo.coedfuse.pluginapiquerygoogle\"\n" +
            "    },\n" +
            "    \"getFutureTemperature\": {\n" +
            "        \"env\": \"PRE\",\n" +
            "        \"functionName\": \"myjf.common.codefusejavafaas.demo.coedfuse.pluginapiqueryweather\"\n" +
            "    }\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "与ai模型对话标题的system值")
    private String codeGPTSystemValue = "Find out the key words according to the content of our conversation and" +
            " give a topic. It requires 1-8 words, and no punctuation mark in the topic, Reply in Chinese";


    @DAttribute
    @DrmAttributeSpec(name = "解语花安全平台参数配置")
    private String rcsmartConfig = "{\n" +
            "    \"switch\": \"ON\",\n" +
            "    \"appName\": \"codeGPT\",\n" +
            "    \"token\": \"codeGPT\",\n" +
            "    \"sceneCode\": \"SC_REALTIME_TEST\",\n" +
            "    \"bizId\": \"BIZ_RCSMART_TEST\"\n" +
            "}";
    @DAttribute
    @DrmAttributeSpec(name = "各个审核平台的相关长度限制")
    private String checkLengthConfig = "{\n" +
            "    \"INFOSEC\": {\n" +
            "        \"maxLength\": 10000,\n" +
            "        \"hasPunctuationLength\": 8000,\n" +
            "        \"overlapLength\": 10\n" +
            "    }\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "页面最长显示的会话列表长度")
    private Integer sessionListMaxSize = 100;

    @DAttribute
    @DrmAttributeSpec(name = "是否是内网应用")
    private boolean intranetApplication = true;

    @DAttribute
    @DrmAttributeSpec(name = "GPTCache的总开关")
    private int gptCacheEnable = 0;

    @DAttribute
    @DrmAttributeSpec(name = "GPTCache的流量开启比例因子，最小比例千分之一")
    private int gptCacheFactor = 1000;

    @DAttribute
    @DrmAttributeSpec(name = "生成对话标题使用的模型")
    private String generateTitleUseModel = "codegpt";

    @DAttribute
    @DrmAttributeSpec(name = "不阻塞AI回复的审核平台")
    private String noBlockingCheckPlatform = "[\"INTENTION\",\"RCSMART\"]";


    @DAttribute
    @DrmAttributeSpec(name = "用户登录通知插件的地址")
    private String userLoginNotifyPluginURL = "https://tsingyan-pre.antgroup.com/v1/user/saas/login/callback";

    @DAttribute
    @DrmAttributeSpec(name = "用户登录通知内网插件的地址")
    private String userLoginNotifyInnerPluginURL = "https://caselike-pre.alipay.com/v1/user/saas/login/callback";

    /**
     * 格式为
     * {
     *     "TechPlay": {
     *         "appName": "codeFuse",
     *         "appToken": "2cxxxxxxxxf9"
     *     },
     *     "work_chat": {
     *         "appName": "codeFuse",
     *         "appToken": "2cxxxxxxxxf9"
     *     },
     *     "codegpt": {
     *         "appName": "codeFuse",
     *         "appToken": "2cxxxxxxxxf9"
     *     },
     *     "antchat": {
     *         "appName": "codeFuse",
     *         "appToken": "2cxxxxxxxxf9"
     *     },
     *     "others": {
     *         "appName": "codeFuse_others",
     *         "appToken": "2cxxxxxxxxf9"
     *     }
     * }
     */
    @DAttribute
    @DrmAttributeSpec(name = "请求antglm使用的appName和appToken")
    private String antGLMAuthConfigStr = "{}";

    @DAttribute
    @DrmAttributeSpec(name = "InfoSec异步处理时间")
    private String infoSecAsyncTime = "{\"loopWaitTime\":1000,\"maxWaitTime\":60000}";

    @DAttribute
    @DrmAttributeSpec(name = "插件的流式消息轮询间隔，单位是ms")
    private Integer pluginStreamDataPollingStep = 50;

    @DAttribute
    @DrmAttributeSpec(name = "插件的流式数据的默认最大间隔，单位是ms")
    private Integer pluginCommonStreamDataWaitTime = 10000;

    @DAttribute
    @DrmAttributeSpec(name = "插件的大模型调用阶段，两个流式数据的最大时间间隔，单位是ms")
    private Integer pluginLLMStageCommonStreamDataWaitTime = 5000;

    @DAttribute
    @DrmAttributeSpec(name = "插件的大模型调用阶段，从开始调用到第一个流式数据的最大时间间隔，单位是ms")
    private Integer pluginLLMStageFirstStreamDataWaitTime = 20000;

    @DAttribute
    @DrmAttributeSpec(name = "插件调用接口的默认超时时间")
    private Integer pluginRequestDefaultTimeOut = 20000;

    @DAttribute
    @DrmAttributeSpec(name = "function call流程的默认超时时间")
    private Integer functionCallDefaultTimeOut = 45000;

    @DAttribute
    @DrmAttributeSpec(name = "function搜索的url")
    private String functionSearchUrl = "https://codegencore-pre.alipay.com/api/test/searchFunction";

    @DAttribute
    @DrmAttributeSpec(name = "插件的大模型调用阶段默认的prompt模板")
    private String pluginLLMStageDefaultPromptTemplate = "这是调用相关接口得到的信息:\n {preResponse}\n\n请根据这些信息回答问题: {query}";

    @DAttribute
    @DrmAttributeSpec(name = "默认助手id")
    private Long sceneDefaultId = 5L;

    @DAttribute
    @DrmAttributeSpec(name = "预发环境允许使用CommonPower接口的TokenUser")
    private String preNeedUseCommonPowerTokenUser = "[\"codegpt\"]";

    @DAttribute
    @DrmAttributeSpec(name = "指定助手中心某个助手排序")
    private String sceneAppointSort = "[{\"sceneId\":5,\"index\":1},{\"sceneId\":6,\"index\":1}]";

    @DAttribute
    @DrmAttributeSpec(name = "chatGPT设置定时扣费时间,单位毫秒")
    private long chatGptTimingFeeTime = 10000;

    @DAttribute
    @DrmAttributeSpec(name = "默认执行function call的模型")
    private String defaultFunctionCallModel = "TOOL-GPT";

    @DAttribute
    @DrmAttributeSpec(name = "function call是否开启多轮支持")
    private boolean functionCallMultiRoundEnabled = false;

    @DAttribute
    @DrmAttributeSpec(name = "参数不足时的策略")
    private String lackParamStrategy = LackParamStrategyEnum.AI_QUERY.name();

    @DAttribute
    @DrmAttributeSpec(name = "执行function call的最大步数")
    private Integer maxFunctionCallRound = 5;

    @DAttribute
    @DrmAttributeSpec(name = "文档对话相关的配置")
    private String documentChatConfig = "{\n" +
            "    \"fileSaveTbaseMaxSeconds\":3600,\n" +
            "    \"documentChatSceneId\":1300001,\n" +
            "    \"supportFileSuffixes\": [\n" +
            "        \"pdf\",\n" +
            "        \"docx\",\n" +
            "        \"txt\"\n" +
            "    ],\n" +
            "    \"fileMaxSize\":10485760,\n" +
            "    \"everyPartMaxSize\":1000,\n" +
            "    \"fileSummaryMethod\":\"frontPart\",\n" +
            "    \"fileSummarySize\":1500,\n" +
            "    \"bindMaxSize\":100,\n" +
            "    \"docListMaxSize\":2500,\n" +
            "    \"maxSumPartSize\":100,\n" +
            "    \"topNSimilarity\":3,\n" +
            "    \"maxCallTimes\":10,\n" +
            "    \"minSimilarity\":0.85,\n" +
            "    \"embeddingService\":\"zark\",\n" +
            "    \"embeddingModel\":\"m3e-base\",\n" +
            "    \"summaryModel\":\"codegpt\",\n" +
            "    \"summaryContentTitle\":\"文档解析完成,以下为此文档摘要内容\n\n\",\n" +
            "    \"summaryFileQuestionPrompt\":\"下面我将给一个较长的文本内容，你需要用中文帮我总结一下这个文本内容的核心梗概，如果你的结果比较长的话尽量精简到100字左右，且尽可能完善。必须以总分的格式，先一句话总结一下，然后分3到5个点来完善细节。\",\n" +
            "    \"promptFormat\":\"查询结果如下:\nsearchResult\n说明: 使用提供的搜索结果，对给定的问题写一个全面的回复。\n确保在引用上下文之后使用 〔number〕 符号引用结果，将代码部分放在```code```中。使用语言 zh-CN 回答。\n问题:question\"\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "oss相关的配置")
    private String ossConfig = "{\n" +
            "    \"bucketName\": \"antsys-codegencore-dev\",\n" +
            "    \"endpoint\": \"cn-hangzhou-alipay-b-internal.oss-internal.aliyun-inc.com\"\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "ACI组件相关配置")
    private String aciComponentConfig = "{\n" +
            "    \"operateModelDocumentUrl\": \"https://yuque.antfin-inc.com/iikq97/lozx5s/egg6oyggqlg88v6a?singleDoc#\",\n" +
            "    \"sceneUrl\": \"https://codegpt-inc.alipay.com/main/home?sceneId=\",\n" +
            "    \"sceneDesc\":\"这是由ACI流水线自动创建的一个助手\"\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "zark相关配置")
    private String zarkConfig = "{\n" +
            "    \"zarkUrl\": \"https://zarkag.antgroup.com\",\n" +
            "    \"embeddingInterface\": \"/deveffinlp/codefuse_vector_service/codefuse_vector_service\"\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "不需要做环境可用的模型")
    private String ignoreEnvCheckModels = "[\"CHATGPT\"]";

    @DAttribute
    @DrmAttributeSpec(name = "长链接转短链临界值")
    private Integer linkLengthConfig = 50;

    @DAttribute
    @DrmAttributeSpec(name = "页面需授权使用的业务接口列表")
    private String needCheckBizApi = "[]";

    @DAttribute
    @DrmAttributeSpec(name = "消息存储格式版本号")
    private String chatMessageFormatVersion = "2.0";

    @DAttribute
    @DrmAttributeSpec(name = "搜索插件发送消息记录数量")
    private Integer messageNum = 8;

    @DAttribute
    @DrmAttributeSpec(name = "faas转发接口需要鉴权的函数名")
    private String needCheckAuthFaasList = "[\"myjf.common.codefusejavafaas.service.tools.codeinterpreter\"]";

    @DAttribute
    @DrmAttributeSpec(name = "对话级表单默认的uiSchema")
    private String messageFormDefaultUiSchema = "{\n" +
            "    \"ui:title\":\"\",\n" +
            "    \"ui:submitButtonOptions\":{\n" +
            "        \"norender\":true\n" +
            "    }\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "模型保活黑名单")
    private String modelKeepLivingBlackList = "";

    @DAttribute
    @DrmAttributeSpec(name = "模型健康度配置信息")
    private String healthDegreeCheckConfig = "{\n" +
            "    \"fullHealth\":100,\n" +
            "    \"costHealth\":1,\n" +
            "    \"healthThreshold\":90,\n" +
            "    \"keepModelMinIntervals\":300\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "平台问题默认通知人")
    private String notifyMembersDefault = "[\"075279\"]";

    @DAttribute
    @DrmAttributeSpec(name = "模型handler配置")
    private String modelHandler = "{\"user\":[\"CodeGPTModelHandler\",\"MayaStreamModelHandler\"],\"admin\":[\"ChatGptModelHandler\","
            + "\"CodeGPTModelHandler\",\"AntGLMModelHandler\",\"MayaStreamModelHandler\",\"ChatGptModelHubHandler\"]}";
    @DAttribute
    @DrmAttributeSpec(name = "模型impl默认配置")
    private String defaultModelImplConfig = "{\"CodeFuse\":\"[{\\\"key\\\":\\\"requestTimeOut\\\",\\\"value\\\":40000,"
            + "\\\"desc\\\":\\\"请求maya服务的整体超时时间\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,"
            + "\\\"max\\\":999999999},{\\\"key\\\":\\\"topK\\\",\\\"value\\\":40,"
            + "\\\"desc\\\":\\\"较小的topK值会使生成的文本更准确，较大的topK值则会增加生成文本的多样性。\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\","
            + "\\\"min\\\":0,\\\"max\\\":999999999},{\\\"key\\\":\\\"topP\\\",\\\"value\\\":\\\"0.95\\\","
            + "\\\"desc\\\":\\\"概率值，0-1。较小的topP值会使生成的文本更准确但可能较短，较大的topP值则会增加生成文本的长度和多样性。   \\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},{\\\"key\\\":\\\"temperature\\\",\\\"value\\\":\\\"0"
            + ".2\\\",\\\"desc\\\":\\\"温度参数。较小的温度值（如0.1）会使得模型更加确定性地生成高概率的单词，而较大的温度值（如1"
            + ".0或更高）则会增加生成文本的多样性，使得低概率的单词也有机会被选择。较高的温度值会使得生成文本更加随机，但也可能导致生成低质量的文本。\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":1},{\\\"key\\\":\\\"batchTimeWindow\\\",\\\"value\\\":3000,"
            + "\\\"desc\\\":\\\"批次的窗口时间，单位毫秒。\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,"
            + "\\\"max\\\":999999999},{\\\"key\\\":\\\"batchSize\\\",\\\"value\\\":3,"
            + "\\\"desc\\\":\\\"批次的最大请求数，codefuse会把每batchTimeWindow毫秒内积攒的最长batchSize个请求同时发给模型的一个节点(pod)"
            + "来推理，可增强模型的吞吐量，是一个用时间换吞吐量的方式。如果不需要这个功能batchSize设置为1即可。\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\","
            + "\\\"min\\\":1,\\\"max\\\":999999999},{\\\"key\\\":\\\"commonStreamDataWaitTime\\\",\\\"value\\\":40000,"
            + "\\\"desc\\\":\\\"间隔时间：codefuse等待算法服务写入第2-N个流(数据包)到tbase的时间和上一个流(数据包)写入成功的最长间隔时间，即：第一个流获取到后，等待第二个流的最长时间；第二个获取到后，等待第三个的最长时间；"
            + "...第N-1个流获取到之后，等待第N个流的最长等待时间。\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,"
            + "\\\"max\\\":999999999},{\\\"key\\\":\\\"repetitionPenalty\\\",\\\"value\\\":\\\"1.0\\\",\\\"desc\\\":\\\"惩罚参数\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},"
            + "{\\\"key\\\":\\\"outSeqLength\\\",\\\"value\\\":2000,\\\"desc\\\":\\\"模型输出数据的最长token数\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,\\\"max\\\":999999999},{\\\"key\\\":\\\"firstStreamDataWaitTime\\\","
            + "\\\"value\\\":40000,\\\"desc\\\":\\\"首包超时时间：codefuse把请求发给maya之后，算法服务会流式的向tbase写入结果，这个时间是codefuse等待算法服务写入第一个流到tbase"
            + "中的最长等待时间。\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,\\\"max\\\":999999999},"
            + "{\\\"key\\\":\\\"systemPrompt\\\",\\\"value\\\":\\\"\\\",\\\"desc\\\":\\\"模型级别默认的systemPrompt\\\",\\\"required\\\":false,"
            + "\\\"type\\\":\\\"textArea\\\"},{\\\"key\\\":\\\"sceneName\\\",\\\"value\\\":\\\"\\\",\\\"desc\\\":\\\"maya部署的模型信息\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"input\\\"},{\\\"key\\\":\\\"chainName\\\",\\\"value\\\":\\\"\\\","
            + "\\\"desc\\\":\\\"maya部署的模型信息\\\",\\\"required\\\":true,\\\"type\\\":\\\"input\\\"},{\\\"key\\\":\\\"requestModel\\\","
            + "\\\"value\\\":\\\"MAYA\\\",\\\"desc\\\":\\\"固定值：MAYA\\\",\\\"required\\\":true,\\\"type\\\":\\\"input\\\"},"
            + "{\\\"key\\\":\\\"streamDataPollingStep\\\",\\\"value\\\":50,"
            + "\\\"desc\\\":\\\"读取结果的步长。当codefuse从tbase到读不到结果的时候，等待streamDataPollingStep毫秒后再尝试读\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},{\\\"key\\\":\\\"bufferMinLength\\\","
            + "\\\"value\\\":5,\\\"desc\\\":\\\"每次最短输出长度\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,"
            + "\\\"max\\\":999999999},{\\\"key\\\":\\\"bufferLengthGrowthStep\\\",\\\"value\\\":5,\\\"desc\\\":\\\"每次输出步长增加长度\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,\\\"max\\\":999999999},"
            + "{\\\"key\\\":\\\"bufferMaxLength\\\",\\\"value\\\":20,\\\"desc\\\":\\\"每次最长输出长度\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,\\\"max\\\":999999999},{\\\"key\\\":\\\"maxRound\\\","
            + "\\\"value\\\":\\\"8\\\",\\\"desc\\\":\\\"上文最大消息数\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,"
            + "\\\"max\\\":999999999},{\\\"key\\\":\\\"maxToken\\\",\\\"value\\\":\\\"1280\\\",\\\"desc\\\":\\\"上文最大token数\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,\\\"max\\\":999999999}]\","
            + "\"AntGLM\":\"[{\\\"key\\\":\\\"requestTimeOut\\\",\\\"value\\\":40000,\\\"desc\\\":\\\"请求maya服务的整体超时时间\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},{\\\"key\\\":\\\"topK\\\","
            + "\\\"value\\\":40,\\\"desc\\\":\\\"较小的topK值会使生成的文本更准确，较大的topK值则会增加生成文本的多样性。\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},{\\\"key\\\":\\\"topP\\\",\\\"value\\\":\\\"0"
            + ".95\\\",\\\"desc\\\":\\\"概率值，0-1。较小的topP值会使生成的文本更准确但可能较短，较大的topP值则会增加生成文本的长度和多样性。   \\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},{\\\"key\\\":\\\"temperature\\\",\\\"value\\\":\\\"0"
            + ".2\\\",\\\"desc\\\":\\\"温度参数。较小的温度值（如0.1）会使得模型更加确定性地生成高概率的单词，而较大的温度值（如1"
            + ".0或更高）则会增加生成文本的多样性，使得低概率的单词也有机会被选择。较高的温度值会使得生成文本更加随机，但也可能导致生成低质量的文本。\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":1},{\\\"key\\\":\\\"commonStreamDataWaitTime\\\","
            + "\\\"value\\\":40000,\\\"desc\\\":\\\"间隔时间：codefuse等待算法服务写入第2-N个流(数据包)到tbase的时间和上一个流(数据包)"
            + "写入成功的最长间隔时间，即：第一个流获取到后，等待第二个流的最长时间；第二个获取到后，等待第三个的最长时间；...第N-1个流获取到之后，等待第N个流的最长等待时间。\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,\\\"max\\\":999999999},{\\\"key\\\":\\\"outSeqLength\\\","
            + "\\\"value\\\":2000,\\\"desc\\\":\\\"模型输出数据的最长token数\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\","
            + "\\\"min\\\":1,\\\"max\\\":999999999},{\\\"key\\\":\\\"firstStreamDataWaitTime\\\",\\\"value\\\":40000,"
            + "\\\"desc\\\":\\\"首包超时时间：codefuse把请求发给maya之后，算法服务会流式的向tbase写入结果，这个时间是codefuse等待算法服务写入第一个流到tbase中的最长等待时间。\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,\\\"max\\\":999999999},"
            + "{\\\"key\\\":\\\"systemPrompt\\\",\\\"value\\\":\\\"\\\",\\\"desc\\\":\\\"模型级别默认的systemPrompt\\\",\\\"required\\\":false,"
            + "\\\"type\\\":\\\"textArea\\\"},{\\\"key\\\":\\\"sceneName\\\",\\\"value\\\":\\\"\\\",\\\"desc\\\":\\\"maya部署的模型信息\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"input\\\"},{\\\"key\\\":\\\"chainName\\\",\\\"value\\\":\\\"\\\","
            + "\\\"desc\\\":\\\"maya部署的模型信息\\\",\\\"required\\\":true,\\\"type\\\":\\\"input\\\"},"
            + "{\\\"key\\\":\\\"streamDataPollingStep\\\",\\\"value\\\":50,"
            + "\\\"desc\\\":\\\"读取结果的步长。当codefuse从tbase到读不到结果的时候，等待streamDataPollingStep毫秒后再尝试读\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},{\\\"key\\\":\\\"bufferMinLength\\\","
            + "\\\"value\\\":5,\\\"desc\\\":\\\"每次最短输出长度\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,"
            + "\\\"max\\\":999999999},{\\\"key\\\":\\\"bufferLengthGrowthStep\\\",\\\"value\\\":5,\\\"desc\\\":\\\"每次输出步长增加长度\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},"
            + "{\\\"key\\\":\\\"bufferMaxLength\\\",\\\"value\\\":20,\\\"desc\\\":\\\"每次最长输出长度\\\",\\\"required\\\":true,"
            + "\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":0,\\\"max\\\":999999999},{\\\"key\\\":\\\"mayaDataVersion\\\","
            + "\\\"value\\\":2,\\\"desc\\\":\\\"数据格式类型，一般就是固定值2\\\",\\\"required\\\":true,\\\"type\\\":\\\"input\\\"},"
            + "{\\\"key\\\":\\\"requestMayaDataKey\\\",\\\"value\\\":\\\"data\\\",\\\"desc\\\":\\\"发送请求给maya的map的key\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"input\\\"},{\\\"key\\\":\\\"responseMayaDataKey\\\",\\\"value\\\":\\\"res\\\","
            + "\\\"desc\\\":\\\"maya的响应的key\\\",\\\"required\\\":true,\\\"type\\\":\\\"input\\\"},{\\\"key\\\":\\\"maxRound\\\","
            + "\\\"value\\\":\\\"1\\\",\\\"desc\\\":\\\"上文最大消息数\\\",\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,"
            + "\\\"max\\\":999999999},{\\\"key\\\":\\\"maxToken\\\",\\\"value\\\":\\\"1024\\\",\\\"desc\\\":\\\"上文最大token数\\\","
            + "\\\"required\\\":true,\\\"type\\\":\\\"inputNumber\\\",\\\"min\\\":1,\\\"max\\\":999999999}]\"}";



    @DAttribute
    @DrmAttributeSpec(name = "基座模型的助手id")
    private String sceneId = "{\"CODEFUSE_CODEGPT_13B_HF_SFT_ANT\":\"10100004\",\"CODEFUSE_CODEGPT_13B_HF_SFT\":\"10100003\","
            + "\"ANTGLM_10B_RLHF_DETOXICITY\":\"9000001\",\"ANTGLM_10B_SFT\":\"9000002\",\"DEPLOY_STATCODER\":\"8600001\","
            + "\"QWEN_7B_HF\":\"8600003\",\"QWEN_14B_HF\":\"8600002\",\"LLAMA_13B_HF\":\"9300004\",\"BAICHUAN_13B_BASE_HF\":\"9300005\","
            + "\"CODELLAMA_7B_HF\":\"9000003\",\"CODELLAMA_34B_HF\":\"9600005\",\"CHATGLM2_6B_HF\":\"8600004\"}";


    @DAttribute
    @DrmAttributeSpec(name = "助手和插件的默认头像列表")
    private String sceneAndPluginAvatarList = "{\n" +
            "    \"scene\": [\n" +
            "        {\n" +
            "            \"sort\":2,\n" +
            "            \"ossUrl\":\"https://antsys-codegencore-dev.cn-hangzhou-alipay-b.oss-cdn.aliyun-inc.com/FORM_UPLOAD_FILE/gOWseIWfRma1N7u6t4nq2w%E7%BC%96%E7%BB%84%2014.svg?Expires=4102329600&OSSAccessKeyId=LTAI5tM2HG73QGR1tc5oSCSQ&Signature=iB6rV06FeUz3QcFeLTgOY0jrrZg%3D\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"sort\":1,\n" +
            "            \"ossUrl\":\"https://antsys-codegencore-dev.cn-hangzhou-alipay-b.oss-cdn.aliyun-inc.com/FORM_UPLOAD_FILE/PabplKI.T4.9CPaQSlDvbg%E7%BC%96%E7%BB%84%2010.svg?Expires=4102329600&OSSAccessKeyId=LTAI5tM2HG73QGR1tc5oSCSQ&Signature=cQ4SrX%2Foy74pGDrwi6zH8BjYlzM%3D\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"plugin\": [\n" +
            "        {\n" +
            "            \"sort\": 1,\n" +
            "            \"ossUrl\": \"https://antsys-codegencore-dev.cn-hangzhou-alipay-b.oss-cdn.aliyun-inc.com/FORM_UPLOAD_FILE/sm2yBaXPQzesqQk52dXB4g%E6%8F%92%E4%BB%B61.svg?Expires=4102329600&OSSAccessKeyId=LTAI5tM2HG73QGR1tc5oSCSQ&Signature=PZ%2BqAcrt7Qi7fwJTrR7kI3z8sP4%3D\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";

    @DAttribute
    @DrmAttributeSpec(name = "文件注解类型")
    private String fileAnnotationType = FileAnnotationTypeEnum.LINK.name();

    @DAttribute
    @DrmAttributeSpec(name = "是否把文档搜索结果加到prompt中")
    private boolean docSearchResultAddToPrompt = true;
    /**
     * zSearch任务重试次数
     */
    @DAttribute
    @DrmAttributeSpec(name = "zsearch任务重试次数")
    private Integer zsearchTaskRetryTimes = 3;

    @DAttribute
    @DrmAttributeSpec(name = "zsearch任务超时时间")
    private Integer zsearchTimeout = 20000;

    @DAttribute
    @DrmAttributeSpec(name = "aci模型模型相关配置")
    private String aciModelConfig = "{\"CodeGpt\":[\"codegpt\",\"qwen\",\"codellama\",\"llama\",\"chatglm\",\"starcoder\",\"baichuan\","
            + "\"codefuse2\"],\"MayaStream\":[\"antglm\",\"opsgpt\"],\"CodeGPTModelHandler\":\"{\\\"requestModel\\\":\\\"MAYA\\\","
            + "\\\"repetitionPenalty\\\":\\\"1.1\\\",\\\"batchTimeWindow\\\":\\\"3000\\\",\\\"batchSize\\\":\\\"1\\\"}\","
            + "\"MayaStreamModelHandler\":\"{\\\"mayaDataVersion\\\":\\\"2\\\",\\\"requestMayaDataKey\\\":\\\"data\\\","
            + "\\\"responseMayaDataKey\\\":\\\"res\\\"}\"}";

    @DAttribute
    @DrmAttributeSpec(name = "openapi调用数据日志开关")
    private String openApiLogSwitch = "close";

    @DAttribute
    @DrmAttributeSpec(name = "自动巡检配置pageIdList")
    private String pageIdList = "[28400217,\n" +
            "            28400216,\n" +
            "            28400215,\n" +
            "            28400213,\n" +
            "            28400211,\n" +
            "            28400210,\n" +
            "            28400205,\n" +
            "            28400206,\n" +
            "            28400203,\n" +
            "            28400227,\n" +
            "            28400226,\n" +
            "            28400225,\n" +
            "            28400219]"
            ;

    @DAttribute
    @DrmAttributeSpec(name = "自动巡检开启")
    private String inspectionSwitch = "open";
    @DAttribute
    @DrmAttributeSpec(name = "AgentSecSdk超时时间")
    private Integer agentSecSdkTimeout = 1000;

    @DAttribute
    @DrmAttributeSpec(name = "AgentSecSdk总体开关")
    private boolean openAgentSecSdk = true;

    /**
     * 获取agentSECSDdk开关
     *
     * <AUTHOR>
     * @since 2024.04.24
     * @return boolean
     */
    public boolean isOpenAgentSecSdk() {
        return openAgentSecSdk;
    }

    /**
     * 设置agentSECSDdk开关
     *
     * <AUTHOR>
     * @since 2024.04.24
     * @param openAgentSecSdk openAgentSecSdk
     */
    public void setOpenAgentSecSdk(boolean openAgentSecSdk) {
        this.openAgentSecSdk = openAgentSecSdk;
    }

    public Integer getAgentSecSdkTimeout() {
        return agentSecSdkTimeout;
    }

    public void setAgentSecSdkTimeout(Integer agentSecSdkTimeout) {
        this.agentSecSdkTimeout = agentSecSdkTimeout;
    }


    @DAttribute
    @DrmAttributeSpec(name = "ideaEvo代码计划生成所用模型")
    private String ideaEvoCodePlanModel = "QWEN15_72B_CHAT_50K";


    @DAttribute
    @DrmAttributeSpec(name = "ideaEvo代码计划生成systemPrompt")
    private String ideaEvoCodePlanSystemPrompt = "你是一个CodeFuse助手，你需要按照用户需求对每个文件生成具体的更改计划。";

    @DAttribute
    @DrmAttributeSpec(name = "ideaEvo代码计划生成prompt模板")
    private String ideaEvoCodePlanPromptTemplate = "你是一个AI助手，你需要分析以下用户需求和代码片段，确定哪些文件需要修改，对需要修改的文件，以JSON格式提供修改计划。输出应包含需要修改的文件数量以及每个文件的具体修改计划。\n" +
            "下面是一个示例：\n" +
            "用户需求： compassprod应用变更创建报告接口，新加入参side字段\n" +
            "文件1： app/biz/cover-report/src/main/java/com/alipay/compass/biz/coverreport/impl/ReportConfigManagerImpl.java\n" +
            "文件1代码片段： \"\"\"@Override\n" +
            "    public String createReportConfig(CoverReportCreateRequest createRequest) {\n" +
            "        ReportConfig reportConfig = ReportConfigHelper.createRequest2ReportConfig(createRequest);\"\"\",\n" +
            "        \"\"\"@Override\n" +
            "    public void createReportByType(ReportCreateByTypeRequest createRequest) {\n" +
            "        if (createRequest.getReportType().equals(TaskTypeEnum.REPORT_CONFIG_STATISTIC.getCode())) {\n" +
            "            createReportConfig(createRequest);\n" +
            "        } else if (createRequest.getReportType().equals(TaskTypeEnum.CM_REPORT_CONFIG_STATS.getCode())) {\n" +
            "            CustomMetricsReportCreateRequest request = new CustomMetricsReportCreateRequest();\n" +
            "            BeanUtils.copyProperties(createRequest, request);\"\"\",\n" +
            "        \"\"\"if (reportConfig == null) {\n" +
            "                    LogUtil.info(LOGGER, String.format(\"create plan cover report for bizId %s appName %s\",\n" +
            "                            bizId, app.getAppName()));\n" +
            "                    createReportConfig(coverReportCreateRequest);\n" +
            "                } else {\n" +
            "                    if (reportConfig.getConfigStatus() == ReportStatusEnum.INIT ||\n" +
            "                            reportConfig.getConfigStatus() == ReportStatusEnum.PROCESSING) {\"\"\"\n" +
            "文件2： app/biz/cover-report/src/main/java/com/alipay/compass/biz/coverreport/impl/sink.java\n" +
            "文件2代码片段： \"\"\"@Override\n" +
            "    public String createReportConfig(CoverReportCreateRequest createRequest) {\n" +
            "        ReportConfig reportConfig = ReportConfigHelper.createRequest2ReportConfig(createRequest);\"\"\",\n" +
            "        \"\"\"@Override\n" +
            "    public void createReportByType(ReportCreateByTypeRequest createRequest) {\n" +
            "        if (createRequest.getReportType().equals(TaskTypeEnum.REPORT_CONFIG_STATISTIC.getCode())) {\n" +
            "            createReportConfig(createRequest);\n" +
            "        } else if (createRequest.getReportType().equals(TaskTypeEnum.CM_REPORT_CONFIG_STATS.getCode())) {\n" +
            "            CustomMetricsReportCreateRequest request = new CustomMetricsReportCreateRequest();\"\"\"\n" +
            "请分析这些代码片段，并提供一个JSON格式的修改计划\n" +
            "{\n" +
            "    \"plan_nums\": 数值, //需要修改的文件数量\n" +
            "    \"plan_info\": [ //具体改动的列表\n" +
            "        {\n" +
            "            \"path\": \"提供文件路径\",\n" +
            "            \"plan\": \"具体的修改计划描述\"\n" +
            "        }\n" +
            "    ]\n" +
            "}\n" +
            "下面是一个输出的示例：\n" +
            "{\n" +
            "    \"plan_nums\":1,\n" +
            "    \"plan_info\":[\n" +
            "        {\n" +
            "            \"path\":\"app/biz/cover-report/src/main/java/com/alipay/compass/biz/coverreport/impl/ReportConfigManagerImpl.java\",\n" +
            "            \"plan\":\"修改createReportConfig方法的调用，添加side字段。\"\n" +
            "        }\n" +
            "    ]\n" +
            "}\n" +
            "下面是用户需求以及相关文件和代码片段，你需要按照用户需求对每个文件下代码片段进行分析，分析找到需要修改的文件对其生成修改计划，并以JSON的形式输出。输出需要严格按照我们规定的输出格式来，其他模型输出的结果信息都不需要。";

    @DAttribute
    @DrmAttributeSpec(name = "ideaEvo代码生成systemPrompt")
    private String ideaEvoCodeGenSystemPrompt = "你是代码生成助手，你需要根据用户的需求来生成代码，这里给你提供了需要修改的文件代码片段以及具体的修改计划，你结合用户需求和修改计划来生成代码，同时需要把之前修改的代码和新修改的代码都展示出来。";


    @DAttribute
    @DrmAttributeSpec(name = "ideaEvo代码生成Prompt模板")
    private String ideaEvoCodeGenPromptTemplate = "你是一个AI代码生成助手，你需要根据用户的需求来生成代码，这里给你提供了需要修改的文件代码片段以及具体的修改计划，你结合用户需求和修改计划来生成代码，同时需要把之前修改的代码和新修改的代码都展示出来。\n" +
            "下面提供一个示例：\n" +
            "用户需求：在createReportConfig接口的调用处添加新的字段side。\n" +
            "文件： app/biz/cover-report/src/main/java/com/alipay/compass/biz/coverreport/impl/test.java\n" +
            "文件修改计划： 修改createReportConfig方法的调用，添加side字段。\n" +
            "文件代码片段： \"\"\"@Override\n" +
            "    public String createReportConfig(CoverReportCreateRequest createRequest) {\n" +
            "        ReportConfig reportConfig = ReportConfigHelper.createRequest2ReportConfig(createRequest);\"\"\",\n" +
            "        \"\"\"@Override\n" +
            "    public void createReportByType(ReportCreateByTypeRequest createRequest) {\n" +
            "        if (createRequest.getReportType().equals(TaskTypeEnum.REPORT_CONFIG_STATISTIC.getCode())) {\n" +
            "            createReportConfig(createRequest);\n" +
            "        } else if (createRequest.getReportType().equals(TaskTypeEnum.CM_REPORT_CONFIG_STATS.getCode())) {\n" +
            "            CustomMetricsReportCreateRequest request = new CustomMetricsReportCreateRequest();\n" +
            "            BeanUtils.copyProperties(createRequest, request);\"\"\",\n" +
            "        \"\"\"if (reportConfig == null) {\n" +
            "                    LogUtil.info(LOGGER, String.format(\"create plan cover report for bizId %s appName %s\",\n" +
            "                            bizId, app.getAppName()));\n" +
            "                    createReportConfig(coverReportCreateRequest);\n" +
            "                } else {\n" +
            "                    if (reportConfig.getConfigStatus() == ReportStatusEnum.INIT ||\n" +
            "                            reportConfig.getConfigStatus() == ReportStatusEnum.PROCESSING) {\"\"\"\n" +
            "请根据上面信息生成新的结果，新结果是一个JSON形式，JSON外部是一个python列表，里面是一个字典，字典内部的key包含3个，分别是path：文件信息；old_code：已经提供的代码片段；new_code：模型新生成的代码片段。\n" +
            "下面是一个正确的输出示例：\n" +
            "[{\n" +
            "    'path':'app/biz/cover-report/src/main/java/com/alipay/compass/biz/coverreport/impl/test.java',\n" +
            "    'old_code':\"\"\" public String createReportConfig(CoverReportCreateRequest createRequest) {\n" +
            "        ReportConfig reportConfig = ReportConfigHelper.createRequest2ReportConfig(createRequest);\"\"\",\n" +
            "        \"\"\"@Override\n" +
            "    public void createReportByType(ReportCreateByTypeRequest createRequest) {\n" +
            "        if (createRequest.getReportType().equals(TaskTypeEnum.REPORT_CONFIG_STATISTIC.getCode())) {\n" +
            "            createReportConfig(createRequest);\n" +
            "        } else if (createRequest.getReportType().equals(TaskTypeEnum.CM_REPORT_CONFIG_STATS.getCode())) {\n" +
            "            CustomMetricsReportCreateRequest request = new CustomMetricsReportCreateRequest();\n" +
            "            BeanUtils.copyProperties(createRequest, request);\"\"\",\n" +
            "        \"\"\"if (reportConfig == null) {\n" +
            "                    LogUtil.info(LOGGER, String.format(\"create plan cover report for bizId %s appName %s\",\n" +
            "                            bizId, app.getAppName()));\n" +
            "                    createReportConfig(coverReportCreateRequest);\n" +
            "                } else {\n" +
            "                    if (reportConfig.getConfigStatus() == ReportStatusEnum.INIT ||\n" +
            "                            reportConfig.getConfigStatus() == ReportStatusEnum.PROCESSING) {\"\"\",\n" +
            "    \"new_code\":\"\"\" public String createReportConfig(CoverReportCreateRequest createRequest, String side) {\n" +
            "        ReportConfig reportConfig = ReportConfigHelper.createRequest2ReportConfig(createRequest);\n" +
            "    @Override\n" +
            "    public void createReportByType(ReportCreateByTypeRequest createRequest) {\n" +
            "        if (createRequest.getReportType().equals(TaskTypeEnum.REPORT_CONFIG_STATISTIC.getCode())) {\n" +
            "            createReportConfig(createRequest, \"IN\");\n" +
            "        } else if (createRequest.getReportType().equals(TaskTypeEnum.CM_REPORT_CONFIG_STATS.getCode())) {\n" +
            "            CustomMetricsReportCreateRequest request = new CustomMetricsReportCreateRequest();\n" +
            "            BeanUtils.copyProperties(createRequest, request);\n" +
            "                if (reportConfig == null) {\n" +
            "                    LogUtil.info(LOGGER, String.format(\"create plan cover report for bizId %s appName %s\",\n" +
            "                            bizId, app.getAppName()));\n" +
            "                    createReportConfig(coverReportCreateRequest, \"IN\");\n" +
            "                } else {\n" +
            "                    if (reportConfig.getConfigStatus() == ReportStatusEnum.INIT ||\n" +
            "                            reportConfig.getConfigStatus() == ReportStatusEnum.PROCESSING) { \"\"\"}]\n" +
            "下面是一些用户需求和对应文件、修改计划和代码片段，你需要根据用户需求和修改计划来生成新的JSON，并按照上面输出格式生成正确的JSON格式。";
    @DAttribute
    @DrmAttributeSpec(name = "ACI插件默认配置信息")
    private String aciDefaultInfo = "{\n" +
            "    \"projectId\":\"127200064\",\n" +
            "    \"branch\":\"master\"\n" +
            "}";
    @DAttribute
    @DrmAttributeSpec(name = "插件默认配置")
    private String pluginDefaultConfig = "{\n" +
            "    \"aci\": \"info:\\n  name: trigger_aci_pipeline\\n  description: 根据流水线id，触发aci流水线\\n  version: v1\\n  type: api\\nparams:\\n  - name: type\\n    schema:\\n      type: string\\n    description: 流水线类型\\n    decideByLLM: false\\n    required: true\\n    default: \\\"\\\"\\n  - name: pipelineTemplateId\\n    schema:\\n      type: string\\n    description: 流水线模版id\\n    decideByLLM: false\\n    default: \\\"\\\"\\n    required: true\\n  - name: projectId\\n    schema:\\n      type: string\\n    description: ACI工程id\\n    decideByLLM: false\\n    default: \\\"\\\"\\n    required: true\\n  - name: branch\\n    schema:\\n      type: string\\n    description: 目标代码分支 \\n    decideByLLM: false\\n    required: true\\n    default: \\\"\\\"\\n  - name: ymlString\\n    schema:\\n      type: string\\n    description: 流水线模版yaml\\n    decideByLLM: false\\n    required: true\\n    default: \\\"\\\"\\n  - name: empId\\n    schema:\\n      type: string\\n    description: 工号\\n    decideByLLM: false\\n    required: true\\n    default: \\\"{empId}\\\"\\nstages:\\n  # 前置接口调用，其接口返回是一个json对象，而且是一个只有一层的kv接口\\n  # 前置接口的返回值会被赋值给preResponse变量，可以被下面的配置使用\\n  - name: preRequest\\n    # api列表，当前只支持一个，后续会支持多个，这样可以兼容chatgpt插件模式\\n    api_list:\\n      - name: trigger_aci_pipeline\\n        shortenLink: false\\n        role: main\\n        url: https://codegencore-pre.alipay.com/api/test/triggerPipeline\\n        responses:\\n          \\\"200\\\":\\n            description: 成功返回\\n            content:\\n              application/json:\\n                schema:\\n                  type: object\\n                  properties:\\n                    pipelineExecutionId:\\n                      type: string\\n                      description: 流水线执行的id\\n                  required:\\n                    - pipelineExecutionId\",\n" +
            "    \"api\": \"info:\\n  name: api_plugin_name\\n  description: api插件的描述，上面的name和这个描述都将作为大模型决策的依据\\n  version: v1\\n  type: api\\nparams:\\n  # 参数名称\\n  - name: type\\n  # 参数类型\\n    schema:\\n      type: string\\n  # 参数描述\\n    description: 流水线类型\\n  # 是否由大模型推理\\n    decideByLLM: false\\n  # 是否必填\\n    required: true\\n  # 默认值\\n    default: \\\"\\\"\\n  # 下面以工号为例：工号，提问的query可以从内存中获取默认值可以设置成\\\"{query}\\\"\\n  - name: empId\\n    schema:\\n      type: string\\n    description: 工号\\n    decideByLLM: false\\n    required: true\\n    default: \\\"{empId}\\\"\\nstages:\\n  # 前置接口调用，其接口返回是一个json对象，而且是一个只有一层的kv接口\\n  # 前置接口的返回值会被赋值给preResponse变量，可以被下面的配置使用\\n  - name: preRequest\\n    # api列表，当前只支持一个，后续会支持多个，这样可以兼容chatgpt插件模式\\n    api_list:\\n      - name: api_name\\n        shortenLink: false\\n        role: main\\n        userConfirm: true\\n        url: https://{api_url}\\n        responses:\\n          \\\"200\\\":\\n            description: 成功返回\\n            content:\\n              application/json:\\n                schema:\\n                  type: object\\n                  properties:\\n                  # 这里是返回参数名称\\n                    backParamName:\\n                      type: string\\n                      description: 返回参数\\n                  required:\\n                    - backParamName\",\n" +
            "    \"pipeline\": \"info:\\n  name: pipeline_plugin\\n  description: Pipeline插件的描述，上面的name和这个描述都将作为大模型决策的依据。\\n  version: \\\"v1\\\"\\nparams:\\n  - name: query\\n    schema:\\n      type: string\\n    description: 用户提问\\n    decideByLLM: false\\n    required: true\\n    default: \\\"{query}\\\"\\n  - name: empId\\n    schema:\\n      type: string\\n    description: 工号\\n    decideByLLM: false\\n    required: true\\n    default: \\\"{empId}\\\"\\n  - name: param\\n    schema:\\n      type: String\\n    description: 参数\\n    decideByLLM: false\\n    required: true\\n    default: 10\\nstages:\\n  # 前置接口调用，其接口返回是一个json对象，而且是一个只有一层的kv接口\\n  # 前置接口的返回值会被赋值给preResponse变量，可以被下面的配置使用\\n  - name: preRequest\\n    # api列表，当前只支持一个，后续会支持多个，这样可以兼容chatgpt插件模式\\n    api_list:\\n      - name: pre_request_name\\n        role: main\\n        url: https:{pre_url}\\n        responses:\\n          '200':\\n            description: 成功返回\\n            content:\\n              application/json:\\n                schema:\\n                  type: object\\n                  properties:\\n                    backparam:\\n                      type: string\\n                      description: 前置请求返回的参数\\n                  required:\\n                    - backparam\\n          '500':\\n            description: 失败返回\\n            content:\\n              application/json:\\n                schema:\\n                  type: object\\n                  properties:\\n                    code:\\n                      type: string\\n                      description: 错误码\\n                    msg:\\n                      type: string\\n                      description: 错误信息\\n                  required:\\n                    - code\\n                    - msg\\n  # 模型调用阶段\\n  # 模型调用阶段的返回值会被赋值给llmResult变量，可以被下面的配置使用\\n  - name: llm\\n    modelName: CODEGPT\\n    promptTemplate: \\\"{preResponse.backparam}\\\"\\n  - name: postRequest\\n    url: https://{post_url}\\n    responses:\\n      '200':\\n        description: 执行返回\\n        content:\\n          application/json:\\n            schema:\\n              type: object\\n              properties:\\n                backparam:\\n                  type: string\\n                  description: 后置请求返回参数\\n              required:\\n                - backparam\\n  - name: summary\\n    # template是一个markdown模板，其中可以通过{}使用变量，变量的值是上面阶段的信息或者一些默认变量\\n    template: \\\"{postResponse.backparam}\\\"\",\n" +
            "    \"component\":\"version: \\\"2.0\\\"\\nstages:\\n  - 组件执行\"\n" +
            "}";


    @DAttribute
    @DrmAttributeSpec(name = "action2code分片数量")
    private int actionGenCodeSplitCount = 5;

    @DAttribute
    @DrmAttributeSpec(name = "action2code相似代码缓存过期时间")
    private int actionGenCodeCodeCacheExpire = 604800;  //一天

    /**
     *  false： 使用原始 query
     *  true： 使用拼接接口、字段信息的 query
     */
    @DAttribute
    @DrmAttributeSpec(name = "action2code需求描述开关")
    private boolean actionGenCodeQuerySwitch = false;

    @DAttribute
    @DrmAttributeSpec(name = "action2code文件级生成模型")
    private String actionGenCodeFileModel = "QWEN2_72B_AIFORCE_30K";

    @DAttribute
    @DrmAttributeSpec(name = "仓库问答搜索关键词对应的回复模板")
    private String repoChatKeywordTemplate = "\uD83D\uDD0D&nbsp;&nbsp;&nbsp;**%s**\n\n";

    @DAttribute
    @DrmAttributeSpec(name = "仓库问答引用文件对应的回复模板")
    private String repoChatFileReferenceTemplate = "\uD83C\uDFAF Reading&nbsp;&nbsp;&nbsp;\uD83D\uDCC3%s\n";

    @DAttribute
    @DrmAttributeSpec(name = "问答索引消费标记")
    private String answerIndexJobPullFlag;

    @DAttribute
    @DrmAttributeSpec(name = "消费job单次查询task数量")
    private int pullJobTaskSize = 3;

    @DAttribute
    @DrmAttributeSpec(name = "job消费检查加锁时长")
    private int jobPullCheckLockTime = 10000;

    @DAttribute
    @DrmAttributeSpec(name = "仓库索引失败任务重跑错误信息")
    private String jobRetryFailMsg = "[\"embeding失败\",\"failed to embed relative path\",\"due to read error\",\"无法插入数据到 Elasticsearch, 已达到最大重试次数 (3)\",\"unsupported operand type(s) for -: 'float' and 'NoneType'\",\"ConflictError\",\"ConnectionTimeout caused by\"]";

    public String getJobRetryFailMsg() {
        return jobRetryFailMsg;
    }

    public void setJobRetryFailMsg(String jobRetryFailMsg) {
        this.jobRetryFailMsg = jobRetryFailMsg;
    }
    @DAttribute
    @DrmAttributeSpec(name = "默认的构建类型，REPO代表全仓库构建，FILE代表分文件构建索引")
    private String defaultIndexScopeType = "FILE";

    @DAttribute
    @DrmAttributeSpec(name = "codeInsight回调环境")
    private String codeInsightCallBackEnv = "prod";

    @DAttribute
    @DrmAttributeSpec(name = "svat代码文件生成prompt模板")
    private String svatCodeGenPromptTemplate = "代码文件：\"\"\"\n" +
            "${codeContent}\n" +
            "\"\"\"\n" +
            "执行计划：\n" +
            "${planStep}\n" +
            "你是一个专业的代码开发人员，专门负责根据需求生成完整的文件级别的代码。\n" +
            "你的任务是根据上文提供的执行计划，对上文提供的代码文件进行详细的修改，同时你还需要保证原有代码文件的完整性。请确保生成的代码是完整的，对当前提供的代码和注释如果没有修改的地方，需要完整拷贝过来，不能有省略。因为用户在输入后就无法再获取原代码片段，所以请直接输出修改后文件代码，不需要输出其它内容。\n" +
            "输出代码中绝对不要用\"// ... [existing methods]\"字样指代没有修改的代码，没有修改的代码就原封不动地输出！\n" +
            "输出代码中绝对不要用\"现有代码、方法、函数、服务\"字样指代没有修改的代码，没有修改的代码就原封不动地输出！\n" +
            "输出代码中绝对不要用之前代码保持不变的字样指代没有修改的代码，没有修改的代码就原封不动地输出！\n" +
            "输出代码中绝对不要用其它方法保持不变的字样指代没有修改的代码，没有修改的代码就原封不动地输出！\n" +
            "输出代码中绝对不要用类似于原始代码保持不变、其余代码保持不变、剩余代码保持不变、之前函数保持不变、其它函数保持不变、其余校验逻辑保持不变的字样表示没有修改的代码，没有修改的代码就原封不动地输出！\n" +
            "输出代码中绝对不要用保留原有代码的字样表示没有修改的代码，没有修改的代码就原封不动地输出！\n" +
            "输出代码中绝对不要省略任何注释！输出代码中绝对不要省略任何注释！输出代码中绝对不要省略任何注释！\n" +
            "输出的格式必须使用一个markdown编辑块，遵循下列格式示例：\n" +
            "```Java\n" +
            "1: package com.alipay.compass.web.home.coverage.controller;\n" +
            "2: \n" +
            "3: import com.alipay.compass.biz.coverreport.ReportConfigManager;\n" +
            "4: import com.alipay.compass.biz.coverreport.form.QueryConfigCoverReportForm;\n" +
            "5: import com.alipay.compass.biz.coverreport.result.GroupCoverReportCoverageResult;\n" +
            "6: import com.alipay.compass.common.service.facade.request.coverage.CoverRecentReportCreateRequest;\n" +
            "7: \n" +
            "8: @Controller\n" +
            "9: @RequestMapping(value = ControllerConstant.COMPASS_OPENAPI_PATH)\n" +
            "10: public class ReportConfigOpenApiController extends BaseController {\n" +
            "11: \n" +
            "12:     @SofaReference\n" +
            "13:     private ReportConfigManager reportConfigManager;\n" +
            "14: \n" +
            "15:     @RequestMapping(value = \"create_report_config.json\")\n" +
            "16:     @ResponseBody\n" +
            "17:     public CommonApiResult createReportConfigOpenApi(@RequestBody CoverReportCreateRequest createRequest) {\n" +
            "18:         return ControllerTemplate.process(new AbstractControllerCallback<String>() {\n" +
            "19:             @Override\n" +
            "20:             public void checkParameter() {\n" +
            "21:                 fillAndCheckSiteIdByRepoApp(createRequest.getTenantSiteId(), createRequest.getGitUrl(), createRequest.getAppId());\n" +
            "22:             }\n" +
            "23: \n" +
            "24:             @Override\n" +
            "25:             public String executeService() {\n" +
            "26:                 return reportConfigManager.createReportConfig(createRequest, \"IN\");\n" +
            "27:             }\n" +
            "28:         }, \"create_report_config.json\", createRequest);\n" +
            "29:     }\n" +
            "30: }\n" +
            "```";


    @DAttribute
    @DrmAttributeSpec(name = "maya模型是否开启队列存储")
    private boolean modelEnableQueue = true;

    @DAttribute
    @DrmAttributeSpec(name = "构建索引任务默认优先级")
    private int TaskPriority = 1;

    public int getTaskPriority() {
        return TaskPriority;
    }

    public void setTaskPriority(int taskPriority) {
        TaskPriority = taskPriority;
    }

    @DAttribute
    @DrmAttributeSpec(name = "antcode侧边栏是否自动构建未构建的仓库")
    private boolean antcodeCopilotAutoIndexRepo = false;

    @DAttribute
    @DrmAttributeSpec(name = "antcode侧边栏仓库构建中回复文案")
    private String antcodeCopilotRepoIndexingResponse = "首次提问需要几分钟构建仓库索引，大仓库时间稍久一些，请稍等一会再试，长时间没有构建成功请联系[Codefuse平台小蜜](https://links.alipay.com/app/room/64c32b512f769c06d4b864e8/)";

    @DAttribute
    @DrmAttributeSpec(name = "antcode侧边栏仓库未构建回复文案")
    private String antcodeCopilotRepoNotIndexResponse = "对应的仓库/分支暂不在仓库问答功能白名单中，如有需要请咨询[Codefuse平台小蜜](https://links.alipay.com/app/room/64c32b512f769c06d4b864e8/)";

    @DAttribute
    @DrmAttributeSpec(name = "语雀分段服务url")
    private String yuQueSegmentationStrategyUrl = "https://paiplusinference.alipay.com/inference/76c5a99f6859f5ab_x1_text_splitter/v1";

    @DAttribute
    @DrmAttributeSpec(name = "仓库问答的客户端请求配置，用于覆盖pybloop层的配置")
    private String repoChatClientConfig = "{\n" +
            "    \"stream\": true\n" +
            "  }";

    @DAttribute
    @DrmAttributeSpec(name = "问答获取wiki时是否做校验")
    private boolean answerWikiCheck = false;

    @DAttribute
    @DrmAttributeSpec(name = "校验仓库的对应ZSearch数据为空后是否重新构建")
    private boolean enableAnswerIndexBuild = false;

    @DAttribute
    @DrmAttributeSpec(name = "增量更新指定切流repo占比")
    private Long repoPartBuildRate = 20L;


    @DAttribute
    @DrmAttributeSpec(name = "从数据库获取Prompt模版")
    private boolean enablePromptTemplateFromDB = false;

    @DAttribute
    @DrmAttributeSpec(name = "索引重试次数限制")
    private int indexBuildRetryCount = 5;

    @DAttribute
    @DrmAttributeSpec(name = "仓库问答支持代码库白名单")
    private String repoUrlWhiteList = "[\"code.alipay.com\",\"gitlab.alipay-inc.com\",\"code.linke.alipay.com\",\"antcodeweb-pool.alipay.com\",\"code.mybank.cn\",\"code.myxiaojin.cn\",\"gitlab.antfin-inc.com\",\"localhost\",\"gitlab-sc.alipay-inc.com\",\"bkcode.antfin-inc.com\",\"codemybank.alipay.com\",\"code-pre.alipay.com\",\"code-sc.alipay.com\"]";

    @DAttribute
    @DrmAttributeSpec(name = "仓库问答代码库白名单开关")
    private boolean enableRepoUrlWhiteList = false;

    @DAttribute
    @DrmAttributeSpec(name = "仓库问答是否打开用户仓库权限校验")
    private boolean enableMemberAccessCheck = false;

    @DAttribute
    @DrmAttributeSpec(name = "ironman特殊仓库配置超时时间")
    private int ironmanTaskTimeout = 10800;

    @DAttribute
    @DrmAttributeSpec(name = "校验仓库的对应ZSearch数量大于应该成功的数量删除对应仓库后构建数量 ")
    private int answerIndexCleanSize =  1;

    @DAttribute
    @DrmAttributeSpec(name = "仓库索引构建scanAction")
    private String indexBuildScanAction = "cubesugar_pybloop_index";

    @DAttribute
    @DrmAttributeSpec(name = "仓库wiki构建scanAction")
    private String wikiBuildScanAction = "repo_wiki_generation";

    @DAttribute
    @DrmAttributeSpec(name = "跳过token计算")
    private boolean skipCalculateToken = false;



    public int getAnswerIndexCleanSize() {
        return answerIndexCleanSize;
    }

    public void setAnswerIndexCleanSize(int answerIndexCleanSize) {
        this.answerIndexCleanSize = answerIndexCleanSize;
    }

    public boolean isEnablePromptTemplateFromDB() {
        return enablePromptTemplateFromDB;
    }

    public void setEnablePromptTemplateFromDB(boolean enablePromptTemplateFromDB) {
        this.enablePromptTemplateFromDB = enablePromptTemplateFromDB;
    }

    public boolean isEnableAnswerIndexBuild() {
        return enableAnswerIndexBuild;
    }

    public void setEnableAnswerIndexBuild(boolean enableAnswerIndexBuild) {
        this.enableAnswerIndexBuild = enableAnswerIndexBuild;
    }

    public boolean isModelEnableQueue() {
        return modelEnableQueue;
    }

    public void setModelEnableQueue(boolean modelEnableQueue) {
        this.modelEnableQueue = modelEnableQueue;
    }

    public String getPluginDefaultConfig() {
        return pluginDefaultConfig;
    }

    public void setPluginDefaultConfig(String pluginDefaultConfig) {
        this.pluginDefaultConfig = pluginDefaultConfig;
    }

    public String getAciDefaultInfo() {
        return aciDefaultInfo;
    }

    public void setAciDefaultInfo(String aciDefaultInfo) {
        this.aciDefaultInfo = aciDefaultInfo;
    }

    public String getInspectionSwitch() {
        return inspectionSwitch;
    }

    public void setInspectionSwitch(String inspectionSwitch) {
        this.inspectionSwitch = inspectionSwitch;
    }

    public String getPageIdList() {
        return pageIdList;
    }

    public void setPageIdList(String pageIdList) {
        this.pageIdList = pageIdList;
    }

    public String getOpenApiLogSwitch() {
        return openApiLogSwitch;
    }

    public void setOpenApiLogSwitch(String openApiLogSwitch) {
        this.openApiLogSwitch = openApiLogSwitch;
    }

    public String getAciModelConfig() {
        return aciModelConfig;
    }

    public void setAciModelConfig(String aciModelConfig) {
        this.aciModelConfig = aciModelConfig;
    }

    public String getSceneAndPluginAvatarList() {
        return sceneAndPluginAvatarList;
    }

    public void setSceneAndPluginAvatarList(String sceneAndPluginAvatarList) {
        this.sceneAndPluginAvatarList = sceneAndPluginAvatarList;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public String getDefaultModelImplConfig() {
        return defaultModelImplConfig;
    }

    public void setDefaultModelImplConfig(String defaultModelImplConfig) {
        this.defaultModelImplConfig = defaultModelImplConfig;
    }

    public String getModelHandler() {
        return modelHandler;
    }

    public void setModelHandler(String modelHandler) {
        this.modelHandler = modelHandler;
    }

    public boolean isMayaModelDeploymentCheck() {
        return mayaModelDeploymentCheck;
    }

    public void setMayaModelDeploymentCheck(boolean mayaModelDeploymentCheck) {
        this.mayaModelDeploymentCheck = mayaModelDeploymentCheck;
    }

    @DAttribute
    @DrmAttributeSpec(name = "是否开启maya模型部署环境监测")
    private boolean mayaModelDeploymentCheck = true;

    public Integer getMessageNum() {
        return messageNum;
    }

    public void setMessageNum(Integer messageNum) {
        this.messageNum = messageNum;
    }

    public String getZarkConfig() {
        return zarkConfig;
    }

    public void setZarkConfig(String zarkConfig) {
        this.zarkConfig = zarkConfig;
    }

    public String getAciComponentConfig() {
        return aciComponentConfig;
    }

    public long getChatGptTimingFeeTime() {
        return chatGptTimingFeeTime;
    }

    public void setChatGptTimingFeeTime(long chatGptTimingFeeTime) {
        this.chatGptTimingFeeTime = chatGptTimingFeeTime;
    }

    public void setAciComponentConfig(String aciComponentConfig) {
        this.aciComponentConfig = aciComponentConfig;
    }

    public String getDocumentChatConfig() {
        return documentChatConfig;
    }

    public void setDocumentChatConfig(String documentChatConfig) {
        this.documentChatConfig = documentChatConfig;
    }

    public String getOssConfig() {
        return ossConfig;
    }

    public void setOssConfig(String ossConfig) {
        this.ossConfig = ossConfig;
    }

    public String getSceneAppointSort() {
        return sceneAppointSort;
    }

    public void setSceneAppointSort(String sceneAppointSort) {
        this.sceneAppointSort = sceneAppointSort;
    }

    public String getPreNeedUseCommonPowerTokenUser() {
        return preNeedUseCommonPowerTokenUser;
    }

    public void setPreNeedUseCommonPowerTokenUser(String preNeedUseCommonPowerTokenUser) {
        this.preNeedUseCommonPowerTokenUser = preNeedUseCommonPowerTokenUser;
    }

    public Long getSceneDefaultId() {
        return sceneDefaultId;
    }

    public void setSceneDefaultId(Long sceneDefaultId) {
        this.sceneDefaultId = sceneDefaultId;
    }

    private JSONObject antGLMAuthConfig = JSON.parseObject(antGLMAuthConfigStr);

    public String getAntGLMAuthConfigStr() {
        return antGLMAuthConfigStr;
    }

    public void setAntGLMAuthConfigStr(String antGLMAuthConfigStr) {
        this.antGLMAuthConfigStr = antGLMAuthConfigStr;
        antGLMAuthConfig = JSON.parseObject(antGLMAuthConfigStr);
    }

    public JSONObject getAntGLMAuthConfig() {
        return antGLMAuthConfig;
    }

    public String getUserLoginNotifyInnerPluginURL() {
        return userLoginNotifyInnerPluginURL;
    }

    public void setUserLoginNotifyInnerPluginURL(String userLoginNotifyInnerPluginURL) {
        this.userLoginNotifyInnerPluginURL = userLoginNotifyInnerPluginURL;
    }

    public String getUserLoginNotifyPluginURL() {
        return userLoginNotifyPluginURL;
    }

    public void setUserLoginNotifyPluginURL(String userLoginNotifyPluginURL) {
        this.userLoginNotifyPluginURL = userLoginNotifyPluginURL;
    }

    public String getNoBlockingCheckPlatform() {
        return noBlockingCheckPlatform;
    }

    public void setNoBlockingCheckPlatform(String noBlockingCheckPlatform) {
        this.noBlockingCheckPlatform = noBlockingCheckPlatform;
    }

    public List<ReviewPlatformEnum> getNoBlockingCheckPlatformList() {
        return JSON.parseArray(noBlockingCheckPlatform, ReviewPlatformEnum.class);
    }

    public String getGenerateTitleUseModel() {
        return generateTitleUseModel;
    }

    public void setGenerateTitleUseModel(String generateTitleUseModel) {
        this.generateTitleUseModel = generateTitleUseModel;
    }

    public int getGptCacheEnable() {
        return gptCacheEnable;
    }

    public void setGptCacheEnable(int gptCacheEnable) {
        this.gptCacheEnable = gptCacheEnable;
    }

    public int getGptCacheFactor() {
        return gptCacheFactor;
    }

    public void setGptCacheFactor(int gptCacheFactor) {
        this.gptCacheFactor = gptCacheFactor;
    }

    public boolean isIntranetApplication() {
        return intranetApplication;
    }

    public void setIntranetApplication(boolean intranetApplication) {
        this.intranetApplication = intranetApplication;
    }

    public Integer getSessionListMaxSize() {
        return sessionListMaxSize;
    }

    public void setSessionListMaxSize(Integer sessionListMaxSize) {
        this.sessionListMaxSize = sessionListMaxSize;
    }

    public String getCheckLengthConfig() {
        return checkLengthConfig;
    }

    public void setCheckLengthConfig(String checkLengthConfig) {
        this.checkLengthConfig = checkLengthConfig;
    }

    public String getRcsmartConfig() {
        return rcsmartConfig;
    }

    public void setRcsmartConfig(String rcsmartConfig) {
        this.rcsmartConfig = rcsmartConfig;
    }

    public String getFaasToolInfo() {
        return faasToolInfo;
    }

    public void setFaasToolInfo(String faasToolInfo) {
        this.faasToolInfo = faasToolInfo;
    }

    public String getCodeGPTSystemValue() {
        return codeGPTSystemValue;
    }

    public void setCodeGPTSystemValue(String codeGPTSystemValue) {
        this.codeGPTSystemValue = codeGPTSystemValue;
    }

    public Integer getOverlapLength() {
        return overlapLength;
    }

    public void setOverlapLength(Integer overlapLength) {
        this.overlapLength = overlapLength;
    }

    public Integer getNeoxOutputMaxLength() {
        return neoxOutputMaxLength;
    }

    public void setNeoxOutputMaxLength(Integer neoxOutputMaxLength) {
        this.neoxOutputMaxLength = neoxOutputMaxLength;
    }

    public String getMockReferenceDataList() {
        return mockReferenceDataList;
    }

    public void setMockReferenceDataList(String mockReferenceDataList) {
        this.mockReferenceDataList = mockReferenceDataList;
    }

    public String getCodeGPTQueryTemplateList() {
        return codeGPTQueryTemplateList;
    }

    public void setCodeGPTQueryTemplateList(String codeGPTQueryTemplateList) {
        this.codeGPTQueryTemplateList = codeGPTQueryTemplateList;
    }

    public String getNeoxRequestUrl() {
        return neoxRequestUrl;
    }

    public void setNeoxRequestUrl(String neoxRequestUrl) {
        this.neoxRequestUrl = neoxRequestUrl;
    }

    public String getInfosecSwitch() {
        return infosecSwitch;
    }

    public void setInfosecSwitch(String infosecSwitch) {
        this.infosecSwitch = infosecSwitch;
    }

    public Integer getKeymapTimeOut() {
        return keymapTimeOut;
    }

    public void setKeymapTimeOut(Integer keymapTimeOut) {
        this.keymapTimeOut = keymapTimeOut;
    }

    public String getKeymapSwitch() {
        return keymapSwitch;
    }

    public void setKeymapSwitch(String keymapSwitch) {
        this.keymapSwitch = keymapSwitch;
    }

    public String getKeymapUrl() {
        return keymapUrl;
    }

    public void setKeymapUrl(String keymapUrl) {
        this.keymapUrl = keymapUrl;
    }

    public Integer getCheckDataMaxSize() {
        return checkDataMaxSize;
    }

    public void setCheckDataMaxSize(Integer checkDataMaxSize) {
        this.checkDataMaxSize = checkDataMaxSize;
    }

    public Integer getCheckDataSizeWithPunctuation() {
        return checkDataSizeWithPunctuation;
    }

    public void setCheckDataSizeWithPunctuation(Integer checkDataSizeWithPunctuation) {
        this.checkDataSizeWithPunctuation = checkDataSizeWithPunctuation;
    }

    public Integer getRateLimiterSleepMills() {
        return rateLimiterSleepMills;
    }

    public void setRateLimiterSleepMills(Integer rateLimiterSleepMills) {
        this.rateLimiterSleepMills = rateLimiterSleepMills;
    }

    public Integer getRateLimiterMaxWaitMills() {
        return rateLimiterMaxWaitMills;
    }

    public void setRateLimiterMaxWaitMills(Integer rateLimiterMaxWaitMills) {
        this.rateLimiterMaxWaitMills = rateLimiterMaxWaitMills;
    }

    public Integer getMaxOnlineUserNum() {
        return maxOnlineUserNum;
    }

    public void setMaxOnlineUserNum(Integer maxOnlineUserNum) {
        this.maxOnlineUserNum = maxOnlineUserNum;
    }

    public Integer getAsyCheckMaxWaitTimeQuestion() {
        return asyCheckMaxWaitTimeQuestion;
    }

    public void setAsyCheckMaxWaitTimeQuestion(Integer asyCheckMaxWaitTimeQuestion) {
        this.asyCheckMaxWaitTimeQuestion = asyCheckMaxWaitTimeQuestion;
    }

    public Integer getAsyCheckMaxWaitTimeAnswer() {
        return asyCheckMaxWaitTimeAnswer;
    }

    public void setAsyCheckMaxWaitTimeAnswer(Integer asyCheckMaxWaitTimeAnswer) {
        this.asyCheckMaxWaitTimeAnswer = asyCheckMaxWaitTimeAnswer;
    }

    public Integer getLoopWaitTime() {
        return loopWaitTime;
    }

    public void setLoopWaitTime(Integer loopWaitTime) {
        this.loopWaitTime = loopWaitTime;
    }

    public Integer getDefaultBalance() {
        return defaultBalance;
    }

    public void setDefaultBalance(Integer defaultBalance) {
        this.defaultBalance = defaultBalance;
    }

    public boolean isDefaultCreateUser() {
        return defaultCreateUser;
    }

    public void setDefaultCreateUser(boolean defaultCreateUser) {
        this.defaultCreateUser = defaultCreateUser;
    }

    public boolean isDefaultApproval() {
        return defaultApproval;
    }

    public void setDefaultApproval(boolean defaultApproval) {
        this.defaultApproval = defaultApproval;
    }

    public String getAntnluServiceUrl() {
        return antnluServiceUrl;
    }

    public void setAntnluServiceUrl(String antnluServiceUrl) {
        this.antnluServiceUrl = antnluServiceUrl;
    }

    public String getAntDsrSwitch() {
        return antDsrSwitch;
    }

    public void setAntDsrSwitch(String antDsrSwitch) {
        this.antDsrSwitch = antDsrSwitch;
    }

    public String getIntentionRecognitionServiceUrl() {
        return intentionRecognitionServiceUrl;
    }

    public void setIntentionRecognitionServiceUrl(String intentionRecognitionServiceUrl) {
        this.intentionRecognitionServiceUrl = intentionRecognitionServiceUrl;
    }

    public String getCodeFuseCheckSwitch() {
        return codeFuseCheckSwitch;
    }

    public void setCodeFuseCheckSwitch(String codeFuseCheckSwitch) {
        this.codeFuseCheckSwitch = codeFuseCheckSwitch;
    }

    public String getCodeFuseCheckFailedMsg() {
        return codeFuseCheckFailedMsg;
    }

    public void setCodeFuseCheckFailedMsg(String codeFuseCheckFailedMsg) {
        this.codeFuseCheckFailedMsg = codeFuseCheckFailedMsg;
    }

    public String getUserCodeFuseCheckSwitch() {
        return userCodeFuseCheckSwitch;
    }

    public void setUserCodeFuseCheckSwitch(String userCodeFuseCheckSwitch) {
        this.userCodeFuseCheckSwitch = userCodeFuseCheckSwitch;
    }

    public String getInfoSecNeedCheckUser() {
        return infoSecNeedCheckUser;
    }

    public void setInfoSecNeedCheckUser(String infoSecNeedCheckUser) {
        this.infoSecNeedCheckUser = infoSecNeedCheckUser;
    }

    public String getInfoSecAsyncTime() {
        return infoSecAsyncTime;
    }

    public void setInfoSecAsyncTime(String infoSecAsyncTime) {
        this.infoSecAsyncTime = infoSecAsyncTime;
    }

    public Integer getPluginStreamDataPollingStep() {
        return pluginStreamDataPollingStep;
    }

    public void setPluginStreamDataPollingStep(Integer pluginStreamDataPollingStep) {
        this.pluginStreamDataPollingStep = pluginStreamDataPollingStep;
    }

    public Integer getPluginCommonStreamDataWaitTime() {
        return pluginCommonStreamDataWaitTime;
    }

    public void setPluginCommonStreamDataWaitTime(Integer pluginCommonStreamDataWaitTime) {
        this.pluginCommonStreamDataWaitTime = pluginCommonStreamDataWaitTime;
    }

    public Integer getPluginLLMStageCommonStreamDataWaitTime() {
        return pluginLLMStageCommonStreamDataWaitTime;
    }

    public void setPluginLLMStageCommonStreamDataWaitTime(Integer pluginLLMStageCommonStreamDataWaitTime) {
        this.pluginLLMStageCommonStreamDataWaitTime = pluginLLMStageCommonStreamDataWaitTime;
    }

    public Integer getPluginLLMStageFirstStreamDataWaitTime() {
        return pluginLLMStageFirstStreamDataWaitTime;
    }

    public void setPluginLLMStageFirstStreamDataWaitTime(Integer pluginLLMStageFirstStreamDataWaitTime) {
        this.pluginLLMStageFirstStreamDataWaitTime = pluginLLMStageFirstStreamDataWaitTime;
    }

    public Integer getPluginRequestDefaultTimeOut() {
        return pluginRequestDefaultTimeOut;
    }

    public void setPluginRequestDefaultTimeOut(Integer pluginRequestDefaultTimeOut) {
        this.pluginRequestDefaultTimeOut = pluginRequestDefaultTimeOut;
    }

    public Integer getFunctionCallDefaultTimeOut() {
        return functionCallDefaultTimeOut;
    }

    public void setFunctionCallDefaultTimeOut(Integer functionCallDefaultTimeOut) {
        this.functionCallDefaultTimeOut = functionCallDefaultTimeOut;
    }

    public String getFunctionSearchUrl() {
        return functionSearchUrl;
    }

    public void setFunctionSearchUrl(String functionSearchUrl) {
        this.functionSearchUrl = functionSearchUrl;
    }

    public String getPluginLLMStageDefaultPromptTemplate() {
        return pluginLLMStageDefaultPromptTemplate;
    }

    public void setPluginLLMStageDefaultPromptTemplate(String pluginLLMStageDefaultPromptTemplate) {
        this.pluginLLMStageDefaultPromptTemplate = pluginLLMStageDefaultPromptTemplate;
    }

    public String getQpxRateLimiterConfig() {
        return qpxRateLimiterConfig;
    }

    public void setQpxRateLimiterConfig(String qpxRateLimiterConfig) {
        this.qpxRateLimiterConfig = qpxRateLimiterConfig;
        this.qpxRateLimiterConfigJson = JSONObject.parseObject(qpxRateLimiterConfig);
    }

    public JSONObject getQpxRateLimiterConfigJson() {
        return qpxRateLimiterConfigJson;
    }

    public void setQpxRateLimiterConfigJson(JSONObject qpxRateLimiterConfigJson) {
        this.qpxRateLimiterConfigJson = qpxRateLimiterConfigJson;
    }

    public String getQpxRateLimiterAllowUseDefaultTokenUser() {
        return qpxRateLimiterAllowUseDefaultTokenUser;
    }

    public void setQpxRateLimiterAllowUseDefaultTokenUser(String qpxRateLimiterAllowUseDefaultTokenUser) {
        this.qpxRateLimiterAllowUseDefaultTokenUser = qpxRateLimiterAllowUseDefaultTokenUser;
        this.qpxRateLimiterAllowUseDefaultTokenUserList = JSON.parseArray(qpxRateLimiterAllowUseDefaultTokenUser,String.class);
    }

    public List<String> getQpxRateLimiterAllowUseDefaultTokenUserList() {
        return qpxRateLimiterAllowUseDefaultTokenUserList;
    }

    public void setQpxRateLimiterAllowUseDefaultTokenUserList(List<String> qpxRateLimiterAllowUseDefaultTokenUserList) {
        this.qpxRateLimiterAllowUseDefaultTokenUserList = qpxRateLimiterAllowUseDefaultTokenUserList;
    }

    public String getNoNeedRateLimitModels() {
        return noNeedRateLimitModels;
    }

    public void setNoNeedRateLimitModels(String noNeedRateLimitModels) {
        this.noNeedRateLimitModels = noNeedRateLimitModels;
        this.noNeedRateLimitModelList = JSON.parseArray(noNeedRateLimitModels,String.class);
    }

    public List<String> getNoNeedRateLimitModelList() {
        return noNeedRateLimitModelList;
    }

    public void setNoNeedRateLimitModelList(List<String> noNeedRateLimitModelList) {
        this.noNeedRateLimitModelList = noNeedRateLimitModelList;
    }

    public String getDefaultFunctionCallModel() {
        return defaultFunctionCallModel;
    }

    public void setDefaultFunctionCallModel(String defaultFunctionCallModel) {
        this.defaultFunctionCallModel = defaultFunctionCallModel;
    }

    public boolean isFunctionCallMultiRoundEnabled() {
        return functionCallMultiRoundEnabled;
    }

    public void setFunctionCallMultiRoundEnabled(boolean functionCallMultiRoundEnabled) {
        this.functionCallMultiRoundEnabled = functionCallMultiRoundEnabled;
    }

    public String getLackParamStrategy() {
        return lackParamStrategy;
    }

    public void setLackParamStrategy(String lackParamStrategy) {
        this.lackParamStrategy = lackParamStrategy;
    }

    public Integer getMaxFunctionCallRound() {
        return maxFunctionCallRound;
    }

    public void setMaxFunctionCallRound(Integer maxFunctionCallRound) {
        this.maxFunctionCallRound = maxFunctionCallRound;
    }

    public String getChatMessageFormatVersion() {
        return chatMessageFormatVersion;
    }

    public void setChatMessageFormatVersion(String chatMessageFormatVersion) {
        this.chatMessageFormatVersion = chatMessageFormatVersion;
    }

    public void setAntGLMAuthConfig(JSONObject antGLMAuthConfig) {
        this.antGLMAuthConfig = antGLMAuthConfig;
    }

    public String getIgnoreEnvCheckModels() {
        return ignoreEnvCheckModels;
    }

    public void setIgnoreEnvCheckModels(String ignoreEnvCheckModels) {
        this.ignoreEnvCheckModels = ignoreEnvCheckModels;
    }

    public Integer getLinkLengthConfig() {
        return linkLengthConfig;
    }

    public void setLinkLengthConfig(Integer linkLengthConfig) {
        this.linkLengthConfig = linkLengthConfig;
    }

    public String getNeedCheckBizApi() {
        return needCheckBizApi;
    }

    public void setNeedCheckBizApi(String needCheckBizApi) {
        this.needCheckBizApi = needCheckBizApi;
    }

    public String getNeedCheckAuthFaasList() {
        return needCheckAuthFaasList;
    }

    public void setNeedCheckAuthFaasList(String needCheckAuthFaasList) {
        this.needCheckAuthFaasList = needCheckAuthFaasList;
    }

    public String getMessageFormDefaultUiSchema() {
        return messageFormDefaultUiSchema;
    }

    public void setMessageFormDefaultUiSchema(String messageFormDefaultUiSchema) {
        this.messageFormDefaultUiSchema = messageFormDefaultUiSchema;
    }

    public String getModelKeepLivingBlackList() {
        return modelKeepLivingBlackList;
    }

    public void setModelKeepLivingBlackList(String modelKeepLivingBlackList) {
        this.modelKeepLivingBlackList = modelKeepLivingBlackList;
    }

    public Integer getZsearchTaskRetryTimes() {
        return zsearchTaskRetryTimes;
    }

    public void setZsearchTaskRetryTimes(Integer zsearchTaskRetryTimes) {
        this.zsearchTaskRetryTimes = zsearchTaskRetryTimes;
    }

    public Integer getZsearchTimeout() {
        return zsearchTimeout;
    }

    public void setZsearchTimeout(Integer zsearchTimeout) {
        this.zsearchTimeout = zsearchTimeout;
    }

    public String getHealthDegreeCheckConfig() {
        return healthDegreeCheckConfig;
    }

    public void setHealthDegreeCheckConfig(String healthDegreeCheckConfig) {
        this.healthDegreeCheckConfig = healthDegreeCheckConfig;
    }

    public String getNotifyMembersDefault() {
        return notifyMembersDefault;
    }

    public void setNotifyMembersDefault(String notifyMembersDefault) {
        this.notifyMembersDefault = notifyMembersDefault;
    }

    public String getFileAnnotationType() {
        return fileAnnotationType;
    }

    public void setFileAnnotationType(String fileAnnotationType) {
        this.fileAnnotationType = fileAnnotationType;
    }

    public boolean isDocSearchResultAddToPrompt() {
        return docSearchResultAddToPrompt;
    }

    public void setDocSearchResultAddToPrompt(boolean docSearchResultAddToPrompt) {
        this.docSearchResultAddToPrompt = docSearchResultAddToPrompt;
    }

    public String getIdeaEvoCodePlanModel() {
        return ideaEvoCodePlanModel;
    }

    public void setIdeaEvoCodePlanModel(String ideaEvoCodePlanModel) {
        this.ideaEvoCodePlanModel = ideaEvoCodePlanModel;
    }

    public String getIdeaEvoCodePlanSystemPrompt() {
        return ideaEvoCodePlanSystemPrompt;
    }

    public void setIdeaEvoCodePlanSystemPrompt(String ideaEvoCodePlanSystemPrompt) {
        this.ideaEvoCodePlanSystemPrompt = ideaEvoCodePlanSystemPrompt;
    }

    public String getIdeaEvoCodePlanPromptTemplate() {
        return ideaEvoCodePlanPromptTemplate;
    }

    public void setIdeaEvoCodePlanPromptTemplate(String ideaEvoCodePlanPromptTemplate) {
        this.ideaEvoCodePlanPromptTemplate = ideaEvoCodePlanPromptTemplate;
    }

    public String getIdeaEvoCodeGenSystemPrompt() {
        return ideaEvoCodeGenSystemPrompt;
    }

    public void setIdeaEvoCodeGenSystemPrompt(String ideaEvoCodeGenSystemPrompt) {
        this.ideaEvoCodeGenSystemPrompt = ideaEvoCodeGenSystemPrompt;
    }

    public String getIdeaEvoCodeGenPromptTemplate() {
        return ideaEvoCodeGenPromptTemplate;
    }

    public void setIdeaEvoCodeGenPromptTemplate(String ideaEvoCodeGenPromptTemplate) {
        this.ideaEvoCodeGenPromptTemplate = ideaEvoCodeGenPromptTemplate;
    }

    public int getActionGenCodeSplitCount() {
        return actionGenCodeSplitCount;
    }

    public void setActionGenCodeSplitCount(int actionGenCodeSplitCount) {
        this.actionGenCodeSplitCount = actionGenCodeSplitCount;
    }

    public String getRepoChatKeywordTemplate() {
        return repoChatKeywordTemplate;
    }

    public void setRepoChatKeywordTemplate(String repoChatKeywordTemplate) {
        this.repoChatKeywordTemplate = repoChatKeywordTemplate;
    }

    public String getRepoChatFileReferenceTemplate() {
        return repoChatFileReferenceTemplate;
    }

    public void setRepoChatFileReferenceTemplate(String repoChatFileReferenceTemplate) {
        this.repoChatFileReferenceTemplate = repoChatFileReferenceTemplate;
    }

    public int getActionGenCodeCodeCacheExpire() {
        return actionGenCodeCodeCacheExpire;
    }

    public void setActionGenCodeCodeCacheExpire(int actionGenCodeCodeCacheExpire) {
        this.actionGenCodeCodeCacheExpire = actionGenCodeCodeCacheExpire;
    }

    public boolean isActionGenCodeQuerySwitch() {
        return actionGenCodeQuerySwitch;
    }

    public void setActionGenCodeQuerySwitch(boolean actionGenCodeQuerySwitch) {
        this.actionGenCodeQuerySwitch = actionGenCodeQuerySwitch;
    }

    public String getAnswerIndexJobPullFlag() {
        return answerIndexJobPullFlag;
    }

    public void setAnswerIndexJobPullFlag(String answerIndexJobPullFlag) {
        this.answerIndexJobPullFlag = answerIndexJobPullFlag;
    }

    public int getPullJobTaskSize() {
        return pullJobTaskSize;
    }

    public void setPullJobTaskSize(int pullJobTaskSize) {
        this.pullJobTaskSize = pullJobTaskSize;
    }

    public int getJobPullCheckLockTime() {
        return jobPullCheckLockTime;
    }

    public void setJobPullCheckLockTime(int jobPullCheckLockTime) {
        this.jobPullCheckLockTime = jobPullCheckLockTime;
    }

    public String getDefaultIndexScopeType() {
        return defaultIndexScopeType;
    }

    public void setDefaultIndexScopeType(String defaultIndexScopeType) {
        this.defaultIndexScopeType = defaultIndexScopeType;
    }

    public String getCodeInsightCallBackEnv() {
        return codeInsightCallBackEnv;
    }

    public void setCodeInsightCallBackEnv(String codeInsightCallBackEnv) {
        this.codeInsightCallBackEnv = codeInsightCallBackEnv;
    }

    public String getSvatCodeGenPromptTemplate() {
        return svatCodeGenPromptTemplate;
    }

    public void setSvatCodeGenPromptTemplate(String svatCodeGenPromptTemplate) {
        this.svatCodeGenPromptTemplate = svatCodeGenPromptTemplate;
    }

    public String getActionGenCodeFileModel() {
        return actionGenCodeFileModel;
    }

    public void setActionGenCodeFileModel(String actionGenCodeFileModel) {
        this.actionGenCodeFileModel = actionGenCodeFileModel;
    }

    public boolean isAntcodeCopilotAutoIndexRepo() {
        return antcodeCopilotAutoIndexRepo;
    }

    public void setAntcodeCopilotAutoIndexRepo(boolean antcodeCopilotAutoIndexRepo) {
        this.antcodeCopilotAutoIndexRepo = antcodeCopilotAutoIndexRepo;
    }

    public String getAntcodeCopilotRepoIndexingResponse() {
        return antcodeCopilotRepoIndexingResponse;
    }

    public void setAntcodeCopilotRepoIndexingResponse(String antcodeCopilotRepoIndexingResponse) {
        this.antcodeCopilotRepoIndexingResponse = antcodeCopilotRepoIndexingResponse;
    }

    public String getAntcodeCopilotRepoNotIndexResponse() {
        return antcodeCopilotRepoNotIndexResponse;
    }

    public void setAntcodeCopilotRepoNotIndexResponse(String antcodeCopilotRepoNotIndexResponse) {
        this.antcodeCopilotRepoNotIndexResponse = antcodeCopilotRepoNotIndexResponse;
    }

    public boolean isAnswerWikiCheck() {
        return answerWikiCheck;
    }

    public void setAnswerWikiCheck(boolean answerWikiCheck) {
        this.answerWikiCheck = answerWikiCheck;
    }

    public String getRepoChatClientConfig() {
        return repoChatClientConfig;
    }

    public void setRepoChatClientConfig(String repoChatClientConfig) {
        this.repoChatClientConfig = repoChatClientConfig;
    }

    public String getYuQueSegmentationStrategyUrl() {
        return yuQueSegmentationStrategyUrl;
    }

    public void setYuQueSegmentationStrategyUrl(String yuQueSegmentationStrategyUrl) {
        this.yuQueSegmentationStrategyUrl = yuQueSegmentationStrategyUrl;
    }

    public Long getRepoPartBuildRate() {
        return repoPartBuildRate;
    }

    public void setRepoPartBuildRate(Long repoPartBuildRate) {
        this.repoPartBuildRate = repoPartBuildRate;
    }

    public int getIndexBuildRetryCount() {
        return indexBuildRetryCount;
    }

    public void setIndexBuildRetryCount(int indexBuildRetryCount) {
        this.indexBuildRetryCount = indexBuildRetryCount;
    }

    public String getRepoUrlWhiteList() {
        return repoUrlWhiteList;
    }

    public void setRepoUrlWhiteList(String repoUrlWhiteList) {
        this.repoUrlWhiteList = repoUrlWhiteList;
    }

    public boolean isEnableRepoUrlWhiteList() {
        return enableRepoUrlWhiteList;
    }

    public void setEnableRepoUrlWhiteList(boolean enableRepoUrlWhiteList) {
        this.enableRepoUrlWhiteList = enableRepoUrlWhiteList;
    }

    public boolean isEnableMemberAccessCheck() {
        return enableMemberAccessCheck;
    }

    public void setEnableMemberAccessCheck(boolean enableMemberAccessCheck) {
        this.enableMemberAccessCheck = enableMemberAccessCheck;
    }

    public int getIronmanTaskTimeout() {
        return ironmanTaskTimeout;
    }

    public void setIronmanTaskTimeout(int ironmanTaskTimeout) {
        this.ironmanTaskTimeout = ironmanTaskTimeout;
    }

    public String getIndexBuildScanAction() {
        return indexBuildScanAction;
    }

    public void setIndexBuildScanAction(String indexBuildScanAction) {
        this.indexBuildScanAction = indexBuildScanAction;
    }

    public String getWikiBuildScanAction() {
        return wikiBuildScanAction;
    }

    public void setWikiBuildScanAction(String wikiBuildScanAction) {
        this.wikiBuildScanAction = wikiBuildScanAction;
    }

    public boolean isSkipCalculateToken() {
        return skipCalculateToken;
    }

    public void setSkipCalculateToken(boolean skipCalculateToken) {
        this.skipCalculateToken = skipCalculateToken;
    }
}
