package com.alipay.codegencore.utils;

import com.alipay.codegencore.utils.dingding.DingDingUtil;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.utils
 * @CreateTime : 2023-04-21
 */
public class DingDingUtilTest extends AbstractTestBase {

    /**
     * 测试
     */
    @Test
    public void test_sendMessage() {
        String secret = "secret";
        String accessToken = "accessToken";
        String testSecret = "testSecret";
        String testAccessToken = "testAccessToken";
        DingDingUtil.init(secret, accessToken, testSecret, testAccessToken);
        String test = DingDingUtil.sendMessage("test", true);
        Assertions.assertTrue(test.contains("ok"));
    }
}
