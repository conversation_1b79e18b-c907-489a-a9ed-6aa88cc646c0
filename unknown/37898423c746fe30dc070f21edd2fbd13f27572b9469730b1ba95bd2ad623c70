package com.alipay.codegencore.model.openai;

/**
 * A chat completion generated by GPT-3.5
 */
public class ChatCompletionChoice {

    /**
     * This index of this completion in the returned list.
     */
    Integer index;

    /**
     * The message which was generated.
     */
    ChatMessage message;

    /**
     * The reason why GPT-3 stopped generating, for example "length".
     */
    String finishReason;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public ChatMessage getMessage() {
        return message;
    }

    public void setMessage(ChatMessage message) {
        this.message = message;
    }

    public String getFinishReason() {
        return finishReason;
    }

    public void setFinishReason(String finishReason) {
        this.finishReason = finishReason;
    }
}
