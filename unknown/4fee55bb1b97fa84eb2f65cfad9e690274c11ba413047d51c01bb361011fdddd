package com.alipay.codegencore.web.common;

import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.common.DocumentHandleService;
import com.alipay.codegencore.service.common.UserAclService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 配置管理controller
 *
 * <AUTHOR>
 * 创建时间 2022-02-28
 */
@RestController
@RequestMapping("/webapi/common")
@Slf4j
public class BaseController {

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;
    @Resource
    private DocumentHandleService documentHandleService;
    @Resource
    private UserAclService userAclService;

    /**
     * 是否域内域外服务
     *
     * @param
     * @return， appName
     */
    @GetMapping(value = "/isInternal")
    public BaseResponse<Boolean> isInternal() {
        return BaseResponse.build(codeGPTDrmConfig.isIntranetApplication());
    }

}
