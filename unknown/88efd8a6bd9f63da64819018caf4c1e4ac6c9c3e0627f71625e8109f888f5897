package com.alipay.codegencore.service.utils;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.model.CompletionsCodeModel;
import com.alipay.codegencore.model.request.CompletionsRequestBean;
import com.alipay.codegencore.model.request.tsingyan.CompletionRequestBean;
import com.alipay.codegencore.model.response.tsingyan.CompletionResultModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 异步日志
 *
 * <AUTHOR>
 * 创建时间 2022-03-16
 */
@Component
public class AsyncLogUtils {

    /**
     * 代码补全输入
     */
    private static final Logger CODEGEN_INPUT_LOGGER = LoggerFactory.getLogger("CODEGENINPUT");
    /**
     * 代码补全输出
     */
    private static final Logger TSINGYAN_LOGGER = LoggerFactory.getLogger("TSINGYANMONITOR");
    /**
     * codegpt输入埋点
     */
    private static final Logger GPT_INPUT = LoggerFactory.getLogger("GPTINPUT");
    /**
     * codegpt采纳埋点
     */
    private static final Logger GPT_ACCEPT = LoggerFactory.getLogger("GPTACCEPT");

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");



    /**
     * 记录补全 结果数据
     * 1。记录rt
     * 2。记录输入
     * 3。记录输出
     *
     * @param result
     * @param rtTime
     * @param completionsRequestBean
     * @param completionsCodeModel
     */
    public void logCompletions(String result, Long rtTime, CompletionsRequestBean completionsRequestBean, CompletionsCodeModel completionsCodeModel) {
        //防止odps报错，prompt不能为空
        completionsRequestBean.setPrompt("TEST");
        completionsRequestBean.setTempCodeAnalysisContext(null);
        CODEGEN_INPUT_LOGGER.info(AppConstants.LOG_CODE_COMPLETIONS_INFO, result, rtTime, JSONObject.toJSONString(completionsRequestBean), JSONObject.toJSONString(completionsCodeModel));
    }

    /**
     * 青燕内网埋点
     * @param result
     * @param rtTime
     * @param completionsCodeModel
     */
    public void logCompletionsWithTsingyan(String result, Long rtTime, CompletionRequestBean completionsRequestBean, CompletionResultModel completionsCodeModel) {
        //防止odps报错，prompt不能为空
        completionsRequestBean.setPrompt("TEST");
        CODEGEN_INPUT_LOGGER.info(AppConstants.LOG_CODE_COMPLETIONS_INFO, result, rtTime, JSONObject.toJSONString(completionsRequestBean), JSONObject.toJSONString(completionsCodeModel));
    }

    /**
     * 大模型请求埋点（临时用)
     * 请求体写日志时是base64编码，防止安全扫描
     * @param result
     * @param rtTime
     * @param completionsRequestBean
     * @param completionsCodeModel
     */
    public void logGPTInput(String result, Long rtTime, CompletionRequestBean completionsRequestBean, CompletionResultModel completionsCodeModel) {
        String requestBase64 = Base64Encoder.encode(JSONObject.toJSONString(completionsCodeModel));
        GPT_INPUT.info(AppConstants.LOG_CODE_COMPLETIONS_INFO, result, rtTime, requestBase64, JSONObject.toJSONString(completionsCodeModel));
    }


    /**
     * 大模型采纳埋点（临时用)
     * @param content
     */
    public void logGPTAcceptInfo(String content) {
        GPT_ACCEPT.info(content);
    }

    /**
     * 第三方调用耗时埋点日志
     * @param startContent 日志的开始内容
     * @param thirdPartyName 第三方平台名称
     * @param startTimeMillisecond 开始时间,单位毫秒
     * @param sync true=同步,false=异步.
     */
    public void logThirdPartyCallTime(String startContent, String thirdPartyName, Long startTimeMillisecond, boolean sync, String contentType, String sceneCode) {
        JSONObject logContent = new JSONObject(true);
        logContent.put("thirdPartyName", thirdPartyName);
        logContent.put("costTime", System.currentTimeMillis() - startTimeMillisecond);
        logContent.put("sync", sync);
        logContent.put("contentType", contentType);
        logContent.put("sceneCode", sceneCode);
        OTHERS_LOGGER.info("{},{}", startContent, logContent.toJSONString());
    }


}
