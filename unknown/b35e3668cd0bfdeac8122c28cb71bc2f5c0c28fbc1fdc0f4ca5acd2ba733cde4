package com.alipay.codegencore.model.enums;

/**
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.model.enums
 * @CreateTime : 2023-07-13
 */
public enum UserTypeEnum {
    /**
     * 正常用户
     */
    USER(0),

    /**
     * 管理员
     */
    ADMIN(1),

    /**
     * 超级管理员
     */
    SUPER_ADMIN(2);

    private int code;

    UserTypeEnum(int code) {
        this.code = code;
    }

    UserTypeEnum getByCode(int code) {
        for(UserTypeEnum e: UserTypeEnum.values()) {
            if(e.code == code) {
                return e;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }
}
