/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.handler;

import cn.hutool.extra.spring.SpringUtil;
import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.service.codegpt.LanguageModelService;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @version AntGLMModelHandler.java
 */
public class AntGLMModelHandler extends AbstractAlgLanguageHandler {

    private LanguageModelService antGLMModelService;

    /**
     * 抽象handler构造函数
     */
    public AntGLMModelHandler() {
        this.antGLMModelService = SpringUtil.getBean("antGLMLanguageModelService");
    }

    @Override
    public ChatMessage chat(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        return antGLMModelService.chat(gptAlgModelServiceRequest);
    }

    @Override
    public Flux<String> chatOnStream(GptAlgModelServiceRequest gptAlgModelServiceRequest) {
        antGLMModelService.streamChatForServlet(gptAlgModelServiceRequest);
        return null;
    }

}