package com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema;

import java.util.Map;

/**
 * API阶段输出
 */
public class ApiStageOutput {
    /**
     * 接口返回体
     */
    private Object responseBody;
    /**
     * 接口返回状态码
     */
    private Integer statusCode;
    /**
     * 接口报错信息
     */
    private Map<String, Object> errorMessage;

    public ApiStageOutput() {
    }

    /**
     * 全参数构造函数
     *
     * @param responseBody API响应体
     * @param statusCode   API状态码
     * @param errorMessage 错误信息
     */
    public ApiStageOutput(Object responseBody, Integer statusCode, Map<String, Object> errorMessage) {
        this.responseBody = responseBody;
        this.statusCode = statusCode;
        this.errorMessage = errorMessage;
    }


    public Object getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(Object responseBody) {
        this.responseBody = responseBody;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public Map<String, Object> getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(Map<String, Object> errorMessage) {
        this.errorMessage = errorMessage;
    }
}