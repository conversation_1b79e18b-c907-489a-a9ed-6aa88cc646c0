package com.alipay.codegencore.model.openai;

import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/25 17:28
 */
public class GenCodeRequest {

    private String sessionId;

    private String appName;

    private RepoInfo repoInfo;

    private GenRequirementInfo requirementInfo;

    private List<Plan> plans;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public RepoInfo getRepoInfo() {
        return repoInfo;
    }

    public void setRepoInfo(RepoInfo repoInfo) {
        this.repoInfo = repoInfo;
    }

    public GenRequirementInfo getRequirementInfo() {
        return requirementInfo;
    }

    public void setRequirementInfo(GenRequirementInfo requirementInfo) {
        this.requirementInfo = requirementInfo;
    }

    public List<Plan> getPlans() {
        return plans;
    }

    public void setPlans(List<Plan> plans) {
        this.plans = plans;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GenCodeRequest.class.getSimpleName() + "[", "]")
                .add("sessionId='" + sessionId + "'")
                .add("appName='" + appName + "'")
                .add("repoInfo=" + repoInfo)
                .add("requirementInfo=" + requirementInfo)
                .add("plans=" + plans)
                .toString();
    }
}
