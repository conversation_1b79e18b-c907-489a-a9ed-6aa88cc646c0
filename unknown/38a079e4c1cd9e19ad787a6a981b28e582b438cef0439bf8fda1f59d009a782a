package com.alipay.codegencore.model.model.codegpt;

/**
 * 每个流的model
 */
public class EveryStreamModel {

    private String content;
    private String finishReason;

    public EveryStreamModel() {
    }

    /**
     * 构造器设置内容
     * @param content
     */
    public EveryStreamModel(String content) {
        this.content = content;
    }

    /**
     * 初始化
     * @param content
     * @param finishReason
     */
    public EveryStreamModel(String content, String finishReason) {
        this.content = content;
        this.finishReason = finishReason;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFinishReason() {
        return finishReason;
    }

    public void setFinishReason(String finishReason) {
        this.finishReason = finishReason;
    }
}
