dbmode=test
# test-url activice(only dev)
run_mode=TEST
# buservice url
sofa.buservice.antbuserviceUrl=https://antbuservice.test.alipay.net

# zdal config
app_data_source_name=codegencore_ds
app_oss_data_source_name=codegencore_oss_ds
zdal_version=ERA10060602

# zcache config
zdal_console_url=http://zdataconsole-pool.${inner.domain}:8080
zdal_tail_name=codegencoreTBaseCache
tbase_time_out=5000

# ???? - java???
alg_generator_line=https://codegencore-pre.alipay.com/api/test/innertest
# ???? - python???
alg_generator_python=https://codegencore-pre.alipay.com/api/test/pytest
# ???? - js/ts???
alg_generator_js=https://codegencore-pre.alipay.com/api/test/jstest