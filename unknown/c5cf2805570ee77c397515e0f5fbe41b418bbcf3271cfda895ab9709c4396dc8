package com.alipay.codegencore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.common.logging.Logger;
import com.alibaba.common.logging.LoggerFactory;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.dal.example.UserPluginRecordsDOExample;
import com.alipay.codegencore.dal.mapper.UserPluginRecordsDOMapper;
import com.alipay.codegencore.dal.mapper.UserPluginRecordsManualMapper;
import com.alipay.codegencore.model.domain.PluginDO;
import com.alipay.codegencore.model.domain.UserAuthDO;
import com.alipay.codegencore.model.domain.UserPluginRecordsDO;
import com.alipay.codegencore.model.enums.ControlTypeEnum;
import com.alipay.codegencore.model.enums.ResponseEnum;
import com.alipay.codegencore.model.openai.UserPluginRecordsVO;
import com.alipay.codegencore.model.response.PageResponse;
import com.alipay.codegencore.service.codegpt.UserPluginRecordsService;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.UserAclService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.08.09
 */
@Service
public class UserPluginRecordsServiceImpl implements UserPluginRecordsService {
    private final Logger LOGGER = LoggerFactory.getLogger(UserPluginRecordsServiceImpl.class);
    @Resource
    private UserPluginRecordsDOMapper userPluginRecordsDOMapper;
    @Resource
    private UserPluginRecordsManualMapper userPluginRecordsManualMapper;
    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;
    @Resource
    private UserAclService userAclService;

    @Override
    public PageResponse<List<UserPluginRecordsVO>> getPluginControlInfo(Long pluginId, String query, ControlTypeEnum controlTypeEnum, int pageNo, int pageSize) {
        Integer controlType = controlTypeEnum == null ? null : controlTypeEnum.getCode();
        List<UserPluginRecordsVO> userPluginRecordsVOS = userPluginRecordsManualMapper.selectUser(pluginId, query, controlType,(pageNo - 1) * pageSize, pageSize);
        Long aLong = userPluginRecordsManualMapper.selectUserCount(pluginId, query, controlType);
        return PageResponse.build(ResponseEnum.SUCCESS, userPluginRecordsVOS, aLong);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void overridePluginUserControl(PluginDO pluginDO, ControlTypeEnum controlTypeEnum, List<String> empIdList) {
        UserPluginRecordsDOExample userPluginRecordsDOExample = new UserPluginRecordsDOExample();
        userPluginRecordsDOExample.createCriteria()
                .andDeletedEqualTo((byte)0)
                .andControlTypeEqualTo(controlTypeEnum.getCode())
                .andPluginIdEqualTo(pluginDO.getId());
        UserPluginRecordsDO userPluginRecordsDO = new UserPluginRecordsDO();
        userPluginRecordsDO.setDeleted((byte)1);
        userPluginRecordsDOMapper.updateByExampleSelective(userPluginRecordsDO, userPluginRecordsDOExample);
        if(CollectionUtils.isEmpty(empIdList)){
            return;
        }
        batchInsertUserScene(pluginDO, empIdList, controlTypeEnum);


    }

    @Override
    public UserPluginRecordsDO getUserPermissionInfo(Long userId, Long pluginId) {
        UserPluginRecordsDOExample userPluginRecordsDOExample = new UserPluginRecordsDOExample();
        userPluginRecordsDOExample.createCriteria()
                .andUserIdEqualTo(userId)
                .andPluginIdEqualTo(pluginId)
                .andDeletedEqualTo((byte)0);
        List<UserPluginRecordsDO> userPluginRecordsDOS = userPluginRecordsDOMapper.selectByExample(userPluginRecordsDOExample);
        return CollectionUtils.isEmpty(userPluginRecordsDOS) ? null : userPluginRecordsDOS.get(0);
    }

    @Override
    public List<String> batchInsertUserScene(PluginDO pluginDO, List<String> empIds, ControlTypeEnum controlTypeEnum) {
        if (CollectionUtil.isEmpty(empIds)) {
            return new ArrayList<>();
        }
        List<String> errorUserIds = codeFuseUserAuthService.insertUserAuth(empIds);
        empIds.removeAll(errorUserIds);
        List<UserAuthDO> userByEmpId = codeFuseUserAuthService.getUserByEmpIds(empIds);

        userByEmpId.forEach(userAuthDO -> {
            //助手创建人不需要添加权限
            if (userAuthDO.getId().longValue() == pluginDO.getUserId().longValue()){
                return;
            }
            UserPluginRecordsDOExample userPluginRecordsDOExample = new UserPluginRecordsDOExample();
            userPluginRecordsDOExample.createCriteria()
                    .andUserIdEqualTo(userAuthDO.getId()).
                    andPluginIdEqualTo(pluginDO.getId());
            List<UserPluginRecordsDO> userPluginRecordsDOS = userPluginRecordsDOMapper.selectByExample(userPluginRecordsDOExample);
            UserPluginRecordsDO userPluginRecordsDO = new UserPluginRecordsDO();
            if (CollectionUtil.isNotEmpty(userPluginRecordsDOS)) {
                LOGGER.warn("用户已经存在于该场景中，userName：" + userAuthDO.getUserName());
                UserPluginRecordsDO pluginRecordsDO = userPluginRecordsDOS.get(0);
                boolean needUpdate = false;
                // 已删除的话加回来,并且权限加成当前所设置权限
                if (pluginRecordsDO.getDeleted() == 1) {
                    userPluginRecordsDO.setDeleted((byte) 0);
                    userPluginRecordsDO.setControlType(controlTypeEnum.getCode());
                    needUpdate = true;
                }
                // 可编辑权限 > 可见权限
                if (pluginRecordsDO.getControlType() == ControlTypeEnum.SEE.getCode() &&
                        controlTypeEnum == ControlTypeEnum.UPDATE) {
                    userPluginRecordsDO.setControlType(ControlTypeEnum.UPDATE.getCode());
                    needUpdate = true;
                }
                if (needUpdate) {
                    userPluginRecordsDOMapper.updateByExampleSelective(userPluginRecordsDO, userPluginRecordsDOExample);
                }
                return;
            }

            userPluginRecordsDO.setPluginId(pluginDO.getId());
            userPluginRecordsDO.setUserId(userAuthDO.getId());
            userPluginRecordsDO.setControlType(controlTypeEnum.getCode());
           userPluginRecordsDOMapper.insertSelective(userPluginRecordsDO);
        });
        UserAuthDO currentUser = userAclService.getCurrentUser();
        LOGGER.info(String.format("操作人：%s 批量插入，场景ID：%d，用户ID：%s，插入失败id：%s", currentUser.getUserName(), pluginDO.getId(),
                JSONObject.toJSONString(empIds), JSONObject.toJSONString(errorUserIds)));
        return errorUserIds;
    }
}
