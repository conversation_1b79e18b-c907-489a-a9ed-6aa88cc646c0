package com.alipay.codegencore.utils.codescan;

import org.antlr.v4.runtime.tree.ErrorNode;

/**
 * 解析listener
 *
 * <AUTHOR>
 * 创建时间 2022-08-01
 */
public class SimpleJavaParserListener extends JavaParserBaseListener{
    /**
     * 自动生成
     * @param ctx the parse tree
     */
    @Override
    public void exitMethodBody(JavaParser.MethodBodyContext ctx) {
        super.exitMethodBody(ctx);
    }

    /**
     * 自动生成
     * @param ctx the parse tree
     */
    @Override
    public void exitVariableDeclarator(JavaParser.VariableDeclaratorContext ctx) {
        super.exitVariableDeclarator(ctx);
    }

    /**
     * 自动生成
     * @param ctx the parse tree
     */
    @Override
    public void exitFieldDeclaration(JavaParser.FieldDeclarationContext ctx) {
        super.exitFieldDeclaration(ctx);
    }

    /**
     * 自动生成
     * @param ctx the parse tree
     */
    @Override
    public void exitElementValue(JavaParser.ElementValueContext ctx) {
        super.exitElementValue(ctx);
    }

    /**
     * 自动生成
     * @param ctx the parse tree
     */
    @Override
    public void exitExpression(JavaParser.ExpressionContext ctx) {
        super.exitExpression(ctx);
    }

    /**
     * 自动生成
     * @param ctx the parse tree
     */
    @Override
    public void exitLocalVariableDeclaration(JavaParser.LocalVariableDeclarationContext ctx) {
        super.exitLocalVariableDeclaration(ctx);
    }


    /**
     * 自动生成
     * @param node
     */
    @Override
    public void visitErrorNode(ErrorNode node) {
        super.visitErrorNode(node);
    }
}
