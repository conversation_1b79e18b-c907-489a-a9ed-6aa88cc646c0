package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.06.24
 */
public enum WebCommonResultCode implements ResultCode{
    /** 成功**/
    SUCCESS("SUCCESS", "处理成功"),
    /** 成功**/
    API_SUCCESS("API_SUCCESS", "API请求处理成功"),
    /** 非法参数**/
    INVALID_PARAMS("INVALID_PARAMS", "非法参数"),
    /** 系统繁忙，请稍后再试 */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统繁忙，请稍后再试"),
    SYSTEM_UPGRADE_NOT_ALLOW_OPERATION("SYSTEM_UPGRADE_NOT_ALLOW_OPERATION", "该功能暂时禁止，升级完毕后开放"),
    NO_VIEW_PERMISSION("NO_VIEW_PERMISSION", "无查看权限"),
    NO_UPDATE_PERMISSION("NO_UPDATE_PERMISSION", "无修改权限"),
    NO_PERMISSION("NO_PERMISSION", "您无权限操作"),
    OUT_SCENE_NO_PERMISSION("OUT_SCENE_NO_PERMISSION", "您无权限操作"),
    AUTH_INFO_TIMEOUT("AUTH_INFO_TIMEOUT", "认证信息过期"),
    AUTH_INFO_VERIFY_FAILED("AUTH_INFO_VERIFY_FAILED", "认证信息验签不通过"),
    FORM_TO_MODEL_CONVERTER_NOT_FOUND("FORM_TO_MODEL_CONVERTER_NOT_FOUND", "表单到业务模型转换器未创建"),
    FORM_TO_MODEL_NEW_INSTANCE_FAILED("FORM_TO_MODEL_NEW_INSTANCE_FAILED", "表单转换到业务模型时实例化VO失败"),
    MODEL_TO_VO_CONVERTER_NOT_FOUND("MODEL_TO_VO_CONVERTER_NOT_FOUND", "业务模型到VO转换器未创建"),
    MODEL_TO_VO_NEW_INSTANCE_FAILED("MODEL_TO_VO_NEW_INSTANCE_FAILED", "业务模型转换到VO时实例化VO失败"),
    FAILED_TO_DOWNLOAD_FILE("FAILED_TO_DOWNLOAD_FILE", "下载文件失败：%s"),
    FAILED_TO_UPLOAD_FILE("FAILED_TO_UPLOAD_FILE", "上传文件失败：%s"),
    NEW_MODEL_FAILED("NEW_MODEL_FAILED", "实例化模型失败：%s"),
    FAILED_TO_READ_XLSX_FILE("FAILED_TO_READ_XLSX_FILE", "读取xlsx文件失败：%s"),
    WORKNOS_EXCEED_MAX_LIMIT_NUM_ONE_THOUSAND("WORKNOS_EXCEED_MAX_LIMIT_NUM_ONE_THOUSAND", "工号超出最大限制数量1000"),
    UPDATE_ROOM_SCHEDULE_FAILED("UPDATE_ROOM_SCHEDULE_FAILED","修改失败，可以联系LinkS开发同学解决"),
    UN_AUTHORIZED("UN_AUTHORIZED", "UnauthorizedException"),
    NO_ROOM_PERMISSION("NO_ROOM_PERMISSION", "无租户权限"),
    DELETE_ROOM_FAILED("DELETE_ROOM_FAILED","删除租户失败"),


    CONVERSATION_STATUS_INVALID("CONVERSATION_STATUS_INVALID","当前会话状态不正确，请检查后重试"),
    NOT_PRESENT_SUPPORTER("NOT_PRESENT_SUPPORTER","您不是当前会话的责任人，请检查后重试"),
    NOT_ALLOW_DELETE_FROM_SUPPORTERS("NOT_ALLOW_DELETE_FROM_SUPPORTERS","不可以将自己移除支持者列表"),
    NOT_LOGON_OR_TIMEOUT("NOT_LOGON_OR_TIMEOUT", "未登录或登录超时"),
    REPEAT_SNIPPET_CATEGORY("REPEAT_SNIPPET_CATEGORY","知识库类目已存在"),

    DING_BUG_BLOCK_CONVERSATION("DING_BUG_BLOCK_CONVERSATION", "提交咨询表单报错，可能是钉钉客户端问题，如是，处理办法：钉钉偏好设置/高级/Web内核，选择【默认】或【Chromium】，如问题依然存在请联系【口合】处理，详见https://yuque.antfin-inc.com/docs/share/0b56f3f1-25e7-4a7e-9e77-1cf59d4cd9e7#iO8WJ"),
    LINK_NO_PERMISSION_EXPIRED("LINK_NO_PERMISSION_EXPIRED", "您无权限访问该链接或该链接已经过期"),

    OPERATOR_USER_NOT_EXISTED("OPERATOR_USER_NOT_EXISTED", "操作用户不存在"),
    ASSIGN_USER_NOT_EXISTED("ASSIGN_USER_NOT_EXISTED", "要转派给的用户不存在"),

    HAS_ALREADY_BIND_ALIPAY("HAS_ALREADY_BIND_ALIPAY", "您已经绑定支付宝账号，无需重复绑定"),
    CURRENT_USER_NOT_ALIPAY("CURRENT_USER_NOT_ALIPAY", "您当前登录账号不是支付宝账号，无法绑定支付宝账号"),
    HAS_BIND_SCORE_TASK("HAS_BIND_SCORE_TASK","存在已绑定的投放任务,不允许删除"),
    INVALID_PARAMS_MESSAGE("INVALID_PARAMS_MESSAGE", "非法参数: %s"),

    NOT_GET_ATECHS_URL("NOT_GET_ATECHS_URL","未获取到工具链接"),
    BUSINESS_IN_PROCESS("BUSINESS_IN_PROCESS","gpt业务处理中，请稍后"),
    MESSAGE_IS_USED("MESSAGE_IS_USED","GPT消息已被发送，请刷新页面"),
            ;

    /** 结果码 */
    private String code;
    /** 结果消息 */
    private String message;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    /**
     * 构造函数
     *
     * @param code 结果码
     * @param message 结果消息
     */
    private WebCommonResultCode(String code, String message) {
        this.code = this.getClass().getSimpleName() + "#" + code;
        this.message = message;
    }

    @Override
    public boolean isSuccessCode() {
        return SUCCESS.getCode().equalsIgnoreCase(this.getCode()) || API_SUCCESS.getCode().equalsIgnoreCase(this.getCode());
    }
}
