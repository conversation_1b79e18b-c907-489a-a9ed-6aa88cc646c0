package com.alipay.codegencore.model.enums;

import com.alipay.codegencore.model.exception.BizException;

/**
 * 权限范围
 */
public enum AllowAccessTypeEnum {

    WEB(1),
    IDE(2),
    ALL(10),
    ;

    private Integer value;

    AllowAccessTypeEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    /**
     * 根据value获取枚举
     * @param value
     * @return
     */
    public static AllowAccessTypeEnum getEnumByValue(Integer value){
        for (AllowAccessTypeEnum item : AllowAccessTypeEnum.values()) {
            if(item.getValue().equals(value)){
                return item;
            }
        }
        throw new BizException(ResponseEnum.ILLEGAL_PARAMETER, "Invalid value: " + value);
    }
}
