package com.alipay.codegencore.web.remote.vo;

import com.alipay.codegencore.model.remote.MessageObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0 2024/6/11 16:46
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskRequestObject {

    @JsonProperty(value = "agent_id", required = true)
    private String agentId;

    @JsonProperty(value = "messages", required = true)
    private List<MessageObject> messages;

    @JsonProperty(value = "session")
    private SessionCreateRequest session;

    @JsonProperty(value = "metadata")
    private Map<String, Object> metadata;

    @JsonProperty(value = "cancel")
    private Boolean cancel;

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public List<MessageObject> getMessages() {
        return messages;
    }

    public void setMessages(List<MessageObject> messages) {
        this.messages = messages;
    }

    public SessionCreateRequest getSession() {
        return session;
    }

    public void setSession(SessionCreateRequest session) {
        this.session = session;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public Boolean getCancel() {
        return cancel;
    }

    public void setCancel(Boolean cancel) {
        this.cancel = cancel;
    }

}
