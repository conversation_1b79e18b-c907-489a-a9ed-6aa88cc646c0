/*
 * This file was automatically generated by SmartUnit
 */

package smartunit.com.alipay.codegencore.utils;

import com.alipay.codegencore.model.AppConstants;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.smartunit.runtime.SmartRunner;
import org.smartunit.runtime.SmartRunnerParameters;

import static org.junit.Assert.assertEquals;

@RunWith(SmartRunner.class) @SmartRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true) 
public class AppConstants_SSTest extends AppConstants_SSTest_scaffolding {
// allCoveredLines:[14]

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      //caseID:983de3c7cdd10b37ef838388072a7304
      //CoveredLines: [14]
      
      AppConstants appConstants0 = new AppConstants();
      
      //Test Result Assert
      assertEquals(259200, AppConstants.CACHE_CODE_TEMPLATE_DATA_TIME_OUT);
  }
}
