package com.alipay.codegencore.model.request.aci;

import java.util.List;

/**
 * 流水线触发请求
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.05.15
 */
public class ACIPipelineExecuteRequest {
    /**
     * 流水线模板ID
     */
    private String pipelineTemplateId;
    /**
     * ACI项目id
     */
    private String projectId;
    /**
     * 目标分支
     */
    private String branch;
    /**
     * 流水线yml文件路径
     */
    private String ymlPath;

    /**
     * 流水线yml配置
     */
    private String ymlString;
    /**
     * 流水线参数
     */
    private List<ACIPipelineParameter> parameters;

    public String getPipelineTemplateId() {
        return pipelineTemplateId;
    }

    public void setPipelineTemplateId(String pipelineTemplateId) {
        this.pipelineTemplateId = pipelineTemplateId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getYmlPath() {
        return ymlPath;
    }

    public void setYmlPath(String ymlPath) {
        this.ymlPath = ymlPath;
    }

    public String getYmlString() {
        return ymlString;
    }

    public void setYmlString(String ymlString) {
        this.ymlString = ymlString;
    }

    public List<ACIPipelineParameter> getParameters() {
        return parameters;
    }

    public void setParameters(List<ACIPipelineParameter> parameters) {
        this.parameters = parameters;
    }
}
