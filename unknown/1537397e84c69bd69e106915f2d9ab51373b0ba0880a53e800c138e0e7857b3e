package com.alipay.codegencore.model.model;

/**
 * 本地模型配置
 *
 * <AUTHOR>
 * 创建时间 2023-02-06
 */
public class LocalGptConfigModel {
    /**
     * top得分阈值
     */
    private double topScoreThreshold = 0.9;
    /**
     * 无效得分阈值
     */
    private double invalidScoreThreshold = 0.1;

    public double getTopScoreThreshold() {
        return topScoreThreshold;
    }

    public void setTopScoreThreshold(double topScoreThreshold) {
        this.topScoreThreshold = topScoreThreshold;
    }

    public double getInvalidScoreThreshold() {
        return invalidScoreThreshold;
    }

    public void setInvalidScoreThreshold(double invalidScoreThreshold) {
        this.invalidScoreThreshold = invalidScoreThreshold;
    }
}
