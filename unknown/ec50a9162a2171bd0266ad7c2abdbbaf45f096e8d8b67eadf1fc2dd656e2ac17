package com.alipay.codegencore.model.copilot;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 代码符号信息类
 * 用于表示代码中的符号（变量、类、方法等）及其位置信息
 */
public class CodeSymbol {
    /**
     * 容器名称，通常是符号所在的上下文
     * 例如: "/^    EXTENDS = \"extends\"$/"
     */
    @JSONField(name = "container_name")
    private String containerName;

    /**
     * 符号类型
     * 可能的值: VARIABLE, CLASS, FIELD 等
     */
    private String kind;

    /**
     * 编程语言
     * 例如: "python", "java" 等
     */
    private String language;

    /**
     * 符号在代码中的位置信息
     */
    private Location location;

    /**
     * 符号名称
     */
    private String name;

    /**
     * 符号的URL链接
     */
    private String url;


    public String getContainerName() {
        return containerName;
    }

    public void setContainerName(String containerName) {
        this.containerName = containerName;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 位置信息类
     * 包含符号在代码中的具体位置信息
     */
    public static class Location {
        /**
         * 符号的范围信息
         */
        private Range range;

        /**
         * 资源信息，包含文件路径等
         */
        private Resource resource;

        public Range getRange() {
            return range;
        }

        public void setRange(Range range) {
            this.range = range;
        }

        public Resource getResource() {
            return resource;
        }

        public void setResource(Resource resource) {
            this.resource = resource;
        }
    }

    /**
     * 范围信息类
     * 表示符号在文件中的开始和结束位置
     */
    public static class Range {
        /**
         * 开始位置
         */
        private Position start;

        /**
         * 结束位置
         */
        private Position end;

        public Position getStart() {
            return start;
        }

        public void setStart(Position start) {
            this.start = start;
        }

        public Position getEnd() {
            return end;
        }

        public void setEnd(Position end) {
            this.end = end;
        }
    }

    /**
     * 位置类
     * 表示具体的行号和字符位置
     */
    public static class Position {
        /**
         * 字符位置（列号）
         */
        private int character;

        /**
         * 行号
         */
        private int line;

        public int getCharacter() {
            return character;
        }

        public void setCharacter(int character) {
            this.character = character;
        }

        public int getLine() {
            return line;
        }

        public void setLine(int line) {
            this.line = line;
        }
    }

    /**
     * 资源类
     * 包含文件路径信息
     */
    public static class Resource {
        /**
         * 文件路径
         */
        private String path;

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }
    }
}