package com.alipay.codegencore.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 用户状态枚举
 *
 * <AUTHOR>
 * 创建时间 2023-03-30
 */
public enum UserStatusEnum {
    /**
     * 待审核
     */
    VERIFY(1),
    /**
     * 正常用户
     */
    ACTIVE(2),
    /**
     * 异常用户
     */
    UNACTIVE(10),

    /**
     * 未知状态
     */
    UNKNOWN(99);

    private int value;

    UserStatusEnum(int value) {
        this.value = value;
    }

    @JsonValue
    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    /**
     * 通过value，将int转化为{@link UserStatusEnum}
     *
     * @param value
     * @return
     */
    public static UserStatusEnum getUserStatusEnumByValue(int value) {
        for (UserStatusEnum userStatusEnum : UserStatusEnum.values()) {
            if (userStatusEnum.getValue() == value) {
                return userStatusEnum;
            }
        }
        return UNKNOWN;
    }
}
