package com.alipay.codegencore.service.impl.answer;


import com.alipay.codegencore.dal.mapper.AnswerIndexBuildJobMapper;
import com.alipay.codegencore.dal.mapper.AnswerIndexBuildTaskMapper;
import com.alipay.codegencore.model.domain.AnswerIndexBuildTaskDO;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.service.middle.tbase.TbaseCacheService;
import com.alipay.codegencore.utils.code.AntCodeClient;
import org.junit.Before;
import org.junit.Test;
import org.smartunit.runtime.PrivateAccess;

import java.sql.Timestamp;

import static org.smartunit.shaded.org.mockito.Mockito.*;

public class AnswerIndexServiceImplTest {

    private AnswerIndexServiceImpl answerIndexService;


    private TbaseCacheService tbaseCacheService;

    private AnswerIndexBuildTaskMapper answerIndexBuildTaskMapper;

    private ConfigService configService;

    private AnswerIndexBuildJobMapper answerIndexBuildJobMapper;


    @Before
    public void setUp() {

        answerIndexService = new AnswerIndexServiceImpl();

        tbaseCacheService = mock(TbaseCacheService.class);
        PrivateAccess.setVariable(AnswerIndexServiceImpl.class, answerIndexService, "tbaseCacheService", tbaseCacheService);

        answerIndexBuildTaskMapper = mock(AnswerIndexBuildTaskMapper.class);
        PrivateAccess.setVariable(AnswerIndexServiceImpl.class, answerIndexService, "answerIndexBuildTaskMapper", answerIndexBuildTaskMapper);

        configService = mock(ConfigService.class);
        PrivateAccess.setVariable(AnswerIndexServiceImpl.class, answerIndexService, "configService", configService);

        answerIndexBuildJobMapper = mock(AnswerIndexBuildJobMapper.class);
        PrivateAccess.setVariable(AnswerIndexServiceImpl.class, answerIndexService, "answerIndexBuildJobMapper", answerIndexBuildJobMapper);

    }

    @Test
    public void testIndexBuild() throws InterruptedException {

//        AnswerIndexServiceImpl answerIndexService = new AnswerIndexServiceImpl();

        AntCodeClient.init("https://code.alipay.com", "BTEZPnew3y7AaFif9Vpe");

        doReturn(true).when(tbaseCacheService).getLock(anyString(), anyInt());


        doReturn(null).when(answerIndexBuildTaskMapper).getByRepoInfo(anyString(), anyString(), anyString());

        doNothing().when(answerIndexBuildTaskMapper).insertTask(any(AnswerIndexBuildTaskDO.class));

        String repoStr = ".*opensource_analyze/.*";
        String configStr = "[\".*target/.*\",\".*.github/.*\",\".*.git/.*\",\".*/smartunit/.*\"]";
        doReturn(repoStr, configStr).when(configService).getConfigByKey(anyString(), anyBoolean());

        doReturn(10).when(answerIndexBuildJobMapper).batchInsertJob(anyList());

        doReturn(1).when(answerIndexBuildTaskMapper).updateTaskBuilding(anyLong(), anyString(), anyString(), anyLong(), any(Timestamp.class));

        answerIndexService.indexBuild(new AntCodeClient.CommitInfo(),
                "https://code.alipay.com/opensource_analyze/redis__jedis.git",1);

        Thread.currentThread().join(30000);
    }

    public void testPullJobs() {
    }

    public void testJobResult() {
    }

    public void testRepoBuildState() {
    }

    public void testRepoChangeHandle() {
    }
}