package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.service.impl.model.CodeGptLanguageModelServiceImpl;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/* 自定义健康检查类
* */
@Component
public class HealthIndicatorService implements HealthIndicator {

    @Resource
    private CodeGptLanguageModelServiceImpl codeGptLanguageModelService;

    @Override
    public Health health() {
        List<String> errorInfoList = check(); // perform some specific health check
        if (errorInfoList.size() != 0) {

            String totalMessage = "error info:" + JSON.toJSONString(errorInfoList);
            return Health.down()
                    .withDetail("Error Code", totalMessage)
                    .build();
        }
        return Health.up().build();
    }

    public List<String> check() {
        // 自定义健康检查逻辑，检查各个类，并且加入错误信息
        List<String> errorInfoList = new ArrayList<>();

        //检查服务
        if(!codeGptLanguageModelService.isServiceOk()){
            errorInfoList.add("codeGptLanguageModelService down");
        }

        return errorInfoList;
    }
}
