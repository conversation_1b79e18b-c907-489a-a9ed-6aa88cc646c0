package com.alipay.codegencore.service.common;

import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.model.CheckResultModel;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.model.openai.ChatMessage;
import com.alipay.codegencore.model.openai.ChatRequestExtData;

import java.util.List;

/**
 * 获取审核结果的service,封装了多平台的审核
 */
public interface CheckService {

    /**
     * 获取一个长文本的审核结果,内部封装了拆分逻辑
     *
     * @param messageUid   消息uid
     * @param longContent  长文本
     * @param extData      ChatCompletionRequest 对象的属性
     * @param questionUid  问题的uid
     * @return 多平台审核结果
     */
    CheckResultModel getAnswerCheckResultLongContent(String messageUid, List<ChatMessage> chatMessageList, String longContent, ChatRequestExtData extData, String questionUid, boolean externalModel);

    /**
     * 获取问题的审核结果
     * @param questionUid 问题的uid
     * @param chatMessageList 审核内容
     * @param extData 扩展字段
     * @param externalModel true代表是外部模型，false代表是内部模型
     * @return 结果
     */
    CheckResultModel getQuestionCheckResult(String questionUid, List<ChatMessage> chatMessageList, ChatRequestExtData extData, boolean externalModel);

    /**
     * 获取 prompt 的审核结果
     * @param questionUid 问题的uid
     * @param prompt 审核内容
     * @param extData 扩展字段
     * @param externalModel true代表是外部模型，false代表是内部模型
     * @return 结果
     */
    CheckResultModel getQuestionCheckResult(String questionUid, String prompt, ChatRequestExtData extData, boolean externalModel);

    /**
     * 获取答案的审核结果
     * @param questionUid 问题的uid
     * @param answerUid 答案的uid
     * @param batch 批次号
     * @param content 内容
     * @param extData 扩展字段
     * @return 结果
     */
    CheckResultModel getAnswerCheckResult(String questionUid, String answerUid, Integer batch, List<ChatMessage> chatMessageList, String content, String completeAnswer, ChatRequestExtData extData, boolean externalModel);

    /**
     * 对一个文本进行infoSec的内容审核
     * @param content 文本
     * @param chatRoleEnum 问题/答案
     * @param batch 批次
     * @param extData 扩展字段
     * @param questionUid 问题ID
     * @param messageUid 答案ID
     * @return 审核结果
     */
    ReviewResultModel getInfoSecCheckRet(String content, ChatRoleEnum chatRoleEnum, Integer batch, ChatRequestExtData extData, String questionUid, String messageUid);
}
