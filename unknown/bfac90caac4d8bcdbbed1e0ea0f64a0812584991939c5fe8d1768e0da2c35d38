package com.alipay.codegencore.utils.embedding;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.model.EmbeddingResponseModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 给定两个向量，计算它们之间的余弦相似度。
 */
public class VectorCalculate {

    private static final Logger LOGGER = LoggerFactory.getLogger(VectorCalculate.class);

    /**
     * 计算两个向量之间的余弦相似度。
     *
     * @param vector1 第一个向量
     * @param vector2 第二个向量
     * @return 余弦相似度值
     */
    public static BigDecimal calculateCosineSimilarity(List<BigDecimal> vector1, List<BigDecimal> vector2) {
        if (vector1.size() != vector2.size()) {
            throw new IllegalArgumentException("Vector sizes do not match");
        }

        BigDecimal dotProduct = BigDecimal.ZERO;
        BigDecimal magnitude1 = BigDecimal.ZERO;
        BigDecimal magnitude2 = BigDecimal.ZERO;

        // 计算点积和长度
        for (int i = 0; i < vector1.size(); i++) {
            BigDecimal value1 = vector1.get(i);
            BigDecimal value2 = vector2.get(i);

            dotProduct = dotProduct.add(value1.multiply(value2));

            magnitude1 = magnitude1.add(value1.pow(2));
            magnitude2 = magnitude2.add(value2.pow(2));
        }

        // 标准化向量长度
        magnitude1 = BigDecimal.valueOf(Math.sqrt(magnitude1.doubleValue())).setScale(2, RoundingMode.HALF_UP);
        magnitude2 = BigDecimal.valueOf(Math.sqrt(magnitude2.doubleValue())).setScale(2, RoundingMode.HALF_UP);

        // 如果向量长度为零，则抛出异常
        if (magnitude1.compareTo(BigDecimal.ZERO) == 0 || magnitude2.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("One of the vectors has zero magnitude");
        }

        // 计算相似度并返回结果
        return dotProduct.divide(magnitude1.multiply(magnitude2), 2, RoundingMode.HALF_UP);
    }


    /**
     * 计算查询向量与多个文档向量之间的余弦相似度。
     *
     * @param queryEmbeddingList         查询向量列表
     * @param embeddingResponseModelList 文档向量列表
     * @param topNSimilarity             返回前n条相似度最高的文档向量
     * @return 相似度排名靠前的文档向量列表
     */
    public static List<EmbeddingResponseModel> calculateCosineSimilarityTopN(List<BigDecimal> queryEmbeddingList, List<EmbeddingResponseModel> embeddingResponseModelList, Integer topNSimilarity) {
            // 存储每个文档向量的相似度及其对应的文档向量对象
            Map<BigDecimal, EmbeddingResponseModel> resultMap = new HashMap<>();
            for (EmbeddingResponseModel embedding : embeddingResponseModelList) {
                if (queryEmbeddingList.size() != embedding.getOriginalEmbeddingList().size()) {
                    LOGGER.warn("queryEmbeddingList:{},embeddingResponseModelList:{}", JSON.toJSONString(queryEmbeddingList), JSON.toJSONString(embeddingResponseModelList));
                }
                BigDecimal similarity = calculateCosineSimilarity(queryEmbeddingList, embedding.getOriginalEmbeddingList());
                embedding.setOriginalSimilarity(similarity);
                resultMap.put(similarity, embedding);
            }
            Map<BigDecimal, EmbeddingResponseModel> sortedMap = resultMap.entrySet().stream().sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
            return sortedMap.entrySet().stream()
                    .limit(topNSimilarity)
                    .map(Map.Entry::getValue)
                    .collect(Collectors.toList());
    }
}

