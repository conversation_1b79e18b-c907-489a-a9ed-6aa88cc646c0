package com.alipay.codegencore.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.enums.ChatRoleEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.model.ReviewResultModel;
import com.alipay.codegencore.service.common.RcsmartCheckService;
import com.alipay.codegencore.service.middle.drm.CodeGPTDrmConfig;
import com.alipay.codegencore.service.utils.AsyncLogUtils;
import com.alipay.codegencore.service.utils.ShortUid;
import com.alipay.rcsmart.common.facade.SyncApprovalFacade;
import com.alipay.rcsmart.common.request.sync.SyncApprovalContent;
import com.alipay.rcsmart.common.request.sync.SyncApprovalReq;
import com.alipay.rcsmart.facade.constants.enums.ResultCodeEnum;
import com.alipay.rcsmart.facade.request.AppInfo;
import com.alipay.rcsmart.facade.result.RcSmartResponse;
import com.alipay.rcsmart.facade.result.sync.SyncApprovalResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 解语花合规审查平台实现类
 */
@Service
public class RcsmartCheckServiceImpl implements RcsmartCheckService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RcsmartCheckService.class);

    private static final Logger OTHERS_LOGGER = LoggerFactory.getLogger("OTHERSINFO");

    private static final Logger CHAT_LOGGER = LoggerFactory.getLogger("CHATCOMPLETION");


    @Resource
    private AsyncLogUtils asyncLogUtils;
    @Resource
    private SyncApprovalFacade syncApprovalFacade;

    @Resource
    private CodeGPTDrmConfig codeGPTDrmConfig;

    @Override
    public ReviewResultModel rcsmartCheck(String messageUid,int batch,String text, ChatRoleEnum chatRoleEnum) {
        ReviewResultModel resultModel = new ReviewResultModel();
        resultModel.setRet(true);
        JSONObject rcsmartConfigJson = JSON.parseObject(codeGPTDrmConfig.getRcsmartConfig());
        if (AppConstants.OFF.equals(rcsmartConfigJson.getString("switch"))) {
            OTHERS_LOGGER.info("解语花合规平台审核开关已经关闭,不进行合规审核");
            resultModel.setCode("NO_REVIEW");
            return resultModel;
        }
        AppInfo appInfo = new AppInfo();
        appInfo.setAppName(rcsmartConfigJson.getString("appName"));
        appInfo.setToken(rcsmartConfigJson.getString("token"));
        SyncApprovalReq syncApprovalReq = new SyncApprovalReq();
        // 请求id，用于双方追踪请求
        String requestId = ShortUid.getUid();
        syncApprovalReq.setRequestId(requestId);
        syncApprovalReq.setSceneCode(rcsmartConfigJson.getString("sceneCode"));
        syncApprovalReq.setBizId(rcsmartConfigJson.getString("bizId"));
        SyncApprovalContent syncApprovalContent = new SyncApprovalContent();
        List<Map<String, Object>> contentData = new ArrayList<>();
        Map<String, Object> contentMap = new HashMap<>();
        Map<String, Object> textMap = new HashMap<>();
        if (ChatRoleEnum.USER == chatRoleEnum) {
            textMap.put("question", text);
        } else {
            textMap.put("answer", text);
        }
        contentMap.put("role", chatRoleEnum.getName());
        contentMap.put("content", textMap);
        contentData.add(contentMap);
        syncApprovalContent.setContentData(contentData);
        // 外部传入文件ID，用于标识素材唯一性
        String outContentId = messageUid+"_"+batch;
        syncApprovalContent.setOutContentId(messageUid+"_"+batch);
        syncApprovalReq.setSyncApprovalContent(syncApprovalContent);
        try {
            OTHERS_LOGGER.info("解语花合规审查,参数:requestId:{},outContentId:{},appInfo:{},syncApprovalReq:{}", requestId, outContentId, JSON.toJSONString(appInfo), JSON.toJSONString(syncApprovalReq));
            long startTime = System.currentTimeMillis();
            RcSmartResponse<SyncApprovalResult> smartResponse = syncApprovalFacade.syncApproval(appInfo, syncApprovalReq);
            asyncLogUtils.logThirdPartyCallTime(AppConstants.SAFETY_LOG_PREFIX, ReviewPlatformEnum.RCSMART.name(), startTime, true,chatRoleEnum.getName(),syncApprovalReq.getSceneCode());
            OTHERS_LOGGER.info("解语花合规审查,结果:requestId:{},outContentId:{},smartResponse:{}", requestId, outContentId, JSON.toJSONString(smartResponse));
            if (!smartResponse.isSuccess()) {
                CHAT_LOGGER.info("解语花合规审查,请求处理失败,requestId:{},outContentId:{},text:{},smartResponse:{}", requestId, outContentId, text, JSON.toJSONString(smartResponse));
                LOGGER.warn("解语花合规审查,请求处理失败,requestId:{},outContentId:{},smartResponse:{}", requestId, outContentId, JSON.toJSONString(smartResponse));
                return resultModel;
            }
            if (ResultCodeEnum.SUCCESS.getCode().equals(smartResponse.getResultCode().getCode())) {
                SyncApprovalResult syncApprovalResult = smartResponse.getData();
                // syncApprovalResult.result 值 true:不合规阻断,false:合规
                resultModel.setRet(!syncApprovalResult.getResult());
            } else {
                CHAT_LOGGER.info("解语花合规审查,状态码非成功,requestId:{},outContentId:{},text:{},smartResponse:{}", requestId, outContentId, text, JSON.toJSONString(smartResponse));
                LOGGER.warn("解语花合规审查,状态码非成功,requestId:{},outContentId:{},smartResponse:{}", requestId, outContentId, JSON.toJSONString(smartResponse));
            }
        } catch (Exception e) {
            CHAT_LOGGER.info("解语花合规审查,未知异常,requestId:{},outContentId:{},text:{}", requestId, outContentId, text);
            LOGGER.error("解语花合规审查,未知异常,requestId:"+requestId+",outContentId:"+outContentId, e);
        }
        return resultModel;
    }


}
