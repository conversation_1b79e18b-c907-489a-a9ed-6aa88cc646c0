package com.alipay.codegencore.model.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;

/**
 * 问答索引构建do
 */
public class AnswerIndexBuildTaskDO {

    private Long id;

    private String repoUrl;

    private String groupPath;

    private String projectPath;

    private String branch;

    private String buildScopeType;

    private String state;

    private String lastBuildCommit;

    private Timestamp lastBuildStartTime;

    private Timestamp lastBuildEndTime;

    private String summaryStatus;

    private String question;

    private Timestamp gmtCreate;

    private Timestamp gmtModified;

    private Integer priority;

    private Long taskRecordId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRepoUrl() {
        return repoUrl;
    }

    public void setRepoUrl(String repoUrl) {
        this.repoUrl = repoUrl;
    }

    public String getGroupPath() {
        return groupPath;
    }

    public void setGroupPath(String groupPath) {
        this.groupPath = groupPath;
    }

    public String getProjectPath() {
        return projectPath;
    }

    public void setProjectPath(String projectPath) {
        this.projectPath = projectPath;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getLastBuildCommit() {
        return lastBuildCommit;
    }

    public void setLastBuildCommit(String lastBuildCommit) {
        this.lastBuildCommit = lastBuildCommit;
    }

    public Timestamp getLastBuildStartTime() {
        return lastBuildStartTime;
    }

    public void setLastBuildStartTime(Timestamp lastBuildStartTime) {
        this.lastBuildStartTime = lastBuildStartTime;
    }

    public Timestamp getLastBuildEndTime() {
        return lastBuildEndTime;
    }

    public void setLastBuildEndTime(Timestamp lastBuildEndTime) {
        this.lastBuildEndTime = lastBuildEndTime;
    }

    public Timestamp getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Timestamp gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Timestamp getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Timestamp gmtModified) {
        this.gmtModified = gmtModified;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public String getSummaryStatus() {
        return summaryStatus;
    }

    public void setSummaryStatus(String summaryStatus) {
        this.summaryStatus = summaryStatus;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getBuildScopeType() {
        return buildScopeType;
    }

    public void setBuildScopeType(String buildScopeType) {
        this.buildScopeType = buildScopeType;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Long getTaskRecordId() {
        return taskRecordId;
    }

    public void setTaskRecordId(Long taskRecordId) {
        this.taskRecordId = taskRecordId;
    }

}
