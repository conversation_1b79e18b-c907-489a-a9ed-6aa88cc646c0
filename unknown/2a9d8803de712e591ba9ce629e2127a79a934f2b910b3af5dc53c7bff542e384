package com.alipay.codegencore.model.model.tool.learning;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.openai.ChatFunction;
import com.alipay.codegencore.model.openai.ChatMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @version : RuntimeInfo.java, v 0.1 2023年11月02日 11:51 baoping Exp $
 */
public class RuntimeInfo {
    Integer pluginCallIndex;

    List<ChatFunction> chatFunctionList;

    List<ChatMessage> chatMessageList;

    JSONObject repoChatInfo;

    public List<ChatFunction> getChatFunctionList() {
        return chatFunctionList;
    }

    public void setChatFunctionList(List<ChatFunction> chatFunctionList) {
        this.chatFunctionList = chatFunctionList;
    }

    public List<ChatMessage> getChatMessageList() {
        return chatMessageList;
    }

    public void setChatMessageList(List<ChatMessage> chatMessageList) {
        this.chatMessageList = chatMessageList;
    }

    public Integer getPluginCallIndex() {
        return pluginCallIndex;
    }

    public void setPluginCallIndex(Integer pluginCallIndex) {
        this.pluginCallIndex = pluginCallIndex;
    }

    public JSONObject getRepoChatInfo() {
        return repoChatInfo;
    }

    public void setRepoChatInfo(JSONObject repoChatInfo) {
        this.repoChatInfo = repoChatInfo;
    }
}
