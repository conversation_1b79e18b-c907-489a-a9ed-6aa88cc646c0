<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.ActionGenCodeSearchInfoMapper">

  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.ActionGenCodeSearchInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="query" jdbcType="VARCHAR" property="query" />
    <result column="recall" jdbcType="VARCHAR" property="recall" />
  </resultMap>


  <sql id="all_column">
    id,
    gmt_create,
    gmt_modified,
    session_id,
    query,
    recall
  </sql>

  <insert id="insert">
    insert into cg_action_gen_code_search_info
    (session_id, query, recall)
    values
    (#{sessionId}, #{query}, #{recall})
  </insert>

  <select id="getBySessionId" resultMap="BaseResultMap">
    select
    <include refid="all_column"/>
    from cg_action_gen_code_search_info
    <where>
      session_id = #{sessionId}
    </where>
  </select>

</mapper>