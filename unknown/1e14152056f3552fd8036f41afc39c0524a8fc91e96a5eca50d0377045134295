/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.service.codegpt;

import java.util.List;

import com.alipay.codegencore.model.domain.UserFeedbackDO;
import com.alipay.codegencore.model.openai.UserFeedBackVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version UserFeedbackService.java, v 0.1 2023年10月31日 下午2:20 lqb01337046
 */
public interface UserFeedbackService {
    /**
     * 查询所有用户反馈
     */
    List<UserFeedbackDO> queryAllUserFeedback();
    /**
     * 新增用户反馈
     */
    Boolean addUserFeedback(MultipartFile[] file,String content);
    /**
     * 新增用户反馈
     *
     * @param userFeedBackVO fileUrl 前端同学文件上传oss后的url
     *                       content 反馈内容
     * @return
     */
    Boolean feedBackViaUrl(UserFeedBackVO userFeedBackVO);
}
