package com.alipay.codegencore.utils;

import com.alipay.codegencore.utils.http.HttpClient;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * GetBuilder 单测试
 */
public class GetBuilderTest extends AbstractTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(GetBuilderTest.class);


    @Test
    public void test_get_syncExecute() {
        String ret = HttpClient.get("https://render.alipay.com/p/yuyan/hr-h5data_notification-config/zh_CN.json")
                .header("Content-Type", "application/json;charset=UTF-8")
                .syncExecute(50000L);
        LOGGER.info("get ret:"+ret);
        Assert.assertTrue(StringUtils.isNotBlank(ret));
    }

    @Test
    public void test_null_header() {
        String ret = HttpClient.get("https://render.alipay.com/p/yuyan/hr-h5data_notification-config/zh_CN.json")
                .header("Content-Type", null)
                .syncExecute(50000L);
        LOGGER.info("get ret:"+ret);
        Assert.assertTrue(StringUtils.isNotBlank(ret));
    }
}
