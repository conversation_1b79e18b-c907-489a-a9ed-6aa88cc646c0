package com.alipay.codegencore.utils;


import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.model.model.RepoInfoModel;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2019-09-20.
 */
public class RepoUrlUtil {

    //处理: *************************:koubei_client/**********************-koubei.git
    //     **************************:junlong.njl/fuzz-demo.git
    private static final Pattern gitRepoPattern1 = Pattern.compile("^git@(\\S*):([\\w\\-\\.]*)/([\\w\\-\\.]*)\\.git$");

    //处理: http://gitlab.alipay-inc.com/koubei_client/**********************-koubei.git
    private static final Pattern gitRepoPattern2 = Pattern.compile("^http[s]?://(\\S*)/([\\w\\-\\.]*)/([\\w\\-\\.]*)\\.git$");

    //处理: *************************/app_release/coveragecenter/tree/master
    private static final Pattern gitRepoPattern3 = Pattern.compile("^git@(\\S*):([\\w\\-\\.]*)/([\\w\\-\\.]*)/tree/(master)$");

    private static final Pattern gitRepoPattern4 = Pattern.compile("^http[s]?://(\\S*)\\/([\\w\\-\\.]*)/([\\w\\-\\.]*)/tree/(master)$");

    //处理: https://@gitlab.alipay-inc.com/app_release/coveragecenter/tree/[branch]
    private static final Pattern gitRepoPattern5 = Pattern.compile("^http[s]?://(\\S*)\\/([\\w\\-\\.]*)/([\\w\\-\\.]*)/tree/(\\S*)$");

    public static RepoInfoModel parseRepoUrl(String repoUrl) {
        if (repoUrl == null) {
            return null;
        }
        for (Pattern pattern : new Pattern[]{gitRepoPattern1, gitRepoPattern2, gitRepoPattern3, gitRepoPattern4, gitRepoPattern5}) {
            Matcher matcher = pattern.matcher((repoUrl));
            if (matcher.find()) {
                RepoInfoModel repoInfoModel = new RepoInfoModel();
                repoInfoModel.setGitDomain(matcher.group(1));
                repoInfoModel.setRepoGroup(matcher.group(2));
                repoInfoModel.setRepoName(matcher.group(3));
                repoInfoModel.setRepoAddr(repoUrl);
                return repoInfoModel;
            }
        }
        return null;
    }

    /**
     * 通过git信息构建ssh格式地址
     *
     * @param repoInfo
     * @return
     */
    public static String buildSSHStypeRepoUrl(RepoInfoModel repoInfo) {
        if (repoInfo == null) {
            return null;
        }
        if (repoInfo.getGitDomain() == null || repoInfo.getRepoGroup() == null || repoInfo.getRepoName() == null) {
            return null;
        }
        return String.format("git@%s:%s/%s.git", repoInfo.getGitDomain(), repoInfo.getRepoGroup(), repoInfo.getRepoName());
    }

    /**
     * 通过git信息构建http格式地址
     *
     * @param repoInfo
     * @return
     */
    public static String buildHttpStypeRepoUrl(RepoInfoModel repoInfo) {
        if (repoInfo == null) {
            return null;
        }
        if (repoInfo.getGitDomain() == null || repoInfo.getRepoGroup() == null || repoInfo.getRepoName() == null) {
            return null;
        }
        return String.format("https://%s/%s/%s.git", repoInfo.getGitDomain(), repoInfo.getRepoGroup(), repoInfo.getRepoName());
    }

    /**
     * 获取http格式的git地址
     *
     * @return
     */
    public static String getHttpStypeRepoUrl(String codeRepoUrl) {
        RepoInfoModel repoInfo = RepoUrlUtil.parseRepoUrl(codeRepoUrl);
        if (repoInfo == null) {
            return null;
        }
        if (repoInfo.getGitDomain() == null || repoInfo.getRepoGroup() == null || repoInfo.getRepoName() == null) {
            return null;
        }
        return String.format("https://%s/%s/%s.git", repoInfo.getGitDomain(), repoInfo.getRepoGroup(), repoInfo.getRepoName());
    }

    /**
     * 获取标准的git地址
     *
     * @return
     */
    public static String getSSHStyleRepoUrl(String codeRepoUrl) {
        RepoInfoModel repoInfo = RepoUrlUtil.parseRepoUrl(codeRepoUrl);
        return buildSSHStypeRepoUrl(repoInfo);
    }

    /**
     * 获取确定的gitDomain标准的git地址
     *
     * @return
     */
    public static String getSSHStypeRepoUrlWithGitDomain(String codeRepoUrl, String gitDomain) {
        if (codeRepoUrl == null) {
            return null;
        }
        RepoInfoModel repoInfo = RepoUrlUtil.parseRepoUrl(codeRepoUrl);
        repoInfo.setGitDomain(gitDomain);
        return buildSSHStypeRepoUrl(repoInfo);
    }

    /**
     * 拼接antCode制定相应代码片段的URL，如下：
     * https://code.alipay.com/common_release/codemeasure/blob/d372499deffee482b3b0f66445e40dd6932cd676
     * /app/bootstrap/src/main/java/com/alipay/codemeasure/CodeFinderTimedServiceInitListener.java#L10-24
     *
     */
    public static String getAntCodeURL(String repoGroup,String repoName,String commitId,String filePath,Integer startLine,Integer endLine) {
        StringBuilder samePath = new StringBuilder("https://");
        samePath.append(AppConstants.ANTCODE_GIT_DOMAIN + "/");
        samePath.append(repoGroup).append("/").append(repoName);
        samePath.append("/blob/");
        samePath.append(commitId).append("/").append(filePath);
        samePath.append("#L").append(startLine).append("-").append(endLine);
        return samePath.toString();
    }
}
