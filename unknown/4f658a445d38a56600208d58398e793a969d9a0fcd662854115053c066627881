package com.alipay.codegencore.model.domain;

import java.sql.Timestamp;

/**
 * svat 代码生成模块保存搜索结果的 DO
 * <AUTHOR>
 * @version 1.0 2024/11/1 15:38
 */
public class ActionGenCodeSearchInfoDO {

    /**
     * 数据库主键，自增 id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 会话 id
     */
    private String sessionId;

    /**
     * 用户输入的 query
     */
    private String query;

    /**
     * 召回结果
     */
    private String recall;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getRecall() {
        return recall;
    }

    public void setRecall(String recall) {
        this.recall = recall;
    }

    public Timestamp getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Timestamp gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Timestamp getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Timestamp gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }
}
