package com.alipay.codegencore.service.codegpt;

import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;

/**
 * NLP模型服务
 * 模型实现类说明文档：https://yuque.antfin.com/iikq97/lozx5s/nt5ow5cttmcbgyvu
 * <AUTHOR>
 */
public interface LanguageModelService {

    /**
     * 对话
     *
     * @param params 请求参数
     * @return
     */
    ChatMessage chat(GptAlgModelServiceRequest params);

    /**
     * 用于httpServlet的流式对话
     *
     * @param params 请求参数
     */
    void streamChatForServlet(GptAlgModelServiceRequest params);

    boolean isServiceOk();

}
