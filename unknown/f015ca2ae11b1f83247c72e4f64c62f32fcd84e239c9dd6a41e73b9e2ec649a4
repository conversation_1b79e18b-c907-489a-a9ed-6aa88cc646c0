package com.alipay.codegencore.model.domain;

import java.util.Date;

public class GptMessageFeedbackDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.gmt_create
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.gmt_modified
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.mongo_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private String mongoId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.deleted
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.user_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private String userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.message_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private String messageId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.agreed
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private Boolean agreed;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_message_feedback.content
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    private String content;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.id
     *
     * @return the value of links_gpt_message_feedback.id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.id
     *
     * @param id the value for links_gpt_message_feedback.id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.gmt_create
     *
     * @return the value of links_gpt_message_feedback.gmt_create
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.gmt_create
     *
     * @param gmtCreate the value for links_gpt_message_feedback.gmt_create
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.gmt_modified
     *
     * @return the value of links_gpt_message_feedback.gmt_modified
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.gmt_modified
     *
     * @param gmtModified the value for links_gpt_message_feedback.gmt_modified
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.mongo_id
     *
     * @return the value of links_gpt_message_feedback.mongo_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public String getMongoId() {
        return mongoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.mongo_id
     *
     * @param mongoId the value for links_gpt_message_feedback.mongo_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setMongoId(String mongoId) {
        this.mongoId = mongoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.deleted
     *
     * @return the value of links_gpt_message_feedback.deleted
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.deleted
     *
     * @param deleted the value for links_gpt_message_feedback.deleted
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.user_id
     *
     * @return the value of links_gpt_message_feedback.user_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.user_id
     *
     * @param userId the value for links_gpt_message_feedback.user_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.message_id
     *
     * @return the value of links_gpt_message_feedback.message_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public String getMessageId() {
        return messageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.message_id
     *
     * @param messageId the value for links_gpt_message_feedback.message_id
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.agreed
     *
     * @return the value of links_gpt_message_feedback.agreed
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public Boolean getAgreed() {
        return agreed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.agreed
     *
     * @param agreed the value for links_gpt_message_feedback.agreed
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setAgreed(Boolean agreed) {
        this.agreed = agreed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_message_feedback.content
     *
     * @return the value of links_gpt_message_feedback.content
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_message_feedback.content
     *
     * @param content the value for links_gpt_message_feedback.content
     *
     * @mbg.generated Wed Jun 26 17:16:49 CST 2024
     */
    public void setContent(String content) {
        this.content = content;
    }
}