<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alipay</groupId>
		<artifactId>codegencore-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<artifactId>codegencore-bootstrap</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<dependencies>
		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>codegencore-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>codegencore-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>healthcheck-sofa-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>isle-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>log4j2-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>startup-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>tracer-alipay-sofa-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.skyscreamer</groupId>
					<artifactId>jsonassert</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.junit.vintage</groupId>
			<artifactId>junit-vintage-engine</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.hamcrest</groupId>
					<artifactId>hamcrest-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<outputDirectory>../../target/boot</outputDirectory>
					<classifier>executable</classifier>
				</configuration>
				<executions>
					<execution>
						<id>package</id>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.6</version>
				<executions>
					<execution>
						<id>copy-resources</id>
						<phase>package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<encoding>UTF-8</encoding>
							<outputDirectory>../../target</outputDirectory>
							<resources>
								<resource>
									<directory>../../conf</directory>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-configs</id>
						<phase>package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<encoding>UTF-8</encoding>
							<outputDirectory>../../target/config</outputDirectory>
							<resources>
								<resource>
									<directory>./src/main/resources/config/${app}</directory>
									<includes>
										<include>*.properties</include>
										<include>*.yaml</include>
										<include>*.yml</include>
									</includes>
								</resource>
								<resource>
									<directory>./src/main/resources/config</directory>
									<includes>
										<include>*.properties</include>
										<include>*.yaml</include>
										<include>*.yml</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
