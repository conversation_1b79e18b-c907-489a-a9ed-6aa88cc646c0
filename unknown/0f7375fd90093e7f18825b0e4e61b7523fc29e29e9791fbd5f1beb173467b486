package com.alipay.codegencore.web.openapi;

import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.response.BaseResponse;
import com.alipay.codegencore.service.answer.AnswerIndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0 2024/7/29 10:24
 */
@RestController
@RequestMapping("/api/callback")
@Slf4j
public class CallbackController {


    @Autowired
    private AnswerIndexService answerIndexService;

    /**
     * 程序分析索引构建回调
     * @param callbackData
     * @return
     */
    @PostMapping("/codeinsight/index")
    public BaseResponse indexBuildCallback(@RequestBody JSONObject callbackData) {

        log.info("index build callback: {}", callbackData);

        answerIndexService.indexBuildCallback(callbackData);

        return BaseResponse.buildSuccess();
    }

    /**
     * 程序分析 wiki 生成回调
     * @param callbackData
     * @return
     */
    @PostMapping("/codeinsight/wiki")
    public BaseResponse wikiBuildCallback(@RequestBody JSONObject callbackData) {

        log.info("wiki build callback: {}", callbackData);

        answerIndexService.wikiBuildCallback(callbackData);

        return BaseResponse.buildSuccess();
    }


}
