package com.alipay.codegencore.utils.search;

import com.alipay.codegencore.model.AppConstants;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字典树
 * 本地化索引的基础数据结构，具备前缀搜索能力
 * 注意：
 * 1. 默认root的childValueFlag值为false
 *
 * <AUTHOR>
 * 创建时间 2022-02-21
 */
public class TrieTree {
    // trie树的根节点
    private TrieNode root;

    public TrieTree() {
        root = new TrieNode(null, null, null);
    }

    /**
     * 清理缓存树
     */
    public void clean() {
        root = new TrieNode(null, null, null);
    }

    /**
     * 增加任务节点
     *
     * @param codeToken
     * @param current
     * @param cacheKey
     * @return
     */
    public TrieNode addNode(String codeToken, TrieNode current, String cacheKey, Map<String, String> dynamicParamMap, boolean isChildValueCharFlag) {
        if (current == null) {
            current = root;
        }
        if (isChildValueCharFlag) {
            for (int i = 0; i < codeToken.toCharArray().length; i++) {
                String key = String.valueOf(codeToken.charAt(i));

                current = current.getChildren().computeIfAbsent(key, k -> new TrieNode(key, cacheKey, dynamicParamMap));
            }
        } else {
            current = current.getChildren().computeIfAbsent(codeToken, k -> new TrieNode(codeToken, cacheKey, dynamicParamMap));
        }
        return current;
    }

    /**
     * 返回符合token的节点
     * 1.查询是否有符合token值的child
     *
     * @param current
     * @param codeToken 子节点，1。如果不为空 && value没有数据，则继续下一次token查询 2.如果为空，则不符合条件 3。如果不为空 && value有值，则根据value去tbase查询结果
     * @return
     */
    public TrieNode searchWithOneLevel(TrieNode current, String codeToken) {
        TrieNode trieNode = current.getChildren().get(codeToken) == null ?
                current.getChildren().get(AppConstants.WILDCARD_SYMBOL) : current.getChildren().get(codeToken);
        if (trieNode != null) {
            return trieNode;
        }
        //如果还查不到，拆分codeToken，按字符查找
        for (int i = 0; i < codeToken.length(); i++) {
            char ch = codeToken.charAt(i);
            TrieNode node = current.getChildren().get(String.valueOf(ch));
            if (node == null) {
                //如果查不到，看下是否符合通配符匹配规则
                return current.getChildren().get(AppConstants.WILDCARD_SYMBOL);
            }
            current = node;
        }
        return current;
    }

    public TrieNode getRoot() {
        return root;
    }

    public void setRoot(TrieNode root) {
        this.root = root;
    }

    /**
     * 字典树叶子节点
     */
    public class TrieNode {
        /**
         * 当前节点拥有的孩子节点，使用hashmap进行存储，在查找子节点时的时间复杂度为O(1)
         */
        private Map<String, TrieNode> children = new ConcurrentHashMap<>();
//        /**
//         * 节点比较类型，默认0
//         * 0：子节点数据不拆分，直接string类型的equal比较
//         * 1: 节点数据需要拆分成char，进行char类型的equal比较
//         */
//        private boolean childValueCharFlag = false;
        /**
         * 当前节点值
         */
        private String value;
        /**
         * 字典树中涉及的动态参数定义
         */
        private Map<String, String> dynamicParamMap;

        /**
         * 代码模版名字
         * 可根据此key，从tbase查询代码模版数据+参数定义
         */
        private String templateId;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public TrieNode(String value, String templateId, Map<String, String> dynamicParamMap) {
            this.value = value;
            this.templateId = templateId;
            this.dynamicParamMap = dynamicParamMap;
        }

        public Map<String, String> getDynamicParamMap() {
            return dynamicParamMap;
        }

        public void setDynamicParamMap(Map<String, String> dynamicParamMap) {
            this.dynamicParamMap = dynamicParamMap;
        }

        public Map<String, TrieNode> getChildren() {
            return children;
        }

        public void setChildren(Map<String, TrieNode> children) {
            this.children = children;
        }

//        public boolean isChildValueCharFlag() {
//            return childValueCharFlag;
//        }
//
//        public void setChildValueCharFlag(boolean childValueCharFlag) {
//            this.childValueCharFlag = childValueCharFlag;
//        }

        public String getTemplateId() {
            return templateId;
        }

        public void setTemplateId(String templateId) {
            this.templateId = templateId;
        }
    }
}
