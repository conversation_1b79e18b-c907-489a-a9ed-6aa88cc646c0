package com.alipay.codegencore.dal.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.08.12
 */
public interface PluginManualMapper {
    @Select({"<script>"
            + "select  id "
            + " from    cg_plugin "
            + "where   deleted = 0  "
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + "and (owner_user_id = #{userId} or user_id = #{userId})"
            + " order by gmt_create DESC"
            + "</script>"})
    List<Long> getEditablePluginByUser(@Param("userId") Long userId, @Param("query") String query );

    @Select({"<script>"
            + "select  id "
            + " from    cg_plugin "
            + "where   deleted = 0  "
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + " order by gmt_create DESC"
            + "</script>"})
    List<Long> getEditablePluginByAdmin(@Param("userId") Long userId, @Param("query") String query);

    @Select({"<script>"
            + "select  id "
            + " from    cg_plugin "
            + "where   deleted = 0 and enable = 1"
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))"
            + "</if>"
            + "and ( (visable_user = 2)  or owner_user_id = #{userId} or user_id = #{userId})"
            + " order by gmt_create DESC "
            + "</script>"})
    List<Long> getAllPluginByEnable(@Param("userId") Long userId, @Param("query") String query);

    @Select({"<script>"
            + "select  id "
            + " from    cg_plugin "
            + "where   deleted = 0 and  enable = 1 "
            + "<if test='query != null'> "
            + "and (name like concat('%',#{query},'%') or description like concat('%',#{query},'%'))  "
            + "</if>"
            + " order by gmt_create DESC  "
            + "</script>"})
    List<Long> getAdminAllPluginByEnable(@Param("userId") Long userId, @Param("query") String query);
}
