package com.alipay.codegencore.utils;

import com.alipay.codegencore.model.model.EmbeddingResponseModel;
import com.alipay.codegencore.utils.embedding.VectorCalculate;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class VectorCalculateTest {

    @Test
    public void testCalculateCosineSimilarity(){
        List<BigDecimal> vector1 = Lists.newArrayList(new BigDecimal("1"),new BigDecimal("2"),new BigDecimal("3"));
        List<BigDecimal> vector2 = Lists.newArrayList(new BigDecimal("1"),new BigDecimal("2"),new BigDecimal("3"));
        BigDecimal bigDecimal = VectorCalculate.calculateCosineSimilarity(vector1,vector2);
        Assert.assertTrue(bigDecimal.compareTo(BigDecimal.ZERO) > 0);
    }

    @Test
    public void testCalculateCosineSimilarityTopN(){
        List<BigDecimal> vector1 = Lists.newArrayList(new BigDecimal("1"),new BigDecimal("2"),new BigDecimal("3"));
        List<EmbeddingResponseModel> embeddingResponseModelList = new ArrayList<>();
        EmbeddingResponseModel embeddingResponseModel = new EmbeddingResponseModel();
        embeddingResponseModel.setOriginalEmbeddingList(Lists.newArrayList(new BigDecimal("1"),new BigDecimal("2"),new BigDecimal("3")));
        embeddingResponseModelList.add(embeddingResponseModel);
        List<EmbeddingResponseModel> list = VectorCalculate.calculateCosineSimilarityTopN(vector1,embeddingResponseModelList,1);
        Assert.assertFalse(list.isEmpty());
    }
}
