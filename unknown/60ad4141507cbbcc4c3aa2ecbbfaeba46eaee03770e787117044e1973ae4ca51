<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.alipay.sofa</groupId>
        <artifactId>sofaboot-alipay-dependencies</artifactId>
        <version>3.21.0</version>
    </parent>
    <groupId>com.alipay</groupId>
    <artifactId>codegencore-parent</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>codegencore</name>
    <description>青燕代码补全内网版本</description>

    <modules>
        <module>app/bootstrap</module>
        <module>app/web</module>
        <module>app/service</module>
        <module>app/dal</module>
        <module>app/model</module>
        <module>app/utils</module>
        <module>app/facade</module>
    </modules>

    <properties>
        <java.version>11</java.version>
        <facade.version>0.0.1-SNAPSHOT</facade.version>
        <openai.version>0.11.0</openai.version>
        <antwork.version>1.0.0.20230329</antwork.version>
        <grpc.version>1.49.2</grpc.version>
        <protobuf.version>3.21.7</protobuf.version>
        <ray.serving.version>1.13.14</ray.serving.version>
        <arks-client.version>1.2.10.RC09</arks-client.version>
        <aclinkelib.version>1.2.5.20211012</aclinkelib.version>
        <zdatafront.vsersion>1.0.0.20190301</zdatafront.vsersion>
     	<zdal.version>5.21.1</zdal.version>
        <freemarker.version>2.3.33</freemarker.version>
        <diff.version>4.12</diff.version>
        <javaparser.version>3.26.1</javaparser.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
        </dependency>
        <dependency>
            <groupId>org.smartunit</groupId>
            <artifactId>smartunit-standalone-runtime</artifactId>
            <version>1.4.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.github.javaparser</groupId>
                <artifactId>javaparser-symbol-solver-core</artifactId>
                <version>${javaparser.version}</version>
            </dependency>

            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.java-diff-utils</groupId>
                <artifactId>java-diff-utils</artifactId>
                <version>${diff.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.8</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.11.0</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>1.30</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>4.9.3</version>
            </dependency>
            <!--			<dependency>-->
            <!--				<groupId>com.github.pagehelper</groupId>-->
            <!--				<artifactId>pagehelper-spring-boot-starter</artifactId>-->
            <!--				<version>1.4.0</version>-->
            <!--			</dependency>-->
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>codegencore-bootstrap</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>codegencore-web</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>codegencore-service</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>codegencore-dal</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>codegencore-model</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>codegencore-utils</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>codegencore-facade</artifactId>
                <version>${facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.zdal</groupId>
                <artifactId>zdal-orm-annotation</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-function</artifactId>
                <version>0.7.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.4.6</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>infosec-tr-sofa-boot3-starter</artifactId>
                <version>2.0.0.20200907</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.antwork</groupId>
                <artifactId>antwork-facade</artifactId>
                <version>${antwork.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.antwork</groupId>
                <artifactId>antwork-common</artifactId>
                <version>${antwork.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.13.3</version>
            </dependency>
            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-core</artifactId>
                <version>3.5.4</version>
            </dependency>
            <!-- maya dependency start -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-bom</artifactId>
                <version>${grpc.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alipay.arks</groupId>
                <artifactId>arks-client-java</artifactId>
                <version>${arks-client.version}</version>
                <!-- 版本信息看这里 https://yuque.antfin-inc.com/mdp/userguide/fscmu0 -->
            </dependency>
            <!-- http client版本非强要求，如果业务系统已集成更高版本，可不用另外修改版本。低版本需升级 -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpasyncclient</artifactId>
                <version>4.1.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>4.4.1</version>
            </dependency>
            <!-- protobuf相关 -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <!-- grpc相关 -->
            <dependency>
                <groupId>com.alipay.arks</groupId>
                <artifactId>arks-client-grpc</artifactId>
                <version>${arks-client.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.9.0</version> <!-- prevent downgrade via protobuf-java-util -->
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>31.1-jre</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>2.0.14</version>
            </dependency>
            <!-- maya dependency end -->
            <dependency>
                <groupId>com.alipay.rcsmart</groupId>
                <artifactId>rcsmart-common-service-facade</artifactId>
                <version>1.0.0.20230425</version>
            </dependency>
            <!-- ray dependency start -->
            <dependency>
                <groupId>com.antgroup.ray</groupId>
                <artifactId>serving-client-grpc</artifactId>
                <version>${ray.serving.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>hessian</groupId>
                        <artifactId>hessian</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--<dependency>
                <groupId>com.antgroup.ray</groupId>
                <artifactId>serving-client-sofaboot-rpc</artifactId>
                <version>${ray.serving.version}</version>
            </dependency>-->
            <dependency>
                <groupId>com.antgroup.ray</groupId>
                <artifactId>serving-client-antvip</artifactId>
                <version>${ray.serving.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>hessian</groupId>
                        <artifactId>hessian</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alipay.antassistant</groupId>
                <artifactId>antassistant-common-service-facade</artifactId>
                <version>1.0.0.20230621</version>
            </dependency>
            <!-- ray dependency end -->

            <!-- 引入为了解析文件end -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>3.0.0-RC1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <!-- 引入为了解析文件end -->

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.15.2</version>
            </dependency>


            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.16.1</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.zdatafront</groupId>
                <artifactId>zdatafront-common-service-facade</artifactId>
                <version>${zdatafront.vsersion}</version>
            </dependency>
            <!-- 接入aci组件的依赖 -->
            <dependency>
                <groupId>com.alipay.linkede</groupId>
                <artifactId>aclinkelib-common-service-facade</artifactId>
                <version>${aclinkelib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.linkede</groupId>
                <artifactId>aclinkelib-common-util</artifactId>
                <version>${aclinkelib.version}</version>
            </dependency>
            <!-- 接入aci组件的依赖end -->
            <dependency>
                <groupId>com.alipay.fc.buservice</groupId>
                <artifactId>buservice-sdk</artifactId>
                <version>1.0.0.20210909</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.search</groupId>
                <artifactId>search-client</artifactId>
                <version>1.3.7</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.security</groupId>
                <artifactId>alipay-security-core</artifactId>
                <version>0.0.19</version>
            </dependency>
            <dependency>
                <groupId>org.commonmark</groupId>
                <artifactId>commonmark</artifactId>
                <version>0.21.0</version>
            </dependency>
            <!--  PcUICaseCommonFacade  -->
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>rcqualitydataprod-common-service-facade</artifactId>
                <version>1.0.0.20240111</version>
            </dependency>
            <!--    API GATEWAY        -->
            <dependency>
                <groupId>com.alipay.devapi</groupId>
                <artifactId>client-sdk</artifactId>
                <version>1.0.0</version>
            </dependency>
            <!--    BackendAppQueryFacade  -->
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>archdatacenter-facade</artifactId>
                <version>1.2.9</version>
            </dependency>
            <!--    AgentSECSDK  -->
            <dependency>
                <groupId>com.alipay</groupId>
                <artifactId>agentsecgateway-common-service-sdk</artifactId>
                <version>0.0.3</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.common.security</groupId>
                <artifactId>alipay-common-security-client</artifactId>
                <version>1.2.5</version>
            </dependency>

            <dependency>
                <groupId>io.github.java-diff-utils</groupId>
                <artifactId>java-diff-utils</artifactId>
                <version>4.12</version>
            </dependency>
<!--蚂蚁流程依赖-->
            <dependency>
                <groupId>com.alipay.fc.process</groupId>

                <artifactId>fcprocess-common-service-facade</artifactId>

                <version>1.0.0.20240923</version>

            </dependency>

            <dependency>
                <groupId>com.alipay.fc.common.lang</groupId>
                <artifactId>fc-common-lang</artifactId>
                <version>2.1.0.20170601</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-boot-alipay-rpc</artifactId>
            </dependency>
            <dependency>
                <groupId>com.alipay.common</groupId>

                <artifactId>alipay-common-error</artifactId>

                <version>1.2</version>

            </dependency>




        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <threadCount>1</threadCount>
                    <includes>
                        <include>**/*Tests.java</include>
                        <include>**/*Test.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/Abstract*.java</exclude>
                    </excludes>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.surefire</groupId>
                        <artifactId>surefire-junit-platform</artifactId>
                        <version>2.22.2</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.0.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <downloadSources>true</downloadSources>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.alipay.sofa.plugins</groupId>
                <artifactId>gitops-maven-plugin</artifactId>
                <version>1.0-SNAPSHOT</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!--蚂蚁主站开发环境maven配置-->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>alipay_test</id>
                    <url>https://maven-test.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>alipay_dev</id>
                    <url>https://maven-dev.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>alipay_test</id>
                    <url>https://maven-test.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
                <pluginRepository>
                    <id>alipay_dev</id>
                    <url>https://maven-dev.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
        <!--蚂蚁主站生产环境maven配置-->
        <profile>
            <id>prod</id>
            <repositories>
                <repository>
                    <id>alipay_test</id>
                    <url>https://maven-test.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>alipay_test</id>
                    <url>https://maven-test.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

</project>
