/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version GptRef.java, v 0.1 2023年11月08日 上午9:52 wb-tzg858080
 */
public class GptRef extends ToString {

    /**
     * id
     */
    private String id;

    /**
     * 类型 - SNIPPET知识点id
     */
    private String type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}