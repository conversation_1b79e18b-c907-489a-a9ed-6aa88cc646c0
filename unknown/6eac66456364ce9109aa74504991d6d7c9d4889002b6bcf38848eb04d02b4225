package com.alipay.codegencore.model.model;

import com.alipay.codegencore.model.model.tool.learning.PluginInfo;
import com.alipay.codegencore.model.openai.ChatFunctionCall;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version : PluginLogModel.java, v 0.1 2023年07月19日 15:58 baoping Exp $
 */
public class PluginLogModel {
    List<StageLogModel> stageLogModels;

    public PluginLogModel() {
        stageLogModels = new ArrayList<>();
    }

    /**
     * 添加阶段日志
     */
    public void addStageLog(StageLogModel stageLogModel) {
        stageLogModels.add(stageLogModel);
    }

    /**
     * 添加阶段日志
     * @param stageName
     * @param status
     * @param content
     */
    public void addStageLog(String stageName, boolean status, String content, PluginInfo pluginInfo) {
        stageLogModels.add(new StageLogModel(null, stageName, status, content, pluginInfo,null));
    }


    /**
     * 添加阶段日志
     * @param index
     * @param stageName
     * @param status
     * @param content
     */
    public void addStageLog(Integer index, String stageName, boolean status, String content, PluginInfo pluginInfo) {
        stageLogModels.add(new StageLogModel(index, stageName, status, content, pluginInfo,null));
    }

    public List<StageLogModel> getStageLogModels() {
        return stageLogModels;
    }

    public void setStageLogModels(List<StageLogModel> stageLogModels) {
        this.stageLogModels = stageLogModels;
    }

    /**
     * stageLogModel 阶段日志model
     */
    public static class StageLogModel {
        /**
         * 表示调用的第几个插件，从0开始，-1表示最终结果
         */
        private Integer index;
        /**
         * 阶段名称，包括preRequest, llm, postRequest, summary
         */
        private String stage;
        /**
         * 是否成功
         */
        private boolean status;
        /**
         * 该阶段的内容，markdown格式的非结构化数据
         */
        private String content;
        /**
         * function call的内容，专门用于functionDecide阶段
         */
        private ChatFunctionCall functionCall;
        /**
         * 插件信息
         */
        private PluginInfo pluginInfo;

        /**
         * 构造函数
         * @param stageName
         * @param status
         * @param content
         */
        public StageLogModel(Integer index, String stageName, boolean status, String content, PluginInfo pluginInfo, ChatFunctionCall functionCall) {
            this.index = index;
            this.stage = stageName;
            this.status = status;
            this.content = content;
            this.pluginInfo = pluginInfo;
            this.functionCall = functionCall;
        }

        public boolean isStatus() {
            return status;
        }

        public void setStatus(boolean status) {
            this.status = status;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getStage() {
            return stage;
        }

        public void setStage(String stage) {
            this.stage = stage;
        }

        public Integer getIndex() {
            return index;
        }

        public void setIndex(Integer index) {
            this.index = index;
        }

        public ChatFunctionCall getFunctionCall() {
            return functionCall;
        }

        public void setFunctionCall(ChatFunctionCall functionCall) {
            this.functionCall = functionCall;
        }

        public PluginInfo getPluginInfo() {
            return pluginInfo;
        }

        public void setPluginInfo(PluginInfo pluginInfo) {
            this.pluginInfo = pluginInfo;
        }
    }
}
