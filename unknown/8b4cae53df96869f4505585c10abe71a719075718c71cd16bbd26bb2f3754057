package com.alipay.codegencore.model.model;

import java.io.Serializable;
import java.util.Set;

/**
 * 方法定义规则匹配
 *
 * <AUTHOR>
 * 创建时间 2022-04-14
 */
public class MethodDefinitionRuleModel implements Serializable {
    /**
     * 防止字段变化导致反序列化失败
     */
    private static final long serialVersionUID = 1L;
    /**
     * 方法名匹配规则
     * eg. [[is],[Empty,Blank]] 代表 isEmpty(),isBlank(),isAbcBlank()都符合要求.单独is或者Empty不行
     */
    private Set<Set<String>> methodNameRuleSet;
    /**
     * 参数类型匹配规则
     * eg. [String] 代表参数必须是String类型。 [String,*] 代表必须要有两个参数，其中之一必须是String类型
     */
    private Set<String> paramClassRuleSet;
    /**
     * 返回值类型匹配规则
     * eg.String 代表返回值必须是String类型
     */
    private String retrunClassRule;


    public String getRetrunClassRule() {
        return retrunClassRule;
    }

    public void setRetrunClassRule(String retrunClassRule) {
        this.retrunClassRule = retrunClassRule;
    }

    public Set<Set<String>> getMethodNameRuleSet() {
        return methodNameRuleSet;
    }

    public void setMethodNameRuleSet(Set<Set<String>> methodNameRuleSet) {
        this.methodNameRuleSet = methodNameRuleSet;
    }

    public Set<String> getParamClassRuleSet() {
        return paramClassRuleSet;
    }

    public void setParamClassRuleSet(Set<String> paramClassRuleSet) {
        this.paramClassRuleSet = paramClassRuleSet;
    }
}
