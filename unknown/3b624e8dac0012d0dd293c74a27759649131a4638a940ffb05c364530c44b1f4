package com.alipay.codegencore.model.model.tool.learning;

import com.alipay.codegencore.model.domain.PluginDO;

/**
 * 插件信息
 * <AUTHOR>
 */
public class PluginInfo {
    /**
     * 插件id
     */
    private Long id;
    /**
     * 插件名称
     */
    private String name;
    private String type;
    /**
     * 插件显示名称
     */
    private String displayName;
    /**
     * 插件版本
     */
    private String version;
    /**
     * 插件描述
     */
    private String description;

    public PluginInfo() {
    }

    /**
     * 构造函数
     * @param id 插件id
     * @param name 插件名称
     */
    public PluginInfo(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    /**
     * 构造函数
     * @param id 插件id
     * @param name 插件名称
     * @param description 插件描述
     */
    public PluginInfo(Long id, String name, String description) {
        this.id = id;
        this.name = name;
        this.description = description;
    }

    /**
     * 构造函数
     * @param pluginDO
     */
    public PluginInfo(PluginDO pluginDO){
        this.id = pluginDO.getId();
        this.name = pluginDO.getName();
        this.description = pluginDO.getDescription();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
