package com.alipay.codegencore.service.tool.learning.plugin.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.service.tool.learning.plugin.CodefusePipelinePlugin;
import com.alipay.codegencore.service.utils.AsynToMarkdownTable;
import com.alipay.codegencore.service.utils.SqlTokenUtils;
import com.alipay.codegencore.utils.http.HttpClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;



/**
 * <AUTHOR>
 */
@Component
public class AssetPlatformText2SqlPipelinePlugin implements CodefusePipelinePlugin {

    private static final Logger LOGGER = LoggerFactory.getLogger( AssetPlatformText2SqlPipelinePlugin.class );
    /**
     * <PERSON><PERSON>hain向量匹配服务地址
     */
    private final String LANGCHAIN_TEXT_VECTOR_MATCHING_URL = "https://granada-prod.alipay.com/AssetGptSql/textVectorMatching";

    /**
     * LangChain查询数据库SCHEMA地址
     */
    private final String LANGCHAIN_SCHEMA_QUERY_URL = "https://qachain.alipay.com/api/schema";

    private final String SQL_EXECUTOR_URL = "https://granada-pre.alipay.com/AssetGptSql/executeSqlFromAssetGpt";


    /**
     * 前置请求
     *
     * @param params 请求参数
     * @return
     */
    @Override
    public JSONObject preRequest(JSONObject params) {
        JSONObject result = new JSONObject();

        String userQuery = params.getString("query");
        String operator = params.getString("empId");
        /* start: 本段逻辑用于提高匹配准确度
         * 这里的代码是用于解析如下输入格式：场景，问题
         * 例如：借呗授信，查询借呗授信的支付宝账号
         * 自动取第一个逗号前的部分（也就是借呗授信）去匹配相关的表
         * 如果问题中不带”，“，就会拿整个问题去匹配表
         */
        StringBuilder question = new StringBuilder(userQuery);

        String[] questionList = question.toString().split("，", 2);
        String scene;
        if (questionList.length == 2) {
            scene = questionList[0];
            question = new StringBuilder(questionList[1]);
        } else {
            scene = question.toString();
        }

        JSONObject langChainBody = new JSONObject();
        langChainBody.put("query", scene);
        langChainBody.put("operator", operator);
        JSONObject langChainResponse = null;
        try {
            String response = HttpClient.post(LANGCHAIN_TEXT_VECTOR_MATCHING_URL)
                    .content(langChainBody.toJSONString())
                    .syncExecuteWithExceptionThrow(10000);

            langChainResponse = JSON.parseObject(response);
            if (!langChainResponse.getBoolean("success")) {
                LOGGER.warn("asset llm call langchain failed: {}", langChainResponse.getString("resultDesc"));
                result.put("msg", "LangChain调用失败");
                result.put("code", "500");
                return result;
            }
        } catch (Exception e) {
            LOGGER.warn("asset llm call langchain get http error");
            result.put("msg", "资产大模型下游LangChain组件调用失败");
            result.put("code", "500");
            return result;
        }

        if (!langChainResponse.containsKey("resultObj") || langChainResponse.getJSONArray("resultObj").isEmpty()) {
            result.put("msg", "下游Embedding查询为空");
            result.put("code", "500");
            return result;
        }
        JSONObject highestScoreObj = null;
        double highestScore = -1;

        JSONArray resultObj = langChainResponse.getJSONArray("resultObj");
        for (int i = 0; i < resultObj.size(); i++) {
            JSONObject obj = resultObj.getJSONObject(i);
            JSONObject arnRes = obj.getJSONObject("meta");
            double score = obj.getDouble("score");

            if (arnRes.containsKey("arn") && obj.getDouble("score") > 0.8 && score > highestScore) {
                highestScoreObj = obj;
                highestScore = score;
            }
        }

        if(highestScoreObj == null){
            result.put("msg", "没有匹配到相关表");
            result.put("code", "500");
            return result;
        }
        LOGGER.info("选取最高分表: {}, score: {}", highestScoreObj.getString("title"), highestScore);


        JSONObject arnRes = highestScoreObj.getJSONObject("meta");
        StringBuilder schema = new StringBuilder();
        String arn = arnRes.getString("arn");
        JSONObject schemaQueryBody = new JSONObject();
        schemaQueryBody.put("arn", arn);

        try{
            String response = HttpClient.post(LANGCHAIN_SCHEMA_QUERY_URL)
                    .content(schemaQueryBody.toJSONString())
                    .syncExecuteWithExceptionThrow(10000);

            JSONObject langChainColumnResult = JSON.parseObject(response);
            schema.append(highestScoreObj.getString("title")).append(" ").append(highestScoreObj.getString("text")).append("(");
            JSONArray columnList = langChainColumnResult.getJSONArray("resultObj");
            for (int i = 0; i < columnList.size(); i++) {
                JSONObject dbColumnInfo = columnList.getJSONObject(i);
                schema.append(dbColumnInfo.getString("columnName")).append(" ");
                String comment = dbColumnInfo.getString("comment");
                if (comment.isEmpty()) {
                    schema.append("null");
                } else {
                    schema.append(comment);
                }
                if (i < columnList.size() - 1) {
                    schema.append(",");
                }
            }
            schema.append(")");
        }catch (Exception e){
            result.put("msg", "请求schema查询接口报错");
            result.put("code", "500");
            return result;
        }

        StringBuilder finalPrompt = new StringBuilder();
        finalPrompt.append("文本生成SQL(text to sql)\n");
        finalPrompt.append("language: text\n");
        finalPrompt.append(question).append("\n");
        finalPrompt.append("schema: ").append(schema).append("\n");
        finalPrompt.append("language: mysql\n");
        result.put("prompt", finalPrompt);
        result.put("tableName", highestScoreObj.getString("title"));
        result.put("tableDesc", highestScoreObj.getString("text"));

        return result;
    }

    /**
     * 后置接口调用
     *
     * @param postParams 请求参数
     * @return
     */
    @Override
    public JSONObject postRequest(JSONObject postParams) {
        JSONObject result = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject preResponse = postParams.getJSONObject("preResponse");
        JSONObject params = postParams.getJSONObject("params");
        String sql = postParams.getString("llmResult");
        // sql判空处理，防止npe
        if(StringUtils.isBlank(sql)){
            result.put("sqlResult","模型推理生成sql语句失败");
            return result;
        }
        //兼容生成的sql
        sql = cleanSqlString(sql);
        String template = String.format("为您找到一些可能符合要求的资产信息，%s ( %s ) 推荐您使用如下SQL查询：\n```sql\n %s \n```\n", preResponse.getString("tableName"),preResponse.getString("tableDesc"),sql);
        content.put("sql",sql);
        content.put("tableName",preResponse.getString("tableName"));
        content.put("operator",params.getString("empId"));
        content.put("limit",params.getString("limit"));
        String token = SqlTokenUtils.getToken(sql,preResponse.getString("tableName"),params.getString("empId"));
        content.put("token",token);
        LOGGER.info("sql executor request:url:{} param:{}",SQL_EXECUTOR_URL,content.toJSONString());
        JSONObject langChainResponse = null;
        try {
            String response = HttpClient.post(SQL_EXECUTOR_URL)
                    .content(content.toJSONString())
                    .syncExecuteWithExceptionThrow(10000);
            langChainResponse = JSON.parseObject(response);
            LOGGER.info("sql executor response :{}",response);
            if (!langChainResponse.getBoolean("success")) {
                LOGGER.warn("sql executor run failed: {}", langChainResponse.getString("resultDesc"));
                result.put("sqlResult", String.format("%s sql执行失败，具体的错误信息如下:\n %s",template,langChainResponse.getString("resultDesc")));
                return result;
            }
        } catch (Exception e) {
            result.put("sqlResult",String.format("%s sql执行接口报错，错误原因是:\n %s",template,e));
            return result;
        }
        JSONObject resultObj = langChainResponse.getJSONObject("resultObj");
        JSONArray resultLong = resultObj.getJSONArray("resultLong");
        String markdownTable = AsynToMarkdownTable.classListToMarkdownTable(resultLong);
        result.put("sqlResult",String.format("%s sql执行成功:\n\n %s \n",template,markdownTable));
        return result;
    }
    /**
     * 去掉生成的多余的标签以及末尾补充;符号
     * <AUTHOR>
     * @since 2024.01.05
     * @param sql sql
     * @return java.lang.String
     */
    private String cleanSqlString(String sql) {
        // 正则表达式用于匹配开头和结尾的<XXX>格式标签
        String regex = "(^<.*?>)|(<.*?>$)";

        // 使用replaceAll方法来移除匹配到的标签
        String cleanedSql = sql.replaceAll(regex, "").trim();
        cleanedSql = cleanedSql.replaceAll("[\r\n]+", "");
        // 返回处理后的字符串
        if(!cleanedSql.endsWith(";")){
            cleanedSql = cleanedSql+";";
        }
        return cleanedSql;
    }

}
