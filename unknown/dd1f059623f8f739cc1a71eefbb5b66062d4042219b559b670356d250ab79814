package com.alipay.codegencore.service.ideaevo;

import com.alipay.codegencore.model.domain.ActionGenCodeSearchInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 2024/11/1 16:08
 */
public interface ActionGenCodeSearchInfoService {

    /**
     * 保存搜索结果信息记录
     *
     * @param sessionId
     * @param query
     * @param recall
     */
    void save(String sessionId, String query, String recall);

    /**
     * 根据sessionId查询搜索结果信息
     *
     * @param sessionId
     * @return
     */
    List<ActionGenCodeSearchInfoDO> queryBySessionId(String sessionId);

}
