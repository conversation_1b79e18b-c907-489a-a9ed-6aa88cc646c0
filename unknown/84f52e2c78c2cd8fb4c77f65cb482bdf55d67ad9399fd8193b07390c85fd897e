package com.alipay.codegencore.model.request.tsingyan;

import com.alipay.codegencore.model.enums.IdeTypeEnum;
import com.alipay.codegencore.model.enums.ProductNameTypeEnum;

import java.io.Serializable;

/**
 * 抽象请求参数
 *
 * <AUTHOR>
 * 创建时间 2022-10-24
 */
public abstract class AbstractRequestBean implements Serializable {
    /**
     * 防止字段变化导致反序列化失败
     */
    private static final long serialVersionUID = 1L;
    /**
     * 用户token
     */
    private String userToken;

    /**
     * 插件版本号
     */
    private String pluginVersion;
    /**
     * 产品类型值.
     * 默认：蚂蚁内青燕
     */
    private int productNameType = ProductNameTypeEnum.ANT_TSINGYAN.getType();
    /**
     * IDE类型值
     * 默认：idea
     */
    private int ideType = IdeTypeEnum.IDEA.getType();

    public int getProductNameType() {
        return productNameType;
    }

    public void setProductNameType(int productNameType) {
        this.productNameType = productNameType;
    }

    public int getIdeType() {
        return ideType;
    }

    public void setIdeType(int ideType) {
        this.ideType = ideType;
    }

    public String getPluginVersion() {
        return pluginVersion;
    }

    public void setPluginVersion(String pluginVersion) {
        this.pluginVersion = pluginVersion;
    }

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }
    
}
