package com.alipay.codegencore.model.request;

import com.alipay.codegencore.model.model.analysis.TempCodeAnalysisResultContext;

/**
 * 代码推荐请求bean
 *
 * <AUTHOR>
 * 创建时间 2022-01-06
 */
public class CompletionsRequestBean extends AbstractClientModel {
    /**
     * 预测所需的代码内容
     */
    private String prompt;
    /**
     * 请求超时时间
     */
    private int timeoutTime = 1000;
    /**
     * 当前class经过扫描分析后的结果
     */
    private TempCodeAnalysisResultContext tempCodeAnalysisContext;

    /**
     * 会话id
     */
    private Long sessionId;

    public Long getSessionId() {
        return sessionId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public TempCodeAnalysisResultContext getTempCodeAnalysisContext() {
        return tempCodeAnalysisContext;
    }

    public void setTempCodeAnalysisContext(TempCodeAnalysisResultContext tempCodeAnalysisContext) {
        this.tempCodeAnalysisContext = tempCodeAnalysisContext;
    }

    public int getTimeoutTime() {
        return timeoutTime;
    }

    public void setTimeoutTime(int timeoutTime) {
        this.timeoutTime = timeoutTime;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

}
