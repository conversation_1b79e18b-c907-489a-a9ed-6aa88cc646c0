/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: Simple.java, v 0.1 2021-11-19 9:57 wb-tzg858080 Exp $$
 */
public class SimpleActionCard extends ToString {
    /**
     * 标题
     */
    private String                       title;
    /**
     * 内容
     */
    private String                       content;
    /**
     * 按钮
     */
    private List<SimpleActionCardButton> buttons;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<SimpleActionCardButton> getButtons() {
        return buttons;
    }

    public void setButtons(List<SimpleActionCardButton> buttons) {
        this.buttons = buttons;
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @return null
     */
    public SimpleActionCard() {
    }
    /**
     *
     * <AUTHOR>
     * @since 2024.07.01
     * @param title title
     * @param content content
     * @param buttons buttons
     * @return null
     */
    public SimpleActionCard(String title, String content, List<SimpleActionCardButton> buttons) {
        this.title = title;
        this.content = content;
        this.buttons = buttons;
    }
}
