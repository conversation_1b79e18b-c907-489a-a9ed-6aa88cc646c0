package com.alipay.codegencore.web.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 对这个接口进行过滤，添加相关参数/api/chat/commonPower/v1/files
 */
@WebFilter("/*") //这个的 url 似乎不起作用？所以目前是在方法里面过滤
@Slf4j
public class CacheBodyFilter implements Filter, Ordered {


    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        HttpServletRequest req = (HttpServletRequest) request;
        String url = new UrlPathHelper().getLookupPathForRequest(req);

        if (url.startsWith("/api/chat/commonPower/v1/files")) {  
            MultiReadHttpServletRequest wrappedRequest = new MultiReadHttpServletRequest((HttpServletRequest) request);

            byte[] body = wrappedRequest.getBody();
            log.info("do cache body, length={}", body!=null? body.length: 0);

            request.setAttribute("cachedBody", wrappedRequest.getBody());
            chain.doFilter(wrappedRequest, response);
        }else {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void init(FilterConfig filterConfig) {
        // 初始化逻辑
        log.info("CacheBodyFilter init");
    }

    @Override
    public void destroy() {
        // 销毁逻辑
    }

    @Override
    public int getOrder() {
        return -100000;
    }
}
