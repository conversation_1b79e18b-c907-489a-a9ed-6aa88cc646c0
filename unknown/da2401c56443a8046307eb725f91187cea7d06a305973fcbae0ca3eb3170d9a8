package com.alipay.codegencore.service.ideaevo;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0 2024/9/22 21:53
 */
public enum VatSearchType {

    V1_SEARCH, V2_ANSWER, V3_BATE, V4_VAT_SEARCH;

    /**
     * 根据名称获取枚举
     * @param name
     * @return
     */
    public static VatSearchType getByName(String name) {
        return Arrays.stream(VatSearchType.values())
                .filter(item -> item.name().equalsIgnoreCase(name))
                .findFirst()
                .orElse(V2_ANSWER);
    }

    /**
     * 判断名称是否合法
     * @param name
     * @return
     */
    public static boolean valid(String name) {
        return Arrays.stream(VatSearchType.values())
                .anyMatch(item -> item.name().equalsIgnoreCase(name));
    }

}
