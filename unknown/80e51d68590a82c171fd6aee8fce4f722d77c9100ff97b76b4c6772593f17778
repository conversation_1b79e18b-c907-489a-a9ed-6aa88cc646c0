package com.alipay.codegencore.model.enums;

/**
 * 审核平台
 */
public enum ReviewPlatformEnum {

    /**
     * PASSED 和 RECOVER 可以认为是检测通过,其他都不通过
     */
    INFOSEC("内容安全审核平台"),

    /**
     * 数据安全审核平台
     */
    KEYMAP("数据安全审核平台"),

    /**
     * 数据安全审核平台
     */
    ANTDSR("数据安全审核平台"),

    /**
     * 「解语花」针对营销宣传、金融产品信息等内容进行自动化、智能化审核，防范营销行为存在误导、夸大等违规情况，守护平台内容合规风险，为业务和生态团队提升效能。
     * <a href="https://rcsmart.alipay.com/">解语花官网</a>
     */
    RCSMART("解语花合规审核平台"),

    /**
     * 意图识别
     */
    INTENTION("意图识别平台"),
    ;

    private String desc;

    ReviewPlatformEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
