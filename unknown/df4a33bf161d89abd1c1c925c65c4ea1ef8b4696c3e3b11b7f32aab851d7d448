/*
 * Alipay.com Inc.
 * Copyright (c) 2004-2020 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 基类
 *
 * <AUTHOR>
 * @version $Id: BaseDO.java, v 0.1 2020-01-02 14:23 zhi.huangcz Exp $$
 */
public class BaseDO extends ToString {
    /**
     * 数据库主键
     */
    public String  id       = "";
    /**
     * 创建时间
     */
    public Date created  = null;
    /**
     * 修改时间
     */
    public Date    modified = null;
    /**
     * 是否删除
     */
    public Boolean deleted  = false;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01
     * @param other other 
     * @return boolean
     */
    public boolean equals(Object other) {
        if (other == null) {
            return false;
        }
        return StringUtils.equalsIgnoreCase(this.id, ((BaseDO) other).id);
    }
    /**
     * 
     * <AUTHOR>
     * @since 2024.07.01 
     * @return int
     */
    public int hashCode() {
        int result = id.hashCode();
        result = 31 * result + (created != null ? created.hashCode() : 0);
        result = 31 * result + deleted.hashCode();
        return result;
    }
}