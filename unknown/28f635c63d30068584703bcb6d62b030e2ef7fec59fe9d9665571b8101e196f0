package com.alipay.codegencore.model.model.tool.learning.plugin.stage.schema;

import java.util.Map;

/**
 * API阶段输入
 */
public class ApiStageInput {

    /**
     * 根据functionCall的结果和yaml描述弹出的表单
     */
    private Map<String, Object> formData;

    /**
     * 用户填写的最终参数
     */
    private Map<String, Object> userFill;

    /**
     * 请求体
     */
    private Map<String, Object> requestBody;

    public ApiStageInput() {
    }

    /**
     * 全参数构造函数
     * @param formData
     * @param userFill
     * @param requestBody
     */
    public ApiStageInput(Map<String, Object> formData, Map<String, Object> userFill, Map<String, Object> requestBody) {
        this.formData = formData;
        this.userFill = userFill;
        this.requestBody = requestBody;
    }

    public Map<String, Object> getFormData() {
        return formData;
    }

    public void setFormData(Map<String, Object> formData) {
        this.formData = formData;
    }

    public Map<String, Object> getUserFill() {
        return userFill;
    }

    public void setUserFill(Map<String, Object> userFill) {
        this.userFill = userFill;
    }

    public Map<String, Object> getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(Map<String, Object> requestBody) {
        this.requestBody = requestBody;
    }
}