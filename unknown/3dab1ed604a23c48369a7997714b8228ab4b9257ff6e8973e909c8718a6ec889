package com.alipay.codegencore.service.middle.drm;

import com.alipay.codegencore.service.ideaevo.VatPlanType;
import com.alipay.codegencore.service.ideaevo.VatSearchType;
import com.alipay.drm.client.api.annotation.DAttribute;
import com.alipay.drm.client.api.annotation.DResource;
import com.alipay.sofa.specs.annotation.drm.DrmAttributeSpec;
import com.alipay.sofa.specs.annotation.drm.DrmResourceSpec;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0 2024/8/27 11:35
 */
@Component
@DResource(id = "com.alipay.codegencore.svatdrmconfig")
@DrmResourceSpec(name = "SVAT相关的DRM配置")
public class SvatDrmConfig {

    @DAttribute
    @DrmAttributeSpec(name = "代码生成允许最大行数")
    private int codeGenAllowMaxLines = 1000;

    @DAttribute
    @DrmAttributeSpec(name = "plan生成屏蔽步骤关键词")
    private String planStepBlackKeyword = ".*(不需要|无需修改).*";

    @DAttribute
    @DrmAttributeSpec(name = "plan生成搜索lexcial")
    private String lexcialIndexConfig = "mm_chunk_level_codesage_512_path";

    @DAttribute
    @DrmAttributeSpec(name = "plan生成搜索lexcial召回size")
    private Integer lexcialRecallSize = 10;

    @DAttribute
    @DrmAttributeSpec(name = "plan生成搜索embedding")
    private String embeddingIndexConfig = "mm_chunk_level_codesage_512_path";

    @DAttribute
    @DrmAttributeSpec(name = "plan生成搜索embedding召回size")
    private Integer embeddingRecallSize = 10;

    @DAttribute
    @DrmAttributeSpec(name = "rerank模型名称")
    private String rerankName = "JINA_RERANKER_V2_BASE_MULTI";

    @DAttribute
    @DrmAttributeSpec(name = "plan生成最大token数")
    private Long planInputMaxTokens = 20000L;

    @DAttribute
    @DrmAttributeSpec(name = "plan生成查询时文件白名单")
    private String searchResultFileSuffixWhite = "java";

    @DAttribute
    @DrmAttributeSpec(name = "搜索类型，指定走哪个搜索方案")
    private String searchType = VatSearchType.V2_ANSWER.name();

    @DAttribute
    @DrmAttributeSpec(name = "plan类型，指定走哪个plna生成方案")
    private String planType = VatPlanType.PART_GEN.name();

    @DAttribute
    @DrmAttributeSpec(name = "索引构建，taskName")
    private String codeSearchTaskName = "VAT_SEARCH_V2";

    @DAttribute
    @DrmAttributeSpec(name = "代码生成最大token数")
    private int codeGenMaxTokens = 20000;

    @DAttribute
    @DrmAttributeSpec(name = "代码生成是否添加行号")
    private boolean codeGenAddLineNumber = true;

    @DAttribute
    @DrmAttributeSpec(name = "svat代码生成prompt模版名称")
    private String codeGenPromptName = "svat_code_gen_prompt";

    @DAttribute
    @DrmAttributeSpec(name = "svatplan生成prompt模板名称")
    private String planGenPromptName = "plan_gen_json";

    @DAttribute
    @DrmAttributeSpec(name = "接口变更plan生成prompt模板")
    private String httpModifyPlanGenPrompt = "角色设定：你是一位资深的Java开发工程师，专注于根据用户的具体需求和提供的代码信息，分析并生成精确的代码修改或实现步骤。你的专业领域覆盖整个应用架构，从Controller层到Service层，乃至DAO层。你的核心职责在于深入分析用户提交的代码，判断其是否需要调整，并在必要时制定详尽的修改计划。\n" +
            "任务描述：你的主要任务源自用户的具体需求，结合他们提供的代码片段，你需细致分析与主文件相关的用户提供的文件，判断这些文件是否需要根据需求进行修改。如果判定需要修改，你有义务制定出每一步骤的代码实现细节；反之，若文件无需改动，应明确标注“不需要”。\n" +
            "输入格式：\n" +
            "- 用户需求：用户提出的具体需求描述\n" +
            "- 文件路径：所涉及的代码文件路径\n" +
            "- 文件类型：标明文件的角色，当文件被标记为主文件时，你必须为其制定实现步骤。\n" +
            "- 代码片段：具体的代码\n" +
            "输出格式：返回一个JSON数组，其中每个元素包含以下字段：\n" +
            "- path：代码文件的路径。\n" +
            "- plan：针对该文件的具体实现步骤，如果判断无需修改，则输出“不需要”。\n" +
            "输出样例：\n" +
            "```json\n" +
            "[\n" +
            "    {\n" +
            "        \"path\": \"/src/main/java/com/example/project/controller/UserController.java\",\n" +
            "        \"plan\": \"生成具体的代码实现步骤\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"path\": \"/src/main/resources/application.properties\",\n" +
            "        \"plan\": \"不需要\"\n" +
            "    }\n" +
            "]\n" +
            "\n" +
            "```\n" +
            "注意事项：\n" +
            "- 用户提供的主文件与其他文件间存在内在联系，在制定修改计划时务必考虑其相互作用。\n" +
            "- 处理主文件时，制定实现步骤是硬性要求；对于其他文件，需根据代码内容判断是否需要生成修改步骤。\n" +
            "- 输出格式须严格遵守示例模板，确保输出结果的自动化处理与解析无障碍。\n" +
            "- 所有生成的代码实现步骤均应为中文表述，清晰且具体。\n" +
            "- 生成结果必须保证存在于用户提供的文件路径中，不要生成用户没有提供的！\n" +
            "以下是用户输入信息：\n" +
            "用户需求：\"${requirement}\"\n" +
            "文件路径: \"${mainPath}\"\n" +
            "文件类型：主文件\n" +
            "代码片段：\"\"\"${mainMethodContent}\"\"\"\n" +
            "文件路径: \"${servicePath}\"\n" +
            "代码片段: \"\"\"${serviceContent}\"\"\"";

    @DAttribute
    @DrmAttributeSpec(name = "plan生成新方案模板")
    private String planGenNewPrompt = "用户需求：\n" +
            "<#list codeList as code>\n" +
            "文件路径：${code_path}\n" +
            "代码片段：${code_content}\n" +
            "</#list>\n" +
            "角色设定：作为一位经验丰富的Java开发专家，你的专长在于深入理解和重构代码，以满足用户提出的特定需求。你的工作范围涵盖整个应用程序架构，从后端逻辑到前端交互，无所不包。当用户提交代码及其需求时，你的首要任务是精确分析用户提供的代码中，哪些代码需要修改，如果需要，你将负责设计一套详尽的代码修改方案。\n" +
            "任务描述：基于用户提出的需求以及他们分享的代码片段，你的核心使命是甄别出哪些文件是关键修改对象。对于需要调整的文件，你必须精心规划每一个修改步骤，确保它们既符合用户需求又保持代码的高质量。而对于那些与当前需求无关的文件，直接抛弃，不需要再生成任何内容。\n" +
            "输入格式：\n" +
            "- 用户需求：用户提出的具体需求描述\n" +
            "- 文件路径：所涉及的代码文件路径\n" +
            "- 代码片段：具体的代码\n" +
            "输出格式：返回一个JSON数组，其中每个元素包含以下字段：\n" +
            "- path：代码文件的路径。\n" +
            "- plan：针对该文件的具体实现步骤\n" +
            "输出样例：\n" +
            "```json\n" +
            "[\n" +
            "    {\n" +
            "        \"path\": \"/src/main/java/com/example/project/controller/UserController.java\",\n" +
            "        \"plan\": \"生成具体的代码实现步骤\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"path\": \"/src/main/java/com/example/project/controller/UserTaskInfoImpl.java\",\n" +
            "        \"plan\": \"生成具体的代码实现步骤\"\n" +
            "    }\n" +
            "]\n" +
            "\n" +
            "```\n" +
            "注意事项：\n" +
            "- 用户提供的文件需要过滤，确保只对与需求直接相关的文件进行修改，无需修改的文件不需要输出！\n" +
            "- 相关文件之间可能会存在一些联系，在制定修改计划时务必考虑其相互作用！\n" +
            "- 输出格式须严格遵守示例模板，确保输出结果的自动化处理与解析无障碍！\n" +
            "- 所有生成的代码实现步骤均应为中文表述，清晰且具体！\n" +
            "- 生成结果必须保证存在于用户提供的文件路径中，不要生成用户没有提供的！\n" +
            "- 生成的结果只需要以JSON格式返回，其他多余信息不需要！\n" +
            "- 如果代码跟用户需求相关，但是无需修改代码，这种也不要输出！";


    public int getCodeGenAllowMaxLines() {
        return codeGenAllowMaxLines;
    }

    public void setCodeGenAllowMaxLines(int codeGenAllowMaxLines) {
        this.codeGenAllowMaxLines = codeGenAllowMaxLines;
    }

    public String getHttpModifyPlanGenPrompt() {
        return httpModifyPlanGenPrompt;
    }

    public void setHttpModifyPlanGenPrompt(String httpModifyPlanGenPrompt) {
        this.httpModifyPlanGenPrompt = httpModifyPlanGenPrompt;
    }

    public String getPlanStepBlackKeyword() {
        return planStepBlackKeyword;
    }

    public void setPlanStepBlackKeyword(String planStepBlackKeyword) {
        this.planStepBlackKeyword = planStepBlackKeyword;
    }

    public String getLexcialIndexConfig() {
        return lexcialIndexConfig;
    }

    public void setLexcialIndexConfig(String lexcialIndexConfig) {
        this.lexcialIndexConfig = lexcialIndexConfig;
    }

    public Integer getLexcialRecallSize() {
        return lexcialRecallSize;
    }

    public void setLexcialRecallSize(Integer lexcialRecallSize) {
        this.lexcialRecallSize = lexcialRecallSize;
    }

    public String getEmbeddingIndexConfig() {
        return embeddingIndexConfig;
    }

    public void setEmbeddingIndexConfig(String embeddingIndexConfig) {
        this.embeddingIndexConfig = embeddingIndexConfig;
    }

    public Integer getEmbeddingRecallSize() {
        return embeddingRecallSize;
    }

    public void setEmbeddingRecallSize(Integer embeddingRecallSize) {
        this.embeddingRecallSize = embeddingRecallSize;
    }

    public String getRerankName() {
        return rerankName;
    }

    public void setRerankName(String rerankName) {
        this.rerankName = rerankName;
    }

    public Long getPlanInputMaxTokens() {
        return planInputMaxTokens;
    }

    public void setPlanInputMaxTokens(Long planInputMaxTokens) {
        this.planInputMaxTokens = planInputMaxTokens;
    }

    public String getPlanGenNewPrompt() {
        return planGenNewPrompt;
    }

    public void setPlanGenNewPrompt(String planGenNewPrompt) {
        this.planGenNewPrompt = planGenNewPrompt;
    }

    public String getSearchResultFileSuffixWhite() {
        return searchResultFileSuffixWhite;
    }

    public void setSearchResultFileSuffixWhite(String searchResultFileSuffixWhite) {
        this.searchResultFileSuffixWhite = searchResultFileSuffixWhite;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public String getPlanType() {
        return planType;
    }

    public void setPlanType(String planType) {
        this.planType = planType;
    }

    public String getCodeSearchTaskName() {
        return codeSearchTaskName;
    }

    public void setCodeSearchTaskName(String codeSearchTaskName) {
        this.codeSearchTaskName = codeSearchTaskName;
    }

    public int getCodeGenMaxTokens() {
        return codeGenMaxTokens;
    }

    public void setCodeGenMaxTokens(int codeGenMaxTokens) {
        this.codeGenMaxTokens = codeGenMaxTokens;
    }

    public boolean isCodeGenAddLineNumber() {
        return codeGenAddLineNumber;
    }

    public void setCodeGenAddLineNumber(boolean codeGenAddLineNumber) {
        this.codeGenAddLineNumber = codeGenAddLineNumber;
    }

    public String getCodeGenPromptName() {
        return codeGenPromptName;
    }

    public void setCodeGenPromptName(String codeGenPromptName) {
        this.codeGenPromptName = codeGenPromptName;
    }

    public String getPlanGenPromptName() {
        return planGenPromptName;
    }

    public void setPlanGenPromptName(String planGenPromptName) {
        this.planGenPromptName = planGenPromptName;
    }
}
