package com.alipay.codegencore.service.ideaevo;

import org.junit.Assert;
import org.junit.Test;

import java.util.Optional;

public class ModelRequestHandlerTest {


    @Test
    public void testParseCodeBlocks() {

        String content =
                "假设`Controller`中存在如下代码片段：\n" +
                "```java\n" +
                "try {\n" +
                "   //代码中处理党费缴纳日期的逻辑\n" +
                "} catch (Exception e) {\n" +
                "   throw new RuntimeEx()\n" +
                "}\n" +
                "```\n" +
                "\n" +
                "根据用户需求，可以生成如下修改计划：\n" +
                "```json\n" +
                "{\n" +
                "   \"plan_nums\": 1,\n" +
                "   \"plan_info\": [\n" +
                "       {\n" +
                "           \"path\": \"app/biz\",\n" +
                "           \"plan\": \"1.在文件顶部\"\n" +
                "       }\n" +
                "   ]\n" +
                "}\n" +
                "```\n";

        Optional<String> optional = ModelRequestHandler.parsePlanBlocks(content);
        Assert.assertTrue(optional.isPresent());
        Assert.assertTrue(optional.get().startsWith("{"));

        Optional<String> codeOptional = ModelRequestHandler.parseCodeBlocks(content);
        Assert.assertTrue(codeOptional.isPresent());
        Assert.assertTrue(codeOptional.get().startsWith("try"));
    }


}