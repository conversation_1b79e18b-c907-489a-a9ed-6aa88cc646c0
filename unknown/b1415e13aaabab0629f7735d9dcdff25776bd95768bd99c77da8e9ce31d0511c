package com.alipay.codegencore.model.domain;

import com.alipay.codegencore.model.enums.UserStatusEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户对象do
 */
public class UserAuthDO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.id
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.gmt_create
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.gmt_modified
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.user_name
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String userName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.token
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String token;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.bu_name
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String buName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.emp_id
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String empId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.admin
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private Byte admin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.alipay_account
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String alipayAccount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.status
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private UserStatusEnum status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.phone_number
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String phoneNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.save_scenes
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String saveScenes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.top_session_uids
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String topSessionUids;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.application_reason
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private String applicationReason;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_user_auth.allow_access_type
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    private Integer allowAccessType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.id
     *
     * @return the value of cg_user_auth.id
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.id
     *
     * @param id the value for cg_user_auth.id
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.gmt_create
     *
     * @return the value of cg_user_auth.gmt_create
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.gmt_create
     *
     * @param gmtCreate the value for cg_user_auth.gmt_create
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.gmt_modified
     *
     * @return the value of cg_user_auth.gmt_modified
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.gmt_modified
     *
     * @param gmtModified the value for cg_user_auth.gmt_modified
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.user_name
     *
     * @return the value of cg_user_auth.user_name
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getUserName() {
        return userName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.user_name
     *
     * @param userName the value for cg_user_auth.user_name
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.token
     *
     * @return the value of cg_user_auth.token
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getToken() {
        return token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.token
     *
     * @param token the value for cg_user_auth.token
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setToken(String token) {
        this.token = token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.bu_name
     *
     * @return the value of cg_user_auth.bu_name
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getBuName() {
        return buName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.bu_name
     *
     * @param buName the value for cg_user_auth.bu_name
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setBuName(String buName) {
        this.buName = buName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.emp_id
     *
     * @return the value of cg_user_auth.emp_id
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getEmpId() {
        return empId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.emp_id
     *
     * @param empId the value for cg_user_auth.emp_id
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setEmpId(String empId) {
        this.empId = empId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.admin
     *
     * @return the value of cg_user_auth.admin
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public Byte getAdmin() {
        return admin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.admin
     *
     * @param admin the value for cg_user_auth.admin
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setAdmin(Byte admin) {
        this.admin = admin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.alipay_account
     *
     * @return the value of cg_user_auth.alipay_account
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getAlipayAccount() {
        return alipayAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.alipay_account
     *
     * @param alipayAccount the value for cg_user_auth.alipay_account
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setAlipayAccount(String alipayAccount) {
        this.alipayAccount = alipayAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.status
     *
     * @return the value of cg_user_auth.status
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public UserStatusEnum getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.status
     *
     * @param status the value for cg_user_auth.status
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setStatus(UserStatusEnum status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.phone_number
     *
     * @return the value of cg_user_auth.phone_number
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getPhoneNumber() {
        return phoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.phone_number
     *
     * @param phoneNumber the value for cg_user_auth.phone_number
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.save_scenes
     *
     * @return the value of cg_user_auth.save_scenes
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getSaveScenes() {
        return saveScenes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.save_scenes
     *
     * @param saveScenes the value for cg_user_auth.save_scenes
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setSaveScenes(String saveScenes) {
        this.saveScenes = saveScenes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.top_session_uids
     *
     * @return the value of cg_user_auth.top_session_uids
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getTopSessionUids() {
        return topSessionUids;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.top_session_uids
     *
     * @param topSessionUids the value for cg_user_auth.top_session_uids
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setTopSessionUids(String topSessionUids) {
        this.topSessionUids = topSessionUids;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.application_reason
     *
     * @return the value of cg_user_auth.application_reason
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public String getApplicationReason() {
        return applicationReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.application_reason
     *
     * @param applicationReason the value for cg_user_auth.application_reason
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setApplicationReason(String applicationReason) {
        this.applicationReason = applicationReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_user_auth.allow_access_type
     *
     * @return the value of cg_user_auth.allow_access_type
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public Integer getAllowAccessType() {
        return allowAccessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_user_auth.allow_access_type
     *
     * @param allowAccessType the value for cg_user_auth.allow_access_type
     *
     * @mbg.generated Tue Oct 17 16:33:42 CST 2023
     */
    public void setAllowAccessType(Integer allowAccessType) {
        this.allowAccessType = allowAccessType;
    }
}