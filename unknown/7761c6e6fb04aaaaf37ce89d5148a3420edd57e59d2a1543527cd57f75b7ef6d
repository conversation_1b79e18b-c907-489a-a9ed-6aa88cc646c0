package com.alipay.codegencore.web.openapi;

import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.service.codegpt.ChatMessageService;
import com.alipay.codegencore.service.codegpt.ChatSessionManageService;
import com.alipay.codegencore.service.codegpt.user.CodeFuseUserAuthService;
import com.alipay.codegencore.service.common.UserAclService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 公司内部系统接口controller
 *
 * <AUTHOR>
 * 创建时间 2022-05-09
 */
@RestController
@RequestMapping("/api/thirdPart")
@Slf4j
public class ChatThirdPartController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChatThirdPartController.class);

    @Resource
    private UserAclService userAclService;

    @Resource
    private ChatMessageService chatMessageService;

    @Resource
    private ChatSessionManageService chatSessionManageService;

    @Resource
    private CodeFuseUserAuthService codeFuseUserAuthService;

    /**
     * 获取会话内容，包含合法的message消息
     *
     * @param empId 工号，创建codefuse用户工号
     * @param sessionUid 会话Id， 用户创建的会话时对应sessionId， 由服务端产生
     * @param user 公司内部系统信息， 用于系统鉴权使用
     * @param token token， 上游系统的token信息，做系统鉴权
     * @return
     */
//    @GetMapping(path = "/getUserMessageList")
//    public BaseResponse<List<ChatMessageDO>> getUserMessageList(HttpServletRequest httpServletRequest,
//                                                                @RequestParam(value = "empId") String empId,
//                                                                @RequestParam(value = "sessionUid") String sessionUid,
//                                                                @RequestHeader(value = "user") String user,
//                                                                @RequestHeader(value = "token") String token) {
//        if (LOGGER.isInfoEnabled()) {
//            LOGGER.info("thirdPart getUserMessageList, empId:{}, sessionUid: {}", empId, sessionUid);
//        }
//        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
//        if (!userAclService.isAuthorizedByToken(user, token, uri)) {
//            LOGGER.warn("thirdPart getUserMessageList, empId:{}, sessionUid: {}, invalid user or token", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.NO_AUTH);
//        }
//
//        Long userId = codeFuseUserAuthService.empId2UserId(empId);
//        if(null == userId) {
//            LOGGER.warn("thirdPart getUserMessageList, empId:{}, sessionUid: {}, no auth record existed", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
//        }
//
//        if(!isValidSession(sessionUid)) {
//            LOGGER.warn("thirdPart getUserMessageList, empId:{}, sessionUid: {}, invalid session info", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.SESSION_IS_NOT_EXIST);
//        }
//
//        List<ChatMessageDO> chatMessageDOList = chatMessageService.listChatMessage(sessionUid, false, true);
//        if(CollectionUtils.isEmpty(chatMessageDOList)) {
//            LOGGER.warn("thirdPart getUserMessageList, empId:{}, sessionUid: {}, query result is empty", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.SESSION_IS_NOT_EXIST);
//
//        }
//
//        if(!isSameUser(chatMessageDOList.get(0), userId.toString())) {
//            LOGGER.warn("thirdPart getUserMessageList, empId:{}, sessionUid: {}, user info mismatch", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.USER_SESSION_NOT_MATCH);
//        }
//
//        chatMessageService.updateCheckFailedContent(chatMessageDOList);
//
//        if(CollectionUtils.isEmpty(chatMessageDOList)) {
//            LOGGER.warn("thirdPart getUserMessageList, empId:{}, sessionUid: {}, after content checking, list is empty", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.SUCCESS);
//        }
//
//        updateMessageInfo(chatMessageDOList);
//
//        return BaseResponse.build(chatMessageDOList);
//    }

    /**
     * 获取消息数量相关数据
     * @param gmtCreateBegin 开始时间
     * @param gmtCreateEnd 结束时间
     * @param empId 工号
     * @param needEmpList 是否需要工号分组的list
     * @return
     */
//    @GetMapping(path = "/getMessageCount")
//    public BaseResponse<MaYiDevMessageCountModel> getUserMessageList(
//            HttpServletRequest httpServletRequest,
//            @RequestHeader(value = "user") String user,
//            @RequestHeader(value = "token") String token,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date gmtCreateBegin,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date gmtCreateEnd,
//            @RequestParam(required = false) String empId,
//            @RequestParam(required = false,defaultValue = "false") boolean needEmpList) {
//        LOGGER.info("thirdPart getMessageCount, empId:{}, gmtCreateBegin:{}, gmtCreateEnd:{}, needEmpList:{}", empId, gmtCreateBegin, gmtCreateEnd, needEmpList);
//        String uri = new UrlPathHelper().getLookupPathForRequest(httpServletRequest);
//        if (!userAclService.isAuthorizedByToken(user, token, uri)) {
//            LOGGER.warn("thirdPart getMessageCount,user:{} invalid user or token", user);
//            return BaseResponse.build(ResponseEnum.NO_AUTH);
//        }
//        return BaseResponse.build(chatMessageService.getMaYiDeveloperCount(gmtCreateBegin,gmtCreateEnd,empId,needEmpList));
//    }

    /**
     * 获取会话内容数据（不包含对于话详情）
     *
     * @param empId 工号，创建codefuse用户工号
     * @param sessionUid 会话Id， 用户创建的会话时对应sessionId， 由服务端产生
     * @param user 公司内部系统信息， 用于系统鉴权使用
     * @param token token， 上游系统的token信息，做系统鉴权
     * @return
     */
//    @GetMapping("/getSessionContent")
//    public BaseResponse<ChatSessionDO> getSessionContent(HttpServletRequest httpServletRequest,
//                                                                @RequestParam(value = "empId") String empId,
//                                                                @RequestParam(value = "sessionUid") String sessionUid,
//                                                                @RequestHeader(value = "user") String user,
//                                                                @RequestHeader(value = "token") String token) {
//        if (LOGGER.isInfoEnabled()) {
//            LOGGER.info("thirdPart getSessionContent, empId:{}, sessionUid: {}", empId, sessionUid);
//        }
//
//        if (!userAclService.isAuthorizedByToken(user, token, httpServletRequest.getRequestURI())) {
//            LOGGER.warn("thirdPart getSessionContent, empId:{}, sessionUid: {}, invalid user or token", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.NO_AUTH);
//        }
//
//        Long userId = codeFuseUserAuthService.empId2UserId(empId);
//        if(null == userId) {
//            LOGGER.warn("thirdPart getSessionContent, empId:{}, sessionUid: {}, no auth record existed", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.ILLEGAL_PARAMETER);
//        }
//
//        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionUid);
//        if(null == chatSessionDO) {
//            LOGGER.warn("thirdPart getSessionContent, empId:{}, sessionUid: {}, invalid session info", empId, sessionUid);
//            return BaseResponse.build(ResponseEnum.SESSION_IS_NOT_EXIST);
//        }
//
//        updateSessionInfo(chatSessionDO);
//
//        return BaseResponse.build(chatSessionDO);
//    }

    /**
     * 判断会话message是否和创建人一致
     * @param chatMessageDO， chatMessage对象
     * @param userId， 用户id
     * @return
     */
    private boolean isSameUser(ChatMessageDO chatMessageDO, String userId) {
        return userId.equalsIgnoreCase(Long.toString(chatMessageDO.getUserId()));
    }


    /**
     * 更新message中内部信息
     * @param chatMessageDOList， message对象列表
     */
    private void updateMessageInfo(List<ChatMessageDO> chatMessageDOList) {
        chatMessageDOList.forEach(c->{
            c.setId(0L);
            c.setUserId(0L);
        });
    }

    /**
     * 确认是否是一个合法的session信息
     * @param sessionId， sessionId
     * @return
     */
    private boolean isValidSession(String sessionId) {
        ChatSessionDO chatSessionDO = chatSessionManageService.getChatSession(sessionId);
        if(null == chatSessionDO) {
            return false;
        }
        return chatSessionDO.getDeleted() == 0;
    }

    /**
     * 更新session对象中内部信息
     * @param chatSessionDO， chatSessionDO信息
     */
    private void updateSessionInfo(ChatSessionDO chatSessionDO) {
        chatSessionDO.setId(0L);
        chatSessionDO.setUserId(0L);
    }
}
