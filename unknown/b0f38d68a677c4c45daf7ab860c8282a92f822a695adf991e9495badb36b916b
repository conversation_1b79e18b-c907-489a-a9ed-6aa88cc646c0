package com.alipay.codegencore.model.domain;

import java.util.Date;

public class RateLimitDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.id
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.gmt_create
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.gmt_modified
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.enable
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Boolean enable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.need_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Boolean needLimit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.stop
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Boolean stop;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.ignore_list
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private String ignoreList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.window_time_mills
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Integer windowTimeMills;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.window_total_quota
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Integer windowTotalQuota;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.type
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.caller
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private String caller;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.target
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private String target;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.template
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private String template;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.sorted
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Integer sorted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.mark_priority
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Byte markPriority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.priority_config
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private String priorityConfig;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.create_user_id
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private Long createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cg_rate_limit.remark
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    private String remark;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.id
     *
     * @return the value of cg_rate_limit.id
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.id
     *
     * @param id the value for cg_rate_limit.id
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.gmt_create
     *
     * @return the value of cg_rate_limit.gmt_create
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.gmt_create
     *
     * @param gmtCreate the value for cg_rate_limit.gmt_create
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.gmt_modified
     *
     * @return the value of cg_rate_limit.gmt_modified
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.gmt_modified
     *
     * @param gmtModified the value for cg_rate_limit.gmt_modified
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.enable
     *
     * @return the value of cg_rate_limit.enable
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.enable
     *
     * @param enable the value for cg_rate_limit.enable
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.need_limit
     *
     * @return the value of cg_rate_limit.need_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Boolean getNeedLimit() {
        return needLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.need_limit
     *
     * @param needLimit the value for cg_rate_limit.need_limit
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setNeedLimit(Boolean needLimit) {
        this.needLimit = needLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.stop
     *
     * @return the value of cg_rate_limit.stop
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Boolean getStop() {
        return stop;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.stop
     *
     * @param stop the value for cg_rate_limit.stop
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setStop(Boolean stop) {
        this.stop = stop;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.ignore_list
     *
     * @return the value of cg_rate_limit.ignore_list
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public String getIgnoreList() {
        return ignoreList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.ignore_list
     *
     * @param ignoreList the value for cg_rate_limit.ignore_list
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setIgnoreList(String ignoreList) {
        this.ignoreList = ignoreList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.window_time_mills
     *
     * @return the value of cg_rate_limit.window_time_mills
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Integer getWindowTimeMills() {
        return windowTimeMills;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.window_time_mills
     *
     * @param windowTimeMills the value for cg_rate_limit.window_time_mills
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setWindowTimeMills(Integer windowTimeMills) {
        this.windowTimeMills = windowTimeMills;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.window_total_quota
     *
     * @return the value of cg_rate_limit.window_total_quota
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Integer getWindowTotalQuota() {
        return windowTotalQuota;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.window_total_quota
     *
     * @param windowTotalQuota the value for cg_rate_limit.window_total_quota
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setWindowTotalQuota(Integer windowTotalQuota) {
        this.windowTotalQuota = windowTotalQuota;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.type
     *
     * @return the value of cg_rate_limit.type
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.type
     *
     * @param type the value for cg_rate_limit.type
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.caller
     *
     * @return the value of cg_rate_limit.caller
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public String getCaller() {
        return caller;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.caller
     *
     * @param caller the value for cg_rate_limit.caller
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setCaller(String caller) {
        this.caller = caller;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.target
     *
     * @return the value of cg_rate_limit.target
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public String getTarget() {
        return target;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.target
     *
     * @param target the value for cg_rate_limit.target
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setTarget(String target) {
        this.target = target;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.template
     *
     * @return the value of cg_rate_limit.template
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public String getTemplate() {
        return template;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.template
     *
     * @param template the value for cg_rate_limit.template
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setTemplate(String template) {
        this.template = template;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.sorted
     *
     * @return the value of cg_rate_limit.sorted
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Integer getSorted() {
        return sorted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.sorted
     *
     * @param sorted the value for cg_rate_limit.sorted
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setSorted(Integer sorted) {
        this.sorted = sorted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.mark_priority
     *
     * @return the value of cg_rate_limit.mark_priority
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Byte getMarkPriority() {
        return markPriority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.mark_priority
     *
     * @param markPriority the value for cg_rate_limit.mark_priority
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setMarkPriority(Byte markPriority) {
        this.markPriority = markPriority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.priority_config
     *
     * @return the value of cg_rate_limit.priority_config
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public String getPriorityConfig() {
        return priorityConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.priority_config
     *
     * @param priorityConfig the value for cg_rate_limit.priority_config
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setPriorityConfig(String priorityConfig) {
        this.priorityConfig = priorityConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.create_user_id
     *
     * @return the value of cg_rate_limit.create_user_id
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public Long getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.create_user_id
     *
     * @param createUserId the value for cg_rate_limit.create_user_id
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column cg_rate_limit.remark
     *
     * @return the value of cg_rate_limit.remark
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column cg_rate_limit.remark
     *
     * @param remark the value for cg_rate_limit.remark
     *
     * @mbg.generated Fri Dec 29 11:32:12 CST 2023
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}