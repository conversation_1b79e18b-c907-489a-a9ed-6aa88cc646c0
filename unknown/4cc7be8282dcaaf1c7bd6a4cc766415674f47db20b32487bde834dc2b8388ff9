package com.alipay.codegencore.service.gptcache.bean;

import com.alipay.codegencore.model.openai.ChatMessage;

import java.util.List;

/**
 * 封装gptCache请求的的返回值
 *
 * <AUTHOR>
 * 2023年06月07日11:55:20
 */
public class GptCacheResponse {
    private String answer;
    private List<ChatMessage> hitQuery;

    /**
     * GptCacheResponse的构造函数
     * @param cacheStr 返回的cache内容
     * @param questionStr 命中的问题
     */
    public GptCacheResponse(String cacheStr, List<ChatMessage> query) {
        this.answer = cacheStr;
        this.hitQuery = query;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public List<ChatMessage> getHitQuery() {
        return hitQuery;
    }

    public void setHitQuery(List<ChatMessage> hitQuery) {
        this.hitQuery = hitQuery;
    }
}
