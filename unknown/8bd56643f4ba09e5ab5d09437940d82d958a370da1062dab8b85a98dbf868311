package com.alipay.codegencore.service.ideaevo.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.openai.RepoInfo;
import com.alipay.codegencore.service.ideaevo.VatSearchType;
import com.alipay.codegencore.utils.code.BloopSearchClient;
import org.junit.Test;

import java.util.List;

public class ActionGenCodeServiceImplTest {

    String code = "{\n" +
            "    \"success\": true,\n" +
            "    \"data\":\n" +
            "    [\n" +
            "        {\n" +
            "            \"path\": \"app/service/workbench/src/main/java/com/alipay/hrworkprod/service/workbench/common/enums/DiamondKey.java\",\n" +
            "            \"startLine\": 129,\n" +
            "            \"endLine\": 207,\n" +
            "            \"content\": \"\\n    HRWORKBENCH_COMMON_APP_NAME(\\\"hrworkbench_common_app_name\\\", \\\"通用应用的 accessKey, 获取ACL 角色 接口用 \\\"),\\n    COMMON_APP_PARAMS(\\\"hrworkbench_common_app_params\\\", \\\"通用角色应用ID，accesskey ,操作等参数 \\\"),\\n\\n    HRWORKBENCH_DB_SYNC_CONFIG(\\\"hrworkbench.dbSync.config\\\", \\\"同步数据的表和字段范围配置\\\"),\\n\\n    OTHERGROUP_VIRTINPS(\\\"masterdata.otherGroup.virtInPs\\\", \\\"虚拟公司\\\"),\\n\\n    MASTERDATA_EXCEPTION_CONTACTMAIl(\\\"masterdata.exception.contactMail\\\", \\\"异常联系邮件\\\"),\\n\\n//    HRWORKBENCH_SWITCH_PS_TO_IHOME(\\\"hrworkbench.switch.ps.to.ihome\\\", \\\"工作台开关,从PS切换到iHome接口\\\"),\\n//    HRWORKBENCH_SWITCH_PS_TO_PAYROLL(\\\"hrworkbench.switch.ps.to.payroll\\\", \\\"工作台开关,从PS切换到薪资接口\\\"),\\n//    HRWORKBENCH_SWITCH_COMPAREWALFPROFILE_ISCONTINUE(\\\"hrworkbench.switch.comparewalfprofile.iscontinue\\\", \\\"工作台开关,是否与PS继续比较数据\\\"),\\n\\n    HRWORKBENCH_ANNIVERSASY_BU(\\\"masterdata.anniversary.bu\\\", \\\"周年庆BU列表\\\"),\\n//    HRWORKBENCH_PS_OFFLINE_SWITCH(\\\"hrworkbench.ps.offline.switch\\\", \\\"ps下线开关\\\"),\\n\\n    HRWORKBENCH_DELEGATEORDERURL(\\\"recruit.fbi.delegateOrderUrl\\\", \\\"beidiaoUrl\\\"),\\n\\n    HRWORKBENCH_PROBATION_CORPINITDATE(\\\"hrworkbench.probation.CorpInitDate\\\", \\\"接入晋升系统的灵界时间点，用于判断分水岭\\\"),\\n\\n    HRWORKBENCH_UPLOADPERSONALPHOTO_SHOWCORP(\\\"masterdata.uploadPersonalPhoto.showCorp\\\", \\\"\\\"),\\n\\n    HRWORKBENCH_PHYSICAL_MAIL(\\\"masterdata.registerCancle.tiJian.mail\\\", \\\"\\\"),\\n\\n    HRWORKBENCH_UPLOADPERSONALPHOTO(\\\"masterdata.uploadPersonalPhoto.Path\\\", \\\"\\\"),\\n\\n    HRWORKBENCH_CONFIG_CONTROL_MAP( \\\"hrworkbench.config.control.map\\\" ,\\\"配置项控件配置\\\"),\\n    // fengsheng.lxc(2017-10-30): 新增工作台-部门管理-变更/删除 临时关闭开关\\n    HRWORKBENCH_DEPT_MANAGE_DISABLED(\\\"hrworkbench.deptManage.disabled\\\", \\\"工作台-部门管理-变更/删除 临时关闭开关\\\"),\\n    // fengsheng.lxc(2017-11-08): 通过配置方式触发新人入职邮件\\n    HRWORKBENCH_ENTRY_SEND_MAIL_CONFIG(\\\"hrworkbench.entrySendMail.config\\\", \\\"新人入职邮件配置\\\"),\\n\\n    HRWORKBENCH_HIDEJOBCODE( \\\"masterdata.JobModel.hideJobCode\\\" ,\\\"隐藏jobcode选项\\\"),\\n\\n    HRWORKBENCH_JS_LEGO(\\\"com.alibaba.hr.hrworkbench.js.lego\\\" ,\\\"乐高前端版本信息\\\"),\\n    HRWORKBENCH_DISPATCH_COUNTRY_LEVEL(\\\"hrworkbench.dispatch.country.level\\\",\\\"跨境派遣异地国家等级配置\\\"),\\n    HRWORKBENCH_DISPATCH_CORPLIST(\\\"hrworkbench.dispatch.corpList\\\",\\\"跨境派遣支持corp配置\\\"),\\n    HRWORKBENCH_DISPATCH_MAILLIST(\\\"hrworkbench.dispatch.mailList\\\",\\\"跨境派遣邮件配置\\\"),\\n    HRWORKBENCH_DISPATCH_VISITFREQUENCY_AMOUNT(\\\"hrworkbench.dispatch.visitfrequency.amount\\\",\\\"预估每次探亲往返费用\\\"),\\n//    HRWORKBENCH_DISPATCH_CAINIAO_VISA(\\\"hrworkbench.dispatch.cainiao.visa\\\",\\\"跨国派遣菜鸟工签配置\\\"),\\n\\n    // xuyanyan(2017-12-12):高德、UC不能查看JobModel界面\\n//    HRWORKBENCH_HIDECORP(\\\"masterdata.jobModel.hideCorp\\\",\\\"jobModel隐藏公司选项\\\"),\\n    REFUSE_RENEW_CONTRACT_MAIL_WORKNOS(\\\"hrworkbench.biz.timer.refuse.Contaract.mail.workNo\\\",\\\"劳动合同到期未提交离职预警收件人\\\"),\\n\\n    // Referer过滤器相关\\n    HRWORKBENCH_FILTER_REFERER_ONOFF(\\\"hrworkbench.filter.referer.on-off\\\", \\\"是否开启过滤功能，为on时候判断Referer是否在白名单中，而是否真正拦截还将受到debug参数控制\\\"),\\n    HRWORKBENCH_FILTER_REFERER_WHITELIST(\\\"hrworkbench.filter.referer.whiteList\\\", \\\"Referer的域名白名单定义\\\"),\\n\\n    HRWORKBENCH_TEMPLATE_ABTEST(\\\"hrworkbench.template.abtest\\\",\\\"新模版系统灰度测试配置\\\",true),\\n    HRWORKBENCH_TEMPLATE_ABTEST_BLACKLIST(\\\"hrworkbench.template.abtest.blacklist\\\",\\\"新模版系统灰度测试黑名单配置\\\"),\\n\\n    HRWORKBENCH_RECRUIT_BATCH_PRACTICE_HK(\\\"hrworkbench.recruit.batch.practice.hk\\\",\\\"香港实习生批次\\\"),\\n    /**\\n     * 跨国派遣相关的开关变量, 放到一起统一管理\\n     */\\n//    HRWORKBENCH_DISPATCH_SWITCHES(\\\"hrworkbench.dispatch.switches\\\",\\\"跨境派遣相关的开关变量\\\"),\\n\\n    // 流程可视化相关\\n    HRWORKBENCH_FLOWDIRVER_ENABLE_FORMKINDLIST(\\\"hrworkbench.flowdirver.enable.formkindlist\\\",\\\"工作台开通流程引擎的表单类型列表\\\"),\\n    HRWORKBENCH_FLOWDIRVER_COMPATIBILITY_SCREENLIST(\\\"hrworkbench.flowdirver.compatibility.screenlist\\\",\\\"老的兼容页面列表\\\"),\\n\\n    // 审批模型自我交付\\n    HRWORKBENCH_APPROVE_MODEL_LEVELS_SHARE(\\\"hrworkbench.approveModel.levels.share\\\", \\\"集团(share)层级配置\\\"),\\n    HRWORKBENCH_APPROVE_MODEL_ROLES(\\\"hrworkbench.approveModel.roles\\\", \\\"审批模型可选角色\\\"),\\n\\n    HRWORKBENCH_PAYROLLTOOL_SHOWEMPINFO(\\\"masterdata.payRollTool.showEmpInfo\\\", \\\"payRollTool 显示员工信息\\\"),\\n    HRWORKBENCH_DEPT_LEAVE_MANAGER_TRIGGER_MAIL_SIZE(\\\"masterdata.deptLeaveManagerTrigger.mailSize\\\",\\\"部门主管离职，发送邮件给hrg的邮件数目\\\"),\\n//    HRWORKBENCH_ODPS_SYNC_INFO(\\\"hrworkbench.odps.sync.info\\\",\\\"有数据安全节点odps同步信息\\\"),\\n\\n    /**\\n     * 用于组件化权限控制降权使用\\n     */\\n//    GATEWAY_HSF_ACCESS_SWITCH(\\\"gateway.hsf.access.switch\\\", \\\"组件化接口使用权限控制的开关\\\"),\\n\\n    ARMORY_API_DOMAIN(\\\"armory.api.domain\\\", \\\"armory系统api访问域名\\\"),\\n\",\n" +
            "            \"rerank_score\": 0.3638729155063629\n" +
            "        },\n" +
            "        {\n" +
            "            \"path\": \"app/service/workbench/src/main/java/com/alipay/hrworkprod/service/workbench/common/enums/DiamondKey.java\",\n" +
            "            \"startLine\": 416,\n" +
            "            \"endLine\": 463,\n" +
            "            \"content\": \"     * 职务, 职位, 层级查询接口切换开关\\n     */\\n//    JOBCODE_LEVEL_POST_MASTERDATA_INTERFACE_SWITCH(\\\"jobCodeLevelPost.masterdata.interface.switch\\\", \\\"主数据职务, 职位, 层级查询接口切换开关\\\", true),\\n\\n    /**\\n     * 蚂蚁范围内CorpNo列表\\n     */\\n    CORPNO_LIST_ANT(\\\"masterdata.common.ant.corpList\\\", \\\"蚂蚁范围内CorpNo列表\\\"),\\n\\n    /**\\n     * F需求邮件密送人\\n     */\\n    DIMISSION_F_MAIL_RECEIVERS(\\\"hrworkbench.dimission.f.mail.receivers\\\", \\\"F需求邮件密送人\\\"),\\n\\n    NICK_RECYCLE_CONFIG(\\\"nick.recycle.config\\\", \\\"花名回收配置\\\"),\\n//    NICK_STRANGE_WORD_CONFIG(\\\"nick.strange.word.config\\\", \\\"花名生僻字配置\\\"),\\n    NICK_CANCEL_URL(\\\"nick.cancel.url\\\", \\\"花名取消url\\\"),\\n    /** @deprecated 下面这个不再使用 */\\n//    NICK_CANCEL_EXCLUDE_CORPS(\\\"nick.cancel.exclude.corps\\\", \\\"花名取消排除的corps(用于花名回收、取消场景)\\\"),\\n//    NICK_CANCEL_EXCLUDE_ANT_CORPS(\\\"nick.cancel.exclude.ant.corps\\\", \\\"花名取消排除蚂蚁corps\\\"),\\n//    NICK_TRADITIONAL_WORD_CONFIG(\\\"nick.traditional.word.config\\\", \\\"花名繁体字配置\\\"),\\n//    ENTRY_MAPPER_LIST(\\\"hrworkbench.entry.mapper.list\\\", \\\"入职用到的所有mapper\\\"),\\n\\n//    ENTRY_TASK_SWITCH(\\\"hrworkbench.entry.task.switch\\\", \\\"入职消息开关\\\"),\\n\\n//    DIMISSION_2_PS_UPDATE_PS_KEY_SWITCH(\\\"hrworkbench.dimission2ps.update.pskey.switch\\\", \\\"离职同步到主数据jobplan，是否更新pskey开关\\\"),\\n\\n    HRWORKBENCH_ENTRY_WELFARE_CONFIG(\\\"hrworkbench.entry.welfare.config\\\", \\\"福利地相关配置\\\"),\\n//    SECRET_VALIDATE_SWITCH(\\\"hrworkbench.secret.validate.switch\\\",\\\"一个敏感数据返回接口的权限校验开关\\\"),\\n    /**\\n     * 入职同步主数据开关\\n     */\\n//    ENTRY_SYN_MASTERDATA_SWITCH(\\\"entry.syn.masterdata.switch\\\", \\\"入职同步主数据开关\\\"),\\n\\n    /**\\n     * entry标签系统 app Secret\\n     */\\n//    ENTRY_TAG_APP_SECRET(\\\"entry.tag.app.secret\\\", \\\"标签系统 app Secret\\\"),\\n\\n    /**\\n     * hrworkbench标签系统 app Secret\\n     */\\n//    HRWORKBENCH_TAG_APP_SECRET(\\\"hrworkbench.tag.app.secret\\\", \\\"标签系统 app Secret\\\"),\\n\\n\\n    HRWORKBENCH_DISPATCH_COUNTRY_AREA(\\\"hrworkbench.dispatch.country.area\\\",\\\"跨境派遣国家区域配置\\\"),\\n\",\n" +
            "            \"rerank_score\": 0.22000710666179657\n" +
            "        },\n" +
            "        {\n" +
            "            \"startLine\": 54,\n" +
            "            \"endLine\": 132,\n" +
            "            \"path\": \"app/service/workbench/src/main/java/com/alipay/hrworkprod/service/workbench/workbench/common/external/DocusignClient.java\",\n" +
            "            \"content\": \"     * The constant EMP_CLIENT_USER_ID.\\n     */\\n    public static final String EMP_CLIENT_USER_ID = \\\"2\\\";\\n\\t/**\\n\\t * 回调url的path。host放在数据库中。query需要上层调用函数加上去\\n\\t * \\n\\t */\\n\\tpublic static final String HRG_CALLBACK_PATH = \\\"workbench/overseas/entry/signEventCallback.htm\\\";\\n    /**\\n     * The constant EMP_CALLBACK_PATH.\\n     */\\n    public static final String EMP_CALLBACK_PATH = \\\"offer/signEventCallback.htm\\\";\\n\\n\\t/**\\n\\t * 获取签名后hrg的邮件发送地址\\n\\t */\\n\\tString getHrgEmail();\\n\\n    /**\\n     * Login login account.\\n     *\\n     * @return the login account\\n     */\\n    LoginAccount login();\\n\\n\\t/**\\n\\t * 限制：\\n\\t * 一个请求只能包涵一个签名人（Signer），一个文档（Document）\\n\\t * 因此，DEFAULT_RECIPIENT_ID，DEFAULT_CLIENT_USER_ID，DEFAULT_DOCUMENT_ID均为1\\n\\t * \\n\\t * 将签名的请求发送至Docusign系统\\n\\t * 请求包括 \\n\\t * 签名的文档，\\n\\t * 签名人的email和name，\\n\\t * 签名位置anchor\\n\\t * 返回值包括\\n\\t * envelopId，用在后面进行下载和删除\\n\\t * \\n\\t * @param request\\n\\t * @return\\n\\t */\\n\\t@Deprecated\\n\\tSignResponse send(SignRequest request);\\n\\t\\n\\t/**\\n\\t * 一个请求包括一个文档(Document)，多个签名人(Signer)\\n\\t * \\n\\t * @param request\\n\\t * @return\\n\\t */\\n\\tSignResponse send(SignRequest2 request);\\n\\t\\n\\t/**\\n\\t * 将查看文档的请求发送至Docusign系统\\n\\t * 请求包括：\\n\\t * 签名人的email和name\\n\\t * 签名的envelopeId，用于标识文档\\n\\t * accountId。\\n\\t */\\n\\tViewResponse send(ViewRequest request);\\n\\t\\n\\t/**\\n\\t * 查询签名的状态，返回一个布尔值，是否完成签名\\n\\t * 请求包括：\\n\\t * envelopeId\\n\\t * 用于标识签名人的recipientId，clientUserId\\n\\t * \\n\\t * @param request\\n\\t * @return\\n\\t */\\n\\tStatusResponse send(StatusRequest request);\\n\\t\\n\\t/**\\n\\t * 从Docusign系统上下载已经签名好的文档\\n\\t * 以字节数组的形式返回\\n\\t * @param envelopId 参考send\\n\\t * @return\\n\\t */\",\n" +
            "            \"lexcial_score\": 185.33057,\n" +
            "            \"rerank_score\": 0.*****************\n" +
            "        },\n" +
            "        {\n" +
            "            \"startLine\": 0,\n" +
            "            \"endLine\": 33,\n" +
            "            \"path\": \"app/service/workbench/src/main/java/com/alipay/hrworkprod/service/workbench/workbench/message/McMessageServiceHelper.java\",\n" +
            "            \"content\": \"package com.alipay.hrworkprod.service.workbench.workbench.message;\\n\\nimport java.util.List;\\n\\npublic interface McMessageServiceHelper {\\n\\n    /**\\n     * 使用taobao mc发送邮件 直接传入邮件的标题和内容，不依赖配置框架（比如bacardi或diamond)\\n     *\\n     * @param receiverList 邮件接收人列表,不能超过40个，taobao MC限制\\n     * @param emailFrom    邮件发件人,形如 \\\"全橙爱平台<<EMAIL>>\\\"\\n     * @param title        要发送的邮件标题\\n     * @param body         要发送的邮件内容,支持HTML\\n     * @param module       模块名，标示是哪个系统调用这个发邮件接口功能\\n     * @return ResultModel\\n     * <AUTHOR>     */\\n    ResultModel<String> sendEmail(List<String> receiverList, String emailFrom, String title,\\n                                  String body, String moudle);\\n\\n    /**\\n     * 使用TAOBAO MC中的通用模板发送短信\\n     *\\n     * @param receiverList 短信接收人手机号码列表, 不能超过20个，这个数量是taobao mc的限制\\n     * @param smsText      要发送的短信内容，如果短信内容超过大约60个字，会分割成多条短信\\n     * @param module       模块名，标示是哪个系统调用这个发短信接口功能\\n     * @return ResultModel\\n     */\\n    ResultModel<String> sendMobileMsg(List<String> receiverList, String smsText,\\n                                      String moudle);\\n\\n}\\n\",\n" +
            "            \"lexcial_score\": 168.85608,\n" +
            "            \"rerank_score\": 0.154057577252388\n" +
            "        },\n" +
            "        {\n" +
            "            \"startLine\": 103,\n" +
            "            \"endLine\": 194,\n" +
            "            \"path\": \"app/infrastructure/api/src/main/java/com/alipay/hrworkprod/infrastructure/api/hrssp/EntryExternalTaskService.java\",\n" +
            "            \"content\": \"     * @return\\n     */\\n    String willHandleCandidateSubmitResume();\\n\\n    /**\\n     * 候选人已经提交履历，驱动bpms流程\\n     *\\n     * @param formNo\\n     */\\n    void handleCandidateSubmittedResume(Integer formNo);\\n\\n    // ------------------------------------------------------------------------\\n    // 邀约报到\\n    // ------------------------------------------------------------------------\\n\\n    /**\\n     * SSC邀约报到\\n     *\\n     * NOTE：这个方法提供给hrssp后台配置，业务系统不要使用\\n     *\\n     * @return\\n     */\\n    String willHandleSscInviteToRegist();\\n\\n    /**\\n     * SSC已操作邀约报到，驱动bpms流程\\n     *\\n     * @param formNo\\n     */\\n    void handleSscInvitedToRegist(Integer formNo);\\n\\n    // ------------------------------------------------------------------------\\n    // 国内合同（发送合同/签署合同）\\n    // ------------------------------------------------------------------------\\n\\n    /**\\n     * SSC发送合同\\n     *\\n     * NOTE：这个方法提供给hrssp后台配置，业务系统不要使用\\n     *\\n     * @return\\n     */\\n    String willHandleSscSendContract();\\n\\n    /**\\n     * SSC已发送合同，驱动bpms流程\\n     *\\n     * @param formNo\\n     */\\n    void handleSscSentContract(Integer formNo);\\n\\n    /**\\n     * 候选人签署合同\\n     *\\n     * NOTE：这个方法提供给hrssp后台配置，业务系统不要使用\\n     *\\n     * @return\\n     */\\n    String willHandleCandidateSignContract();\\n\\n    /**\\n     * 候选人已签署合同，驱动bpms流程\\n     *\\n     * @param formNo\\n     */\\n    void handleCandidateFinishedContractSign(Integer formNo);\\n\\n    /**\\n     * HRG签署合同\\n     *\\n     * NOTE：这个方法提供给hrssp后台配置，业务系统不要使用\\n     *\\n     * @return\\n     */\\n    String willHandleHrSignContract();\\n\\n    /**\\n     * HRG已签署合同，驱动bpms流程\\n     *\\n     * @param formNo\\n     */\\n    void handleHrFinishedContractSign(Integer formNo);\\n\\n    /**\\n     * 候选人拒绝签署合同，驱动bpms流程\\n     *\\n     * @param formNo\\n     */\\n    void handleCandidateRejectedContractSign(Integer formNo);\\n\\n    // NOTE：下面两个方法本身是一个handleContractSignOnlineOfflineSwitch，调用后在工作流查询合同是否在线签署，\",\n" +
            "            \"lexcial_score\": 167.41327,\n" +
            "            \"rerank_score\": 0.14033624529838562\n" +
            "        },\n" +
            "        {\n" +
            "            \"path\": \"app/service/workbench/src/main/java/com/alipay/hrworkprod/service/workbench/workbench/common/external/impl/AntDocusignUtil.java\",\n" +
            "            \"startLine\": 0,\n" +
            "            \"endLine\": 78,\n" +
            "            \"content\": \"package com.alipay.hrworkprod.service.workbench.workbench.common.external.impl;\\n\\nimport com.alipay.antlescenter.common.service.facade.request.SignTaskDetailRequest;\\nimport com.alipay.antlescenter.common.service.facade.request.docusign.GetRecipientViewRequest;\\nimport com.alipay.hrworkprod.dal.workbench.domain.dispatch.EmpDispatch;\\nimport com.alipay.hrworkprod.service.workbench.common.FormType;\\nimport com.alipay.hrworkprod.service.workbench.common.util.FlowV2UrlUtils;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.impl.ParamsConfigUtils;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.dto.SignContext;\\nimport com.google.common.base.Strings;\\n\\npublic class AntDocusignUtil {\\n    private static final String SIGN_CALLBACK_PATH = \\\"/workbench/dispatch/signCallback.htm\\\";\\n    /**\\n     * 应用名，复用hrworkcore\\n     */\\n    public static final String APPLICATION_NAME = \\\"hrworkcore\\\";\\n\\n    /**\\n     * 电子签租户\\n     */\\n    public static final String TENANT = \\\"ANT_GROUP\\\";\\n\\n    /**\\n     * 场景code，共用入职系统的\\n     */\\n    public static final String SCENE_CODE = \\\"OVERSEAS_ENTY_DOCUSIGN\\\";\\n\\n    /**\\n     * 业务线code\\n     */\\n    public static final String BUSINESS_LINE_CODE = \\\"HR\\\";\\n\\n    public static final String CONTRACT_VERSION = \\\"0\\\";\\n\\n    /**\\n     * 蚂蚁法务OSS存储路径\\n     */\\n    public static final String OSS_PATH = \\\"ESIGN/ANT_CONSUMER/hrworkcore/dispatch\\\";\\n\\n    public static GetRecipientViewRequest convert(EmpDispatch dispatch,SignContext.Signer signer, String signTaskId){\\n        GetRecipientViewRequest getRecipientViewRequest = new GetRecipientViewRequest();\\n        getRecipientViewRequest.setSignTaskId(signTaskId);\\n        getRecipientViewRequest.setUserName(signer.getUsername());\\n        getRecipientViewRequest.setEmail(signer.getEmail());\\n        getRecipientViewRequest.setBizNo(dispatch.getOssKey());\\n        String actionType;\\n        if(\\\"3\\\".equals(signer.getClientUserId())){\\n            actionType =\\\"emp\\\";\\n        }else if(\\\"2\\\".equals(signer.getClientUserId())){\\n            actionType =\\\"host\\\";\\n        }else{\\n            actionType =\\\"home\\\";\\n        }\\n        String callback = String.format(\\\"%s%s?formNo=%d&actionType=%s\\\",\\n                ParamsConfigUtils.getWorkbenchDomain(), SIGN_CALLBACK_PATH, dispatch.getFormNo(),actionType);\\n        getRecipientViewRequest.setReturnUrl(callback);\\n        return getRecipientViewRequest;\\n    }\\n\\n    public static SignTaskDetailRequest buildSignTaskDetailRequest(String signTaskId){\\n        SignTaskDetailRequest signTaskDetailRequest = new SignTaskDetailRequest();\\n        signTaskDetailRequest.setSignTaskId(signTaskId);\\n        signTaskDetailRequest.setApplicationSystem(APPLICATION_NAME);\\n        signTaskDetailRequest.setTenant(TENANT);\\n        signTaskDetailRequest.setBusinessLineCode(BUSINESS_LINE_CODE);\\n        signTaskDetailRequest.setSceneCode(SCENE_CODE);\\n        return signTaskDetailRequest;\\n    }\\n\\n    public static String buildFileKey(String fileName){\\n        return String.format(\\\"%s/%s\\\",OSS_PATH,fileName);\\n    }\\n\\n    public static String buildFileName(String formNo,String fileId){\\n        return String.format(\\\"dispatch_%s_%s.pdf\\\",formNo,fileId);\\n    }\\n\",\n" +
            "            \"rerank_score\": 0.25982561707496643\n" +
            "        },\n" +
            "        {\n" +
            "            \"startLine\": 71,\n" +
            "            \"endLine\": 92,\n" +
            "            \"path\": \"app/web/src/main/java/com/alipay/hrworkprod/web/workbench/module/rpc/dispatch/DispatchRpc.java\",\n" +
            "            \"content\": \"import com.alipay.hrworkprod.service.workbench.workbench.common.external.DocusignClient;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.external.DocusignClient.ViewRequest;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.external.DocusignClient.ViewResponse;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.external.impl.AntDocusignUtil;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.impl.ParamsConfigUtils;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.EmpDispatchService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.dto.BenefitStandards;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.dto.DispatchBasicInfo;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.dto.DispatchTaskAction;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.dto.SignContext;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.dto.WorkbenchFlowDto;\\nimport com.alipay.hrworkprod.service.workbench.workbench.employee.EmpEmployeeJobPlanService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.employee.EmpEmployeeJobService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.employee.dept.EmpDepartmentService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.employee.emp.EmpPersonService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.jobsys.EmpJobLevelRpcService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.jobsys.EmpJobcodeNewService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.jobsys.EmpPostJobcodeLevelService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.permission.AclMasterdataService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.taskList.EmpTaskQueryService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.transfer.EmpEmployeeTransferListService;\",\n" +
            "            \"embedding_score\": 0.72262645,\n" +
            "            \"rerank_score\": 0.2240554392337799\n" +
            "        },\n" +
            "        {\n" +
            "            \"path\": \"app/web/src/main/java/com/alipay/hrworkprod/web/workbench/module/rpc/dispatch/DispatchRpc.java\",\n" +
            "            \"startLine\": 945,\n" +
            "            \"endLine\": 1037,\n" +
            "            \"content\": \"\\t\\tswitch (signStatus) {\\n\\t\\t\\tcase UN_SIGN: {\\n\\t\\t\\t\\t// 派出国hrg的签名链接\\n\\t\\t\\t\\tif (StringsUtils.differs(loginWorkNo, dispatch.getHrgWorkNo())) {\\n\\t\\t\\t\\t\\tthrow ex.setMessage(LanguageUtil.get(\\\"dispatch.error.signUrl\\\"));\\n\\t\\t\\t\\t}\\n\\n\\t\\t\\t\\ttry {\\n\\t\\t\\t\\t\\t// 这里使用同步方式，如果之前已签署但是系统处理失败了，可以立即处理并跳过后续getHomeHrgSignUrl\\n\\t\\t\\t\\t\\tdispatchTaskFactory.newCheckSignTask(dispatch).run();\\n\\t\\t\\t\\t\\tif (StringsUtils.differs(oldSignStatus, dispatch.getSignStatus())) {\\n\\t\\t\\t\\t\\t\\t// 已签署\\n\\t\\t\\t\\t\\t\\tLogUtil.warn(logger,\\\"getSignUrl signStatus changed from {} to {}\\\", oldSignStatus, dispatch.getSignStatus());\\n\\t\\t\\t\\t\\t\\treturn String.format(\\\"/workbench/dispatch/agreement/preview.do?formNo=%d\\\", dispatch.getFormNo());\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t} catch (Exception e) {\\n\\t\\t\\t\\t\\tLogUtil.error(logger,\\\"getSignUrl call CheckSignTask failed\\\", e);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\t// 这里会新建SignContext，也就是重新上传pdf、生成新的envelopeId，为了防止线程异步问题，上述调用使用了同步方式\\n\\t\\t\\t\\tSignContext context = dispatchBiz.getSignContext(dispatch);\\n\\t\\t\\t\\tif(context==null){\\n\\t\\t\\t\\t\\tcontext = dispatchBiz.createSignContext(dispatch);\\n\\t\\t\\t\\t\\tdispatchBiz.saveSignContext(context);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tfor (int i = 0; i < 10; i++) {\\n\\t\\t\\t\\t\\ttry {\\n\\t\\t\\t\\t\\t\\tTimeUnit.SECONDS.sleep(1);\\n\\t\\t\\t\\t\\t\\tCommonResult<DocusignSignDetail> result = aesDocuSignFacade.querySignResult(AntDocusignUtil.buildSignTaskDetailRequest(context.getEnvelopeId()));\\n\\t\\t\\t\\t\\t\\tif(result.isSuccess() && AntDocusignUtil.from(result.getData().getStatus())== AntDocusignUtil.SignTaskStatus.SIGNING){\\n\\t\\t\\t\\t\\t\\t\\tbreak;\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t} catch (InterruptedException e) {\\n\\t\\t\\t\\t\\t\\t//e.printStackTrace();\\n\\t\\t\\t\\t\\t\\tthrow ex.setMessage(e.getMessage());\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\treturn getHomeHrgSignUrl(dispatch);\\n\\t\\t\\t}\\n\\n\\t\\t\\tcase HOME_SIGN: {\\n\\t\\t\\t\\t// 派驻国hrg的签名链接\\n\\t\\t\\t\\tif (StringsUtils.differs(loginWorkNo, dispatch.getNewHrgWorkNo())) {\\n\\t\\t\\t\\t\\tthrow ex.setMessage(LanguageUtil.get(\\\"dispatch.error.signUrl\\\"));\\n\\t\\t\\t\\t}\\n\\n\\t\\t\\t\\tjdkExecutorService.submit(dispatchTaskFactory.newCheckSignTask(dispatch));\\n\\t\\t\\t\\treturn getHostHrgSignUrl(dispatch);\\n\\t\\t\\t}\\n\\n\\t\\t\\tcase HOST_SIGN: {\\n\\t\\t\\t\\t// 员工的签名链接\\n\\t\\t\\t\\tif (StringsUtils.differs(loginWorkNo, dispatch.getWorkNo())) {\\n\\t\\t\\t\\t\\tthrow ex.setMessage(LanguageUtil.get(\\\"dispatch.error.signUrl\\\"));\\n\\t\\t\\t\\t}\\n\\n\\t\\t\\t\\tjdkExecutorService.submit(dispatchTaskFactory.newCheckSignTask(dispatch));\\n\\t\\t\\t\\treturn getEmpSignUrl(dispatch);\\n\\t\\t\\t}\\n\\n\\t\\t\\tdefault:\\n\\t\\t\\t\\t// 签署完成后，签名的url统一返回预览的url\\n\\t\\t\\t\\tif (SetsUtils.asSet(dispatch.getWorkNo(), dispatch.getHrgWorkNo(), dispatch.getNewHrgWorkNo())\\n\\t\\t\\t\\t\\t\\t.contains(loginWorkNo)) {\\n\\t\\t\\t\\t\\treturn String.format(\\\"/workbench/dispatch/agreement/preview.do?formNo=%d\\\", dispatch.getFormNo());\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tthrow ex.setMessage(LanguageUtil.get(\\\"dispatch.error.signUrl\\\"));\\n\\t\\t}\\n\\t}\\n\\n\\tprivate String getHomeHrgSignUrl(EmpDispatch dispatch) {\\n\\t\\t// 小心处理派出国hrg和派驻国hrg为同一个人的情况\\n\\t\\tSignContext context = dispatchBiz.getSignContext(dispatch);\\n\\t\\tViewRequest request = context.getHomeHrgViewReqeust();\\n\\t\\tViewResponse response = docusignClient.send(request);\\n\\t\\treturn appendLocale(response.getUrl());\\n\\t}\\n\\n\\tprivate String getHostHrgSignUrl(EmpDispatch dispatch) {\\n\\t\\tSignContext context = dispatchBiz.getSignContext(dispatch);\\n\\t\\tViewRequest request = context.getHostHrgViewRequest();\\n\\t\\tViewResponse response = docusignClient.send(request);\\n\\t\\treturn appendLocale(response.getUrl());\\n\\t}\\n\\n\\tprivate String getEmpSignUrl(EmpDispatch dispatch) {\\n\\t\\tSignContext context = dispatchBiz.getSignContext(dispatch);\\n\\t\\tViewRequest request = context.getEmpViewRequest();\\n\\t\\tViewResponse response = docusignClient.send(request);\\n\\t\\treturn appendLocale(response.getUrl());\\n\\t}\\n\\n\\tprivate String appendLocale(String url) {\",\n" +
            "            \"rerank_score\": 0.19072403013706207\n" +
            "        },\n" +
            "        {\n" +
            "            \"path\": \"app/service/workbench/src/main/java/com/alipay/hrworkprod/service/workbench/workbench/common/external/impl/AntDocusignClientImpl.java\",\n" +
            "            \"startLine\": 13,\n" +
            "            \"endLine\": 108,\n" +
            "            \"content\": \"import com.alipay.antlescenter.common.service.facade.vo.CreateESignTaskFileVO;\\nimport com.alipay.antlescenter.common.service.facade.vo.MultiSignerAndTabVosDTO;\\nimport com.alipay.antlescenter.common.service.facade.vo.SealTabsVO;\\nimport com.alipay.hrworkprod.dal.workbench.domain.dispatch.EmpDispatch;\\nimport com.alipay.hrworkprod.service.workbench.common.exception.BizException;\\nimport com.alipay.hrworkprod.service.workbench.common.util.AssertUtilsExt;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.basicdata.EmpCompanyService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.EmpDispatchService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.dto.SignContext;\\nimport com.aliyun.oss.OSS;\\nimport com.google.common.base.Preconditions;\\nimport com.google.common.base.Splitter;\\nimport com.google.common.base.Strings;\\nimport com.google.common.collect.Lists;\\nimport com.google.common.io.ByteStreams;\\nimport lombok.extern.slf4j.Slf4j;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Value;\\nimport org.springframework.stereotype.Component;\\n\\nimport javax.annotation.Resource;\\nimport java.io.ByteArrayOutputStream;\\nimport java.io.IOException;\\nimport java.io.InputStream;\\nimport java.util.*;\\n\\nimport static com.alipay.hrworkprod.service.workbench.workbench.common.external.impl.AntDocusignUtil.*;\\n\\n/**\\n * Default DocusignClient implementation\\n *\\n * <AUTHOR> Jiang\\n */\\n@Slf4j\\n@Component\\npublic class AntDocusignClientImpl extends AbstractDocusignClientImpl {\\n    /**\\n     * 应用名，复用hrworkcore\\n     */\\n    public static final String APPLICATION_NAME = \\\"hrworkcore\\\";\\n\\n    /**\\n     * 电子签租户\\n     */\\n    public static final String TENANT = \\\"ANT_GROUP\\\";\\n\\n    /**\\n     * 场景code，共用入职系统的\\n     */\\n    public static final String SCENE_CODE = \\\"OVERSEAS_ENTY_DOCUSIGN\\\";\\n\\n    /**\\n     * 业务线code\\n     */\\n    public static final String BUSINESS_LINE_CODE = \\\"HR\\\";\\n\\n    public static final String CONTRACT_VERSION = \\\"0\\\";\\n\\n    @Autowired\\n    private AesDocuSignFacade aesDocuSignFacade;\\n\\n    @Autowired\\n    private EmpCompanyService empCompanyService;\\n\\n    @Autowired\\n    private OSS dispatchAntlescenterOss;\\n\\n    @Value(\\\"${antlescenter.oss.bucket.name}\\\")\\n    private String bucketName;\\n\\n    @Resource\\n    EmpDispatchService empDispatchService;\\n\\n    @Override\\n    public void setup() {\\n    }\\n\\n    @Override\\n    protected String getDefaultEmailSubject() {\\n        return SignContext.SUBJECT;\\n    }\\n\\n    @Override\\n    public ViewResponse send(ViewRequest request) {\\n        Preconditions.checkNotNull(request);\\n        CommonResult<DocusignSignDetail> result = querySignResult(request.getEnvelopeId());\\n        Preconditions.checkState(result.isSuccess());\\n        GetRecipientViewRequest getRecipientViewRequest = new GetRecipientViewRequest();\\n        getRecipientViewRequest.setSignTaskId(request.getEnvelopeId());\\n        getRecipientViewRequest.setUserName(request.getUsername());\\n        getRecipientViewRequest.setEmail(request.getEmail());\\n        getRecipientViewRequest.setRecipientId(request.getRecipientId());\\n        getRecipientViewRequest.setBizNo(result.getData().getApplicationId());\\n        getRecipientViewRequest.setReturnUrl(request.getCallback());\\n        log.info(\\\"send getRecipientViewRequest : {}\\\", JSON.toJSONString(getRecipientViewRequest));\",\n" +
            "            \"rerank_score\": 0.2735744118690491\n" +
            "        },\n" +
            "        {\n" +
            "            \"startLine\": 0,\n" +
            "            \"endLine\": 48,\n" +
            "            \"path\": \"app/biz/workbench/src/main/java/com/alipay/hrworkprod/biz/workbench/workbench/dispatch/DispatchSignActuator.java\",\n" +
            "            \"content\": \"package com.alipay.hrworkprod.biz.workbench.workbench.dispatch;\\n\\nimport com.alipay.hrworkprod.dal.workbench.domain.common.EmpActionMessage;\\nimport com.alipay.hrworkprod.dal.workbench.domain.common.EmpMsgTaskcenter;\\nimport com.alipay.hrworkprod.dal.workbench.domain.dispatch.EmpDispatch;\\nimport com.alipay.hrworkprod.infrastructure.api.taskCenter.common.model.BusinessId;\\nimport com.alipay.hrworkprod.infrastructure.api.taskCenter.common.model.ComponentId;\\nimport com.alipay.hrworkprod.infrastructure.api.taskCenter.common.model.Task;\\nimport com.alipay.hrworkprod.service.workbench.common.MethodUtil;\\nimport com.alipay.hrworkprod.service.workbench.common.SignConsts;\\nimport com.alipay.hrworkprod.service.workbench.common.enums.TaskCenter.TaskCenterStatus;\\nimport com.alipay.hrworkprod.service.workbench.common.enums.TaskCenter.TaskType;\\nimport com.alipay.hrworkprod.service.workbench.common.enums.TaskCenter.TaskcenterBizType;\\nimport com.alipay.hrworkprod.service.workbench.common.exception.HrCommonException;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.AbstractTaskcenterActuator;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.EmpActionMessageService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.external.DocusignClient;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.impl.EmpMsgTaskcenterActuator;\\n\\nimport javax.annotation.Resource;\\nimport java.util.Date;\\n\\n/**\\n * The type Dispatch sign actuator.\\n */\\npublic class DispatchSignActuator extends AbstractTaskcenterActuator{\\n    /**\\n     * The Dispatch biz.\\n     */\\n    @Resource\\n\\tDispatchBiz dispatchBiz;\\n    /**\\n     * The Emp action message service.\\n     */\\n    @Resource\\n\\tEmpActionMessageService empActionMessageService;\\n\\n    /**\\n     * The Source name.\\n     */\\n    String sourceName = \\\"HR工作台\\\";\\n    /**\\n     * The Subject.\\n     */\\n    String subject = \\\"跨境派遣协议签署\\\";// title\\n    /**\\n     * The Subject en.\\n     */\",\n" +
            "            \"embedding_score\": 0.725055,\n" +
            "            \"rerank_score\": 0.30321571230888367\n" +
            "        },\n" +
            "        {\n" +
            "            \"startLine\": 0,\n" +
            "            \"endLine\": 40,\n" +
            "            \"path\": \"app/biz/workbench/src/main/java/com/alipay/hrworkprod/biz/workbench/workbench/dispatch/impl/DispatchTaskFactoryImpl.java\",\n" +
            "            \"content\": \"package com.alipay.hrworkprod.biz.workbench.workbench.dispatch.impl;\\n\\nimport com.alipay.hrworkprod.biz.workbench.workbench.dispatch.DispatchBiz;\\nimport com.alipay.hrworkprod.biz.workbench.workbench.dispatch.DispatchTaskFactory;\\nimport com.alipay.hrworkprod.dal.workbench.domain.dispatch.EmpDispatch;\\nimport com.alipay.hrworkprod.infrastructure.api.keycenter.CryptographApi;\\nimport com.alipay.hrworkprod.model.common.util.LogUtil;\\nimport com.alipay.hrworkprod.service.workbench.common.ConstUtil;\\nimport com.alipay.hrworkprod.service.workbench.common.PrivilegeInfo;\\nimport com.alipay.hrworkprod.service.workbench.common.enums.Dispatch.SignStatus;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.EmpMsgMcRecordService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.JdkExecutorService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.external.DocusignClient;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.external.DocusignClient.StatusRequest;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.external.DocusignClient.StatusResponse;\\nimport com.alipay.hrworkprod.service.workbench.workbench.common.external.OssFileService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.EmpDispatchService;\\nimport com.alipay.hrworkprod.service.workbench.workbench.dispatch.dto.SignContext;\\nimport org.apache.commons.lang3.StringUtils;\\nimport org.slf4j.Logger;\\nimport org.slf4j.LoggerFactory;\\nimport org.springframework.beans.factory.annotation.Autowired;\\nimport org.springframework.beans.factory.annotation.Qualifier;\\nimport org.springframework.stereotype.Service;\\n\\nimport javax.annotation.Resource;\\nimport java.util.concurrent.TimeUnit;\\n\\n/**\\n * The type Dispatch task factory.\\n */\\n@Service\\npublic class DispatchTaskFactoryImpl implements DispatchTaskFactory {\\n\\tprivate static final Logger logger = LoggerFactory.getLogger(DispatchTaskFactoryImpl.class);\\n    /**\\n     * The Pvg info.\\n     */\\n    @Resource\\n\\tPrivilegeInfo pvgInfo;\\n    /**\",\n" +
            "            \"embedding_score\": 0.7195225,\n" +
            "            \"rerank_score\": 0.2267836332321167\n" +
            "        }\n" +
            "    ]\n" +
            "}";

    @Test
    public void searchPostHandle() {

        ActionGenCodeServiceImpl actionGenCodeService = new ActionGenCodeServiceImpl();
        RepoInfo repoInfo = new RepoInfo();
        repoInfo.setBranch("vat_eval_8216a3f0");
        repoInfo.setRepoPath("common_release/hrworkprod");
        JSONObject js = JSON.parseObject(code);
        List<BloopSearchClient.CodeResult> codeResultList = JSON.parseArray(js.getJSONArray("data").toJSONString(), BloopSearchClient.CodeResult.class);
        List<BloopSearchClient.CodeResult> codeResultList2 = actionGenCodeService.searchPostHandle(repoInfo, codeResultList, VatSearchType.V3_BATE);
        codeResultList2.stream()
                .forEach(item -> {
                    System.out.println(item.getPath());
                    System.out.println(item.getRerankScore());
                });


    }
}