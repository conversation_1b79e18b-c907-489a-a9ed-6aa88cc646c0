
package com.alipay.codegencore.model.copilot;

import com.alipay.codegencore.model.model.links.CopilotButton;

import java.util.List;
/**
 * Antcode智能助手的问题配置信息。
 *
 */
public class AntcodeCopilotRecommendQuestionConfig {
    // antcode页面类型
    private String antcodePageType;

    // 查询模板列表
    private List<CopilotButton> queryTemplateList;


    public String getAntcodePageType() {
        return antcodePageType;
    }

    public void setAntcodePageType(String antcodePageType) {
        this.antcodePageType = antcodePageType;
    }

    public List<CopilotButton> getQueryTemplateList() {
        return queryTemplateList;
    }

    public void setQueryTemplateList(List<CopilotButton> queryTemplateList) {
        this.queryTemplateList = queryTemplateList;
    }
}
