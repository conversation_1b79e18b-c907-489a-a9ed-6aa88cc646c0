package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.model.domain.AnswerIndexBuildJobDO;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * 问答索引构建job
 */
public interface AnswerIndexBuildJobMapper {

    /**
     * 批量插入job
     * 这个同样的文件,后续变更产生commit后,会插入新的job
     * @param answerIndexBuildJobDOList
     * @return
     */
    int batchInsertJob(@Param("jobList") List<AnswerIndexBuildJobDO> answerIndexBuildJobDOList);

    /**
     * 获取需要构建的job列表
     * 后续考虑加上 taskId 的条件,提高查询效率
     * @param size
     * @return
     */
    List<AnswerIndexBuildJobDO> getNeedBuildJobs(@Param("taskIdList") List<Long> taskIdList, @Param("size") int size);

    /**
     *
     * @param jobIdList
     * @return
     */
    int markJobBuilding(@Param("jobIdList") List<Long> jobIdList, @Param("handleInfo") String handleInfo);

    /**
     * 获取指定的job信息
     * id
     * failCount
     * @param jobId
     * @return
     */
    AnswerIndexBuildJobDO getById(Long jobId);

    /**
     * 批量更新job状态
     * @param jobIdList
     * @param state
     * @return
     */
    int markJobBuildingState(@Param("jobIdList") List<Long> jobIdList,
                             @Param("state") String state,
                             @Param("failMessage") String failMessage);

    /**
     * 指定任务需要构建的job数量
     * @param taskId
     * @return
     */
    int countNeedBuildByTaskId(Long taskId);

    /**
     * 查询异常构建的job列表(id, failCount)
     * @param taskIdList
     * @param timeout
     * @return
     */
    List<AnswerIndexBuildJobDO> queryBuildingTimeout(@Param("taskIdList") List<Long> taskIdList, @Param("timeout") Timestamp timeout);

    /**
     * 根据id批量删除job，物理删除
     *
     * <AUTHOR>
     * @since 2024.08.16
     * @param idList idList
     * @return int
     */
    int deleteByIdList (@Param("idList") List<Long> idList);
    /**
     * 查询小于id的jobId列表
     *
     * <AUTHOR>
     * @since 2024.08.16
     * @param id id
     * @return java.util.List<java.lang.Long>
     */
    List<Long> queryJobIdListLessThanId(@Param("id") Long id,@Param("size") int size);

    /**
     * 查询失败的job列表
     * <AUTHOR>
     * @since 2024.08.02
     * @param taskId taskId
     * @return java.util.List<com.alipay.codegencore.model.domain.AnswerIndexBuildJobDO>
     */
    List<AnswerIndexBuildJobDO> queryFailJobs(@Param("taskId")Long taskId);

    int retryJobs(@Param("jobIdList") List<Long> jobIdList, @Param("state") String state);

    int updateJobState(@Param("jobIdList") List<Long> jobIdList, @Param("state") String state);

    List<AnswerIndexBuildJobDO> getJobByIds(@Param("jobIdList") List<Long> jobIdList);

    /**
     * 删除任务对应的 job
     * @param taskId
     * @return
     */
    long deleteById(Long taskId);

    /**
     * 失败 job 重置
     * @param taskId
     * @param failMessage
     * @return
     */
    long resetFailHandle(@Param("taskId") Long taskId, @Param("failMessage") String failMessage);
}
