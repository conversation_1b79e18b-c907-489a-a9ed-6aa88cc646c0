package com.alipay.codegencore.model.request.maya;

import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.codegencore.model.openai.ChatMessage;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 2024/12/9 16:07
 */
public class MayaBLStreamRequestBean {

    @JSONField(name = "__entry_point__")
    private String entryPoint;

    @JSONField(name = "model")
    private String model;

    @JSONField(name = "messages")
    private List<ChatMessage> messages;

    @JSONField(name = "stream")
    private Boolean stream;

    @J<PERSON><PERSON>ield(name = "max_tokens")
    private Integer maxTokens;

    @JSO<PERSON>ield(name = "temperature")
    private BigDecimal temperature;

    @JSONField(name = "repetition_penalty")
    private BigDecimal repetitionPenalty;

    @J<PERSON>NField(name = "top_p")
    private BigDecimal topP;

    @JSONField(name = "top_k")
    private Integer topK;

    public String getEntryPoint() {
        return entryPoint;
    }

    public void setEntryPoint(String entryPoint) {
        this.entryPoint = entryPoint;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<ChatMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<ChatMessage> messages) {
        this.messages = messages;
    }

    public Boolean getStream() {
        return stream;
    }

    public void setStream(Boolean stream) {
        this.stream = stream;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public BigDecimal getRepetitionPenalty() {
        return repetitionPenalty;
    }

    public void setRepetitionPenalty(BigDecimal repetitionPenalty) {
        this.repetitionPenalty = repetitionPenalty;
    }

    public BigDecimal getTopP() {
        return topP;
    }

    public void setTopP(BigDecimal topP) {
        this.topP = topP;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

}
