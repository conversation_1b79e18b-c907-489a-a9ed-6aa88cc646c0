/*
 * Ant Group
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.enums;

/**
 * <AUTHOR>
 * @version ControlTypeEnum.java, v 0.1 2023年08月15日 下午5:18 lqb01337046
 */
public enum ControlTypeEnum {
    /**
     * 可见
     */
    SEE(1),

    /**
     * 可编辑
     */
    UPDATE(2);


    private int code;

    ControlTypeEnum(int code) {
        this.code = code;
    }

    ControlTypeEnum getByCode(int code) {
        for(ControlTypeEnum e: ControlTypeEnum.values()) {
            if(e.code == code) {
                return e;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }
}
