/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.alipay.codegencore.model.model.links;

import com.alipay.codegencore.model.model.links.Enum.ReplaysSystemEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * gpt返回诊断工具信息
 * <AUTHOR>
 * @version $Id: DiagTool.java, v 0.1 2023-11-02 上午10:41 admin Exp $$
 */
public class DiagTool extends ToString {

    /**
     * appName
     * accagovernance ：云图工具
     * atechs ：聆图工具
     */
    private String appName;

    /**
     * 工具id
     */
    private String toolId;

    /**
     * 工具名称
     */
    private String toolName;

    /**
     * 工具描述
     */
    private String toolDesc;

    /**
     * 是否直接进行工具诊断
     */
    private Boolean flow ;

    /**
     * 诊断的表单数据
     */
    private PlatformToolResponse platformToolResponse ;

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getToolId() {
        return toolId;
    }

    public void setToolId(String toolId) {
        this.toolId = toolId;
    }

    public String getToolName() {
        return toolName;
    }

    public void setToolName(String toolName) {
        this.toolName = toolName;
    }

    public String getToolDesc() {
        return toolDesc;
    }

    public void setToolDesc(String toolDesc) {
        this.toolDesc = toolDesc;
    }

    public Boolean getFlow() {
        return flow;
    }

    public void setFlow(Boolean flow) {
        this.flow = flow;
    }

    public PlatformToolResponse getPlatformToolResponse() {
        return platformToolResponse;
    }

    public void setPlatformToolResponse(PlatformToolResponse platformToolResponse) {
        this.platformToolResponse = platformToolResponse;
    }

    /**
     * 获取系统
     *
     * @return 系统信息
     */
    public ReplaysSystemEnum getSystem() {
        return StringUtils.equalsIgnoreCase(appName, "atechs") ? ReplaysSystemEnum.ATECHS : ReplaysSystemEnum.YUNTU;
    }
}