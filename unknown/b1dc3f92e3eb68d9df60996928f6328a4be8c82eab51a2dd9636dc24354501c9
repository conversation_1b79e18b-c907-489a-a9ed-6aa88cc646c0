package com.alipay.codegencore.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * list处理工具
 */
public class ListUtils {

    /**
     * 对list进行分页返回
     *
     * @param list
     * @param pageSize
     * @param pageNo
     * @return
     */
    public static <T> List<T> getPageList(List<T> list, int pageSize, int pageNo) {
        int fromIndex = (pageNo - 1) * pageSize;
        if (fromIndex >= list.size()) {
            return new ArrayList<>();
        }
        int toIndex = Math.min(fromIndex + pageSize, list.size());
        return list.subList(fromIndex, toIndex);
    }

}
