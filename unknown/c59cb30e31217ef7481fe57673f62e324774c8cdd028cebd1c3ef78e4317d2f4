package com.alipay.codegencore.model.enums;

/**
 * openai的response里面model的枚举
 */
public enum ChatGPTModelEnum {

    /**
     * 达芬奇模型
     */
    DAVINCI("text-davinci"),
    /**
     * GPT3.5模型
     */
    GPT_35("gpt-3.5"),
    /**
     * GPT4模型
     */
    GPT4("gpt-4"),
    ;

    private String modelNamePrefix;

    ChatGPTModelEnum(String modelNamePrefix) {
        this.modelNamePrefix = modelNamePrefix;
    }

    public String getModelNamePrefix() {
        return modelNamePrefix;
    }

    public void setModelNamePrefix(String modelNamePrefix) {
        this.modelNamePrefix = modelNamePrefix;
    }

    /**
     * 根据openai的模型名称获取模型
     * @param modelName 模型
     * @return ChatGPTModelEnum
     */
    public static ChatGPTModelEnum getByModelName(String modelName){
        if (modelName==null) {
            return null;
        }
        for (ChatGPTModelEnum chatGPTModelEnum : values()) {
            if (modelName.startsWith(chatGPTModelEnum.getModelNamePrefix())) {
                return chatGPTModelEnum;
            }
        }
        return null;
    }


}

