package com.alipay.codegencore.service.handler;

import com.alipay.codegencore.model.model.codegpt.GptAlgModelServiceRequest;
import com.alipay.codegencore.model.openai.ChatMessage;
import reactor.core.publisher.Flux;

/**
 * 算法语言模型抽象处理器
 *
 * <AUTHOR>
 * 创建时间 2023-03-22
 */
public abstract class AbstractAlgLanguageHandler {

    /**
     * 调用算法模型，进行单次对话
     *
     * @return
     */
    public abstract ChatMessage chat(GptAlgModelServiceRequest t);

    /**
     * 调用算法模型，进行流式对话
     *
     * @return
     */
    public abstract Flux<String> chatOnStream(GptAlgModelServiceRequest t);

}
