package com.alipay.codegencore.service;

import com.alibaba.fastjson.JSON;
import com.alipay.codegencore.model.openai.CodeInfo;
import com.alipay.codegencore.model.openai.GenCodeRequest;
import com.alipay.codegencore.model.openai.Plan;
import com.alipay.codegencore.model.openai.RepoInfo;
import com.alipay.codegencore.service.utils.Action2CodeUtil;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class Action2CodeUtilTest {
    @Test
    public void testDiff() {
        String oldCode =
                "public class HelloWorld {\n" +
                        "    public static void main(String[] args) {\n" +
                        "        System.out.println(\"Hello, World!\");\n" +
                        "    }\n" +
                        "}";
        String newCode =
                "public class HelloWorld {\n" +
                        "    public static void main(String[] args) {\n" +
                        "        System.out.println(\"Hello, Java!\");\n" +
                        "        System.out.println(\"Welcome to programming!\");\n" +
                        "    }\n" +
                        "}";

        String desiredDiff = "```diff\n" +
                " public class HelloWorld {\n" +
                "     public static void main(String[] args) {\n" +
                "-        System.out.println(\"Hello, World!\");\n" +
                "+        System.out.println(\"Hello, Java!\");\n" +
                "+        System.out.println(\"Welcome to programming!\");\n" +
                "     }\n" +
                " }\n" +
                "```";

        String diffMarkdown = Action2CodeUtil.generateDiffMarkdown(oldCode, newCode);
        Assert.assertEquals(desiredDiff, diffMarkdown);
    }

    @Test
    public void testMarkdown(){

        String planStr = "[\n" +
                "            {\n" +
                "                \"id\": 0,\n" +
                "                \"type\": null,\n" +
                "                \"filePath\": \"app/common/dal/src/main/java/com/alipay/archcompass/common/dal/transaction/auto/daointerface/ProjectDAO.java\",\n" +
                "                \"step\": \"在insert方法中添加备注字段，修改方法签名为public String insert(ProjectDO project, String remark);\",\n" +
                "                \"similarityCode\": \"\\n    public String insert(ProjectDO project);\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 1,\n" +
                "                \"type\": null,\n" +
                "                \"filePath\": \"app/common/dal/src/main/java/com/alipay/archcompass/common/dal/transaction/auto/daointerface/ProjectDAO.java\",\n" +
                "                \"step\": \"虽然此文件中的selectById方法不需要修改，但为了保持一致性，可以在此文件中添加一个新方法public ProjectDO selectByIdWithRemark(String projectId);以备后续可能的扩展需求。\",\n" +
                "                \"similarityCode\": \"\\n    public ProjectDO selectById(String projectId);\"\n" +
                "            }\n" +
                "        ]";
        // Example usage
        List<Plan> plans = JSON.parseArray(planStr, Plan.class);

        String codeStr = "[\n" +
                "            {\n" +
                "                \"filePath\": \"app/common/dal/src/main/java/com/alipay/archcompass/common/dal/transaction/auto/daointerface/ProjectDAO.java\",\n" +
                "                \"oldCode\": \"public String insert(ProjectDO project);\",\n" +
                "                \"newCode\": \"public String insert(ProjectDO project, String remark);\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"filePath\": \"app/common/dal/src/main/java/com/alipay/archcompass/common/dal/transaction/auto/daointerface/ProjectDAO.java\",\n" +
                "                \"oldCode\": \"public ProjectDO selectById(String projectId);\",\n" +
                "                \"newCode\": \"public ProjectDO selectByIdWithRemark(String projectId) {\\n    // 实现代码，例如：\\n    ProjectDO project = selectById(projectId);\\n    // 添加备注字段逻辑\\n    return project;\\n}\"\n" +
                "            }\n" +
                "        ]";
        List<CodeInfo> codeInfos = JSON.parseArray(codeStr, CodeInfo.class);
        GenCodeRequest genCodeRequest = new GenCodeRequest();
        RepoInfo repoInfo = new RepoInfo();
        repoInfo.setRepoPath("LinkedE/archcompass");
        repoInfo.setBranch("master");
        genCodeRequest.setRepoInfo(repoInfo);
        String report = Action2CodeUtil.generateReport(plans, codeInfos, genCodeRequest);
        System.out.println(report);
    }
}
