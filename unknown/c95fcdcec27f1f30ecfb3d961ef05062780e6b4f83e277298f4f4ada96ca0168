package com.alipay.codegencore.dal.mapper;

import com.alipay.codegencore.dal.example.FileDataDOExample;
import com.alipay.codegencore.model.domain.FileDataDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface FileDataDOMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    long countByExample(FileDataDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    int deleteByExample(FileDataDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    @Delete({
            "delete from cg_file_data",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    @Insert({
            "insert into cg_file_data (file_name, version, ",
            "group_name, file_sha, ",
            "os_arch, os_type, ",
            "is_used, download_url)",
            "values (#{fileName,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, ",
            "#{groupName,jdbcType=VARCHAR}, #{fileSha,jdbcType=VARCHAR}, ",
            "#{osArch,jdbcType=TINYINT}, #{osType,jdbcType=TINYINT}, ",
            "#{isUsed,jdbcType=TINYINT}, #{downloadUrl,jdbcType=VARCHAR})"
    })
    @SelectKey(statement="SELECT LAST_INSERT_ID() as id", keyProperty="id", before=false, resultType=Long.class)
    int insert(FileDataDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    int insertSelective(FileDataDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    List<FileDataDO> selectByExample(FileDataDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    @Select({
            "select",
            "id, file_name, version, group_name, file_sha, os_arch, os_type, is_used, download_url",
            "from cg_file_data",
            "where id = #{id,jdbcType=BIGINT}"
    })
    FileDataDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    int updateByExampleSelective(@Param("record") FileDataDO record, @Param("example") FileDataDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    int updateByExample(@Param("record") FileDataDO record, @Param("example") FileDataDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    int updateByPrimaryKeySelective(FileDataDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table cg_file_data
     *
     * @mbg.generated Thu Dec 01 10:46:36 CST 2022
     */
    @Update({
            "update cg_file_data",
            "set file_name = #{fileName,jdbcType=VARCHAR},",
            "version = #{version,jdbcType=VARCHAR},",
            "group_name = #{groupName,jdbcType=VARCHAR},",
            "file_sha = #{fileSha,jdbcType=VARCHAR},",
            "os_arch = #{osArch,jdbcType=TINYINT},",
            "os_type = #{osType,jdbcType=TINYINT},",
            "is_used = #{isUsed,jdbcType=TINYINT},",
            "download_url = #{downloadUrl,jdbcType=VARCHAR}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(FileDataDO record);
}