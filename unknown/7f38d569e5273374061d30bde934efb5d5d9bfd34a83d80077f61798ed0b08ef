<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alipay.codegencore.dal.mapper.TokenDOMapper">
  <resultMap id="BaseResultMap" type="com.alipay.codegencore.model.domain.TokenDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="user" jdbcType="VARCHAR" property="user" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="uri_pattern_list" jdbcType="VARCHAR" property="uriPatternList" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="owner_user_id" jdbcType="VARCHAR" property="ownerUserId" />
    <result column="enable_status" jdbcType="TINYINT" property="enableStatus" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    id, gmt_create, gmt_modified, user, token, uri_pattern_list, description, balance, 
    owner_user_id, enable_status, app_name
  </sql>
  <select id="selectByExample" parameterType="com.alipay.codegencore.dal.example.TokenDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cg_token
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.alipay.codegencore.dal.example.TokenDOExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    delete from cg_token
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alipay.codegencore.model.domain.TokenDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID() as id
    </selectKey>
    insert into cg_token
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="user != null">
        user,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="uriPatternList != null">
        uri_pattern_list,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="balance != null">
        balance,
      </if>
      <if test="ownerUserId != null">
        owner_user_id,
      </if>
      <if test="enableStatus != null">
        enable_status,
      </if>
      <if test="appName != null">
        app_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="uriPatternList != null">
        #{uriPatternList,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=DECIMAL},
      </if>
      <if test="ownerUserId != null">
        #{ownerUserId,jdbcType=VARCHAR},
      </if>
      <if test="enableStatus != null">
        #{enableStatus,jdbcType=TINYINT},
      </if>
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alipay.codegencore.dal.example.TokenDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    select count(*) from cg_token
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    update cg_token
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.user != null">
        user = #{record.user,jdbcType=VARCHAR},
      </if>
      <if test="record.token != null">
        token = #{record.token,jdbcType=VARCHAR},
      </if>
      <if test="record.uriPatternList != null">
        uri_pattern_list = #{record.uriPatternList,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.balance != null">
        balance = #{record.balance,jdbcType=DECIMAL},
      </if>
      <if test="record.ownerUserId != null">
        owner_user_id = #{record.ownerUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.enableStatus != null">
        enable_status = #{record.enableStatus,jdbcType=TINYINT},
      </if>
      <if test="record.appName != null">
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    update cg_token
    set id = #{record.id,jdbcType=BIGINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      user = #{record.user,jdbcType=VARCHAR},
      token = #{record.token,jdbcType=VARCHAR},
      uri_pattern_list = #{record.uriPatternList,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      balance = #{record.balance,jdbcType=DECIMAL},
      owner_user_id = #{record.ownerUserId,jdbcType=VARCHAR},
      enable_status = #{record.enableStatus,jdbcType=TINYINT},
      app_name = #{record.appName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alipay.codegencore.model.domain.TokenDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 11 15:11:56 CST 2024.
    -->
    update cg_token
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="user != null">
        user = #{user,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="uriPatternList != null">
        uri_pattern_list = #{uriPatternList,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=DECIMAL},
      </if>
      <if test="ownerUserId != null">
        owner_user_id = #{ownerUserId,jdbcType=VARCHAR},
      </if>
      <if test="enableStatus != null">
        enable_status = #{enableStatus,jdbcType=TINYINT},
      </if>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>