package com.alipay.codegencore;

import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.service.common.ConfigService;
import com.alipay.codegencore.utils.code.AntCodeClient;
import com.alipay.codegencore.utils.code.CodeInsightClient;
import com.alipay.codegencore.utils.code.CodeSearchClient;
import com.alipay.codegencore.utils.code.CodefuseSearchClient;
import com.alipay.codegencore.utils.dingding.DingDingUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version : UtilInitComponent.java, v 0.1 2023年10月23日 10:50 baoping Exp $
 */
@Component
@Order(1)
public class UtilInitComponent {
    private static final Logger LOGGER = LoggerFactory.getLogger( UtilInitComponent.class );

    @Resource
    private ConfigService configService;

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        LOGGER.info("begin system init");
        initDingDingUtil();

        initAntCodeClient();

        initCodeSearch();

        initCodeInsight();

        initCodeSearchClient();
    }

    /**
     * 初始化钉钉工具
     */
    private void initDingDingUtil() {
        String dingSecret = configService.getConfigByKey(AppConstants.DING_SECRET, false);
        String dingAccessToken = configService.getConfigByKey(AppConstants.DING_ACCESS_TOKEN, false);
        String dingTestSecret = configService.getConfigByKey(AppConstants.DING_TEST_SECRET, false);
        String dingTestAccessToken = configService.getConfigByKey(AppConstants.DING_TEST_ACCESS_TOKEN, false);

        DingDingUtil.init(dingSecret, dingAccessToken, dingTestSecret, dingTestAccessToken);
    }

    /**
     * 初始化 ant code 客户端
     */
    private void initAntCodeClient() {
        String host = configService.getConfigByKey(AntCodeClient.ANT_CODE_HOST, false);
        String token = configService.getConfigByKey(AntCodeClient.ANT_CODE_TOKEN, false);
        LOGGER.info("begin antCode client init:{};{}", host, token);
        AntCodeClient.init(host, token);
    }

    /**
     * 初始化 code search
     */
    private void initCodeSearch() {
        String config = configService.getConfigByKey(CodeSearchClient.CODE_SEARCH_CONFIG, false);
        CodeSearchClient.init(config);
    }

    /**
     * 初始化 code insight
     */
    private void initCodeInsight() {
        String config = configService.getConfigByKey(CodeInsightClient.CONFIG, false);
        CodeInsightClient.init(config);
    }

    /**
     * 初始化 code search client
     */
    private void initCodeSearchClient() {
        String config = configService.getConfigByKey(CodefuseSearchClient.CODEFUSE_SEARCH_CONFIG, false);
        CodefuseSearchClient.init(config);
    }
}
