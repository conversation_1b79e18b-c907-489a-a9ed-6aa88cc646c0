package com.alipay.codegencore.utils.codefuse;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;


/**
 * <AUTHOR>
 * @BelongsProject :codegencore
 * @BelongsPackage : com.alipay.codegencore.utils.codeFuse
 * @CreateTime : 2023-11-02
 */
public class SignUtil {

    private static final String KEY_AES = "AES";

    private static final String SIGN_FORMAT = "accessKey=%s&timestamp=%d";

    /**
     * Dima签名方法
     * @param accessKey
     * @param accessSecret
     * @param timestamp
     * @return
     * @throws Exception
     */
    public static String dimaProjectSign(String accessKey, String accessSecret, Long timestamp) throws Exception {
        String signStr = String.format(SIGN_FORMAT, accessKey, timestamp);
        return encrypt(signStr, accessSecret);
    }

    private static String encrypt(String src, String key) throws Exception {
        if (key == null || key.length() != 16) {
            throw new Exception("key不满足条件");
        }
        byte[] raw = key.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_AES);
        Cipher cipher = Cipher.getInstance(KEY_AES);
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(src.getBytes());
        return byte2hex(encrypted);
    }

    private static String byte2hex(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp = "";
        for (byte value : b) {
            stmp = (Integer.toHexString(value & 0XFF));
            if (stmp.length() == 1) {
                hs.append("0").append(stmp);
            }
            else {
                hs.append(stmp);
            }
        }
        return hs.toString().toUpperCase();
    }

}
