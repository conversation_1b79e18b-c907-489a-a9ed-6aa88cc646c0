package com.alipay.codegencore.model.model.links;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024.06.24
 */
public interface ResultCode {
    /**
     * 获取结果码
     *
     * @return 结果码
     */
    public String getCode();

    /**
     * 获取结果信息
     *
     * @return 结果信息
     */
    public String getMessage();

    /**
     * 获取标识为成功的结果码
     *
     * @return 成功结果码返回<code>true</code>;否则返回<code>false</code>
     */
    public boolean isSuccessCode();
}
