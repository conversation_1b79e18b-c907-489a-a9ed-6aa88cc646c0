package com.alipay.codegencore.model.openai;

import com.alipay.codegencore.model.enums.ControlTypeEnum;
import com.alipay.codegencore.model.model.PluginCommand;

import java.util.List;

/**
 * 场景表VO
 *
 * <AUTHOR>
 * @BelongsProject :codegpt
 * @BelongsPackage : com.alipay.codegencore.model.openai
 * @CreateTime : 2023-07-18
 */
public class SceneVO {

    /**
     * id
     */
    private Long id;
    /**
     * 场景名
     */
    private String name;

    /**
     * 场景功能描述
     */

    private String description;
    /**
     * system prompt
     */
    private String systemPrompt;

    /**
     * 提问模板，json列表，元素包含两个字段，display和query
     */
    private String queryTemplateListJson;

    /**
     * 模式，0表示无插件模式，1表示有插件模式
     */
    private Integer mode;

    /**
     * 使用的模型
     */
    private String model;

    /**
     * 插件列表
     */
    private String pluginList;

    /**
     * 是否启用
     */
    private Integer enable;
    /**
     * 标签
     */
    private String sceneTag;
    /**
     * 审核状态 0 未审核 1审核中 2 审核通过
     */
    private String auditStatus;

    /**
     * 环境可见范围 1 全部可见 2 仅预发可见
     */
    private Integer visableEnv;
    /**
     * 用户可见范围  1 仅当前用户可见 2 全员可见 3 管理员可见, 4 部分人可见
     */
    private Integer visableUser;
    /**
     * 图标的OSS地址
     */
    private String iconUrl;
    /**
     * 图标的背景色
     */
    private String iconBackgroundColor;
    /**
     * 助手负责人的工号
     */
    private String ownerUserEmpId;
    /**
     * 助手可见用户的工号
     */
    private List<String> viewUserEmpIdList;
    /**
     * 助手可编辑用户的工号
     */
    private List<String> editUserEmpIdList;
    /**
     * 权限类型
     */
    private ControlTypeEnum controlTypeEnum;
    /**
     * 工号列表
     */
    private  List<String> empIdList;
    /**
     * 使用说明
     */
    private  String useInstructions;
    /**
     * 0 除第一轮问答，其他直接模型回答 1 每一轮都是插件调用
     */
    private Boolean callFunctionEveryRound;
    /**
     * 是否开启插件端
     */
    private Boolean pluginEnable;
    /**
     * 工具指令列表
     */
    private List<PluginCommand> pluginCommandList;
    /**
     * 插件端助手提示语
     */
    private String sceneTips;

    public Boolean getCallFunctionEveryRound() {
        return callFunctionEveryRound;
    }

    public void setCallFunctionEveryRound(Boolean callFunctionEveryRound) {
        this.callFunctionEveryRound = callFunctionEveryRound;
    }

    public String getUseInstructions() {
        return useInstructions;
    }

    public void setUseInstructions(String useInstructions) {
        this.useInstructions = useInstructions;
    }

    public ControlTypeEnum getControlTypeEnum() {
        return controlTypeEnum;
    }

    public void setControlTypeEnum(ControlTypeEnum controlTypeEnum) {
        this.controlTypeEnum = controlTypeEnum;
    }

    public List<String> getEmpIdList() {
        return empIdList;
    }

    public void setEmpIdList(List<String> empIdList) {
        this.empIdList = empIdList;
    }

    public Integer getVisableEnv() {
        return visableEnv;
    }

    public void setVisableEnv(Integer visableEnv) {
        this.visableEnv = visableEnv;
    }

    public Integer getVisableUser() {
        return visableUser;
    }

    public void setVisableUser(Integer visableUser) {
        this.visableUser = visableUser;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public void setSystemPrompt(String systemPrompt) {
        this.systemPrompt = systemPrompt;
    }

    public String getQueryTemplateListJson() {
        return queryTemplateListJson;
    }

    public void setQueryTemplateListJson(String queryTemplateListJson) {
        this.queryTemplateListJson = queryTemplateListJson;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getPluginList() {
        return pluginList;
    }

    public void setPluginList(String pluginList) {
        this.pluginList = pluginList;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public String getSceneTag() {
        return sceneTag;
    }

    public void setSceneTag(String sceneTag) {
        this.sceneTag = sceneTag;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getIconBackgroundColor() {
        return iconBackgroundColor;
    }

    public void setIconBackgroundColor(String iconBackgroundColor) {
        this.iconBackgroundColor = iconBackgroundColor;
    }

    public String getOwnerUserEmpId() {
        return ownerUserEmpId;
    }

    public void setOwnerUserEmpId(String ownerUserEmpId) {
        this.ownerUserEmpId = ownerUserEmpId;
    }

    public List<String> getViewUserEmpIdList() {
        return viewUserEmpIdList;
    }

    public void setViewUserEmpIdList(List<String> viewUserEmpIdList) {
        this.viewUserEmpIdList = viewUserEmpIdList;
    }

    public List<String> getEditUserEmpIdList() {
        return editUserEmpIdList;
    }

    public void setEditUserEmpIdList(List<String> editUserEmpIdList) {
        this.editUserEmpIdList = editUserEmpIdList;
    }

    public Boolean getPluginEnable() {
        return pluginEnable;
    }

    public void setPluginEnable(Boolean pluginEnable) {
        this.pluginEnable = pluginEnable;
    }

    public List<PluginCommand> getPluginCommandList() {
        return pluginCommandList;
    }

    public void setPluginCommandList(List<PluginCommand> pluginCommandList) {
        this.pluginCommandList = pluginCommandList;
    }

    public String getSceneTips() {
        return sceneTips;
    }

    public void setSceneTips(String sceneTips) {
        this.sceneTips = sceneTips;
    }
}
