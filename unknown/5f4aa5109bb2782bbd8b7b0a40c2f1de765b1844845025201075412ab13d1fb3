package com.alipay.codegencore.service.tool.learning;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.codegencore.model.model.tool.learning.plugin.PluginLogGroup;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

public class ToolLearningUtilTest {

    @Test
    public void test_001_addStageInfoToPluginLog_pipelinePlugin() {
        testAddStageInfoToPluginLog("/plugin_event_stream/pipeline_plugin.txt");
    }


    @Test
    public void test_002_addStageInfoToPluginLog_abnormal_pipelinePlugin() {
        testAddStageInfoToPluginLog("/plugin_event_stream/abnormal_pipeline_plugin.txt");
    }

    @Test
    public void test_003_addStageInfoToPluginLog_multi_plugin() {
        testAddStageInfoToPluginLog("/plugin_event_stream/multi_plugin.txt");
    }

    private void testAddStageInfoToPluginLog(String resourcePath){
        try {
            List<String> pluginStreamResponseList = IOUtils.readLines(
                    Objects.requireNonNull(this.getClass().getResourceAsStream(resourcePath)),
                    StandardCharsets.UTF_8);

            PluginLogGroup pluginLogGroup = new PluginLogGroup();
            for (String data: pluginStreamResponseList){
                if(StringUtils.isNotBlank(data)){
                    String streamDataStr = data.substring("data: ".length());
                    NewPluginStreamPartResponse pluginStreamPartResponse = JSON.parseObject(streamDataStr, NewPluginStreamPartResponse.class);
                    ToolLearningUtil.addStageInfoToPluginLog(pluginLogGroup, pluginStreamPartResponse);
                }
            }
            System.out.println(JSON.toJSONString(pluginLogGroup, SerializerFeature.PrettyFormat));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}