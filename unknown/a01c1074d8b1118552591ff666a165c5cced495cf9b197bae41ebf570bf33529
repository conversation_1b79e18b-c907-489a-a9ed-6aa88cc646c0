package com.alipay.codegencore.dal.config;


import com.alipay.codegencore.model.AppConstants;
import com.alipay.codegencore.utils.codescan.CodeStaticScanService;
import com.alipay.codegencore.utils.codescan.SimpleCodeAnalysisServiceImpl;
import com.alipay.sofa.runtime.api.annotation.AppConfig;
import com.alipay.zcache.impl.RefreshableCommonTbaseCacheManager;
import com.alipay.zcache.serialize.SerializationType;
import com.alipay.zdal.client.jdbc.ZdalDataSource;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.web.filter.CharacterEncodingFilter;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * bean生产工厂
 *
 * <AUTHOR>
 * 创建时间 2021-12-24
 */
@Configuration
public class AppBeanFactory {
    /**
     * 系统线程name前缀
     */
    private static final String CODEGENCORE_THREAD_NAME_PREFIX = "CODEGENCORE-THREAD-POOL-";
    /**
     * 线程计数
     */
    private static final AtomicInteger THREAD_NO = new AtomicInteger(0);


    @AppConfig("app_name")
    public String appName;

    @AppConfig("zdal_version")
    public String zdalVerson;

    @AppConfig("app_data_source_name")
    public String appDataSourceName;

    @AppConfig("zdal_tail_name")
    public String appTailName;

    @AppConfig("zdal_console_url")
    public String zdalConsoleUrl;

    @AppConfig("tbase_time_out")
    public String tbaseTimeOut;
    @AppConfig("app_oss_data_source_name")
    public String appOssDataSourceName;

    @AppConfig("app_thread_pool_num")
    public String appThreadPoolNum;

    @AppConfig("com.alipay.sofa.dds.config.obproxy-host")
    public String zdalObproxyHost;

    /**
     * 数据库模式，可选值为dev, sit, prod
     */
    @AppConfig("dbmode")
    public String dbmode;

    /**
     * 数据源
     */
    private DataSource dataSource;

    /**
     * 创建tbase客户端，默认采用hessian2方式进行序列化
     *
     * @return tbase客户端
     * @throws Exception 初始化过程中抛的异常
     */
    @Bean(name = "defaultCacheManager")
    public RefreshableCommonTbaseCacheManager createCacheManager() throws Exception {
        RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager = new RefreshableCommonTbaseCacheManager();
        // 应用名称
        refreshableCommonTbaseCacheManager.setAppName(appName);
        // 实际使用时需要替换成配置管控平台提交申请时填写的【应用Tbase租户名称】
        refreshableCommonTbaseCacheManager.setAppTairName(appTailName);
        // 配置管控地址，根据实际使用的环境进行替换
        refreshableCommonTbaseCacheManager.setZdalConsoleUrl(zdalConsoleUrl);
        // 设置访问tbase的超时时间，如需改变 tair/tbase timeout（默认为配置里的 200ms），可以设置该属性。一般用于线下测试，线上不要修改。
        if(!"prod".equalsIgnoreCase(dbmode)){
            refreshableCommonTbaseCacheManager.setTimeout(Integer.valueOf(tbaseTimeOut));
        }
        refreshableCommonTbaseCacheManager.init();
        return refreshableCommonTbaseCacheManager;
    }


    /**
     * 创建tbase客户端
     *
     * @return tbase客户端
     * @throws Exception 初始化过程中抛的异常
     */
    @Bean(name = "noneSerializationCacheManager")
    public RefreshableCommonTbaseCacheManager noneSerializationCacheManager() throws Exception {
        RefreshableCommonTbaseCacheManager refreshableCommonTbaseCacheManager = new RefreshableCommonTbaseCacheManager();
        // 应用名称
        refreshableCommonTbaseCacheManager.setAppName(appName);
        // 实际使用时需要替换成配置管控平台提交申请时填写的【应用Tbase租户名称】
        refreshableCommonTbaseCacheManager.setAppTairName(appTailName);
        // 配置管控地址，根据实际使用的环境进行替换
        refreshableCommonTbaseCacheManager.setZdalConsoleUrl(zdalConsoleUrl);
        // 设置访问tbase的超时时间，如需改变 tair/tbase timeout（默认为配置里的 200ms），可以设置该属性。一般用于线下测试，线上不要修改。
        if(!"prod".equalsIgnoreCase(dbmode)){
            refreshableCommonTbaseCacheManager.setTimeout(Integer.valueOf(tbaseTimeOut));
        }
        refreshableCommonTbaseCacheManager.setSerializationType(SerializationType.none);
        refreshableCommonTbaseCacheManager.init();
        return refreshableCommonTbaseCacheManager;
    }

    /**
     * 数据源配置
     *
     * @return 数据源 {@link DataSource}
     */
    @Bean(name = "dataSource")
    public DataSource createZDalDataSource() {
        ZdalDataSource zdalDataSource = new ZdalDataSource();
        zdalDataSource.setAppName(appName);
        zdalDataSource.setAppDataSourceName(appDataSourceName);
        zdalDataSource.setVersion(zdalVerson);
        Map<String, String> map = new HashMap<>();
        map.put(AppConstants.ZDAL_CONFIG_KEY_PASSWORD_ENCRYPT, AppConstants.ZDAL_CONFIG_VALUE_PASSWORD_ENCRYPT);
        map.put(AppConstants.ZDAL_CONFIG_KEY_OBPROXY, zdalObproxyHost);
        zdalDataSource.setConfigProperties(map);
        zdalDataSource.init();
        dataSource = zdalDataSource;
        return zdalDataSource;
    }

    /**
     * 创建sqlSessionFactory
     *
     * @return 返回sqlSessionFactory
     * @throws Exception 会抛出异常
     */
    @Bean
    @DependsOn("dataSource")
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setVfs(SpringBootVFS.class);
        sqlSessionFactoryBean.setTypeHandlersPackage(AppConstants.MYBATIS_ENUM_TYPEHANDLER_PATH);
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/*.xml"));
        sqlSessionFactoryBean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        return sqlSessionFactoryBean.getObject();
    }


    /**
     * 创建oss客户端
     *
     * @return oss客户端
     * @throws Exception 初始化过程中抛的异常
     */
//    @Bean(name = "ossAdaptor")
//    public OSSAdaptor createOssAdaptor() throws Exception {
//        RefreshableOssStorageManager refreshableOssStorageManager = new RefreshableOssStorageManager();
//        refreshableOssStorageManager.setAppName(appName);
//        refreshableOssStorageManager.setAppDsName(appOssDataSourceName);
//        refreshableOssStorageManager.setVersion(zdalVerson);
//        refreshableOssStorageManager.setDataType(AppConstants.ZDAL_DATA_TYPE_OSS);
//        refreshableOssStorageManager.init();
//        return refreshableOssStorageManager.getObject();
//    }

    /**
     * 创建系统线程池
     * corePoolSize:核心线程数为 {@link #appThreadPoolNum}
     * maxPoolSize:最大线程数为 {@link #appThreadPoolNum}
     * queueSize:队列数量为 {@link #appThreadPoolNum}
     * 拒绝策略:如果超过线程池承载能力,则用主线程跑任务
     *
     * @return 系统线程池。线程池大小为{@link #appThreadPoolNum}
     */
    @Bean(name = "appThreadPool")
    public ExecutorService createThreadPool() {
        return new ThreadPoolExecutor(Integer.valueOf(appThreadPoolNum), Integer.valueOf(appThreadPoolNum), 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(Integer.valueOf(appThreadPoolNum)),
                new ThreadFactory() {
                    @Override
                    public Thread newThread(Runnable r) {
                        return new Thread(r, CODEGENCORE_THREAD_NAME_PREFIX + THREAD_NO.incrementAndGet());
                    }
                }, new ThreadPoolExecutor.CallerRunsPolicy());
    }

    /**
     * 代码分析服务初始化
     * {@note CodeAnalysisService服务于客户端+服务端，故需要手动初始化}
     *
     * @return
     */
    @Bean(name = "codeStaticScanService")
    public CodeStaticScanService createCodeStaticScanService() {
        return new SimpleCodeAnalysisServiceImpl();
    }


    /**
     * 缓存池，5分钟后释放
     *
     * @return
     */
    @Bean(name = "cachePool")
    public Cache createCachePool() {
        return CacheBuilder.newBuilder().maximumSize(500).expireAfterWrite(5, TimeUnit.MINUTES).build();
    }

    /**
     * 缓存池，1分钟后释放
     *
     * @return
     */
    @Bean(name = "tokenCachePool")
    public Cache tokenCachePool() {
        return CacheBuilder.newBuilder().maximumSize(500).expireAfterWrite(1, TimeUnit.MINUTES).build();
    }

    /**
     * 缓存池，10分钟后释放
     *
     * @return
     */
    @Bean(name = "promptCachePool")
    public Cache promptCachePool() {
        return CacheBuilder.newBuilder().maximumSize(500).expireAfterWrite(10, TimeUnit.MINUTES).build();
    }


    /**
     * 事务管理器
     * @return
     */
    @Bean(name = "dataSourceTransactionManager")
    @DependsOn("dataSource")
    public DataSourceTransactionManager dataSourceTransactionManager() {
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager();
        dataSourceTransactionManager.setDataSource(dataSource);
        return dataSourceTransactionManager;
    }


    /**
     * 默认编码过滤器
     * @return 过滤器
     */
    @Bean(name="characterEncodingFilter")
    public CharacterEncodingFilter characterEncodingFilter(){
        CharacterEncodingFilter characterEncodingFilter=new CharacterEncodingFilter();
        characterEncodingFilter.setEncoding("UTF-8");
        characterEncodingFilter.setForceEncoding(true);
        return characterEncodingFilter;
    }

}
