package com.alipay.codegencore.model.domain;

import java.util.Date;

public class GptConversationDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.gmt_create
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.gmt_modified
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.mongo_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String mongoId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.deleted
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.room_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String roomId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.user_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.status
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.channel
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String channel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.biz_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String bizId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.gmt_last_message
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private Date gmtLastMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.title
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.conversation_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String conversationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column links_gpt_conversation.ext_info
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    private String extInfo;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.id
     *
     * @return the value of links_gpt_conversation.id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.id
     *
     * @param id the value for links_gpt_conversation.id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.gmt_create
     *
     * @return the value of links_gpt_conversation.gmt_create
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.gmt_create
     *
     * @param gmtCreate the value for links_gpt_conversation.gmt_create
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.gmt_modified
     *
     * @return the value of links_gpt_conversation.gmt_modified
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.gmt_modified
     *
     * @param gmtModified the value for links_gpt_conversation.gmt_modified
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.mongo_id
     *
     * @return the value of links_gpt_conversation.mongo_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getMongoId() {
        return mongoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.mongo_id
     *
     * @param mongoId the value for links_gpt_conversation.mongo_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setMongoId(String mongoId) {
        this.mongoId = mongoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.deleted
     *
     * @return the value of links_gpt_conversation.deleted
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.deleted
     *
     * @param deleted the value for links_gpt_conversation.deleted
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.room_id
     *
     * @return the value of links_gpt_conversation.room_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getRoomId() {
        return roomId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.room_id
     *
     * @param roomId the value for links_gpt_conversation.room_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.user_id
     *
     * @return the value of links_gpt_conversation.user_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.user_id
     *
     * @param userId the value for links_gpt_conversation.user_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.status
     *
     * @return the value of links_gpt_conversation.status
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.status
     *
     * @param status the value for links_gpt_conversation.status
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.channel
     *
     * @return the value of links_gpt_conversation.channel
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getChannel() {
        return channel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.channel
     *
     * @param channel the value for links_gpt_conversation.channel
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setChannel(String channel) {
        this.channel = channel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.biz_id
     *
     * @return the value of links_gpt_conversation.biz_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getBizId() {
        return bizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.biz_id
     *
     * @param bizId the value for links_gpt_conversation.biz_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.gmt_last_message
     *
     * @return the value of links_gpt_conversation.gmt_last_message
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public Date getGmtLastMessage() {
        return gmtLastMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.gmt_last_message
     *
     * @param gmtLastMessage the value for links_gpt_conversation.gmt_last_message
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setGmtLastMessage(Date gmtLastMessage) {
        this.gmtLastMessage = gmtLastMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.title
     *
     * @return the value of links_gpt_conversation.title
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.title
     *
     * @param title the value for links_gpt_conversation.title
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.conversation_id
     *
     * @return the value of links_gpt_conversation.conversation_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getConversationId() {
        return conversationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.conversation_id
     *
     * @param conversationId the value for links_gpt_conversation.conversation_id
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column links_gpt_conversation.ext_info
     *
     * @return the value of links_gpt_conversation.ext_info
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public String getExtInfo() {
        return extInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column links_gpt_conversation.ext_info
     *
     * @param extInfo the value for links_gpt_conversation.ext_info
     *
     * @mbg.generated Mon Jul 01 15:23:46 CST 2024
     */
    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
}